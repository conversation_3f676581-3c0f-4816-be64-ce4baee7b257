﻿<%@ Page Title="Create Consumer" Language="C#" MasterPageFile="~/Masters/MasterEmpty.master" AutoEventWireup="True" Inherits="Home_Consumers_CreateConsumerWizard" ValidateRequest="false" Codebehind="CreateConsumerWizard.aspx.cs" %>
<%@ Import Namespace="System.Web.Optimization" %>
<%@ Register TagPrefix="sw" TagName="AssignedStaffGrid" Src="~/Home/Consumers/ConsumerAssignedStaff.ascx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolderEmpty" runat="server">
    <telerik:RadWindowManager ID="RadWindowManager1" ShowContentDuringLoad="false"
        VisibleStatusbar="false" Modal="true" ReloadOnShow="true" VisibleTitlebar="false"
        runat="server" Skin="Vista" Behaviors="None" Animation="None" KeepInScreenBounds="true"
        AutoSize="true" />
    <telerik:RadScriptManager ID="RadScriptManager1" runat="server" OnAsyncPostBackError="ScriptManager1_AsyncPostBackError" AsyncPostBackTimeout="5000">
        <Scripts>
            <asp:ScriptReference Assembly="Telerik.Web.UI" Name="Telerik.Web.UI.Common.Core.js" />
            <asp:ScriptReference Assembly="Telerik.Web.UI" Name="Telerik.Web.UI.Common.jQuery.js" />
        </Scripts>
    </telerik:RadScriptManager>
    <telerik:RadAjaxManager ID="RadAjaxManager1" runat="server" UpdatePanelsRenderMode="Inline" DefaultLoadingPanelID="LoadingPanel1" OnAjaxRequest="RadAjaxManager1_AjaxRequest">
        <AjaxSettings>
            <telerik:AjaxSetting AjaxControlID="RadComboBoxPrimaryDisability">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadComboBoxPrimaryDisability" />
                    <telerik:AjaxUpdatedControl ControlID="RadListBoxDisabilitiesDestination" />
                    <telerik:AjaxUpdatedControl ControlID="RadListBoxDisabilitiesSource" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadListBoxDisabilitiesSource">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadListBoxDisabilitiesDestination" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadListBoxDisabilitiesDestination">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadListBoxDisabilitiesSource" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="DisabilitiesSearch">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadListBoxDisabilitiesSource" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="filterDisabilitiesButton">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadListBoxDisabilitiesSource" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadAjaxManager1">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="HiddenACTION_TYPE" />
                    <telerik:AjaxUpdatedControl ControlID="HiddenCONSUMER_ID" />
                    <telerik:AjaxUpdatedControl ControlID="HiddenTEMPCONSUMER_ID" />
                    <telerik:AjaxUpdatedControl ControlID="HiddenOnClose_NavigateToConsumerID" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxFelon">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="FelonNotes" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxNoHold">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="NoHoldNotes" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxSexOffender">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="SexOffenderNotes" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxMembership">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="MembershipExpiration" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxPhotoConsent">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="PhotoConsentExpiration" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxBestInformant">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="BestInformantNotes" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxInterpreterNeeded">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="InterpreterNotes" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="ChkConsumerLegalMatters">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="LegalMattersNotes" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadTextBoxLastName">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="NameWarningDIV" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="ConsumerSSTxt">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="SSWarningDIV" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="ConsumerMRRadTextBox">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="MRWarningDIV" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="ConsumerDMHTxt">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="DMHWarningDIV" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadGridConsumerPhones">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadGridConsumerPhones" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                    <telerik:AjaxUpdatedControl ControlID="ErrorPhoneDIV" />
                    <telerik:AjaxUpdatedControl ControlID="HiddenRadgridInit" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadGridConsumerIdentifiers">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadGridConsumerIdentifiers" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                    <telerik:AjaxUpdatedControl ControlID="ErrorDIV" />
                    <telerik:AjaxUpdatedControl ControlID="HiddenRadgridInit" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxIntlAddress">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="Country" />
                    <telerik:AjaxUpdatedControl ControlID="StateProvinceRegion" />
                    <telerik:AjaxUpdatedControl ControlID="InternationalPostalCode" />
                    <telerik:AjaxUpdatedControl ControlID="ZIP" />
                    <telerik:AjaxUpdatedControl ControlID="State" />
                    <telerik:AjaxUpdatedControl ControlID="County" />
                    <telerik:AjaxUpdatedControl ControlID="RadGridConsumerPhones" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="CheckBoxSecondIntlAddress">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="SecondCountry" />
                    <telerik:AjaxUpdatedControl ControlID="SecondStateProvinceRegion" />
                    <telerik:AjaxUpdatedControl ControlID="SecondInternationalPostalCode" />
                    <telerik:AjaxUpdatedControl ControlID="SecondZIP" />
                    <telerik:AjaxUpdatedControl ControlID="SecondState" />
                    <telerik:AjaxUpdatedControl ControlID="SecondCounty" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadComboBoxState">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadComboBoxCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadComboBoxSecondState">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadComboBoxSecondCounty" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="LinkButtonUpdate">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="LinkButtonUpdate" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                    <telerik:AjaxUpdatedControl ControlID="ChildBenefitGrid" />
                    <telerik:AjaxUpdatedControl ControlID="ConsumerPhones" />
                    <telerik:AjaxUpdatedControl ControlID="ConsumerIdentifiers" />
                    <telerik:AjaxUpdatedControl ControlID="HiddenOnClose_NavigateToConsumerID" />
                    <telerik:AjaxUpdatedControl ControlID="ErrorPhoneDIV" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="ConsumerAddressTxt">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="CustomFieldValidatorCounty" />
                    <telerik:AjaxUpdatedControl ControlID="RequiredFieldValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="RegularExpressionValidatorZIP" />
                    <telerik:AjaxUpdatedControl ControlID="AssignedStaffGrid1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="ChildBenefitGrid">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="ChildBenefitGrid" />
                    <telerik:AjaxUpdatedControl ControlID="HiddenRadgridInit" />
                </UpdatedControls>
            </telerik:AjaxSetting>
        </AjaxSettings>
    </telerik:RadAjaxManager>
<telerik:RadAjaxLoadingPanel ID="LoadingPanel1" runat="server" Transparency="40" BackColor="#ffffff" BackgroundPosition="Center" />
    <div class="popup_window_title" style="width: 100%;">
        <table>
            <tr>
                <td>
                    <asp:Label ID="lblCreateNewConsumer" Width="200px" runat="server" />
                </td>
                <td>

                    <asp:UpdateProgress DynamicLayout="false" ID="UpdateProgressLoaderPlain" runat="server"
                        DisplayAfter="0">
                        <ProgressTemplate>
                            <asp:Image ID="ImageAjaxLoaderPLain" runat="server" ImageUrl="~/images/ajax-loaderPLain.gif" />
                            <asp:Label ID="labelLoading" runat="server" CssClass="SmallText" Text="LOADING..." />
                        </ProgressTemplate>
                    </asp:UpdateProgress>
                </td>
                <td align="right" style="width: 100%">
                    <table>
                        <tr>
                            <td>
                                <asp:LinkButton Draggable="false" Text="update" ForeColor="Black" runat="server" OnClick="btnSaveConsumer_Click"
                                    ID="LinkButtonUpdate" CssClass="Button" ></asp:LinkButton>
                            </td>
                            <td>
                                <div style="display: inline;">
                                    <asp:LinkButton Draggable="false" Text="cancel" ForeColor="Black" runat="server" 
                                    OnClientClick="return Cancel()"
                                        ID="LinkButtonClose" CausesValidation="false" CssClass="Button" OnClick="LinkButtonClose_Click"/>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <div id="lform">  
        <telerik:RadWizard ID="RadWizard1" runat="server" DisplayCancelButton="False" OnClientButtonClicked="WizardButtonClicked" DisplayNavigationButtons="false"
            OnFinishButtonClick="RadWizard1_FinishButtonClick" NavigationButtonsPosition="Top" 
            OnClientLoad="radwizard_Load">
            <WizardSteps>
                <telerik:RadWizardStep ID="RadWizardStepPrimaryInfo" runat="server" Title="Primary Information" 
                ToolTip="Primary Information" ValidationGroup="primaryInfo"  CausesValidation="true">
                    <asp:ValidationSummary ID="ValidationSummaryPrimaryInfo" runat="server" ValidationGroup="primaryInfo" CssClass="ErrorDIV" DisplayMode="List" />
                    <fieldset id="Department" runat="server" class="fieldsetPlacements" style="width: 754px">
                        <legend>
                            <asp:Label runat="server" ID="Label53" Text="Department" /></legend>
                        <ol>
                            <li>
                                <label>
                                    <asp:Label runat="server" ID="lblDepartment" Text="*department:" CssClass="label"></asp:Label></label>
                                <telerik:RadComboBox ID="RadComboBoxDepartment" AllowCustomText="false" runat="server" EmptyMessage="Search or Select..." 
                                    Filter="Contains" MarkFirstMatch="True" ToolTip="Select a Department" ValidationGroup="primaryInfo" Text="Test" Width="180px">
                                </telerik:RadComboBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorDepartment" runat="server" ControlToValidate="RadComboBoxDepartment"
                                    ValidationGroup="primaryInfo" InitialValue="" ErrorMessage="Missing a department." SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorDepartment" runat="server" ControlToValidate="RadComboBoxDepartment"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid department." SetFocusOnError="true" Display="None" ValidationGroup="primaryInfo" />
                            </li>
                        </ol>
                    </fieldset>
                    <div style="float: left;">
                        <fieldset id="FieldsetPersonalinformation" runat="server" class="fieldsetPlacements" style="width: 350px">
                            <legend>
                                <asp:Label runat="server" ID="Label40" Text="Personal Information" /></legend>
                            <ol>
                                <li id="FirstName" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="lblStartFirstName" Text="*first name:" AssociatedControlID="ConsumerFirstNameTxt"></asp:Label></label>
                                    <telerik:RadTextBox ID="ConsumerFirstNameTxt" runat="server" ValidationGroup="primaryInfo" Enabled="true" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="ConsumerFirstNameValidator" runat="server" ControlToValidate="ConsumerFirstNameTxt"
                                        ErrorMessage="Missing a first name." ValidationGroup="primaryInfo" SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="MiddleName" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelMiddleName" Text="middle name:" AssociatedControlID="RadTextBoxMiddleName" /></label>
                                    <telerik:RadTextBox ID="RadTextBoxMiddleName" runat="server" Enabled="true" Width="180px"/>
                                </li>
                                <li id="LastName" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelLastName" Text="*last name:" AssociatedControlID="RadTextBoxLastName" /></label>
                                    <telerik:RadTextBox ID="RadTextBoxLastName" runat="server" ValidationGroup="primaryInfo" Enabled="True" AutoPostBack="True" OnTextChanged="RadTextBoxLastName_TextChanged"
                                         ClientEvents-OnValueChanged="ResetValidation" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="ConsumerLastNameValidator" runat="server" ControlToValidate="RadTextBoxLastName"
                                        ErrorMessage="Missing a last name." ValidationGroup="primaryInfo" SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="Suffix" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelSuffix" Text="suffix:" AssociatedControlID="RadComboBoxSuffix" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxSuffix" runat="server" ValidationGroup="primaryInfo" Enabled="true" EmptyMessage="Search or Select..."
                                        ShowDropDownOnTextboxClick="true" AllowCustomText="true" MarkFirstMatch="true" Filter="Contains" Width="180px">
                                        <Items>
                                            <telerik:RadComboBoxItem Text="Jr" />
                                            <telerik:RadComboBoxItem Text="Sr" />
                                            <telerik:RadComboBoxItem Text="II" />
                                            <telerik:RadComboBoxItem Text="III" />
                                            <telerik:RadComboBoxItem Text="IV" />
                                        </Items>
                                    </telerik:RadComboBox>
                                </li>
                                <div id="NameWarningDIV" class="WarningDIV" runat="server" style="display: none; margin-right: 20px !important;">
                                    <asp:Label ID="lblNameWarning" ForeColor="black" runat="server" />
                                </div>
                                <li id="NickName" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelNickName" Text="nick name:" AssociatedControlID="TextBoxNickName" /></label>
                                    <telerik:RadTextBox ID="TextBoxNickName" runat="server" Enabled="true" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorNickname" runat="server" ControlToValidate="TextBoxNickName" Enabled="False" OnInit="RequiredFieldValidator_OnInit"
                                        ValidationGroup="primaryInfo" ErrorMessage="Missing nickname." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="Pronouns" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelPronouns" Text="pronouns:" AssociatedControlID="TextBoxPronouns" /></label>
                                    <telerik:RadTextBox ID="TextBoxPronouns" runat="server" Enabled="true" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorPronouns" runat="server" ControlToValidate="TextBoxPronouns" Enabled="False" OnInit="RequiredFieldValidator_OnInit"
                                        ValidationGroup="primaryInfo" ErrorMessage="Missing pronouns." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="DOB" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelDOB" Text="*DOB:" AssociatedControlID="RadDatePickerDOB" /></label>
                                    <telerik:RadDatePicker ID="RadDatePickerDOB" Style="vertical-align: middle;" ShowPopupOnFocus="true"
                                        MinDate="1753-1-1" MaxDate="9999-12-31" runat="server" Width="180px">
                                        <DatePopupButton Visible="false" />
                                        <DateInput ID="DateInput1" runat="server" CausesValidation="true"
                                            ValidationGroup="primaryInfo" EmptyMessage="Please enter a date" />
                                    </telerik:RadDatePicker>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorDOB" runat="server" ControlToValidate="RadDatePickerDOB"
                                        ValidationGroup="primaryInfo" ErrorMessage="Missing a valid date of birth (DOB)." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="Gender" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelGender" Text="gender:" AssociatedControlID="RadComboBoxGender" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxGender" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." Filter="Contains" OnClientDropDownClosed="OnClientDropDownClosed"
                                        MarkFirstMatch="True" ToolTip="Select an option" ValidationGroup="primaryInfo" Width="180px">
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorGender" runat="server"
                                        ControlToValidate="RadComboBoxGender" InitialValue="" ValidationGroup="primaryInfo"
                                        ErrorMessage="Missing a gender." SetFocusOnError="true" Display="None" />
                                    <asp:CustomValidator ID="CustomValidatorGender" runat="server" ControlToValidate="RadComboBoxGender"
                                        ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                        ErrorMessage="Select a valid option." SetFocusOnError="true" Display="None" ValidationGroup="primaryInfo" />
                                </li>
                                <li id="Ethnicity" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelEthnicity" Text="ethnicity:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxEthnicityMulti" runat="server" CheckBoxes="true" DataSourceID="SqlDataSourceEthnicities" Width="180px"
                                        DataTextField="Description" DataValueField="Ethnicity_ID" EmptyMessage="Search or Select..." ExpandDirection="Down" Filter="Contains" EnableScreenBoundaryDetection="true">
                                    </telerik:RadComboBox>
                                    <asp:SqlDataSource ID="SqlDataSourceEthnicities" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[CONSUMER_EXPIRATION.getConsumerEthnicityData_1.0.0]"
                                        SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator10" runat="server"
                                        ControlToValidate="RadComboBoxEthnicityMulti" InitialValue="" ValidationGroup="primaryInfo"
                                        ErrorMessage="Missing an ethnicity" SetFocusOnError="true" Display="None" />
                                    <asp:CustomValidator ID="CustomValidatorEthnicity" runat="server" ControlToValidate="RadComboBoxEthnicityMulti"
                                        ClientValidationFunction="validateComboCheckbox" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                        ErrorMessage="Select a valid ethnicity." SetFocusOnError="true" Display="None" ValidationGroup="primaryInfo" />
                                </li>
                                <li>
                                    <label>
                                        <asp:Label runat="server" ID="LabelRace" Text="race:"/>
                                    </label>
                                    <telerik:RadComboBox ID="RadComboBoxRace" runat="server" Filter="Contains" DataSourceID="SqlDataSourceRaces" Width="180px"
                                                         AllowCustomText="False" AutoPostBack="False" EmptyMessage="Search or Select..." ToolTip="Select an option"
                                                         DataTextField="Description" DataValueField="CodeValue_ID" ExpandDirection="Down" EnableScreenBoundaryDetection="True"
                                                         CheckBoxes="True">
                                        <Items>
                                            <telerik:RadComboBoxItem runat="server" />
                                        </Items>
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorRace" runat="server" Enabled="False"
                                        ControlToValidate="RadComboBoxRace" InitialValue="" ValidationGroup="primaryInfo" OnInit="RequiredFieldValidator_OnInit"
                                        ErrorMessage="Missing a race." SetFocusOnError="true" Display="None" />
                                    <asp:SqlDataSource ID="SqlDataSourceRaces" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="
                                        SELECT CodeValue.Description, CodeValue.CodeValue_ID, CodeValue.Precedence
                                        FROM [CodeValue] 
                                        INNER JOIN CodeSet ON CodeSet.CodeSet_ID = CodeValue.CodeSet_ID AND CodeSet.Code = 'RACE' AND CodeSet.Client_ID = @Client_ID 
                                        WHERE CodeValue.Client_ID = @Client_ID AND CodeValue.Active = '1' 
                                        UNION
                                        SELECT CodeValue.Description, CodeValue.CodeValue_ID, CodeValue.Precedence
                                        FROM CodeValue
                                        WHERE CodeValue.Client_ID = @Client_ID
                                        AND CodeValue.CodeValue_ID IN (
                                        	SELECT CV_Race_ID 
                                        	FROM Consumer_Race CR 
                                        	WHERE CR.Client_ID = @Client_ID AND CR.Consumer_ID = @Consumer_ID
                                        )
                                        ORDER BY Precedence, CodeValue.Description"
                                        SelectCommandType="Text">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" DefaultValue="0" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String"/>
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                            </ol>
                        </fieldset>
                        <fieldset id="FieldsetIdentifiers" runat="server" class="fieldsetPlacements" style="width: 350px">
                            <legend>
                                <asp:Label runat="server" ID="Label54" Text="Identifiers" /></legend>
                            <ol>
                                <li id="MR" runat="server">
                                    <label>
                                        <asp:Label runat="server" Text="*MR#" ID="LabelMR" AssociatedControlID="ConsumerMRRadTextBox" />
                                    </label>
                                    <telerik:RadTextBox ID="ConsumerMRRadTextBox" runat="server" Enabled="true" ValidationGroup="primaryInfo" OnTextChanged="ConsumerMRRadTextBox_TextChanged" AutoPostBack="true"
                                        ClientEvents-OnValueChanged="ResetValidation" Width="180px">
                                    </telerik:RadTextBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorMR" runat="server"
                                        ControlToValidate="ConsumerMRRadTextBox" InitialValue="" ValidationGroup="primaryInfo"
                                        ErrorMessage="Missing a MR#." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="MRWarning" runat="server">
                                    <div id="MRWarningDIV" class="WarningDIV" runat="server" visible="true" style="display: none; margin-right: 20px !important;">
                                        <asp:Label ID="lblMRWarning" ForeColor="black" Visible="true" runat="server" />
                                    </div>
                                </li>
                                <li id="Medicaid" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelMedicaid" Text="medicaid:" AssociatedControlID="ConsumerMedicaidTxt" /></label>
                                    <telerik:RadTextBox ID="ConsumerMedicaidTxt" runat="server" Enabled="true" Width="180px">
                                    </telerik:RadTextBox >
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorMedicaid" runat="server" ControlToValidate="ConsumerMedicaidTxt" InitialValue=""
                                        ValidationGroup="primaryInfo" ErrorMessage="Missing a medicaid number." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="DMH" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelDMH" Text="DMH#" AssociatedControlID="ConsumerDMHTxt" ToolTip="Department of Mental Health Number" /></label>
                                    <telerik:RadTextBox ID="ConsumerDMHTxt" runat="server" CausesValidation="true" Width="180px" ClientEvents-OnValueChanged="ResetValidation"
                                         TextMode="SingleLine" AutoPostBack="true" OnTextChanged="ConsumerDMHTxt_TextChanged" />
                                </li>
                                <li id="DMHWarning" runat="server">
                                    <div id="DMHWarningDIV" class="WarningDIV" runat="server" visible="true" style="display: none; margin-right: 20px !important;">
                                        <asp:Label ID="lblDMHWarning" ForeColor="black" Visible="true" runat="server" />
                                    </div>
                                </li>
                                <li id="VR" runat="server">
                                    <label>
                                        <asp:Label runat="server" Text="*VR#" ID="LabelVR" AssociatedControlID="ConsumerVRTxt" ToolTip="Vocational Rehabilitation Number" />
                                    </label>
                                    <telerik:RadTextBox ID="ConsumerVRTxt" runat="server" Enabled="true" ValidationGroup="primaryInfo" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorVR" runat="server" ControlToValidate="ConsumerVRTxt" InitialValue=""
                                        ValidationGroup="primaryInfo" ErrorMessage="Missing a vocational rehabilitation number (VR#)." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="CNDS" runat="server" style="display: none;">
                                    <label>
                                        <asp:Label runat="server" Text="CNDS#" ID="Label49" ToolTip="Common Name Data Service (CNDS) ID Number" />
                                    </label>
                                    <telerik:RadTextBox ID="TextBoxCNDS" runat="server" Enabled="true" ValidationGroup="primaryInfo" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorCNDS" runat="server" Enabled="false" ControlToValidate="TextBoxCNDS"
                                        InitialValue="" ValidationGroup="primaryInfo" ErrorMessage="Missing a Common Name Data Service (CNDS) ID Number." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="ADSA" runat="server" style="display: none;">
                                    <label>
                                        <asp:Label runat="server" Text="ADSA#" ID="Label62" ToolTip="Aging and Disability Services Administration (ADSA) ID Number" />
                                    </label>
                                    <telerik:RadTextBox ID="TextBoxADSA" runat="server" Enabled="true" ValidationGroup="primaryInfo" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" Enabled="false" ControlToValidate="TextBoxADSA"
                                        InitialValue="" ValidationGroup="primaryInfo" ErrorMessage="Missing a Aging and Disability Services Administration (ADSA) ID Number." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="SS" runat="server">
                                    <label>
                                        <asp:Label runat="server" Text="*SS#" ID="LabelSSN" AssociatedControlID="ConsumerSSTxt"></asp:Label></label>
                                    <telerik:RadMaskedTextBox ID="ConsumerSSTxt" runat="server" Mask="###-##-####" AutoPostBack="true" OnTextChanged="ConsumerSSTxt_TextChanged"
                                        CausesValidation="true" TextMode="SingleLine" ClientEvents-OnValueChanged="ResetValidation" Width="180px"/>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorSS" runat="server" ControlToValidate="ConsumerSSTxt"
                                        ValidationGroup="primaryInfo" ErrorMessage="Missing a Social Security Number (SS#)." SetFocusOnError="true" Display="None" />
                                    <asp:RegularExpressionValidator ID="RegularExpressionValidatorSS" runat="server" ControlToValidate="ConsumerSSTxt"
                                        ValidationGroup="primaryInfo" ErrorMessage="Social Security Number is incorrectly formatted." ValidationExpression="\d{3}-\d{2}-\d{4}"
                                        SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="SSWarning" runat="server">
                                    <div id="SSWarningDIV" class="WarningDIV" runat="server" visible="true" style="display: none; margin-right: 20px !important;">
                                        <asp:Label ID="lblSSWarning" ForeColor="black" Visible="true" runat="server" />
                                    </div>
                                </li>

                                <div id="ErrorDIV" class="ErrorDIV" runat="server" Visible="True" style="display: none;">
                                    <asp:Label ID="lblError" ForeColor="black" Visible="True" runat="server"/>
                                </div>
                                <li id="AgencyIdentifiers" runat="server">
                                    <asp:Label runat="server" ID="Label55" Text="agency identifiers:" /><br />
                                    <telerik:RadGrid ID="RadGridConsumerIdentifiers" runat="server" DataSourceID="SqlDataSourceConsumerIdentifier"
                                        AllowAutomaticInserts="True" AllowAutomaticUpdates="True" AllowAutomaticDeletes="True"
                                        AllowSorting="True" AutoGenerateEditColumn="True" OnItemCommand="RadGridConsumerIdentifiers_OnItemCommand"
                                        OnItemDataBound="RadGridConsumerIdentifiers_ItemDataBound">
                                        <MasterTableView AutoGenerateColumns="False" DataKeyNames="Consumer_Identifier_ID"
                                            DataSourceID="SqlDataSourceConsumerIdentifier" CommandItemDisplay="Bottom" EditMode="EditForms">
                                            <Columns>
                                                <telerik:GridBoundColumn DataField="Consumer_Identifier_ID" DataType="System.Int32"
                                                    HeaderText="Consumer_Identifier_ID" ReadOnly="True" SortExpression="Consumer_Identifier_ID"
                                                    UniqueName="Consumer_Identifier_ID" Visible="False">
                                                </telerik:GridBoundColumn>
                                                <telerik:GridTemplateColumn HeaderText="Type" ItemStyle-Width="140px" UniqueName="ConsumerIdentifierType">
                                                    <ItemTemplate>
                                                        <%#DataBinder.Eval(Container.DataItem, "IdentifierTypeDescription")%>
                                                    </ItemTemplate>
                                                    <EditItemTemplate>
                                                        <telerik:RadComboBox runat="server" ID="RadComboBoxConsumerIdentifierType" DataTextField="IdentifierTypeDescription"
                                                            DataValueField="IdentifierType_ID" DataSourceID="SqlDataSourceIdentifierTypeIDALL"
                                                            SelectedValue='<%#Bind("IdentifierType_ID") %>' Height="100px" OnClientLoad="consumerIdentifierComboBoxDataBound">
                                                        </telerik:RadComboBox>
                                                        <asp:RequiredFieldValidator ID="RadComboBoxConsumerIdentifierTypeValidator" ControlToValidate="RadComboBoxConsumerIdentifierType"
                                                            runat="server" SetFocusOnError="true" ValidationGroup="identifiers">
                                                        </asp:RequiredFieldValidator>
                                                    </EditItemTemplate>
                                                    <InsertItemTemplate>
                                                        <telerik:RadComboBox runat="server" ID="RadComboBoxConsumerIdentifierType" DataTextField="IdentifierTypeDescription"
                                                            DataValueField="IdentifierType_ID" DataSourceID="SqlDataSourceIdentifierTypeID"
                                                            SelectedValue='<%#Bind("IdentifierType_ID") %>' Height="100px" OnClientLoad="consumerIdentifierComboBoxDataBound">
                                                        </telerik:RadComboBox>
                                                        <asp:RequiredFieldValidator ID="RadComboBoxConsumerIdentifierTypeValidator" ControlToValidate="RadComboBoxConsumerIdentifierType"
                                                            runat="server" SetFocusOnError="true" ValidationGroup="identifiers">
                                                            <span class="RequiredFieldValidator">*</span>
                                                        </asp:RequiredFieldValidator>
                                                    </InsertItemTemplate>
                                                </telerik:GridTemplateColumn>
                                                <telerik:GridTemplateColumn HeaderText="Identifier" ItemStyle-Width="240px" UniqueName="IdentifierValue">
                                                    <ItemTemplate>
                                                        <%#HttpUtility.HtmlEncode((String)DataBinder.Eval(Container.DataItem, "Value"))%>
                                                    </ItemTemplate>
                                                    <EditItemTemplate>
                                                        <telerik:RadTextBox Width="138px" ID="RadTextBoxValue" runat="server" TextMode="SingleLine"
                                                            Text='<%#Bind("Value") %>' EmptyMessage="Please enter an identifier"/>
                                                        <asp:RequiredFieldValidator ID="RadTextBoxValueValidator" ControlToValidate="RadTextBoxValue"
                                                            runat="server" SetFocusOnError="true">
                                                            <span class="RequiredFieldValidator">*</span>
                                                        </asp:RequiredFieldValidator>
                                                    </EditItemTemplate>
                                                </telerik:GridTemplateColumn>
                                                <telerik:GridTemplateColumn HeaderText="Start Date" SortExpression="StartDate" UniqueName="StartDate">
                                                    <ItemTemplate>
                                                        <%#DataBinder.Eval(Container.DataItem, "StartDate", "{0:MM/dd/yyyy}")%>
                                                    </ItemTemplate>
                                                    <EditItemTemplate>
                                                        <telerik:RadDatePicker ID="RadDatePickerStartDate" DateInput-EmptyMessage="Please enter a date"
                                                            MinDate="1753-1-1" MaxDate="2200-1-1" runat="server" DbSelectedDate='<%#Bind("StartDate") %>'
                                                            ShowPopupOnFocus="true" Width="164px">
                                                        </telerik:RadDatePicker>
                                                    </EditItemTemplate>
                                                    <InsertItemTemplate>
                                                        <telerik:RadDatePicker ID="RadDatePickerStartDate" DateInput-EmptyMessage="Please enter a date"
                                                            MinDate="1753-1-1" MaxDate="2200-1-1" runat="server" ShowPopupOnFocus="true"
                                                            DbSelectedDate='<%# Bind("StartDate") %>' Width="164px" >
                                                        </telerik:RadDatePicker>
                                                    </InsertItemTemplate>
                                                </telerik:GridTemplateColumn>
                                                <telerik:GridTemplateColumn HeaderText="End Date" SortExpression="EndDate" UniqueName="EndDate">
                                                    <ItemTemplate>
                                                        <%#DataBinder.Eval(Container.DataItem, "EndDate", "{0:MM/dd/yyyy}")%>
                                                    </ItemTemplate>
                                                    <EditItemTemplate>
                                                        <telerik:RadDatePicker ID="RadDatePickerDate" DateInput-EmptyMessage="Please enter a date"
                                                            MinDate="1753-1-1" MaxDate="2200-1-1" runat="server" DbSelectedDate='<%#Bind("EndDate") %>'
                                                            ShowPopupOnFocus="true" Width="164px" >
                                                        </telerik:RadDatePicker>
                                                    </EditItemTemplate>
                                                    <InsertItemTemplate>
                                                        <telerik:RadDatePicker ID="RadDatePickerDate" DateInput-EmptyMessage="Please enter a date"
                                                            MinDate="1753-1-1" MaxDate="2200-1-1" runat="server" ShowPopupOnFocus="true"
                                                            DbSelectedDate='<%# Bind("EndDate") %>' Width="164px" >
                                                        </telerik:RadDatePicker>
                                                    </InsertItemTemplate>
                                                </telerik:GridTemplateColumn>
                                                <%-- WARNING: Setting ButtonType to ImageButton here will cause the button to intercept enter key presses from other controls --%>
                                                <telerik:GridButtonColumn Text="Delete" CommandName="Delete" ButtonType="LinkButton" UniqueName="DeleteButton" ConfirmText="Are you sure you want to delete the selected row?">
                                                    <HeaderStyle Width="2%" />
                                                </telerik:GridButtonColumn>
                                            </Columns>
                                        </MasterTableView>
                                    </telerik:RadGrid>
                                    <asp:SqlDataSource ID="SqlDataSourceConsumerIdentifier" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommandType="StoredProcedure" SelectCommand="[dbo].[CONSUMER_IDENTIFIERS.getConsumerIdentifiersByConsumerIDAndClientIDAndNoDateFilter_1.0.0]"
                                        UpdateCommandType="StoredProcedure" UpdateCommand="[dbo].[CONSUMER_IDENTIFIERS.setConsumerIdentifier_1.0.0]"
                                        InsertCommandType="StoredProcedure" InsertCommand="[dbo].[CONSUMER_IDENTIFIERS.createConsumerIdentifier_1.0.1]"
                                        DeleteCommandType="StoredProcedure" DeleteCommand="[dbo].[CONSUMER_IDENTIFIERS.deleteConsumerIdentifierByConsumerIdentifierID_1.0.0]">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="ConsumerID" ControlID="HiddenTEMPCONSUMER_ID" Type="String" />
                                            <asp:ControlParameter Name="ClientID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                        <UpdateParameters>
                                            <asp:Parameter Name="Consumer_Identifier_ID" Type="Int32" />
                                            <asp:Parameter Name="IdentifierType_ID" Type="String" />
                                            <asp:Parameter Name="Value" Type="String" />
                                            <asp:Parameter Name="StartDate" Type="DateTime" />
                                            <asp:Parameter Name="EndDate" Type="DateTime" />
                                            <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME"
                                                Type="String" />
                                        </UpdateParameters>
                                        <InsertParameters>
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" Type="String" />
                                            <asp:Parameter Name="IdentifierType_ID" Type="String" />
                                            <asp:Parameter Name="Active" Type="Boolean" DefaultValue="TRUE" />
                                            <asp:Parameter Name="Value" Type="String" />
                                            <asp:Parameter Name="StartDate" Type="DateTime" />
                                            <asp:Parameter Name="EndDate" Type="DateTime"  />
                                            <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME" Type="String" />
                                        </InsertParameters>
                                        <DeleteParameters>
                                            <asp:Parameter Name="Consumer_Identifier_ID" Type="Int32" />
                                        </DeleteParameters>
                                    </asp:SqlDataSource>
                                    <asp:SqlDataSource ID="SqlDataSourceIdentifierTypeID" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="SELECT IdentifierType.Description AS 'IdentifierTypeDescription', IdentifierType.IdentifierType_ID AS IdentifierType_ID FROM [IdentifierType] WHERE Client_ID = @Client_ID AND Active = '1' AND ApplicableEntityCode='CONSUMER' ORDER BY Precedence">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                    <asp:SqlDataSource ID="SqlDataSourceIdentifierTypeIDALL" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="SELECT IdentifierType.Description AS 'IdentifierTypeDescription', IdentifierType.IdentifierType_ID AS IdentifierType_ID FROM [IdentifierType] WHERE Client_ID = @Client_ID  AND ApplicableEntityCode='CONSUMER' ORDER BY Precedence">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                            </ol>
                        </fieldset>
                    </div>
                    <fieldset id="FieldsetPreferences" runat="server" class="fieldsetPlacements" style="width: 380px">
                        <legend>
                            <asp:Label runat="server" ID="Label6" Text="Preferences" /></legend>
                        <ol>
                            <li id="Language" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelLanguage" Text="language:" AssociatedControlID="RadComboBoxLanguage" /></label>
                                <telerik:RadComboBox ID="RadComboBoxLanguage" AllowCustomText="false" runat="server" DataSourceID="SqlDataSourceLanguage" OnClientDropDownClosed="OnClientDropDownClosed"
                                    EmptyMessage="Search or Select..." Filter="Contains" DataValueField="Language_ID" DataTextField="Description"
                                    MarkFirstMatch="True" ToolTip="Select a Language" ValidationGroup="primaryInfo" Width="230px">
                                </telerik:RadComboBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorLanguage" runat="server" ControlToValidate="RadComboBoxLanguage"
                                    InitialValue="" Text="" ValidationGroup="primaryInfo" ErrorMessage="Missing a primary language preference." SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorLanguage" runat="server" ControlToValidate="RadComboBoxLanguage"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid primary language preference." SetFocusOnError="true" Display="None" ValidationGroup="primaryInfo" />
                                <asp:SqlDataSource ID="SqlDataSourceLanguage" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT Description, Language_ID FROM Language WHERE Client_ID = @Client_ID AND Active = 1 ORDER BY Description" SelectCommandType="Text">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="SecondaryLanguage" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelSecondaryLanguage" Text="secondary language:" AssociatedControlID="RadComboBoxSecondaryLanguage" /></label>
                                <telerik:RadComboBox ID="RadComboBoxSecondaryLanguage" AllowCustomText="false" runat="server" DataSourceID="SqlDataSourceSecondaryLanguage" OnClientDropDownClosed="OnClientDropDownClosed"
                                    EmptyMessage="Search or Select..." Filter="Contains" DataValueField="Secondary_Language_ID" DataTextField="Description"
                                    MarkFirstMatch="True" ToolTip="Select a Language" AppendDataBoundItems="true" Width="230px" OnDataBound="RadComboBox_InsertZeroOnDataBound">
                                </telerik:RadComboBox>
                                <asp:SqlDataSource ID="SqlDataSourceSecondaryLanguage" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT Description, Language_ID AS 'Secondary_Language_ID' FROM Language WHERE Client_ID = @Client_ID AND Active = 1 ORDER BY Description" SelectCommandType="Text">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="ModeOfCommunication" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelModeOfCommunication" Text="mode of communication:" AssociatedControlID="RadComboBoxModeOfCommunication" /></label>
                                <telerik:RadComboBox ID="RadComboBoxModeOfCommunication" AllowCustomText="false" runat="server" DataSourceID="SqlDataSourceModeOfCommunication"
                                    EmptyMessage="Search or Select..." Filter="Contains" DataValueField="ModeOfCommunication_ID" DataTextField="Description" OnClientDropDownClosed="OnClientDropDownClosed"
                                    MarkFirstMatch="True" ToolTip="Select a Mode" AppendDataBoundItems="true" Width="230px" OnDataBound="RadComboBox_InsertZeroOnDataBound">
                                </telerik:RadComboBox>
                                <asp:SqlDataSource ID="SqlDataSourceModeOfCommunication" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="
                            SELECT CodeValue.Description,
                            CodeValue.CodeValue_ID AS 'ModeOfCommunication_ID'
                            FROM
                            CodeValue
                            INNER JOIN CodeSet ON CodeValue.CodeSet_ID = CodeSet.CodeSet_ID
                            WHERE
                            CodeSet.Code = 'CONSUMER_MODE_OF_COMMUNICATION'
                            AND CodeValue.Client_ID = @Client_ID AND CodeValue.Active = '1' ORDER BY Precedence">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="Transportation" runat="server">
                                <label>
                                    <asp:Label ID="LabelTransportation" Text="transportation:" CssClass="label" runat="server" AssociatedControlID="RadComboBoxTransportation"></asp:Label></label>
                                <telerik:RadComboBox ID="RadComboBoxTransportation" AllowCustomText="false" runat="server" Width="230px"
                                    EmptyMessage="Search or Select..." ToolTip="Select a transportation option"
                                    DataSourceID="SqlDataSourceTransportation" DataTextField="Description"
                                    DataValueField="Transportation_ID" ValidationGroup="primaryInfo" Filter="Contains">
                                </telerik:RadComboBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorTransportation" runat="server" ControlToValidate="RadComboBoxTransportation"
                                    InitialValue="" Text="" ValidationGroup="primaryInfo" ErrorMessage="Missing a transportation preference." Enabled="false" SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorTransportation" runat="server" ControlToValidate="RadComboBoxTransportation"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate" Enabled="false"
                                    ErrorMessage="Select a valid transportation preference." SetFocusOnError="true" Display="None" ValidationGroup="primaryInfo" />
                                <asp:SqlDataSource ID="SqlDataSourceTransportation" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT Description, Transportation_ID FROM Transportation WHERE Client_ID = @Client_ID AND Active = 1 
UNION SELECT Description, Transportation.Transportation_ID FROM Transportation INNER JOIN Consumer ON Consumer.Transportation_ID = Transportation.Transportation_ID WHERE Consumer.Consumer_ID = @ConsumerID
UNION Select '', 0 ORDER BY Description"
                                    SelectCommandType="Text">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        <asp:ControlParameter Name="ConsumerID" ControlID="HiddenTEMPCONSUMER_ID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="InterpreterNeeded" runat="server">
                                <label>&nbsp;</label>
                                <asp:CheckBox ID="CheckBoxInterpreterNeeded" runat="server" Checked="false" Text="interpreter needed" CssClass="checkbox" AutoPostBack="true" OnCheckedChanged="CheckBoxInterpreterNeeded_CheckedChanged" />
                            </li>
                            <li runat="server" id="InterpreterNotes" visible="false">
                                <label>&nbsp;</label>
                                <telerik:RadTextBox ID="RadTextBoxInterpreterNotes" EmptyMessage="Interpreter notes" runat="server" TextMode="SingleLine" Visible="true"/>
                            </li>
                            <li id="BestInformant" runat="server">
                                <label>&nbsp;</label>
                                <asp:CheckBox ID="CheckBoxBestInformant" runat="server" Checked="false" Text="is best informant" CssClass="checkbox" AutoPostBack="true" OnCheckedChanged="CheckBoxBestInformant_CheckedChanged" />
                            </li>
                            <li runat="server" id="BestInformantNotes" visible="false">
                                <label>&nbsp;</label>
                                <telerik:RadTextBox ID="RadTextBoxBestInformantNote" EmptyMessage="Best informant notes" runat="server" TextMode="SingleLine" Visible="true"/>
                            </li>
                        </ol>
                    </fieldset>
                    <fieldset id="FieldsetMedicalInformation" runat="server" class="fieldsetPlacements" style="width: 380px;">
                        <legend>
                            <asp:Label runat="server" ID="Label41" Text="Medical Information" /></legend>
                        <ol>
                            <li id="PrimaryDisability" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="Label42" Text="primary disability:" /></label>
                                <telerik:RadComboBox ID="RadComboBoxPrimaryDisability" AllowCustomText="false" runat="server" Width="230px" Height="350px"
                                    ExpandDirection="Up" EmptyMessage="Search or Select..." HighlightTemplatedItems="true" Filter="Contains" OnClientDropDownClosed="OnClientDropDownClosed"
                                    MarkFirstMatch="True" ToolTip="Select a Disability" AutoPostBack="true" OnClientSelectedIndexChanged="ResetValidation" DropDownWidth="270px">
                                    <HeaderTemplate>
                                        <table>
                                            <tr>
                                                <td style="width: 150px;">Description</td>
                                                <td style="width: 80px;">ICD10</td>
                                            </tr>
                                        </table>
                                    </HeaderTemplate>
                                    <ItemTemplate>
                                        <table>
                                            <tr>
                                                <td style="width: 150px; word-wrap: normal; min-width: 150px; max-width: 150px">
                                                    <%# Eval("Description") %>
                                                </td>
                                                <td style="width: 80px; min-width: 80px; max-width: 80px;">
                                                    <%# Eval("ICD10Formatted") %>
                                                </td>
                                            </tr>
                                        </table>
                                    </ItemTemplate>
                                </telerik:RadComboBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator9" runat="server"
                                    ControlToValidate="RadComboBoxPrimaryDisability" InitialValue="" ValidationGroup="primaryInfo"
                                    ErrorMessage="Missing a primary disability." SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorPrimaryDisability" runat="server" ControlToValidate="RadComboBoxPrimaryDisability"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid primary disability." SetFocusOnError="true" Display="None" ValidationGroup="primaryInfo" />
                            </li>
                            <li id="BehavioralConcerns" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="Label50" Text="behavioral concerns:" /></label>
                                <telerik:RadTextBox  ID="BehavioralConcernsTxt" runat="server" Enabled="true" MaxLength="500" Width="230px">
                                </telerik:RadTextBox >
                            </li>
                            <li id="AcuityScore" runat="server" style="display: none;">
                                <label>
                                    <asp:Label ID="LabelAcuityScore" Text="acuity score:" CssClass="label" runat="server"></asp:Label></label>
                                <telerik:RadComboBox ID="RadComboBoxAcuityScore" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." ToolTip="Select an option" Filter="Contains"
                                    AutoPostBack="false" DataSourceID="SqlDataSourceAcuityScore" DataTextField="Description"
                                    DataValueField="CodeValue_ID" Width="230px">
                                </telerik:RadComboBox>
                                <asp:SqlDataSource ID="SqlDataSourceAcuityScore" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT CodeValue.Description, CodeValue.CodeValue_ID FROM [CodeValue] INNER JOIN CodeSet ON CodeSet.CodeSet_ID = CodeValue.CodeSet_ID AND CodeSet.Code = 'CONSUMER_ACUITY_SCORE' AND CodeSet.Client_ID = @Client_ID WHERE CodeValue.Client_ID = @Client_ID AND CodeValue.Active = '1' ORDER BY Precedence, CodeValue.Description">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li runat="server" ID="SSARecordStatus" style="display: none;">
                                <label>&nbsp;</label>
                                <asp:Checkbox runat="server" ID="SSARecordBlindnessCheckbox" AutoPostBack="False" Text="blindness on SSA record" CssClass="checkbox" />
                            </li>
                            <li runat="server" ID="SSARecordStatusDatePicker" style="display: none;">
                                date added to SSA record: <telerik:RadDatePicker runat="server" ShowPopupOnFocus="True" 
                                                                                 ID="SSARecordBlindnessDate" AutoPostBack="False"/>
                                <asp:HiddenField runat="server" ID="SSARecordID" />
                            </li>
                        </ol>
                    </fieldset>
                    <fieldset id="ConsumerStatus" runat="server" class="fieldsetPlacements" style="width: 380px;">
                        <legend>
                            <asp:Label runat="server" ID="Label39" Text="Status" /></legend>
                        <ol>
                            <li>
                                <label>&nbsp;</label>
                                <asp:CheckBox ID="ConsumerStatusChk" runat="server" Enabled="true" Checked="true" CssClass="checkbox"
                                    AutoPostBack="true" Text="active consumer" Width="200px" OnCheckedChanged="ConsumerStatus_OnCheckedChanged" onchange="ResetValidation(); return true;" />
                            </li>
                            <li>
                                <asp:Label runat="server" ID="LabelInactiveReason" Text="inactive reason:" Visible="false" />
                                <telerik:RadComboBox runat="server" ID="RadComboBoxInactiveReason" DataTextField="Description"
                                    DataValueField="Consumer_Inactive_Reason_ID" DataSourceID="SqlDataSourceActiveReasonConsumer" Filter="Contains"
                                    EmptyMessage="Search or Select..." Width="250px" AppendDataBoundItems="true" Visible="true" OnDataBound="RadComboBox_InsertZeroOnDataBound">
                                </telerik:RadComboBox>
                                 <asp:RequiredFieldValidator ID="RequiredFieldValidatorInactiveReason" runat="server" ControlToValidate="RadComboBoxInactiveReason"
                                        ErrorMessage="Missing an inactive reason." ValidationGroup="primaryInfo" SetFocusOnError="true" />
                                <asp:SqlDataSource ID="SqlDataSourceActiveReasonConsumer" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT Description, Consumer_Inactive_Reason_ID FROM [Consumer_Inactive_Reason] WHERE Client_ID = @Client_ID AND Active = '1' ORDER BY Code, Description ASC">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li>
                                <asp:Label runat="server" ID="LabelInactiveDate" Text="inactive date:" Visible="false" />
                                <telerik:RadDatePicker ID="RadDatePickerInactiveDate" Style="vertical-align: middle;"
                                    DateInput-EmptyMessage="" DateInput-CausesValidation="true" Width="80px" MinDate="1753-1-1"
                                    runat="server" AutoPostBack="false" Visible="false" ShowPopupOnFocus="true">
                                    <DatePopupButton Visible="false" />
                                    <DateInput ID="DateInput5" runat="server" CausesValidation="false" />
                                </telerik:RadDatePicker>
                            </li>
                        </ol>
                    </fieldset>
                </telerik:RadWizardStep>
                <telerik:RadWizardStep ID="RadWizardStepContactInfo" runat="server" Title="Contact Information" ToolTip="Contact Information" ValidationGroup="contactInfo">
                    <asp:ValidationSummary ID="ValidationSummaryContactInfo" runat="server" ValidationGroup="contactInfo" CssClass="ErrorDIV" DisplayMode="List" />
                    <fieldset id="FieldsetContactInformation" runat="server" class="fieldsetPlacements" style="float: left; width: 350px;">
                        <legend>
                            <asp:Label runat="server" ID="Label13" Text="Contact Information" /></legend>
                        <ol>
                            <li>
                                <asp:Label ID="Label73" runat="server"></asp:Label>&nbsp;</li>
                            <li id="InternationalAddress" runat="server">
                                <asp:Label ID="LabelIntlAddress" runat="server"></asp:Label>
                                <asp:CheckBox ID="CheckBoxIntlAddress" runat="server" Text="international address:" OnCheckedChanged="CheckBoxIntlAddress_CheckedChanged" AutoPostBack="true" onchange="ResetValidation(); return true;" />
                            </li>
                            <li>
                                <label>
                                    <asp:Label runat="server" ID="Label14" Text="*address:" AssociatedControlID="ConsumerAddressTxt" /></label>
                                <asp:TextBox ID="ConsumerAddressTxt" runat="server" ValidationGroup="contactInfo" Enabled="true" />
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="ConsumerAddressTxt"
                                    ValidationGroup="contactInfo" ErrorMessage="Missing a street address." SetFocusOnError="true" Display="None" />
                                <asp:HiddenField ID="oldAddress" runat="server"></asp:HiddenField>
                                <asp:HiddenField runat="server" ID="oldAddressID"/>
                            </li>
                            <li id="Address2" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="Label15" Text="address 2:" AssociatedControlID="ConsumerAddress2Txt" /></label>
                                <asp:TextBox ID="ConsumerAddress2Txt" runat="server" ValidationGroup="contactInfo" Enabled="true" />
                                <asp:HiddenField runat="server" ID="oldAddress2"/>
                            </li>
                            <li>
                                <label>
                                    <asp:Label runat="server" ID="Label16" Text="*city:" AssociatedControlID="ConsumerCityTxt" /></label>
                                <asp:TextBox ID="ConsumerCityTxt" runat="server" ValidationGroup="contactInfo" Enabled="true" />
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="ConsumerCityTxt" ValidationGroup="contactInfo"
                                    ErrorMessage="Missing a city." SetFocusOnError="true" Display="None" />
                                <asp:HiddenField runat="server" ID="oldCity"></asp:HiddenField>
                            </li>
                            <li id="State" runat="server" visible="true">
                                <label id="LabelState" runat="server">
                                    <asp:Label runat="server" ID="Label17" Text="*state:" AssociatedControlID="RadComboBoxState" /></label>
                                <telerik:RadComboBox ID="RadComboBoxState" OnClientLoad='DisableAutofill' AllowCustomText="false" runat="server" OnSelectedIndexChanged="RadComboBoxState_SelectedIndexChanged"
                                    EmptyMessage="Search or Select..." MarkFirstMatch="true" AppendDataBoundItems="true" AutoPostBack="true" OnClientSelectedIndexChanged="ResetValidation"
                                    ToolTip="Select a State">
                                </telerik:RadComboBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorState" runat="server" ControlToValidate="RadComboBoxState" ValidationGroup="contactInfo"
                                    ErrorMessage="Missing a state." SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorState" runat="server" ControlToValidate="RadComboBoxState"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid state." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                                <asp:HiddenField runat="server" ID="oldStateID"></asp:HiddenField>
                            </li>
                            <li id="County" runat="server" visible="true">
                                <label id="LabelCountyLabel" runat="server">
                                    <asp:Label runat="server" ID="LabelCounty" Text="*county:" AssociatedControlID="RadComboBoxCounty" /></label>
                                <telerik:RadComboBox ID="RadComboBoxCounty" OnClientLoad="DisableAutofill" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." Filter="StartsWith" ToolTip="Select a County">
                                </telerik:RadComboBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorCounty" runat="server" ControlToValidate="RadComboBoxCounty" ValidationGroup="contactInfo"
                                    ErrorMessage="Missing a county." SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorCounty" runat="server" ControlToValidate="RadComboBoxCounty"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid county." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                            </li>
                            <li id="StateProvinceRegion" runat="server" visible="false">
                                <label id="LabelStateProvinceRegion" runat="server">
                                    <asp:Label runat="server" ID="Label19" Text="*state/province/region:" AssociatedControlID="TextBoxStateProvinceRegion" /></label>
                                <asp:TextBox ID="TextBoxStateProvinceRegion" runat="server" ValidationGroup="contactInfo" />
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorStateProvinceRegion" runat="server" ControlToValidate="TextBoxStateProvinceRegion"
                                    ValidationGroup="contactInfo" ErrorMessage="Missing a state/province/region." SetFocusOnError="true" Display="None" />
                                <asp:HiddenField runat="server" ID="oldProvince"></asp:HiddenField>
                            </li>
                            <li id="ZIP" runat="server" visible="true">
                                <label runat="server">
                                    <asp:Label runat="server" ID="LabelZip" Text="*zip:" AssociatedControlID="TextBoxZIP" /></label>
                                <asp:TextBox ID="TextBoxZIP" runat="server" Enabled="true" ValidationGroup="contactInfo" />
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorZIP" runat="server" ControlToValidate="TextBoxZIP" ValidationGroup="contactInfo"
                                    ErrorMessage="Missing a ZIP code." SetFocusOnError="true" Display="None" />
                                <asp:RegularExpressionValidator ID="RegularExpressionValidatorZIP" ControlToValidate="TextBoxZIP" ValidationGroup="contactInfo"
                                    ErrorMessage="Invalid ZIP code format. Ex. 12345 or 12345-1234." runat="server" ValidationExpression="^\d{5}(\-\d{4})?$"
                                    SetFocusOnError="true" Display="None" />
                                <asp:HiddenField runat="server" ID="oldZip"></asp:HiddenField>
                            </li>
                            <li id="InternationalPostalCode" runat="server" visible="false">
                                <label id="LabelPostalcode" runat="server">
                                    <asp:Label runat="server" ID="Label44" Text="*postalcode:" AssociatedControlID="ConsumerI18NPostalCode" /></label>
                                <asp:TextBox ID="ConsumerI18NPostalCode" runat="server" Enabled="true" ValidationGroup="contactInfo"></asp:TextBox >
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorInternationalPostalCode" runat="server" ControlToValidate="ConsumerI18NPostalCode"
                                    ValidationGroup="contactInfo" ErrorMessage="Missing a postal code." SetFocusOnError="true" Display="None" />
                            </li>
                            <li id="Country" runat="server" visible="false">
                                <label id="LabelCountry" runat="server">
                                    <asp:Label runat="server" ID="Label21" Text="*country:" AssociatedControlID="RadComboBoxCountry" /></label>
                                <telerik:RadComboBox ID="RadComboBoxCountry" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." Filter="StartsWith" DataSourceID="SqlDataSourceCountry"
                                    DataTextField="Country_Long" DataValueField="Country_ID" ToolTip="Select a Country" MarkFirstMatch="true" />
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorCountry" runat="server" ControlToValidate="RadComboBoxCountry"
                                    ValidationGroup="contactInfo" ErrorMessage="Missing a country." SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorCountry" runat="server" ControlToValidate="RadComboBoxCountry"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid country." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                                <asp:SqlDataSource ID="SqlDataSourceCountry" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT Country.Country_ID, Country.Country_Long FROM [Client_Country] INNER JOIN Country ON Country.Country_ID = Client_Country.Country_ID WHERE Client_ID = @Client_ID AND Active = 1">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="Zone" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelZone" Text="zone:" AssociatedControlID="RadComboBoxZone" /></label>
                                <telerik:RadComboBox ID="RadComboBoxZone" runat="server" EmptyMessage="Search or Select..."
                                    Filter="Contains" ToolTip="Select a Zone" MarkFirstMatch="true" AllowCustomText="false" />
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorZone" runat="server" ControlToValidate="RadComboBoxZone"
                                    ValidationGroup="contactInfo" ErrorMessage="Missing a zone." SetFocusOnError="true" Display="None" />
                                <asp:CustomValidator ID="CustomValidatorZone" runat="server" ControlToValidate="RadComboBoxZone"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid zone." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                            </li>
                            <li id="EMail" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelEmail" Text="e-mail:" AssociatedControlID="RadTextBoxEMail" /></label>
                                <asp:TextBox ID="RadTextBoxEMail" runat="server" Enabled="true" ValidationGroup="contactInfo"></asp:TextBox >
                                <asp:RequiredFieldValidator ID="RequiredFieldValidatorEmail" runat="server" ControlToValidate="RadTextBoxEMail"
                                                                        ValidationGroup="contactInfo" ErrorMessage="Missing an Email." SetFocusOnError="true" Display="None" Enabled="False" />
                                <asp:RegularExpressionValidator ID="RegularExpressionValidatorEmail" ControlToValidate="RadTextBoxEMail" ValidationGroup="contactInfo"
                                    runat="server" SetFocusOnError="true" Display="None" ErrorMessage="Invalid email address."
                                    ValidationExpression="^(([A-Za-z0-9]+['._+-]*)|(['A-Za-z0-9]+['._+-]*))*[A-Za-z0-9]+@([A-Za-z0-9-]+\.)+[A-Za-z]{2,6}$" />                            </li>
                            <li>
                            <div id="ErrorPhoneDIV" class="ErrorDIV" runat="server" Visible="True" style="display: none;">
                                <asp:Label ID="lblErrorPhone" ForeColor="black" Visible="True" runat="server"/>
                            </div>
                                <asp:Label runat="server" ID="LabelConsumerPhones" Text="phone numbers:" /><br />
                                <telerik:RadGrid ID="RadGridConsumerPhones" runat="server" DataSourceID="SqlDataSourceConsumerPhones" OnDataBound="RadGridConsumerPhones_OnDataBound"
                                    AllowAutomaticInserts="True" AllowAutomaticUpdates="True" AllowAutomaticDeletes="True" OnItemCommand="RadGridConsumerPhones_OnItemCommand"
                                    AllowSorting="True" AutoGenerateEditColumn="True">
                                    <MasterTableView AutoGenerateColumns="False" DataKeyNames="Consumer_Phone_ID" DataSourceID="SqlDataSourceConsumerPhones"
                                        CommandItemDisplay="Bottom" EditMode="EditForms">
                                        <Columns>
                                            <telerik:GridBoundColumn DataField="Consumer_Phone_ID" DataType="System.Int32" HeaderText="Consumer_Phone_ID"
                                                ReadOnly="True" SortExpression="Consumer_Phone_ID" UniqueName="Consumer_Phone_ID"
                                                Visible="False">
                                            </telerik:GridBoundColumn>
                                            <telerik:GridTemplateColumn HeaderText="Type" ItemStyle-Width="140px">
                                                <ItemTemplate>
                                                    <%#DataBinder.Eval(Container.DataItem, "PhoneTypeDescription")%>
                                                </ItemTemplate>
                                                <EditItemTemplate>
                                                    <telerik:RadComboBox runat="server" ID="RadComboBoxConsumerPhoneType" DataTextField="PhoneTypeDescription"
                                                        DataValueField="PhoneType_ID" DataSourceID="SqlDataSourcePhoneTypeIDALL" SelectedValue='<%#Bind("PhoneType_ID") %>'
                                                        Height="100px">
                                                    </telerik:RadComboBox>
                                                </EditItemTemplate>
                                                <InsertItemTemplate>
                                                    <telerik:RadComboBox runat="server" ID="RadComboBoxConsumerPhoneType" DataTextField="PhoneTypeDescription"
                                                        DataValueField="PhoneType_ID" DataSourceID="SqlDataSourcePhoneTypeID" SelectedValue='<%#Bind("PhoneType_ID") %>'
                                                        Height="100px">
                                                    </telerik:RadComboBox>
                                                </InsertItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridTemplateColumn HeaderText="Phone" ItemStyle-Width="240px">
                                                <ItemTemplate>
                                                    <%#HttpUtility.HtmlEncode((String)DataBinder.Eval(Container.DataItem, "PhoneFormatted"))%>
                                                </ItemTemplate>
                                                <EditItemTemplate>
                                                    <telerik:RadMaskedTextBox Width="120px" ID="RadTextBoxValue" runat="server" Mask='<%# CheckBoxIntlAddress.Checked ? "##########" : "(###)###-####"%>'
                                                        CausesValidation="true" TextMode="SingleLine" Text='<%#Bind("Value") %>' ClientEvents-OnLoad="filterPhoneNumberPaste" />
                                                    <asp:RequiredFieldValidator ID="RadTextBoxValueValidator" ControlToValidate="RadTextBoxValue" ErrorMessage="*" runat="server" />
                                                </EditItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridTemplateColumn HeaderText="Extension" ItemStyle-Width="240px">
                                                <ItemTemplate>
                                                    <%# HttpUtility.HtmlEncode((String)DataBinder.Eval(Container.DataItem, "Extension"))%>
                                                </ItemTemplate>
                                                <EditItemTemplate>
                                                    <asp:TextBox ID="RadTextBoxExtension" TextMode="SingleLine" MaxLength="50" runat="server"
                                                        Text='<%#Bind("Extension") %>'>
                                                    </asp:TextBox >
                                                </EditItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <%-- WARNING: Setting ButtonType to ImageButton here will cause the button to intercept enter key presses from other controls --%>
                                            <telerik:GridButtonColumn Text="Delete" CommandName="Delete" ButtonType="LinkButton" UniqueName="DeleteButton" 
                                                ConfirmDialogType="RadWindow" ConfirmText="Are you sure you want to delete the selected phone number?">
                                                <HeaderStyle Width="2%" />
                                            </telerik:GridButtonColumn>
                                        </Columns>
                                    </MasterTableView>
                                </telerik:RadGrid>
                                <asp:SqlDataSource ID="SqlDataSourceConsumerPhones" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT Consumer_Phone.Consumer_Phone_ID, Consumer_Phone.PhoneType_ID, Consumer_Phone.Consumer_ID, PhoneType.Description AS 'PhoneTypeDescription', dbo.udf_FormatPhoneNumber2(Consumer_Phone.Value, @IsIntl_Address, CHARINDEX('CELL', PhoneType.Code, 0)) AS 'PhoneFormatted', Consumer_Phone.Value, CASE WHEN Consumer_Phone.Extension IS NULL THEN '' ELSE Consumer_Phone.Extension END AS Extension FROM Consumer_Phone INNER JOIN PhoneType ON Consumer_Phone.PhoneType_ID = PhoneType.PhoneType_ID WHERE Consumer_Phone.Consumer_ID = @ConsumerID ORDER BY Precedence"
                                    SelectCommandType="Text" UpdateCommand="UPDATE [Consumer_Phone] SET [PhoneType_ID] = @PhoneType_ID, [Value] = @Value, [Extension] = @Extension, [lst_update_username] = @lst_update_username, [lst_update_timestamp] = getDate() WHERE [Consumer_Phone_ID] = @Consumer_Phone_ID"
                                    UpdateCommandType="Text" InsertCommand="INSERT INTO [Consumer_Phone] ([Consumer_ID], [PhoneType_ID],[Active],[Value],[Extension],[lst_update_username],[lst_update_timestamp]) VALUES (@ConsumerID, @PhoneType_ID, @Active, @Value, @Extension, @lst_update_username, getDate())"
                                    InsertCommandType="Text" DeleteCommand="DELETE FROM [Consumer_Phone] WHERE [Consumer_Phone_ID] = @Consumer_Phone_ID"
                                    DeleteCommandType="Text">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="ConsumerID" ControlID="HiddenTEMPCONSUMER_ID" Type="String" />
                                        <asp:ControlParameter Name="IsIntl_Address" ControlID="CheckBoxIntlAddress" Type="Boolean" />
                                    </SelectParameters>
                                    <UpdateParameters>
                                        <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME"
                                            Type="String" />
                                    </UpdateParameters>
                                    <InsertParameters>
                                        <asp:ControlParameter Name="ConsumerID" ControlID="HiddenTEMPCONSUMER_ID" Type="String" />
                                        <asp:Parameter Name="Active" Type="Boolean" DefaultValue="TRUE" />
                                        <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME"
                                            Type="String" />
                                    </InsertParameters>
                                </asp:SqlDataSource>
                                <asp:SqlDataSource ID="SqlDataSourcePhoneTypeID" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT PhoneType.Description AS 'PhoneTypeDescription', PhoneType.PhoneType_ID AS PhoneType_ID FROM [PhoneType] WHERE Client_ID = @Client_ID AND Active = '1' AND ApplicableEntityCode='CONSUMER' ORDER BY Precedence">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                                <asp:SqlDataSource ID="SqlDataSourcePhoneTypeIDALL" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT PhoneType.Description AS 'PhoneTypeDescription', PhoneType.PhoneType_ID AS PhoneType_ID FROM [PhoneType] WHERE Client_ID = @Client_ID  AND ApplicableEntityCode='CONSUMER' ORDER BY Precedence">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                        </ol>
                    </fieldset>
                    <fieldset id="secondAddressBox" runat="server" class="fieldsetPlacements" style="float: left; width: 350px;">
                        <legend>
                            <asp:Label runat="server" ID="Label8" Text="Secondary Address" /></legend>
                        <ol>
                            <li>
                                <label>
                                    <asp:Label runat="server" ID="Label72" Text="address description:" AssociatedControlID="SecondAddressDescriptionTxt" /></label>
                                <asp:TextBox ID="SecondAddressDescriptionTxt" runat="server" ValidationGroup="contactInfo" Enabled="true" />
                                <asp:CustomValidator ID="CustomValidatorSecondAddressDescription" runat="server" ControlToValidate="SecondAddressDescriptionTxt"
                                    OnServerValidate="CustomValidatorTextBox_ServerValidate" ClientValidationFunction="validateTextBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing an address description for secondary address." SetFocusOnError="true" Display="none" ValidationGroup="contactInfo" />
                            </li>
                            <li id="SecondIntlAddress" runat="server">
                                <asp:Label ID="LabelSecondIntlAddress" runat="server"></asp:Label>
                                <asp:CheckBox ID="CheckBoxSecondIntlAddress" runat="server" Text="international address:" OnCheckedChanged="CheckBoxSecondIntlAddress_CheckedChanged" AutoPostBack="true" onchange="ResetValidation(); return true;" />
                            </li>
                            <li>
                                <label>
                                    <asp:Label runat="server" ID="Label63" Text="address:" AssociatedControlID="ConsumerSecondAddressTxt" /></label>
                                <asp:TextBox ID="ConsumerSecondAddressTxt" runat="server" ValidationGroup="contactInfo" Enabled="true" />
                                <asp:CustomValidator ID="CustomValidatorConsumerSecondAddressTxt" runat="server" ControlToValidate="ConsumerSecondAddressTxt"
                                    OnServerValidate="CustomValidatorTextBox_ServerValidate" ClientValidationFunction="validateTextBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a street address for secondary address." SetFocusOnError="true" Display="none" ValidationGroup="contactInfo" />
                            </li>
                            <li id="SecondAddress2" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="Label64" Text="address 2:" AssociatedControlID="ConsumerSecondAddress2Txt" /></label>
                                <asp:TextBox ID="ConsumerSecondAddress2Txt" runat="server" ValidationGroup="contactInfo" Enabled="true" />
                            </li>
                            <li>
                                <label>
                                    <asp:Label runat="server" ID="Label65" Text="city:" AssociatedControlID="ConsumerSecondCityTxt" /></label>
                                <asp:TextBox ID="ConsumerSecondCityTxt" runat="server" ValidationGroup="contactInfo" Enabled="true" />
                                <asp:CustomValidator ID="CustomValidatorConsumerSecondCityTxt" runat="server" ControlToValidate="ConsumerSecondCityTxt"
                                    OnServerValidate="CustomValidatorTextBox_ServerValidate" ClientValidationFunction="validateTextBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a city for secondary address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                            </li>
                            <li id="SecondState" runat="server" visible="true">
                                <label id="LabelSecondState" runat="server">
                                    <asp:Label runat="server" ID="Label66" Text="state:" AssociatedControlID="RadComboBoxSecondState" /></label>
                                <telerik:RadComboBox ID="RadComboBoxSecondState" AllowCustomText="false" runat="server" OnSelectedIndexChanged="RadComboBoxSecondState_SelectedIndexChanged"
                                    EmptyMessage="Search or Select..." MarkFirstMatch="true" AppendDataBoundItems="true" OnClientSelectedIndexChanged="ResetValidation"
                                    ToolTip="Select a State" AutoPostBack="true">
                                </telerik:RadComboBox>
                                <asp:CustomValidator ID="CustomValidatorEmptySecondState" runat="server" ControlToValidate="RadComboBoxSecondState"
                                    OnServerValidate="CustomValidatorEmptyRadComboBox_ServerValidate" ClientValidationFunction="validateComboBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a state for second address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                                <asp:CustomValidator ID="CustomValidatorSecondState" runat="server" ControlToValidate="RadComboBoxSecondState"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid state for secondary address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                            </li>
                            <li id="SecondCounty" runat="server" visible="true">
                                <label id="LabelSecondCountyLabel" runat="server">
                                    <asp:Label runat="server" ID="LabelSecondCounty" Text="county:" AssociatedControlID="RadComboBoxSecondCounty" /></label>
                                <telerik:RadComboBox ID="RadComboBoxSecondCounty" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." Filter="StartsWith" ToolTip="Select a County" />
                                <asp:CustomValidator ID="CustomValidatorEmptySecondCounty" runat="server" ControlToValidate="RadComboBoxSecondCounty"
                                    OnServerValidate="CustomValidatorEmptyRadComboBox_ServerValidate" ClientValidationFunction="validateComboBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a county for second address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                                <asp:CustomValidator ID="CustomValidatorSecondCounty" runat="server" ControlToValidate="RadComboBoxSecondCounty"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid county for secondary address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                            </li>
                            <li id="SecondStateProvinceRegion" runat="server" visible="false">
                                <label id="LabelSecondStateProvinceRegion" runat="server">
                                    <asp:Label runat="server" ID="Label68" Text="state/province/region:" AssociatedControlID="TextBoxSecondStateProvinceRegion" /></label>
                                <asp:TextBox ID="TextBoxSecondStateProvinceRegion" runat="server" ValidationGroup="contactInfo" />
                                <asp:CustomValidator ID="CustomValidatorEmptySecondStateProvinceRegion" runat="server" ControlToValidate="TextBoxSecondStateProvinceRegion"
                                    OnServerValidate="CustomValidatorTextBox_ServerValidate" ClientValidationFunction="validateTextBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a State/Province/Region for secondary address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                            </li>
                            <li id="SecondZIP" runat="server" visible="true">
                                <label id="LabelSecondZIP" runat="server">
                                    <asp:Label runat="server" ID="Label69" Text="zip:" AssociatedControlID="ConsumerSecondZIP" /></label>
                                <asp:TextBox ID="ConsumerSecondZip" runat="server" Enabled="true" ValidationGroup="contactInfo" />
                                <asp:CustomValidator ID="CustomValidatorSecondZIP" runat="server" ControlToValidate="ConsumerSecondZip"
                                    OnServerValidate="CustomValidatorTextBox_ServerValidate" ClientValidationFunction="validateTextBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a ZIP code for second address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                                <asp:RegularExpressionValidator ID="RegularExpressionValidatorSecondZIP" ControlToValidate="ConsumerSecondZIP" ValidationGroup="contactInfo"
                                    ErrorMessage="Invalid ZIP code format for secondary address." runat="server" ValidationExpression="^\d{5}(\-\d{4})?$"
                                    SetFocusOnError="true" Display="None" />
                            </li>
                            <li id="SecondInternationalPostalCode" runat="server" visible="false">
                                <label id="LabelSecondPostalcode" runat="server">
                                    <asp:Label runat="server" ID="Label70" Text="postalcode:" AssociatedControlID="ConsumerI18NSecondPostalCode" /></label>
                                <asp:TextBox ID="ConsumerI18NSecondPostalCode" runat="server" Enabled="true" ValidationGroup="contactInfos"></asp:TextBox >
                                <asp:CustomValidator ID="CustomValidatorConsumerI18NSecondPostalCode" runat="server" ControlToValidate="ConsumerI18NSecondPostalCode"
                                    OnServerValidate="CustomValidatorTextBox_ServerValidate" ClientValidationFunction="validateTextBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a postal code for secondary address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                            </li>
                            <li id="SecondCountry" runat="server" visible="false">
                                <label id="LabelSecondCountry" runat="server">
                                    <asp:Label runat="server" ID="Label71" Text="country:" AssociatedControlID="RadComboBoxSecondCountry" /></label>
                                <telerik:RadComboBox ID="RadComboBoxSecondCountry" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." Filter="StartsWith" DataSourceID="SqlDataSourceSecondCountry"
                                    DataTextField="Country_Long" DataValueField="Country_ID" ToolTip="Select a Country" MarkFirstMatch="true" />
                                <asp:CustomValidator ID="CustomValidatorEmptySecondCountry" runat="server" ControlToValidate="RadComboBoxSecondCountry"
                                    OnServerValidate="CustomValidatorEmptyRadComboBox_ServerValidate" ClientValidationFunction="validateComboBoxSecondAddress" ValidateEmptyText="true"
                                    ErrorMessage="Missing a country for secondary address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                                <asp:CustomValidator ID="CustomValidatorSecondCountry" runat="server" ControlToValidate="RadComboBoxSecondCountry"
                                    ClientValidationFunction="validateCombo" OnServerValidate="CustomValidatorRadComboBox_ServerValidate"
                                    ErrorMessage="Select a valid country for secondary address." SetFocusOnError="true" Display="None" ValidationGroup="contactInfo" />
                                <asp:SqlDataSource ID="SqlDataSourceSecondCountry" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT Country.Country_ID, Country.Country_Long FROM [Client_Country] INNER JOIN Country ON Country.Country_ID = Client_Country.Country_ID WHERE Client_ID = @Client_ID AND Active = 1">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                        </ol>
                    </fieldset>
                    <fieldset id="fsAssignedStaff" runat="server" class="fieldsetPlacements" style="width: 350px">
                        <legend>
                            <asp:Label runat="server" ID="LabelfsAssignedStaff" Text="Assigned Staff" /></legend>
                        <ol>
                            <li id="AssignedStaff" runat="server">
                                <%-- WARNING: if other controls are added to this fieldset, the delete button in this grid will need to be changed to an LinkButton (see other warning comments on this page) --%>
                                <sw:AssignedStaffGrid ID="AssignedStaffGrid1" runat="server" ValidationGroup="contactInfo" />
                            </li>
                            <li id="FundingSourceContacts" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelFundingSourceContacts" Text="funding source contact:"></asp:Label></label>

                                <telerik:RadComboBox ID="RadComboBoxFundingSourceContacts" AllowCustomText="false" runat="server"
                                    Filter="Contains" EmptyMessage="Search or Select..." ValidationGroup="contactInfo" OnClientDropDownClosed="OnClientDropDownClosed"
                                    ToolTip="Select an funding source contact" AutoPostBack="false" DataSourceID="SqlDataSourceFundingSourceContact"
                                    DataTextField="Description" DataValueField="FundingSource_Contact_ID" AppendDataBoundItems="true" OnDataBound="RadComboBox_InsertZeroOnDataBound">
                                </telerik:RadComboBox>
                                <asp:SqlDataSource ID="SqlDataSourceFundingSourceContact" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT LastName + ', ' + FirstName AS 'Description', FundingSource_Contact_ID FROM FundingSource_Contact WHERE Client_ID = @Client_ID ORDER BY Description"
                                    SelectCommandType="Text">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                        </ol>
                    </fieldset>
                </telerik:RadWizardStep>
                <telerik:RadWizardStep ID="RadWizardStepAddlDemographics" runat="server" Title="Housing/Education/Legal" ToolTip="Housing/Education/Legal" ValidationGroup="addlDemographics">
                    <asp:ValidationSummary ID="ValidationSummaryAddlDemographics" runat="server" ValidationGroup="addlDemographics" CssClass="ErrorDIV" DisplayMode="List" />
                    <div style="float: left;">
                        <fieldset id="FieldsetHousing" runat="server" class="fieldsetPlacements" style="width: 350px;">
                            <legend>
                                <asp:Label runat="server" ID="Label56" Text="Housing" /></legend>
                            <ol>
                                <li id="ResidenceType" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelResidenceType" Text="residence type:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxResidenceType" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." ToolTip="Select a Residence Type" Filter="Contains"
                                        AutoPostBack="false" DataSourceID="SqlDataSourceResidenceType" DataTextField="Description"
                                        DataValueField="ResidenceType_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorResidenceType" runat="server" ControlToValidate="RadComboBoxResidenceType" Enabled="False" OnInit="RequiredFieldValidator_OnInit"
                                        ValidationGroup="addlDemographics" ErrorMessage="Missing residence type." SetFocusOnError="true" Display="None" />
                                    <asp:SqlDataSource ID="SqlDataSourceResidenceType" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[RESIDENCETYPE.getResidenceTypesByClientIDAndActive_1.0.1]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="Housing" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelHousingType" Text="housing:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxHousingType" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." ToolTip="Select a Housing option" Filter="Contains"
                                        AutoPostBack="false" DataSourceID="SqlDataSourceHousing" DataTextField="Description" ValidationGroup="addlDemographics"
                                        DataValueField="Housing_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorHousingType" runat="server" ControlToValidate="RadComboBoxHousingType" Enabled="False" OnInit="RequiredFieldValidator_OnInit"
                                        ValidationGroup="addlDemographics" ErrorMessage="Missing housing." SetFocusOnError="true" Display="None" />
                                    <asp:SqlDataSource ID="SqlDataSourceHousing" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[HOUSING.getHousingByClientIDAndActive_1.0.1]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="HouseholdIncome" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelHouseholdIncome" Text="household income:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxHouseholdIncome" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." ToolTip="Select a Household Income" Filter="Contains" ValidationGroup="addlDemographics"
                                        AutoPostBack="false" DataSourceID="SqlDataSourceHouseholdIncome" DataTextField="Description"
                                        DataValueField="CodeValue_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorHouseholdIncome" runat="server" ControlToValidate="RadComboBoxHouseholdIncome" Enabled="False" OnInit="RequiredFieldValidator_OnInit"
                                                                            ValidationGroup="addlDemographics" ErrorMessage="Missing household income." SetFocusOnError="true" Display="None" />
                                    <asp:SqlDataSource ID="SqlDataSourceHouseholdIncome" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[HOUSING.getHouseholdIncomesByClientIDAndActive_1.0.0]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="FamilyType" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelFamilyType" Text="family type:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxFamilyType" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." ToolTip="Select a Family type" Filter="Contains"
                                        AutoPostBack="false" DataSourceID="SqlDataSourceFamilyType" DataTextField="Description"
                                        DataValueField="Familytype_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:SqlDataSource ID="SqlDataSourceFamilyType" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[FAMILYTYPE.getFamilytypeByClientIDAndActive_1.0.1]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="ConsideringMarriage" runat="server" style="display:none;">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxConsideringMarriage" runat="server" Checked="false" Text="considering marriage" CssClass="checkbox" />
                                </li>
                                <li id="FamilySize" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="Label29" Text="family size:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxFamilySize" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." ToolTip="Select a Family size" Filter="Contains"
                                        AutoPostBack="false" DataSourceID="SqlDataSourceFamilySize" DataTextField="Description"
                                        DataValueField="Familysize_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:SqlDataSource ID="SqlDataSourceFamilySize" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[FAMILYSIZE.getFamilysizeByClientIDAndActive_1.0.1]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="GuardianshipStatus" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelGuardianshipStatus" Text="guardianship status:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxGuardianshipStatus" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." ToolTip="Select a Guardianship Status option" Filter="Contains"
                                        AutoPostBack="false" DataSourceID="SqlDataSourceGuardianshipStatus" DataTextField="Description"
                                        DataValueField="Guardianship_Status_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorGuardianshipStatus" runat="server" ControlToValidate="RadComboBoxGuardianshipStatus" Enabled="False" OnInit="RequiredFieldValidator_OnInit"
                                                                    ValidationGroup="addlDemographics" ErrorMessage="Missing guardianship status." SetFocusOnError="true" Display="None" />
                                    <asp:SqlDataSource ID="SqlDataSourceGuardianshipStatus" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="
                                            SELECT Guardianship_Status.Description,
                                            Guardianship_Status.Guardianship_Status_ID
                                            FROM
                                            Guardianship_Status
                                            WHERE
                                            Guardianship_Status.Client_ID = @Client_ID AND Guardianship_Status.Active = '1' ORDER BY Description">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="IsOwnGuardian" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxOwnGuardian" runat="server" Checked="false" Text="is own guardian" CssClass="checkbox" />
                                </li>
                            </ol>
                        </fieldset>
                        <fieldset id="FieldsetEducation" runat="server" class="fieldsetPlacements" style="width: 350px;">
                            <legend>
                                <asp:Label runat="server" ID="Label26" Text="Education" /></legend>
                            <ol>
                                <li id="CompletedEducationLevel" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelCompletedEducationLevel" Text="completed education level:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxEducation" AllowCustomText="false" runat="server"
                                        Filter="Contains" EmptyMessage="Search or Select..." OnClientDropDownClosed="OnClientDropDownClosed"
                                        ToolTip="Select an education level" AutoPostBack="false" DataSourceID="SqlDataSourceEducation"
                                        DataTextField="Description" DataValueField="Education_ID" AppendDataBoundItems="true" OnDataBound="RadComboBox_InsertZeroOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorCompletedEducationLevel" runat="server" Enabled="False"
                                        ControlToValidate="RadComboBoxEducation" InitialValue="" ValidationGroup="addlDemographics" OnInit="RequiredFieldValidator_OnInit"
                                        ErrorMessage="Missing completed education level." SetFocusOnError="true" Display="None" />
                                    <asp:SqlDataSource ID="SqlDataSourceEducation" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="SELECT Description, Education_ID FROM Education WHERE Client_ID = @Client_ID AND Education.Active = 1 ORDER BY Description"
                                        SelectCommandType="Text">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="Certification" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelCertification" Text="certification:" /></label>
                                    <telerik:RadTextBox ID="RadTextBoxCertification" runat="server" Enabled="true">
                                    </telerik:RadTextBox >
                                </li>
                            </ol>
                        </fieldset>
                    </div>
                    <div style="float: left;">
                        <fieldset id="FieldsetLegal" runat="server" class="fieldsetPlacements" style="width: 350px;">
                            <legend>
                                <asp:Label runat="server" ID="Label58" Text="Legal" /></legend>
                            <ol>
                                <li id="LegalMatters" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="ChkConsumerLegalMatters" AutoPostBack="true" runat="server" Enabled="true" Checked="false" CssClass="checkbox" OnCheckedChanged="CheckBoxLegalMatters_CheckedChanged"
                                        Text="legal matter(s)" onchange="ResetValidation(); return true;" />
                                </li>
                                <li runat="server" id="LegalMattersNotes" visible="false">
                                    <label>&nbsp;</label>
                                    <telerik:RadTextBox ID="RadTextBoxLegalMattersNotes" EmptyMessage="Enter explanation of legal matters" runat="server" TextMode="SingleLine" Visible="true"/>
                                </li>
                                <li id="Felon" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxFelon" runat="server" Enabled="true" Checked="false" CssClass="checkbox" AutoPostBack="true" OnCheckedChanged="CheckBoxFelon_CheckedChanged"
                                        Text="is a felon" onchange="ResetValidation(); return true;" />
                                </li>
                                <li runat="server" id="FelonNotes" visible="false">
                                    <label>&nbsp;</label>
                                    <telerik:RadTextBox ID="RadTextBoxFelonNotes" EmptyMessage="Enter explanation of felon status" runat="server" TextMode="SingleLine" Visible="true"/>
                                </li>
                                <li id="NoHold" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxNoHold" runat="server" Enabled="true" Checked="false" CssClass="checkbox" AutoPostBack="true" OnCheckedChanged="CheckBoxNoHold_CheckedChanged"
                                        Text="has a no hold" onchange="ResetValidation(); return true;" />
                                </li>
                                <li id="NoHoldNotes" runat="server" visible="false">
                                    <label>&nbsp;</label>
                                    <telerik:RadTextBox ID="RadTextBoxNoHoldNotes" EmptyMessage="Enter explanation of no hold status" runat="server" TextMode="SingleLine" Visible="true"/>
                                </li>
                                <li id="SexOffender" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxSexOffender" runat="server" Enabled="true" Checked="false" CssClass="checkbox" AutoPostBack="true" OnCheckedChanged="CheckBoxSexOffender_CheckedChanged"
                                        Text="is a sex offender" onchange="ResetValidation(); return true;" />
                                </li>
                                <li runat="server" id="SexOffenderNotes" visible="false">
                                    <label>&nbsp;</label>
                                    <telerik:RadTextBox ID="RadTextBoxSexOffenderNotes" EmptyMessage="Enter explanation of sex offender status" runat="server" TextMode="SingleLine" Visible="true"/>
                                </li>
                            </ol>
                        </fieldset>
                        <fieldset class="fieldsetPlacements" style="width: 350px;" id="AdditionalDemographics" runat="server">
                            <legend>
                                <asp:Label runat="server" ID="AdditionalDetailsHeader" Text="Additional Demographics" /></legend>
                            <ol>
                                <li id="AnnualReview" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelAnnualReview" Text="annual review:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxAnnualReview" AllowCustomText="false" runat="server"
                                        EmptyMessage="Search or Select..." OnClientDropDownClosed="OnClientDropDownClosed" Filter="Contains"
                                        ToolTip="Select an annual review month" AutoPostBack="false"> 
                                        <Items>
                                            <telerik:RadComboBoxItem Text="" Value="-1" />
                                            <telerik:RadComboBoxItem Text="January" Value="0" />
                                            <telerik:RadComboBoxItem Text="February" Value="1" />
                                            <telerik:RadComboBoxItem Text="March" Value="2" />
                                            <telerik:RadComboBoxItem Text="April" Value="3" />
                                            <telerik:RadComboBoxItem Text="May" Value="4" />
                                            <telerik:RadComboBoxItem Text="June" Value="5" />
                                            <telerik:RadComboBoxItem Text="July" Value="6" />
                                            <telerik:RadComboBoxItem Text="August" Value="7" />
                                            <telerik:RadComboBoxItem Text="September" Value="8" />
                                            <telerik:RadComboBoxItem Text="October" Value="9" />
                                            <telerik:RadComboBoxItem Text="November" Value="10" />
                                            <telerik:RadComboBoxItem Text="December" Value="11" />
                                        </Items>
                                    </telerik:RadComboBox>
                                </li>
                                <li id="MedsAdministrationNeeded" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxMedsAdministrationNeeded" runat="server" Checked="false" Text="meds administration needed" CssClass="checkbox" />
                                </li>
                                <li id="Membership" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxMembership" runat="server" Enabled="true" Checked="false" CssClass="checkbox"
                                        Text="membership" AutoPostBack="true" OnCheckedChanged="CheckBoxMembership_CheckedChanged" onchange="ResetValidation(); return true;" />
                                </li>
                                <li id="MembershipExpiration" runat="server" visible="false">
                                    <label>&nbsp;</label>
                                    <telerik:RadDatePicker ID="RadDatePickerMembershipExpiration" Style="vertical-align: middle;" CssClass="raddatepicker"
                                        DateInput-EmptyMessage="expiration" Width="80px" ShowPopupOnFocus="true"
                                        MinDate="1753-1-1" runat="server" AutoPostBack="false">
                                        <DatePopupButton Visible="false" />
                                        <DateInput ID="DateInput3" runat="server" />
                                    </telerik:RadDatePicker>
                                </li>
                                <li id="PhotoConsent" runat="server">
                                    <label>&nbsp;</label>
                                    <asp:CheckBox ID="CheckBoxPhotoConsent" runat="server" Enabled="true" Checked="false" CssClass="checkbox"
                                        Text="photo consent" AutoPostBack="true" OnCheckedChanged="CheckBoxPhotoConsent_CheckedChanged" onchange="ResetValidation(); return true;" />
                                </li>
                                <li id="PhotoConsentExpiration" runat="server" visible="false">
                                    <label>&nbsp;</label>
                                    <telerik:RadDatePicker ID="RadDatePickerPhotoConsentExpiration" CssClass="raddatepicker" Width="80px"
                                        DateInput-EmptyMessage="expiration" ShowPopupOnFocus="true"
                                        MinDate="1753-1-1" runat="server" AutoPostBack="false">
                                        <DatePopupButton Visible="false" />
                                        <DateInput ID="DateInput2" runat="server" />
                                    </telerik:RadDatePicker>
                                </li>
                                <li id="VoterStatus" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelVotingStatus" Text="voting status:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxVotingStatus" AllowCustomText="false" runat="server"
                                        Filter="StartsWith" EmptyMessage="Search or Select..." OnClientDropDownClosed="OnClientDropDownClosed"
                                        ToolTip="Select a voting status" AutoPostBack="false" DataSourceID="SqlDataSourceVotingStatus" AppendDataBoundItems="true"
                                        DataTextField="Description" DataValueField="CodeValue_ID">
                                    </telerik:RadComboBox>
                                    <asp:SqlDataSource ID="SqlDataSourceVotingStatus" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[CODE_VALUE.getCodeValueByCodeSetCodeAndClientID_1.0.4]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="Int32" />
                                            <asp:Parameter Name="CodeSetCode" DefaultValue="VOTER_REGISTRATION_STATUS" Type="String" />
                                            <asp:Parameter Name="ActiveCodeValue" DefaultValue="1"/>
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="VeteranStatus" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelVeteranStatus" Text="veteran status:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxVeteranStatus" AllowCustomText="false" runat="server"
                                                         Filter="Contains" EmptyMessage="Search or Select..." OnClientDropDownClosed="OnClientDropDownClosed"
                                                         ToolTip="Select veteran status" AutoPostBack="false" DataSourceID="SqlDataSourceVeteranStatus" AppendDataBoundItems="true"
                                                         DataTextField="Description" DataValueField="CodeValue_ID" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:SqlDataSource ID="SqlDataSourceVeteranStatus" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                                       SelectCommand="[CODE_VALUE.getCodeValueByCodeSetCodeAndClientID_1.0.4]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="Int32" />
                                            <asp:Parameter Name="CodeSetCode" DefaultValue="VETERAN_STATUS" Type="String" />
                                            <asp:Parameter Name="ActiveCodeValue" DefaultValue="1"/>
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="LiCitizenshipStatus" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelCitizenshipStatus" Text="citizenship status:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxCitizenshipStatus" AllowCustomText="false" runat="server"
                                                         Filter="Contains" EmptyMessage="Search or Select..." OnClientDropDownClosed="OnClientDropDownClosed"
                                                         ToolTip="Select citizenship status" AutoPostBack="false" DataSourceID="SqlDataSourceCitizenshipStatus" AppendDataBoundItems="true"
                                                         DataTextField="Description" DataValueField="CodeValue_ID" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                    </telerik:RadComboBox>
                                    <asp:SqlDataSource ID="SqlDataSourceCitizenshipStatus" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                                       SelectCommand="[CODE_VALUE.getCodeValueByCodeSetCodeAndClientID_1.0.4]" SelectCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="Int32" />
                                            <asp:Parameter Name="CodeSetCode" DefaultValue="CITIZENSHIP_STATUS" Type="String" />
                                            <asp:Parameter Name="ActiveCodeValue" DefaultValue="1"/>
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                            </ol>
                        </fieldset>
                    </div> 
                    <div style="float: left;">
                        <fieldset class="fieldsetPlacements" style="width: 600px;display:none" runat="server" ID="FamilyBenefitStatus">
                            <legend>Family Benefit Status</legend>
                            <ol>
                                <li runat="server" ID="SpouseBenefitStatus">
                                    <asp:Checkbox runat="server" ID="SpouseReceivesBenefits" Width="200px" Text="spouse receives benefits" AutoPostBack="False" CssClass="checkbox" />
                                    <br>
                                    notes: <br />
                                    <telerik:RadTextBox ID="SpouseBenefitNotes" runat="server" TextMode="MultiLine" Height="50px" Width="460px" AutoPostBack="False" />
                                    <asp:HiddenField runat="server" ID="SpouseBenefitStatusID" />
                                </li>
                                <li runat="server" ID="ChildBenefitStatus">
                                    children: <br />
                                    <telerik:RadGrid ID="ChildBenefitGrid" AutoGenerateEditButton="True" runat="server" 
                                                     DataSourceID="SQLDataSourceChildBenefits" Width="600px"
                                                     AllowPaging="True" AutoGenerateColumns="False" AllowSorting="True" 
                                                     AllowAutomaticInserts="True" AllowAutomaticUpdates="True" OnItemCommand="ChildBenefitGrid_OnItemCommand"
                                                     AllowAutomaticDeletes="True">
                                        
                                        <PagerStyle Mode="NextPrevAndNumeric"></PagerStyle>
                                        <MasterTableView DataKeyNames="ConsumerRelationBenefitStatus_ID" 
                                                         CommandItemDisplay="Bottom" ClientDataKeyNames="ConsumerRelationBenefitStatus_ID"
                                                         DataSourceID="SQLDataSourceChildBenefits" TableLayout="Auto" CellSpacing="-1"
                                                         InsertItemPageIndexAction="ShowItemOnCurrentPage">
                                            <Columns>
                                                <telerik:GridBoundColumn DataField="ConsumerRelationBenefitStatus_ID" Visible="False" ReadOnly="True" />
                                                <telerik:GridNumericColumn DataField="Age" UniqueName="Age" HeaderText="Age" NumericType="Number" MinValue="0"/>
                                                <telerik:GridCheckBoxColumn DataField="ReceivingBenefits" UniqueName="ReceivingBenefits" HeaderText="Receiving Benefits"/>
                                                <telerik:GridEditCommandColumn ButtonType="LinkButton" UniqueName="EditCommandColumn">
                                                    <HeaderStyle Width="50px"></HeaderStyle>
                                                </telerik:GridEditCommandColumn>
                                                <telerik:GridButtonColumn ButtonType="ImageButton" Text="Delete" CommandName="Delete" UniqueName="DeleteButton"
                                                                          ConfirmText="Are you sure you want to delete the item?" />
                                            </Columns>
                                        </MasterTableView>
                                    </telerik:RadGrid>
                                    <asp:SqlDataSource runat="server" ID="SQLDataSourceChildBenefits" 
                                           ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>" 
                                           SelectCommand="[CONSUMER_RELATION_BENEFIT_STATUS.getConsumerRelationBenefitStatusByID_1.0.0]" SelectCommandType="StoredProcedure"
                                           UpdateCommand="[CONSUMER_RELATION_BENEFIT_STATUS.setConsumerRelationBenefitStatus_1.0.0]" UpdateCommandType="StoredProcedure"
                                           InsertCommand="[CONSUMER_RELATION_BENEFIT_STATUS.createConsumerRelationBenefitStatus_1.0.0]" InsertCommandType="StoredProcedure" 
                                           DeleteCommand="[CONSUMER_RELATION_BENEFIT_STATUS.deleteConsumerRelationBenefitStatusByID_1.0.1]" DeleteCommandType="StoredProcedure">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String"  />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenCONSUMER_ID" Type="String"/>
                                            <asp:Parameter Name="RelationType" Type="String" DefaultValue="Child" />
                                        </SelectParameters>
                                        <InsertParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String"  />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenCONSUMER_ID" Type="String"  />
                                            <asp:ControlParameter Name="lst_update_userID" ControlID="HiddenCURRENTWINDOWUSERID" Type="String" />
                                            <asp:Parameter Name="RelationType" Type="String" DefaultValue="Child" />
                                            <asp:Parameter Name="Age" Type="Double" />
                                            <asp:Parameter Name="ReceivingBenefits" Type="String" />
                                            <asp:Parameter Name="Notes" Type="String" />
                                        </InsertParameters>
                                        <UpdateParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String"  />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenCONSUMER_ID" Type="String"  />
                                            <asp:ControlParameter Name="lst_update_userID" ControlID="HiddenCURRENTWINDOWUSERID" Type="String" />
                                            <asp:Parameter Name="RelationType" Type="String" DefaultValue="Child" />
                                            <asp:Parameter Name="Age" Type="Double" />
                                            <asp:Parameter Name="ReceivingBenefits" Type="String" />
                                            <asp:Parameter Name="Notes" Type="String" />
                                            <asp:Parameter Name="ConsumerRelationBenefitStatus_ID" Type="Int32" />
                                        </UpdateParameters>
                                        <DeleteParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="Int32"/>
                                            <asp:Parameter Name="ConsumerRelationBenefitStatus_ID" Type="Int32" />
                                        </DeleteParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li runat="server" ID="ParentBenefitStatus">
                                    <asp:Checkbox runat="server" ID="ParentDeceased" Text="parent(s) deceased" AutoPostBack="False" CssClass="checkbox"/> <br />
                                </li>
                                <li>
                                    <asp:Checkbox runat="server" ID="ParentRetired" Text="parent(s) retired" AutoPostBack="False" CssClass="checkbox"/> <br />
                                </li>
                                <li>
                                    notes: <br />
                                    <telerik:RadTextBox ID="ParentBenefitNotes" runat="server" TextMode="MultiLine" Height="50px" Width="460px" />
                                    <asp:HiddenField runat="server" ID="ParentBenefitStatusID" />
                                </li>
                            </ol>
                        </fieldset>
                    </div>
                </telerik:RadWizardStep>
                <telerik:RadWizardStep ID="RadWizardStepAddlDetails" runat="server" Title="Additional Details" ToolTip="Additional Details" ValidationGroup="addlDetails">
                    <asp:ValidationSummary ID="ValidationSummaryAddlDetails" runat="server" ValidationGroup="addlDetails" CssClass="ErrorDIV" DisplayMode="List" />
                    <div style="float: left">
                        <fieldset id="FieldsetSecondaryDisabilities" runat="server" class="fieldsetPlacements" style="width: 500px;">
                            <legend>
                                <asp:Label runat="server" ID="LabelSecondaryDisabilities" Text="Secondary Disabilities" /></legend>
                            <ol>
                                <li id="SecondaryDisabilities" runat="server">
                                    <asp:Panel DefaultButton="filterDisabilitiesButton" ID="PanelfilterDisabilitiesSearch" runat="server" Style="margin-bottom: 5px;">
                                        <telerik:RadTextBox ID="DisabilitiesSearch" runat="server" DisplayText="Search Disabilities" Width="130px"></telerik:RadTextBox>
                                        <asp:Button ID="filterDisabilitiesButton" runat="server" Text="Search" Width="55px" CssClass="Button" />
                                    </asp:Panel>
                                    <telerik:RadListBox runat="server" ID="RadListBoxDisabilitiesSource" Height="150px" CssClass="listbox" TabIndex="1"
                                        AllowTransfer="true" TransferToID="RadListBoxDisabilitiesDestination"
                                        ButtonSettings-ShowTransferAll="false" DataKeyField="Disability_ID" DataSourceID="SqlDataSourceForDisabilitiesSource"
                                        AllowTransferOnDoubleClick="true" DataTextField="Description" DataValueField="Disability_ID"
                                        OnClientTransferred="SourceTransferred" OnItemDataBound="RadListBoxDisabilities_ItemDataBound"
                                        AutoPostBackOnTransfer="false" TransferMode="Move" ButtonSettings-Position="Right">
                                        <HeaderTemplate>
                                            <table>
                                                <tr>
                                                    <td style="width: 150px;">Description</td>
                                                    <td style="width: 80px;">ICD10</td>
                                                </tr>
                                            </table>
                                        </HeaderTemplate>
                                        <ItemTemplate>
                                            <table>
                                                <tr>
                                                    <td style="width: 150px; word-wrap: normal; min-width: 150px; max-width: 150px">
                                                        <%# Eval("Description") %>
                                                    </td>
                                                    <td style="width: 80px; min-width: 80px; max-width: 80px;">
                                                        <%# Eval("ICD10Formatted") %>
                                                    </td>
                                                </tr>
                                            </table>
                                        </ItemTemplate>
                                        <ClientItemTemplate>
                                            <table>
                                                <tr>
                                                    <td style="width: 150px; word-wrap: normal; min-width: 150px; max-width: 150px">
                                                        #= Text #
                                                    </td>
                                                    <td style="width: 80px; min-width: 80px; max-width: 80px;">
                                                        #= Attributes.ICD10Formatted #
                                                    </td>
                                                </tr>
                                            </table>
                                        </ClientItemTemplate>
                                    </telerik:RadListBox>
                                    <asp:SqlDataSource ID="SqlDataSourceForDisabilitiesSource" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[CONSUMER_DISABILITY.getConsumerDisabilitiesByClientIDExcludingSelectedAndPrimary_1.0.2]"
                                        SelectCommandType="StoredProcedure" EnableViewState="False">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                                Type="Int32" />
                                            <asp:ControlParameter Name="Disability_ID" ControlID="RadComboBoxPrimaryDisability"
                                                PropertyName="SelectedValue" Type="String" DefaultValue="0"/>
                                            <asp:ControlParameter Name="Query" ControlID="DisabilitiesSearch" DefaultValue="0" PropertyName="Text" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                    <telerik:RadListBox runat="server" ID="RadListBoxDisabilitiesDestination" Height="150px" CssClass="listbox" TabIndex="2"
                                        DataSourceID="SqlDataSourceForDisabilityDestination" DataTextField="Description"
                                        OnClientTransferred="SourceTransferred" OnItemDataBound="RadListBoxDisabilities_ItemDataBound"
                                        DataKeyField="Disability_ID" DataValueField="Disability_ID" AutoPostBackOnTransfer="false">
                                        <HeaderTemplate>
                                            <table>
                                                <tr>
                                                    <td style="width: 150px;">Description</td>
                                                    <td style="width: 80px;">ICD10</td>
                                                </tr>
                                            </table>
                                        </HeaderTemplate>
                                        <ItemTemplate>
                                            <table>
                                                <tr>
                                                    <td style="width: 150px; word-wrap: normal; min-width: 150px; max-width: 150px">
                                                        <%# Eval("Description") %>
                                                    </td>
                                                    <td style="width: 80px; min-width: 80px; max-width: 80px;">
                                                        <%# Eval("ICD10Formatted") %>
                                                    </td>
                                                </tr>
                                            </table>
                                        </ItemTemplate>
                                        <ClientItemTemplate>
                                            <table>
                                                <tr>
                                                    <td style="width: 150px; word-wrap: normal; min-width: 150px; max-width: 150px">
                                                        #= Text #
                                                    </td>
                                                    <td style="width: 80px; min-width: 80px; max-width: 80px;">
                                                        #= Attributes.ICD10Formatted #
                                                    </td>
                                                </tr>
                                            </table>
                                        </ClientItemTemplate>
                                    </telerik:RadListBox>
                                    <asp:SqlDataSource ID="SqlDataSourceForDisabilityDestination" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[CONSUMER_DISABILITY.getConsumerDisabilityByConsumerIDAndClientID_1.0.1]"
                                        SelectCommandType="StoredProcedure" EnableViewState="False">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                                Type="Int32" />
                                            <asp:ControlParameter Name="Disability_ID" ControlID="RadComboBoxPrimaryDisability"
                                                PropertyName="SelectedValue" Type="String" DefaultValue="0" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                            </ol>
                        </fieldset>
                        <fieldset class="fieldsetPlacements" style="width: 500px;" id="Programs" runat="server">
                            <legend>
                                <asp:Label runat="server" ID="Label37" Text="Programs" /></legend>
                            <ol>
                                <li runat="server">
                                    <asp:Label runat="server" ID="Label38" Text="programs:" /><br />
                                    <telerik:RadListBox runat="server" ID="RadListBoxProgramsSource" Height="100px" CssClass="listbox" TabIndex="3"
                                        AllowTransfer="true" TransferToID="RadListBoxProgramsDestination" ButtonSettings-ShowTransferAll="false"
                                        DataKeyField="Program_ID" DataSourceID="SqlDataSourceForProgramsSource" AllowTransferOnDoubleClick="true"
                                        DataTextField="Description" DataValueField="Program_ID" AutoPostBackOnTransfer="false"
                                        TransferMode="Move" ButtonSettings-Position="Right">
                                    </telerik:RadListBox>
                                    <asp:SqlDataSource ID="SqlDataSourceForProgramsSource" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[CONSUMER_PROGRAMS.getConsumerProgramsByClientIDExcludingSelected_1.0.0]"
                                        SelectCommandType="StoredProcedure" EnableViewState="False">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                                Type="Int32" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                    <telerik:RadListBox runat="server" ID="RadListBoxProgramsDestination" Height="100px" CssClass="listbox" TabIndex="4"
                                        DataSourceID="SqlDataSourceForProgramsDestination" DataTextField="Description"
                                        DataKeyField="Program_ID" DataValueField="Program_ID" AutoPostBackOnTransfer="false">
                                    </telerik:RadListBox>
                                    <asp:SqlDataSource ID="SqlDataSourceForProgramsDestination" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="[CONSUMER_PROGRAM.getConsumerProgramByConsumerIDAndClientID_1.0.0]"
                                        SelectCommandType="StoredProcedure" EnableViewState="False">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                                Type="Int32" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                            </ol>
                        </fieldset>
                        <fieldset id="FieldsetAdditionalDetails" runat="server" class="fieldsetPlacements" style="width: 500px; margin-bottom: 20px;">
                            <legend>
                                <asp:Label runat="server" ID="Label22" Text="Additional Details" /></legend>
                            <ol>
                                <li id="Memo" runat="server">
                                    <asp:Label runat="server" ID="LabelMemoFS" Text="memo:" /><br />
                                    <telerik:RadTextBox ID="TextBoxMemo" runat="server" TextMode="MultiLine" Height="50px" Width="460px" />
                                </li>
                                <li>
                                    <label>
                                    <asp:Label ID="LabelReferralSource" runat="server" Text="referral source:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxReferralSource" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." ToolTip="Select a referral source option" Filter="Contains"
                                    AutoPostBack="false" DataSourceID="SqlDataSourceReferralSource" DataTextField="Description"
                                    DataValueField="CodeValue_ID" AppendDataBoundItems="True" Width="200">
                                </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorReferralSource" runat="server" ControlToValidate="RadComboBoxReferralSource" Enabled="False" OnInit="RequiredFieldValidator_OnInit"
                                                                            ValidationGroup="addlDetails" ErrorMessage="Missing referral source." SetFocusOnError="true" Display="None" />
                                <asp:SqlDataSource ID="SqlDataSourceReferralSource" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="SELECT CV.Description, CV.CodeValue_ID, CV.Precedence FROM CodeValue CV INNER JOIN CodeSet CS ON CS.CodeSet_ID = CV.CodeSet_ID WHERE CV.Active = 1 AND CV.Client_ID = @Client_ID and CS.Code = 'REFERRAL_SOURCE' ORDER BY Precedence, Description">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                                </li>
                                <li>
                                    <label>
                                    <asp:Label ID="LabelReferralMethod" runat="server" Text="referral method:" /></label>
                                    <telerik:RadComboBox ID="RadComboBoxReferralMethod" AllowCustomText="false" runat="server" 
                                        EmptyMessage="Search or Select..." ToolTip="Select a referral method" Filter="Contains"
                                        AutoPostBack="false" DataSourceID="SqlDataSourceReferralMethod" DataTextField="Description"
                                        DataValueField="CodeValue_ID" AppendDataBoundItems="True" Width="200">
                                    </telerik:RadComboBox>
                                    <asp:SqlDataSource ID="SqlDataSourceReferralMethod" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                        SelectCommand="SELECT CV.Description, CV.CodeValue_ID, CV.Precedence FROM CodeValue CV INNER JOIN CodeSet CS ON CS.CodeSet_ID = CV.CodeSet_ID WHERE CV.Active = 1 AND CV.Client_ID = @Client_ID and CS.Code = 'REFERRAL_METHOD' ORDER BY Precedence, Description">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                                <li id="IntakeDate" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelIntakeDate" Text="intake date:" /></label>
                                    <telerik:RadDatePicker ID="RadDatePickerIntakeDate" Style="vertical-align: middle;"
                                        DateInput-EmptyMessage="" DateInput-CausesValidation="true" Width="80px" MinDate="1753-1-1"
                                        runat="server" AutoPostBack="false" ShowPopupOnFocus="true">
                                        <DatePopupButton Visible="false" />
                                        <DateInput ID="DateInput4" runat="server" CausesValidation="false" />
                                    </telerik:RadDatePicker>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorIntakeDate" runat="server" Enabled="False"
                                        ControlToValidate="RadDatePickerIntakeDate" InitialValue="" ValidationGroup="addlDetails" OnInit="RequiredFieldValidator_OnInit"
                                        ErrorMessage="Missing the intake date." SetFocusOnError="true" Display="None" />
                                </li>
                                <li id="LegislativeDistrict" runat="server">
                                    <label><asp:Label runat="server" ID="Label3335" Text="legislative district:" /></label>
                                    <telerik:RadNumericTextBox ID="ConsumerLegislativeDistrict" runat="server" Type="Number" Width="70"
                                        MinValue="0" ShowSpinButtons="True" AllowOutOfRangeAutoCorrect="true"
                                        NumberFormat-DecimalDigits="0">
                                    </telerik:RadNumericTextBox>
                                    <telerik:RadToolTip runat="server" ID="lblLegislativeDistrictToolTip" RelativeTo="Element" Width="390px" AutoCloseDelay="0"
                                            Height="70px" TargetControlID="ConsumerLegislativeDistrict" IsClientID="false" Animation="Fade" Position="TopLeft" IgnoreAltAttribute="true">
                                        Legislative District automatically updates in certain states, e.g. app.leg.wa.gov in Washington state.  In these cases, upon save, any manual changes to this field will be overwritten by the legislative district associated to the primary address in the state system.
                                    </telerik:RadToolTip>
                                </li>
                                <li id="Religion" runat="server">
                                    <label>
                                        <asp:Label runat="server" ID="LabelReligion" Text="religion:" AssociatedControlID="RadComboBoxReligion"/>
                                    </label>
                                    <telerik:RadComboBox ID="RadComboBoxReligion" runat="server" Filter="Contains" DataSourceID="SqlDataSourceReligions" Width="200" ValidationGroup="addlDetails"
                                                         AllowCustomText="False" AutoPostBack="False" EmptyMessage="Search or Select..." ToolTip="Select an option"
                                                         DataTextField="Description" DataValueField="CodeValue_ID" ExpandDirection="Down" EnableScreenBoundaryDetection="True">
                                        <Items>
                                            <telerik:RadComboBoxItem runat="server" />
                                        </Items>
                                    </telerik:RadComboBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorReligion" runat="server" Enabled="True" 
                                        ControlToValidate="RadComboBoxReligion" InitialValue="" ValidationGroup="addlDetails" OnInit="RequiredFieldValidator_OnInit"
                                        ErrorMessage="Missing a religion." SetFocusOnError="true" Display="None" />
                                    <asp:SqlDataSource ID="SqlDataSourceReligions" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                                       SelectCommand="SELECT CodeValue.Description, CodeValue.CodeValue_ID FROM [CodeValue] INNER JOIN CodeSet ON CodeSet.CodeSet_ID = CodeValue.CodeSet_ID AND CodeSet.Code = 'RELIGION' AND CodeSet.Client_ID = @Client_ID WHERE CodeValue.Client_ID = @Client_ID AND CodeValue.Active = '1' ORDER BY Precedence, CodeValue.Description" SelectCommandType="Text">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Client_ID" DefaultValue="0" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String"/>
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </li>
                            </ol>
                        </fieldset>
                    </div>
                    <fieldset class="fieldsetPlacements" style="width: 500px;" id="FundingSource" runat="server">
                        <legend>
                            <asp:Label runat="server" ID="Label59" Text="Funding Source" /></legend>
                        <ol>
                            <li id="HealthBenefits" runat="server">
                                <asp:Label runat="server" ID="Label48" Text="health benefits:" /><br />
                                <telerik:RadListBox runat="server" ID="RadListBoxHealthBenefitsSource" Height="100px" CssClass="listbox" TabIndex="5"
                                    AllowTransfer="true" TransferToID="RadListBoxHealthBenefitsDestination" ButtonSettings-ShowTransferAll="false"
                                    DataKeyField="Health_Benefit_ID" DataSourceID="SqlDataSourceForHealthBenefitsSource" AllowTransferOnDoubleClick="true"
                                    DataTextField="Description" DataValueField="Health_Benefit_ID" AutoPostBackOnTransfer="false"
                                    TransferMode="Move" ButtonSettings-Position="Right">
                                </telerik:RadListBox>
                                <asp:SqlDataSource ID="SqlDataSourceForHealthBenefitsSource" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="[CONSUMER_HEALTHBENEFITS.getConsumerHealthBenefitsByClientIDExcludingSelected_1.0.0]"
                                    SelectCommandType="StoredProcedure" EnableViewState="False">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                            Type="Int32" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                                <telerik:RadListBox runat="server" ID="RadListBoxHealthBenefitsDestination" Height="100px" CssClass="listbox" TabIndex="6"
                                    DataSourceID="SqlDataSourceForHealthBenefitsDestination" DataTextField="Description"
                                    DataKeyField="Health_Benefit_ID" DataValueField="Health_Benefit_ID" AutoPostBackOnTransfer="false">
                                </telerik:RadListBox>
                                <asp:SqlDataSource ID="SqlDataSourceForHealthBenefitsDestination" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="[CONSUMER_HEALTHBENEFITS.getConsumerHealthBenefitsByConsumerIDAndClientID_1.0.0]"
                                    SelectCommandType="StoredProcedure" EnableViewState="False">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                            Type="Int32" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="FeeLevel" runat="server" style="padding-top: 10px">
                                <label>
                                    <asp:Label runat="server" ID="Label25" Text="fee level:" /></label>
                                <telerik:RadNumericTextBox ID="ConsumerFeeLevelTxt" runat="server" Type="Number"
                                    MaxValue="30" MinValue="0" ShowSpinButtons="True" AllowOutOfRangeAutoCorrect="true"
                                    NumberFormat-DecimalDigits="0">
                                </telerik:RadNumericTextBox>
                            </li>
                            <li id="Medicare" runat="server" style="padding-top: 10px">
                                <label>
                                    <asp:Label runat="server" ID="Label33" Text="medicare:" /></label>
                                <telerik:RadTextBox ID="ConsumerMedicareTxt" runat="server" Enabled="true">
                                </telerik:RadTextBox >
                            </li>
                            <li id="MedicareType" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="Label34" Text="medicare type:" /></label>
                                <telerik:RadComboBox ID="RadComboBoxMedicareType" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." ToolTip="Select a Medicare Insurance Type option" Filter="Contains"
                                    AutoPostBack="false" DataSourceID="SqlDataSourceMedicareInsuranceType" DataTextField="Description"
                                    DataValueField="CodeValue_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                </telerik:RadComboBox>
                                <asp:SqlDataSource ID="SqlDataSourceMedicareInsuranceType" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="[CODE_VALUE.getCodeValueByCodeSetCodeAndClientIDActiveOnly_1.0.2]" SelectCommandType="StoredProcedure">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="Int32" />
                                        <asp:Parameter Name="CodeSetCode" DefaultValue="MEDICARE_INSURANCE_TYPE" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="MedicaidStatus2" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelMedicaidStatus2" Visible="False" Text="medicaid status:" /></label>
                                <telerik:RadComboBox ID="RadComboBoxMedicaidStatus2" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." ToolTip="Select a Medicaid Status option" Filter="Contains" Visible="False"
                                    AutoPostBack="false" DataSourceID="SqlDataSourceMedicaidStatus" DataTextField="Description"
                                    DataValueField="Medicaid_Status_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                </telerik:RadComboBox>
                                <asp:SqlDataSource ID="SqlDataSourceMedicaidStatus" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="
                                        SELECT Medicaid_Status.Description,
                                        Medicaid_Status.Medicaid_Status_ID
                                        FROM
                                        Medicaid_Status
                                        WHERE
                                        Medicaid_Status.Client_ID = @Client_ID AND Medicaid_Status.Active = '1' ORDER BY Medicaid_Status.Description">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="MedicaidWaiver" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelMedicaidWaiver" Text="medicaid waiver:" /></label>
                                <telerik:RadComboBox ID="RadComboBoxMedicaidWaiver" AllowCustomText="false" runat="server"
                                    EmptyMessage="Search or Select..." ToolTip="Select a Medicaid Waiver option" Filter="Contains"
                                    AutoPostBack="false" DataSourceID="SqlDataSourceMedicaidWaiver" DataTextField="Description"
                                    DataValueField="Medicaid_Waiver_ID" AppendDataBoundItems="True" OnDataBound="RadComboBox_InsertEmptyOnDataBound">
                                </telerik:RadComboBox>
                                <asp:SqlDataSource ID="SqlDataSourceMedicaidWaiver" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="
                                        SELECT Medicaid_Waiver.Description,
                                        Medicaid_Waiver.Medicaid_Waiver_ID
                                        FROM
                                        Medicaid_Waiver
                                        WHERE
                                        Medicaid_Waiver.Client_ID = @Client_ID AND Medicaid_Waiver.Active = '1' ORDER BY Medicaid_Waiver.Description">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                            <li id="WaiverSlotNumber" runat="server">
                                <label>
                                    <asp:Label runat="server" ID="LabelWaiverSlotNumber" Text="waiver slot number:" /></label>
                                <telerik:RadTextBox ID="TextBoxWaiverSlotNumber" runat="server" Enabled="true" ValidationGroup="1" MaxLength="50">
                                </telerik:RadTextBox >
                            </li>
                        </ol>
                    </fieldset>
                    <fieldset id="FieldsetSourcesOfIncome" runat="server" class="fieldsetPlacements" style="width: 500px;">
                        <legend>
                            <asp:Label runat="server" ID="LabelIncomeSource" Text="Sources of Income" /></legend>
                        <ol>
                            <li runat="server" id="IncomeSource">
                                <telerik:RadListBox runat="server" ID="RadListBoxIncomeSource" Height="100px" CssClass="listbox" TabIndex="5"
                                    AllowTransfer="true" TransferToID="RadListBoxIncomeDestination" ButtonSettings-ShowTransferAll="false"
                                    DataKeyField="IncomeSource_ID" DataSourceID="SqlDataSourceForIncomeSource" AllowTransferOnDoubleClick="true"
                                    DataTextField="Description" DataValueField="IncomeSource_ID" AutoPostBackOnTransfer="false"
                                    TransferMode="Move" ButtonSettings-Position="Right">
                                </telerik:RadListBox>
                                <asp:SqlDataSource ID="SqlDataSourceForIncomeSource" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="[CONSUMER_INCOMESOURCE.getConsumerIncomeSourceByClientIDExcludingSelected_1.0.0]"
                                    SelectCommandType="StoredProcedure" EnableViewState="False">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                            Type="Int32" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                                <telerik:RadListBox runat="server" ID="RadListBoxIncomeDestination" Height="100px" CssClass="listbox" TabIndex="6"
                                    DataSourceID="SqlDataSourceForIncomeDestination" DataTextField="Description"
                                    DataKeyField="IncomeSource_ID" DataValueField="IncomeSource_ID" AutoPostBackOnTransfer="false">
                                </telerik:RadListBox>
                                <asp:CustomValidator ID="CustomValidatorIncomeDestination" runat="server" ControlToValidate="RadListBoxIncomeDestination"
                                    ClientValidationFunction="validateIncomeDestination" OnServerValidate="CustomValidatorIncomeDestination_ServerValidate"
                                    ErrorMessage="Missing a source of income." Display="None" ValidationGroup="addlDetails" Enabled="false" ValidateEmptyText="true" />
                                <asp:SqlDataSource ID="SqlDataSourceForIncomeDestination" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                    SelectCommand="[CONSUMER_INCOMESOURCE.getConsumerIncomeSourceByConsumerIDAndClientID_1.0.0]"
                                    SelectCommandType="StoredProcedure" EnableViewState="False">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenTEMPCONSUMER_ID" DefaultValue="0"
                                            Type="Int32" />
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </li>
                        </ol>
                    </fieldset>
                    <fieldset id="FieldsetHIEInformation" runat="server" class="fieldsetPlacements" style="width: 500px;">
                         <legend>
                             <asp:Label runat="server" ID="LabelHIEInformation" Text="Data Exchange Information" />
                         </legend>
                         <ol>
                             <li>
                                 <telerik:RadCheckBox runat="server" ID="HIEIntegrationEnabled" AutoPostBack="False" Text="Send to Data Exchange"/>
                             </li>
                         </ol>
                    </fieldset>
                </telerik:RadWizardStep>
            </WizardSteps>
        </telerik:RadWizard>
    </div>
    <asp:HiddenField ID="HiddenACTION_TYPE" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCONSUMER_ID" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenNEWCONSUMER_ID" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenTEMPCONSUMER_ID" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTWINDOWUSERNAME" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTWINDOWUSERID" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTWINDOWCLIENTID" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTLYSAVING" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenOnClose_NavigateToConsumerID" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenRadgridInit" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenMedSupportRecordExists" runat="server" Value="False"></asp:HiddenField>

    <telerik:RadCodeBlock ID="RadCodeBlock1" runat="server">
        <script type="text/javascript" src='<%= PathHelper.getAbsoluteUrl("~/js/common-utilities.js", true)%>'></script>
                        <%: Scripts.Render("~/js/SWIFrameManager.js",
                                "~/Scripts/radcomboboxfix.js",
                                "~/js/jquery-3.3.1.min.js","~/bundles/Scripts/responsive") %>
        <style type="text/css">
            .rwzProgressBar {
                margin-bottom: 10px !important;
                margin-top: -5px !important;
            }

            .rwzNav {
                margin-top: -113px !important;
                margin-bottom: 91px !important;
                margin-right: 98px !important;
                position: absolute !important;
                right: 10px !important;
            }
        </style>
        <script type="text/javascript">
            function radwizard_Load(sender, eventArgs) {
                
            }
            
            function DisableAutofill(sender, eventArgs) {
                sender.get_inputDomElement().setAttribute('autocomplete', 'new-password');
            }
            
            function SourceTransferred(source, eventArgs) {
                eventArgs.get_item().bindTemplate()
            }

            function showPopup(id) {
                var datePicker = $find(id);
                if (datePicker !== null) {
                    datePicker.showPopup();
                } else {
                    console.error('An element with ID [%s] was not found', id);
                }
            }

            function navigateAfterCreatingConsumer(consumerID) {
                GetRadWindow().BrowserWindow.createNewConsumerNavigate(consumerID);
            }

            function ResetValidation(sender, args) {
                Page_BlockSubmit = false;
            }

            function WizardButtonClicked(sender, args) {
                // handle Cancel clicked
                if (args.get_command() === 3) {
                    Cancel();
                }
                else {
                    var wizard = sender;
                    var step = args.get_activeStep();
                    var actionType = document.getElementById("<%= HiddenACTION_TYPE.ClientID %>").value;
                    // Auto save the consumer if create and selecting one of the non-essential tabs
                    if (step._index > 1 && actionType === "CREATE") {
                        var validationFailed = false;

                        for (var i = 0; i < 2; i++) {
                            if (!Page_ClientValidate(wizard.get_wizardSteps().getWizardStep(i)._validationGroup)) {
                                wizard.set_activeIndex(wizard.get_wizardSteps().getWizardStep(i)._index);
                                validationFailed = true;
                                Page_BlockSubmit = false;
                                break;
                            }
                        }

                        if (!validationFailed) {
                            var ajaxManager = $find("<%= RadAjaxManager1.ClientID %>");
                            ajaxManager.ajaxRequest(step._index);
                            document.getElementById("<%= HiddenACTION_TYPE.ClientID %>").value = "MODIFY";
                        }
                   
                        return false;
                    }
                    else {
                        var iFrameURL = getAttribute(step.get_element(), "IFrameURL");
                        if (iFrameURL) {
                            var parentDIV = step.get_element().children[0];
                            var consumerID = document.getElementById("<%= HiddenTEMPCONSUMER_ID.ClientID %>").value;
                            var topicID = getAttribute(step.get_element(), "TopicID");
                            var iFrameID = "IFrame" + parentDIV.id + "ID";
                            if (topicID) {
                                createIFrame(parentDIV, iFrameID, iFrameURL, "ConsumerID=" + consumerID + "&ConversationTopicID=" + topicID, false, "850px");
                            } else {
                                createIFrame(parentDIV, iFrameID, iFrameURL, "ConsumerID=" + consumerID, false);
                            }
                        }
                    }
                }
            }

            function navigateToStepIndex(stepIndex) {
                var wizard = $find("<%= RadWizard1.ClientID %>");
                var step = wizard.get_wizardSteps().getWizardStep(stepIndex);
                console.log('Step index: ' + stepIndex);
                console.log(step);
                var iFrameURL = getAttribute(step.get_element(), "IFrameURL");
                if (iFrameURL) {
                    var parentDIV = step.get_element().children[0];
                    var consumerID = document.getElementById("<%= HiddenTEMPCONSUMER_ID.ClientID %>").value;

                    var topicID = getAttribute(step.get_element(), "TopicID");
                    var iFrameID = "IFrame" + parentDIV.id + "ID";
                    if (topicID) {
                        createIFrame(parentDIV, iFrameID, iFrameURL, "ConsumerID=" + consumerID + "&ConversationTopicID=" + topicID, false, "850px");
                    } else {
                        createIFrame(parentDIV, iFrameID, iFrameURL, "ConsumerID=" + consumerID, false);
                    }
                }
            }
            
            function navigateToStepIndexTest(index) {
                var wizard = $find('<%= RadWizard1.ClientID %>');
                wizard.set_activeIndex(index);
            }

            function getAttribute(element, attr) {
                var node = element.attributes[attr];
                if (node && node.value) {
                    return node.value;
                }
                return null;
            }

            function onSaveClick() {
                var wizard = $find("<%= RadWizard1.ClientID %>");
                var steps = wizard.get_wizardSteps();
                for (var i = 0; i < steps.get_count() ; i++) {
                    if (!Page_ClientValidate(steps.getWizardStep(i)._validationGroup)) {
                        wizard.set_activeIndex(i);
                        Page_BlockSubmit = false;
                        return false;
                    }
                }
                return true;
            }

            function CloseWindow() {
                var consumerID = document.getElementById("<%= HiddenOnClose_NavigateToConsumerID.ClientID %>").value;
                if (consumerID !== "False") {
                    
                    navigateAfterCreatingConsumer(consumerID);
                }
                else {
                    if(window.checkMobile()) {
                        history.back();
                        return false;
                    }
                    else {
                        var oWindow = GetRadWindow();
                        oWindow.Close(true);
                    }
                }
            }

               function Cancel() {
                   var oWindow = GetRadWindow();
                   if (oWindow !== null) {
                       oWindow.Close();
                   } else {
                       history.back();
                        return false;
                   }
               }

            /**
             * A handler for a custom validation function to ensure that a valid value is selected.
             * Assumes the controlToValidate is a ComboBox.
             */
            function validateCombo(source, args) {
                args.IsValid = false;
                var combo = $find(source.controltovalidate);
                var text = combo.get_text();
                if (text.length < 1) {
                    args.IsValid = false;
                }
                else {
                    var node = combo.findItemByText(text);
                    if (node) {
                        var value = node.get_value();
                        if (value.length > 0) {
                            args.IsValid = true;
                        }
                    }
                    else {
                        args.IsValid = false;
                    }
                }
            }

            function validateComboCheckbox(source, args) {
                args.IsValid = false;
                var combo = $find(source.controltovalidate);
                var text = combo.get_text();
                if (text.length < 1) {
                    args.IsValid = false;
                }
                else {
                    args.IsValid = true;
                }
            }

            function validateTextBox(source, args) {
                args.IsValid = false;
                var textBox = $find(source.controltovalidate);
                var text = textBox.get_text();
                if (textBox.text == null) {
                    args.IsValid = false;
                }
                else {
                    args.IsValid = true;
                }
            }

            function validateTextBoxSecondAddress(source, args) {
                args.IsValid = false;
                var textBox = $get(source.controltovalidate)
                if (textBox.value != null && textBox.value != '') {
                    args.IsValid = true;
                }
                else if (checkSecondAddressFields()) {
                    args.IsValid = true;
                }
            }

            function validateComboBoxSecondAddress(source, args) {
                args.IsValid = false;
                var combo = $find(source.controltovalidate);
                if (combo != null && combo.get_value() != "") {
                    args.IsValid = true;
                }
                else if (checkSecondAddressFields()) {
                    args.IsValid = true;
                }
            }

            function checkSecondAddressFields(validator) {
                if ($get("<%= CheckBoxSecondIntlAddress.ClientID %>") == null ||  $get("<%= CheckBoxSecondIntlAddress.ClientID %>").checked == false) {
                    if (document.getElementById("<%= SecondAddressDescriptionTxt.ClientID %>").value == "" &&
                        document.getElementById("<%= ConsumerSecondAddressTxt.ClientID %>").value == "" &&
                        document.getElementById("<%= ConsumerSecondAddress2Txt.ClientID %>").value == "" &&
                        document.getElementById("<%= ConsumerSecondCityTxt.ClientID %>").value == "" &&
                        ($find("<%= RadComboBoxSecondState.ClientID %>").get_value() == null ||
                        $find("<%= RadComboBoxSecondState.ClientID %>").get_value() == "") &&
                        document.getElementById("<%= ConsumerSecondZip.ClientID %>").value == "" &&
                        ($find('<%= RadComboBoxSecondCounty.ClientID %>').get_value() == null ||
                        $find('<%= RadComboBoxSecondCounty.ClientID %>').get_value() == "")) {
                        return (true);
                    }
                    else {
                        return false;
                    }
                }
                else {
                    return false;
                }
            }

            function validateIncomeDestination(source, args) {
                args.IsValid = false;
                var list = $find("<%= RadListBoxIncomeDestination.ClientID %>");
                var items = list.get_items();
                args.IsValid = items.get_count() > 0;
            }
            
            function consumerIdentifierComboBoxDataBound(sender, args) {
                var medSupportRecordExists = $get('<%=HiddenMedSupportRecordExists.ClientID %>').value;
                if(medSupportRecordExists === 'True') {
                    var comboBox = sender;
                    var items = comboBox.get_items();
                    for (var i = items.get_count() - 1; i >= 0; i--) {
                        var item = items.getItem(i);
                        var text = item.get_text().toLowerCase();
                        if (text.indexOf("medsupport") !== -1) {
                            comboBox.get_items().remove(item);
                        }
                    }
                }
            }
        </script>
    </telerik:RadCodeBlock>
    <asp:HiddenField ID="HiddenEXISTINGUSER" runat="server"></asp:HiddenField>
</asp:Content>
