<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sigil</name>
    </assembly>
    <members>
        <member name="T:Sigil.CatchBlock">
            <summary>
            Represents a catch block which appears in an ExceptionBlock.
            
            To create a CatchBlock, call BeginCatchBlock(Type) or BeginCatchAllBlock().
            </summary>
        </member>
        <member name="P:Sigil.CatchBlock.ExceptionBlock">
            <summary>
            The ExceptionBlock this CatchBlock appears in.
            </summary>
        </member>
        <member name="P:Sigil.CatchBlock.IsCatchAll">
            <summary>
            Returns true if this CatchBlock will catch all exceptions.
            
            This is equivalent to `catch(Exception e)` in C#.
            </summary>
        </member>
        <member name="P:Sigil.CatchBlock.ExceptionType">
            <summary>
            The type of exception being caught by this CatchBlock.
            
            When the CatchBlock is entered, an exception of this type will
            be pushed onto the stack.
            </summary>
        </member>
        <member name="T:Sigil.DisassembledOperations`1">
            <summary>
            Represents a decompiled delegate.
            
            The operations of the decompiled delegate can be inspected, and it can be replayed to a new Emit.
            </summary>
        </member>
        <member name="P:Sigil.DisassembledOperations`1.Count">
            <summary>
            The total number of operations that were decompiled.
            </summary>
        </member>
        <member name="P:Sigil.DisassembledOperations`1.Parameters">
            <summary>
            The parameters the decompiled delegate takes.
            </summary>
        </member>
        <member name="P:Sigil.DisassembledOperations`1.Locals">
            <summary>
            The locals the decompiled delegate declared and uses.
            </summary>
        </member>
        <member name="P:Sigil.DisassembledOperations`1.Labels">
            <summary>
            The labels the decompile delegate uses.
            </summary>
        </member>
        <member name="P:Sigil.DisassembledOperations`1.Usage">
            <summary>
            Traces where values produced by certain operations are used.
            
            This is roughly equivalent to having built the disassembled delegate via Sigil originally,
            and saving the results of TraceOperationResultUsage().
            </summary>
        </member>
        <member name="P:Sigil.DisassembledOperations`1.CanEmit">
            <summary>
            Returns true if a call to EmitAll will succeed.
            
            This property will be false if the delegate that was disassembled closed over it's environment,
            thereby adding an implicit `this` that cannot be represented (and thus cannot be returned).
            </summary>
        </member>
        <member name="P:Sigil.DisassembledOperations`1.Item(System.Int32)">
            <summary>
            Returns the operation that would be emitted at the given index.
            </summary>
        </member>
        <member name="M:Sigil.DisassembledOperations`1.EmitAll(System.String,System.Reflection.Emit.ModuleBuilder)">
            <summary>
            Emits the disassembled instructions into a new Emit.
            </summary>
        </member>
        <member name="M:Sigil.DisassembledOperations`1.GetEnumerator">
            <summary>
            Returns an enumerator which steps over the Operations that are in this DisassembledOperations.
            </summary>
        </member>
        <member name="M:Sigil.DisassembledOperations`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator which steps over the Operations that are in this DisassembledOperations.
            </summary>
        </member>
        <member name="T:Sigil.Disassembler`1">
            <summary>
            Helper for disassembling delegates into a series of Emit operations.
            
            This can be used to inspect delegates, or combine them via Sigil.
            </summary>
            <typeparam name="DelegateType">The type of delegate being disassembled</typeparam>
        </member>
        <member name="M:Sigil.Disassembler`1.Disassemble(`0)">
            <summary>
            Disassembles a delegate into a DisassembledOperations object.
            </summary>
            <param name="del">The delegate to disassemble</param>
        </member>
        <member name="T:Sigil.Emit`1">
            <summary>
            Helper for CIL generation that fails as soon as a sequence of instructions
            can be shown to be invalid.
            </summary>
            <typeparam name="DelegateType">The type of delegate being built</typeparam>
        </member>
        <member name="M:Sigil.Emit`1.ArgumentList">
            <summary>
            Pushes a pointer to the current argument list onto the stack.
            
            This instruction can only be used in VarArgs methods.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Add">
            <summary>
            Pops two arguments off the stack, adds them, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.AddOverflow">
            <summary>
            Pops two arguments off the stack, adds them, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedAddOverflow">
            <summary>
            Pops two arguments off the stack, adds them as if they were unsigned, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Divide">
            <summary>
            Pops two arguments off the stack, divides the second by the first, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedDivide">
            <summary>
            Pops two arguments off the stack, divides the second by the first as if they were unsigned, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Multiply">
            <summary>
            Pops two arguments off the stack, multiplies them, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.MultiplyOverflow">
            <summary>
            Pops two arguments off the stack, multiplies them, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedMultiplyOverflow">
            <summary>
            Pops two arguments off the stack, multiplies them as if they were unsigned, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Remainder">
            <summary>
            Pops two arguments off the stack, calculates the remainder of the second divided by the first, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedRemainder">
            <summary>
            Pops two arguments off the stack, calculates the remainder of the second divided by the first as if both were unsigned, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Subtract">
            <summary>
            Pops two arguments off the stack, subtracts the first from the second, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.SubtractOverflow">
            <summary>
            Pops two arguments off the stack, subtracts the first from the second, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedSubtractOverflow">
            <summary>
            Pops two arguments off the stack, subtracts the first from the second as if they were unsigned, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Negate">
            <summary>
            Pops an argument off the stack, negates it, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.And">
            <summary>
            Pops two arguments off the stack, performs a bitwise and, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Or">
            <summary>
            Pops two arguments off the stack, performs a bitwise or, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Xor">
            <summary>
            Pops two arguments off the stack, performs a bitwise xor, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Not">
            <summary>
            Pops one argument off the stack, performs a bitwise inversion, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ShiftLeft">
            <summary>
            Pops two arguments off the stack, shifts the second value left by the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ShiftRight">
            <summary>
            Pops two arguments off the stack, shifts the second value right by the first value.
            
            Sign extends from the left.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedShiftRight">
            <summary>
            Pops two arguments off the stack, shifts the second value right by the first value.
            
            Acts as if the value were unsigned, zeros always coming in from the left.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Box``1">
            <summary>
            Boxes the given value type on the stack, converting it into a reference.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Box(System.Type)">
            <summary>
            Boxes the given value type on the stack, converting it into a reference.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Branch(Sigil.Label)">
            <summary>
            Unconditionally branches to the given label.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Branch(System.String)">
            <summary>
            Unconditionally branches to the label with the given name.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, if both are equal branches to the given label.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfEqual(System.String)">
            <summary>
            Pops two arguments from the stack, if both are equal branches to the label with the given name.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfNotEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, if they are not equal (when treated as unsigned values) branches to the given label.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfNotEqual(System.String)">
            <summary>
            Pops two arguments from the stack, if they are not equal (when treated as unsigned values) branches to the label with the given name.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfGreaterOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfGreaterOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfGreaterOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfGreaterOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfGreater(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfGreater(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfGreater(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfGreater(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfLessOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfLessOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfLessOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfLessOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfLess(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfLess(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than the first value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfLess(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedBranchIfLess(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfFalse(Sigil.Label)">
            <summary>
            Pops one argument from the stack, branches to the given label if the value is false.
            
            A value is false if it is zero or null.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfFalse(System.String)">
            <summary>
            Pops one argument from the stack, branches to the label with the given name if the value is false.
            
            A value is false if it is zero or null.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfTrue(Sigil.Label)">
            <summary>
            Pops one argument from the stack, branches to the given label if the value is true.
            
            A value is true if it is non-zero or non-null.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BranchIfTrue(System.String)">
            <summary>
            Pops one argument from the stack, branches to the label with the given name if the value is true.
            
            A value is true if it is non-zero or non-null.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Break">
            <summary>
            Emits a break instruction for use with a debugger.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Call``1(Sigil.Emit{``0},System.Type[])">
            <summary>
            Calls the method being constructed by the given emit.  Emits so used must have been constructed with BuildMethod or related methods.
            
            Pops its arguments in reverse order (left-most deepest in the stack), and pushes the return value if it is non-void.
            
            If the given method is an instance method, the `this` reference should appear before any parameters.
            
            Call does not respect overrides, the implementation defined by the given MethodInfo is what will be called at runtime.
            
            To call overrides of instance methods, use CallVirtual.
            
            Recursive calls can only be performed with DynamicMethods, other passed in Emits must already have their methods created.
            
            When calling VarArgs methods, arglist should be set to the types of the extra parameters to be passed.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Call(System.Reflection.MethodInfo,System.Type[])">
            <summary>
            Calls the given method.  Pops its arguments in reverse order (left-most deepest in the stack), and pushes the return value if it is non-void.
            
            If the given method is an instance method, the `this` reference should appear before any parameters.
            
            Call does not respect overrides, the implementation defined by the given MethodInfo is what will be called at runtime.
            
            To call overrides of instance methods, use CallVirtual.
            
            When calling VarArgs methods, arglist should be set to the types of the extra parameters to be passed.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Call(System.Reflection.ConstructorInfo)">
            <summary>
            Calls the given constructor.  Pops its arguments in reverse order (left-most deepest in the stack).
            
            The `this` reference should appear before any parameters.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes a void return and no parameters.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``1(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and no parameters.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``2(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``3(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``4(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``5(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``6(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``7(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``8(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``9(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``10(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``11(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``12(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``13(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``14(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``15(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``16(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect``17(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect(System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[])">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This override allows an arglist to be passed for calling VarArgs methods.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallIndirect(System.Reflection.CallingConventions,System.Type,System.Type[])">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CallVirtual(System.Reflection.MethodInfo,System.Type,System.Type[])">
            <summary>
            Calls the given method virtually.  Pops its arguments in reverse order (left-most deepest in the stack), and pushes the return value if it is non-void.
            
            The `this` reference should appear before any arguments (deepest in the stack).
            
            The method invoked at runtime is determined by the type of the `this` reference.
            
            If the method invoked shouldn't vary (or if the method is static), use Call instead.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CastClass``1">
            <summary>
            Cast a reference on the stack to the given reference type.
            
            If the cast is not legal, a CastClassException will be thrown at runtime.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CastClass(System.Type)">
            <summary>
            Cast a reference on the stack to the given reference type.
            
            If the cast is not legal, a CastClassException will be thrown at runtime.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CheckFinite">
            <summary>
            Throws an ArithmeticException on runtime if the value on the stack is not a finite number.
            
            This leaves the value checked on the stack, rather than popping it as might be expected.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CompareEqual">
            <summary>
            Pops two values from the stack, and pushes a 1 if they are equal and 0 if they are not.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CompareGreaterThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is greater than the first value and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedCompareGreaterThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is greater than the first value (as unsigned values) and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CompareLessThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is less than the first value and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedCompareLessThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is less than the first value (as unsigned values) and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Convert``1">
            <summary>
            Convert a value on the stack to the given non-character primitive type.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Convert(System.Type)">
            <summary>
            Convert a value on the stack to the given non-character primitive type.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ConvertOverflow``1">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ConvertOverflow(System.Type)">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedConvertOverflow``1">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type as if it were unsigned.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedConvertOverflow(System.Type)">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type as if it were unsigned.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnsignedConvertToFloat">
            <summary>
            Converts a primitive type on the stack to a float, as if it were unsigned.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CopyBlock(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Expects a destination pointer, a source pointer, and a length on the stack.  Pops all three values.
            
            Copies length bytes from destination to the source.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CopyObject``1">
            <summary>
            Takes a destination pointer, a source pointer as arguments.  Pops both off the stack.
            
            Copies the given value type from the source to the destination.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CopyObject(System.Type)">
            <summary>
            Takes a destination pointer, a source pointer as arguments.  Pops both off the stack.
            
            Copies the given value type from the source to the destination.
            </summary>
        </member>
        <member name="P:Sigil.Emit`1.AllowsUnverifiableCIL">
            <summary>
            Returns true if this Emit can make use of unverifiable instructions.
            </summary>
        </member>
        <member name="P:Sigil.Emit`1.MaxStackSize">
            <summary>
            Returns the maxmimum number of items on the stack for the IL stream created with the current emit.
            
            This is not the maximum that *can be placed*, but the maximum that actually are.
            </summary>
        </member>
        <member name="P:Sigil.Emit`1.Locals">
            <summary>
            Lookup for the locals currently in scope by name.
            
            Locals go out of scope when released (by calling Dispose() directly, or via using) and go into scope
            immediately after a DeclareLocal()
            </summary>
        </member>
        <member name="P:Sigil.Emit`1.Labels">
            <summary>
            Lookup for declared labels by name.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.AsShorthand">
            <summary>
            Returns a proxy for this Emit that exposes method names that more closely
            match the fields on System.Reflection.Emit.OpCodes.
            
            IF you're well versed in ILGenerator, the shorthand version may be easier to use.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Instructions">
            <summary>
            Returns a string representation of the CIL opcodes written to this Emit to date.
            
            This method is meant for debugging purposes only.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ILOffset">
            <summary>
            Returns the current instruction offset (effectively, the length of the CIL stream to date).
            
            This does not necessarily increase monotonically, as rewrites can cause it to shrink.
            
            Likewise the effect of any given call is not guaranteed to be the same under all circumstance, as current and future
            state may influence opcode choice.
            
            This method is meant for debugging purposes only.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.TraceOperationResultUsage">
            <summary>
            Traces where the values produced by certain operations are used.
            
            For example:
              ldc.i4 32
              ldc.i4 64
              add
              ret
              
            Would be represented by a series of OperationResultUsage like so:
              - (lcd.i4 32) -> add
              - (ldc.i4 64) -> add
              - (add) -> ret
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateDelegate(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Converts the CIL stream into a delegate.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            `instructions` will be set to a representation of the instructions making up the returned delegate.
            Note that this string is typically *not* enough to regenerate the delegate, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned delegate fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateDelegate(Sigil.OptimizationOptions)">
            <summary>
            Converts the CIL stream into a delegate.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateMethod(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the MethodBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a MethodBuilder, which can be used to define overrides or for further inspection.
            
            `instructions` will be set to a representation of the instructions making up the returned method.
            Note that this string is typically *not* enough to regenerate the method, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned method fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateMethod(Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the MethodBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a MethodBuilder, which can be used to define overrides or for further inspection.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateConstructor(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            
            `instructions` will be set to a representation of the instructions making up the returned constructor.
            Note that this string is typically *not* enough to regenerate the constructor, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned constructor fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateConstructor(Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateTypeInitializer(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            
            `instructions` will be set to a representation of the instructions making up the returned constructor.
            Note that this string is typically *not* enough to regenerate the constructor, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned constructor fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.CreateTypeInitializer(Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewDynamicMethod(System.String,System.Reflection.Emit.ModuleBuilder,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, optionally using the provided name and module for the inner DynamicMethod.
            
            If name is not defined, a sane default is generated.
            
            If module is not defined, a module with the same trust as the executing assembly is used instead.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewDynamicMethod(System.Type,System.String,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, optionally using the provided name and owner for the inner DynamicMethod.
            
            If name is not defined, a sane default is generated.
            
            If owner is not defined, a module with the same trust as the executing assembly is used instead.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BuildMethod(System.Reflection.Emit.TypeBuilder,System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, suitable for building a method on the given TypeBuilder.
            
            The DelegateType and MethodBuilder must agree on return types, parameter types, and parameter counts.
            
            If you intend to use unveriable code, you must set allowUnverifiableCode to true.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BuildStaticMethod(System.Reflection.Emit.TypeBuilder,System.String,System.Reflection.MethodAttributes,System.Boolean,System.Boolean)">
            <summary>
            Convenience method for creating static methods.
            
            Equivalent to calling to BuildMethod, but with MethodAttributes.Static set and CallingConventions.Standard.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BuildInstanceMethod(System.Reflection.Emit.TypeBuilder,System.String,System.Reflection.MethodAttributes,System.Boolean,System.Boolean)">
            <summary>
            Convenience method for creating instance methods.
            
            Equivalent to calling to BuildMethod, but with CallingConventions.HasThis.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BuildConstructor(System.Reflection.Emit.TypeBuilder,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, suitable for building a constructor on the given TypeBuilder.
            
            The DelegateType and TypeBuilder must agree on parameter types and parameter counts.
            
            If you intend to use unveriable code, you must set allowUnverifiableCode to true.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BuildTypeInitializer(System.Reflection.Emit.TypeBuilder,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, suitable for building a type initializer on the given TypeBuilder.
            
            The DelegateType and TypeBuilder must agree on parameter types and parameter counts.
            
            If you intend to use unveriable code, you must set allowUnverifiableCode to true.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Duplicate">
            <summary>
            Pushes a copy of the current top value on the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.InitializeBlock(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Expects a pointer, an initialization value, and a count on the stack.  Pops all three.
            
            Writes the initialization value to count bytes at the passed pointer.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.InitializeObject``1">
            <summary>
            Expects an instance of the type to be initialized on the stack.
            
            Initializes all the fields on a value type to null or an appropriate zero value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.InitializeObject(System.Type)">
            <summary>
            Expects an instance of the type to be initialized on the stack.
            
            Initializes all the fields on a value type to null or an appropriate zero value.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.IsInstance``1">
            <summary>
            Pops a value from the stack and casts to the given type if possible pushing the result, otherwise pushes a null.
            
            This is analogous to C#'s `as` operator.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.IsInstance(System.Type)">
            <summary>
            Pops a value from the stack and casts to the given type if possible pushing the result, otherwise pushes a null.
            
            This is analogous to C#'s `as` operator.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Jump(System.Reflection.MethodInfo)">
            <summary>
            Transfers control to another method.
            
            The parameters and calling convention of method must match the current one's.
            
            The stack must be empty to jump.
            
            Like the branching instructions, Jump cannot leave exception blocks.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.DefineLabel(System.String)">
            <summary>
            Defines a new label.
            
            This label can be used for branching, leave, and switch instructions.
            
            A label must be marked exactly once after being defined, using the MarkLabel() method.        
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.DefineLabel(Sigil.Label@,System.String)">
            <summary>
            Defines a new label.
            
            This label can be used for branching, leave, and switch instructions.
            
            A label must be marked exactly once after being defined, using the MarkLabel() method.        
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.MarkLabel(Sigil.Label)">
            <summary>
            Marks a label in the instruction stream.
            
            When branching, leaving, or switching with a label control will be transfered to where it was *marked* not defined.
            
            Labels can only be marked once, and *must* be marked before creating a delegate.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.MarkLabel(System.String)">
            <summary>
            Marks a label with the given name in the instruction stream.
            
            When branching, leaving, or switching with a label control will be transfered to where it was *marked* not defined.
            
            Labels can only be marked once, and *must* be marked before creating a delegate.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Leave(Sigil.Label)">
            <summary>
            Leave an exception or catch block, branching to the given label.
            
            This instruction empties the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Leave(System.String)">
            <summary>
            Leave an exception or catch block, branching to the label with the given name.
            
            This instruction empties the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadArgument(System.UInt16)">
            <summary>
            Loads the argument at the given index (starting at 0) for the current method onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadArgumentAddress(System.UInt16)">
            <summary>
            Loads a pointer to the argument at index (starting at zero) onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Boolean)">
            <summary>
            Push a 1 onto the stack if b is true, and 0 if false.
            
            Pushed values are int32s.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Int32)">
            <summary>
            Push a constant int32 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.UInt32)">
            <summary>
            Push a constant int32 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Int64)">
            <summary>
            Push a constant int64 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.UInt64)">
            <summary>
            Push a constant int64 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Single)">
            <summary>
            Push a constant float onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Double)">
            <summary>
            Push a constant double onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.String)">
            <summary>
            Push a constant string onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Reflection.FieldInfo)">
            <summary>
            Push a constant RuntimeFieldHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Reflection.MethodInfo)">
            <summary>
            Push a constant RuntimeMethodHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant``1">
            <summary>
            Push a constant RuntimeTypeHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadConstant(System.Type)">
            <summary>
            Push a constant RuntimeTypeHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadNull">
            <summary>
            Loads a null reference onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadElement``1">
            <summary>
            Expects a reference to an array and an index on the stack.
            
            Pops both, and pushes the element in the array at the index onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadElement(System.Type)">
            <summary>
            Expects a reference to an array and an index on the stack.
            
            Pops both, and pushes the element in the array at the index onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadElementAddress``1">
            <summary>
            Expects a reference to an array of the given element type and an index on the stack.
            
            Pops both, and pushes the address of the element at the given index.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadElementAddress(System.Type)">
            <summary>
            Expects a reference to an array of the given element type and an index on the stack.
            
            Pops both, and pushes the address of the element at the given index.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadField(System.Reflection.FieldInfo,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary>
            Loads a field onto the stack.
            
            Instance fields expect a reference on the stack, which is popped.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadFieldAddress(System.Reflection.FieldInfo)">
            <summary>
            Loads the address of the given field onto the stack.
            
            If the field is an instance field, a `this` reference is expected on the stack and will be popped.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadFunctionPointer(System.Reflection.MethodInfo)">
            <summary>
            Pushes a pointer to the given function onto the stack, as a native int.
            
            To resolve a method at runtime using an object, use LoadVirtualFunctionPointer instead.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadFunctionPointer(System.Reflection.Emit.MethodBuilder,System.Type[])">
            <summary>
            Pushes a pointer to the given function onto the stack, as a native int.
            
            To resolve a method at runtime using an object, use LoadVirtualFunctionPointer instead.
            
            This method is provided as MethodBuilder cannot be inspected for parameter information at runtime.  If the passed parameterTypes
            do not match the given method, the produced code will be invalid.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadIndirect``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack and pushes the value (of the given type) at that address onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadIndirect(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack and pushes the value (of the given type) at that address onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadLength``1">
            <summary>
            Pops a reference to a rank 1 array off the stack, and pushes it's length onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadLength(System.Type)">
            <summary>
            Pops a reference to a rank 1 array off the stack, and pushes it's length onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadLocal(Sigil.Local)">
            <summary>
            Loads the value in the given local onto the stack.
            
            To create a local, use DeclareLocal().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadLocal(System.String)">
            <summary>
            Loads the value in the local with the given name onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadLocalAddress(Sigil.Local)">
            <summary>
            Pushes a pointer to the given local onto the stack.
            
            To create a local, use DeclareLocal.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadLocalAddress(System.String)">
            <summary>
            Pushes a pointer to the local with the given name onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadObject``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack, and pushes the given value type it points to onto the stack.
            
            For primitive and reference types, use LoadIndirect().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadObject(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack, and pushes the given value type it points to onto the stack.
            
            For primitive and reference types, use LoadIndirect().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadVirtualFunctionPointer(System.Reflection.MethodInfo)">
            <summary>
            Pops an object reference off the stack, and pushes a pointer to the given method's implementation on that object.
            
            For static or non-virtual functions, use LoadFunctionPointer
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LoadVirtualFunctionPointer(System.Reflection.Emit.MethodBuilder,System.Type[])">
            <summary>
            Pops an object reference off the stack, and pushes a pointer to the given method's implementation on that object.
            
            For static or non-virtual functions, use LoadFunctionPointer
            
            This method is provided as MethodBuilder cannot be inspected for parameter information at runtime.  If the passed parameterTypes
            do not match the given method, the produced code will be invalid.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.LocalAllocate">
            <summary>
            Pops a size from the stack, allocates size bytes on the local dynamic memory pool, and pushes a pointer to the allocated block.
            
            LocalAllocate can only be called if the stack is empty aside from the size value.
            
            Memory allocated with LocalAllocate is released when the current method ends execution.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.DeclareLocal``1(System.String,System.Boolean)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            
            Jil will reuse local index on the stack if the corresponding Local instance has been disposed.
            By default Jil will set reused locals to their default value, you can change this behavior
            by passing initializeReused = false.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.DeclareLocal(System.Type,System.String,System.Boolean)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            
            Jil will reuse local index on the stack if the corresponding Local instance has been disposed.
            By default Jil will set reused locals to their default value, you can change this behavior
            by passing initializeReused = false.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.DeclareLocal``1(Sigil.Local@,System.String,System.Boolean)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            
            Jil will reuse local index on the stack if the corresponding Local instance has been disposed.
            By default Jil will set reused locals to their default value, you can change this behavior
            by passing initializeReused = false.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.DeclareLocal(System.Type,Sigil.Local@,System.String,System.Boolean)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            
            Jil will reuse local index on the stack if the corresponding Local instance has been disposed.
            By default Jil will set reused locals to their default value, you can change this behavior
            by passing initializeReused = false.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.MakeReferenceAny``1">
            <summary>
            Converts a pointer or reference to a value on the stack into a TypedReference of the given type.
            
            TypedReferences can be used with ReferenceAnyType and ReferenceAnyValue to pass arbitrary types as parameters.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.MakeReferenceAny(System.Type)">
            <summary>
            Converts a pointer or reference to a value on the stack into a TypedReference of the given type.
            
            TypedReferences can be used with ReferenceAnyType and ReferenceAnyValue to pass arbitrary types as parameters.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewArray``1">
            <summary>
            Pops a size from the stack, allocates a rank-1 array of the given type, and pushes a reference to the new array onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewArray(System.Type)">
            <summary>
            Pops a size from the stack, allocates a rank-1 array of the given type, and pushes a reference to the new array onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``1">
            <summary>
            Invokes the parameterless constructor of the given type, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``2">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``3">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``4">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``5">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``6">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``7">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``8">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``9">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``10">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``11">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``12">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``13">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``14">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``15">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``16">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject``17">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject(System.Type,System.Type[])">
            <summary>
            Pops parameterTypes.Length arguments from the stack, invokes the constructor on the given type that matches parameterTypes, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject(System.Reflection.ConstructorInfo)">
            <summary>
            Pops # of parameters to the given constructor arguments from the stack, invokes the constructor, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.NewObject(System.Reflection.Emit.ConstructorBuilder,System.Type[])">
            <summary>
            Pops # of parameters from the stack, invokes the constructor, and pushes a reference to the new object onto the stack.
            
            This method is provided as ConstructorBuilder cannot be inspected for parameter information at runtime.  If the passed parameterTypes
            do not match the given constructor, the produced code will be invalid.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Nop">
            <summary>
            Emits an instruction that does nothing.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Pop">
            <summary>
            Removes the top value on the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ReferenceAnyType">
            <summary>
            Converts a TypedReference on the stack into a RuntimeTypeHandle for the type contained with it.
            
            __makeref(int) on the stack would become the RuntimeTypeHandle for typeof(int), for example.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ReferenceAnyValue``1">
            <summary>
            Converts a TypedReference on the stack into a reference to the contained object, given the type contained in the TypedReference.
            
            __makeref(int) on the stack would become an int&amp;, for example.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ReferenceAnyValue(System.Type)">
            <summary>
            Converts a TypedReference on the stack into a reference to the contained object, given the type contained in the TypedReference.
            
            __makeref(int) on the stack would become an int&amp;, for example.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.ReThrow">
            <summary>
            From within a catch block, rethrows the exception that caused the catch block to be entered.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Return">
            <summary>
            Ends the execution of the current method.
            
            If the current method does not return void, pops a value from the stack and returns it to the calling method.
            
            Return should leave the stack empty.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.SizeOf``1">
            <summary>
            Pushes the size of the given value type onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.SizeOf(System.Type)">
            <summary>
            Pushes the size of the given value type onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreArgument(System.UInt16)">
            <summary>
            Pops a value off the stack and stores it into the argument to the current method identified by index.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreElement``1">
            <summary>
            Pops a value, an index, and a reference to an array off the stack.  Places the given value into the given array at the given index.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreElement(System.Type)">
            <summary>
            Pops a value, an index, and a reference to an array off the stack.  Places the given value into the given array at the given index.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreField(System.Reflection.FieldInfo,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value from the stack and stores it in the given field.
            
            If the field is an instance member, both a value and a reference to the instance are popped from the stack.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreIndirect``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value of the given type and a pointer off the stack, and stores the value at the address in the pointer.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreIndirect(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value of the given type and a pointer off the stack, and stores the value at the address in the pointer.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreLocal(Sigil.Local)">
            <summary>
            Pops a value off the stack and stores it into the given local.
            
            To create a local, use DeclareLocal().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreLocal(System.String)">
            <summary>
            Pops a value off the stack and stores it in the local with the given name.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreObject``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value type and a pointer off of the stack and copies the given value to the given address.
            
            For primitive and reference types use StoreIndirect.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.StoreObject(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value type and a pointer off of the stack and copies the given value to the given address.
            
            For primitive and reference types use StoreIndirect.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Switch(Sigil.Label[])">
            <summary>
            Pops a value off the stack and branches to the label at the index of that value in the given labels.
            
            If the value is out of range, execution falls through to the next instruction.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Switch(System.String[])">
            <summary>
            Pops a value off the stack and branches to the label at the index of that value in the given label names.
            
            If the value is out of range, execution falls through to the next instruction.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Throw">
            <summary>
            Pops a value off the stack and throws it as an exception.
            
            Throw expects the value to be or extend from a System.Exception.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginExceptionBlock">
            <summary>
            Start a new exception block.  This is roughly analogous to a `try` block in C#, but an exception block contains it's catch and finally blocks.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginExceptionBlock(Sigil.ExceptionBlock@)">
            <summary>
            Start a new exception block.  This is roughly analogous to a `try` block in C#, but an exception block contains it's catch and finally blocks.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.EndExceptionBlock(Sigil.ExceptionBlock)">
            <summary>
            Ends the given exception block.
            
            All catch and finally blocks associated with the given exception block must be ended before this method is called.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginCatchBlock``1(Sigil.ExceptionBlock)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginCatchBlock``1(Sigil.ExceptionBlock,Sigil.CatchBlock@)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginCatchAllBlock(Sigil.ExceptionBlock)">
             <summary>
             Begins a catch block for all exceptions in the given exception block
            
             The given exception block must still be open.
             
             Equivalent to BeginCatchBlock(typeof(Exception), forTry).
             </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginCatchAllBlock(Sigil.ExceptionBlock,Sigil.CatchBlock@)">
             <summary>
             Begins a catch block for all exceptions in the given exception block
            
             The given exception block must still be open.
             
             Equivalent to BeginCatchBlock(typeof(Exception), forTry).
             </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginCatchBlock(Sigil.ExceptionBlock,System.Type)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginCatchBlock(Sigil.ExceptionBlock,System.Type,Sigil.CatchBlock@)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.EndCatchBlock(Sigil.CatchBlock)">
            <summary>
            Ends the given catch block.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginFinallyBlock(Sigil.ExceptionBlock,Sigil.FinallyBlock@)">
            <summary>
            Begins a finally block on the given exception block.
            
            Only one finally block can be defined per exception block, and the block cannot appear within a catch block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.BeginFinallyBlock(Sigil.ExceptionBlock)">
            <summary>
            Begins a finally block on the given exception block.
            
            Only one finally block can be defined per exception block, and the block cannot appear within a catch block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.EndFinallyBlock(Sigil.FinallyBlock)">
            <summary>
            Ends the given finally block.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Unbox``1">
            <summary>
            Pops a boxed value from the stack and pushes a pointer to it's unboxed value.
            
            To load the value directly onto the stack, use UnboxAny().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Unbox(System.Type)">
            <summary>
            Pops a boxed value from the stack and pushes a pointer to it's unboxed value.
            
            To load the value directly onto the stack, use UnboxAny().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnboxAny``1">
            <summary>
            Pops a boxed value from the stack, unboxes it and pushes the value onto the stack.
            
            To get an address for the unboxed value instead, use Unbox().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.UnboxAny(System.Type)">
            <summary>
            Pops a boxed value from the stack, unboxes it and pushes the value onto the stack.
            
            To get an address for the unboxed value instead, use Unbox().
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.Validate">
            <summary>
            Called to confirm that the IL emit'd to date can be turned into a delegate without error.
            
            Checks that the stack is empty, that all paths returns, that all labels are marked, etc. etc.
            </summary>
        </member>
        <member name="M:Sigil.Emit`1.WriteLine(System.String,Sigil.Local[])">
            <summary>
            Emits IL that calls Console.WriteLine(string) for the given string if no locals are passed.
            
            If any locals are passed, line is treated as a format string and local values are used in a call
            to Console.WriteLine(string, object[]).
            </summary>
        </member>
        <member name="T:Sigil.EmitShorthand`1">
            <summary>
            A version of Emit with shorter named versions of it's methods.
            
            Method names map more or less to OpCodes fields.
            </summary>
        </member>
        <member name="P:Sigil.EmitShorthand`1.AllowsUnverifiableCIL">
            <summary>
            Returns true if this Emit can make use of unverifiable instructions.
            </summary>
        </member>
        <member name="P:Sigil.EmitShorthand`1.MaxStackSize">
            <summary>
            Returns the maxmimum number of items on the stack for the IL stream created with the current emit.
            
            This is not the maximum that *can be placed*, but the maximum that actually are.
            </summary>
        </member>
        <member name="P:Sigil.EmitShorthand`1.Locals">
            <summary>
            Lookup for the locals currently in scope by name.
            
            Locals go out of scope when released (by calling Dispose() directly, or via using) and go into scope
            immediately after a DeclareLocal()
            </summary>
        </member>
        <member name="P:Sigil.EmitShorthand`1.Labels">
            <summary>
            Lookup for declared labels by name.
            </summary>
        </member>
        <member name="M:Sigil.EmitShorthand`1.AsLonghand">
            <summary>
            Returns the original Emit instance that AsShorthand() was called on.
            </summary>
        </member>
        <member name="M:Sigil.EmitShorthand`1.Instructions">
            <summary>
            Returns a string representation of the CIL opcodes written to this Emit to date.
            
            This method is meant for debugging purposes only.
            </summary>
        </member>
        <member name="M:Sigil.EmitShorthand`1.DeclareLocal``1(System.String,System.Boolean)">
            <summary cref="M:Sigil.Emit`1.DeclareLocal``1(System.String, System.Boolean)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.DeclareLocal``1(Sigil.Local@,System.String,System.Boolean)">
            <summary cref="M:Sigil.Emit`1.DeclareLocal``1(Sigil.Local, System.String, System.Boolean)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.DeclareLocal(System.Type,System.String,System.Boolean)">
            <summary cref="M:Sigil.Emit`1.DeclareLocal(System.Type, System.String, System.Boolean)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.DeclareLocal(System.Type,Sigil.Local@,System.String,System.Boolean)">
            <summary cref="M:Sigil.Emit`1.DeclareLocal(System.Type, Sigil.Local, System.String, System.Boolean)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.DefineLabel(System.String)">
            <summary cref="M:Sigil.Emit`1.DefineLabel(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.DefineLabel(Sigil.Label@,System.String)">
            <summary cref="M:Sigil.Emit`1.DefineLabel(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.MarkLabel(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.MarkLabel(Sigil.Label, IEnumerable``1)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.MarkLabel(System.String)">
            <summary cref="M:Sigil.Emit`1.MarkLabel(System.String, IEnumerable``1)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginExceptionBlock">
            <summary cref="M:Sigil.Emit`1.BeginExceptionBlock" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginExceptionBlock(Sigil.ExceptionBlock@)">
            <summary cref="M:Sigil.Emit`1.BeginExceptionBlock" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginCatchBlock``1(Sigil.ExceptionBlock)">
            <summary cref="M:Sigil.Emit`1.BeginCatchBlock``1(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginCatchBlock``1(Sigil.ExceptionBlock,Sigil.CatchBlock@)">
            <summary cref="M:Sigil.Emit`1.BeginCatchBlock``1(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginCatchBlock(Sigil.ExceptionBlock,System.Type)">
            <summary cref="M:Sigil.Emit`1.BeginCatchBlock(System.Type, Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginCatchBlock(Sigil.ExceptionBlock,System.Type,Sigil.CatchBlock@)">
            <summary cref="M:Sigil.Emit`1.BeginCatchBlock(System.Type, Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.EndCatchBlock(Sigil.CatchBlock)">
            <summary cref="M:Sigil.Emit`1.EndCatchBlock(Sigil.CatchBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginFinallyBlock(Sigil.ExceptionBlock)">
            <summary cref="M:Sigil.Emit`1.BeginFinallyBlock(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.BeginFinallyBlock(Sigil.ExceptionBlock,Sigil.FinallyBlock@)">
            <summary cref="M:Sigil.Emit`1.BeginFinallyBlock(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.EndFinallyBlock(Sigil.FinallyBlock)">
            <summary cref="M:Sigil.Emit`1.EndFinallyBlock(Sigil.FinallyBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.EndExceptionBlock(Sigil.ExceptionBlock)">
            <summary cref="M:Sigil.Emit`1.EndExceptionBlock(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.CreateDelegate(Sigil.OptimizationOptions)">
            <summary cref="M:Sigil.Emit`1.CreateDelegate(Sigil.OptimizationOptions)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.CreateMethod">
            <summary cref="M:Sigil.Emit`1.CreateMethod" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.CreateConstructor">
            <summary cref="M:Sigil.Emit`1.CreateConstructor" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Add">
            <summary cref="M:Sigil.Emit`1.Add" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Add_Ovf">
            <summary cref="M:Sigil.Emit`1.AddOverflow" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Add_Ovf_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedAddOverflow" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.And">
            <summary cref="M:Sigil.Emit`1.And" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Beq(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.BranchIfEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Beq(System.String)">
            <summary cref="M:Sigil.Emit`1.BranchIfEqual(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bge(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.BranchIfGreaterOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bge(System.String)">
            <summary cref="M:Sigil.Emit`1.BranchIfGreaterOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bge_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfGreaterOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bge_Un(System.String)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfGreaterOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bgt(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.BranchIfGreater(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bgt(System.String)">
            <summary cref="M:Sigil.Emit`1.BranchIfGreater(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bgt_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfGreater(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bgt_Un(System.String)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfGreater(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ble(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.BranchIfLessOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ble(System.String)">
            <summary cref="M:Sigil.Emit`1.BranchIfLessOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ble_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfLessOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ble_Un(System.String)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfLessOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Blt(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.BranchIfLess(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Blt(System.String)">
            <summary cref="M:Sigil.Emit`1.BranchIfLess(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Blt_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfLess(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Blt_Un(System.String)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfLess(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bne_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfNotEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Bne_Un(System.String)">
            <summary cref="M:Sigil.Emit`1.UnsignedBranchIfNotEqual(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Box``1">
            <summary cref="M:Sigil.Emit`1.Box``1()" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Box(System.Type)">
            <summary cref="M:Sigil.Emit`1.Box(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Br(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.Branch(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Br(System.String)">
            <summary cref="M:Sigil.Emit`1.Branch(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Break">
            <summary cref="M:Sigil.Emit`1.Break" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Brfalse(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.BranchIfFalse(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Brfalse(System.String)">
            <summary cref="M:Sigil.Emit`1.BranchIfFalse(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Brtrue(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.BranchIfTrue(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Brtrue(System.String)">
            <summary cref="M:Sigil.Emit`1.BranchIfTrue(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Call(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit`1.Call(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Calli(System.Reflection.CallingConventions,System.Type,System.Type[])">
            <summary cref="M:Sigil.Emit`1.CallIndirect(System.Reflection.CallingConventions,System.Type,System.Type[])" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Callvirt(System.Reflection.MethodInfo,System.Type)">
            <summary cref="M:Sigil.Emit`1.CallVirtual(System.Reflection.MethodInfo, System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Castclass``1">
            <summary cref="M:Sigil.Emit`1.CastClass``1" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Castclass(System.Type)">
            <summary cref="M:Sigil.Emit`1.CastClass(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ceq">
            <summary cref="M:Sigil.Emit`1.CompareEqual" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Cgt">
            <summary cref="M:Sigil.Emit`1.CompareGreaterThan" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Cgt_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedCompareGreaterThan" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ckfinite">
            <summary cref="M:Sigil.Emit`1.CheckFinite" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Clt">
            <summary cref="M:Sigil.Emit`1.CompareLessThan" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Clt_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedCompareLessThan" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Conv_Ovf_Un``1">
            <summary cref="M:Sigil.Emit`1.UnsignedConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Conv_Ovf_Un(System.Type)">
            <summary cref="M:Sigil.Emit`1.UnsignedConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Conv_R_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedConvertToFloat" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Conv``1">
            <summary cref="M:Sigil.Emit`1.Convert(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Conv(System.Type)">
            <summary cref="M:Sigil.Emit`1.Convert(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Conv_Ovf``1">
            <summary cref="M:Sigil.Emit`1.ConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Conv_Ovf(System.Type)">
            <summary cref="M:Sigil.Emit`1.ConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Cpblk(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.CopyBlock(System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Cpobj``1">
            <summary cref="M:Sigil.Emit`1.CopyObject(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Cpobj(System.Type)">
            <summary cref="M:Sigil.Emit`1.CopyObject(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Div">
            <summary cref="M:Sigil.Emit`1.Divide" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Div_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedDivide" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Dup">
            <summary cref="M:Sigil.Emit`1.Duplicate" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Initblk(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.InitializeBlock(System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Initobj``1">
            <summary cref="M:Sigil.Emit`1.InitializeObject(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Initobj(System.Type)">
            <summary cref="M:Sigil.Emit`1.InitializeObject(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Isinst``1">
            <summary cref="M:Sigil.Emit`1.IsInstance(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Isinst(System.Type)">
            <summary cref="M:Sigil.Emit`1.IsInstance(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Jmp(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit`1.Jump(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldarg(System.UInt16)">
            <summary cref="M:Sigil.Emit`1.LoadArgument(System.Int32)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldarga(System.UInt16)">
            <summary cref="M:Sigil.Emit`1.LoadArgumentAddress(System.Int32)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldc(System.Boolean)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Boolean)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldc(System.Single)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Single)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldc(System.Double)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Double)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldc(System.UInt32)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.UInt32)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldc(System.Int32)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Int32)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldc(System.Int64)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Int64)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldc(System.UInt64)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.UInt64)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldelem``1">
            <summary cref="M:Sigil.Emit`1.LoadElement``1" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldelem(System.Type)">
            <summary cref="M:Sigil.Emit`1.LoadElement(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldelema``1">
            <summary cref="M:Sigil.Emit`1.LoadElementAddress``1" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldelema(System.Type)">
            <summary cref="M:Sigil.Emit`1.LoadElementAddress(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldfld(System.Reflection.FieldInfo,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.LoadField(System.Reflection.FieldInfo, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldflda(System.Reflection.FieldInfo)">
            <summary cref="M:Sigil.Emit`1.LoadFieldAddress(System.Reflection.FieldInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldftn(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit`1.LoadFunctionPointer(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldind``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.LoadIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldind(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.LoadIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldlen``1">
            <summary cref="M:Sigil.Emit`1.LoadLength``1" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldlen(System.Type)">
            <summary cref="M:Sigil.Emit`1.LoadLength(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldloc(Sigil.Local)">
            <summary cref="M:Sigil.Emit`1.LoadLocal(Sigil.Local)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldloc(System.String)">
            <summary cref="M:Sigil.Emit`1.LoadLocal(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldloca(Sigil.Local)">
            <summary cref="M:Sigil.Emit`1.LoadLocalAddress(Sigil.Local)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldloca(System.String)">
            <summary cref="M:Sigil.Emit`1.LoadLocalAddress(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldnull">
            <summary cref="M:Sigil.Emit`1.LoadNull" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldobj``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.LoadObject(System.Type, System.Boolen, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldobj(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.LoadObject(System.Type, System.Boolen, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldstr(System.String)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldtoken(System.Reflection.FieldInfo)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Reflection.FieldInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldtoken(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldtoken``1">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldtoken(System.Type)">
            <summary cref="M:Sigil.Emit`1.LoadConstant(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ldvirtftn(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit`1.LoadVirtualFunctionPointer(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Leave(Sigil.Label)">
            <summary cref="M:Sigil.Emit`1.Leave(Sigil.Label)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Leave(System.String)">
            <summary cref="M:Sigil.Emit`1.Leave(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Localloc">
            <summary cref="M:Sigil.Emit`1.LocalAllocate" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Mul">
            <summary cref="M:Sigil.Emit`1.Multiply" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Mul_Ovf">
            <summary cref="M:Sigil.Emit`1.MultiplyOverflow" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Mul_Ovf_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedMultiplyOverflow" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Neg">
            <summary cref="M:Sigil.Emit`1.Negate" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Newarr``1">
            <summary cref="M:Sigil.Emit`1.NewArray(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Newarr(System.Type)">
            <summary cref="M:Sigil.Emit`1.NewArray(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Newobj(System.Reflection.ConstructorInfo)">
            <summary cref="M:Sigil.Emit`1.NewObject(System.Reflection.ConstructorInfo)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Nop">
            <summary cref="M:Sigil.Emit`1.Nop" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Not">
            <summary cref="M:Sigil.Emit`1.Not" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Or">
            <summary cref="M:Sigil.Emit`1.Or" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Pop">
            <summary cref="M:Sigil.Emit`1.Pop" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Rem">
            <summary cref="M:Sigil.Emit`1.Remainder" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Rem_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedRemainder" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Ret">
            <summary cref="M:Sigil.Emit`1.Return" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Rethrow">
            <summary cref="M:Sigil.Emit`1.ReThrow" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Shl">
            <summary cref="M:Sigil.Emit`1.ShiftLeft" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Shr">
            <summary cref="M:Sigil.Emit`1.ShiftRight" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Shr_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedShiftRight" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Sizeof``1">
            <summary cref="M:Sigil.Emit`1.SizeOf(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Sizeof(System.Type)">
            <summary cref="M:Sigil.Emit`1.SizeOf(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Starg(System.UInt16)">
            <summary cref="M:Sigil.Emit`1.StoreArgument(System.Int32)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stelem``1">
            <summary cref="M:Sigil.Emit`1.StoreElement``1" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stelem(System.Type)">
            <summary cref="M:Sigil.Emit`1.StoreElement(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stfld(System.Reflection.FieldInfo,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.StoreField(System.Reflection.FieldInfo, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stind``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.StoreIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stind(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.StoreIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stloc(Sigil.Local)">
            <summary cref="M:Sigil.Emit`1.StoreLocal(Sigil.Local)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stloc(System.String)">
            <summary cref="M:Sigil.Emit`1.StoreLocal(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stobj``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.StoreObject(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Stobj(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit`1.StoreObject(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Sub">
            <summary cref="M:Sigil.Emit`1.Subtract" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Sub_Ovf">
            <summary cref="M:Sigil.Emit`1.SubtractOverflow" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Sub_Ovf_Un">
            <summary cref="M:Sigil.Emit`1.UnsignedSubtractOverflow" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Switch(Sigil.Label[])">
            <summary cref="M:Sigil.Emit`1.Switch(Sigil.Label[])" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Switch(System.String[])">
            <summary cref="M:Sigil.Emit`1.Switch(System.String[])" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Throw">
            <summary cref="M:Sigil.Emit`1.Throw" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Unbox``1">
            <summary cref="M:Sigil.Emit`1.Unbox(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Unbox(System.Type)">
            <summary cref="M:Sigil.Emit`1.Unbox(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Unbox_Any``1">
            <summary cref="M:Sigil.Emit`1.UnboxAny(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Unbox_Any(System.Type)">
            <summary cref="M:Sigil.Emit`1.UnboxAny(System.Type)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.WriteLine(System.String,Sigil.Local[])">
            <summary cref="M:Sigil.Emit`1.WriteLine(System.String)" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.Xor">
            <summary cref="M:Sigil.Emit`1.Xor" />
        </member>
        <member name="M:Sigil.EmitShorthand`1.TraceOperationResultUsage">
            <summary cref="M:Sigil.Emit`1.TraceOperationResultUsage" />
        </member>
        <member name="T:Sigil.ExceptionBlock">
            <summary>
            Represents an ExceptionBlock, which is roughly analogous to a try + catch + finally block in C#.
            
            To create an ExceptionBlock call BeginExceptionBlock().
            </summary>
        </member>
        <member name="P:Sigil.ExceptionBlock.Label">
            <summary>
            A label which marks the end of the ExceptionBlock.
            
            This Label is meant to be targetted by Leave() from anywhere except a FinallyBlock
            in the ExceptionBlock.
            
            Remember that it is illegal to branch from within an ExceptionBlock to outside.
            </summary>
        </member>
        <member name="T:Sigil.FinallyBlock">
            <summary>
            Represents a finally block, which appears within an ExceptionBlock.
            
            This is roughly analogous to `finally` in C#.
            </summary>
        </member>
        <member name="P:Sigil.FinallyBlock.ExceptionBlock">
            <summary>
            The ExceptionBlock this FinallyBlock appears as part of.
            </summary>
        </member>
        <member name="T:Sigil.Impl.NonGenericPlaceholderDelegate">
            <summary>
            This is a placeholder type used from EmitNonGeneric to take the place of DelegateType in proper Emit.
            </summary>
        </member>
        <member name="F:Sigil.Impl.RollingVerifier.UsesStrictBranchVerification">
            From the spec [see section - III.1.7.5 Backward branch constraints]:
              In particular, if that single-pass analysis arrives at an instruction, call it location X, that 
              immediately follows an unconditional branch, and where X is not the target of an earlier branch 
              instruction, then the state of the evaluation stack at X, clearly, cannot be derived from existing 
              information. In this case, the CLI demands that the evaluation stack at X be empty.
            
            In practice, DynamicMethods don't need to follow this rule *but* that doesn't mean stricter
            verification won't be needed elsewhere.
            
            If this is set, then an "expectation of empty stack" transition is inserted before unconditional branches
            where needed.
        </member>
        <member name="T:Sigil.Impl.NullType">
            <summary>
            This type represents a provably null value on the stack.
            
            Nulls typically arrive on the stack via LoadNull.
            
            Null can be assigned to any reference type safely, without the need for a CastClass.
            
            This type is exposed to allow for stack assertions containing null via Emit.MarkLabel.
            </summary>
        </member>
        <member name="T:Sigil.Impl.NativeIntType">
            <summary>
            This type represents a "native int" on the stack.
            
            The size of native int varies depending on the architecture an assembly is executed on.
            Raw pointers are often of type native int.
            
            This type is exposed to allow for stack assertions containing native int via Emit.MarkLabel.
            </summary>
        </member>
        <member name="T:Sigil.Impl.TypeHelpers">
            <summary>
            Contains helper methods to shim over the difference between different Type APIs in
            different frameworks
            </summary>
        </member>
        <member name="M:Sigil.Impl.TypeOnStack.Mark(Sigil.Impl.InstructionAndTransitions,System.Int32)">
            <summary>
            Call to indicate that something on the stack was used
            as the #{index}'d (starting at 0) parameter to the {code} 
            opcode.
            </summary>
        </member>
        <member name="M:Sigil.Impl.TypeOnStack.CountMarks(System.Reflection.Emit.OpCode,System.Int32)">
            <summary>
            Returns the # of times this value was used as the given #{index}'d parameter to the {code} instruction.
            </summary>
        </member>
        <member name="M:Sigil.Impl.TypeOnStack.CountMarks">
            <summary>
            Returns the total number of times this value was marked.
            </summary>
        </member>
        <member name="T:Sigil.Label">
            <summary>
            Represents a Label in a CIL stream, and thus a Leave and Branch target.
            
            To create a Label call DefineLabel().
            
            Before creating a delegate, all Labels must be marked.  To mark a label, call MarkLabel().
            </summary>
        </member>
        <member name="P:Sigil.Label.Name">
            <summary>
            The name of this Label.
            
            If one is omitted during creation a random one is created instead.
            
            Names are purely for debugging aid, and will not appear in the generated delegate.
            </summary>
        </member>
        <member name="M:Sigil.Label.ToString">
            <summary>
            Equivalent to Name.
            </summary>
        </member>
        <member name="T:Sigil.LabelLookup">
            <summary>
            Provides a way to lookup labels declared with an emit.
            </summary>
        </member>
        <member name="P:Sigil.LabelLookup.Item(System.String)">
            <summary>
            Returns the label with the given name.
            
            Throws KeyNotFoundException if no label by that name is found".
            </summary>
        </member>
        <member name="P:Sigil.LabelLookup.Count">
            <summary>
            Returns the number of labels declared
            </summary>
        </member>
        <member name="P:Sigil.LabelLookup.Names">
            <summary>
            Returns the names of all the declared labels
            </summary>
        </member>
        <member name="T:Sigil.Local">
            <summary>
            Represents a variable local to the delegate being created.
            
            To create a Local, call DeclareLocal().
            </summary>
        </member>
        <member name="P:Sigil.Local.Name">
            <summary>
            The name of this local.
            
            If one is omitted during creation a random one is created instead.
            
            Names are purely for debugging aid, and will not appear in the generated delegate.
            </summary>
        </member>
        <member name="P:Sigil.Local.LocalType">
            <summary>
            The type stored in this local.
            </summary>
        </member>
        <member name="M:Sigil.Local.ToString">
            <summary>
            Returns the type and name of this Local, in string form.
            </summary>
        </member>
        <member name="M:Sigil.Local.Dispose">
            <summary>
            Frees this local.
            
            While not strictly required, freeing a local allows it's index to be reused.
            
            Locals are only eligible for reuse when the new local is exactly the same type.
            </summary>
        </member>
        <member name="T:Sigil.LocalLookup">
            <summary>
            Provides a way to lookup locals in scope by name.
            </summary>
        </member>
        <member name="P:Sigil.LocalLookup.Item(System.String)">
            <summary>
            Returns the local with the given name.
            
            Throws KeyNotFoundException if no local by that name is found".
            </summary>
        </member>
        <member name="P:Sigil.LocalLookup.Count">
            <summary>
            Returns the number of locals in scope
            </summary>
        </member>
        <member name="P:Sigil.LocalLookup.Names">
            <summary>
            Returns the names of all the locals in scope
            </summary>
        </member>
        <member name="T:Sigil.NonGeneric.Emit">
            <summary>
            Helper for CIL generation that fails as soon as a sequence of instructions
            can be shown to be invalid.
            
            Unlike Emit&lt;DelegateType&gt;, does not require a known delegate type to construct.
            However, if possible use Emit&lt;DelegateType&gt; so as to avoid common type mistakes.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ArgumentList">
            <summary>
            Pushes a pointer to the current argument list onto the stack.
            
            This instruction can only be used in VarArgs methods.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Add">
            <summary>
            Pops two arguments off the stack, adds them, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.AddOverflow">
            <summary>
            Pops two arguments off the stack, adds them, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedAddOverflow">
            <summary>
            Pops two arguments off the stack, adds them as if they were unsigned, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Divide">
            <summary>
            Pops two arguments off the stack, divides the second by the first, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedDivide">
            <summary>
            Pops two arguments off the stack, divides the second by the first as if they were unsigned, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Multiply">
            <summary>
            Pops two arguments off the stack, multiplies them, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.MultiplyOverflow">
            <summary>
            Pops two arguments off the stack, multiplies them, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedMultiplyOverflow">
            <summary>
            Pops two arguments off the stack, multiplies them as if they were unsigned, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Remainder">
            <summary>
            Pops two arguments off the stack, calculates the remainder of the second divided by the first, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedRemainder">
            <summary>
            Pops two arguments off the stack, calculates the remainder of the second divided by the first as if both were unsigned, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Subtract">
            <summary>
            Pops two arguments off the stack, subtracts the first from the second, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.SubtractOverflow">
            <summary>
            Pops two arguments off the stack, subtracts the first from the second, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedSubtractOverflow">
            <summary>
            Pops two arguments off the stack, subtracts the first from the second as if they were unsigned, and pushes the result.
            
            Throws an OverflowException if the result overflows the destination type.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Negate">
            <summary>
            Pops an argument off the stack, negates it, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.And">
            <summary>
            Pops two arguments off the stack, performs a bitwise and, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Or">
            <summary>
            Pops two arguments off the stack, performs a bitwise or, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Xor">
            <summary>
            Pops two arguments off the stack, performs a bitwise xor, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Not">
            <summary>
            Pops one argument off the stack, performs a bitwise inversion, and pushes the result.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ShiftLeft">
            <summary>
            Pops two arguments off the stack, shifts the second value left by the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ShiftRight">
            <summary>
            Pops two arguments off the stack, shifts the second value right by the first value.
            
            Sign extends from the left.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedShiftRight">
            <summary>
            Pops two arguments off the stack, shifts the second value right by the first value.
            
            Acts as if the value were unsigned, zeros always coming in from the left.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Box``1">
            <summary>
            Boxes the given value type on the stack, converting it into a reference.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Box(System.Type)">
            <summary>
            Boxes the given value type on the stack, converting it into a reference.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Branch(Sigil.Label)">
            <summary>
            Unconditionally branches to the given label.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Branch(System.String)">
            <summary>
            Unconditionally branches to the label with the given name.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, if both are equal branches to the given label.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfEqual(System.String)">
            <summary>
            Pops two arguments from the stack, if both are equal branches to the label with the given name.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfNotEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, if they are not equal (when treated as unsigned values) branches to the given label.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfNotEqual(System.String)">
            <summary>
            Pops two arguments from the stack, if they are not equal (when treated as unsigned values) branches to the label with the given name.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfGreaterOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfGreaterOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfGreaterOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfGreaterOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfGreater(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfGreater(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfGreater(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is greater than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfGreater(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is greater than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfLessOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfLessOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than or equal to the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfLessOrEqual(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfLessOrEqual(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than or equal to the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfLess(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfLess(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than the first value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfLess(Sigil.Label)">
            <summary>
            Pops two arguments from the stack, branches to the given label if the second value is less than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedBranchIfLess(System.String)">
            <summary>
            Pops two arguments from the stack, branches to the label with the given name if the second value is less than the first value (when treated as unsigned values).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfFalse(Sigil.Label)">
            <summary>
            Pops one argument from the stack, branches to the given label if the value is false.
            
            A value is false if it is zero or null.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfFalse(System.String)">
            <summary>
            Pops one argument from the stack, branches to the label with the given name if the value is false.
            
            A value is false if it is zero or null.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfTrue(Sigil.Label)">
            <summary>
            Pops one argument from the stack, branches to the given label if the value is true.
            
            A value is true if it is non-zero or non-null.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BranchIfTrue(System.String)">
            <summary>
            Pops one argument from the stack, branches to the label with the given name if the value is true.
            
            A value is true if it is non-zero or non-null.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Break">
            <summary>
            Emits a break instruction for use with a debugger.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Call(System.Reflection.MethodInfo,System.Type[])">
            <summary>
            Calls the given method.  Pops its arguments in reverse order (left-most deepest in the stack), and pushes the return value if it is non-void.
            
            If the given method is an instance method, the `this` reference should appear before any parameters.
            
            Call does not respect overrides, the implementation defined by the given MethodInfo is what will be called at runtime.
            
            To call overrides of instance methods, use CallVirtual.
            
            When calling VarArgs methods, arglist should be set to the types of the extra parameters to be passed.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Call(System.Reflection.ConstructorInfo)">
            <summary>
            Calls the given constructor.  Pops its arguments in reverse order (left-most deepest in the stack).
            
            The `this` reference should appear before any parameters.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Call(Sigil.NonGeneric.Emit,System.Type[])">
            <summary>
            Calls the method being constructed by the given emit.  Emits so used must have been constructed with BuildMethod or related methods.
            
            Pops its arguments in reverse order (left-most deepest in the stack), and pushes the return value if it is non-void.
            
            If the given method is an instance method, the `this` reference should appear before any parameters.
            
            Call does not respect overrides, the implementation defined by the given MethodInfo is what will be called at runtime.
            
            To call overrides of instance methods, use CallVirtual.
            Recursive calls can only be performed with DynamicMethods, other passed in Emits must already have their methods created.
            When calling VarArgs methods, arglist should be set to the types of the extra parameters to be passed.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes a void return and no parameters.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``1(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and no parameters.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``2(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``3(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``4(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``5(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``6(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``7(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``8(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``9(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``10(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``11(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``12(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``13(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``14(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``15(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``16(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect``17(System.Reflection.CallingConventions)">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This helper assumes ReturnType as a return and parameters of the types given in ParameterType*.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect(System.Reflection.CallingConventions,System.Type,System.Type[],System.Type[])">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            
            This override allows an arglist to be passed for calling VarArgs methods.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallIndirect(System.Reflection.CallingConventions,System.Type,System.Type[])">
            <summary>
            Pops a pointer to a method, and then all it's arguments (in reverse order, left-most parameter is deepest on the stack) and calls
            invokes the method pointer.  If the method returns a non-void result, it is pushed onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CallVirtual(System.Reflection.MethodInfo,System.Type,System.Type[])">
            <summary>
            Calls the given method virtually.  Pops its arguments in reverse order (left-most deepest in the stack), and pushes the return value if it is non-void.
            
            The `this` reference should appear before any arguments (deepest in the stack).
            
            The method invoked at runtime is determined by the type of the `this` reference.
            
            If the method invoked shouldn't vary (or if the method is static), use Call instead.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CastClass``1">
            <summary>
            Cast a reference on the stack to the given reference type.
            
            If the cast is not legal, a CastClassException will be thrown at runtime.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CastClass(System.Type)">
            <summary>
            Cast a reference on the stack to the given reference type.
            
            If the cast is not legal, a CastClassException will be thrown at runtime.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CheckFinite">
            <summary>
            Throws an ArithmeticException on runtime if the value on the stack is not a finite number.
            
            This leaves the value checked on the stack, rather than popping it as might be expected.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CompareEqual">
            <summary>
            Pops two values from the stack, and pushes a 1 if they are equal and 0 if they are not.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CompareGreaterThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is greater than the first value and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedCompareGreaterThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is greater than the first value (as unsigned values) and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CompareLessThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is less than the first value and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedCompareLessThan">
            <summary>
            Pops two arguments from the stack, pushes a 1 if the second value is less than the first value (as unsigned values) and a 0 otherwise.
            
            New value on the stack is an Int32.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Convert``1">
            <summary>
            Convert a value on the stack to the given non-character primitive type.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Convert(System.Type)">
            <summary>
            Convert a value on the stack to the given non-character primitive type.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ConvertOverflow``1">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ConvertOverflow(System.Type)">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedConvertOverflow``1">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type as if it were unsigned.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedConvertOverflow(System.Type)">
            <summary>
            Convert a value on the stack to the given non-character, non-float, non-double primitive type as if it were unsigned.
            If the conversion would overflow at runtime, an OverflowException is thrown.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr). 
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnsignedConvertToFloat">
            <summary>
            Converts a primitive type on the stack to a float, as if it were unsigned.
            
            Primitives are int8, uint8, int16, uint16, int32, uint32, int64, uint64, float, double, native int (IntPtr), and unsigned native int (UIntPtr).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CopyBlock(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Expects a destination pointer, a source pointer, and a length on the stack.  Pops all three values.
            
            Copies length bytes from destination to the source.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CopyObject``1">
            <summary>
            Takes a destination pointer, a source pointer as arguments.  Pops both off the stack.
            
            Copies the given value type from the source to the destination.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CopyObject(System.Type)">
            <summary>
            Takes a destination pointer, a source pointer as arguments.  Pops both off the stack.
            
            Copies the given value type from the source to the destination.
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.Emit.Labels">
            <summary>
            Lookup for declared labels by name.
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.Emit.MaxStackSize">
            <summary>
            Returns the maxmimum number of items on the stack for the IL stream created with the current emit.
            
            This is not the maximum that *can be placed*, but the maximum that actually are.
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.Emit.Locals">
            <summary>
            Lookup for the locals currently in scope by name.
            
            Locals go out of scope when released (by calling Dispose() directly, or via using) and go into scope
            immediately after a DeclareLocal()
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.Emit.AllowsUnverifiableCIL">
            <summary>
            Returns true if this Emit can make use of unverifiable instructions.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewDynamicMethod(System.Type,System.Type[],System.String,System.Reflection.Emit.ModuleBuilder,System.Boolean,System.Boolean)">
            <summary>
            Creates a new EmitNonGeneric, optionally using the provided name and module for the inner DynamicMethod.
            
            If name is not defined, a sane default is generated.
            
            If module is not defined, a module with the same trust as the executing assembly is used instead.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateDelegate(System.Type,System.String@,Sigil.OptimizationOptions)">
            <summary>
            Converts the CIL stream into a delegate.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            `instructions` will be set to a representation of the instructions making up the returned delegate.
            Note that this string is typically *not* enough to regenerate the delegate, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned delegate fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateDelegate(System.Type,Sigil.OptimizationOptions)">
            <summary>
            Converts the CIL stream into a delegate.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateDelegate``1(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Converts the CIL stream into a delegate.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            `instructions` will be set to a representation of the instructions making up the returned delegate.
            Note that this string is typically *not* enough to regenerate the delegate, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned delegate fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateDelegate``1(Sigil.OptimizationOptions)">
            <summary>
            Converts the CIL stream into a delegate.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BuildMethod(System.Type,System.Type[],System.Reflection.Emit.TypeBuilder,System.String,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, suitable for building a method on the given TypeBuilder.
            
            The DelegateType and MethodBuilder must agree on return types, parameter types, and parameter counts.
            
            If you intend to use unveriable code, you must set allowUnverifiableCode to true.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BuildInstanceMethod(System.Type,System.Type[],System.Reflection.Emit.TypeBuilder,System.String,System.Reflection.MethodAttributes,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Convenience method for creating instance methods.
            
            Equivalent to calling to BuildMethod, but with CallingConventions.HasThis.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BuildStaticMethod(System.Type,System.Type[],System.Reflection.Emit.TypeBuilder,System.String,System.Reflection.MethodAttributes,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Convenience method for creating static methods.
            
            Equivalent to calling to BuildMethod, but with MethodAttributes.Static set and CallingConventions.Standard.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateMethod(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the MethodBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a MethodBuilder, which can be used to define overrides or for further inspection.
            
            `instructions` will be set to a representation of the instructions making up the returned method.
            Note that this string is typically *not* enough to regenerate the method, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned method fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateMethod(Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the MethodBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a MethodBuilder, which can be used to define overrides or for further inspection.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BuildConstructor(System.Type[],System.Reflection.Emit.TypeBuilder,System.Reflection.MethodAttributes,System.Reflection.CallingConventions,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, suitable for building a constructor on the given TypeBuilder.
            
            The DelegateType and TypeBuilder must agree on parameter types and parameter counts.
            
            If you intend to use unveriable code, you must set allowUnverifiableCode to true.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BuildTypeInitializer(System.Reflection.Emit.TypeBuilder,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new Emit, suitable for building a type initializer on the given TypeBuilder.
            
            If you intend to use unveriable code, you must set allowUnverifiableCode to true.
            
            If doVerify is false (default is true) Sigil will *not* throw an exception on invalid IL.  This is faster, but the benefits
            of Sigil are reduced to "a nicer ILGenerator interface".
            
            If strictBranchValidation is true (default is false) Sigil will enforce "Backward branch constraints" which are *technically* required
            for valid CIL, but in practice often ignored.  The most common case to set this option is if you are generating types to write to disk.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateConstructor(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            
            `instructions` will be set to a representation of the instructions making up the returned constructor.
            Note that this string is typically *not* enough to regenerate the constructor, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned constructor fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateConstructor(Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateTypeInitializer(System.String@,Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            
            `instructions` will be set to a representation of the instructions making up the returned constructor.
            Note that this string is typically *not* enough to regenerate the constructor, it is available for
            debugging purposes only.  Consumers may find it useful to log the instruction stream in case
            the returned constructor fails validation (indicative of a bug in Sigil) or
            behaves unexpectedly (indicative of a logic bug in the consumer code).
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.CreateTypeInitializer(Sigil.OptimizationOptions)">
            <summary>
            Writes the CIL stream out to the ConstructorBuilder used to create this Emit.
            
            Validation that cannot be run until a method is finished is run, and various instructions
            are re-written to choose "optimal" forms (Br may become Br_S, for example).
            
            Once this method is called the Emit may no longer be modified.
            
            Returns a ConstructorBuilder, which can be used to define overrides or for further inspection.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Instructions">
            <summary>
            Returns a string representation of the CIL opcodes written to this Emit to date.
            
            This method is meant for debugging purposes only.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.AsShorthand">
            <summary>
            Returns a proxy for this Emit that exposes method names that more closely
            match the fields on System.Reflection.Emit.OpCodes.
            
            IF you're well versed in ILGenerator, the shorthand version may be easier to use.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Duplicate">
            <summary>
            Pushes a copy of the current top value on the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.InitializeBlock(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Expects a pointer, an initialization value, and a count on the stack.  Pops all three.
            
            Writes the initialization value to count bytes at the passed pointer.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.InitializeObject``1">
            <summary>
            Expects an instance of the type to be initialized on the stack.
            
            Initializes all the fields on a value type to null or an appropriate zero value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.InitializeObject(System.Type)">
            <summary>
            Expects an instance of the type to be initialized on the stack.
            
            Initializes all the fields on a value type to null or an appropriate zero value.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.IsInstance``1">
            <summary>
            Pops a value from the stack and casts to the given type if possible pushing the result, otherwise pushes a null.
            
            This is analogous to C#'s `as` operator.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.IsInstance(System.Type)">
            <summary>
            Pops a value from the stack and casts to the given type if possible pushing the result, otherwise pushes a null.
            
            This is analogous to C#'s `as` operator.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Jump(System.Reflection.MethodInfo)">
            <summary>
            Transfers control to another method.
            
            The parameters and calling convention of method must match the current one's.
            
            The stack must be empty to jump.
            
            Like the branching instructions, Jump cannot leave exception blocks.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.DefineLabel(System.String)">
            <summary>
            Defines a new label.
            
            This label can be used for branching, leave, and switch instructions.
            
            A label must be marked exactly once after being defined, using the MarkLabel() method.        
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.DefineLabel(Sigil.Label@,System.String)">
            <summary>
            Defines a new label.
            
            This label can be used for branching, leave, and switch instructions.
            
            A label must be marked exactly once after being defined, using the MarkLabel() method.        
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.MarkLabel(Sigil.Label)">
            <summary>
            Marks a label in the instruction stream.
            
            When branching, leaving, or switching with a label control will be transfered to where it was *marked* not defined.
            
            Labels can only be marked once, and *must* be marked before creating a delegate.
            
            Logically after a Branch or Leave instruction, a stack assertion is required to continue emiting.  The stack
            is assumed to match that state in these cases.
            
            In all other cases, a stack assertion is merely checked (and if failing, a verification exception is thrown).
            
            In the assertion, the top of the stack is the first (0-indexed, left-most) parameter.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.MarkLabel(System.String)">
            <summary>
            Marks a label with the given name in the instruction stream.
            
            When branching, leaving, or switching with a label control will be transfered to where it was *marked* not defined.
            
            Labels can only be marked once, and *must* be marked before creating a delegate.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Leave(Sigil.Label)">
            <summary>
            Leave an exception or catch block, branching to the given label.
            
            This instruction empties the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Leave(System.String)">
            <summary>
            Leave an exception or catch block, branching to the label with the given name.
            
            This instruction empties the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadArgument(System.UInt16)">
            <summary>
            Loads the argument at the given index (starting at 0) for the current method onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadArgumentAddress(System.UInt16)">
            <summary>
            Loads a pointer to the argument at index (starting at zero) onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Boolean)">
            <summary>
            Push a 1 onto the stack if b is true, and 0 if false.
            
            Pushed values are int32s.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Int32)">
            <summary>
            Push a constant int32 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.UInt32)">
            <summary>
            Push a constant int32 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Int64)">
            <summary>
            Push a constant int64 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.UInt64)">
            <summary>
            Push a constant int64 onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Single)">
            <summary>
            Push a constant float onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Double)">
            <summary>
            Push a constant double onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.String)">
            <summary>
            Push a constant string onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Reflection.FieldInfo)">
            <summary>
            Push a constant RuntimeFieldHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Reflection.MethodInfo)">
            <summary>
            Push a constant RuntimeMethodHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant``1">
            <summary>
            Push a constant RuntimeTypeHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadConstant(System.Type)">
            <summary>
            Push a constant RuntimeTypeHandle onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadNull">
            <summary>
            Loads a null reference onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadElement``1">
            <summary>
            Expects a reference to an array and an index on the stack.
            
            Pops both, and pushes the element in the array at the index onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadElement(System.Type)">
            <summary>
            Expects a reference to an array and an index on the stack.
            
            Pops both, and pushes the element in the array at the index onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadElementAddress``1">
            <summary>
            Expects a reference to an array of the given element type and an index on the stack.
            
            Pops both, and pushes the address of the element at the given index.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadElementAddress(System.Type)">
            <summary>
            Expects a reference to an array of the given element type and an index on the stack.
            
            Pops both, and pushes the address of the element at the given index.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadField(System.Reflection.FieldInfo,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary>
            Loads a field onto the stack.
            
            Instance fields expect a reference on the stack, which is popped.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadFieldAddress(System.Reflection.FieldInfo)">
            <summary>
            Loads the address of the given field onto the stack.
            
            If the field is an instance field, a `this` reference is expected on the stack and will be popped.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadFunctionPointer(System.Reflection.MethodInfo)">
            <summary>
            Pushes a pointer to the given function onto the stack, as a native int.
            
            To resolve a method at runtime using an object, use LoadVirtualFunctionPointer instead.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadFunctionPointer(System.Reflection.Emit.MethodBuilder,System.Type[])">
            <summary>
            Pushes a pointer to the given function onto the stack, as a native int.
            
            To resolve a method at runtime using an object, use LoadVirtualFunctionPointer instead.
            
            This method is provided as MethodBuilder cannot be inspected for parameter information at runtime.  If the passed parameterTypes
            do not match the given method, the produced code will be invalid.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadIndirect``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack and pushes the value (of the given type) at that address onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadIndirect(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack and pushes the value (of the given type) at that address onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadLength``1">
            <summary>
            Pops a reference to a rank 1 array off the stack, and pushes it's length onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadLength(System.Type)">
            <summary>
            Pops a reference to a rank 1 array off the stack, and pushes it's length onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadLocal(Sigil.Local)">
            <summary>
            Loads the value in the given local onto the stack.
            
            To create a local, use DeclareLocal().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadLocal(System.String)">
            <summary>
            Loads the value in the local with the given name onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadLocalAddress(Sigil.Local)">
            <summary>
            Pushes a pointer to the given local onto the stack.
            
            To create a local, use DeclareLocal.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadLocalAddress(System.String)">
            <summary>
            Pushes a pointer to the local with the given name onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadObject``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack, and pushes the given value type it points to onto the stack.
            
            For primitive and reference types, use LoadIndirect().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadObject(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a pointer from the stack, and pushes the given value type it points to onto the stack.
            
            For primitive and reference types, use LoadIndirect().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadVirtualFunctionPointer(System.Reflection.MethodInfo)">
            <summary>
            Pops an object reference off the stack, and pushes a pointer to the given method's implementation on that object.
            
            For static or non-virtual functions, use LoadFunctionPointer
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LoadVirtualFunctionPointer(System.Reflection.Emit.MethodBuilder,System.Type[])">
            <summary>
            Pops an object reference off the stack, and pushes a pointer to the given method's implementation on that object.
            
            For static or non-virtual functions, use LoadFunctionPointer.
            
            This method is provided as MethodBuilder cannot be inspected for parameter information at runtime.  If the passed parameterTypes
            do not match the given method, the produced code will be invalid.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.LocalAllocate">
            <summary>
            Pops a size from the stack, allocates size bytes on the local dynamic memory pool, and pushes a pointer to the allocated block.
            
            LocalAllocate can only be called if the stack is empty aside from the size value.
            
            Memory allocated with LocalAllocate is released when the current method ends execution.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.DeclareLocal``1(System.String)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.DeclareLocal(System.Type,System.String)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.DeclareLocal``1(Sigil.Local@,System.String)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.DeclareLocal(System.Type,Sigil.Local@,System.String)">
            <summary>
            Declare a new local of the given type in the current method.
            
            Name is optional, and only provided for debugging purposes.  It has no
            effect on emitted IL.
            
            Be aware that each local takes some space on the stack, inefficient use of locals
            could lead to StackOverflowExceptions at runtime.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.MakeReferenceAny``1">
            <summary>
            Converts a pointer or reference to a value on the stack into a TypedReference of the given type.
            
            TypedReferences can be used with ReferenceAnyType and ReferenceAnyValue to pass arbitrary types as parameters.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.MakeReferenceAny(System.Type)">
            <summary>
            Converts a pointer or reference to a value on the stack into a TypedReference of the given type.
            
            TypedReferences can be used with ReferenceAnyType and ReferenceAnyValue to pass arbitrary types as parameters.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewArray``1">
            <summary>
            Pops a size from the stack, allocates a rank-1 array of the given type, and pushes a reference to the new array onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewArray(System.Type)">
            <summary>
            Pops a size from the stack, allocates a rank-1 array of the given type, and pushes a reference to the new array onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``1">
            <summary>
            Invokes the parameterless constructor of the given type, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``2">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``3">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``4">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``5">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``6">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``7">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``8">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``9">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``10">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``11">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``12">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``13">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``14">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``15">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``16">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject``17">
            <summary>
            Pops # of parameter arguments from the stack, invokes the the constructor of the given reference type that matches the given parameter types, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject(System.Type,System.Type[])">
            <summary>
            Pops parameterTypes.Length arguments from the stack, invokes the constructor on the given type that matches parameterTypes, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject(System.Reflection.ConstructorInfo)">
            <summary>
            Pops # of parameters to the given constructor arguments from the stack, invokes the constructor, and pushes a reference to the new object onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.NewObject(System.Reflection.Emit.ConstructorBuilder,System.Type[])">
            <summary>
            Pops # of parameters from the stack, invokes the constructor, and pushes a reference to the new object onto the stack.
            
            This method is provided as ConstructorBuilder cannot be inspected for parameter information at runtime.  If the passed parameterTypes
            do not match the given constructor, the produced code will be invalid.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Nop">
            <summary>
            Emits an instruction that does nothing.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Pop">
            <summary>
            Removes the top value on the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ReferenceAnyType">
            <summary>
            Converts a TypedReference on the stack into a RuntimeTypeHandle for the type contained with it.
            
            __makeref(int) on the stack would become the RuntimeTypeHandle for typeof(int), for example.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ReferenceAnyValue``1">
            <summary>
            Converts a TypedReference on the stack into a reference to the contained object, given the type contained in the TypedReference.
            
            __makeref(int) on the stack would become an int&amp;, for example.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ReferenceAnyValue(System.Type)">
            <summary>
            Converts a TypedReference on the stack into a reference to the contained object, given the type contained in the TypedReference.
            
            __makeref(int) on the stack would become an int&amp;, for example.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.ReThrow">
            <summary>
            From within a catch block, rethrows the exception that caused the catch block to be entered.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Return">
            <summary>
            Ends the execution of the current method.
            
            If the current method does not return void, pops a value from the stack and returns it to the calling method.
            
            Return should leave the stack empty.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.SizeOf``1">
            <summary>
            Pushes the size of the given value type onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.SizeOf(System.Type)">
            <summary>
            Pushes the size of the given value type onto the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreArgument(System.UInt16)">
            <summary>
            Pops a value off the stack and stores it into the argument to the current method identified by index.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreElement``1">
            <summary>
            Pops a value, an index, and a reference to an array off the stack.  Places the given value into the given array at the given index.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreElement(System.Type)">
            <summary>
            Pops a value, an index, and a reference to an array off the stack.  Places the given value into the given array at the given index.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreField(System.Reflection.FieldInfo,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value from the stack and stores it in the given field.
            
            If the field is an instance member, both a value and a reference to the instance are popped from the stack.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreIndirect``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value of the given type and a pointer off the stack, and stores the value at the address in the pointer.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreIndirect(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value of the given type and a pointer off the stack, and stores the value at the address in the pointer.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreLocal(Sigil.Local)">
            <summary>
            Pops a value off the stack and stores it into the given local.
            
            To create a local, use DeclareLocal().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreLocal(System.String)">
            <summary>
            Pops a value off the stack and stores it in the local with the given name.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreObject``1(System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value type and a pointer off of the stack and copies the given value to the given address.
            
            For primitive and reference types use StoreIndirect.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.StoreObject(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Pops a value type and a pointer off of the stack and copies the given value to the given address.
            
            For primitive and reference types use StoreIndirect.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Switch(Sigil.Label[])">
            <summary>
            Pops a value off the stack and branches to the label at the index of that value in the given labels.
            
            If the value is out of range, execution falls through to the next instruction.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Switch(System.String[])">
            <summary>
            Pops a value off the stack and branches to the label at the index of that value in the given label names.
            
            If the value is out of range, execution falls through to the next instruction.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Throw">
            <summary>
            Pops a value off the stack and throws it as an exception.
            
            Throw expects the value to be or extend from a System.Exception.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginExceptionBlock">
            <summary>
            Start a new exception block.  This is roughly analogous to a `try` block in C#, but an exception block contains it's catch and finally blocks.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginExceptionBlock(Sigil.ExceptionBlock@)">
            <summary>
            Start a new exception block.  This is roughly analogous to a `try` block in C#, but an exception block contains it's catch and finally blocks.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.EndExceptionBlock(Sigil.ExceptionBlock)">
            <summary>
            Ends the given exception block.
            
            All catch and finally blocks associated with the given exception block must be ended before this method is called.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginCatchBlock``1(Sigil.ExceptionBlock)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginCatchBlock``1(Sigil.ExceptionBlock,Sigil.CatchBlock@)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginCatchAllBlock(Sigil.ExceptionBlock)">
             <summary>
             Begins a catch block for all exceptions in the given exception block
            
             The given exception block must still be open.
             
             Equivalent to BeginCatchBlock(typeof(Exception), forTry).
             </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginCatchAllBlock(Sigil.ExceptionBlock,Sigil.CatchBlock@)">
             <summary>
             Begins a catch block for all exceptions in the given exception block
            
             The given exception block must still be open.
             
             Equivalent to BeginCatchBlock(typeof(Exception), forTry).
             </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginCatchBlock(Sigil.ExceptionBlock,System.Type)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginCatchBlock(Sigil.ExceptionBlock,System.Type,Sigil.CatchBlock@)">
            <summary>
            Begins a catch block for the given exception type in the given exception block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.EndCatchBlock(Sigil.CatchBlock)">
            <summary>
            Ends the given catch block.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginFinallyBlock(Sigil.ExceptionBlock,Sigil.FinallyBlock@)">
            <summary>
            Begins a finally block on the given exception block.
            
            Only one finally block can be defined per exception block, and the block cannot appear within a catch block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.BeginFinallyBlock(Sigil.ExceptionBlock)">
            <summary>
            Begins a finally block on the given exception block.
            
            Only one finally block can be defined per exception block, and the block cannot appear within a catch block.
            
            The given exception block must still be open.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.EndFinallyBlock(Sigil.FinallyBlock)">
            <summary>
            Ends the given finally block.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Unbox``1">
            <summary>
            Pops a boxed value from the stack and pushes a pointer to it's unboxed value.
            
            To load the value directly onto the stack, use UnboxAny().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.Unbox(System.Type)">
            <summary>
            Pops a boxed value from the stack and pushes a pointer to it's unboxed value.
            
            To load the value directly onto the stack, use UnboxAny().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnboxAny``1">
            <summary>
            Pops a boxed value from the stack, unboxes it and pushes the value onto the stack.
            
            To get an address for the unboxed value instead, use Unbox().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.UnboxAny(System.Type)">
            <summary>
            Pops a boxed value from the stack, unboxes it and pushes the value onto the stack.
            
            To get an address for the unboxed value instead, use Unbox().
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.Emit.WriteLine(System.String,Sigil.Local[])">
            <summary>
            Emits IL that calls Console.WriteLine(string) for the given string if no locals are passed.
            
            If any locals are passed, line is treated as a format string and local values are used in a call
            to Console.WriteLine(string, object[]).
            </summary>
        </member>
        <member name="T:Sigil.NonGeneric.EmitShorthand">
            <summary>
            A version of Emit with shorter named versions of it's methods.
            
            Method names map more or less to OpCodes fields.
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.EmitShorthand.AllowsUnverifiableCIL">
            <summary>
            Returns true if this Emit can make use of unverifiable instructions.
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.EmitShorthand.MaxStackSize">
            <summary>
            Returns the maxmimum number of items on the stack for the IL stream created with the current emit.
            
            This is not the maximum that *can be placed*, but the maximum that actually are.
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.EmitShorthand.Locals">
            <summary>
            Lookup for the locals currently in scope by name.
            
            Locals go out of scope when released (by calling Dispose() directly, or via using) and go into scope
            immediately after a DeclareLocal()
            </summary>
        </member>
        <member name="P:Sigil.NonGeneric.EmitShorthand.Labels">
            <summary>
            Lookup for declared labels by name.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.AsLonghand">
            <summary>
            Returns the original Emit instance that AsShorthand() was called on.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Instructions">
            <summary>
            Returns a string representation of the CIL opcodes written to this Emit to date.
            
            This method is meant for debugging purposes only.
            </summary>
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.DeclareLocal``1(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.DeclareLocal``1(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.DeclareLocal``1(Sigil.Local@,System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.DeclareLocal``1(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.DeclareLocal(System.Type,System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.DeclareLocal(System.Type, System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.DeclareLocal(System.Type,Sigil.Local@,System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.DeclareLocal(System.Type, System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.DefineLabel(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.DefineLabel(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.DefineLabel(Sigil.Label@,System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.DefineLabel(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.MarkLabel(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.MarkLabel(Sigil.Label, IEnumerable``1)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.MarkLabel(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.MarkLabel(System.String, IEnumerable``1)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginExceptionBlock">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginExceptionBlock" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginExceptionBlock(Sigil.ExceptionBlock@)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginExceptionBlock" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginCatchBlock``1(Sigil.ExceptionBlock)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginCatchBlock``1(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginCatchBlock``1(Sigil.ExceptionBlock,Sigil.CatchBlock@)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginCatchBlock``1(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginCatchBlock(Sigil.ExceptionBlock,System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginCatchBlock(System.Type, Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginCatchBlock(Sigil.ExceptionBlock,System.Type,Sigil.CatchBlock@)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginCatchBlock(System.Type, Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.EndCatchBlock(Sigil.CatchBlock)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.EndCatchBlock(Sigil.CatchBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginFinallyBlock(Sigil.ExceptionBlock)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginFinallyBlock(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.BeginFinallyBlock(Sigil.ExceptionBlock,Sigil.FinallyBlock@)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BeginFinallyBlock(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.EndFinallyBlock(Sigil.FinallyBlock)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.EndFinallyBlock(Sigil.FinallyBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.EndExceptionBlock(Sigil.ExceptionBlock)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.EndExceptionBlock(Sigil.ExceptionBlock)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.CreateDelegate(System.Type,Sigil.OptimizationOptions)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CreateDelegate(System.Type, Sigil.OptimizationOptions)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.CreateDelegate``1(Sigil.OptimizationOptions)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CreateDelegate`2" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.CreateMethod">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CreateMethod" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.CreateConstructor">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CreateConstructor" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Add">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Add" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Add_Ovf">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.AddOverflow" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Add_Ovf_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedAddOverflow" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.And">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.And" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Beq(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Beq(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfEqual(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bge(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfGreaterOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bge(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfGreaterOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bge_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfGreaterOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bge_Un(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfGreaterOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bgt(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfGreater(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bgt(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfGreater(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bgt_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfGreater(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bgt_Un(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfGreater(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ble(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfLessOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ble(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfLessOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ble_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfLessOrEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ble_Un(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfLessOrEqual(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Blt(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfLess(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Blt(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfLess(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Blt_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfLess(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Blt_Un(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfLess(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bne_Un(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfNotEqual(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Bne_Un(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedBranchIfNotEqual(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Box``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Box``1()" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Box(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Box(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Br(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Branch(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Br(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Branch(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Break">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Break" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Brfalse(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfFalse(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Brfalse(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfFalse(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Brtrue(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfTrue(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Brtrue(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.BranchIfTrue(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Call(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Call(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Calli(System.Reflection.CallingConventions,System.Type,System.Type[])">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CallIndirect(System.Reflection.CallingConventions,System.Type,System.Type[])" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Callvirt(System.Reflection.MethodInfo,System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CallVirtual(System.Reflection.MethodInfo, System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Castclass``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CastClass``1" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Castclass(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CastClass(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ceq">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CompareEqual" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Cgt">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CompareGreaterThan" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Cgt_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedCompareGreaterThan" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ckfinite">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CheckFinite" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Clt">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CompareLessThan" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Clt_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedCompareLessThan" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Conv_Ovf_Un``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Conv_Ovf_Un(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Conv_R_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedConvertToFloat" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Conv``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Convert(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Conv(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Convert(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Conv_Ovf``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.ConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Conv_Ovf(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.ConvertOverflow(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Cpblk(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CopyBlock(System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Cpobj``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CopyObject(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Cpobj(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.CopyObject(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Div">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Divide" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Div_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedDivide" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Dup">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Duplicate" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Initblk(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.InitializeBlock(System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Initobj``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.InitializeObject(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Initobj(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.InitializeObject(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Isinst``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.IsInstance(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Isinst(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.IsInstance(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Jmp(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Jump(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldarg(System.UInt16)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadArgument(System.Int32)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldarga(System.UInt16)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadArgumentAddress(System.Int32)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldc(System.Boolean)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Boolean)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldc(System.Single)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Single)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldc(System.Double)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Double)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldc(System.UInt32)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.UInt32)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldc(System.Int32)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Int32)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldc(System.Int64)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Int64)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldc(System.UInt64)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.UInt64)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldelem``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadElement``1" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldelem(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadElement(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldelema``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadElementAddress``1" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldelema(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadElementAddress(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldfld(System.Reflection.FieldInfo,System.Nullable{System.Boolean},System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadField(System.Reflection.FieldInfo, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldflda(System.Reflection.FieldInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadFieldAddress(System.Reflection.FieldInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldftn(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadFunctionPointer(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldind``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldind(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldlen``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadLength``1" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldlen(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadLength(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldloc(Sigil.Local)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadLocal(Sigil.Local)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldloc(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadLocal(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldloca(Sigil.Local)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadLocalAddress(Sigil.Local)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldloca(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadLocalAddress(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldnull">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadNull" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldobj``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadObject(System.Type, System.Boolen, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldobj(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadObject(System.Type, System.Boolen, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldstr(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldtoken(System.Reflection.FieldInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Reflection.FieldInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldtoken(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldtoken``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldtoken(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadConstant(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ldvirtftn(System.Reflection.MethodInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LoadVirtualFunctionPointer(System.Reflection.MethodInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Leave(Sigil.Label)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Leave(Sigil.Label)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Leave(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Leave(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Localloc">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.LocalAllocate" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Mul">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Multiply" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Mul_Ovf">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.MultiplyOverflow" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Mul_Ovf_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedMultiplyOverflow" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Neg">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Negate" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Newarr``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.NewArray(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Newarr(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.NewArray(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Newobj(System.Reflection.ConstructorInfo)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.NewObject(System.Reflection.ConstructorInfo)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Nop">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Nop" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Not">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Not" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Or">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Or" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Pop">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Pop" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Rem">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Remainder" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Rem_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedRemainder" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Ret">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Return" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Rethrow">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.ReThrow" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Shl">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.ShiftLeft" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Shr">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.ShiftRight" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Shr_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedShiftRight" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Sizeof``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.SizeOf(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Sizeof(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.SizeOf(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Starg(System.UInt16)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreArgument(System.Int32)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stelem``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreElement``1" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stelem(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreElement(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stfld(System.Reflection.FieldInfo,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreField(System.Reflection.FieldInfo, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stind``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stind(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreIndirect(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stloc(Sigil.Local)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreLocal(Sigil.Local)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stloc(System.String)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreLocal(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stobj``1(System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreObject(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Stobj(System.Type,System.Boolean,System.Nullable{System.Int32})">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.StoreObject(System.Type, System.Boolean, System.Nullable&lt;int&gt;)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Sub">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Subtract" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Sub_Ovf">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.SubtractOverflow" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Sub_Ovf_Un">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnsignedSubtractOverflow" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Switch(Sigil.Label[])">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Switch(Sigil.Label[])" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Switch(System.String[])">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Switch(System.String[])" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Throw">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Throw" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Unbox``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Unbox(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Unbox(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Unbox(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Unbox_Any``1">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnboxAny(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Unbox_Any(System.Type)">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.UnboxAny(System.Type)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.WriteLine(System.String,Sigil.Local[])">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.WriteLine(System.String)" />
        </member>
        <member name="M:Sigil.NonGeneric.EmitShorthand.Xor">
            <summary cref="M:Sigil.Emit.NonGeneric.Emit`1.Xor" />
        </member>
        <member name="T:Sigil.Operation`1">
            <summary>
            Represents a call to an Emit, used when providing introspection details about the generated IL stream.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.OpCode">
            <summary>
            The OpCode that corresponds to an Emit call.
            Note that the opcode may not correspond to the final short forms and other optimizations.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.Parameters">
            <summary>
            The parameters passsed to a call to Emit.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsExceptionBlockStart">
            <summary>
            This operation marks the beginning of an exception block,
            which is analogous to a call to Emit.BeginExceptionBlock.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsExceptionBlockEnd">
            <summary>
            This operation marks the end of an exception block,
            which is analogous to a call to Emit.EndExceptionBlock.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsCatchBlockStart">
            <summary>
            This operation marks the beginning of a catch block,
            which is analogous to a call to Emit.BeginCatchBlock.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsCatchBlockEnd">
            <summary>
            This operation marks the end of a catch block,
            which is analogous to a call to Emit.EndCatchBlock.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsFinallyBlockStart">
            <summary>
            This operation marks the beginning of a finally block,
            which is analogous to a call to Emit.BeginFinallyBlock.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsFinallyBlockEnd">
            <summary>
            This operation marks the end of a finally block,
            which is analogous to a call to Emit.EndFinallyBlock.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsMarkLabel">
            <summary>
            This operation marks a label, the name of the label is given in LabelName.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.LabelName">
            <summary>
            If this operation marks a label, which is indicated by IsMarkLabel, then this property
            returns the name of the label being marked.
            </summary>
        </member>
        <member name="P:Sigil.Operation`1.IsOpCode">
            <summary>
            Returns true if this operation is emitted a CIL opcode.
            </summary>
        </member>
        <member name="M:Sigil.Operation`1.ToString">
            <summary>
            A string representation of this Operation.
            </summary>
        </member>
        <member name="T:Sigil.OperationResultUsage`1">
            <summary>
            Represents an IL operation, and the subsequent operations that may use it's result.
            </summary>
        </member>
        <member name="P:Sigil.OperationResultUsage`1.ProducesResult">
            <summary>
            The operation that is producing a result.
            </summary>
        </member>
        <member name="P:Sigil.OperationResultUsage`1.ResultUsedBy">
            <summary>
            The operations that may use the result produced by the ProducesResult operation.
            </summary>
        </member>
        <member name="M:Sigil.OperationResultUsage`1.ToString">
            <summary>
            Returns a string representation of this OperationResultUsage.
            </summary>
        </member>
        <member name="T:Sigil.OptimizationOptions">
            <summary>
            Sigil can perform optimizations to the emitted IL. This enum tells Sigil which optimizations to perform.
            </summary>
        </member>
        <member name="F:Sigil.OptimizationOptions.None">
            <summary>
            Perform no optional optimizations.
            </summary>
        </member>
        <member name="F:Sigil.OptimizationOptions.EnableBranchPatching">
            <summary>
            Choose optimal branch instructions.
            </summary>
        </member>
        <member name="F:Sigil.OptimizationOptions.EnableTrivialCastEliding">
            <summary>
            Elide CastClass and IsInstance instructions which are no-ops, such as casting a System.String to a System.Object.
            </summary>
        </member>
        <member name="F:Sigil.OptimizationOptions.All">
            <summary>
            Perform all optimizations.
            </summary>
        </member>
        <member name="T:Sigil.Parameter">
            <summary>
            Represents a parameter to a decompiled delegate.
            </summary>
        </member>
        <member name="P:Sigil.Parameter.Position">
            <summary>
            The index of the parameter.
            </summary>
        </member>
        <member name="P:Sigil.Parameter.ParameterType">
            <summary>
            The type of the parameter.
            </summary>
        </member>
        <member name="M:Sigil.Parameter.ToString">
            <summary>
            Returns a string representation of this Parameter.
            </summary>
        </member>
        <member name="T:Sigil.SigilVerificationException">
            <summary>
            A SigilVerificationException is thrown whenever a CIL stream becomes invalid.
            
            There are many possible causes of this including: operator type mismatches, underflowing the stack, and branching from one stack state to another.
            
            Invalid arguments, non-sensical parameters, and other non-correctness related errors will throw more specific exceptions.
            
            SigilVerificationException will typically include the state of the stack (or stacks) at the instruction in error.
            </summary>
        </member>
        <member name="M:Sigil.SigilVerificationException.GetDebugInfo">
            <summary>
            Returns a string representation of any stacks attached to this exception.
            
            This is meant for debugging purposes, and should not be called during normal operation.
            </summary>
        </member>
        <member name="M:Sigil.SigilVerificationException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Implementation for ISerializable.
            </summary>
        </member>
        <member name="M:Sigil.SigilVerificationException.ToString">
            <summary>
            Returns the message and stacks on this exception, in string form.
            </summary>
        </member>
    </members>
</doc>
