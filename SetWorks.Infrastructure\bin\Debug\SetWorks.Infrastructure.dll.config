﻿<?xml version="1.0" encoding="utf-8"?>

<configuration>
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="AWSSDK" publicKeyToken="9f476d3089b52be3" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-2.3.55.2" newVersion="2.3.55.2" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Telerik.Reporting" publicKeyToken="a9d7983dfcc261be" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-12.1.18.824" newVersion="12.1.18.824" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="DocumentFormat.OpenXml" publicKeyToken="31bf3856ad364e35" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-2.5.5631.0" newVersion="2.5.5631.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
    <configSections>
        
        <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>
    <entityFramework>
        <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
            <parameters>
                <parameter value="mssqllocaldb" />
            </parameters>
        </defaultConnectionFactory>
        <providers>
            <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
        </providers>
        <contexts>
            <context type="SetWorks.Infrastructure.SetWorksDataContext, SetWorks.Infrastructure" disableDatabaseInitialization="true" />
        </contexts>
    </entityFramework>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.1" />
    </startup>
</configuration>