using System;
using System.Configuration;
using System.Web;
using StackExchange.Redis;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Monitoring utility for Redis report storage to track memory usage and performance
    /// </summary>
    public static class ReportStorageMonitor
    {
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(ReportStorageMonitor));

        /// <summary>
        /// Get current Redis memory usage for report storage
        /// </summary>
        public static RedisMemoryInfo GetReportRedisMemoryInfo()
        {
            try
            {
                if (!ReportStorageConfiguration.UseRedisStorage())
                {
                    return new RedisMemoryInfo { UsedMemoryHuman = "N/A - Not using Redis storage" };
                }

                using (var storage = new RedisReportStorage())
                {
                    return storage.GetMemoryInfo();
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error getting Redis memory info", ex);
                return new RedisMemoryInfo { UsedMemoryHuman = "Error retrieving info" };
            }
        }

        /// <summary>
        /// Get session Redis memory usage for comparison
        /// </summary>
        public static RedisMemoryInfo GetSessionRedisMemoryInfo()
        {
            try
            {
                var connectionString = ConfigurationManager.AppSettings["SWRedisConnection"];
                if (string.IsNullOrEmpty(connectionString))
                {
                    return new RedisMemoryInfo { UsedMemoryHuman = "N/A - No Redis connection" };
                }

                using (var redis = ConnectionMultiplexer.Connect(connectionString))
                {
                    var server = redis.GetServer(redis.GetEndPoints()[0]);
                    var info = server.Info("memory");
                    
                    var memoryInfo = new RedisMemoryInfo();
                    foreach (var section in info)
                    {
                        foreach (var item in section)
                        {
                            switch (item.Key.ToLower())
                            {
                                case "used_memory":
                                    long.TryParse(item.Value, out memoryInfo.UsedMemory);
                                    break;
                                case "used_memory_human":
                                    memoryInfo.UsedMemoryHuman = item.Value;
                                    break;
                                case "maxmemory":
                                    long.TryParse(item.Value, out memoryInfo.MaxMemory);
                                    break;
                            }
                        }
                    }
                    return memoryInfo;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error getting session Redis memory info", ex);
                return new RedisMemoryInfo { UsedMemoryHuman = "Error retrieving info" };
            }
        }

        /// <summary>
        /// Log memory usage statistics - call this periodically for monitoring
        /// </summary>
        public static void LogMemoryUsage()
        {
            try
            {
                var reportMemory = GetReportRedisMemoryInfo();
                var sessionMemory = GetSessionRedisMemoryInfo();

                Log.Info($"Redis Memory Usage - Session: {sessionMemory.UsedMemoryHuman}, Reports: {reportMemory.UsedMemoryHuman}");
                
                // Alert if memory usage is high
                if (sessionMemory.MemoryUsagePercentage > 80)
                {
                    Log.Warn($"Session Redis memory usage is high: {sessionMemory.MemoryUsagePercentage:F1}%");
                }
                
                if (reportMemory.MemoryUsagePercentage > 80)
                {
                    Log.Warn($"Report Redis memory usage is high: {reportMemory.MemoryUsagePercentage:F1}%");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error logging memory usage", ex);
            }
        }

        /// <summary>
        /// Clear expired report data manually - useful for troubleshooting
        /// </summary>
        public static int ClearExpiredReportData()
        {
            try
            {
                if (!ReportStorageConfiguration.UseRedisStorage())
                {
                    Log.Info("Not using Redis storage - no cleanup needed");
                    return 0;
                }

                var connectionString = GetReportRedisConnectionString();
                using (var redis = ConnectionMultiplexer.Connect(connectionString))
                {
                    var server = redis.GetServer(redis.GetEndPoints()[0]);
                    var database = redis.GetDatabase();
                    
                    var keys = server.Keys(pattern: "TelerikReport:*");
                    int expiredCount = 0;
                    
                    foreach (var key in keys)
                    {
                        var ttl = database.KeyTimeToLive(key);
                        if (!ttl.HasValue || ttl.Value.TotalSeconds <= 0)
                        {
                            database.KeyDelete(key);
                            expiredCount++;
                        }
                    }
                    
                    Log.Info($"Cleared {expiredCount} expired report storage entries");
                    return expiredCount;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error clearing expired report data", ex);
                return -1;
            }
        }

        /// <summary>
        /// Get count of report storage keys
        /// </summary>
        public static int GetReportStorageKeyCount()
        {
            try
            {
                if (!ReportStorageConfiguration.UseRedisStorage())
                {
                    return 0;
                }

                var connectionString = GetReportRedisConnectionString();
                using (var redis = ConnectionMultiplexer.Connect(connectionString))
                {
                    var server = redis.GetServer(redis.GetEndPoints()[0]);
                    var keys = server.Keys(pattern: "TelerikReport:*");
                    
                    int count = 0;
                    foreach (var key in keys)
                    {
                        count++;
                    }
                    
                    return count;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error getting report storage key count", ex);
                return -1;
            }
        }

        private static string GetReportRedisConnectionString()
        {
            // Try to get dedicated report Redis connection string first
            var reportRedisConnection = ConfigurationManager.AppSettings["SWReportRedisConnection"];
            if (!string.IsNullOrEmpty(reportRedisConnection))
            {
                return reportRedisConnection;
            }

            // Fall back to main Redis connection but with different database
            var mainRedisConnection = ConfigurationManager.AppSettings["SWRedisConnection"];
            if (!string.IsNullOrEmpty(mainRedisConnection))
            {
                // Use database 1 for reports (default session uses database 0)
                return mainRedisConnection + ",defaultDatabase=1";
            }

            throw new ConfigurationErrorsException("No Redis connection string found for report storage");
        }

        /// <summary>
        /// Initialize periodic monitoring - call this in Application_Start
        /// </summary>
        public static void InitializeMonitoring()
        {
            try
            {
                // Log memory usage every 15 minutes
                var monitoringInterval = TimeSpan.FromMinutes(15);
                
                var timer = new System.Threading.Timer(
                    callback: _ => {
                        try
                        {
                            LogMemoryUsage();
                        }
                        catch (Exception ex)
                        {
                            Log.Error("Error during scheduled monitoring", ex);
                        }
                    },
                    state: null,
                    dueTime: monitoringInterval,
                    period: monitoringInterval
                );

                // Store timer reference to prevent garbage collection
                HttpContext.Current.Application["ReportStorageMonitoringTimer"] = timer;
                
                Log.Info("Report storage monitoring initialized");
            }
            catch (Exception ex)
            {
                Log.Error("Failed to initialize report storage monitoring", ex);
            }
        }
    }
}
