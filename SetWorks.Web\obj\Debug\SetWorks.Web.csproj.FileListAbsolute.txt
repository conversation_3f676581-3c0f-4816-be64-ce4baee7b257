C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\ApplicationInsights.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\csc.exe
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\csc.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\csc.rsp
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\csi.exe
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\csi.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\csi.rsp
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.CSharp.Core.targets
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.VisualBasic.Core.targets
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.AppContext.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Console.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.IO.Compression.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.IO.Pipes.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Reflection.Metadata.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Security.AccessControl.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Security.Claims.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Security.Principal.Windows.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Threading.Thread.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Xml.XPath.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\vbc.exe
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\vbc.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\vbc.rsp
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\VBCSCompiler.exe
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\roslyn\VBCSCompiler.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Web.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Web.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Web.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\netstandard.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.AppContext.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Collections.Concurrent.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Collections.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Collections.NonGeneric.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Collections.Specialized.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ComponentModel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Console.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Data.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.Debug.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.Tools.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Drawing.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Dynamic.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Globalization.Calendars.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Globalization.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Globalization.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.Compression.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.Pipes.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Linq.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Linq.Expressions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Linq.Parallel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Linq.Queryable.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Http.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.NameResolution.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.NetworkInformation.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Ping.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Requests.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Security.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Sockets.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.WebSockets.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ObjectModel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Reflection.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Reflection.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Reflection.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Resources.Reader.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Resources.ResourceManager.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Resources.Writer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.Handles.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.InteropServices.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.Numerics.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.Claims.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.Principal.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Security.SecureString.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Text.Encoding.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Text.RegularExpressions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.Overlapped.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.Tasks.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.Thread.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.ThreadPool.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.Timer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Xml.XDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Xml.XPath.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Antlr3.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AutoMapper.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Datadog.Trace.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\EntityFramework.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\EntityFramework.SqlServer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\LazyCache.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\log4net.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.Agent.Intercept.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.DependencyCollector.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.PerfCounterCollector.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.ServerTelemetryChannel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.Web.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.WindowsServer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.ApplicationInsights.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNet.Identity.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNet.Identity.EntityFramework.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNet.Identity.Owin.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Cors.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Http.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Net.Http.Headers.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Host.SystemWeb.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Cookies.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Facebook.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Google.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.MicrosoftAccount.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.OAuth.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Twitter.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Web.Infrastructure.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\MsgPack.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Ninject.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Ninject.Web.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Ninject.Web.Mvc.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Owin.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Infrastructure.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Services.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SETWorksDAO.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Buffers.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Http.Formatting.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Text.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Cors.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Helpers.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Http.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Http.WebHost.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Mvc.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Optimization.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Razor.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.WebPages.Deployment.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.WebPages.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.WebPages.Razor.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\WebActivatorEx.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\WebGrease.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Azure.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Azure.AI.OpenAI.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\QuikGraph.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Flee.Net45.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Reporting.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Markdig.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\UAParser.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Twilio.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\RestSharp.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.XLS.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Renci.SshNet.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.Pdf.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.S3.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.SimpleEmail.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Telerik.ReportViewer.WebForms.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Telerik.Windows.Zip.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ClientModel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Memory.Data.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.Doc.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.License.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.mshtml.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Common.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Common.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Core.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Infrastructure.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Infrastructure.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Services.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Services.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SETWorksDAO.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SETWorksDAO.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Antlr3.Runtime.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AutoMapper.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Datadog.Trace.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\EntityFramework.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\EntityFramework.SqlServer.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\log4net.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.DependencyCollector.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.ServerTelemetryChannel.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.Web.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AI.WindowsServer.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.ApplicationInsights.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNet.Identity.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNet.Identity.EntityFramework.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNet.Identity.Owin.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Cors.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Http.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Http.Extensions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.AspNetCore.Http.Features.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Bcl.AsyncInterfaces.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Caching.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Caching.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Configuration.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.DependencyInjection.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.FileProviders.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Logging.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Options.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Extensions.Primitives.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.JsonWebTokens.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Logging.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Protocols.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Protocols.OpenIdConnect.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.IdentityModel.Tokens.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Net.Http.Headers.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Host.SystemWeb.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Cookies.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Facebook.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Google.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.MicrosoftAccount.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.OAuth.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Microsoft.Owin.Security.Twitter.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\MsgPack.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Ninject.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Ninject.Web.Common.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Ninject.Web.Mvc.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Buffers.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Diagnostics.DiagnosticSource.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.IdentityModel.Tokens.Jwt.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Net.Http.Formatting.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Numerics.Vectors.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Text.Encodings.Web.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Text.Json.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Threading.Tasks.Extensions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ValueTuple.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Helpers.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Http.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Http.WebHost.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Mvc.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Optimization.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.Razor.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.WebPages.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.WebPages.Deployment.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Web.WebPages.Razor.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Azure.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Azure.AI.OpenAI.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\QuikGraph.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\SetWorks.Reporting.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Markdig.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\UAParser.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Twilio.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\RestSharp.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.XLS.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Renci.SshNet.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.Pdf.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.S3.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.S3.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.SimpleEmail.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\AWSSDK.SimpleEmail.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Telerik.ReportViewer.WebForms.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Telerik.ReportViewer.WebForms.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Telerik.Windows.Zip.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.ClientModel.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\System.Memory.Data.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.Doc.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\bin\Spire.License.xml
C:\Users\<USER>\Documents\SW\SetWorks.Web\obj\Debug\SetWorks.Web.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\SW\SetWorks.Web\obj\Debug\SetWorks.Web.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\SW\SetWorks.Web\obj\Debug\SetWorks.Web.csproj.CopyComplete
C:\Users\<USER>\Documents\SW\SetWorks.Web\obj\Debug\SetWorks.Web.dll
C:\Users\<USER>\Documents\SW\SetWorks.Web\obj\Debug\SetWorks.Web.pdb
