using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Web;
using Telerik.Reporting.Cache;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Custom storage provider for Telerik Reporting that uses database storage instead of session
    /// This prevents large report data from overwhelming the Redis session store
    /// </summary>
    public class CustomReportStorage : IStorage
    {
        private readonly string _connectionString;
        private readonly string _tableName = "TelerikReportStorage";
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromHours(2);

        public CustomReportStorage()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["MainConnectionString"].ConnectionString;
            EnsureTableExists();
        }

        public CustomReportStorage(string connectionString) : this()
        {
            _connectionString = connectionString;
            EnsureTableExists();
        }

        public bool Contains(string key)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var command = new SqlCommand(
                        $"SELECT COUNT(1) FROM {_tableName} WHERE StorageKey = @key AND ExpirationDate > @now", 
                        connection);
                    command.Parameters.AddWithValue("@key", key);
                    command.Parameters.AddWithValue("@now", DateTime.UtcNow);
                    
                    var count = (int)command.ExecuteScalar();
                    return count > 0;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error checking if key exists: {key}", ex);
                return false;
            }
        }

        public object GetValue(string key)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var command = new SqlCommand(
                        $"SELECT StorageValue FROM {_tableName} WHERE StorageKey = @key AND ExpirationDate > @now", 
                        connection);
                    command.Parameters.AddWithValue("@key", key);
                    command.Parameters.AddWithValue("@now", DateTime.UtcNow);
                    
                    var result = command.ExecuteScalar();
                    if (result != null && result != DBNull.Value)
                    {
                        var bytes = (byte[])result;
                        return DeserializeObject(bytes);
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error retrieving value for key: {key}", ex);
            }
            return null;
        }

        public void SetValue(string key, object value)
        {
            SetValue(key, value, _defaultExpiration);
        }

        public void SetValue(string key, object value, TimeSpan timeout)
        {
            try
            {
                var serializedValue = SerializeObject(value);
                var expirationDate = DateTime.UtcNow.Add(timeout);

                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    
                    // Use MERGE to handle both insert and update
                    var command = new SqlCommand($@"
                        MERGE {_tableName} AS target
                        USING (SELECT @key AS StorageKey) AS source
                        ON target.StorageKey = source.StorageKey
                        WHEN MATCHED THEN
                            UPDATE SET StorageValue = @value, ExpirationDate = @expiration, LastAccessed = @now
                        WHEN NOT MATCHED THEN
                            INSERT (StorageKey, StorageValue, ExpirationDate, LastAccessed)
                            VALUES (@key, @value, @expiration, @now);", connection);
                    
                    command.Parameters.AddWithValue("@key", key);
                    command.Parameters.AddWithValue("@value", serializedValue);
                    command.Parameters.AddWithValue("@expiration", expirationDate);
                    command.Parameters.AddWithValue("@now", DateTime.UtcNow);
                    
                    command.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                LogError($"Error setting value for key: {key}", ex);
                throw;
            }
        }

        public bool Remove(string key)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var command = new SqlCommand($"DELETE FROM {_tableName} WHERE StorageKey = @key", connection);
                    command.Parameters.AddWithValue("@key", key);
                    
                    var rowsAffected = command.ExecuteNonQuery();
                    return rowsAffected > 0;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error removing key: {key}", ex);
                return false;
            }
        }

        public void Clear()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var command = new SqlCommand($"DELETE FROM {_tableName}", connection);
                    command.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                LogError("Error clearing storage", ex);
            }
        }

        /// <summary>
        /// Clean up expired entries - should be called periodically
        /// </summary>
        public void CleanupExpiredEntries()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var command = new SqlCommand($"DELETE FROM {_tableName} WHERE ExpirationDate <= @now", connection);
                    command.Parameters.AddWithValue("@now", DateTime.UtcNow);
                    
                    var deletedRows = command.ExecuteNonQuery();
                    if (deletedRows > 0)
                    {
                        LogInfo($"Cleaned up {deletedRows} expired report storage entries");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("Error during cleanup of expired entries", ex);
            }
        }

        private void EnsureTableExists()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    connection.Open();
                    var command = new SqlCommand($@"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='{_tableName}' AND xtype='U')
                        CREATE TABLE {_tableName} (
                            StorageKey NVARCHAR(255) PRIMARY KEY,
                            StorageValue VARBINARY(MAX) NOT NULL,
                            ExpirationDate DATETIME2 NOT NULL,
                            LastAccessed DATETIME2 NOT NULL,
                            INDEX IX_{_tableName}_Expiration (ExpirationDate)
                        )", connection);
                    command.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                LogError("Error creating storage table", ex);
                throw;
            }
        }

        private byte[] SerializeObject(object obj)
        {
            if (obj == null) return null;
            
            using (var memoryStream = new MemoryStream())
            {
                var formatter = new BinaryFormatter();
                formatter.Serialize(memoryStream, obj);
                return memoryStream.ToArray();
            }
        }

        private object DeserializeObject(byte[] data)
        {
            if (data == null || data.Length == 0) return null;
            
            using (var memoryStream = new MemoryStream(data))
            {
                var formatter = new BinaryFormatter();
                return formatter.Deserialize(memoryStream);
            }
        }

        private void LogError(string message, Exception ex)
        {
            // Use your existing logging mechanism
            try
            {
                var log = log4net.LogManager.GetLogger(typeof(CustomReportStorage));
                log.Error($"CustomReportStorage: {message}", ex);
            }
            catch
            {
                // Fallback logging if log4net fails
                System.Diagnostics.Debug.WriteLine($"CustomReportStorage Error: {message} - {ex}");
            }
        }

        private void LogInfo(string message)
        {
            try
            {
                var log = log4net.LogManager.GetLogger(typeof(CustomReportStorage));
                log.Info($"CustomReportStorage: {message}");
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"CustomReportStorage Info: {message}");
            }
        }
    }
}
