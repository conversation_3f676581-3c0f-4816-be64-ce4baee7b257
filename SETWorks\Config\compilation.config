<?xml version="1.0" encoding="utf-8" ?>
<compilation debug="true" targetFramework="4.5.1" tempDirectory="c:\Temporary ASP.NET Files\" strict="false" explicit="true" batch="false" >
  <assemblies>
    <add assembly="System.Design, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
    <add assembly="System.Transactions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
    <add assembly="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
    <add assembly="Telerik.Web.Design, Version=2016.2.607.45, Culture=neutral, PublicKeyToken=121FAE78165BA3D4" />
    <add assembly="Telerik.Web.UI, Version=2016.2.607.45, Culture=neutral, PublicKeyToken=121FAE78165BA3D4" />
    <add assembly="Telerik.Reporting, Version=12.1.18.620, Culture=neutral, PublicKeyToken=a9d7983dfcc261be" />
    <add assembly="Telerik.ReportViewer.WebForms, Version=12.1.18.620, Culture=neutral, PublicKeyToken=a9d7983dfcc261be" />
    <add assembly="System.Net.Http, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
    <!--<add assembly="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />-->
  </assemblies>
</compilation>