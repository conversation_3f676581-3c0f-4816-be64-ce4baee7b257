using System;
using System.Collections.Generic;
using System.Text.Json;
using BOs;
using SW.UI;
using Telerik.Reporting;

public partial class Home_Reports_ActivityRecordsInterventionAssessmentKeyReport : SWReportPage
{
    private const string RECORDOPTIONS_LOCATION = "~/Home/Reports/Reports.aspx";

    protected void Page_Init(object sender, EventArgs e)
    {
        filters = SWFiltersManager.createReportFilters(filtersPanel, RadAjaxManager1, ReportViewerActivityRecordsReport, processReport, DIVReportHeader, getUserID(), getReportID()
            , SWFilterFactory.createDepartmentFilter()
            , SWFilterFactory.createConsumerStatusFilter()
            , SWFilterFactory.createConsumerFilter(false)
            , SWFilterFactory.createDateRangeFromFilter()
            , SWFilterFactory.createDateRangeToFilter()
            , SWFilterFactory.createActivityRecordServiceFilter()
            , SWFilterFactory.createActivityRecordSignedStatesFilter()
            , SWFilterFactory.createContractFilter()
            , SWFilterFactory.createAuthorizationFilter());
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            //populateDropDowns();
        }
    }

    protected override void processReport()
    {
        ActivityRecordsInterventionAssessmentKeyReport arReportSpreadsheet = new ActivityRecordsInterventionAssessmentKeyReport();
        ActivityRecordsInterventionAssessmentKeyReport formattedReport = (ActivityRecordsInterventionAssessmentKeyReport)formatReport((Report)arReportSpreadsheet);
        // writing one big query to populate dataset using joins on all the tables and all the data I need
        ReportViewerActivityRecordsReport.ReportSource = formattedReport;

        var filterData = new Dictionary<string, object>
        {
            { "Department", SWFiltersManager.getDepartmentFilter(filters).SelectedValue },
            { "Consumer", SWFiltersManager.getConsumerFilter(filters).SelectedValue },
            { "ConsumerStatus", SWFiltersManager.getConsumerStatusFilter(filters).SelectedValue },
            { "DateFrom", SWFiltersManager.getDateRangeFromFilter(filters).SelectedDate?.ToString("yyyy-MM-dd") },
            { "DateTo", SWFiltersManager.getDateRangeToFilter(filters).SelectedDate?.ToString("yyyy-MM-dd") },
            { "Authorization", SWFiltersManager.getAuthorizationFilter(filters).SelectedValue },
            { "Service", SWFiltersManager.getServiceFilter(filters).SelectedValue },
            { "Contract", SWFiltersManager.getContractFilter(filters).SelectedValue },
            { "SignedState", SWFiltersManager.getActivityRecordSignedStatesFilter(filters).SelectedValue }
        };
        string filterJson = JsonSerializer.Serialize(filterData);
        BOAudit.setAudit(
            getClientID(),
            getUserID(),
            Request.Url.AbsoluteUri,
            "FILTERS",
            filterJson,
            getUsername(),
            Session.SessionID
        );

        ActivityRecordsInterventionAssessmentKeyReportBO arBO = new ActivityRecordsInterventionAssessmentKeyReportBO(getClientID().ToString(), 
            SWFiltersManager.getDepartmentFilter(filters).SelectedValue,
            SWFiltersManager.getConsumerFilter(filters).SelectedValue,
            SWFiltersManager.getConsumerStatusFilter(filters).SelectedValue,
            SWFiltersManager.getDateRangeFromFilter(filters).SelectedDate.Value,
            SWFiltersManager.getDateRangeToFilter(filters).SelectedDate.Value, 
            SessionHelper.getClientDescription(Session),
            SWFiltersManager.getAuthorizationFilter(filters).SelectedValue,
            SWFiltersManager.getServiceFilter(filters).SelectedValue,
            SWFiltersManager.getContractFilter(filters).SelectedValue,
            Int32.Parse(SWFiltersManager.getActivityRecordSignedStatesFilter(filters).SelectedValue),
            "Activity Records Intervention Assessment Key Report  " + SWFiltersManager.getDateRangeFromFilter(filters).SelectedDate.Value.ToShortDateString() + " - " + SWFiltersManager.getDateRangeToFilter(filters).SelectedDate.Value.ToShortDateString(), getUserPrivs());

        formattedReport.DataSource = arBO.getHeaderDataSet();
        formattedReport.crosstab1.DataSource = arBO.getactivityRecordInterAssessReportSet();
        formattedReport.crosstabKeys.DataSource = arBO.getactivityRecordInterAssessReportSet();
    }



}
