using System;
using System.Collections.Generic;
using System.Data;
using BOs;
using Newtonsoft.Json;
using SetWorks.Common.Tools.DataExtractor.Core;
using SetWorks.Common.Tools.DataExtractor.V3;
using SetWorks.Common.Tools.DataExtractor.V3.Core;
using SetWorks.Infrastructure;
using SetWorks.Services.Extractor;
using DataExtractor = SetWorks.Core.Models.DataExtractor;


namespace SetWorks.Services.Extractor;

public class DataExtractorRunner
{
    public SetWorks.Common.Tools.DataExtractor.Core.DataExtractor dataExtractor;
    public List<DSDataExportFilter> filters;
    
    public void prepareDataExtractor(int dataExtractorID, int clientId, Guid userId)
    {
        SetWorksDataContext context = new SetWorksDataContext();
                        
        DataExtractorService des = new DataExtractorService(context);
        DataExtractor dataExtractorModel = des.getDataExtractorByDataExtractorID(dataExtractorID);
        string jsonConfig = dataExtractorModel.JsonConfig;
        dynamic extractConfigJson = JsonConvert.DeserializeObject<dynamic>(jsonConfig);
        jsonConfig = extractConfigJson.ToString();
        dataExtractor = DataExtractorV3Renderer.createDataExtractor(clientId, userId, jsonConfig);
    }
    
    public void assignFilters(List<DSDataExportFilter> filters)
    {
        reformatDateRangeFilters(filters);
        this.filters = filters;
    }
    
    internal void reformatDateRangeFilters(List<DSDataExportFilter> filters)
    {
        for(var i = 0; i < filters.Count; i++)
        {
            var filter = filters[i];
            if (filter.FilterID.Contains("Date"))
            {
                var dateTime = DateTime.MinValue;
                var canConvert = DateTime.TryParse(filter.FilterValue.ToString(), out dateTime);
                        
                if(canConvert)
                {
                    filters[i].FilterValue = dateTime.Date;
                }
            }
        }
    }
    
    public DataTable processReport(int clientId, Guid userId)
    {
        dataExtractor.setInputParameterValuesByReportFilters(filters);
        DataTable dataTable = null;
        var translationTable = DataExtractorV3Manager.TranslatedFields(clientId);
        translationTable = translationTable.Merge(dataExtractor.getTranslationTable());
        OutputResult outputResult = DataExtractorV3Renderer.runDataExtractor(dataExtractor, clientId, userId, false, translationTable);
        if (outputResult.getOutputStatus == OutputStatus.SUCCESS)
        {
            dataTable = (DataTable) outputResult.getOutput;
        }
        else if (outputResult.getOutputStatus == OutputStatus.ERROR)
        {
            if (outputResult.getOutputType == OutputType.OUTPUT_ERROR_ARRAY)
            {
                OutputError[] errors = (OutputError[])outputResult.getOutput;
                dataTable = new DataTable();
                string errorsString = "";
                dataTable.Columns.Add("Error");
                foreach (OutputError error in errors)
                {
                    if (errorsString.Length > 0)
                        errorsString += "<br />";

                    errorsString += error.getErrorString;
                }
                dataTable.Rows.Add(errorsString);
            }
        }
                
        return dataTable;
    }
}