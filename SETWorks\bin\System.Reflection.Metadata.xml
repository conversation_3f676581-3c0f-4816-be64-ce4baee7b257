<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Reflection.Metadata</name>
    </assembly>
    <members>
        <member name="T:System.Reflection.Internal.AbstractMemoryBlock">
            <summary>
            Represents a disposable blob of memory accessed via unsafe pointer.
            </summary>
        </member>
        <member name="P:System.Reflection.Internal.AbstractMemoryBlock.Pointer">
            <summary>
            Pointer to the underlying data (not valid after disposal).
            </summary>
        </member>
        <member name="P:System.Reflection.Internal.AbstractMemoryBlock.Size">
            <summary>
            Size of the block.
            </summary>
        </member>
        <member name="M:System.Reflection.Internal.AbstractMemoryBlock.GetContentUnchecked(System.Int32,System.Int32)">
            <summary>
            Returns the content of the entire memory block. 
            </summary>
            <remarks>
            Does not check bounds.
            
            Only creates a copy of the data if they are not represented by a managed byte array, 
            or if the specified range doens't span the entire block.
            </remarks>
        </member>
        <member name="M:System.Reflection.Internal.AbstractMemoryBlock.Dispose">
            <summary>
            Disposes the block. 
            </summary>
            <remarks>
            The operation is idempotent, but must not be called concurrently with any other operations on the block
            or with another call to Dispose.
            
            Using the block after dispose is an error in our code and therefore no effort is made to throw a tidy 
            ObjectDisposedException and null ref or AV is possible.
            </remarks>
        </member>
        <member name="T:System.Reflection.Internal.ByteArrayMemoryBlock">
            <summary>
            Represents a memory block backed by an array of bytes.
            </summary>
        </member>
        <member name="T:System.Reflection.Internal.ExternalMemoryBlock">
            <summary>
            Class representing raw memory but not owning the memory.
            </summary>
        </member>
        <member name="T:System.Reflection.Internal.ExternalMemoryBlockProvider">
            <summary>
            Represents raw memory owned by an external object. 
            </summary>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlockProvider.GetMemoryBlock">
            <summary>
            Creates and hydrates a memory block representing all data.
            </summary>
            <exception cref="T:System.IO.IOException">Error while reading from the memory source.</exception>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlockProvider.GetMemoryBlock(System.Int32,System.Int32)">
            <summary>
            Creates and hydrates a memory block representing data in the specified range.
            </summary>
            <param name="start">Starting offset relative to the beginning of the data represented by this provider.</param>
            <param name="size">Size of the resulting block.</param>
            <exception cref="T:System.IO.IOException">Error while reading from the memory source.</exception>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlockProvider.GetMemoryBlockImpl(System.Int32,System.Int32)">
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlockProvider.GetStream(System.Reflection.Internal.StreamConstraints@)">
            <summary>
            Gets a seekable and readable <see cref="T:System.IO.Stream"/> that can be used to read all data.
            The operations on the stream has to be done under a lock of <see cref="F:System.Reflection.Internal.StreamConstraints.GuardOpt"/> if non-null.
            The image starts at <see cref="F:System.Reflection.Internal.StreamConstraints.ImageStart"/> and has size <see cref="F:System.Reflection.Internal.StreamConstraints.ImageSize"/>.
            It is the caller's responsibility not to read outside those bounds.
            </summary>
        </member>
        <member name="P:System.Reflection.Internal.MemoryBlockProvider.Size">
            <summary>
            The size of the data.
            </summary>
        </member>
        <member name="T:System.Reflection.Internal.NativeHeapMemoryBlock">
            <summary>
            Represents memory block allocated on native heap.
            </summary>
            <remarks>
            Owns the native memory resource.
            </remarks>
        </member>
        <member name="T:System.Reflection.Internal.StreamMemoryBlockProvider">
            <summary>
            Represents data read from a stream.
            </summary>
            <remarks>
            Uses memory map to load data from streams backed by files that are bigger than <see cref="F:System.Reflection.Internal.StreamMemoryBlockProvider.MemoryMapThreshold"/>.
            </remarks>
        </member>
        <member name="M:System.Reflection.Internal.StreamMemoryBlockProvider.ReadMemoryBlockNoLock(System.IO.Stream,System.Boolean,System.Int64,System.Int32)">
            <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
        </member>
        <member name="M:System.Reflection.Internal.StreamMemoryBlockProvider.GetMemoryBlockImpl(System.Int32,System.Int32)">
            <exception cref="T:System.IO.IOException">Error while reading from the stream.</exception>
        </member>
        <member name="M:System.Reflection.Internal.StreamMemoryBlockProvider.TryCreateMemoryMappedFileBlock(System.Int64,System.Int32,System.Reflection.Internal.MemoryMappedFileBlock@)">
            <exception cref="T:System.IO.IOException">IO error while mapping memory or not enough memory to create the mapping.</exception>
        </member>
        <member name="T:System.Reflection.Internal.EncodingHelper">
             <summary>
             Provides helpers to decode strings from unmanaged memory to System.String while avoiding
             intermediate allocation.
             
             This has three components:
             
               (1) Light-up Encoding.GetString(byte*, int) via reflection and resurface it as extension 
                   method. 
            
                   This is a new API that will provide API convergence across all platforms for 
                   this scenario. It is already on .NET 4.6+ and ASP.NET vNext, but not yet available 
                   on every platform we support. See below for how we fall back.
            
               (2) Deal with WinRT prefixes. 
            
                  When reading managed winmds with projections enabled, the metadata   reader needs to prepend 
                  a WinRT prefix in some case . Doing this without allocation poses a problem
                  as we don't have the prefix and input in contiguous data that we can pass to the
                  Encoding.GetString. We handle this case using pooled managed scratch buffers where we copy
                  the prefix and input and decode using Encoding.GetString(byte[], int, int).
            
               (3) Deal with platforms that don't yet have Encoding.GetString(byte*, int). 
               
                  If we're running on a full framework earlier than 4.6, we will bind to the internal
                  String.CreateStringFromEncoding which is equivalent and Encoding.GetString is just a trivial 
                  wrapper around it in .NET 4.6. This means that we always have the fast path on every
                  full framework version we support.
            
                  If we can't bind to it via reflection, then we emulate it using what is effectively (2) and 
                  with an empty prefix. 
            
             For both (2) and (3), the pooled buffers have a fixed size deemed large enough for the
             vast majority of metadata strings. In the rare worst case (byteCount > threshold and
             (lightUpAttemptFailed || prefix != null), we give up and allocate a temporary array,
             copy to it, decode, and throw it away.
             </summary>
        </member>
        <member name="F:System.Reflection.Internal.Hash.FnvOffsetBias">
            <summary>
            The offset bias value used in the FNV-1a algorithm
            See http://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function
            </summary>
        </member>
        <member name="F:System.Reflection.Internal.Hash.FnvPrime">
            <summary>
            The generative factor used in the FNV-1a algorithm
            See http://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function
            </summary>
        </member>
        <member name="M:System.Reflection.Internal.Hash.GetFNVHashCode(System.Byte[])">
            <summary>
            Compute the FNV-1a hash of a sequence of bytes
            See http://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function
            </summary>
            <param name="data">The sequence of bytes</param>
            <returns>The FNV-1a hash of <paramref name="data"/></returns>
        </member>
        <member name="M:System.Reflection.Internal.Hash.GetFNVHashCode(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <summary>
            Compute the FNV-1a hash of a sequence of bytes
            See http://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function
            </summary>
            <param name="data">The sequence of bytes</param>
            <returns>The FNV-1a hash of <paramref name="data"/></returns>
        </member>
        <member name="T:System.Reflection.Internal.ImmutableByteArrayInterop">
             <summary>
             Provides tools for using <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> in interop scenarios.
             </summary>
             <remarks>
             *** WARNING *** 
             
             If you decide to copy this code elsewhere, please retain the documentation here
             and the Dangerous prefixes in the API names. This will help track down and audit
             other places where this technique (with dangerous consequences when misused) may
             be applied.
            
             A generic version of this API was once public in a pre-release of immutable 
             collections, but  it was deemed to be too subject to abuse when available publicly.
             
             This implementation is scoped to byte arrays as that is all that the metadata reader needs.
             
             Also, since we don't have access to immutable collection internals, we use a trick involving
             overlapping a <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> with a <see cref="T:Byte[]"/> refer. While
             unverifiable, it is valid. See ECMA-335, section II.10.7 Controlling instance layout:
             
             "It is possible to overlap fields in this way, though offsets occupied by an object reference 
             shall not overlap with offsets occupied by a built-in value type or a part of
             another object reference. While one object reference can completely overlap another, this is
             unverifiable."
             
             Furthermore, the fact that <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> backed by a single <see cref="T:Byte[]"/> 
             field is something inherent to the design of ImmutableArray in order to get its performance
             characteristics and therefore something we (Microsoft) are comfortable defining as a contract that 
             can be depended upon as below.
             </remarks>
        </member>
        <member name="M:System.Reflection.Internal.ImmutableByteArrayInterop.DangerousCreateFromUnderlyingArray(System.Byte[]@)">
            <summary>
            Creates a new instance of <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> using a given mutable array as the backing
            field, without creating a defensive copy. It is the responsibility of the caller to ensure no other mutable 
            references exist to the array.  Do not mutate the array after calling this method.
            </summary>
            <param name="array">The mutable array to use as the backing field. The incoming reference is set to null 
            since it should not be retained by the caller.</param>
            <remarks>
            Users of this method should take extra care to ensure that the mutable array given as a parameter
            is never modified. The returned <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> will use the given array as its backing
            field without creating a defensive copy, so changes made to the given mutable array will be observable
            on the returned <see cref="T:System.Collections.Immutable.ImmutableArray`1"/>.  Instance and static methods of <see cref="T:System.Collections.Immutable.ImmutableArray`1"/>
            and <see cref="T:System.Collections.Immutable.ImmutableArray"/> may malfunction if they operate on an <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> instance
            whose underlying backing field is modified. 
            </remarks>
            <returns>An immutable array.</returns>
        </member>
        <member name="M:System.Reflection.Internal.ImmutableByteArrayInterop.DangerousGetUnderlyingArray(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <summary>
            Access the backing mutable array instance for the given <see cref="T:System.Collections.Immutable.ImmutableArray`1"/>, without
            creating a defensive copy.  It is the responsibility of the caller to ensure the array is not modified
            through the returned mutable reference.  Do not mutate the returned array.
            </summary>
            <param name="array">The <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> from which to retrieve the backing field.</param>
            <remarks>
            Users of this method should take extra care to ensure that the returned mutable array is never modified.
            The returned mutable array continues to be used as the backing field of the given <see cref="T:System.Collections.Immutable.ImmutableArray`1"/>
            without creating a defensive copy, so changes made to the returned mutable array will be observable
            on the given <see cref="T:System.Collections.Immutable.ImmutableArray`1"/>.  Instance and static methods of <see cref="T:System.Collections.Immutable.ImmutableArray`1"/>
            and <see cref="T:System.Collections.Immutable.ImmutableArray"/> may malfunction if they operate on an <see cref="T:System.Collections.Immutable.ImmutableArray`1"/> instance
            whose underlying backing field is modified. 
            </remarks>
            <returns>The underlying array, or null if <see cref="P:System.Collections.Immutable.ImmutableArray`1.IsDefault"/> is true.</returns>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlock.PeekCompressedInteger(System.Int32,System.Int32@)">
            <summary>
            Decodes a compressed integer value starting at offset. 
            See Metadata Specification section II.23.2: Blobs and signatures.
            </summary>
            <param name="offset">Offset to the start of the compressed data.</param>
            <param name="numberOfBytesRead">Bytes actually read.</param>
            <returns>
            Value between 0 and 0x1fffffff, or <see cref="F:System.Reflection.Metadata.BlobReader.InvalidCompressedInteger"/> if the value encoding is invalid.
            </returns>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlock.PeekUtf8NullTerminated(System.Int32,System.Byte[],System.Reflection.Metadata.MetadataStringDecoder,System.Int32@,System.Char)">
            <summary>
            Read UTF8 at the given offset up to the given terminator, null terminator, or end-of-block.
            </summary>
            <param name="offset">Offset in to the block where the UTF8 bytes start.</param>
            <param name="prefix">UTF8 encoded prefix to prepend to the bytes at the offset before decoding.</param>
            <param name="utf8Decoder">The UTF8 decoder to use that allows user to adjust fallback and/or reuse existing strings without allocating a new one.</param>
            <param name="numberOfBytesRead">The number of bytes read, which includes the terminator if we did not hit the end of the block.</param>
            <param name="terminator">A character in the ASCII range that marks the end of the string. 
            If a value other than '\0' is passed we still stop at the null terminator if encountered first.</param>
            <returns>The decoded string.</returns>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlock.GetUtf8NullTerminatedLength(System.Int32,System.Int32@,System.Char)">
            <summary>
            Get number of bytes from offset to given terminator, null terminator, or end-of-block (whichever comes first).
            Returned length does not include the terminator, but numberOfBytesRead out parameter does.
            </summary>
            <param name="offset">Offset in to the block where the UTF8 bytes start.</param>
            <param name="terminator">A character in the ASCII range that marks the end of the string. 
            If a value other than '\0' is passed we still stop at the null terminator if encountered first.</param>
            <param name="numberOfBytesRead">The number of bytes read, which includes the terminator if we did not hit the end of the block.</param>
            <returns>Length (byte count) not including terminator.</returns>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlock.BinarySearchForSlot(System.Int32,System.Int32,System.Int32,System.UInt32,System.Boolean)">
            <summary>
            In a table that specifies children via a list field (e.g. TypeDef.FieldList, TypeDef.MethodList), 
            searches for the parent given a reference to a child.
            </summary>
            <returns>Returns row number [0..RowCount).</returns>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlock.BinarySearchReference(System.Int32,System.Int32,System.Int32,System.UInt32,System.Boolean)">
            <summary>
            In a table ordered by a column containing entity references searches for a row with the specified reference.
            </summary>
            <returns>Returns row number [0..RowCount) or -1 if not found.</returns>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlock.BinarySearchReferenceRange(System.Int32,System.Int32,System.Int32,System.UInt32,System.Boolean,System.Int32@,System.Int32@)">
            <summary>
            Calculates a range of rows that have specified value in the specified column in a table that is sorted by that column.
            </summary>
        </member>
        <member name="M:System.Reflection.Internal.MemoryBlock.BinarySearchReferenceRange(System.Int32[],System.Int32,System.Int32,System.UInt32,System.Boolean,System.Int32@,System.Int32@)">
            <summary>
            Calculates a range of rows that have specified value in the specified column in a table that is sorted by that column.
            </summary>
        </member>
        <member name="T:System.Reflection.Internal.PooledStringBuilder">
            <summary>
            The usage is:
                   var inst = PooledStringBuilder.GetInstance();
                   var sb = inst.builder;
                   ... Do Stuff...
                   ... sb.ToString() ...
                   inst.Free();
            </summary>
        </member>
        <member name="T:System.Reflection.Internal.ObjectPool`1">
            <summary>
            Generic implementation of object pooling pattern with predefined pool size limit. The main
            purpose is that limited number of frequently used objects can be kept in the pool for
            further recycling.
            
            Notes: 
            1) it is not the goal to keep all returned objects. Pool is not meant for storage. If there
               is no space in the pool, extra returned objects will be dropped.
            
            2) it is implied that if object was obtained from a pool, the caller will return it back in
               a relatively short time. Keeping checked out objects for long durations is ok, but 
               reduces usefulness of pooling. Just new up your own.
            
            Not returning objects to the pool in not detrimental to the pool's work, but is a bad practice. 
            Rationale: 
               If there is no intent for reusing the object, do not use pool - just use "new". 
            </summary>
        </member>
        <member name="M:System.Reflection.Internal.ObjectPool`1.Allocate">
            <summary>
            Produces an instance.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically 
            reducing how far we will typically search.
            </remarks>
        </member>
        <member name="M:System.Reflection.Internal.ObjectPool`1.Free(`0)">
            <summary>
            Returns objects to the pool.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically 
            reducing how far we will typically search in Allocate.
            </remarks>
        </member>
        <member name="M:System.Reflection.Internal.StreamExtensions.CopyTo(System.IO.Stream,System.Byte*,System.Int32)">
            <summary>
            Copies specified amount of data from given stream to a target memory pointer.
            </summary>
            <exception cref="T:System.IO.IOException">unexpected stream end.</exception>
        </member>
        <member name="M:System.Reflection.Internal.StreamExtensions.TryReadAll(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Attempts to read all of the requested bytes from the stream into the buffer
            </summary>
            <returns>
            The number of bytes read. Less than <paramref name="count" /> will
            only be returned if the end of stream is reached before all bytes can be read.
            </returns>
            <remarks>
            Unlike <see cref="M:System.IO.Stream.Read(System.Byte[],System.Int32,System.Int32)"/> it is not guaranteed that
            the stream position or the output buffer will be unchanged if an exception is
            returned.
            </remarks>
        </member>
        <member name="M:System.Reflection.Internal.StreamExtensions.GetAndValidateSize(System.IO.Stream,System.Int32,System.String)">
            <summary>
            Resolve image size as either the given user-specified size or distance from current position to end-of-stream.
            Also performs the relevant argument validation and publicly visible caller has same argument names.
            </summary>
            <exception cref="T:System.ArgumentException">size is 0 and distance from current position to end-of-stream can't fit in Int32.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end-of-stream from current position.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.PathUtilities.IndexOfFileName(System.String)">
            <summary>
            Returns the position in given path where the file name starts.
            </summary>
            <returns>-1 if path is null.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.PathUtilities.GetFileName(System.String,System.Boolean)">
            <summary>
            Get file name from path.
            </summary>
            <remarks>Unlike <see cref="M:System.IO.Path.GetFileName(System.String)"/> doesn't check for invalid path characters.</remarks>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder">
            <summary>
            Encodes method body stream.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.AddMethodBody(System.Int32,System.Int32,System.Int32,System.Boolean,System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.Ecma335.MethodBodyAttributes)">
            <summary>
            Encodes a method body and adds it to the method body stream.
            </summary>
            <param name="codeSize">Number of bytes to be reserved for instructions.</param>
            <param name="maxStack">Max stack.</param>
            <param name="exceptionRegionCount">Number of exception regions.</param>
            <param name="hasSmallExceptionRegions">True if the exception regions should be encoded in 'small' format.</param>
            <param name="localVariablesSignature">Local variables signature handle.</param>
            <param name="attributes">Attributes.</param>
            <returns>The offset of the encoded body within the method body stream.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="codeSize"/>, <paramref name="exceptionRegionCount"/>, or <paramref name="maxStack"/> is out of allowed range.
            </exception>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.MethodBody.Offset">
            <summary>
            Offset of the encoded method body in method body stream.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.MethodBody.Instructions">
            <summary>
            Blob reserved for instructions.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.MethodBody.ExceptionRegions">
            <summary>
            Use to encode exception regions to the method body.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MethodBodyStreamEncoder.AddMethodBody(System.Reflection.Metadata.Ecma335.InstructionEncoder,System.Int32,System.Reflection.Metadata.StandaloneSignatureHandle,System.Reflection.Metadata.Ecma335.MethodBodyAttributes)">
            <summary>
            Encodes a method body and adds it to the method body stream.
            </summary>
            <param name="instructionEncoder">Instruction encoder.</param>
            <param name="maxStack">Max stack.</param>
            <param name="localVariablesSignature">Local variables signature handle.</param>
            <param name="attributes">Attributes.</param>
            <returns>The offset of the encoded body within the method body stream.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="instructionEncoder"/> has default value.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="maxStack"/> is out of range [0, <see cref="F:System.UInt16.MaxValue"/>].</exception>
            <exception cref="T:System.InvalidOperationException">
            A label targeted by a branch in the instruction stream has not been marked,
            or the distance between a branch instruction and the target label is doesn't fit the size of the instruction operand.
            </exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.SetCapacity(System.Reflection.Metadata.Ecma335.TableIndex,System.Int32)">
            <summary>
            Sets the capacity of the specified table. 
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="table"/> is not a valid table index.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="rowCount"/> is negative.</exception>
            <remarks>
            Use to reduce allocations if the approximate number of rows is known ahead of time.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetRowCount(System.Reflection.Metadata.Ecma335.TableIndex)">
            <summary>
            Returns the current number of entires in the specified table.
            </summary>
            <param name="table">Table index.</param>
            <returns>The number of entires in the table.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="table"/> is not a valid table index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetRowCounts">
            <summary>
            Returns the current number of entires in each table.
            </summary>
            <returns>
            An array of size <see cref="F:System.Reflection.Metadata.Ecma335.MetadataTokens.TableCount"/> with each item filled with the current row count of the corresponding table.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddTypeDefinition(System.Reflection.TypeAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.FieldDefinitionHandle,System.Reflection.Metadata.MethodDefinitionHandle)">
            <summary>
            Adds a type definition.
            </summary>
            <param name="attributes">Attributes</param>
            <param name="namespace">Namespace</param>
            <param name="name">Type name</param>
            <param name="baseType"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>, <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/> or nil.</param>
            <param name="fieldList">
            If the type declares fields the handle of the first one, otherwise the handle of the first field declared by the next type definition.
            If no type defines any fields in the module, <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.FieldDefinitionHandle(System.Int32)"/>(1).
            </param>
            <param name="methodList">
            If the type declares methods the handle of the first one, otherwise the handle of the first method declared by the next type definition.
            If no type defines any methods in the module, <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.MethodDefinitionHandle(System.Int32)"/>(1).
            </param>
            <exception cref="T:System.ArgumentException"><paramref name="baseType"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddTypeLayout(System.Reflection.Metadata.TypeDefinitionHandle,System.UInt16,System.UInt32)">
            <summary>
            Defines a type layout of a type definition.
            </summary>
            <param name="type">Type definition.</param>
            <param name="packingSize">
            Specifies that fields should be placed within the type instance at byte addresses which are a multiple of the value, 
            or at natural alignment for that field type, whichever is smaller. Shall be one of the following: 0, 1, 2, 4, 8, 16, 32, 64, or 128. 
            A value of zero indicates that the packing size used should match the default for the current platform.
            </param>
            <param name="size">
            Indicates a minimum size of the type instance, and is intended to allow for padding. 
            The amount of memory allocated is the maximum of the size calculated from the layout and <paramref name="size"/>. 
            Note that if this directive applies to a value type, then the size shall be less than 1 MB.
            </param>
            <remarks>
            Entires must be added in the same order as the corresponding type definitions.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddInterfaceImplementation(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Adds an interface implementation to a type.
            </summary>
            <param name="type">The type implementing the interface.</param>
            <param name="implementedInterface">
            The interface being implemented: 
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>.
            </param>
            <remarks>
            Interface implementations must be added in the same order as the corresponding type definitions implementing the interface.
            If a type implements multiple interfaces the corresponding entries must be added in the order determined by their coded indices (<see cref="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeDefOrRefOrSpec(System.Reflection.Metadata.EntityHandle)"/>).
            </remarks>
            <exception cref="T:System.ArgumentException"><paramref name="implementedInterface"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddNestedType(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.TypeDefinitionHandle)">
            <summary>
            Defines a nesting relationship to specified type definitions.
            </summary>
            <param name="type">The nested type definition handle.</param>
            <param name="enclosingType">The enclosing type definition handle.</param>
            <remarks>
            Entries must be added in the same order as the corresponding nested type definitions.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddTypeReference(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle)">
            <summary>
            Add a type reference.
            </summary>
            <param name="resolutionScope">
            The entity declaring the target type: 
            <see cref="T:System.Reflection.Metadata.ModuleDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>, <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>, or nil.
            </param>
            <param name="namespace">Namespace.</param>
            <param name="name">Type name.</param>
            <exception cref="T:System.ArgumentException"><paramref name="resolutionScope"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddProperty(System.Reflection.PropertyAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Adds a property defintion.
            </summary>
            <param name="attributes">Attributes</param>
            <param name="name">Name</param>
            <param name="signature">Signature of the property.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddEvent(System.Reflection.EventAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Adds an event defintion.
            </summary>
            <param name="attributes">Attributes</param>
            <param name="name">Name</param>
            <param name="type">Type of the event: <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>, or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/></param>
            <exception cref="T:System.ArgumentException"><paramref name="type"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddConstant(System.Reflection.Metadata.EntityHandle,System.Object)">
            <summary>
            Adds a default value for a parameter, field or property.
            </summary>
            <param name="parent"><see cref="T:System.Reflection.Metadata.ParameterHandle"/>, <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>, or <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/></param>
            <param name="value">The constant value.</param>
            <remarks>
            Entries may be added in any order. The table is automatically sorted when serialized.
            </remarks>
            <exception cref="T:System.ArgumentException"><paramref name="parent"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodSemantics(System.Reflection.Metadata.EntityHandle,System.Reflection.MethodSemanticsAttributes,System.Reflection.Metadata.MethodDefinitionHandle)">
            <summary>
            Associates a method (a getter, a setter, an adder, etc.) with a property or an event.
            </summary>
            <param name="association"><see cref="T:System.Reflection.Metadata.EventDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/>.</param>
            <param name="semantics">Semantics.</param>
            <param name="methodDefinition">Method definition.</param>
            <exception cref="T:System.ArgumentException"><paramref name="association"/> doesn't have the expected handle kind.</exception>
            <remarks>
            Entries may be added in any order. The table is automatically sorted when serialized.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddCustomAttribute(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Add a custom attribute.
            </summary>
            <param name="parent">
            An entity to attach the custom attribute to: 
            <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.ParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.InterfaceImplementationHandle"/>,
            <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle"/>,
            <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.EventDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.StandaloneSignatureHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>,
            <see cref="T:System.Reflection.Metadata.ExportedTypeHandle"/>,
            <see cref="T:System.Reflection.Metadata.ManifestResourceHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterConstraintHandle"/> or
            <see cref="T:System.Reflection.Metadata.MethodSpecificationHandle"/>.
            </param>
            <param name="constructor">
            Custom attribute constructor: <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/>
            </param>
            <param name="value">
            Custom attribute value blob.
            </param>
            <remarks>
            Entries may be added in any order. The table is automatically sorted when serialized.
            </remarks>
            <exception cref="T:System.ArgumentException"><paramref name="parent"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodSpecification(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Adds a method specification (instantiation).
            </summary>
            <param name="method">Generic method: <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/></param>
            <param name="instantiation">Instantiation blob encoding the generic arguments of the method.</param>
            <exception cref="T:System.ArgumentException"><paramref name="method"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddParameter(System.Reflection.ParameterAttributes,System.Reflection.Metadata.StringHandle,System.Int32)">
            <summary>
            Adds a parameter definition.
            </summary>
            <param name="attributes"><see cref="T:System.Reflection.ParameterAttributes"/></param>
            <param name="name">Parameter name (optional).</param>
            <param name="sequenceNumber">Sequence number of the parameter. Value of 0 refers to the owner method's return type; its parameters are then numbered from 1 onwards.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="sequenceNumber"/> is greater than <see cref="F:System.UInt16.MaxValue"/>.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddGenericParameter(System.Reflection.Metadata.EntityHandle,System.Reflection.GenericParameterAttributes,System.Reflection.Metadata.StringHandle,System.Int32)">
            <summary>
            Adds a generic parameter definition.
            </summary>
            <param name="parent">Parent entity handle: <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/></param>
            <param name="attributes">Attributes.</param>
            <param name="name">Parameter name.</param>
            <param name="index">Zero-based parameter index.</param>
            <remarks>
            Generic parameters must be added in an order determined by the coded index of their parent entity (<see cref="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeOrMethodDef(System.Reflection.Metadata.EntityHandle)"/>).
            Generic parameters with the same parent must be ordered by their <paramref name="index"/>.
            </remarks>
            <exception cref="T:System.ArgumentException"><paramref name="parent"/> doesn't have the expected handle kind.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is greater than <see cref="F:System.UInt16.MaxValue"/>.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddGenericParameterConstraint(System.Reflection.Metadata.GenericParameterHandle,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Adds a type constraint to a generic parameter.
            </summary>
            <param name="genericParameter">Generic parameter to constrain.</param>
            <param name="constraint">Type constraint: <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/></param>
            <exception cref="T:System.ArgumentException"><paramref name="genericParameter"/> doesn't have the expected handle kind.</exception>
            <remarks>
            Constraints must be added in the same order as the corresponding generic parameters.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddFieldDefinition(System.Reflection.FieldAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Adds a field definition.
            </summary>
            <param name="attributes">Field attributes.</param>
            <param name="name">Field name.</param>
            <param name="signature">Field signature. Use <see cref="M:System.Reflection.Metadata.Ecma335.BlobEncoder.FieldSignature"/> to construct the blob.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddFieldLayout(System.Reflection.Metadata.FieldDefinitionHandle,System.Int32)">
            <summary>
            Defines a field layout of a field definition.
            </summary>
            <param name="field">Field definition.</param>
            <param name="offset">The byte offset of the field within the declaring type instance.</param>
            <remarks>
            Entires must be added in the same order as the corresponding field definitions.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMarshallingDescriptor(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Add marshalling information to a field or a parameter.
            </summary>
            <param name="parent"><see cref="T:System.Reflection.Metadata.ParameterHandle"/> or <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>.</param>
            <param name="descriptor">Descriptor blob.</param>
            <exception cref="T:System.ArgumentException"><paramref name="parent"/> doesn't have the expected handle kind.</exception>
            <remarks>
            Entries may be added in any order. The table is automatically sorted when serialized.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddFieldRelativeVirtualAddress(System.Reflection.Metadata.FieldDefinitionHandle,System.Int32)">
            <summary>
            Adds a mapping from a field to its initial value stored in the PE image.
            </summary>
            <param name="field">Field definition handle.</param>
            <param name="offset">
            Offset within the block in the PE image that stores initial values of mapped fields (usually in .text section).
            The final relative virtual address stored in the metadata is calculated when the metadata is serialized
            by adding the offset to the virtual address of the block start.
            </param>
            <remarks>
            Entires must be added in the same order as the corresponding field definitions.
            </remarks>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="offset"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodDefinition(System.Reflection.MethodAttributes,System.Reflection.MethodImplAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle,System.Int32,System.Reflection.Metadata.ParameterHandle)">
            <summary>
            Adds a method definition.
            </summary>
            <param name="attributes"><see cref="T:System.Reflection.MethodAttributes"/></param>
            <param name="implAttributes"><see cref="T:System.Reflection.MethodImplAttributes"/></param>
            <param name="name">Method name/</param>
            <param name="signature">Method signature.</param>
            <param name="bodyOffset">
            Offset within the block in the PE image that stores method bodies (IL stream), 
            or -1 if the method doesn't have a body.
            
            The final relative virtual address stored in the metadata is calculated when the metadata is serialized
            by adding the offset to the virtual address of the beginning of the block.
            </param>
            <param name="parameterList">
            If the method declares parameters in Params table the handle of the first one, otherwise the handle of the first parameter declared by the next method definition.
            If no parameters are declared in the module, <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.ParameterHandle(System.Int32)"/>(1).
            </param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="bodyOffset"/> is less than -1.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodImport(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.MethodImportAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.ModuleReferenceHandle)">
            <summary>
            Adds import information to a method definition (P/Invoke).
            </summary>
            <param name="method">Method definition handle.</param>
            <param name="attributes">Attributes.</param>
            <param name="name">Unmanaged method name.</param>
            <param name="module">Module containing the unmanaged method.</param>
            <remarks>
            Method imports must be added in the same order as the corresponding method definitions.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodImplementation(System.Reflection.Metadata.TypeDefinitionHandle,System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Defines an implementation for a method declaration within a type.
            </summary>
            <param name="type">Type</param>
            <param name="methodBody"><see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/> which provides the implementation.</param>
            <param name="methodDeclaration"><see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/> the method being implemented.</param>
            <remarks>
            Method implementations must be added in the same order as the corresponding type definitions.
            </remarks>
            <exception cref="T:System.ArgumentException"><paramref name="methodBody"/> or <paramref name="methodDeclaration"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMemberReference(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Adds a MemberRef table row.
            </summary>
            <param name="parent">Containing entity:
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, 
            <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>, 
            <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>, or 
            <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>.
            </param>
            <param name="name">Member name.</param>
            <param name="signature">Member signature.</param>
            <exception cref="T:System.ArgumentException"><paramref name="parent"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddManifestResource(System.Reflection.ManifestResourceAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle,System.UInt32)">
            <summary>
            Adds a manifest resource.
            </summary>
            <param name="attributes">Attributes</param>
            <param name="name">Resource name</param>
            <param name="implementation"><see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>, <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>, or nil</param>
            <param name="offset">Specifies the byte offset within the referenced file at which this resource record begins.</param>
            <exception cref="T:System.ArgumentException"><paramref name="implementation"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddExportedType(System.Reflection.TypeAttributes,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.EntityHandle,System.Int32)">
            <summary>
            Adds an exported type.
            </summary>
            <param name="attributes">Attributes</param>
            <param name="namespace">Namespace</param>
            <param name="name">Type name</param>
            <param name="implementation"><see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>, <see cref="T:System.Reflection.Metadata.ExportedTypeHandle"/> or <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/></param>
            <param name="typeDefinitionId">Type definition id</param>
            <exception cref="T:System.ArgumentException"><paramref name="implementation"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddDeclarativeSecurityAttribute(System.Reflection.Metadata.EntityHandle,System.Reflection.DeclarativeSecurityAction,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Adds declarative security attribute to a type, method or an assembly.
            </summary>
            <param name="parent"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>, or <see cref="T:System.Reflection.Metadata.AssemblyDefinitionHandle"/></param>
            <param name="action">Security action</param>
            <param name="permissionSet">Permission set blob.</param>
            <exception cref="T:System.ArgumentException"><paramref name="parent"/> doesn't have the expected handle kind.</exception>
            <remarks>
            Entries may be added in any order. The table is automatically sorted when serialized.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddDocument(System.Reflection.Metadata.BlobHandle,System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.BlobHandle,System.Reflection.Metadata.GuidHandle)">
            <summary>
            Add document debug information.
            </summary>
            <param name="name">
            Document Name blob.
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#document-name-blob
            </param>
            <param name="hashAlgorithm">
            GUID of the hash algorithm used to calculate the value of <paramref name="hash"/>.
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#document-table-0x30 for common values.
            </param>
            <param name="hash">
            The hash of the document content.
            </param>
            <param name="language">
            GUID of the language.
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#document-table-0x30 for common values.
            </param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddMethodDebugInformation(System.Reflection.Metadata.DocumentHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Add method debug information.
            </summary>
            <param name="document">
            The handle of a single document containing all sequence points of the method, or nil if the method doesn't have sequence points or spans multiple documents.
            </param>
            <param name="sequencePoints">
            Sequence Points blob, or nil if the method doesn't have sequence points.
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#sequence-points-blob.
            </param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddLocalScope(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.Metadata.ImportScopeHandle,System.Reflection.Metadata.LocalVariableHandle,System.Reflection.Metadata.LocalConstantHandle,System.Int32,System.Int32)">
            <summary>
            Add local scope debug information.
            </summary>
            <param name="method">The containing method.</param>
            <param name="importScope">Handle of the associated import scope.</param>
            <param name="variableList">
            If the scope declares variables the handle of the first one, otherwise the handle of the first variable declared by the next scope definition.
            If no scope defines any variables, <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.LocalVariableHandle(System.Int32)"/>(1).
            </param>
            <param name="constantList">
            If the scope declares constants the handle of the first one, otherwise the handle of the first constant declared by the next scope definition.
            If no scope defines any constants, <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.LocalConstantHandle(System.Int32)"/>(1).
            </param>
            <param name="startOffset">Offset of the first instruction covered by the scope.</param>
            <param name="length">The length (in bytes) of the scope.</param>
            <remarks>
            Local scopes should be added in the same order as the corresponding method definition. 
            Within a method they should be ordered by ascending <paramref name="startOffset"/> and then by descending <paramref name="length"/>.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddLocalVariable(System.Reflection.Metadata.LocalVariableAttributes,System.Int32,System.Reflection.Metadata.StringHandle)">
            <summary>
            Add local variable debug information.
            </summary>
            <param name="attributes"><see cref="T:System.Reflection.Metadata.LocalVariableAttributes"/></param>
            <param name="index">Local variable index in the local signature (zero-based).</param>
            <param name="name">Name of the variable.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is greater than <see cref="F:System.UInt16.MaxValue"/>.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddLocalConstant(System.Reflection.Metadata.StringHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Add local constant debug information.
            </summary>
            <param name="name">Name of the variable.</param>
            <param name="signature">
            LocalConstantSig blob, see https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#localconstantsig-blob. 
            </param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddImportScope(System.Reflection.Metadata.ImportScopeHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Add local scope debug information.
            </summary>
            <param name="parentScope">Parent scope handle.</param>
            <param name="imports">
            Imports blob, see https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#imports-blob. 
            </param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddStateMachineMethod(System.Reflection.Metadata.MethodDefinitionHandle,System.Reflection.Metadata.MethodDefinitionHandle)">
            <summary>
            Add state machine method debug information.
            </summary>
            <param name="moveNextMethod">Handle of the MoveNext method of the state machine (the compiler-generated method).</param>
            <param name="kickoffMethod">Handle of the kickoff method (the user defined iterator/async method)</param>
            <remarks>
            Entries should be added in the same order as the corresponding MoveNext method definitions.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.AddCustomDebugInformation(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.GuidHandle,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Add custom debug information.
            </summary>
            <param name="parent">
            An entity to attach the debug information to: 
            <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.ParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.InterfaceImplementationHandle"/>,
            <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle"/>,
            <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.EventDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.StandaloneSignatureHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>,
            <see cref="T:System.Reflection.Metadata.ExportedTypeHandle"/>,
            <see cref="T:System.Reflection.Metadata.ManifestResourceHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterConstraintHandle"/>,
            <see cref="T:System.Reflection.Metadata.MethodSpecificationHandle"/>,
            <see cref="T:System.Reflection.Metadata.DocumentHandle"/>,
            <see cref="T:System.Reflection.Metadata.LocalScopeHandle"/>,
            <see cref="T:System.Reflection.Metadata.LocalVariableHandle"/>,
            <see cref="T:System.Reflection.Metadata.LocalConstantHandle"/> or
            <see cref="T:System.Reflection.Metadata.ImportScopeHandle"/>.
            </param>
            <param name="kind">Information kind. Determines the structure of the <paramref name="value"/> blob.</param>
            <param name="value">Custom debug information blob.</param>
            <exception cref="T:System.ArgumentException"><paramref name="parent"/> doesn't have the expected handle kind.</exception>
            <remarks>
            Entries may be added in any order. The table is automatically sorted when serialized.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a builder for metadata tables and heaps.
            </summary>
            <param name="userStringHeapStartOffset">
            Start offset of the User String heap. 
            The cumulative size of User String heaps of all previous EnC generations. Should be 0 unless the metadata is EnC delta metadata.
            </param>
            <param name="stringHeapStartOffset">
            Start offset of the String heap. 
            The cumulative size of String heaps of all previous EnC generations. Should be 0 unless the metadata is EnC delta metadata.
            </param>
            <param name="blobHeapStartOffset">
            Start offset of the Blob heap. 
            The cumulative size of Blob heaps of all previous EnC generations. Should be 0 unless the metadata is EnC delta metadata.
            </param>
            <param name="guidHeapStartOffset">
            Start offset of the Guid heap. 
            The cumulative size of Guid heaps of all previous EnC generations. Should be 0 unless the metadata is EnC delta metadata.
            </param>
            <exception cref="T:System.Reflection.Metadata.ImageFormatLimitationException">Offset is too big.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Offset is negative.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="guidHeapStartOffset"/> is not a multiple of size of GUID.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.SetCapacity(System.Reflection.Metadata.Ecma335.HeapIndex,System.Int32)">
            <summary>
            Sets the capacity of the specified table. 
            </summary>
            <param name="heap">Heap index.</param>
            <param name="byteCount">Number of bytes.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="heap"/> is not a valid heap index.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
            <remarks>
            Use to reduce allocations if the approximate number of bytes is known ahead of time.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlob(System.Reflection.Metadata.BlobBuilder)">
            <summary>
            Adds specified blob to Blob heap, if it's not there already.
            </summary>
            <param name="value"><see cref="T:System.Reflection.Metadata.BlobBuilder"/> containing the blob.</param>
            <returns>Handle to the added or existing blob.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlob(System.Byte[])">
            <summary>
            Adds specified blob to Blob heap, if it's not there already.
            </summary>
            <param name="value">Array containing the blob.</param>
            <returns>Handle to the added or existing blob.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlob(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <summary>
            Adds specified blob to Blob heap, if it's not there already.
            </summary>
            <param name="value">Array containing the blob.</param>
            <returns>Handle to the added or existing blob.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddConstantBlob(System.Object)">
            <summary>
            Encodes a constant value to a blob and adds it to the Blob heap, if it's not there already.
            Uses UTF16 to encode string constants.
            </summary>
            <param name="value">Constant value.</param>
            <returns>Handle to the added or existing blob.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlobUTF16(System.String)">
            <summary>
            Encodes a string using UTF16 encoding to a blob and adds it to the Blob heap, if it's not there already.
            </summary>
            <param name="value">String.</param>
            <returns>Handle to the added or existing blob.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddBlobUTF8(System.String,System.Boolean)">
            <summary>
            Encodes a string using UTF8 encoding to a blob and adds it to the Blob heap, if it's not there already.
            </summary>
            <param name="value">Constant value.</param>
            <param name="allowUnpairedSurrogates">
            True to encode unpaired surrogates as specified, otherwise replace them with U+FFFD character.
            </param>
            <returns>Handle to the added or existing blob.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddDocumentName(System.String)">
            <summary>
            Encodes a debug document name and adds it to the Blob heap, if it's not there already.
            </summary>
            <param name="value">Document name.</param>
            <returns>
            Handle to the added or existing document name blob
            (see https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#DocumentNameBlob).
            </returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddGuid(System.Guid)">
            <summary>
            Adds specified Guid to Guid heap, if it's not there already.
            </summary>
            <param name="guid">Guid to add.</param>
            <returns>Handle to the added or existing Guid.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.ReserveGuid">
            <summary>
            Reserves space on the Guid heap for a GUID.
            </summary>
            <returns>
            Handle to the reserved Guid and a <see cref="T:System.Reflection.Metadata.Blob"/> representing the GUID blob as stored on the heap.
            </returns>
            <exception cref="T:System.Reflection.Metadata.ImageFormatLimitationException">The remaining space on the heap is too small to fit the string.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddString(System.String)">
            <summary>
            Adds specified string to String heap, if it's not there already.
            </summary>
            <param name="value">Array containing the blob.</param>
            <returns>Handle to the added or existing blob.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.ReserveUserString(System.Int32)">
            <summary>
            Reserves space on the User String heap for a string of specified length.
            </summary>
            <param name="length">The number of characters to reserve.</param>
            <returns>
            Handle to the reserved User String and a <see cref="T:System.Reflection.Metadata.Blob"/> representing the entire User String blob (including its length and terminal character).
            
            Handle may be used in <see cref="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadString(System.Reflection.Metadata.UserStringHandle)"/>.
            Use <see cref="M:System.Reflection.Metadata.BlobWriter.WriteUserString(System.String)"/> to fill in the blob content.
            </returns>
            <exception cref="T:System.Reflection.Metadata.ImageFormatLimitationException">The remaining space on the heap is too small to fit the string.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="length"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.GetOrAddUserString(System.String)">
            <summary>
            Adds specified string to User String heap, if it's not there already.
            </summary>
            <param name="value">String to add.</param>
            <returns>
            Handle to the added or existing string.
            May be used in <see cref="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadString(System.Reflection.Metadata.UserStringHandle)"/>.
            </returns>
            <exception cref="T:System.Reflection.Metadata.ImageFormatLimitationException">The remaining space on the heap is too small to fit the string.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataBuilder.SerializeStringHeap(System.Reflection.Metadata.BlobBuilder,System.Collections.Generic.Dictionary{System.String,System.Reflection.Metadata.StringHandle},System.Int32)">
            <summary>
            Fills in stringIndexMap with data from stringIndex and write to stringWriter.
            Releases stringIndex as the stringTable is sealed after this point.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.MetadataBuilder.SuffixSort">
            <summary>
            Sorts strings such that a string is followed immediately by all strings
            that are a suffix of it.  
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.MetadataRootBuilder">
            <summary>
            Builder of a Metadata Root to be embedded in a Portable Executable image.
            </summary>
            <remarks>
            Metadata root constitutes of a metadata header followed by metadata streams (#~, #Strings, #US, #Guid and #Blob).
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.MetadataVersion">
            <summary>
            Metadata version string.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.SuppressValidation">
            <summary>
            True to suppresses basic validation of metadata tables. 
            The validation verifies that entries in the tables were added in order required by the ECMA specification.
            It does not enforce all specification requirements on metadata tables.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.#ctor(System.Reflection.Metadata.Ecma335.MetadataBuilder,System.String,System.Boolean)">
            <summary>
            Creates a builder of a metadata root.
            </summary>
            <param name="tablesAndHeaps">
            Builder populated with metadata entities stored in tables and values stored in heaps.
            The entities and values will be enumerated when serializing the metadata root.
            </param>
            <param name="metadataVersion">
            The version string written to the metadata header. The default value is "v4.0.30319".
            </param>
            <param name="suppressValidation">
            True to suppresses basic validation of metadata tables during serialization.
            The validation verifies that entries in the tables were added in order required by the ECMA specification.
            It does not enforce all specification requirements on metadata tables.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="tablesAndHeaps"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="metadataVersion"/> is too long (the number of bytes when UTF8-encoded must be less than 255).</exception>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.Sizes">
            <summary>
            Returns sizes of various metadata structures.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.Serialize(System.Reflection.Metadata.BlobBuilder,System.Int32,System.Int32)">
            <summary>
            Serializes metadata root content into the given <see cref="T:System.Reflection.Metadata.BlobBuilder"/>.
            </summary>
            <param name="builder">Builder to write to.</param>
            <param name="methodBodyStreamRva">
            The relative virtual address of the start of the method body stream.
            Used to calculate the final value of RVA fields of MethodDef table.
            </param>
            <param name="mappedFieldDataStreamRva">
            The relative virtual address of the start of the field init data stream.
            Used to calculate the final value of RVA fields of FieldRVA table.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="builder"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="methodBodyStreamRva"/> or <paramref name="mappedFieldDataStreamRva"/> is negative.</exception>
            <exception cref="T:System.InvalidOperationException">
            A metadata table is not ordered as required by the specification and <see cref="P:System.Reflection.Metadata.Ecma335.MetadataRootBuilder.SuppressValidation"/> is false.
            </exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.StringHeap.EqualsRaw(System.Reflection.Metadata.StringHandle,System.String)">
            <summary>
            Returns true if the given raw (non-virtual) handle represents the same string as given ASCII string.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.StringHeap.IndexOfRaw(System.Int32,System.Char)">
            <summary>
            Returns the heap index of the given ASCII character or -1 if not found prior null terminator or end of heap.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.StringHeap.StartsWithRaw(System.Reflection.Metadata.StringHandle,System.String)">
            <summary>
            Returns true if the given raw (non-virtual) handle represents a string that starts with given ASCII prefix.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.StringHeap.BinarySearchRaw(System.String[],System.Reflection.Metadata.StringHandle)">
            <summary>
            Equivalent to Array.BinarySearch, searches for given raw (non-virtual) handle in given array of ASCII strings.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.CustomAttributeDecoder`1">
            <summary>
            Decodes custom attribute blobs.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddFinallyRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
            <summary>
            Adds finally region.
            </summary>
            <param name="tryStart">Label marking the first instruction of the try block.</param>
            <param name="tryEnd">Label marking the instruction immediately following the try block.</param>
            <param name="handlerStart">Label marking the first instruction of the handler.</param>
            <param name="handlerEnd">Label marking the instruction immediately following the handler.</param>
            <returns>Encoder for the next clause.</returns>
            <exception cref="T:System.ArgumentException">A label was not defined by an instruction encoder this builder is associated with.</exception>
            <exception cref="T:System.ArgumentNullException">A label has default value.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddFaultRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
            <summary>
            Adds fault region.
            </summary>
            <param name="tryStart">Label marking the first instruction of the try block.</param>
            <param name="tryEnd">Label marking the instruction immediately following the try block.</param>
            <param name="handlerStart">Label marking the first instruction of the handler.</param>
            <param name="handlerEnd">Label marking the instruction immediately following the handler.</param>
            <exception cref="T:System.ArgumentException">A label was not defined by an instruction encoder this builder is associated with.</exception>
            <exception cref="T:System.ArgumentNullException">A label has default value.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddCatchRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Adds catch region.
            </summary>
            <param name="tryStart">Label marking the first instruction of the try block.</param>
            <param name="tryEnd">Label marking the instruction immediately following the try block.</param>
            <param name="handlerStart">Label marking the first instruction of the handler.</param>
            <param name="handlerEnd">Label marking the instruction immediately following the handler.</param>
            <param name="catchType">The type of exception to be caught: <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>.</param>
            <exception cref="T:System.ArgumentException">A label was not defined by an instruction encoder this builder is associated with.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="catchType"/> is not a valid type handle.</exception>
            <exception cref="T:System.ArgumentNullException">A label has default value.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.AddFilterRegion(System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle,System.Reflection.Metadata.Ecma335.LabelHandle)">
            <summary>
            Adds catch region.
            </summary>
            <param name="tryStart">Label marking the first instruction of the try block.</param>
            <param name="tryEnd">Label marking the instruction immediately following the try block.</param>
            <param name="handlerStart">Label marking the first instruction of the handler.</param>
            <param name="handlerEnd">Label marking the instruction immediately following the handler.</param>
            <param name="filterStart">Label marking the first instruction of the filter block.</param>
            <exception cref="T:System.ArgumentException">A label was not defined by an instruction encoder this builder is associated with.</exception>
            <exception cref="T:System.ArgumentNullException">A label has default value.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ControlFlowBuilder.CopyCodeAndFixupBranches(System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.BlobBuilder)">
            <exception cref="T:System.InvalidOperationException" />
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.Builder">
            <summary>
            The underlying builder.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.HasSmallFormat">
            <summary>
            True if the encoder uses small format.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.IsSmallRegionCount(System.Int32)">
            <summary>
            Returns true if the number of exception regions first small format.
            </summary>
            <param name="exceptionRegionCount">Number of exception regions.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.IsSmallExceptionRegion(System.Int32,System.Int32)">
            <summary>
            Returns true if the region fits small format.
            </summary>
            <param name="startOffset">Start offset of the region.</param>
            <param name="length">Length of the region.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddFinally(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adds a finally clause.
            </summary>
            <param name="tryOffset">Try block start offset.</param>
            <param name="tryLength">Try block length.</param>
            <param name="handlerOffset">Handler start offset.</param>
            <param name="handlerLength">Handler length.</param>
            <returns>Encoder for the next clause.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="tryOffset"/>, <paramref name="tryLength"/>, <paramref name="handlerOffset"/> or <paramref name="handlerLength"/> is out of range.
            </exception>
            <exception cref="T:System.InvalidOperationException">Method body was not declared to have exception regions.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddFault(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adds a fault clause.
            </summary>
            <param name="tryOffset">Try block start offset.</param>
            <param name="tryLength">Try block length.</param>
            <param name="handlerOffset">Handler start offset.</param>
            <param name="handlerLength">Handler length.</param>
            <returns>Encoder for the next clause.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="tryOffset"/>, <paramref name="tryLength"/>, <paramref name="handlerOffset"/> or <paramref name="handlerLength"/> is out of range.
            </exception>
            <exception cref="T:System.InvalidOperationException">Method body was not declared to have exception regions.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddCatch(System.Int32,System.Int32,System.Int32,System.Int32,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Adds a fault clause.
            </summary>
            <param name="tryOffset">Try block start offset.</param>
            <param name="tryLength">Try block length.</param>
            <param name="handlerOffset">Handler start offset.</param>
            <param name="handlerLength">Handler length.</param>
            <param name="catchType">
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>.
            </param>
            <returns>Encoder for the next clause.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="catchType"/> is invalid.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="tryOffset"/>, <paramref name="tryLength"/>, <paramref name="handlerOffset"/> or <paramref name="handlerLength"/> is out of range.
            </exception>
            <exception cref="T:System.InvalidOperationException">Method body was not declared to have exception regions.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.AddFilter(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adds a fault clause.
            </summary>
            <param name="tryOffset">Try block start offset.</param>
            <param name="tryLength">Try block length.</param>
            <param name="handlerOffset">Handler start offset.</param>
            <param name="handlerLength">Handler length.</param>
            <param name="filterOffset">Offset of the filter block.</param>
            <returns>Encoder for the next clause.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="tryOffset"/>, <paramref name="tryLength"/>, <paramref name="handlerOffset"/> or <paramref name="handlerLength"/> is out of range.
            </exception>
            <exception cref="T:System.InvalidOperationException">Method body was not declared to have exception regions.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExceptionRegionEncoder.Add(System.Reflection.Metadata.ExceptionRegionKind,System.Int32,System.Int32,System.Int32,System.Int32,System.Reflection.Metadata.EntityHandle,System.Int32)">
            <summary>
            Adds an exception clause.
            </summary>
            <param name="kind">Clause kind.</param>
            <param name="tryOffset">Try block start offset.</param>
            <param name="tryLength">Try block length.</param>
            <param name="handlerOffset">Handler start offset.</param>
            <param name="handlerLength">Handler length.</param>
            <param name="catchType">
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>, 
            or nil if <paramref name="kind"/> is not <see cref="F:System.Reflection.Metadata.ExceptionRegionKind.Catch"/>
            </param>
            <param name="filterOffset">
            Offset of the filter block, or 0 if the <paramref name="kind"/> is not <see cref="F:System.Reflection.Metadata.ExceptionRegionKind.Filter"/>.
            </param>
            <returns>Encoder for the next clause.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="catchType"/> is invalid.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="kind"/> has invalid value.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="tryOffset"/>, <paramref name="tryLength"/>, <paramref name="handlerOffset"/> or <paramref name="handlerLength"/> is out of range.
            </exception>
            <exception cref="T:System.InvalidOperationException">Method body was not declared to have exception regions.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.InstructionEncoder">
            <summary>
            Encodes instructions.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.CodeBuilder">
            <summary>
            Underlying builder where encoded instructions are written to.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.ControlFlowBuilder">
            <summary>
            Builder tracking labels, branches and exception handlers.
            </summary>
            <remarks>
            If null the encoder doesn't support constuction of control flow.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.#ctor(System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.Ecma335.ControlFlowBuilder)">
            <summary>
            Creates an encoder backed by code and control-flow builders.
            </summary>
            <param name="codeBuilder">Builder to write encoded instructions to.</param>
            <param name="controlFlowBuilder">
            Builder tracking labels, branches and exception handlers.
            Must be specified to be able to use some of the control-flow factory methods of <see cref="T:System.Reflection.Metadata.Ecma335.InstructionEncoder"/>,
            such as <see cref="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Branch(System.Reflection.Metadata.ILOpCode,System.Reflection.Metadata.Ecma335.LabelHandle)"/>, <see cref="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.DefineLabel"/>, <see cref="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.MarkLabel(System.Reflection.Metadata.Ecma335.LabelHandle)"/> etc.
            </param>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.Offset">
            <summary>
            Offset of the next encoded instruction.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.OpCode(System.Reflection.Metadata.ILOpCode)">
            <summary>
            Encodes specified op-code.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Token(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Encodes a token.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Token(System.Int32)">
            <summary>
            Encodes a token.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadString(System.Reflection.Metadata.UserStringHandle)">
            <summary>
            Encodes <code>ldstr</code> instruction and its operand.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Encodes <code>call</code> instruction and its operand.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.MethodDefinitionHandle)">
            <summary>
            Encodes <code>call</code> instruction and its operand.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.MethodSpecificationHandle)">
            <summary>
            Encodes <code>call</code> instruction and its operand.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Call(System.Reflection.Metadata.MemberReferenceHandle)">
            <summary>
            Encodes <code>call</code> instruction and its operand.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.CallIndirect(System.Reflection.Metadata.StandaloneSignatureHandle)">
            <summary>
            Encodes <code>calli</code> instruction and its operand.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantI4(System.Int32)">
            <summary>
            Encodes <see cref="T:System.Int32"/> constant load instruction.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantI8(System.Int64)">
            <summary>
            Encodes <see cref="T:System.Int64"/> constant load instruction.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantR4(System.Single)">
            <summary>
            Encodes <see cref="T:System.Single"/> constant load instruction.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadConstantR8(System.Double)">
            <summary>
            Encodes <see cref="T:System.Double"/> constant load instruction.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadLocal(System.Int32)">
            <summary>
            Encodes local variable load instruction.
            </summary>
            <param name="slotIndex">Index of the local variable slot.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="slotIndex"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.StoreLocal(System.Int32)">
            <summary>
            Encodes local variable store instruction.
            </summary>
            <param name="slotIndex">Index of the local variable slot.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="slotIndex"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadLocalAddress(System.Int32)">
            <summary>
            Encodes local variable address load instruction.
            </summary>
            <param name="slotIndex">Index of the local variable slot.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="slotIndex"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadArgument(System.Int32)">
            <summary>
            Encodes argument load instruction.
            </summary>
            <param name="argumentIndex">Index of the argument.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="argumentIndex"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.LoadArgumentAddress(System.Int32)">
            <summary>
            Encodes argument address load instruction.
            </summary>
            <param name="argumentIndex">Index of the argument.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="argumentIndex"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.StoreArgument(System.Int32)">
            <summary>
            Encodes argument store instruction.
            </summary>
            <param name="argumentIndex">Index of the argument.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="argumentIndex"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.DefineLabel">
            <summary>
            Defines a label that can later be used to mark and refer to a location in the instruction stream.
            </summary>
            <returns>Label handle.</returns>
            <exception cref="T:System.InvalidOperationException"><see cref="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.ControlFlowBuilder"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.Branch(System.Reflection.Metadata.ILOpCode,System.Reflection.Metadata.Ecma335.LabelHandle)">
            <summary>
            Encodes a branch instruction.
            </summary>
            <param name="code">Branch instruction to encode.</param>
            <param name="label">Label of the target location in instruction stream.</param>
            <exception cref="T:System.ArgumentException"><paramref name="code"/> is not a branch instruction.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="label"/> was not defined by this encoder.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.ControlFlowBuilder"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="label"/> has default value.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.InstructionEncoder.MarkLabel(System.Reflection.Metadata.Ecma335.LabelHandle)">
            <summary>
            Associates specified label with the current IL offset.
            </summary>
            <param name="label">Label to mark.</param>
            <remarks>
            A single label may be marked multiple times, the last offset wins.
            </remarks>
            <exception cref="T:System.InvalidOperationException"><see cref="P:System.Reflection.Metadata.Ecma335.InstructionEncoder.ControlFlowBuilder"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="label"/> was not defined by this encoder.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="label"/> has default value.</exception>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.LabelHandle.Id">
            <summary>
            1-based id identifying the label within the context of a <see cref="T:System.Reflection.Metadata.Ecma335.ControlFlowBuilder"/>.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasCustomAttribute(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a HasCustomAttribute coded index for the specified handle.
            </summary>
            <param name="handle">
            <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.ParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.InterfaceImplementationHandle"/>,
            <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle"/>,
            <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.EventDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.StandaloneSignatureHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>,
            <see cref="T:System.Reflection.Metadata.ExportedTypeHandle"/>,
            <see cref="T:System.Reflection.Metadata.ManifestResourceHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterConstraintHandle"/> or
            <see cref="T:System.Reflection.Metadata.MethodSpecificationHandle"/>.
            </param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasConstant(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a HasConstant coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.ParameterHandle"/>, <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>, or <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.CustomAttributeType(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a CustomAttributeType coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasDeclSecurity(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a HasDeclSecurity coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>, or <see cref="T:System.Reflection.Metadata.AssemblyDefinitionHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasFieldMarshal(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a HasFieldMarshal coded index for the specified handle.
            </summary>
            <param name="handle"><see cref = "T:System.Reflection.Metadata.ParameterHandle" /> or <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasSemantics(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a HasSemantics coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.EventDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.Implementation(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a Implementation coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>, <see cref="T:System.Reflection.Metadata.ExportedTypeHandle"/> or <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.MemberForwarded(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a MemberForwarded coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.FieldDefinition"/>, <see cref="T:System.Reflection.Metadata.MethodDefinition"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.MemberRefParent(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a MemberRefParent coded index for the specified handle.
            </summary>
            <param name="handle">
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, 
            <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>, 
            <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>, or 
            <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>.
            </param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.MethodDefOrRef(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a MethodDefOrRef coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.ResolutionScope(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a ResolutionScope coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.ModuleDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>, <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeDefOrRef(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a TypeDefOrRef coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeDefOrRefOrSpec(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a TypeDefOrRefOrSpec coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.TypeOrMethodDef(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a TypeOrMethodDef coded index for the specified handle.
            </summary>
            <param name="handle"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/></param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CodedIndex.HasCustomDebugInformation(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Calculates a HasCustomDebugInformation coded index for the specified handle.
            </summary>
            <param name="handle">
            <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.ParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.InterfaceImplementationHandle"/>,
            <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.DeclarativeSecurityAttributeHandle"/>,
            <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.EventDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.StandaloneSignatureHandle"/>,
            <see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyDefinitionHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>,
            <see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>,
            <see cref="T:System.Reflection.Metadata.ExportedTypeHandle"/>,
            <see cref="T:System.Reflection.Metadata.ManifestResourceHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterHandle"/>,
            <see cref="T:System.Reflection.Metadata.GenericParameterConstraintHandle"/>,
            <see cref="T:System.Reflection.Metadata.MethodSpecificationHandle"/>,
            <see cref="T:System.Reflection.Metadata.DocumentHandle"/>,
            <see cref="T:System.Reflection.Metadata.LocalScopeHandle"/>,
            <see cref="T:System.Reflection.Metadata.LocalVariableHandle"/>,
            <see cref="T:System.Reflection.Metadata.LocalConstantHandle"/> or
            <see cref="T:System.Reflection.Metadata.ImportScopeHandle"/>.
            </param>
            <exception cref="T:System.ArgumentException">Unexpected handle kind.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.PortablePdbBuilder">
            <summary>
            Builder of a Portable PDB image.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.#ctor(System.Reflection.Metadata.Ecma335.MetadataBuilder,System.Collections.Immutable.ImmutableArray{System.Int32},System.Reflection.Metadata.MethodDefinitionHandle,System.Func{System.Collections.Generic.IEnumerable{System.Reflection.Metadata.Blob},System.Reflection.Metadata.BlobContentId})">
            <summary>
            Creates a builder of a Portable PDB image.
            </summary>
            <param name="tablesAndHeaps">
            Builder populated with debug metadata entities stored in tables and values stored in heaps.
            The entities and values will be enumerated when serializing the Portable PDB image.
            </param>
            <param name="typeSystemRowCounts">
            Row counts of all tables that the associated type-system metadata contain.
            Each slot in the array corresponds to a table (<see cref="T:System.Reflection.Metadata.Ecma335.TableIndex"/>).
            The length of the array must be equal to <see cref="F:System.Reflection.Metadata.Ecma335.MetadataTokens.TableCount"/>.
            </param>
            <param name="entryPoint">
            Entry point method definition handle.
            </param>
            <param name="idProvider">
            Function calculating id of content represented as a sequence of blobs.
            If not specified a default function that ignores the content and returns current time-based content id is used 
            (<see cref="M:System.Reflection.Metadata.BlobContentId.GetTimeBasedProvider"/>).
            You must specify a deterministic function to produce a deterministic Portable PDB image.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="tablesAndHeaps"/> or <paramref name="typeSystemRowCounts"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.SerializeStandalonePdbStream(System.Reflection.Metadata.BlobBuilder)">
            <summary>
            Serialized #Pdb stream.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.PortablePdbBuilder.Serialize(System.Reflection.Metadata.BlobBuilder)">
            <summary>
            Serializes Portable PDB content into the given <see cref="T:System.Reflection.Metadata.BlobBuilder"/>.
            </summary>
            <param name="builder">Builder to write to.</param>
            <returns>The id of the serialized content.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="builder"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.FieldSignature">
            <summary>
            Encodes Field Signature blob.
            </summary>
            <returns>Encoder of the field type.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.MethodSpecificationSignature(System.Int32)">
            <summary>
            Encodes Method Specification Signature blob.
            </summary>
            <param name="genericArgumentCount">Number of generic arguments.</param>
            <returns>Encoder of generic arguments.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="genericArgumentCount"/> is not in range [0, 0xffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.MethodSignature(System.Reflection.Metadata.SignatureCallingConvention,System.Int32,System.Boolean)">
            <summary>
            Encodes Method Signature blob.
            </summary>
            <param name="convention">Calling convention.</param>
            <param name="genericParameterCount">Number of generic parameters.</param>
            <param name="isInstanceMethod">True to encode an instance method signature, false to encode a static method signature.</param>
            <returns>An Encoder of the rest of the signature including return value and parameters.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="genericParameterCount"/> is not in range [0, 0xffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.PropertySignature(System.Boolean)">
            <summary>
            Encodes Property Signature blob.
            </summary>
            <param name="isInstanceProperty">True to encode an instance property signature, false to encode a static property signature.</param>
            <returns>An Encoder of the rest of the signature including return value and parameters, which has the same structure as Method Signature.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.CustomAttributeSignature(System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder@,System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder@)">
            <summary>
            Encodes Custom Attribute Signature blob.
            Returns a pair of encoders that must be used in the order they appear in the parameter list.
            </summary>
            <param name="fixedArguments">Use first, to encode fixed arguments.</param>
            <param name="namedArguments">Use second, to encode named arguments.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.CustomAttributeSignature(System.Action{System.Reflection.Metadata.Ecma335.FixedArgumentsEncoder},System.Action{System.Reflection.Metadata.Ecma335.CustomAttributeNamedArgumentsEncoder})">
            <summary>
            Encodes Custom Attribute Signature blob.
            </summary>
            <param name="fixedArguments">Called first, to encode fixed arguments.</param>
            <param name="namedArguments">Called second, to encode named arguments.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="fixedArguments"/> or <paramref name="namedArguments"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.LocalVariableSignature(System.Int32)">
            <summary>
            Encodes Local Variable Signature.
            </summary>
            <param name="variableCount">Number of local variables.</param>
            <returns>Encoder of a sequence of local variables.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="variableCount"/> is not in range [0, 0x1fffffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.TypeSpecificationSignature">
            <summary>
            Encodes Type Specification Signature.
            </summary>
            <returns>
            Type encoder of the structured type represented by the Type Specification (it shall not encode a primitive type).
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.PermissionSetBlob(System.Int32)">
            <summary>
            Encodes a Permission Set blob.
            </summary>
            <param name="attributeCount">Number of attributes in the set.</param>
            <returns>Permission Set encoder.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="attributeCount"/> is not in range [0, 0x1fffffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.BlobEncoder.PermissionSetArguments(System.Int32)">
            <summary>
            Encodes Permission Set arguments.
            </summary>
            <param name="argumentCount">Number of arguments in the set.</param>
            <returns>Encoder of the arguments of the set.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder.Parameters(System.Int32,System.Reflection.Metadata.Ecma335.ReturnTypeEncoder@,System.Reflection.Metadata.Ecma335.ParametersEncoder@)">
            <summary>
            Encodes return type and parameters.
            Returns a pair of encoders that must be used in the order they appear in the parameter list.
            </summary>
            <param name="parameterCount">Number of parameters.</param>
            <param name="returnType">Use first, to encode the return types.</param>
            <param name="parameters">Use second, to encode the actual parameters.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MethodSignatureEncoder.Parameters(System.Int32,System.Action{System.Reflection.Metadata.Ecma335.ReturnTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.ParametersEncoder})">
            <summary>
            Encodes return type and parameters.
            </summary>
            <param name="parameterCount">Number of parameters.</param>
            <param name="returnType">Called first, to encode the return type.</param>
            <param name="parameters">Called second, to encode the actual parameters.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="returnType"/> or <paramref name="parameters"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedVector(System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder@,System.Reflection.Metadata.Ecma335.VectorEncoder@)">
            <summary>
            Encodes the type and the items of a vector literal.
            Returns a pair of encoders that must be used in the order they appear in the parameter list.
            </summary>        
            <param name="arrayType">Use first, to encode the type of the vector.</param>
            <param name="vector">Use second, to encode the items of the vector.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedVector(System.Action{System.Reflection.Metadata.Ecma335.CustomAttributeArrayTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.VectorEncoder})">
            <summary>
            Encodes the type and the items of a vector literal.
            </summary>
            <param name="arrayType">Called first, to encode the type of the vector.</param>
            <param name="vector">Called second, to encode the items of the vector.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="arrayType"/> or <paramref name="vector"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.Scalar">
            <summary>
            Encodes a scalar literal.
            </summary>
            <returns>Encoder of the literal value.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedScalar(System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder@,System.Reflection.Metadata.Ecma335.ScalarEncoder@)">
            <summary>
            Encodes the type and the value of a literal.
            Returns a pair of encoders that must be used in the order they appear in the parameter list.
            </summary>
            <param name="type">Called first, to encode the type of the literal.</param>
            <param name="scalar">Called second, to encode the value of the literal.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.LiteralEncoder.TaggedScalar(System.Action{System.Reflection.Metadata.Ecma335.CustomAttributeElementTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.ScalarEncoder})">
            <summary>
            Encodes the type and the value of a literal.
            </summary>
            <param name="type">Called first, to encode the type of the literal.</param>
            <param name="scalar">Called second, to encode the value of the literal.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="type"/> or <paramref name="scalar"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ScalarEncoder.NullArray">
            <summary>
            Encodes <c>null</c> literal of type <see cref="T:System.Array"/>.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ScalarEncoder.Constant(System.Object)">
            <summary>
            Encodes constant literal.
            </summary>
            <param name="value">
            Constant of type 
            <see cref="T:System.Boolean"/>,
            <see cref="T:System.Byte"/>,
            <see cref="T:System.SByte"/>,
            <see cref="T:System.Int16"/>,
            <see cref="T:System.UInt16"/>,
            <see cref="T:System.Int32"/>,
            <see cref="T:System.UInt32"/>,
            <see cref="T:System.Int64"/>,
            <see cref="T:System.UInt64"/>,
            <see cref="T:System.Single"/>,
            <see cref="T:System.Double"/>,
            <see cref="T:System.Char"/> (encoded as two-byte Unicode character),
            <see cref="T:System.String"/> (encoded as SerString), or
            <see cref="T:System.Enum"/> (encoded as the underlying integer value).
            </param>
            <exception cref="T:System.ArgumentException">Unexpected constant type.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ScalarEncoder.SystemType(System.String)">
            <summary>
            Encodes literal of type <see cref="T:System.Type"/> (possibly null).
            </summary>
            <param name="serializedTypeName">The name of the type, or null.</param>
            <exception cref="T:System.ArgumentException"><paramref name="serializedTypeName"/> is empty.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder.AddArgument(System.Boolean,System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder@,System.Reflection.Metadata.Ecma335.NameEncoder@,System.Reflection.Metadata.Ecma335.LiteralEncoder@)">
            <summary>
            Encodes a named argument (field or property).
            Returns a triplet of encoders that must be used in the order they appear in the parameter list.
            </summary>
            <param name="isField">True to encode a field, false to encode a property.</param>
            <param name="type">Use first, to encode the type of the argument.</param>
            <param name="name">Use second, to encode the name of the field or property.</param>
            <param name="literal">Use third, to encode the literal value of the argument.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamedArgumentsEncoder.AddArgument(System.Boolean,System.Action{System.Reflection.Metadata.Ecma335.NamedArgumentTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.NameEncoder},System.Action{System.Reflection.Metadata.Ecma335.LiteralEncoder})">
            <summary>
            Encodes a named argument (field or property).
            </summary>
            <param name="isField">True to encode a field, false to encode a property.</param>
            <param name="type">Called first, to encode the type of the argument.</param>
            <param name="name">Called second, to encode the name of the field or property.</param>
            <param name="literal">Called third, to encode the literal value of the argument.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="type"/>, <paramref name="name"/> or <paramref name="literal"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.PrimitiveType(System.Reflection.Metadata.PrimitiveTypeCode)">
            <summary>
            Writes primitive type code.
            </summary>
            <param name="type">Any primitive type code except for <see cref="F:System.Reflection.Metadata.PrimitiveTypeCode.TypedReference"/> and <see cref="F:System.Reflection.Metadata.PrimitiveTypeCode.Void"/>.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="type"/> is not valid in this context.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Array(System.Reflection.Metadata.Ecma335.SignatureTypeEncoder@,System.Reflection.Metadata.Ecma335.ArrayShapeEncoder@)">
            <summary>
            Encodes an array type.
            Returns a pair of encoders that must be used in the order they appear in the parameter list.
            </summary>
            <param name="elementType">Use first, to encode the type of the element.</param>
            <param name="arrayShape">Use second, to encode the shape of the array.</param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Array(System.Action{System.Reflection.Metadata.Ecma335.SignatureTypeEncoder},System.Action{System.Reflection.Metadata.Ecma335.ArrayShapeEncoder})">
            <summary>
            Encodes an array type.
            </summary>
            <param name="elementType">Called first, to encode the type of the element.</param>
            <param name="arrayShape">Called second, to encode the shape of the array.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="elementType"/> or <paramref name="arrayShape"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Type(System.Reflection.Metadata.EntityHandle,System.Boolean)">
            <summary>
            Encodes a reference to a type.
            </summary>
            <param name="type"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>.</param>
            <param name="isValueType">True to mark the type as value type, false to mark it as a reference type in the signature.</param>
            <exception cref="T:System.ArgumentException"><paramref name="type"/> doesn't have the expected handle kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.FunctionPointer(System.Reflection.Metadata.SignatureCallingConvention,System.Reflection.Metadata.Ecma335.FunctionPointerAttributes,System.Int32)">
            <summary>
            Starts a function pointer signature.
            </summary>
            <param name="convention">Calling convention.</param>
            <param name="attributes">Function pointer attributes.</param>
            <param name="genericParameterCount">Generic parameter count.</param>
            <exception cref="T:System.ArgumentException"><paramref name="attributes"/> is invalid.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="genericParameterCount"/> is not in range [0, 0xffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.GenericInstantiation(System.Reflection.Metadata.EntityHandle,System.Int32,System.Boolean)">
            <summary>
            Starts a generic instantiation signature.
            </summary>
            <param name="genericType"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>.</param>
            <param name="genericArgumentCount">Generic argument count.</param>
            <param name="isValueType">True to mark the type as value type, false to mark it as a reference type in the signature.</param>
            <exception cref="T:System.ArgumentException"><paramref name="genericType"/> doesn't have the expected handle kind.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="genericArgumentCount"/> is not in range [1, 0xffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.GenericMethodTypeParameter(System.Int32)">
            <summary>
            Encodes a reference to type parameter of a containing generic method.
            </summary>
            <param name="parameterIndex">Parameter index.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="parameterIndex"/> is not in range [0, 0xffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.GenericTypeParameter(System.Int32)">
            <summary>
            Encodes a reference to type parameter of a containing generic type.
            </summary>
            <param name="parameterIndex">Parameter index.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="parameterIndex"/> is not in range [0, 0xffff].</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.Pointer">
            <summary>
            Starts pointer signature.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.VoidPointer">
            <summary>
            Encodes <code>void*</code>.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.SZArray">
            <summary>
            Starts SZ array (vector) signature.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureTypeEncoder.CustomModifiers">
            <summary>
            Starts a signature of a type with custom modifiers.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.CustomModifiersEncoder.AddModifier(System.Reflection.Metadata.EntityHandle,System.Boolean)">
            <summary>
            Encodes a custom modifier.
            </summary>
            <param name="type"><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>.</param>
            <param name="isOptional">Is optional modifier.</param>
            <returns>Encoder of subsequent modifiers.</returns>
            <exception cref="T:System.ArgumentException"><paramref name="type"/> is nil or of an unexpected kind.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ArrayShapeEncoder.Shape(System.Int32,System.Collections.Immutable.ImmutableArray{System.Int32},System.Collections.Immutable.ImmutableArray{System.Int32})">
            <summary>
            Encodes array shape.
            </summary>
            <param name="rank">The number of dimensions in the array (shall be 1 or more).</param>
            <param name="sizes">
            Dimension sizes. The array may be shorter than <paramref name="rank"/> but not longer.
            </param>
            <param name="lowerBounds">
            Dimension lower bounds, or <c>default(<see cref="T:System.Collections.Immutable.ImmutableArray`1"/>)</c> to set all <paramref name="rank"/> lower bounds to 0. 
            The array may be shorter than <paramref name="rank"/> but not longer.
            </param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="rank"/> is outside of range [1, 0xffff],
            smaller than <paramref name="sizes"/>.Length, or
            smaller than <paramref name="lowerBounds"/>.Length.
            </exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="sizes"/> is null.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.MetadataSizes">
            <summary>
            Provides information on sizes of various metadata structures.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.HeapSizes">
            <summary>
            Exact (unaligned) heap sizes.
            </summary>
            <remarks>Use <see cref="M:System.Reflection.Metadata.Ecma335.MetadataSizes.GetAlignedHeapSize(System.Reflection.Metadata.Ecma335.HeapIndex)"/> to get an aligned heap size.</remarks>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.RowCounts">
            <summary>
            Table row counts. 
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.ExternalRowCounts">
            <summary>
            External table row counts. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.MetadataSizes.PresentTablesMask">
            <summary>
            Non-empty tables that are emitted into the metadata table stream.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.MetadataSizes.ExternalTablesMask">
            <summary>
            Non-empty tables stored in an external metadata table stream that might be referenced from the metadata table stream being emitted.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.MetadataSizes.MetadataStreamStorageSize">
            <summary>
            Overall size of metadata stream storage (stream headers, table stream, heaps, additional streams).
            Aligned to <see cref="F:System.Reflection.Metadata.Ecma335.MetadataSizes.StreamAlignment"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.MetadataSizes.MetadataTableStreamSize">
            <summary>
            The size of metadata stream (#- or #~). Aligned.
            Aligned to <see cref="F:System.Reflection.Metadata.Ecma335.MetadataSizes.StreamAlignment"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.MetadataSizes.StandalonePdbStreamSize">
            <summary>
            The size of #Pdb stream. Aligned.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.MetadataHeaderSize">
            <summary>
            Metadata header size.
            Includes:
            - metadata storage signature
            - storage header
            - stream headers
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.MetadataSizes.MetadataSize">
            <summary>
            Total size of metadata (header and all streams).
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataSizes.GetAlignedHeapSize(System.Reflection.Metadata.Ecma335.HeapIndex)">
            <summary>
            Returns aligned size of the specified heap.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.SignatureDecoder`2">
            <summary>
            Decodes signature blobs.
            See Metadata Specification section II.23.2: Blobs and signatures.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.#ctor(System.Reflection.Metadata.ISignatureTypeProvider{`0,`1},System.Reflection.Metadata.MetadataReader,`1)">
            <summary>
            Creates a new SignatureDecoder.
            </summary>
            <param name="provider">The provider used to obtain type symbols as the signature is decoded.</param>
            <param name="metadataReader">
            The metadata reader from which the signature was obtained. It may be null if the given provider allows it.
            </param>
            <param name="genericContext">
            Additional context needed to resolve generic parameters.
            </param>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeType(System.Reflection.Metadata.BlobReader@,System.Boolean)">
            <summary>
            Decodes a type embedded in a signature and advances the reader past the type.
            </summary>
            <param name="blobReader">The blob reader positioned at the leading SignatureTypeCode</param>
            <param name="allowTypeSpecifications">Allow a <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/> to follow a (CLASS | VALUETYPE) in the signature.
            At present, the only context where that would be valid is in a LocalConstantSig as defined by the Portable PDB specification.
            </param>
            <returns>The decoded type.</returns>
            <exception cref="T:System.BadImageFormatException">The reader was not positioned at a valid signature type.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeTypeSequence(System.Reflection.Metadata.BlobReader@)">
            <summary> 
            Decodes a list of types, with at least one instance that is preceded by its count as a compressed integer.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeMethodSignature(System.Reflection.Metadata.BlobReader@)">
            <summary>
            Decodes a method (definition, reference, or standalone) or property signature blob.
            </summary>
            <param name="blobReader">BlobReader positioned at a method signature.</param>
            <returns>The decoded method signature.</returns>
            <exception cref="T:System.BadImageFormatException">The method signature is invalid.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeMethodSpecificationSignature(System.Reflection.Metadata.BlobReader@)">
            <summary>
            Decodes a method specification signature blob and advances the reader past the signature.
            </summary>
            <param name="blobReader">A BlobReader positioned at a valid method specification signature.</param>
            <returns>The types used to instantiate a generic method via the method specification.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeLocalSignature(System.Reflection.Metadata.BlobReader@)">
            <summary>
            Decodes a local variable signature blob and advances the reader past the signature.
            </summary>
            <param name="blobReader">The blob reader positioned at a local variable signature.</param>
            <returns>The local variable types.</returns>
            <exception cref="T:System.BadImageFormatException">The local variable signature is invalid.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.SignatureDecoder`2.DecodeFieldSignature(System.Reflection.Metadata.BlobReader@)">
            <summary>
            Decodes a field signature blob and advances the reader past the signature.
            </summary>
            <param name="blobReader">The blob reader positioned at a field signature.</param>
            <returns>The decoded field type.</returns>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.ExportedTypeExtensions">
            <summary>
            Provides an extension method to access the TypeDefinitionId column of the ExportedType table.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.ExportedTypeExtensions.GetTypeDefinitionId(System.Reflection.Metadata.ExportedType)">
            <summary>
            Gets a hint at the likely row number of the target type in the TypeDef table of its module. 
            If the namespaces and names do not match, resolution falls back to a full search of the 
            target TypeDef table. Ignored and should be zero if <see cref="P:System.Reflection.Metadata.ExportedType.IsForwarder"/> is
            true.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions">
            <summary>
            Provides extension methods for working with certain raw elements of the ECMA-335 metadata tables and heaps.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTableRowCount(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.TableIndex)">
            <summary>
            Returns the number of rows in the specified table.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="tableIndex"/> is not a valid table index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTableRowSize(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.TableIndex)">
            <summary>
            Returns the size of a row in the specified table.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="tableIndex"/> is not a valid table index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTableMetadataOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.TableIndex)">
            <summary>
            Returns the offset from the start of metadata to the specified table.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="tableIndex"/> is not a valid table index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetHeapSize(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.HeapIndex)">
            <summary>
            Returns the size of the specified heap.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="heapIndex"/> is not a valid heap index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetHeapMetadataOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.HeapIndex)">
            <summary>
            Returns the offset from the start of metadata to the specified heap.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="heapIndex"/> is not a valid heap index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetMetadataBlock(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Ecma335.HeapIndex)">
            <summary>
            Returns the size of the specified heap.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="heapIndex"/> is not a valid heap index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetNextHandle(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.UserStringHandle)">
            <summary>
            Returns the a handle to the UserString that follows the given one in the UserString heap or a nil handle if it is the last one.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetNextHandle(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.BlobHandle)">
            <summary>
            Returns the a handle to the Blob that follows the given one in the Blob heap or a nil handle if it is the last one.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetNextHandle(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.StringHandle)">
            <summary>
            Returns the a handle to the String that follows the given one in the String heap or a nil handle if it is the last one.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetEditAndContinueLogEntries(System.Reflection.Metadata.MetadataReader)">
            <summary>
            Enumerates entries of EnC log.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception> 
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetEditAndContinueMapEntries(System.Reflection.Metadata.MetadataReader)">
            <summary>
            Enumerates entries of EnC map.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="reader"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTypesWithProperties(System.Reflection.Metadata.MetadataReader)">
            <summary>
            Enumerate types that define one or more properties. 
            </summary>
            <returns>
            The resulting sequence corresponds exactly to entries in PropertyMap table, 
            i.e. n-th returned <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> is stored in n-th row of PropertyMap.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.GetTypesWithEvents(System.Reflection.Metadata.MetadataReader)">
            <summary>
            Enumerate types that define one or more events. 
            </summary>
            <returns>
            The resulting sequence corresponds exactly to entries in EventMap table, 
            i.e. n-th returned <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> is stored in n-th row of EventMap.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)">
            <summary>
            Given a type handle and a raw type kind found in a signature blob determines whether the target type is a value type or a reference type.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.MetadataTokens.TableCount">
            <summary>
            Maximum number of tables that can be present in Ecma335 metadata.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.MetadataTokens.HeapCount">
            <summary>
            Maximum number of tables that can be present in Ecma335 metadata.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetRowNumber(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Returns the row number of a metadata table entry that corresponds 
            to the specified <paramref name="handle"/> in the context of <paramref name="reader"/>.
            </summary>
            <returns>One based row number.</returns>
            <exception cref="T:System.ArgumentException">The <paramref name="handle"/> is not a valid metadata table handle.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)">
            <summary>
            Returns the offset of metadata heap data that corresponds 
            to the specified <paramref name="handle"/> in the context of <paramref name="reader"/>.
            </summary>
            <returns>Zero based offset, or -1 if <paramref name="handle"/> isn't a metadata heap handle.</returns>
            <exception cref="T:System.NotSupportedException">The operation is not supported for the specified <paramref name="handle"/>.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="handle"/> is invalid.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Returns the metadata token of the specified <paramref name="handle"/> in the context of <paramref name="reader"/>.
            </summary>
            <returns>Metadata token.</returns>
            <exception cref="T:System.NotSupportedException">The operation is not supported for the specified <paramref name="handle"/>.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)">
            <summary>
            Returns the metadata token of the specified <paramref name="handle"/> in the context of <paramref name="reader"/>.
            </summary>
            <returns>Metadata token.</returns>
            <exception cref="T:System.ArgumentException">
            Handle represents a metadata entity that doesn't have a token.
            A token can only be retrieved for a metadata table handle or a heap handle of type <see cref="F:System.Reflection.Metadata.HandleKind.UserString"/>.
            </exception>
            <exception cref="T:System.NotSupportedException">The operation is not supported for the specified <paramref name="handle"/>.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetRowNumber(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Returns the row number of a metadata table entry that corresponds 
            to the specified <paramref name="handle"/>.
            </summary>
            <returns>
            One based row number, or -1 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetRowNumber(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle)"/>.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.Handle)">
            <summary>
            Returns the offset of metadata heap data that corresponds 
            to the specified <paramref name="handle"/>.
            </summary>
            <returns>
            An offset in the corresponding heap, or -1 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/> or <see cref="T:System.Reflection.Metadata.Ecma335.MetadataBuilder"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)"/>.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.BlobHandle)">
            <summary>
            Returns the offset of metadata heap data that corresponds 
            to the specified <paramref name="handle"/>.
            </summary>
            <returns>
            Zero based offset, or -1 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/> or <see cref="T:System.Reflection.Metadata.Ecma335.MetadataBuilder"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)"/>.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.GuidHandle)">
            <summary>
            Returns the offset of metadata heap data that corresponds 
            to the specified <paramref name="handle"/>.
            </summary>
            <returns>
            Zero based offset, or -1 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/> or <see cref="T:System.Reflection.Metadata.Ecma335.MetadataBuilder"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)"/>.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.UserStringHandle)">
            <summary>
            Returns the offset of metadata heap data that corresponds 
            to the specified <paramref name="handle"/>.
            </summary>
            <returns>
            Zero based offset, or -1 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/> or <see cref="T:System.Reflection.Metadata.Ecma335.MetadataBuilder"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)"/>.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.StringHandle)">
            <summary>
            Returns the offset of metadata heap data that corresponds 
            to the specified <paramref name="handle"/>.
            </summary>
            <returns>
            Zero based offset, or -1 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/> or <see cref="T:System.Reflection.Metadata.Ecma335.MetadataBuilder"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetHeapOffset(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)"/>.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.Handle)">
            <summary>
            Returns the metadata token of the specified <paramref name="handle"/>.
            </summary>
            <returns>
            Metadata token, or 0 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.Handle)"/>.
            </returns>
            <exception cref="T:System.ArgumentException">
            Handle represents a metadata entity that doesn't have a token.
            A token can only be retrieved for a metadata table handle or a heap handle of type <see cref="F:System.Reflection.Metadata.HandleKind.UserString"/>.
            </exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.EntityHandle)">
            <summary>
            Returns the metadata token of the specified <paramref name="handle"/>.
            </summary>
            <returns>
            Metadata token, or 0 if <paramref name="handle"/> can only be interpreted in a context of a specific <see cref="T:System.Reflection.Metadata.MetadataReader"/>.
            See <see cref="M:System.Reflection.Metadata.Ecma335.MetadataTokens.GetToken(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle)"/>.
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.TryGetTableIndex(System.Reflection.Metadata.HandleKind,System.Reflection.Metadata.Ecma335.TableIndex@)">
            <summary>
            Gets the <see cref="T:System.Reflection.Metadata.Ecma335.TableIndex"/> of the table corresponding to the specified <see cref="T:System.Reflection.Metadata.HandleKind"/>.
            </summary>
            <param name="type">Handle type.</param>
            <param name="index">Table index.</param>
            <returns>True if the handle type corresponds to an Ecma335 or Portable PDB table, false otherwise.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.TryGetHeapIndex(System.Reflection.Metadata.HandleKind,System.Reflection.Metadata.Ecma335.HeapIndex@)">
            <summary>
            Gets the <see cref="T:System.Reflection.Metadata.Ecma335.HeapIndex"/> of the heap corresponding to the specified <see cref="T:System.Reflection.Metadata.HandleKind"/>.
            </summary>
            <param name="type">Handle type.</param>
            <param name="index">Heap index.</param>
            <returns>True if the handle type corresponds to an Ecma335 heap, false otherwise.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.Handle(System.Int32)">
            <summary>
            Creates a handle from a token value.
            </summary>
            <exception cref="T:System.ArgumentException">
            <paramref name="token"/> is not a valid metadata token.
            It must encode a metadata table entity or an offset in <see cref="F:System.Reflection.Metadata.HandleKind.UserString"/> heap.
            </exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.EntityHandle(System.Int32)">
            <summary>
            Creates an entity handle from a token value.
            </summary>
            <exception cref="T:System.ArgumentException"><paramref name="token"/> is not a valid metadata entity token.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.EntityHandle(System.Reflection.Metadata.Ecma335.TableIndex,System.Int32)">
            <summary>
            Creates an <see cref="T:System.Reflection.Metadata.EntityHandle"/> from a token value.
            </summary>
            <exception cref="T:System.ArgumentException">
            <paramref name="tableIndex"/> is not a valid table index.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.MetadataTokens.Handle(System.Reflection.Metadata.Ecma335.TableIndex,System.Int32)">
            <summary>
            Creates an <see cref="T:System.Reflection.Metadata.EntityHandle"/> from a token value.
            </summary>
            <exception cref="T:System.ArgumentException">
            <paramref name="tableIndex"/> is not a valid table index.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.HandleType">
            <summary>
            These constants are all in the byte range and apply to the interpretation of <see cref="P:System.Reflection.Metadata.Handle.VType"/>,
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.HandleType.VirtualBit">
            <summary>
            Use the highest bit to mark tokens that are virtual (synthesized).
            We create virtual tokens to represent projected WinMD entities. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.HandleType.NonVirtualStringTypeMask">
            <summary>
            In the case of string handles, the two lower bits that (in addition to the 
            virtual bit not included in this mask) encode how to obtain the string value.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.TokenTypeIds.VirtualBit">
            <summary>
            Use the highest bit to mark tokens that are virtual (synthesized).
            We create virtual tokens to represent projected WinMD entities. 
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.TokenTypeIds.IsEntityOrUserStringToken(System.UInt32)">
            <summary>
            Returns true if the token value can escape the metadata reader.
            We don't allow virtual tokens and heap tokens other than UserString to escape 
            since the token type ids are internal to the reader and not specified by ECMA spec.
            
            Spec (Partition III, 1.9 Metadata tokens):
            Many CIL instructions are followed by a "metadata token". This is a 4-byte value, that specifies a row in a
            metadata table, or a starting byte offset in the User String heap. 
            
            For example, a value of 0x02 specifies the TypeDef table; a value of 0x70 specifies the User
            String heap.The value corresponds to the number assigned to that metadata table (see Partition II for the full
            list of tables) or to 0x70 for the User String heap.The least-significant 3 bytes specify the target row within that
            metadata table, or starting byte offset within the User String heap.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Ecma335.NamespaceCache.CacheIsRealized">
            <summary>
            Returns whether the namespaceTable has been created. If it hasn't, calling a GetXXX method
            on this will probably have a very high amount of overhead.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.GetSimpleName(System.Reflection.Metadata.NamespaceDefinitionHandle,System.Int32)">
             <summary>
             This will return a StringHandle for the simple name of a namespace name at the given segment index.
             If no segment index is passed explicitly or the "segment" index is greater than or equal to the number
             of segments, then the last segment is used. "Segment" in this context refers to part of a namespace
             name between dots.
            
             Example: Given a NamespaceDefinitionHandle to "System.Collections.Generic.Test" called 'handle':
            
               reader.GetString(GetSimpleName(handle)) == "Test"
               reader.GetString(GetSimpleName(handle, 0)) == "System"
               reader.GetString(GetSimpleName(handle, 1)) == "Collections"
               reader.GetString(GetSimpleName(handle, 2)) == "Generic"
               reader.GetString(GetSimpleName(handle, 3)) == "Test"
               reader.GetString(GetSimpleName(handle, 1000)) == "Test"
             </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.PopulateNamespaceTable">
            <summary>
            Two distinct namespace handles represent the same namespace if their full names are the same. This
            method merges builders corresponding to such namespace handles.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.MergeDuplicateNamespaces(System.Collections.Generic.Dictionary{System.Reflection.Metadata.NamespaceDefinitionHandle,System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder},System.Collections.Generic.Dictionary{System.String,System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder}@)">
            <summary>
            This will take 'table' and merge all of the NamespaceData instances that point to the same
            namespace. It has to create 'stringTable' as an intermediate dictionary, so it will hand it
            back to the caller should the caller want to use it.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.SynthesizeNamespaceData(System.String,System.Reflection.Metadata.NamespaceDefinitionHandle)">
            <summary>
            Creates a NamespaceDataBuilder instance that contains a synthesized NamespaceDefinitionHandle, 
            as well as the name provided.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.LinkChildDataToParentData(System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder,System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder)">
            <summary>
            Quick convenience method that handles linking together child + parent
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.LinkChildToParentNamespace(System.Collections.Generic.Dictionary{System.String,System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder},System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder,System.Collections.Generic.List{System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder}@)">
            <summary>
            Links a child to its parent namespace. If the parent namespace doesn't exist, this will create a
            virtual one. This will automatically link any virtual namespaces it creates up to its parents.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.ResolveParentChildRelationships(System.Collections.Generic.Dictionary{System.String,System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder},System.Collections.Generic.List{System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder}@)">
            <summary>
            This will link all parents/children in the given namespaces dictionary up to each other.
            
            In some cases, we need to synthesize namespaces that do not have any type definitions or forwarders
            of their own, but do have child namespaces. These are returned via the virtualNamespaces out
            parameter.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.PopulateTableWithTypeDefinitions(System.Collections.Generic.Dictionary{System.Reflection.Metadata.NamespaceDefinitionHandle,System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder})">
            <summary>
            Loops through all type definitions in metadata, adding them to the given table
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.PopulateTableWithExportedTypes(System.Collections.Generic.Dictionary{System.Reflection.Metadata.NamespaceDefinitionHandle,System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder})">
            <summary>
            Loops through all type forwarders in metadata, adding them to the given table
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.PopulateNamespaceList">
            <summary>
            Populates namespaceList with distinct namespaces. No ordering is guaranteed.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.EnsureNamespaceTableIsPopulated">
            <summary>
            If the namespace table doesn't exist, populates it!
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.EnsureNamespaceListIsPopulated">
            <summary>
            If the namespace list doesn't exist, populates it!
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder">
            <summary>
            An intermediate class used to build NamespaceData instances. This was created because we wanted to
            use ImmutableArrays in NamespaceData, but having ArrayBuilders and ImmutableArrays that served the
            same purpose in NamespaceData got ugly. With the current design of how we create our Namespace
            dictionary, this needs to be a class because we have a many-to-one mapping between NamespaceHandles
            and NamespaceData. So, the pointer semantics must be preserved.
            
            This class assumes that the builders will not be modified in any way after the first call to
            Freeze().
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.NamespaceCache.NamespaceDataBuilder.Freeze">
            <summary>
            Returns a NamespaceData that represents this NamespaceDataBuilder instance. After calling
            this method, it is an error to use any methods or fields except Freeze() on the target 
            NamespaceDataBuilder.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.Ecma335.FieldLayoutTableReader.FindFieldLayoutRowId(System.Reflection.Metadata.FieldDefinitionHandle)">
            <summary>
            Returns field offset for given field RowId, or -1 if not available. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.Ecma335.AssemblyRefTableReader.NumberOfNonVirtualRows">
            <summary>
            In CLI metadata equal to the actual number of entries in AssemblyRef table.
            In WinMD metadata it includes synthesized AssemblyRefs in addition.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.EntityHandle">
            <summary>
            Represents a metadata entity (type reference/definition/specification, method definition, custom attribute, etc.). 
            </summary>
            <remarks>
            Use <see cref="T:System.Reflection.Metadata.EntityHandle"/> to store multiple kinds of entity handles.
            It has smaller memory footprint than <see cref="T:System.Reflection.Metadata.Handle"/>.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.EntityHandle.SpecificHandleValue">
            <summary>
            Value stored in a specific entity handle (see <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>, etc.).
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.ContentEquals(System.Reflection.Metadata.BlobWriter)">
            <summary>
            Compares the current content of this writer with another one.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.ToArray(System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the buffer content.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.ToImmutableArray(System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the buffer content.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte,System.Int32)">
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte*,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Reflection.Metadata.BlobBuilder)">
            <exception cref="T:System.ArgumentNullException"><paramref name="source"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.IO.Stream,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="source"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte},System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the <paramref name="buffer"/>.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte[])">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteBytes(System.Byte[],System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the <paramref name="buffer"/>.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteReference(System.Int32,System.Boolean)">
            <summary>
            Writes a reference to a heap (heap offset) or a table (row number).
            </summary>
            <param name="reference">Heap offset or table row number.</param>
            <param name="isSmall">True to encode the reference as 16-bit integer, false to encode as 32-bit integer.</param>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteUTF16(System.Char[])">
            <summary>
            Writes UTF16 (little-endian) encoded string at the current position.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteUTF16(System.String)">
            <summary>
            Writes UTF16 (little-endian) encoded string at the current position.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteSerializedString(System.String)">
            <summary>
            Writes string in SerString format (see ECMA-335-II 23.3 Custom attributes).
            </summary>
            <remarks>
            The string is UTF8 encoded and prefixed by the its size in bytes. 
            Null string is represented as a single byte 0xFF.
            </remarks>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteUserString(System.String)">
            <summary>
            Writes string in User String (#US) heap format (see ECMA-335-II 24.2.4 #US and #Blob heaps):
            </summary>
            <remarks>
            The string is UTF16 encoded and prefixed by the its size in bytes.
            
            This final byte holds the value 1 if and only if any UTF16 character within the string has any bit set in its top byte,
            or its low byte is any of the following: 0x01–0x08, 0x0E–0x1F, 0x27, 0x2D, 0x7F. Otherwise, it holds 0. 
            The 1 signifies Unicode characters that require handling beyond that normally provided for 8-bit encoding sets. 
            </remarks>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteUTF8(System.String,System.Boolean)">
            <summary>
            Writes UTF8 encoded string at the current position.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteCompressedSignedInteger(System.Int32)">
            <summary>
            Implements compressed signed integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.
            </summary>
            <remarks>
            If the value lies between -64 (0xFFFFFFC0) and 63 (0x3F), inclusive, encode as a one-byte integer: 
            bit 7 clear, value bits 5 through 0 held in bits 6 through 1, sign bit (value bit 31) in bit 0.
            
            If the value lies between -8192 (0xFFFFE000) and 8191 (0x1FFF), inclusive, encode as a two-byte integer: 
            15 set, bit 14 clear, value bits 12 through 0 held in bits 13 through 1, sign bit(value bit 31) in bit 0.
            
            If the value lies between -268435456 (0xF000000) and 268435455 (0x0FFFFFFF), inclusive, encode as a four-byte integer: 
            31 set, 30 set, bit 29 clear, value bits 27 through 0 held in bits 28 through 1, sign bit(value bit 31) in bit 0.
            </remarks>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="value"/> can't be represented as a compressed signed integer.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteCompressedInteger(System.Int32)">
            <summary>
            Implements compressed unsigned integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.
            </summary>
            <remarks>
            If the value lies between 0 (0x00) and 127 (0x7F), inclusive, 
            encode as a one-byte integer (bit 7 is clear, value held in bits 6 through 0).
            
            If the value lies between 28 (0x80) and 214 – 1 (0x3FFF), inclusive, 
            encode as a 2-byte integer with bit 15 set, bit 14 clear(value held in bits 13 through 0).
            
            Otherwise, encode as a 4-byte integer, with bit 31 set, bit 30 set, bit 29 clear (value held in bits 28 through 0).
            </remarks>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="value"/> can't be represented as a compressed unsigned integer.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobWriter.WriteConstant(System.Object)">
            <summary>
            Writes a constant value (see ECMA-335 Partition II section 22.9) at the current position.
            </summary>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> is not of a constant type.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.GetBlobs">
            <summary>
            Returns a sequence of all blobs that represent the content of the builder.
            </summary>
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.ContentEquals(System.Reflection.Metadata.BlobBuilder)">
            <summary>
            Compares the current content of this writer with another one.
            </summary>
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.ToArray">
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.ToArray(System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the buffer content.</exception>
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.ToImmutableArray">
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.ToImmutableArray(System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the buffer content.</exception>
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteContentTo(System.IO.Stream)">
            <exception cref="T:System.ArgumentNullException"><paramref name="destination"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteContentTo(System.Reflection.Metadata.BlobWriter@)">
            <exception cref="T:System.ArgumentNullException"><paramref name="destination"/> is default(<see cref="T:System.Reflection.Metadata.BlobWriter"/>).</exception>
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteContentTo(System.Reflection.Metadata.BlobBuilder)">
            <exception cref="T:System.ArgumentNullException"><paramref name="destination"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Content is not available, the builder has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.LinkPrefix(System.Reflection.Metadata.BlobBuilder)">
            <exception cref="T:System.ArgumentNullException"><paramref name="prefix"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.LinkSuffix(System.Reflection.Metadata.BlobBuilder)">
            <exception cref="T:System.ArgumentNullException"><paramref name="suffix"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.ReserveBytes(System.Int32)">
            <summary>
            Reserves a contiguous block of bytes.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte,System.Int32)">
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte*,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.TryWriteBytes(System.IO.Stream,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="source"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="byteCount"/> is negative.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
            <returns>Bytes successfully written from the <paramref name="source" />.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Collections.Immutable.ImmutableArray{System.Byte},System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the <paramref name="buffer"/>.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte[])">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBytes(System.Byte[],System.Int32,System.Int32)">
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Range specified by <paramref name="start"/> and <paramref name="byteCount"/> falls outside of the bounds of the <paramref name="buffer"/>.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.PadTo(System.Int32)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.Align(System.Int32)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteBoolean(System.Boolean)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteByte(System.Byte)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteSByte(System.SByte)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteDouble(System.Double)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteSingle(System.Single)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt16(System.Int16)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt16(System.UInt16)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt16BE(System.Int16)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt16BE(System.UInt16)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt32BE(System.Int32)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt32BE(System.UInt32)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt32(System.Int32)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt32(System.UInt32)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteInt64(System.Int64)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUInt64(System.UInt64)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteDecimal(System.Decimal)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteGuid(System.Guid)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteDateTime(System.DateTime)">
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteReference(System.Int32,System.Boolean)">
            <summary>
            Writes a reference to a heap (heap offset) or a table (row number).
            </summary>
            <param name="reference">Heap offset or table row number.</param>
            <param name="isSmall">True to encode the reference as 16-bit integer, false to encode as 32-bit integer.</param>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUTF16(System.Char[])">
            <summary>
            Writes UTF16 (little-endian) encoded string at the current position.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUTF16(System.String)">
            <summary>
            Writes UTF16 (little-endian) encoded string at the current position.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteSerializedString(System.String)">
            <summary>
            Writes string in SerString format (see ECMA-335-II 23.3 Custom attributes).
            </summary>
            <remarks>
            The string is UTF8 encoded and prefixed by the its size in bytes. 
            Null string is represented as a single byte 0xFF.
            </remarks>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUserString(System.String)">
            <summary>
            Writes string in User String (#US) heap format (see ECMA-335-II 24.2.4 #US and #Blob heaps):
            </summary>
            <remarks>
            The string is UTF16 encoded and prefixed by the its size in bytes.
            
            This final byte holds the value 1 if and only if any UTF16 character within the string has any bit set in its top byte,
            or its low byte is any of the following: 0x01–0x08, 0x0E–0x1F, 0x27, 0x2D, 0x7F. Otherwise, it holds 0. 
            The 1 signifies Unicode characters that require handling beyond that normally provided for 8-bit encoding sets. 
            </remarks>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteUTF8(System.String,System.Boolean)">
            <summary>
            Writes UTF8 encoded string at the current position.
            </summary>
            <param name="value">Constant value.</param>
            <param name="allowUnpairedSurrogates">
            True to encode unpaired surrogates as specified, otherwise replace them with U+FFFD character.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="value"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteCompressedSignedInteger(System.Int32)">
            <summary>
            Implements compressed signed integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.
            </summary>
            <remarks>
            If the value lies between -64 (0xFFFFFFC0) and 63 (0x3F), inclusive, encode as a one-byte integer: 
            bit 7 clear, value bits 5 through 0 held in bits 6 through 1, sign bit (value bit 31) in bit 0.
            
            If the value lies between -8192 (0xFFFFE000) and 8191 (0x1FFF), inclusive, encode as a two-byte integer: 
            15 set, bit 14 clear, value bits 12 through 0 held in bits 13 through 1, sign bit(value bit 31) in bit 0.
            
            If the value lies between -268435456 (0xF000000) and 268435455 (0x0FFFFFFF), inclusive, encode as a four-byte integer: 
            31 set, 30 set, bit 29 clear, value bits 27 through 0 held in bits 28 through 1, sign bit(value bit 31) in bit 0.
            </remarks>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="value"/> can't be represented as a compressed signed integer.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteCompressedInteger(System.Int32)">
            <summary>
            Implements compressed unsigned integer encoding as defined by ECMA-335-II chapter 23.2: Blobs and signatures.
            </summary>
            <remarks>
            If the value lies between 0 (0x00) and 127 (0x7F), inclusive, 
            encode as a one-byte integer (bit 7 is clear, value held in bits 6 through 0).
            
            If the value lies between 28 (0x80) and 214 – 1 (0x3FFF), inclusive, 
            encode as a 2-byte integer with bit 15 set, bit 14 clear (value held in bits 13 through 0).
            
            Otherwise, encode as a 4-byte integer, with bit 31 set, bit 30 set, bit 29 clear (value held in bits 28 through 0).
            </remarks>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="value"/> can't be represented as a compressed unsigned integer.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobBuilder.WriteConstant(System.Object)">
            <summary>
            Writes a constant value (see ECMA-335 Partition II section 22.9) at the current position.
            </summary>
            <exception cref="T:System.ArgumentException"><paramref name="value"/> is not of a constant type.</exception>
            <exception cref="T:System.InvalidOperationException">Builder is not writable, it has been linked with another one.</exception>
        </member>
        <member name="F:System.Reflection.Metadata.PortablePdbVersions.DefaultMetadataVersion">
            <summary>
            Version of Portable PDB format emitted by the writer by default. Metadata version string.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.PortablePdbVersions.DefaultFormatVersion">
            <summary>
            Version of Portable PDB format emitted by the writer by default.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.PortablePdbVersions.MinFormatVersion">
            <summary>
            Minimal supported version of Portable PDB format.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.PortablePdbVersions.MinEmbeddedVersion">
            <summary>
            Minimal supported version of Embedded Portable PDB blob.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.PortablePdbVersions.DefaultEmbeddedVersion">
            <summary>
            Version of Embedded Portable PDB blob format emitted by the writer by default.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.PortablePdbVersions.MinUnsupportedEmbeddedVersion">
            <summary>
            Minimal version of the Embedded Portable PDB blob that the current reader can't interpret.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.ReservedBlob`1">
            <summary>
            Represents a handle and a corresponding blob on a metadata heap that was reserved for future content update.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ReservedBlob`1.CreateWriter">
            <summary>
            Returns a <see cref="T:System.Reflection.Metadata.BlobWriter"/> to be used to update the content.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.GetSystemType">
            <summary>
            Gets the TType representation for <see cref="T:System.Type"/>.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.IsSystemType(`0)">
            <summary>
            Returns true if the given type represents <see cref="T:System.Type"/>.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.GetTypeFromSerializedName(System.String)">
            <summary>
            Get the type symbol for the given serialized type name.
            The serialized type name is in so-called "reflection notation" (i.e. as understood by <see cref="M:System.Type.GetType(System.String)"/>.)
            </summary>
            <exception cref="T:System.BadImageFormatException">The name is malformed.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.ICustomAttributeTypeProvider`1.GetUnderlyingEnumType(`0)">
            <summary>
            Gets the underlying type of the given enum type symbol.
            </summary>
            <exception cref="T:System.BadImageFormatException">The given type symbol does not represent an enum.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.IsBranch(System.Reflection.Metadata.ILOpCode)">
            <summary>
            Returns true of the specified op-code is a branch to a label.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.GetBranchOperandSize(System.Reflection.Metadata.ILOpCode)">
            <summary>
            Calculate the size of the specified branch instruction operand.
            </summary>
            <param name="opCode">Branch op-code.</param>
            <returns>1 if <paramref name="opCode"/> is a short branch or 4 if it is a long branch.</returns>
            <exception cref="T:System.ArgumentException">Specified <paramref name="opCode"/> is not a branch op-code.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.GetShortBranch(System.Reflection.Metadata.ILOpCode)">
            <summary>
            Get a short form of the specified branch op-code.
            </summary>
            <param name="opCode">Branch op-code.</param>
            <returns>Short form of the branch op-code.</returns>
            <exception cref="T:System.ArgumentException">Specified <paramref name="opCode"/> is not a branch op-code.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.ILOpCodeExtensions.GetLongBranch(System.Reflection.Metadata.ILOpCode)">
            <summary>
            Get a long form of the specified branch op-code.
            </summary>
            <param name="opCode">Branch op-code.</param>
            <returns>Long form of the branch op-code.</returns>
            <exception cref="T:System.ArgumentException">Specified <paramref name="opCode"/> is not a branch op-code.</exception>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataStreamOptions.Default">
            <summary>
            By default the stream is disposed when <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> is disposed and sections of the PE image are read lazily.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen">
            <summary>
            Keep the stream open when the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> is disposed.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata">
            <summary>
            Reads PDB metadata into memory right away. 
            </summary>
            <remarks>
            The underlying file may be closed and even deleted after <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> is constructed.
            <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> closes the stream automatically by the time the constructor returns unless <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen"/> is specified.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.MetadataReaderProvider">
            <summary>
            Provides a <see cref="T:System.Reflection.Metadata.MetadataReader"/> metadata stored in an array of bytes, a memory block, or a stream.
            </summary>
            <remarks>
            Supported formats:
            - ECMA-335 CLI (Common Language Infrastructure) metadata (<see cref="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataImage(System.Byte*,System.Int32)"/>)
            - Edit and Continue metadata delta (<see cref="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataImage(System.Byte*,System.Int32)"/>)
            - Portable PDB metadata (<see cref="M:System.Reflection.Metadata.MetadataReaderProvider.FromPortablePdbImage(System.Byte*,System.Int32)"/>)
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromPortablePdbImage(System.Byte*,System.Int32)">
            <summary>
            Creates a Portable PDB metadata provider over a blob stored in memory.
            </summary>
            <param name="start">Pointer to the start of the Portable PDB blob.</param>
            <param name="size">The size of the Portable PDB blob.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="start"/> is <see cref="F:System.IntPtr.Zero"/>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="size"/> is negative.</exception>
            <remarks>
            The memory is owned by the caller and not released on disposal of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>.
            The caller is responsible for keeping the memory alive and unmodified throughout the lifetime of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>.
            The content of the blob is not read during the construction of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataImage(System.Byte*,System.Int32)">
            <summary>
            Creates a metadata provider over an image stored in memory.
            </summary>
            <param name="start">Pointer to the start of the metadata blob.</param>
            <param name="size">The size of the metadata blob.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="start"/> is <see cref="F:System.IntPtr.Zero"/>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="size"/> is negative.</exception>
            <remarks>
            The memory is owned by the caller and not released on disposal of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>.
            The caller is responsible for keeping the memory alive and unmodified throughout the lifetime of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>.
            The content of the blob is not read during the construction of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromPortablePdbImage(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <summary>
            Creates a Portable PDB metadata provider over a byte array.
            </summary>
            <param name="image">Portable PDB image.</param>
            <remarks>
            The content of the image is not read during the construction of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="image"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataImage(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <summary>
            Creates a provider over a byte array.
            </summary>
            <param name="image">Metadata image.</param>
            <remarks>
            The content of the image is not read during the construction of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="image"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromPortablePdbStream(System.IO.Stream,System.Reflection.Metadata.MetadataStreamOptions,System.Int32)">
            <summary>
            Creates a provider for a stream of the specified size beginning at its current position.
            </summary>
            <param name="stream">Stream.</param>
            <param name="size">Size of the metadata blob in the stream. If not specified the metadata blob is assumed to span to the end of the stream.</param>
            <param name="options">
            Options specifying how sections of the image are read from the stream.
            
            Unless <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen"/> is specified, ownership of the stream is transferred to the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> 
            upon successful argument validation. It will be disposed by the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> and the caller must not manipulate it.
            
            Unless <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata"/> is specified no data 
            is read from the stream during the construction of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>. Furthermore, the stream must not be manipulated
            by caller while the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> is alive and undisposed.
            
            If <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata"/>, the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> 
            will have read all of the data requested during construction. As such, if <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen"/> is also
            specified, the caller retains full ownership of the stream and is assured that it will not be manipulated by the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>
            after construction.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="stream"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="stream"/> doesn't support read and seek operations.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end of the stream.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.FromMetadataStream(System.IO.Stream,System.Reflection.Metadata.MetadataStreamOptions,System.Int32)">
            <summary>
            Creates a provider for a stream of the specified size beginning at its current position.
            </summary>
            <param name="stream">Stream.</param>
            <param name="size">Size of the metadata blob in the stream. If not specified the metadata blob is assumed to span to the end of the stream.</param>
            <param name="options">
            Options specifying how sections of the image are read from the stream.
            
            Unless <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen"/> is specified, ownership of the stream is transferred to the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> 
            upon successful argument validation. It will be disposed by the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> and the caller must not manipulate it.
            
            Unless <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata"/> is specified no data 
            is read from the stream during the construction of the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>. Furthermore, the stream must not be manipulated
            by caller while the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> is alive and undisposed.
            
            If <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata"/>, the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> 
            will have read all of the data requested during construction. As such, if <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.LeaveOpen"/> is also
            specified, the caller retains full ownership of the stream and is assured that it will not be manipulated by the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>
            after construction.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="stream"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="stream"/> doesn't support read and seek operations.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end of the stream.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the stream (only when <see cref="F:System.Reflection.Metadata.MetadataStreamOptions.PrefetchMetadata"/> is specified).</exception>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.Dispose">
            <summary>
            Disposes all memory allocated by the reader.
            </summary>
            <remarks>
            <see cref="M:System.Reflection.Metadata.MetadataReaderProvider.Dispose"/>  can be called multiple times (but not in parallel).
            It is not safe to call <see cref="M:System.Reflection.Metadata.MetadataReaderProvider.Dispose"/> in parallel with any other operation on the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>
            or reading from the underlying memory.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.GetMetadataReader(System.Reflection.Metadata.MetadataReaderOptions,System.Reflection.Metadata.MetadataStringDecoder)">
            <summary>
            Gets a <see cref="T:System.Reflection.Metadata.MetadataReader"/> from a <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/>.
            </summary>
            <remarks>
            The caller must keep the <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> alive and undisposed throughout the lifetime of the metadata reader.
            
            If this method is called multiple times each call with arguments equal to the arguments passed to the previous successful call 
            returns the same instance of <see cref="T:System.Reflection.Metadata.MetadataReader"/> as the previous call.
            </remarks>
            <exception cref="T:System.ArgumentException">The encoding of <paramref name="utf8Decoder"/> is not <see cref="T:System.Text.UTF8Encoding"/>.</exception>
            <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
            <exception cref="T:System.ObjectDisposedException">Provider has been disposed.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReaderProvider.GetMetadataBlock">
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
            <exception cref="T:System.ObjectDisposedException">Provider has been disposed.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.PrimitiveSerializationTypeCode">
            <summary>
            Type codes used to encode types of primitive values in Custom Attribute value blob.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MethodDefinitionHandle.ToDebugInformationHandle">
            <summary>
            Returns a handle to <see cref="T:System.Reflection.Metadata.MethodDebugInformation"/> corresponding to this handle.
            </summary>
            <remarks>
            The resulting handle is only valid within the context of a <see cref="T:System.Reflection.Metadata.MetadataReader"/> open on the Portable PDB blob,
            which in case of standalone PDB file is a different reader than the one containing this method definition.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.UserStringHandle">
            <summary>
            #UserString heap handle.
            </summary>
            <remarks>
            The handle is 32-bit wide.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.NamespaceDefinitionHandle">
            <summary>
            A handle that represents a namespace definition. 
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.AssemblyFile.ContainsMetadata">
            <summary>
            True if the file contains metadata.
            </summary>
            <remarks>
            Corresponds to Flags field of File table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.AssemblyFile.Name">
            <summary>
            File name with extension.
            </summary>
            <remarks>
            Corresponds to Name field of File table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.AssemblyFile.HashValue">
            <summary>
            Hash value of the file content calculated using <see cref="P:System.Reflection.Metadata.AssemblyDefinition.HashAlgorithm"/>.
            </summary>
            <remarks>
            Corresponds to HashValue field of File table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="F:System.Reflection.Metadata.BlobReader.s_nullCharArray">
            <summary>An array containing the '\0' character.</summary>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.#ctor(System.Byte*,System.Int32)">
            <summary>
            Creates a reader of the specified memory block.
            </summary>
            <param name="buffer">Pointer to the start of the memory block.</param>
            <param name="length">Length in bytes of the memory block.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="buffer"/> is null and <paramref name="length"/> is greater than zero.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="length"/> is negative.</exception>
            <exception cref="T:System.PlatformNotSupportedException">The current platform is not little-endian.</exception>
        </member>
        <member name="P:System.Reflection.Metadata.BlobReader.StartPointer">
            <summary>
            Pointer to the byte at the start of the underlying memory block.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.BlobReader.CurrentPointer">
            <summary>
            Pointer to the byte at the current position of the reader.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.BlobReader.Length">
            <summary>
            The total length of the underlying memory block.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.BlobReader.Offset">
            <summary>
            Gets or sets the offset from start of the blob to the current position.
            </summary>
            <exception cref="T:System.BadImageFormatException">Offset is set outside the bounds of underlying reader.</exception>
        </member>
        <member name="P:System.Reflection.Metadata.BlobReader.RemainingBytes">
            <summary>
            Bytes remaining from current position to end of underlying memory block.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.Reset">
            <summary>
            Repositions the reader to the start of the underluing memory block.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.Align(System.Byte)">
            <summary>
            Repositions the reader forward by the number of bytes required to satisfy the given alignment.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadDecimal">
            <summary>
            Reads <see cref="T:System.Decimal"/> number.
            </summary>
            <remarks>
            Decimal number is encoded in 13 bytes as follows:
            - byte 0: highest bit indicates sign (1 for negative, 0 for non-negative); the remaining 7 bits encode scale
            - bytes 1..12: 96-bit unsigned integer in little endian encoding.
            </remarks>
            <exception cref="T:System.BadImageFormatException">The data at the current position was not a valid <see cref="T:System.Decimal"/> number.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.IndexOf(System.Byte)">
            <summary>
            Finds specified byte in the blob following the current position.
            </summary>
            <returns>
            Index relative to the current position, or -1 if the byte is not found in the blob following the current position.
            </returns>
            <remarks>
            Doesn't change the current position.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadUTF8(System.Int32)">
            <summary>
            Reads UTF8 encoded string starting at the current position. 
            </summary>
            <param name="byteCount">The number of bytes to read.</param>
            <returns>The string.</returns>
            <exception cref="T:System.BadImageFormatException"><paramref name="byteCount"/> bytes not available.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadUTF16(System.Int32)">
            <summary>
            Reads UTF16 (little-endian) encoded string starting at the current position. 
            </summary>
            <param name="byteCount">The number of bytes to read.</param>
            <returns>The string.</returns>
            <exception cref="T:System.BadImageFormatException"><paramref name="byteCount"/> bytes not available.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadBytes(System.Int32)">
            <summary>
            Reads bytes starting at the current position. 
            </summary>
            <param name="byteCount">The number of bytes to read.</param>
            <returns>The byte array.</returns>
            <exception cref="T:System.BadImageFormatException"><paramref name="byteCount"/> bytes not available.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadBytes(System.Int32,System.Byte[],System.Int32)">
            <summary>
            Reads bytes starting at the current position in to the given buffer at the given offset;
            </summary>
            <param name="byteCount">The number of bytes to read.</param>
            <param name="buffer">The destination buffer the bytes read will be written.</param>
            <param name="bufferOffset">The offset in the destination buffer where the bytes read will be written.</param>
            <exception cref="T:System.BadImageFormatException"><paramref name="byteCount"/> bytes not available.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.TryReadCompressedInteger(System.Int32@)">
            <summary>
            Reads an unsigned compressed integer value. 
            See Metadata Specification section II.23.2: Blobs and signatures.
            </summary>
            <param name="value">The value of the compressed integer that was read.</param>
            <returns>true if the value was read successfully. false if the data at the current position was not a valid compressed integer.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadCompressedInteger">
            <summary>
            Reads an unsigned compressed integer value. 
            See Metadata Specification section II.23.2: Blobs and signatures.
            </summary>
            <returns>The value of the compressed integer that was read.</returns>
            <exception cref="T:System.BadImageFormatException">The data at the current position was not a valid compressed integer.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.TryReadCompressedSignedInteger(System.Int32@)">
            <summary>
            Reads a signed compressed integer value. 
            See Metadata Specification section II.23.2: Blobs and signatures.
            </summary>
            <param name="value">The value of the compressed integer that was read.</param>
            <returns>true if the value was read successfully. false if the data at the current position was not a valid compressed integer.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadCompressedSignedInteger">
            <summary>
            Reads a signed compressed integer value. 
            See Metadata Specification section II.23.2: Blobs and signatures.
            </summary>
            <returns>The value of the compressed integer that was read.</returns>
            <exception cref="T:System.BadImageFormatException">The data at the current position was not a valid compressed integer.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadSerializationTypeCode">
            <summary>
            Reads type code encoded in a serialized custom attribute value. 
            </summary>
            <returns><see cref="F:System.Reflection.Metadata.SerializationTypeCode.Invalid"/> if the encoding is invalid.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadSignatureTypeCode">
            <summary>
            Reads type code encoded in a signature. 
            </summary>
            <returns><see cref="F:System.Reflection.Metadata.SignatureTypeCode.Invalid"/> if the encoding is invalid.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadSerializedString">
            <summary>
            Reads a string encoded as a compressed integer containing its length followed by
            its contents in UTF8. Null strings are encoded as a single 0xFF byte.
            </summary>
            <remarks>Defined as a 'SerString' in the ECMA CLI specification.</remarks>
            <returns>String value or null.</returns>
            <exception cref="T:System.BadImageFormatException">If the encoding is invalid.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadTypeHandle">
            <summary>
            Reads a type handle encoded in a signature as TypeDefOrRefOrSpecEncoded (see ECMA-335 II.23.2.8).
            </summary>
            <returns>The handle or nil if the encoding is invalid.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadBlobHandle">
            <summary>
            Reads a #Blob heap handle encoded as a compressed integer.
            </summary>
            <remarks>
            Blobs that contain references to other blobs are used in Portable PDB format, for example <see cref="P:System.Reflection.Metadata.Document.Name"/>.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.BlobReader.ReadConstant(System.Reflection.Metadata.ConstantTypeCode)">
            <summary>
            Reads a constant value (see ECMA-335 Partition II section 22.9) from the current position.
            </summary>
            <exception cref="T:System.BadImageFormatException">Error while reading from the blob.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="typeCode"/> is not a valid <see cref="T:System.Reflection.Metadata.ConstantTypeCode"/>.</exception>
            <returns>
            Boxed constant value. To avoid allocating the object use Read* methods directly.
            Constants of type <see cref="F:System.Reflection.Metadata.ConstantTypeCode.String"/> are encoded as UTF16 strings, use <see cref="M:System.Reflection.Metadata.BlobReader.ReadUTF16(System.Int32)"/> to read them.
            </returns>
        </member>
        <member name="P:System.Reflection.Metadata.Constant.TypeCode">
            <summary>
            The type of the constant value.
            </summary>
            <remarks>
            Corresponds to Type field of Constant table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.Constant.Value">
            <summary>
            The constant value.
            </summary>
            <remarks>
            Corresponds to Value field of Constant table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.Constant.Parent">
            <summary>
            The parent handle (<see cref="T:System.Reflection.Metadata.ParameterHandle"/>, <see cref="T:System.Reflection.Metadata.FieldDefinitionHandle"/>, or <see cref="T:System.Reflection.Metadata.PropertyDefinitionHandle"/>).
            </summary>
            <remarks>
            Corresponds to Parent field of Constant table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.CustomAttribute.Constructor">
            <summary>
            The constructor (<see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/>) of the custom attribute type.
            </summary>
            <remarks>
            Corresponds to Type field of CustomAttribute table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.CustomAttribute.Parent">
            <summary>
            The handle of the metadata entity the attribute is applied to.
            </summary>
            <remarks>
            Corresponds to Parent field of CustomAttribute table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.CustomAttribute.Value">
            <summary>
            The value of the attribute.
            </summary>
            <remarks>
            Corresponds to Value field of CustomAttribute table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.CustomAttribute.DecodeValue``1(System.Reflection.Metadata.ICustomAttributeTypeProvider{``0})">
            <summary>
            Decodes the arguments encoded in the value blob.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.ArrayShape">
            <summary>
            Represents the shape of an array type.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ArrayShape.Rank">
            <summary>
            Gets the number of dimensions in the array.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ArrayShape.Sizes">
            <summary>
            Gets the sizes of each dimension. Length may be smaller than rank, in which case the trailing dimensions have unspecified sizes.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ArrayShape.LowerBounds">
            <summary>
            Gets the lower-bounds of each dimension. Length may be smaller than rank, in which case the trailing dimensions have unspecified lower bounds.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ISZArrayTypeProvider`1.GetSZArrayType(`0)">
            <summary>
            Gets the type symbol for a single-dimensional array with zero lower bounds of the given element type.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetFunctionPointerType(System.Reflection.Metadata.MethodSignature{`0})">
            <summary>
            Gets the a type symbol for the function pointer type of the given method signature.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetGenericMethodParameter(`1,System.Int32)">
            <summary>
            Gets the type symbol for the generic method parameter at the given zero-based index.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetGenericTypeParameter(`1,System.Int32)">
            <summary>
            Gets the type symbol for the generic type parameter at the given zero-based index.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetModifiedType(`0,`0,System.Boolean)">
            <summary>
            Gets the type symbol for a type with a custom modifier applied.
            </summary>
            <param name="modifier">The modifier type applied. </param>
            <param name="unmodifiedType">The type symbol of the underlying type without modifiers applied.</param>
            <param name="isRequired">True if the modifier is required, false if it's optional.</param>
        </member>
        <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetPinnedType(`0)">
            <summary>
            Gets the type symbol for a local variable type that is marked as pinned.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ISignatureTypeProvider`2.GetTypeFromSpecification(System.Reflection.Metadata.MetadataReader,`1,System.Reflection.Metadata.TypeSpecificationHandle,System.Byte)">
            <summary>
            Gets the type symbol for a type specification.
            </summary>
            <param name="reader">
            The metadata reader that was passed to the signature decoder. It may be null.
            </param>
            <param name="genericContext">
            The context that was passed to the signature decoder.
            </param>
            <param name="handle">
            The type specification handle.
            </param>
            <param name="rawTypeKind">
            The kind of the type as specified in the signature. To interpret this value use <see cref="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)"/>
            Note that when the signature comes from a WinMD file additional processing is needed to determine whether the target type is a value type or a reference type.
            </param>
        </member>
        <member name="M:System.Reflection.Metadata.ISimpleTypeProvider`1.GetPrimitiveType(System.Reflection.Metadata.PrimitiveTypeCode)">
            <summary>
            Gets the type symbol for a primitive type.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.ISimpleTypeProvider`1.GetTypeFromDefinition(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.TypeDefinitionHandle,System.Byte)">
            <summary>
            Gets the type symbol for a type definition.
            </summary>
            <param name="reader">
            The metadata reader that was passed to the signature decoder. It may be null.
            </param>
            <param name="handle">
            The type definition handle.
            </param>
            <param name="rawTypeKind">
            The kind of the type as specified in the signature. To interpret this value use <see cref="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)"/>
            Note that when the signature comes from a WinMD file additional processing is needed to determine whether the target type is a value type or a reference type.
            </param>
        </member>
        <member name="M:System.Reflection.Metadata.ISimpleTypeProvider`1.GetTypeFromReference(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.TypeReferenceHandle,System.Byte)">
            <summary>
            Gets the type symbol for a type reference.
            </summary>
            <param name="reader">
            The metadata reader that was passed to the signature decoder. It may be null.
            </param>
            <param name="handle">
            The type definition handle.
            </param>
            <param name="rawTypeKind">
            The kind of the type as specified in the signature. To interpret this value use <see cref="M:System.Reflection.Metadata.Ecma335.MetadataReaderExtensions.ResolveSignatureTypeKind(System.Reflection.Metadata.MetadataReader,System.Reflection.Metadata.EntityHandle,System.Byte)"/>
            Note that when the signature comes from a WinMD file additional processing is needed to determine whether the target type is a value type or a reference type.
            </param>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeKind.Unknown">
            <summary>
            It is not known in the current context if the type reference or definition is a class or value type.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeKind.Class">
            <summary>
            The type definition or reference refers to a class.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeKind.ValueType">
            <summary>
            The type definition or reference refers to a value type.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetGenericInstantiation(`0,System.Collections.Immutable.ImmutableArray{`0})">
            <summary>
            Gets the type symbol for a generic instantiation of the given generic type with the given type arguments.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetArrayType(`0,System.Reflection.Metadata.ArrayShape)">
            <summary>
            Gets the type symbol for a generalized array of the given element type and shape. 
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetByReferenceType(`0)">
            <summary>
            Gets the type symbol for a managed pointer to the given element type.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.IConstructedTypeProvider`1.GetPointerType(`0)">
            <summary>
            Gets the type symbol for an unmanaged pointer to the given element ty
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.MethodSignature`1">
            <summary>
            Represents a method (definition, reference, or standalone) or property signature.
            In the case of properties, the signature matches that of a getter with a distinguishing <see cref="T:System.Reflection.Metadata.SignatureHeader"/>.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodSignature`1.Header">
            <summary>
            Represents the information in the leading byte of the signature (kind, calling convention, flags).
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodSignature`1.ReturnType">
            <summary>
            Gets the method's return type.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodSignature`1.RequiredParameterCount">
            <summary>
            Gets the number of parameters that are required. Will be equal to the length <see cref="P:System.Reflection.Metadata.MethodSignature`1.ParameterTypes"/> of
            unless this signature represents the standalone call site of a vararg method, in which case the entries
            extra entries in <see cref="P:System.Reflection.Metadata.MethodSignature`1.ParameterTypes"/> are the types used for the optional parameters.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodSignature`1.GenericParameterCount">
            <summary>
            Gets the number of generic type parameters of the method. Will be 0 for non-generic methods.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodSignature`1.ParameterTypes">
            <summary>
            Gets the method's parameter types.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.PrimitiveTypeCode">
            <summary>
            Represents a primitive type found in metadata signatures.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExceptionRegion.TryOffset">
            <summary>
            Start IL offset of the try block.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExceptionRegion.TryLength">
            <summary>
            Length in bytes of try block.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExceptionRegion.HandlerOffset">
            <summary>
            Start IL offset of the exception handler.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExceptionRegion.HandlerLength">
            <summary>
            Length in bytes of the exception handler.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExceptionRegion.FilterOffset">
            <summary>
            IL offset of the start of the filter block, or -1 if the region is not a filter.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExceptionRegion.CatchType">
            <summary>
            Returns a TypeRef, TypeDef, or TypeSpec handle if the region represents a catch, nil token otherwise. 
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExportedType.Name">
            <summary>
            Name of the target type, or nil if the type is nested or defined in a root namespace.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExportedType.Namespace">
            <summary>
            Full name of the namespace where the target type, or nil if the type is nested or defined in a root namespace.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExportedType.NamespaceDefinition">
            <summary>
            The definition handle of the namespace where the target type is defined, or nil if the type is nested or defined in a root namespace.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ExportedType.Implementation">
            <summary>
            Handle to resolve the implementation of the target type.
            </summary>
            <returns>
            <list type="bullet">
            <item><description><see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/> representing another module in the assembly.</description></item>
            <item><description><see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/> representing another assembly if <see cref="P:System.Reflection.Metadata.ExportedType.IsForwarder"/> is true.</description></item>
            <item><description><see cref="T:System.Reflection.Metadata.ExportedTypeHandle"/> representing the declaring exported type in which this was is nested.</description></item>
            </list>
            </returns>
        </member>
        <member name="M:System.Reflection.Metadata.FieldDefinition.GetOffset">
            <summary>
            Returns field layout offset, or -1 if not available.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.GenericParameter.Parent">
            <summary>
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>.
            </summary>
            <remarks>
            Corresponds to Owner field of GenericParam table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.GenericParameter.Attributes">
            <summary>
            Attributes specifying variance and constraints.
            </summary>
            <remarks>
            Corresponds to Flags field of GenericParam table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.GenericParameter.Index">
            <summary>
            Zero-based index of the parameter within the declaring generic type or method declaration.
            </summary>
            <remarks>
            Corresponds to Number field of GenericParam table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.GenericParameter.Name">
            <summary>
            The name of the generic parameter.
            </summary>
            <remarks>
            Corresponds to Name field of GenericParam table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.GenericParameterConstraint.Parameter">
            <summary>
            The constrained <see cref="T:System.Reflection.Metadata.GenericParameterHandle"/>.
            </summary>
            <remarks>
            Corresponds to Owner field of GenericParamConstraint table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.GenericParameterConstraint.Type">
            <summary>
            Handle (<see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>, or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>) 
            specifying from which type this generic parameter is constrained to derive,
            or which interface this generic parameter is constrained to implement.
            </summary>
            <remarks>
            Corresponds to Constraint field of GenericParamConstraint table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.GenericParameterHandleCollection">
            <summary>
            Represents generic type parameters of a method or type.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.GenericParameterConstraintHandleCollection">
            <summary>
            Represents constraints of a generic type parameter.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.ParameterHandleCollection">
            <summary>
            Collection of parameters of a specified method.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.TypeDefinitionHandleCollection">
            <summary>
            Represents a collection of <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.TypeReferenceHandleCollection">
            <summary>
            Represents a collection of <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.ExportedTypeHandleCollection">
            <summary>
            Represents a collection of <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.MemberReferenceHandleCollection">
            <summary>
            Represents a collection of <see cref="T:System.Reflection.Metadata.MemberReferenceHandle"/>.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.AssemblyReferenceHandleCollection">
            <summary>
            Collection of assembly references.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.ManifestResourceHandleCollection">
            <summary>
            Represents a collection of <see cref="T:System.Reflection.Metadata.ManifestResourceHandle"/>.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.AssemblyFileHandleCollection">
            <summary>
            Represents a collection of <see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.HandleComparer.Compare(System.Reflection.Metadata.Handle,System.Reflection.Metadata.Handle)">
            <summary>
            Compares two handles.
            </summary>
            <remarks>
            The order of handles that differ in kind and are not <see cref="T:System.Reflection.Metadata.EntityHandle"/> is undefined.
            Returns 0 if and only if <see cref="M:System.Reflection.Metadata.HandleComparer.Equals(System.Reflection.Metadata.Handle,System.Reflection.Metadata.Handle)"/> returns true.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.HandleComparer.Compare(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)">
            <summary>
            Compares two entity handles.
            </summary>
            <remarks>
            Returns 0 if and only if <see cref="M:System.Reflection.Metadata.HandleComparer.Equals(System.Reflection.Metadata.EntityHandle,System.Reflection.Metadata.EntityHandle)"/> returns true.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.Handle">
            <summary>
            Represents any metadata entity (type reference/definition/specification, method definition, custom attribute, etc.) or value (string, blob, guid, user string).
            </summary>
            <remarks>
            Use <see cref="T:System.Reflection.Metadata.Handle"/> to store multiple kinds of handles.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.Handle.FromVToken(System.UInt32)">
            <summary>
            Creates <see cref="T:System.Reflection.Metadata.Handle"/> from a token or a token combined with a virtual flag.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Handle.EntityHandleType">
            <summary>
            Token type (0x##000000), does not include virtual flag.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Handle.Type">
            <summary>
            Small token type (0x##), does not include virtual flag.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Handle.EntityHandleValue">
            <summary>
            Value stored in an <see cref="T:System.Reflection.Metadata.EntityHandle"/>.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Handle.SpecificEntityHandleValue">
            <summary>
            Value stored in a concrete entity handle (see <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.MethodDefinitionHandle"/>, etc.).
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.InterfaceImplementation.Interface">
            <summary>
            The interface that is implemented
            <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/>, or <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.ManifestResource.Offset">
            <summary>
            Specifies the byte offset within the referenced file at which this resource record begins.
            </summary>
            <remarks>
            Corresponds to Offset field of ManifestResource table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.ManifestResource.Attributes">
            <summary>
            Resource attributes.
            </summary>
            <remarks>
            Corresponds to Flags field of ManifestResource table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.ManifestResource.Name">
            <summary>
            Name of the resource.
            </summary>
            <remarks>
            Corresponds to Name field of ManifestResource table in ECMA-335 Standard.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.ManifestResource.Implementation">
            <summary>
            <see cref="T:System.Reflection.Metadata.AssemblyFileHandle"/>, <see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>, or nil handle.
            </summary>
            <remarks>
            Corresponds to Implementation field of ManifestResource table in ECMA-335 Standard.
            
            If nil then <see cref="P:System.Reflection.Metadata.ManifestResource.Offset"/> is an offset in the PE image that contains the metadata, 
            starting from the Resource entry in the CLI header.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.MemberReference.Parent">
            <summary>
            MethodDef, ModuleRef,TypeDef, TypeRef, or TypeSpec handle.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MemberReference.Signature">
            <summary>
            Gets a handle to the signature blob.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MemberReference.GetKind">
            <summary>
            Determines if the member reference is to a method or field.
            </summary>
            <exception cref="T:System.BadImageFormatException">The member reference signature is invalid.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.StandaloneSignatureKind">
            <summary>
            Indicates whether a <see cref="T:System.Reflection.Metadata.StandaloneSignature"/> represents a standalone method or local variable signature.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.StandaloneSignatureKind.Method">
            <summary>
            The <see cref="T:System.Reflection.Metadata.StandaloneSignature"/> represents a standalone method signature.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.StandaloneSignatureKind.LocalVariables">
            <summary>
            The <see cref="T:System.Reflection.Metadata.MemberReference"/> references a local variable signature.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.MemberReferenceKind">
            <summary>
            Indicates whether a <see cref="T:System.Reflection.Metadata.MemberReference"/> references a method or field.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MemberReferenceKind.Method">
            <summary>
            The <see cref="T:System.Reflection.Metadata.MemberReference"/> references a method.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MemberReferenceKind.Field">
            <summary>
            The <see cref="T:System.Reflection.Metadata.MemberReference"/> references a field.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataKind.Ecma335">
            <summary>
            CLI metadata.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataKind.WindowsMetadata">
            <summary>
            Windows Metadata.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataKind.ManagedWindowsMetadata">
            <summary>
            Windows Metadata generated by managed compilers.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.MetadataReader">
            <summary>
            Reads metadata as defined byte the ECMA 335 CLI specification.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.#ctor(System.Byte*,System.Int32)">
            <summary>
            Creates a metadata reader from the metadata stored at the given memory location.
            </summary>
            <remarks>
            The memory is owned by the caller and it must be kept memory alive and unmodified throughout the lifetime of the <see cref="T:System.Reflection.Metadata.MetadataReader"/>.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.#ctor(System.Byte*,System.Int32,System.Reflection.Metadata.MetadataReaderOptions)">
            <summary>
            Creates a metadata reader from the metadata stored at the given memory location.
            </summary>
            <remarks>
            The memory is owned by the caller and it must be kept memory alive and unmodified throughout the lifetime of the <see cref="T:System.Reflection.Metadata.MetadataReader"/>.
            Use <see cref="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader,System.Reflection.Metadata.MetadataReaderOptions)"/> to obtain 
            metadata from a PE image.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.#ctor(System.Byte*,System.Int32,System.Reflection.Metadata.MetadataReaderOptions,System.Reflection.Metadata.MetadataStringDecoder)">
            <summary>
            Creates a metadata reader from the metadata stored at the given memory location.
            </summary>
            <remarks>
            The memory is owned by the caller and it must be kept memory alive and unmodified throughout the lifetime of the <see cref="T:System.Reflection.Metadata.MetadataReader"/>.
            Use <see cref="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader,System.Reflection.Metadata.MetadataReaderOptions,System.Reflection.Metadata.MetadataStringDecoder)"/> to obtain 
            metadata from a PE image.
            </remarks>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="length"/> is not positive.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="metadata"/> is null.</exception>
            <exception cref="T:System.ArgumentException">The encoding of <paramref name="utf8Decoder"/> is not <see cref="T:System.Text.UTF8Encoding"/>.</exception>
            <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
            <exception cref="T:System.BadImageFormatException">Bad metadata header.</exception>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataReader.IsMinimalDelta">
            <summary>
            True if the metadata stream has minimal delta format. Used for EnC.
            </summary>
            <remarks>
            The metadata stream has minimal delta format if "#JTD" stream is present.
            Minimal delta format uses large size (4B) when encoding table/heap references.
            The heaps in minimal delta only contain data of the delta, 
            there is no padding at the beginning of the heaps that would align them 
            with the original full metadata heaps.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.ReadMetadataHeader(System.Reflection.Metadata.BlobReader@,System.String@)">
            <summary>
            Looks like this function reads beginning of the header described in
            ECMA-335 24.2.1 Metadata root
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.ReadStreamHeaders(System.Reflection.Metadata.BlobReader@)">
            <summary>
            Reads stream headers described in ECMA-335 24.2.2 Stream header
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataReader.TableRowCounts">
            <summary>
            A row count for each possible table. May be indexed by <see cref="T:System.Reflection.Metadata.Ecma335.TableIndex"/>.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.MetadataPointer">
            <summary>
            Pointer to the underlying data.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.MetadataLength">
            <summary>
            Length of the underlying data.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.Options">
            <summary>
            Options passed to the constructor.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.MetadataVersion">
            <summary>
            Version string read from metadata header.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.DebugMetadataHeader">
            <summary>
            Information decoded from #Pdb stream, or null if the stream is not present.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.MetadataKind">
            <summary>
            The kind of the metadata (plain ECMA335, WinMD, etc.).
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.StringComparer">
            <summary>
            Comparer used to compare strings stored in metadata.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.UTF8Decoder">
            <summary>
            The decoder used by the reader to produce <see cref="T:System.String"/> instances from UTF8 encoded byte sequences.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataReader.IsAssembly">
            <summary>
            Returns true if the metadata represent an assembly.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.GetNestedTypes(System.Reflection.Metadata.TypeDefinitionHandle)">
            <summary>
            Returns an array of types nested in the specified type.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.CalculateFieldDefTreatmentAndRowId(System.Reflection.Metadata.FieldDefinitionHandle)">
            <summary>
            The backing field of a WinRT enumeration type is not public although the backing fields
            of managed enumerations are. To allow managed languages to directly access this field,
            it is made public by the metadata adapter.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.ImplementsRedirectedInterface(System.Reflection.Metadata.MemberReferenceHandle,System.Boolean@)">
            <summary>
            We want to know if a given method implements a redirected interface.
            For example, if we are given the method RemoveAt on a class "A" 
            which implements the IVector interface (which is redirected
            to IList in .NET) then this method would return true. The most 
            likely reason why we would want to know this is that we wish to hide
            (mark private) all methods which implement methods on a redirected 
            interface.
            </summary>
            <param name="memberRef">The declaration token for the method</param>
            <param name="isIDisposable">
            Returns true if the redirected interface is <see cref="T:System.IDisposable"/>.
            </param>
            <returns>True if the method implements a method on a redirected interface.
            False otherwise.</returns>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataReader.GetAttributeTypeRaw(System.Reflection.Metadata.CustomAttributeHandle)">
            <summary>
            Returns the type definition or reference handle of the attribute type.
            </summary>
            <returns><see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/> or <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or nil token if the metadata is invalid and the type can't be determined.</returns>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataReaderOptions.None">
            <summary>
            All options are disabled.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataReaderOptions.Default">
            <summary>
            The options that are used when a <see cref="T:System.Reflection.Metadata.MetadataReader"/> is obtained
            via an overload that does not take a <see cref="T:System.Reflection.Metadata.MetadataReaderOptions"/>
            argument.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.MetadataReaderOptions.ApplyWindowsRuntimeProjections">
            <summary>
            Windows Runtime projections are enabled (on by default).
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.MetadataStringComparer">
             <summary>
             Provides string comparison helpers to query strings in metadata while
             avoiding allocation where possible.
             </summary>
            
             <remarks>
             No allocation is performed unless both the handle argument and the
             value argument contain non-ascii text.
            
             Obtain instances using <see cref="P:System.Reflection.Metadata.MetadataReader.StringComparer"/>.
            
             A default-initialized instance is useless and behaves as a null reference.
            
             The code is optimized such that there is no additional overhead in
             re-obtaining a a comparer over hoisting it in to a local.
             
             That is to say that a construct like:
            
             <code>
             if (reader.StringComparer.Equals(typeDef.Namespace, "System") &amp;&amp; 
                 reader.StringComparer.Equals(typeDef.Name, "Object")
             {
                 // found System.Object
             }
             </code>
             
             is no less efficient than:
             
             <code>
             var comparer = reader.StringComparer;
             if (comparer.Equals(typeDef.Namespace, "System") &amp;&amp;
                 comparer.Equals(typeDef.Name, "Object")
             {
                 // found System.Object
             }
             </code>
            
             The choice between them is therefore one of style and not performance.
             </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.MetadataStringDecoder">
             <summary>
             Provides the <see cref="T:System.Reflection.Metadata.MetadataReader"/> with a custom mechanism for decoding
             byte sequences in metadata that represent text.
             </summary>
             <remarks>
             This can be used for the following purposes:
             
             1) To customize the treatment of invalid input. When no decoder is provided,
                the <see cref="T:System.Reflection.Metadata.MetadataReader"/> uses the default fallback replacement 
                with \uFFFD)
            
             2) To reuse existing strings instead of allocating a new one for each decoding
                operation.
             </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataStringDecoder.Encoding">
            <summary>
            Gets the encoding used by this instance. 
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MetadataStringDecoder.DefaultUTF8">
            <summary>
            The default decoder used by <see cref="T:System.Reflection.Metadata.MetadataReader"/> to decode UTF-8 when
            no decoder is provided to the constructor.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataStringDecoder.#ctor(System.Text.Encoding)">
            <summary>
            Creates a <see cref="T:System.Reflection.Metadata.MetadataStringDecoder"/> for the given encoding.
            </summary>
            <param name="encoding">The encoding to use.</param>
            <remarks>
            To cache and reuse existing strings. Create a derived class and override <see cref="M:System.Reflection.Metadata.MetadataStringDecoder.GetString(System.Byte*,System.Int32)"/> 
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MetadataStringDecoder.GetString(System.Byte*,System.Int32)">
            <summary>
            The mechanism through which the <see cref="T:System.Reflection.Metadata.MetadataReader"/> obtains strings
            for byte sequences in metadata. Override this to cache strings if required.
            Otherwise, it is implemented by forwarding straight to <see cref="P:System.Reflection.Metadata.MetadataStringDecoder.Encoding"/>
            and every call will allocate a new string.
            </summary>
            <param name="bytes">Pointer to bytes to decode.</param>
            <param name="byteCount">Number of bytes to decode.</param>
            <returns>The decoded string.</returns>
        </member>
        <member name="P:System.Reflection.Metadata.MethodBodyBlock.Size">
            <summary>
            Size of the method body - includes the header, IL and exception regions.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodSpecification.Method">
            <summary>
            MethodDef or MemberRef handle specifying to which generic method this <see cref="T:System.Reflection.Metadata.MethodSpecification"/> refers,
            that is which generic method is it an instantiation of.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodSpecification.Signature">
            <summary>
            Gets a handle to the signature blob.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.NamespaceDefinition.Name">
            <summary>
            Gets the unqualified name of the NamespaceDefinition.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.NamespaceDefinition.Parent">
            <summary>
            Gets the parent namespace.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.NamespaceDefinition.NamespaceDefinitions">
            <summary>
            Gets the namespace definitions that are direct children of the current
            namespace definition. 
            
            System.Collections and System.Linq are direct children of System. 
            System.Collections.Generic is a direct child of System.Collections.
            System.Collections.Generic is *not* a direct child of System.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.NamespaceDefinition.TypeDefinitions">
            <summary>
            Gets all type definitions that reside directly in a namespace.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.NamespaceDefinition.ExportedTypes">
            <summary>
            Gets all exported types that reside directly in a namespace.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.Document">
            <summary>
            Source document in debug metadata. 
            </summary>
            <remarks>
            See also https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#document-table-0x30.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.Document.Name">
            <summary>
            Returns Document Name Blob.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Document.Language">
            <summary>
            Source code language (C#, VB, F#, etc.)
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Document.HashAlgorithm">
            <summary>
            Hash algorithm used to calculate <see cref="P:System.Reflection.Metadata.Document.Hash"/> (SHA1, SHA256, etc.)
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.Document.Hash">
            <summary>
            Document content hash.
            </summary>
            <remarks>
            <see cref="P:System.Reflection.Metadata.Document.HashAlgorithm"/> determines the algorithm used to produce this hash.
            The source document is hashed in its binary form as stored in the file. 
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.DocumentNameBlobHandle">
            <summary>
            <see cref="T:System.Reflection.Metadata.BlobHandle"/> representing a blob on #Blob heap in Portable PDB 
            structured as Document Name. 
            </summary>
            <remarks>
            The kind of the handle is <see cref="F:System.Reflection.Metadata.HandleKind.Blob"/>. 
            The handle is a specialization of <see cref="T:System.Reflection.Metadata.BlobHandle"/> and doesn't have a distinct kind. 
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.MethodDebugInformationHandle.ToDefinitionHandle">
            <summary>
            Returns a handle to <see cref="T:System.Reflection.Metadata.MethodDefinition"/> corresponding to this handle.
            </summary>
            <remarks>
            The resulting handle is only valid within the context of a <see cref="T:System.Reflection.Metadata.MetadataReader"/> open on the type system metadata blob,
            which in case of standalone PDB file is a different reader than the one containing this method debug information.
            </remarks>
        </member>
        <member name="M:System.Reflection.Metadata.ImportDefinitionCollection.Enumerator.MoveNext">
            <exception cref="T:System.BadImageFormatException">Invalid blob format.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.LocalConstant">
            <summary>
            Local constant. Stored in debug metadata.
            </summary>
            <remarks>
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#localconstant-table-0x34.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.LocalConstant.Signature">
            <summary>
            The constant signature.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.ImportScope">
            <summary>
            Lexical scope within which a group of imports are available. Stored in debug metadata.
            </summary>
            <remarks>
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#importscope-table-0x35
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.LocalScope">
            <summary>
            Scope of local variables and constants. Stored in debug metadata.
            </summary>
            <remarks>
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#localscope-table-0x32.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.LocalVariable">
            <summary>
            Local variable. Stored in debug metadata.
            </summary>
            <remarks>
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#localvariable-table-0x33.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.MethodDebugInformation">
            <summary>
            Debug information associated with a method definition. Stored in debug metadata.
            </summary>
            <remarks>
            See https://github.com/dotnet/corefx/blob/master/src/System.Reflection.Metadata/specs/PortablePdb-Metadata.md#methoddebuginformation-table-0x31.
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.MethodDebugInformation.SequencePointsBlob">
            <summary>
            Returns a blob encoding sequence points.
            Use <see cref="M:System.Reflection.Metadata.MethodDebugInformation.GetSequencePoints"/> to decode.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodDebugInformation.Document">
            <summary>
            The document containing the first sequence point of the method, 
            or nil if the method doesn't have sequence points.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.MethodDebugInformation.LocalSignature">
            <summary>
            Returns local signature handle.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.MethodDebugInformation.GetStateMachineKickoffMethod">
            <summary>
            If the method is a MoveNext method of a state machine returns the kickoff method of the state machine, otherwise returns nil handle.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMethodBody(System.Reflection.PortableExecutable.PEReader,System.Int32)">
            <summary>
            Returns a body block of a method with specified Relative Virtual Address (RVA);
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="peReader"/> is null.</exception>
            <exception cref="T:System.BadImageFormatException">The body is not found in the metadata or is invalid.</exception>
            <exception cref="T:System.InvalidOperationException">Section where the method is stored is not available.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader)">
            <summary>
            Gets a <see cref="T:System.Reflection.Metadata.MetadataReader"/> from a <see cref="T:System.Reflection.PortableExecutable.PEReader"/>.
            </summary>
            <remarks>
            The caller must keep the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> alive and undisposed throughout the lifetime of the metadata reader.
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="peReader"/> is null</exception>
            <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader,System.Reflection.Metadata.MetadataReaderOptions)">
            <summary>
            Gets a <see cref="T:System.Reflection.Metadata.MetadataReader"/> from a <see cref="T:System.Reflection.PortableExecutable.PEReader"/>.
            </summary>
            <remarks>
            The caller must keep the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> alive and undisposed throughout the lifetime of the metadata reader.
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="peReader"/> is null</exception>
            <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
        </member>
        <member name="M:System.Reflection.Metadata.PEReaderExtensions.GetMetadataReader(System.Reflection.PortableExecutable.PEReader,System.Reflection.Metadata.MetadataReaderOptions,System.Reflection.Metadata.MetadataStringDecoder)">
            <summary>
            Gets a <see cref="T:System.Reflection.Metadata.MetadataReader"/> from a <see cref="T:System.Reflection.PortableExecutable.PEReader"/>.
            </summary>
            <remarks>
            The caller must keep the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> alive and undisposed throughout the lifetime of the metadata reader.
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="peReader"/> is null</exception>
            <exception cref="T:System.ArgumentException">The encoding of <paramref name="utf8Decoder"/> is not <see cref="T:System.Text.UTF8Encoding"/>.</exception>
            <exception cref="T:System.PlatformNotSupportedException">The current platform is big-endian.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
        </member>
        <member name="T:System.Reflection.Metadata.SerializationTypeCode">
            <summary>
            Type codes used to encode types of values in Custom Attribute value blob.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Invalid">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Invalid"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Boolean">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Boolean"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Char">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Char"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.SByte">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.SByte"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Byte">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Byte"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Int16">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Int16"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.UInt16">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.UInt16"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Int32">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Int32"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.UInt32">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.UInt32"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Int64">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Int64"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.UInt64">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.UInt64"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Single">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Single"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Double">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.Double"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.String">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.String"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.SZArray">
            <summary>
            Equivalent to <see cref="F:System.Reflection.Metadata.SignatureTypeCode.SZArray"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Type">
            <summary>
            The attribute argument is a System.Type instance.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.TaggedObject">
            <summary>
            The attribute argument is "boxed" (passed to a parameter, field, or property of type object) and carries type information in the attribute blob.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SerializationTypeCode.Enum">
            <summary>
            The attribute argument is an Enum instance.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.SignatureAttributes">
            <summary>
            Specified additional flags that can be applied to method signatures.
            Underlying values correspond to the representation in the leading signature 
            byte represented by <see cref="T:System.Reflection.Metadata.SignatureHeader"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureAttributes.None">
            <summary>
            No flags.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureAttributes.Generic">
            <summary>
            Generic method.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureAttributes.Instance">
            <summary>
            Instance method.
            </summary>
            <remarks>Ecma 335 CLI Specification refers to this flag as HAS_THIS.</remarks>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureAttributes.ExplicitThis">
            <summary>
            The first explicitly declared parameter represents the instance pointer.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.SignatureCallingConvention">
            <summary>
            Specifies how arguments in a given signature are passed from the caller to the callee.
            Underlying values correspond to the representation in the leading signature byte 
            represented by <see cref="T:System.Reflection.Metadata.SignatureHeader"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureCallingConvention.Default">
            <summary>
            Managed calling convention with fixed-length argument list.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureCallingConvention.CDecl">
            <summary>
            Unmanaged C/C++-style calling convention where the call stack is cleaned by the caller.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureCallingConvention.StdCall">
            <summary>
            Unmanaged calling convention where call stack is cleaned up by the callee.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureCallingConvention.ThisCall">
            <summary>
            Unmanaged C++-style calling convention for calling instance member functions with a fixed argument list.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureCallingConvention.FastCall">
            <summary>
            Unmanaged calling convention where arguments are passed in registers when possible.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureCallingConvention.VarArgs">
            <summary>
            Managed calling convention for passing extra arguments.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.SignatureHeader">
            <summary>
            Represents the signature characteristics specified by the leading byte of signature blobs.
            </summary>
            <remarks>
            This header byte is present in all method definition, method reference, standalone method, field, 
            property, and local variable signatures, but not in type specification signatures.
            </remarks>
        </member>
        <member name="T:System.Reflection.Metadata.SignatureKind">
            <summary>
            Specifies the signature kind. Underlying values correspond to the representation 
            in the leading signature byte represented by <see cref="T:System.Reflection.Metadata.SignatureHeader"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureKind.Method">
            <summary>
            Method reference, method definition, or standalone method signature.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureKind.Field">
            <summary>
            Field signature.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureKind.LocalVariables">
            <summary>
            Local variables signature.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureKind.Property">
            <summary>
            Property signature.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureKind.MethodSpecification">
            <summary>
            Method specification signature.
            </summary>
        </member>
        <member name="T:System.Reflection.Metadata.SignatureTypeCode">
            <summary>
            Represents the type codes that are used in signature encoding.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Invalid">
            <summary>
            Represents an invalid or uninitialized type code. It will not appear in valid signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Void">
            <summary>
            Represents <see cref="T:System.Void"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Boolean">
            <summary>
            Represents <see cref="T:System.Boolean"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Char">
            <summary>
            Represents <see cref="T:System.Char"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.SByte">
            <summary>
            Represents <see cref="T:System.SByte"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Byte">
            <summary>
            Represents <see cref="T:System.Byte"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Int16">
            <summary>
            Represents <see cref="T:System.Int16"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.UInt16">
            <summary>
            Represents <see cref="T:System.UInt16"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Int32">
            <summary>
            Represents <see cref="T:System.Int32"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.UInt32">
            <summary>
            Represents <see cref="T:System.UInt32"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Int64">
            <summary>
            Represents <see cref="T:System.Int64"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.UInt64">
            <summary>
            Represents <see cref="T:System.UInt64"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Single">
            <summary>
            Represents <see cref="T:System.Single"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Double">
            <summary>
            Represents <see cref="T:System.Double"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.String">
            <summary>
            Represents <see cref="T:System.String"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Pointer">
            <summary>
            Represents a unmanaged pointers in signatures.
            It is followed in the blob by the signature encoding of the underlying type.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.ByReference">
            <summary>
            Represents managed pointers (byref return values and parameters) in signatures.
            It is followed in the blob by the signature encoding of the underlying type.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.GenericTypeParameter">
            <summary>
            Represents a generic type parameter used within a signature. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Array">
            <summary>
            Represents a generalized <see cref="T:System.Array"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.GenericTypeInstance">
            <summary>
            Represents the instantiation of a generic type in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.TypedReference">
            <summary>
            Represents a System.TypedReference in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.IntPtr">
            <summary>
            Represents a <see cref="T:System.IntPtr"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.UIntPtr">
            <summary>
            Represents a <see cref="T:System.UIntPtr"/> in signatures.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.FunctionPointer">
            <summary>
            Represents function pointer types in signatures. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Object">
            <summary>
            Represents <see cref="T:System.Object"/>
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.SZArray">
            <summary>
            Represents a single dimensional <see cref="T:System.Array"/> with 0 lower bound.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.GenericMethodParameter">
            <summary>
            Represents a generic method parameter used within a signature. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.RequiredModifier">
            <summary>
            Represents a custom modifier applied to a type within a signature that the caller must understand. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.OptionalModifier">
            <summary>
            Represents a custom modifier applied to a type within a signature that the caller can ignore.
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.TypeHandle">
            <summary>
            Precedes a type <see cref="T:System.Reflection.Metadata.EntityHandle"/> in signatures.
            </summary>
            <remarks>
            In raw metadata, this will be encoded as either ELEMENT_TYPE_CLASS (0x12) for reference
            types and ELEMENT_TYPE_VALUETYPE (0x11) for value types. This is collapsed to a single
            code because Windows Runtime projections can project from class to value type or vice-versa
            and the raw code is misleading in those cases.
            </remarks>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Sentinel">
            <summary>
            Represents a marker to indicate the end of fixed arguments and the beginning of variable arguments. 
            </summary>
        </member>
        <member name="F:System.Reflection.Metadata.SignatureTypeCode.Pinned">
            <summary>
            Represents a local variable that is pinned by garbage collector
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.StandaloneSignature.Signature">
            <summary>
            Gets a handle to the signature blob.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.StandaloneSignature.GetKind">
            <summary>
            Determines the kind of signature, which can be <see cref="F:System.Reflection.Metadata.SignatureKind.Method"/> or <see cref="F:System.Reflection.Metadata.SignatureKind.LocalVariables"/>
            </summary>
            <exception cref="T:System.BadImageFormatException">The signature is invalid.</exception>
        </member>
        <member name="P:System.Reflection.Metadata.TypeDefinition.Name">
            <summary>
            Name of the type.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.TypeDefinition.Namespace">
            <summary>
            Full name of the namespace where the type is defined, or nil if the type is nested or defined in a root namespace.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.TypeDefinition.NamespaceDefinition">
            <summary>
            The definition handle of the namespace where the type is defined, or nil if the type is nested or defined in a root namespace.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.TypeDefinition.BaseType">
            <summary>
            The base type of the type definition: either
            <see cref="T:System.Reflection.Metadata.TypeSpecificationHandle"/>, <see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> or <see cref="T:System.Reflection.Metadata.TypeDefinitionHandle"/>.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.TypeDefinition.GetDeclaringType">
            <summary>
            Returns the enclosing type of a specified nested type or nil handle if the type is not nested.
            </summary>
        </member>
        <member name="M:System.Reflection.Metadata.TypeDefinition.GetNestedTypes">
            <summary>
            Returns an array of types nested in the specified type.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.TypeReference.ResolutionScope">
            <summary>
            Resolution scope in which the target type is defined and is uniquely identified by the specified <see cref="P:System.Reflection.Metadata.TypeReference.Namespace"/> and <see cref="P:System.Reflection.Metadata.TypeReference.Name"/>.
            </summary>
            <remarks>
            Resolution scope can be one of the following handles:
            <list type="bullet">
            <item><description><see cref="T:System.Reflection.Metadata.TypeReferenceHandle"/> of the enclosing type, if the target type is a nested type.</description></item>
            <item><description><see cref="T:System.Reflection.Metadata.ModuleReferenceHandle"/>, if the target type is defined in another module within the same assembly as this one.</description></item>
            <item><description><see cref="F:System.Reflection.Metadata.EntityHandle.ModuleDefinition"/>, if the target type is defined in the current module. This should not occur in a CLI compressed metadata module.</description></item>
            <item><description><see cref="T:System.Reflection.Metadata.AssemblyReferenceHandle"/>, if the target type is defined in a different assembly from the current module.</description></item>
            <item><description>Nil handle if the target type must be resolved by searching the <see cref="P:System.Reflection.Metadata.MetadataReader.ExportedTypes"/> for a matching <see cref="P:System.Reflection.Metadata.TypeReference.Namespace"/> and <see cref="P:System.Reflection.Metadata.TypeReference.Name"/>.</description></item>
            </list>
            </remarks>
        </member>
        <member name="P:System.Reflection.Metadata.TypeReference.Name">
            <summary>
            Name of the target type.
            </summary>
        </member>
        <member name="P:System.Reflection.Metadata.TypeReference.Namespace">
            <summary>
            Full name of the namespace where the target type is defined, or nil if the type is nested or defined in a root namespace.
            </summary>
        </member>
        <member name="T:System.Reflection.PortableExecutable.ManagedTextSection">
            <summary>
            Managed .text PE section.
            </summary>
            <remarks>
            Contains in the following order:
            - Import Address Table
            - COR Header
            - IL
            - Metadata
            - Managed Resource Data
            - Strong Name Signature
            - Debug Data (directory and extra info)
            - Import Table
            - Name Table
            - Runtime Startup Stub
            - Mapped Field Data
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.ILStreamSize">
            <summary>
            The size of IL stream (unaligned).
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.MetadataSize">
            <summary>
            Total size of metadata (header and all streams).
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.ResourceDataSize">
            <summary>
            The size of managed resource data stream.
            Aligned to <see cref="F:System.Reflection.PortableExecutable.ManagedTextSection.ManagedResourcesDataAlignment"/>.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.StrongNameSignatureSize">
            <summary>
            Size of strong name hash.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.DebugDataSize">
            <summary>
            Size of Debug data.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.MappedFieldDataSize">
            <summary>
            The size of mapped field data stream.
            Aligned to <see cref="F:System.Reflection.PortableExecutable.ManagedTextSection.MappedFieldDataAlignment"/>.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.RequiresStartupStub">
            <summary>
            If set, the module must include a machine code stub that transfers control to the virtual execution system.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.ManagedTextSection.Requires64bits">
            <summary>
            If set, the module contains instructions that assume a 64 bit instruction set. For example it may depend on an address being 64 bits.
            This may be true even if the module contains only IL instructions because of PlatformInvoke and COM interop.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.ManagedTextSection.Serialize(System.Reflection.Metadata.BlobBuilder,System.Int32,System.Int32,System.Reflection.PortableExecutable.CorFlags,System.UInt64,System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.BlobBuilder,System.Reflection.Metadata.Blob@)">
            <summary>
            Serializes .text section data into a specified <paramref name="builder"/>.
            </summary>
            <param name="builder">An empty builder to serialize section data to.</param>
            <param name="relativeVirtualAddess">Relative virtual address of the section within the containing PE file.</param>
            <param name="entryPointTokenOrRelativeVirtualAddress">Entry point token or RVA (<see cref="P:System.Reflection.PortableExecutable.CorHeader.EntryPointTokenOrRelativeVirtualAddress"/>)</param>
            <param name="corFlags">COR Flags (<see cref="P:System.Reflection.PortableExecutable.CorHeader.Flags"/>).</param>
            <param name="baseAddress">Base address of the PE image.</param>
            <param name="metadataBuilder"><see cref="T:System.Reflection.Metadata.BlobBuilder"/> containing metadata. Must be populated with data. Linked into the <paramref name="builder"/> and can't be expanded afterwards.</param>
            <param name="ilBuilder"><see cref="T:System.Reflection.Metadata.BlobBuilder"/> containing IL stream. Must be populated with data. Linked into the <paramref name="builder"/> and can't be expanded afterwards.</param>
            <param name="mappedFieldDataBuilderOpt"><see cref="T:System.Reflection.Metadata.BlobBuilder"/> containing mapped field data. Must be populated with data. Linked into the <paramref name="builder"/> and can't be expanded afterwards.</param>
            <param name="resourceBuilderOpt"><see cref="T:System.Reflection.Metadata.BlobBuilder"/> containing managed resource data. Must be populated with data. Linked into the <paramref name="builder"/> and can't be expanded afterwards.</param>
            <param name="debugDataBuilderOpt"><see cref="T:System.Reflection.Metadata.BlobBuilder"/> containing PE debug table and data. Must be populated with data. Linked into the <paramref name="builder"/> and can't be expanded afterwards.</param>
            <param name="strongNameSignature">Blob reserved in the <paramref name="builder"/> for strong name signature.</param>
        </member>
        <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddCodeViewEntry(System.String,System.Reflection.Metadata.BlobContentId,System.UInt16)">
            <summary>
            Adds a CodeView entry.
            </summary>
            <param name="pdbPath">Path to the PDB. Shall not be empty.</param>
            <param name="pdbContentId">Unique id of the PDB content.</param>
            <param name="portablePdbVersion">Version of Portable PDB format (e.g. 0x0100 for 1.0), or 0 if the PDB is not portable.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="pdbPath"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="pdbPath"/> contains NUL character.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="portablePdbVersion"/> is smaller than 0x0100.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddReproducibleEntry">
            <summary>
            Adds Reproducible entry.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.AddEmbeddedPortablePdbEntry(System.Reflection.Metadata.BlobBuilder,System.UInt16)">
            <summary>
            Adds Embedded Portable PDB entry.
            </summary>
            <param name="debugMetadata">Portable PDB metadata builder.</param>
            <param name="portablePdbVersion">Version of Portable PDB format (e.g. 0x0100 for 1.0).</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="debugMetadata"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="portablePdbVersion"/> is smaller than 0x0100.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.DebugDirectoryBuilder.Serialize(System.Reflection.Metadata.BlobBuilder,System.Reflection.PortableExecutable.SectionLocation,System.Int32)">
            <summary>
            Serialize the Debug Table and Data.
            </summary>
            <param name="builder">Builder.</param>
            <param name="sectionLocation">The containing PE section location.</param>
            <param name="sectionOffset">Offset of the table within the containing section.</param>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ExportTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_EXPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ImportTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_IMPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ResourceTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_RESOURCE.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ExceptionTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_EXCEPTION.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.BaseRelocationTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_BASERELOC.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.DebugTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_DEBUG.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.CopyrightTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_COPYRIGHT or IMAGE_DIRECTORY_ENTRY_ARCHITECTURE.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.GlobalPointerTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_GLOBALPTR.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ThreadLocalStorageTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_TLS.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.LoadConfigTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_LOAD_CONFIG.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.BoundImportTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_BOUND_IMPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.ImportAddressTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_IAT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.DelayImportTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_DELAY_IMPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEDirectoriesBuilder.CorHeaderTable">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_COM_DESCRIPTOR.
            </remarks>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEHeaderBuilder.#ctor(System.Reflection.PortableExecutable.Machine,System.Int32,System.Int32,System.UInt64,System.Byte,System.Byte,System.UInt16,System.UInt16,System.UInt16,System.UInt16,System.UInt16,System.UInt16,System.Reflection.PortableExecutable.Subsystem,System.Reflection.PortableExecutable.DllCharacteristics,System.Reflection.PortableExecutable.Characteristics,System.UInt64,System.UInt64,System.UInt64,System.UInt64)">
            <summary>
            Creates PE header builder.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="fileAlignment"/> is not power of 2 between 512 and 64K, or
            <paramref name="sectionAlignment"/> not power of 2 or it's less than <paramref name="fileAlignment"/>.
            </exception>
        </member>
        <member name="T:System.Reflection.PortableExecutable.ResourceSectionBuilder">
            <summary>
            Base class for PE resource section builder. Implement to provide serialization logic for native resources.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CoffHeader.Machine">
            <summary>
            The type of target machine.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CoffHeader.NumberOfSections">
            <summary>
            The number of sections. This indicates the size of the section table, which immediately follows the headers.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CoffHeader.TimeDateStamp">
            <summary>
            The low 32 bits of the number of seconds since 00:00 January 1, 1970, that indicates when the file was created.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CoffHeader.PointerToSymbolTable">
            <summary>
            The file pointer to the COFF symbol table, or zero if no COFF symbol table is present. 
            This value should be zero for a PE image.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CoffHeader.NumberOfSymbols">
            <summary>
            The number of entries in the symbol table. This data can be used to locate the string table, 
            which immediately follows the symbol table. This value should be zero for a PE image.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CoffHeader.SizeOfOptionalHeader">
            <summary>
            The size of the optional header, which is required for executable files but not for object files. 
            This value should be zero for an object file. 
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CoffHeader.Characteristics">
            <summary>
            The flags that indicate the attributes of the file. 
            </summary>
        </member>
        <member name="T:System.Reflection.PortableExecutable.CorFlags">
            <summary>
            COR20Flags
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CodeViewDebugDirectoryData.Guid">
            <summary>
            GUID (Globally Unique Identifier) of the associated PDB.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CodeViewDebugDirectoryData.Age">
            <summary>
            Iteration of the PDB. The first iteration is 1. The iteration is incremented each time the PDB content is augmented.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.CodeViewDebugDirectoryData.Path">
            <summary>
            Path to the .pdb file containing debug information for the PE/COFF file.
            </summary>
        </member>
        <member name="T:System.Reflection.PortableExecutable.DebugDirectoryEntry">
            <summary>
            Identifies the location, size and format of a block of debug information.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.Stamp">
            <summary>
            The time and date that the debug data was created if the PE/COFF file is not deterministic,
            otherwise a value based on the hash of the content. 
            </summary>
            <remarks>
            The algorithm used to calculate this value is an implementation 
            detail of the tool that produced the file.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.MajorVersion">
            <summary>
            The major version number of the debug data format.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.MinorVersion">
            <summary>
            The minor version number of the debug data format.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.Type">
            <summary>
            The format of debugging information. 
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.DataSize">
            <summary>
            The size of the debug data (not including the debug directory itself).
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.DataRelativeVirtualAddress">
            <summary>
            The address of the debug data when loaded, relative to the image base.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.DataPointer">
            <summary>
            The file pointer to the debug data.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.DebugDirectoryEntry.IsPortableCodeView">
            <summary>
            True if the the entry is a <see cref="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.CodeView"/> entry pointing to a Portable PDB.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.Unknown">
            <summary>
            An unknown value that is ignored by all tools.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.Coff">
            <summary>
            The COFF debug information (line numbers, symbol table, and string table). 
            This type of debug information is also pointed to by fields in the file headers.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.CodeView">
            <summary>
            Associated PDB file description.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.Reproducible">
            <summary>
            Presence of this entry indicates deterministic PE/COFF file.
            </summary>
            <remarks>
            <para>
            The tool that produced the deterministic PE/COFF file guarantees that the entire content of the file 
            is based solely on documented inputs given to the tool (such as source files, resource files, compiler options, etc.) 
            rather than ambient environment variables (such as the current time, the operating system, 
            the bitness of the process running the tool, etc.).
            </para>
            <para>
            The value of field TimeDateStamp in COFF File Header of a deterministic PE/COFF file 
            does not indicate the date and time when the file was produced and should not be interpreted that way.
            Instead the value of the field is derived from a hash of the file content. The algorithm to calculate 
            this value is an implementation detail of the tool that produced the file.
            </para>
            <para>
            The debug directory entry of type <see cref="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.Reproducible"/> must have all fields, except for Type zeroed.
            </para>
            </remarks>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.EmbeddedPortablePdb">
            <summary>
            The entry points to a blob containing Embedded Portable PDB.
            </summary>
            <remarks>
            The Embedded Portable PDB blob has the following format:
            
            blob ::= uncompressed-size data
            
            Data spans the remainder of the blob and contains a Deflate-compressed Portable PDB.
            </remarks>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Unknown">
            <summary>
            The target CPU is unknown or not specified.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.I386">
            <summary>
            Intel 386.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.WceMipsV2">
            <summary>
            MIPS little-endian WCE v2
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Alpha">
            <summary>
            Alpha
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.SH3">
            <summary>
            Hitachi SH3 little endian
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.SH3Dsp">
            <summary>
            Hitachi SH3 DSP.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.SH3E">
            <summary>
            Hitachi SH3 little endian.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.SH4">
            <summary>
            Hitachi SH4 little endian.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.SH5">
            <summary>
            Hitachi SH5.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Arm">
            <summary>
            ARM little endian
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Thumb">
            <summary>
            Thumb.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.ArmThumb2">
            <summary>
            ARM Thumb-2 little endian.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.AM33">
            <summary>
            Matsushita AM33.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.PowerPC">
            <summary>
            IBM PowerPC little endian.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.PowerPCFP">
            <summary>
            PowerPCFP
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.IA64">
            <summary>
            Intel 64
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.MIPS16">
            <summary>
            MIPS
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Alpha64">
            <summary>
            ALPHA64
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.MipsFpu">
            <summary>
            MIPS with FPU.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.MipsFpu16">
            <summary>
            MIPS16 with FPU.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Tricore">
            <summary>
            Infineon
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Ebc">
            <summary>
            EFI Byte Code
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.Amd64">
            <summary>
            AMD64 (K8)
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.Machine.M32R">
            <summary>
            M32R little-endian
            </summary>
        </member>
        <member name="T:System.Reflection.PortableExecutable.PEBinaryReader">
             <summary>
             Simple BinaryReader wrapper to:
            
              1) throw BadImageFormat instead of EndOfStream or ArgumentOutOfRange.
              2) limit reads to a subset of the base stream.
            
             Only methods that are needed to read PE headers are implemented.
             </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEBinaryReader.ReadNullPaddedUTF8(System.Int32)">
            <summary>
            Reads a fixed-length byte block as a null-padded UTF8-encoded string.
            The padding is not included in the returned string.
            
            Note that it is legal for UTF8 strings to contain NUL; if NUL occurs
            between non-NUL codepoints, it is not considered to be padding and
            is included in the result.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ProcessInit">
            <summary>
            Reserved.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ProcessTerm">
            <summary>
            Reserved.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ThreadInit">
            <summary>
            Reserved.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.ThreadTerm">
            <summary>
            Reserved.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.HighEntropyVirtualAddressSpace">
            <summary>
            Image can handle a high entropy 64-bit virtual address space.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.DynamicBase">
            <summary>
            DLL can move.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NxCompatible">
            <summary>
            Image is NX compatible.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NoIsolation">
            <summary>
            Image understands isolation and doesn't want it.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NoSeh">
            <summary>
            Image does not use SEH.  No SE handler may reside in this image.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.NoBind">
            <summary>
            Do not bind this image.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.AppContainer">
            <summary>
            The image must run inside an AppContainer.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.DllCharacteristics.WdmDriver">
            <summary>
            Driver uses WDM model.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.Magic">
            <summary>
            Identifies the format of the image file.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorLinkerVersion">
            <summary>
            The linker major version number.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorLinkerVersion">
            <summary>
            The linker minor version number.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfCode">
            <summary>
            The size of the code (text) section, or the sum of all code sections if there are multiple sections.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfInitializedData">
            <summary>
            The size of the initialized data section, or the sum of all such sections if there are multiple data sections.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfUninitializedData">
            <summary>
            The size of the uninitialized data section (BSS), or the sum of all such sections if there are multiple BSS sections.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.AddressOfEntryPoint">
            <summary>
            The address of the entry point relative to the image base when the PE file is loaded into memory. 
            For program images, this is the starting address. For device drivers, this is the address of the initialization function.
            An entry point is optional for DLLs. When no entry point is present, this field must be zero.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.BaseOfCode">
            <summary>
            The address that is relative to the image base of the beginning-of-code section when it is loaded into memory.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.BaseOfData">
            <summary>
            The address that is relative to the image base of the beginning-of-data section when it is loaded into memory.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.ImageBase">
            <summary>
            The preferred address of the first byte of image when loaded into memory; 
            must be a multiple of 64K.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SectionAlignment">
            <summary>
            The alignment (in bytes) of sections when they are loaded into memory. It must be greater than or equal to <see cref="P:System.Reflection.PortableExecutable.PEHeader.FileAlignment"/>. 
            The default is the page size for the architecture.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.FileAlignment">
            <summary>
            The alignment factor (in bytes) that is used to align the raw data of sections in the image file. 
            The value should be a power of 2 between 512 and 64K, inclusive. The default is 512. 
            If the <see cref="P:System.Reflection.PortableExecutable.PEHeader.SectionAlignment"/> is less than the architecture's page size, 
            then <see cref="P:System.Reflection.PortableExecutable.PEHeader.FileAlignment"/> must match <see cref="P:System.Reflection.PortableExecutable.PEHeader.SectionAlignment"/>.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorOperatingSystemVersion">
            <summary>
            The major version number of the required operating system.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorOperatingSystemVersion">
            <summary>
            The minor version number of the required operating system.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorImageVersion">
            <summary>
            The major version number of the image.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorImageVersion">
            <summary>
            The minor version number of the image.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MajorSubsystemVersion">
            <summary>
            The major version number of the subsystem.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.MinorSubsystemVersion">
            <summary>
            The minor version number of the subsystem.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfImage">
            <summary>
            The size (in bytes) of the image, including all headers, as the image is loaded in memory. 
            It must be a multiple of <see cref="P:System.Reflection.PortableExecutable.PEHeader.SectionAlignment"/>.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeaders">
            <summary>
            The combined size of an MS DOS stub, PE header, and section headers rounded up to a multiple of FileAlignment.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.CheckSum">
            <summary>
            The image file checksum. 
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.Subsystem">
            <summary>
            The subsystem that is required to run this image. 
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfStackReserve">
            <summary>
            The size of the stack to reserve. Only <see cref="P:System.Reflection.PortableExecutable.PEHeader.SizeOfStackCommit"/> is committed; 
            the rest is made available one page at a time until the reserve size is reached.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfStackCommit">
            <summary>
            The size of the stack to commit.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeapReserve">
            <summary>
            The size of the local heap space to reserve. Only <see cref="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeapCommit"/> is committed; 
            the rest is made available one page at a time until the reserve size is reached.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.SizeOfHeapCommit">
            <summary>
            The size of the local heap space to commit.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.NumberOfRvaAndSizes">
            <summary>
            The number of data-directory entries in the remainder of the <see cref="T:System.Reflection.PortableExecutable.PEHeader"/>. Each describes a location and size.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.ExportTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_EXPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.ImportTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_IMPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.ResourceTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_RESOURCE.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.ExceptionTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_EXCEPTION.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.CertificateTableDirectory">
            <summary>
            The Certificate Table entry points to a table of attribute certificates. 
            </summary>
            <remarks>
            These certificates are not loaded into memory as part of the image.
            As such, the first field of this entry, which is normally an RVA, is a file pointer instead.
            
            Aka IMAGE_DIRECTORY_ENTRY_SECURITY.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.BaseRelocationTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_BASERELOC.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.DebugTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_DEBUG.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.CopyrightTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_COPYRIGHT or IMAGE_DIRECTORY_ENTRY_ARCHITECTURE.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.GlobalPointerTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_GLOBALPTR.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.ThreadLocalStorageTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_TLS.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.LoadConfigTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_LOAD_CONFIG.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.BoundImportTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_BOUND_IMPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.ImportAddressTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_IAT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.DelayImportTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_DELAY_IMPORT.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeader.CorHeaderTableDirectory">
            <remarks>
            Aka IMAGE_DIRECTORY_ENTRY_COM_DESCRIPTOR.
            </remarks>
        </member>
        <member name="T:System.Reflection.PortableExecutable.PEHeaders">
            <summary>
            An object used to read PE (Portable Executable) and COFF (Common Object File Format) headers from a stream.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEHeaders.#ctor(System.IO.Stream)">
            <summary>
            Reads PE headers from the current location in the stream.
            </summary>
            <param name="peStream">Stream containing PE image starting at the stream's current position and ending at the end of the stream.</param>
            <exception cref="T:System.BadImageFormatException">The data read from stream have invalid format.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
            <exception cref="T:System.ArgumentException">The stream doesn't support seek operations.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="peStream"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEHeaders.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Reads PE headers from the current location in the stream.
            </summary>
            <param name="peStream">Stream containing PE image of the given size starting at its current position.</param>
            <param name="size">Size of the PE image.</param>
            <exception cref="T:System.BadImageFormatException">The data read from stream have invalid format.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
            <exception cref="T:System.ArgumentException">The stream doesn't support seek operations.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="peStream"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end of the stream.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEHeaders.#ctor(System.IO.Stream,System.Int32,System.Boolean)">
            <summary>
            Reads PE headers from the current location in the stream.
            </summary>
            <param name="peStream">Stream containing PE image of the given size starting at its current position.</param>
            <param name="size">Size of the PE image.</param>
            <param name="isLoadedImage">True if the PE image has been loaded into memory by the OS loader.</param>
            <exception cref="T:System.BadImageFormatException">The data read from stream have invalid format.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
            <exception cref="T:System.ArgumentException">The stream doesn't support seek operations.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="peStream"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end of the stream.</exception>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.MetadataStartOffset">
            <summary>
            Gets the offset (in bytes) from the start of the PE image to the start of the CLI metadata.
            or -1 if the image does not contain metadata.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.MetadataSize">
            <summary>
            Gets the size of the CLI metadata 0 if the image does not contain metadata.)
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.CoffHeader">
            <summary>
            Gets the COFF header of the image.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.CoffHeaderStartOffset">
            <summary>
            Gets the byte offset from the start of the PE image to the start of the COFF header.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsCoffOnly">
            <summary>
            Determines if the image is Coff only.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.PEHeader">
            <summary>
            Gets the PE header of the image or null if the image is COFF only.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.PEHeaderStartOffset">
            <summary>
            Gets the byte offset from the start of the image to 
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.SectionHeaders">
            <summary>
            Gets the PE section headers.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.CorHeader">
            <summary>
            Gets the CLI header or null if the image does not have one.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.CorHeaderStartOffset">
            <summary>
            Gets the byte offset from the start of the image to the COR header or -1 if the image does not have one.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsConsoleApplication">
            <summary>
            Determines if the image represents a Windows console application.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsDll">
            <summary>
            Determines if the image represents a dynamically linked library.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEHeaders.IsExe">
            <summary>
            Determines if the image represents an executable.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEHeaders.TryGetDirectoryOffset(System.Reflection.PortableExecutable.DirectoryEntry,System.Int32@)">
            <summary>
            Gets the offset (in bytes) from the start of the image to the given directory data.
            </summary>
            <param name="directory">PE directory entry</param>
            <param name="offset">Offset from the start of the image to the given directory data</param>
            <returns>True if the directory data is found, false otherwise.</returns>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEHeaders.GetContainingSectionIndex(System.Int32)">
            <summary>
            Searches sections of the PE image for the one that contains specified Relative Virtual Address.
            </summary>
            <param name="relativeVirtualAddress">Address.</param>
            <returns>
            Index of the section that contains <paramref name="relativeVirtualAddress"/>,
            or -1 if there is none.
            </returns>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEMemoryBlock.Pointer">
            <summary>
            Pointer to the first byte of the block.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEMemoryBlock.Length">
            <summary>
            Length of the block.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetReader">
            <summary>
            Creates <see cref="T:System.Reflection.Metadata.BlobReader"/> for a blob spanning the entire block.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetReader(System.Int32,System.Int32)">
            <summary>
            Creates <see cref="T:System.Reflection.Metadata.BlobReader"/> for a blob spanning a part of the block.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Specified range is not contained within the block.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetContent">
            <summary>
            Reads the content of the entire block into an array.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEMemoryBlock.GetContent(System.Int32,System.Int32)">
            <summary>
            Reads the content of a part of the block into an array.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Specified range is not contained within the block.</exception>
        </member>
        <member name="T:System.Reflection.PortableExecutable.PEReader">
            <summary>
            Portable Executable format reader.
            </summary>
            <remarks>
            The implementation is thread-safe, that is multiple threads can read data from the reader in parallel.
            Disposal of the reader is not thread-safe (see <see cref="M:System.Reflection.PortableExecutable.PEReader.Dispose"/>).
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEReader.IsLoadedImage">
            <summary>
            True if the PE image has been loaded into memory by the OS loader.
            </summary>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.Byte*,System.Int32)">
            <summary>
            Creates a Portable Executable reader over a PE image stored in memory.
            </summary>
            <param name="peImage">Pointer to the start of the PE image.</param>
            <param name="size">The size of the PE image.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="peImage"/> is <see cref="F:System.IntPtr.Zero"/>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="size"/> is negative.</exception>
            <remarks>
            The memory is owned by the caller and not released on disposal of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>.
            The caller is responsible for keeping the memory alive and unmodified throughout the lifetime of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>.
            The content of the image is not read during the construction of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>
            </remarks>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.Byte*,System.Int32,System.Boolean)">
            <summary>
            Creates a Portable Executable reader over a PE image stored in memory.
            </summary>
            <param name="peImage">Pointer to the start of the PE image.</param>
            <param name="size">The size of the PE image.</param>
            <param name="isLoadedImage">True if the PE image has been loaded into memory by the OS loader.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="peImage"/> is <see cref="F:System.IntPtr.Zero"/>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="size"/> is negative.</exception>
            <remarks>
            The memory is owned by the caller and not released on disposal of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>.
            The caller is responsible for keeping the memory alive and unmodified throughout the lifetime of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>.
            The content of the image is not read during the construction of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>
            </remarks>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.IO.Stream)">
            <summary>
            Creates a Portable Executable reader over a PE image stored in a stream.
            </summary>
            <param name="peStream">PE image stream.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="peStream"/> is null.</exception>
            <remarks>
            Ownership of the stream is transferred to the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> upon successful validation of constructor arguments. It will be 
            disposed by the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> and the caller must not manipulate it.
            </remarks>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.IO.Stream,System.Reflection.PortableExecutable.PEStreamOptions)">
            <summary>
            Creates a Portable Executable reader over a PE image stored in a stream beginning at its current position and ending at the end of the stream.
            </summary>
            <param name="peStream">PE image stream.</param>
            <param name="options">
            Options specifying how sections of the PE image are read from the stream.
            
            Unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen"/> is specified, ownership of the stream is transferred to the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> 
            upon successful argument validation. It will be disposed by the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> and the caller must not manipulate it.
            
            Unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata"/> or <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchEntireImage"/> is specified no data 
            is read from the stream during the construction of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>. Furthermore, the stream must not be manipulated
            by caller while the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> is alive and undisposed.
            
            If <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata"/> or <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchEntireImage"/>, the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> 
            will have read all of the data requested during construction. As such, if <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen"/> is also
            specified, the caller retains full ownership of the stream and is assured that it will not be manipulated by the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>
            after construction.
            </param>
            <exception cref="T:System.ArgumentNullException"><paramref name="peStream"/> is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="options"/> has an invalid value.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the stream (only when prefetching data).</exception>
            <exception cref="T:System.BadImageFormatException"><see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata"/> is specified and the PE headers of the image are invalid.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.IO.Stream,System.Reflection.PortableExecutable.PEStreamOptions,System.Int32)">
            <summary>
            Creates a Portable Executable reader over a PE image of the given size beginning at the stream's current position.
            </summary>
            <param name="peStream">PE image stream.</param>
            <param name="size">PE image size.</param>
            <param name="options">
            Options specifying how sections of the PE image are read from the stream.
            
            Unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen"/> is specified, ownership of the stream is transferred to the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> 
            upon successful argument validation. It will be disposed by the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> and the caller must not manipulate it.
            
            Unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata"/> or <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchEntireImage"/> is specified no data 
            is read from the stream during the construction of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>. Furthermore, the stream must not be manipulated
            by caller while the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> is alive and undisposed.
            
            If <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata"/> or <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchEntireImage"/>, the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> 
            will have read all of the data requested during construction. As such, if <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen"/> is also
            specified, the caller retains full ownership of the stream and is assured that it will not be manipulated by the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>
            after construction.
            </param>
            <exception cref="T:System.ArgumentOutOfRangeException">Size is negative or extends past the end of the stream.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the stream (only when prefetching data).</exception>
            <exception cref="T:System.BadImageFormatException"><see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata"/> is specified and the PE headers of the image are invalid.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.#ctor(System.Collections.Immutable.ImmutableArray{System.Byte})">
            <summary>
            Creates a Portable Executable reader over a PE image stored in a byte array.
            </summary>
            <param name="peImage">PE image.</param>
            <remarks>
            The content of the image is not read during the construction of the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="peImage"/> is null.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.Dispose">
            <summary>
            Disposes all memory allocated by the reader.
            </summary>
            <remarks>
            <see cref="M:System.Reflection.PortableExecutable.PEReader.Dispose"/>  can be called multiple times (but not in parallel).
            It is not safe to call <see cref="M:System.Reflection.PortableExecutable.PEReader.Dispose"/> in parallel with any other operation on the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>
            or reading from <see cref="T:System.Reflection.PortableExecutable.PEMemoryBlock"/>s retrieved from the reader.
            </remarks>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEReader.PEHeaders">
            <summary>
            Gets the PE headers.
            </summary>
            <exception cref="T:System.BadImageFormatException">The headers contain invalid data.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.InitializePEHeaders">
            <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.ReadPEHeadersNoLock(System.IO.Stream,System.Int64,System.Int32,System.Boolean)">
            <exception cref="T:System.IO.IOException">Error reading from the stream.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.GetEntireImageBlock">
            <summary>
            Returns a view of the entire image as a pointer and length.
            </summary>
            <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.GetMetadataBlock">
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
            <exception cref="T:System.InvalidOperationException">PE image doesn't have metadata.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.GetPESectionBlock(System.Int32)">
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
            <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEReader.IsEntireImageAvailable">
            <summary>
            Return true if the reader can access the entire PE image.
            </summary>
            <remarks>
            Returns false if the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> is constructed from a stream and only part of it is prefetched into memory.
            </remarks>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.GetEntireImage">
            <summary>
            Gets a pointer to and size of the PE image if available (<see cref="P:System.Reflection.PortableExecutable.PEReader.IsEntireImageAvailable"/>).
            </summary>
            <exception cref="T:System.InvalidOperationException">The entire PE image is not available.</exception>
        </member>
        <member name="P:System.Reflection.PortableExecutable.PEReader.HasMetadata">
            <summary>
            Returns true if the PE image contains CLI metadata.
            </summary>
            <exception cref="T:System.BadImageFormatException">The PE headers contain invalid data.</exception>
            <exception cref="T:System.IO.IOException">Error reading from the underlying stream.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.GetMetadata">
            <summary>
            Loads PE section that contains CLI metadata.
            </summary>
            <exception cref="T:System.InvalidOperationException">The PE image doesn't contain metadata (<see cref="P:System.Reflection.PortableExecutable.PEReader.HasMetadata"/> returns false).</exception>
            <exception cref="T:System.BadImageFormatException">The PE headers contain invalid data.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.GetSectionData(System.Int32)">
            <summary>
            Loads PE section that contains the specified <paramref name="relativeVirtualAddress"/> into memory
            and returns a memory block that starts at <paramref name="relativeVirtualAddress"/> and ends at the end of the containing section.
            </summary>
            <param name="relativeVirtualAddress">Relative Virtual Address of the data to read.</param>
            <returns>
            An empty block if <paramref name="relativeVirtualAddress"/> doesn't represent a location in any of the PE sections of this PE image.
            </returns>
            <exception cref="T:System.BadImageFormatException">The PE headers contain invalid data.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
            <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="relativeVirtualAddress"/> is negative.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.GetSectionData(System.String)">
            <summary>
            Loads PE section of the specified name into memory and returns a memory block that spans the section.
            </summary>
            <param name="sectionName">Name of the section.</param>
            <returns>
            An empty block if no section of the given <paramref name="sectionName"/> exists in this PE image.
            </returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="sectionName"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.ReadDebugDirectory">
            <summary>
            Reads all Debug Directory table entries.
            </summary>
            <exception cref="T:System.BadImageFormatException">Bad format of the entry.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
            <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.ReadCodeViewDebugDirectoryData(System.Reflection.PortableExecutable.DebugDirectoryEntry)">
            <summary>
            Reads the data pointed to by the specified Debug Directory entry and interprets them as CodeView.
            </summary>
            <exception cref="T:System.ArgumentException"><paramref name="entry"/> is not a CodeView entry.</exception>
            <exception cref="T:System.BadImageFormatException">Bad format of the data.</exception>
            <exception cref="T:System.IO.IOException">IO error while reading from the underlying stream.</exception>
            <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.ReadEmbeddedPortablePdbDebugDirectoryData(System.Reflection.PortableExecutable.DebugDirectoryEntry)">
            <summary>
            Reads the data pointed to by the specified Debug Directory entry and interprets them as Embedded Portable PDB blob.
            </summary>
            <returns>
            Provider of a metadata reader reading Portable PDB image.
            </returns>
            <exception cref="T:System.ArgumentException"><paramref name="entry"/> is not a <see cref="F:System.Reflection.PortableExecutable.DebugDirectoryEntryType.EmbeddedPortablePdb"/> entry.</exception>
            <exception cref="T:System.BadImageFormatException">Bad format of the data.</exception>
            <exception cref="T:System.InvalidOperationException">PE image not available.</exception>
        </member>
        <member name="M:System.Reflection.PortableExecutable.PEReader.TryOpenAssociatedPortablePdb(System.String,System.Func{System.String,System.IO.Stream},System.Reflection.Metadata.MetadataReaderProvider@,System.String@)">
            <summary>
            Opens a Portable PDB associated with this PE image.
            </summary>
            <param name="peImagePath">
            The path to the PE image. The path is used to locate the PDB file located in the directory containing the PE file.
            </param>
            <param name="pdbFileStreamProvider">
            If specified, called to open a <see cref="T:System.IO.Stream"/> for a given file path. 
            The provider is expected to either return a readable and seekable <see cref="T:System.IO.Stream"/>, 
            or <c>null</c> if the target file doesn't exist or should be ignored for some reason.
            
            The provider shall throw <see cref="T:System.IO.IOException"/> if it fails to open the file due to an unexpected IO error.
            </param>
            <param name="pdbReaderProvider">
            If successful, a new instance of <see cref="T:System.Reflection.Metadata.MetadataReaderProvider"/> to be used to read the Portable PDB,.
            </param>
            <param name="pdbPath">
            If successful and the PDB is found in a file, the path to the file. Returns <c>null</c> if the PDB is embedded in the PE image itself.
            </param>
            <returns>
            True if the PE image has a PDB associated with it and the PDB has been successfully opened.
            </returns>
            <remarks>
            Implements a simple PDB file lookup based on the content of the PE image Debug Directory.
            A sophisticated tool might need to follow up with additional lookup on search paths or symbol server.
            
            The method looks the PDB up in the following steps in the listed order:
            1) Check for a matching PDB file of the name found in the CodeView entry in the directory containing the PE file (the directory of <paramref name="peImagePath"/>).
            2) Check for a PDB embedded in the PE image itself.
            
            The first PDB that matches the information specified in the Debug Directory is returned.
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="peImagePath"/> or <paramref name="pdbFileStreamProvider"/> is null.</exception>
            <exception cref="T:System.InvalidOperationException">The stream returned from <paramref name="pdbFileStreamProvider"/> doesn't support read and seek operations.</exception>
            <exception cref="T:System.BadImageFormatException">No matching PDB file is found due to an error: The PE image or the PDB is invalid.</exception>
            <exception cref="T:System.IO.IOException">No matching PDB file is found due to an error: An IO error occurred while reading the PE image or the PDB.</exception>
        </member>
        <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.Default">
            <summary>
            By default the stream is disposed when <see cref="T:System.Reflection.PortableExecutable.PEReader"/> is disposed and sections of the PE image are read lazily.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen">
            <summary>
            Keep the stream open when the <see cref="T:System.Reflection.PortableExecutable.PEReader"/> is disposed.
            </summary>
        </member>
        <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchMetadata">
            <summary>
            Reads metadata section into memory right away. 
            </summary>
            <remarks>
            Reading from other sections of the file is not allowed (<see cref="T:System.InvalidOperationException"/> is thrown by the <see cref="T:System.Reflection.PortableExecutable.PEReader"/>).
            The underlying file may be closed and even deleted after <see cref="T:System.Reflection.PortableExecutable.PEReader"/> is constructed.
            
            <see cref="T:System.Reflection.PortableExecutable.PEReader"/> closes the stream automatically by the time the constructor returns unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen"/> is specified.
            </remarks>
        </member>
        <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.PrefetchEntireImage">
            <summary>
            Reads the entire image into memory right away. 
            </summary>
            <remarks>
            <see cref="T:System.Reflection.PortableExecutable.PEReader"/> closes the stream automatically by the time the constructor returns unless <see cref="F:System.Reflection.PortableExecutable.PEStreamOptions.LeaveOpen"/> is specified.
            </remarks>
        </member>
        <member name="F:System.Reflection.PortableExecutable.PEStreamOptions.IsLoadedImage">
            <summary>
            Indicates that the underlying PE image has been loaded into memory by the OS loader.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.Name">
            <summary>
            The name of the section.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.VirtualSize">
            <summary>
            The total size of the section when loaded into memory. 
            If this value is greater than <see cref="P:System.Reflection.PortableExecutable.SectionHeader.SizeOfRawData"/>, the section is zero-padded. 
            This field is valid only for PE images and should be set to zero for object files.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.VirtualAddress">
            <summary>
            For PE images, the address of the first byte of the section relative to the image base when the 
            section is loaded into memory. For object files, this field is the address of the first byte before
            relocation is applied; for simplicity, compilers should set this to zero. Otherwise, 
            it is an arbitrary value that is subtracted from offsets during relocation.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.SizeOfRawData">
            <summary>
            The size of the section (for object files) or the size of the initialized data on disk (for image files).
            For PE images, this must be a multiple of <see cref="P:System.Reflection.PortableExecutable.PEHeader.FileAlignment"/>.
            If this is less than <see cref="P:System.Reflection.PortableExecutable.SectionHeader.VirtualSize"/>, the remainder of the section is zero-filled. 
            Because the <see cref="P:System.Reflection.PortableExecutable.SectionHeader.SizeOfRawData"/> field is rounded but the <see cref="P:System.Reflection.PortableExecutable.SectionHeader.VirtualSize"/> field is not, 
            it is possible for <see cref="P:System.Reflection.PortableExecutable.SectionHeader.SizeOfRawData"/> to be greater than <see cref="P:System.Reflection.PortableExecutable.SectionHeader.VirtualSize"/> as well.
             When a section contains only uninitialized data, this field should be zero.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.PointerToRawData">
            <summary>
            The file pointer to the first page of the section within the COFF file. 
            For PE images, this must be a multiple of <see cref="P:System.Reflection.PortableExecutable.PEHeader.FileAlignment"/>. 
            For object files, the value should be aligned on a 4 byte boundary for best performance. 
            When a section contains only uninitialized data, this field should be zero.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.PointerToRelocations">
            <summary>
            The file pointer to the beginning of relocation entries for the section.
            This is set to zero for PE images or if there are no relocations.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.PointerToLineNumbers">
            <summary>
            The file pointer to the beginning of line-number entries for the section. 
            This is set to zero if there are no COFF line numbers. 
            This value should be zero for an image because COFF debugging information is deprecated.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.NumberOfRelocations">
            <summary>
            The number of relocation entries for the section. This is set to zero for PE images.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.NumberOfLineNumbers">
            <summary>
            The number of line-number entries for the section.
             This value should be zero for an image because COFF debugging information is deprecated.
            </summary>
        </member>
        <member name="P:System.Reflection.PortableExecutable.SectionHeader.SectionCharacteristics">
            <summary>
            The flags that describe the characteristics of the section. 
            </summary>
        </member>
        <member name="F:System.Reflection.MethodSemanticsAttributes.Setter">
            <summary>
            Used to modify the value of the property.
            CLS-compliant setters are named with set_ prefix.
            </summary>
        </member>
        <member name="F:System.Reflection.MethodSemanticsAttributes.Getter">
            <summary>
            Used to read the value of the property.
            CLS-compliant getters are named with get_ prefix.
            </summary>
        </member>
        <member name="F:System.Reflection.MethodSemanticsAttributes.Other">
            <summary>
            Other method for property (not getter or setter) or event (not adder, remover, or raiser).
            </summary>
        </member>
        <member name="F:System.Reflection.MethodSemanticsAttributes.Adder">
            <summary>
            Used to add a handler for an event.
            Corresponds to the AddOn flag in the Ecma 335 CLI specification.
            CLS-compliant adders are named with add_ prefix.
            </summary>
        </member>
        <member name="F:System.Reflection.MethodSemanticsAttributes.Remover">
            <summary>
            Used to remove a handler for an event.
            Corresponds to the RemoveOn flag in the Ecma 335 CLI specification.
            CLS-compliant removers are named with remove_ prefix.
            </summary>
        </member>
        <member name="F:System.Reflection.MethodSemanticsAttributes.Raiser">
            <summary>
            Used to indicate that an event has occurred.
            Corresponds to the Fire flag in the Ecma 335 CLI specification.
            CLS-compliant raisers are named with raise_ prefix.
            </summary>
        </member>
        <member name="T:System.Reflection.DeclarativeSecurityAction">
            <summary>
            Specifies the security actions that can be performed using declarative security.
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.None">
            <summary>
            No declarative security action.
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.Demand">
            <summary>
            Check that all callers in the call chain have been granted specified permission,
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.Assert">
            <summary>
            The calling code can access the resource identified by the current permission object, even if callers higher in the stack have not been granted permission to access the resource.
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.Deny">
            <summary>
            Without further checks refuse Demand for the specified permission.
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.PermitOnly">
            <summary>
            Without further checks, refuse Demand for all permissions other than those specified.
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.LinkDemand">
            <summary>
            Check that the immediate caller has been granted the specified permission;
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.InheritanceDemand">
            <summary>
            The derived class inheriting the class or overriding a method is required to have been granted the specified permission.
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.RequestMinimum">
            <summary>
            The request for the minimum permissions required for code to run. This action can only be used within the scope of the assembly.
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.RequestOptional">
            <summary>
            The request for additional permissions that are optional (not required to run). This request implicitly refuses all other permissions not specifically requested. This action can only be used within the scope of the assembly. 
            </summary>
        </member>
        <member name="F:System.Reflection.DeclarativeSecurityAction.RequestRefuse">
            <summary>
            The request that permissions that might be misused will not be granted to the calling code. This action can only be used within the scope of the assembly.
            </summary>
        </member>
        <member name="F:System.Reflection.ManifestResourceAttributes.Public">
            <summary>
            The Resource is exported from the Assembly
            </summary>
        </member>
        <member name="F:System.Reflection.ManifestResourceAttributes.Private">
            <summary>
            The Resource is not exported from the Assembly
            </summary>
        </member>
        <member name="F:System.Reflection.ManifestResourceAttributes.VisibilityMask">
            <summary>
            Masks just the visibility-related attributes.
            </summary>
        </member>
        <member name="T:System.Reflection.AssemblyHashAlgorithm">
            <summary>
            Specifies all the hash algorithms used for hashing assembly files and for generating the strong name.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyHashAlgorithm.None">
            <summary>
            A mask indicating that there is no hash algorithm. If you specify None for a multi-module assembly, the common language runtime defaults to the SHA1 algorithm, since multi-module assemblies need to generate a hash.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyHashAlgorithm.MD5">
            <summary>
            Retrieves the MD5 message-digest algorithm. MD5 was developed by Rivest in 1991. It is basically MD4 with safety-belts and while it is slightly slower than MD4, it helps provide more security. The algorithm consists of four distinct rounds, which has a slightly different design from that of MD4. Message-digest size, as well as padding requirements, remain the same.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha1">
            <summary>
            Retrieves a revision of the Secure Hash Algorithm that corrects an unpublished flaw in SHA.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha256">
            <summary>
            Retrieves a version of the Secure Hash Algorithm with a hash size of 256 bits.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha384">
            <summary>
            Retrieves a version of the Secure Hash Algorithm with a hash size of 384 bits.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyHashAlgorithm.Sha512">
            <summary>
            Retrieves a version of the Secure Hash Algorithm with a hash size of 512 bits.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyFlags.PublicKey">
            <summary>
            The assembly reference holds the full (unhashed) public key.
            Not applicable on assembly definition.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyFlags.Retargetable">
            <summary>
            The implementation of the referenced assembly used at runtime is not expected to match the version seen at compile time.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyFlags.WindowsRuntime">
            <summary>
            The assembly contains Windows Runtime code.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyFlags.ContentTypeMask">
            <summary>
            Content type mask. Masked bits correspond to values of <see cref="T:System.Reflection.AssemblyContentType"/>.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyFlags.DisableJitCompileOptimizer">
            <summary>
            Specifies that just-in-time (JIT) compiler optimization is disabled for the assembly.
            </summary>
        </member>
        <member name="F:System.Reflection.AssemblyFlags.EnableJitCompileTracking">
            <summary>
            Specifies that just-in-time (JIT) compiler tracking is enabled for the assembly.
            </summary>
        </member>
    </members>
</doc>
