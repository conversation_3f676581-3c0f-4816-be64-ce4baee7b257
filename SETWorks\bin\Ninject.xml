<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Ninject</name>
    </assembly>
    <members>
        <member name="T:Ninject.Activation.Blocks.ActivationBlock">
            <summary>
            A block used for deterministic disposal of activated instances. When the block is
            disposed, all instances activated via it will be deactivated.
            </summary>
        </member>
        <member name="T:Ninject.Infrastructure.Disposal.DisposableObject">
            <summary>
            An object that notifies when it is disposed.
            </summary>
        </member>
        <member name="T:Ninject.Infrastructure.Disposal.IDisposableObject">
            <summary>
            An object that can report whether or not it is disposed.
            </summary>
        </member>
        <member name="P:Ninject.Infrastructure.Disposal.IDisposableObject.IsDisposed">
            <summary>
            Gets a value indicating whether this instance is disposed.
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Disposal.DisposableObject.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Disposal.DisposableObject.Dispose(System.Boolean)">
            <summary>
            Releases resources held by the object.
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Disposal.DisposableObject.Finalize">
            <summary>
            Releases resources before the object is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="P:Ninject.Infrastructure.Disposal.DisposableObject.IsDisposed">
            <summary>
            Gets a value indicating whether this instance is disposed.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Blocks.IActivationBlock">
            <summary>
            A block used for deterministic disposal of activated instances. When the block is
            disposed, all instances activated via it will be deactivated.
            </summary>
        </member>
        <member name="T:Ninject.Syntax.IResolutionRoot">
            <summary>
            Provides a path to resolve instances.
            </summary>
        </member>
        <member name="T:Ninject.Syntax.IFluentSyntax">
            <summary>
            A hack to hide methods defined on <see cref="T:System.Object"/> for IntelliSense
            on fluent interfaces. Credit to Daniel Cazzulino.
            </summary>
        </member>
        <member name="M:Ninject.Syntax.IFluentSyntax.GetType">
            <summary>
            Gets the type of this instance.
            </summary>
            <returns>The type of this instance.</returns>
        </member>
        <member name="M:Ninject.Syntax.IFluentSyntax.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="M:Ninject.Syntax.IFluentSyntax.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:Ninject.Syntax.IFluentSyntax.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="other">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
                <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Syntax.IResolutionRoot.CanResolve(Ninject.Activation.IRequest)">
            <summary>
            Determines whether the specified request can be resolved.
            </summary>
            <param name="request">The request.</param>
            <returns><c>True</c> if the request can be resolved; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Syntax.IResolutionRoot.CanResolve(Ninject.Activation.IRequest,System.Boolean)">
            <summary>
            Determines whether the specified request can be resolved.
            </summary>
            <param name="request">The request.</param>
            <param name="ignoreImplicitBindings">if set to <c>true</c> implicit bindings are ignored.</param>
            <returns>
                <c>True</c> if the request can be resolved; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Syntax.IResolutionRoot.Resolve(Ninject.Activation.IRequest)">
            <summary>
            Resolves instances for the specified request. The instances are not actually resolved
            until a consumer iterates over the enumerator.
            </summary>
            <param name="request">The request to resolve.</param>
            <returns>An enumerator of instances that match the request.</returns>
        </member>
        <member name="M:Ninject.Syntax.IResolutionRoot.CreateRequest(System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},System.Collections.Generic.IEnumerable{Ninject.Parameters.IParameter},System.Boolean,System.Boolean)">
            <summary>
            Creates a request for the specified service.
            </summary>
            <param name="service">The service that is being requested.</param>
            <param name="constraint">The constraint to apply to the bindings to determine if they match the request.</param>
            <param name="parameters">The parameters to pass to the resolution.</param>
            <param name="isOptional"><c>True</c> if the request is optional; otherwise, <c>false</c>.</param>
            <param name="isUnique"><c>True</c> if the request should return a unique result; otherwise, <c>false</c>.</param>
            <returns>The created request.</returns>
        </member>
        <member name="M:Ninject.Syntax.IResolutionRoot.Release(System.Object)">
            <summary>
            Deactivates and releases the specified instance if it is currently managed by Ninject.
            </summary>
            <param name="instance">The instance to release.</param>
            <returns><see langword="True"/> if the instance was found and released; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="T:Ninject.Infrastructure.Disposal.INotifyWhenDisposed">
            <summary>
            An object that fires an event when it is disposed.
            </summary>
        </member>
        <member name="E:Ninject.Infrastructure.Disposal.INotifyWhenDisposed.Disposed">
            <summary>
            Occurs when the object is disposed.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Blocks.ActivationBlock.#ctor(Ninject.Syntax.IResolutionRoot)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Blocks.ActivationBlock"/> class.
            </summary>
            <param name="parent">The parent resolution root.</param>
        </member>
        <member name="M:Ninject.Activation.Blocks.ActivationBlock.Dispose(System.Boolean)">
            <summary>
            Releases resources held by the object.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Blocks.ActivationBlock.CanResolve(Ninject.Activation.IRequest)">
            <summary>
            Determines whether the specified request can be resolved.
            </summary>
            <param name="request">The request.</param>
            <returns><c>True</c> if the request can be resolved; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Activation.Blocks.ActivationBlock.CanResolve(Ninject.Activation.IRequest,System.Boolean)">
            <summary>
            Determines whether the specified request can be resolved.
            </summary>
            <param name="request">The request.</param>
            <param name="ignoreImplicitBindings">if set to <c>true</c> implicit bindings are ignored.</param>
            <returns>
                <c>True</c> if the request can be resolved; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Activation.Blocks.ActivationBlock.Resolve(Ninject.Activation.IRequest)">
            <summary>
            Resolves instances for the specified request. The instances are not actually resolved
            until a consumer iterates over the enumerator.
            </summary>
            <param name="request">The request to resolve.</param>
            <returns>An enumerator of instances that match the request.</returns>
        </member>
        <member name="M:Ninject.Activation.Blocks.ActivationBlock.CreateRequest(System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},System.Collections.Generic.IEnumerable{Ninject.Parameters.IParameter},System.Boolean,System.Boolean)">
            <summary>
            Creates a request for the specified service.
            </summary>
            <param name="service">The service that is being requested.</param>
            <param name="constraint">The constraint to apply to the bindings to determine if they match the request.</param>
            <param name="parameters">The parameters to pass to the resolution.</param>
            <param name="isOptional"><c>True</c> if the request is optional; otherwise, <c>false</c>.</param>
            <param name="isUnique"><c>True</c> if the request should return a unique result; otherwise, <c>false</c>.</param>
            <returns>The created request.</returns>
        </member>
        <member name="M:Ninject.Activation.Blocks.ActivationBlock.Release(System.Object)">
            <summary>
            Deactivates and releases the specified instance if it is currently managed by Ninject.
            </summary>
            <param name="instance">The instance to release.</param>
            <returns><see langword="True"/> if the instance was found and released; otherwise <see langword="false"/>.</returns>
            <remarks></remarks>
        </member>
        <member name="P:Ninject.Activation.Blocks.ActivationBlock.Parent">
            <summary>
            Gets or sets the parent resolution root (usually the kernel).
            </summary>
        </member>
        <member name="E:Ninject.Activation.Blocks.ActivationBlock.Disposed">
            <summary>
            Occurs when the object is disposed.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Caching.ActivationCache">
            <summary>
            Stores the objects that were activated
            </summary>
        </member>
        <member name="T:Ninject.Components.NinjectComponent">
            <summary>
            A component that contributes to the internals of Ninject.
            </summary>
        </member>
        <member name="T:Ninject.Components.INinjectComponent">
            <summary>
            A component that contributes to the internals of Ninject.
            </summary>
        </member>
        <member name="P:Ninject.Components.INinjectComponent.Settings">
            <summary>
            Gets or sets the settings.
            </summary>
        </member>
        <member name="P:Ninject.Components.NinjectComponent.Settings">
            <summary>
            Gets or sets the settings.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Caching.IActivationCache">
            <summary>
            Stores the objects that were activated
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.IActivationCache.Clear">
            <summary>
            Clears the cache.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.IActivationCache.AddActivatedInstance(System.Object)">
            <summary>
            Adds an activated instance.
            </summary>
            <param name="instance">The instance to be added.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.IActivationCache.AddDeactivatedInstance(System.Object)">
            <summary>
            Adds an deactivated instance.
            </summary>
            <param name="instance">The instance to be added.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.IActivationCache.IsActivated(System.Object)">
            <summary>
            Determines whether the specified instance is activated.
            </summary>
            <param name="instance">The instance.</param>
            <returns>
                <c>true</c> if the specified instance is activated; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Activation.Caching.IActivationCache.IsDeactivated(System.Object)">
            <summary>
            Determines whether the specified instance is deactivated.
            </summary>
            <param name="instance">The instance.</param>
            <returns>
                <c>true</c> if the specified instance is deactivated; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Ninject.Activation.Caching.IPruneable">
            <summary>
            An object that is prunealble.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.IPruneable.Prune">
            <summary>
            Removes instances from the cache which should no longer be re-used.
            </summary>
        </member>
        <member name="F:Ninject.Activation.Caching.ActivationCache.activatedObjects">
            <summary>
            The objects that were activated as reference equal weak references.
            </summary>
        </member>
        <member name="F:Ninject.Activation.Caching.ActivationCache.deactivatedObjects">
            <summary>
            The objects that were activated as reference equal weak references.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.#ctor(Ninject.Activation.Caching.ICachePruner)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Caching.ActivationCache"/> class.
            </summary>
            <param name="cachePruner">The cache pruner.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.Clear">
            <summary>
            Clears the cache.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.AddActivatedInstance(System.Object)">
            <summary>
            Adds an activated instance.
            </summary>
            <param name="instance">The instance to be added.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.AddDeactivatedInstance(System.Object)">
            <summary>
            Adds an deactivated instance.
            </summary>
            <param name="instance">The instance to be added.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.IsActivated(System.Object)">
            <summary>
            Determines whether the specified instance is activated.
            </summary>
            <param name="instance">The instance.</param>
            <returns>
                <c>true</c> if the specified instance is activated; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.IsDeactivated(System.Object)">
            <summary>
            Determines whether the specified instance is deactivated.
            </summary>
            <param name="instance">The instance.</param>
            <returns>
                <c>true</c> if the specified instance is deactivated; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.Prune">
            <summary>
            Prunes this instance.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.ActivationCache.RemoveDeadObjects(System.Collections.Generic.HashSet{System.Object})">
            <summary>
            Removes all dead objects.
            </summary>
            <param name="objects">The objects collection to be freed of dead objects.</param>
        </member>
        <member name="P:Ninject.Activation.Caching.ActivationCache.ActivatedObjectCount">
            <summary>
            Gets the activated object count.
            </summary>
            <value>The activated object count.</value>
        </member>
        <member name="P:Ninject.Activation.Caching.ActivationCache.DeactivatedObjectCount">
            <summary>
            Gets the deactivated object count.
            </summary>
            <value>The deactivated object count.</value>
        </member>
        <member name="T:Ninject.Activation.Caching.Cache">
            <summary>
            Tracks instances for re-use in certain scopes.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Caching.ICache">
            <summary>
            Tracks instances for re-use in certain scopes.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.ICache.Remember(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Stores the specified instance in the cache.
            </summary>
            <param name="context">The context to store.</param>
            <param name="reference">The instance reference.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.ICache.TryGet(Ninject.Activation.IContext)">
            <summary>
            Tries to retrieve an instance to re-use in the specified context.
            </summary>
            <param name="context">The context that is being activated.</param>
            <returns>The instance for re-use, or <see langword="null"/> if none has been stored.</returns>
        </member>
        <member name="M:Ninject.Activation.Caching.ICache.Release(System.Object)">
            <summary>
            Deactivates and releases the specified instance from the cache.
            </summary>
            <param name="instance">The instance to release.</param>
            <returns><see langword="True"/> if the instance was found and released; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Ninject.Activation.Caching.ICache.Clear(System.Object)">
            <summary>
            Immediately deactivates and removes all instances in the cache that are owned by
            the specified scope.
            </summary>
            <param name="scope">The scope whose instances should be deactivated.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.ICache.Clear">
            <summary>
            Immediately deactivates and removes all instances in the cache, regardless of scope.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Caching.ICache.Count">
            <summary>
            Gets the number of entries currently stored in the cache.
            </summary>
        </member>
        <member name="F:Ninject.Activation.Caching.Cache.entries">
            <summary>
            Contains all cached instances.
            This is a dictionary of scopes to a multimap for bindings to cache entries.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.#ctor(Ninject.Activation.IPipeline,Ninject.Activation.Caching.ICachePruner)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Caching.Cache"/> class.
            </summary>
            <param name="pipeline">The pipeline component.</param>
            <param name="cachePruner">The cache pruner component.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Dispose(System.Boolean)">
            <summary>
            Releases resources held by the object.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Remember(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Stores the specified context in the cache.
            </summary>
            <param name="context">The context to store.</param>
            <param name="reference">The instance reference.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.TryGet(Ninject.Activation.IContext)">
            <summary>
            Tries to retrieve an instance to re-use in the specified context.
            </summary>
            <param name="context">The context that is being activated.</param>
            <returns>The instance for re-use, or <see langword="null"/> if none has been stored.</returns>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Release(System.Object)">
            <summary>
            Deactivates and releases the specified instance from the cache.
            </summary>
            <param name="instance">The instance to release.</param>
            <returns><see langword="True"/> if the instance was found and released; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Prune">
            <summary>
            Removes instances from the cache which should no longer be re-used.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Clear(System.Object)">
            <summary>
            Immediately deactivates and removes all instances in the cache that are owned by
            the specified scope.
            </summary>
            <param name="scope">The scope whose instances should be deactivated.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Clear">
            <summary>
            Immediately deactivates and removes all instances in the cache, regardless of scope.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.GetAllBindingEntries(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{Ninject.Planning.Bindings.IBindingConfiguration,System.Collections.Generic.ICollection{Ninject.Activation.Caching.Cache.CacheEntry}}})">
            <summary>
            Gets all entries for a binding withing the selected scope.
            </summary>
            <param name="bindings">The bindings.</param>
            <returns>All bindings of a binding.</returns>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.GetAllCacheEntries">
            <summary>
            Gets all cache entries.
            </summary>
            <returns>Returns all cache entries.</returns>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Forget(System.Collections.Generic.IEnumerable{Ninject.Activation.Caching.Cache.CacheEntry})">
            <summary>
            Forgets the specified cache entries.
            </summary>
            <param name="cacheEntries">The cache entries.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.Forget(Ninject.Activation.Caching.Cache.CacheEntry)">
            <summary>
            Forgets the specified entry.
            </summary>
            <param name="entry">The entry.</param>
        </member>
        <member name="P:Ninject.Activation.Caching.Cache.Pipeline">
            <summary>
            Gets the pipeline component.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Caching.Cache.Count">
            <summary>
            Gets the number of entries currently stored in the cache.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Caching.Cache.CacheEntry">
            <summary>
            An entry in the cache.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.Cache.CacheEntry.#ctor(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Caching.Cache.CacheEntry"/> class.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">The instance reference.</param>
        </member>
        <member name="P:Ninject.Activation.Caching.Cache.CacheEntry.Context">
            <summary>
            Gets the context of the instance.
            </summary>
            <value>The context.</value>
        </member>
        <member name="P:Ninject.Activation.Caching.Cache.CacheEntry.Reference">
            <summary>
            Gets the instance reference.
            </summary>
            <value>The instance reference.</value>
        </member>
        <member name="T:Ninject.Activation.Caching.GarbageCollectionCachePruner">
            <summary>
            Uses a <see cref="T:System.Threading.Timer"/> and some <see cref="T:System.WeakReference"/> magic to poll
            the garbage collector to see if it has run.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Caching.ICachePruner">
            <summary>
            Prunes instances from an <see cref="T:Ninject.Activation.Caching.ICache"/> based on environmental information.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.ICachePruner.Start(Ninject.Activation.Caching.IPruneable)">
            <summary>
            Starts pruning the specified cache based on the rules of the pruner.
            </summary>
            <param name="cache">The cache that will be pruned.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.ICachePruner.Stop">
            <summary>
            Stops pruning.
            </summary>
        </member>
        <member name="F:Ninject.Activation.Caching.GarbageCollectionCachePruner.indicator">
            <summary>
            indicator for if GC has been run.
            </summary>
        </member>
        <member name="F:Ninject.Activation.Caching.GarbageCollectionCachePruner.caches">
            <summary>
            The caches that are being pruned.
            </summary>
        </member>
        <member name="F:Ninject.Activation.Caching.GarbageCollectionCachePruner.timer">
            <summary>
            The timer used to trigger the cache pruning
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.GarbageCollectionCachePruner.Dispose(System.Boolean)">
            <summary>
            Releases resources held by the object.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.GarbageCollectionCachePruner.Start(Ninject.Activation.Caching.IPruneable)">
            <summary>
            Starts pruning the specified pruneable based on the rules of the pruner.
            </summary>
            <param name="pruneable">The pruneable that will be pruned.</param>
        </member>
        <member name="M:Ninject.Activation.Caching.GarbageCollectionCachePruner.Stop">
            <summary>
            Stops pruning.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Caching.WeakReferenceEqualityComparer">
            <summary>
            Compares ReferenceEqualWeakReferences to objects
            </summary>
        </member>
        <member name="M:Ninject.Activation.Caching.WeakReferenceEqualityComparer.Equals(System.Object,System.Object)">
            <summary>
            Returns if the specifed objects are equal.
            </summary>
            <param name="x">The first object.</param>
            <param name="y">The second object.</param>
            <returns>True if the objects are equal; otherwise false</returns>
        </member>
        <member name="M:Ninject.Activation.Caching.WeakReferenceEqualityComparer.GetHashCode(System.Object)">
            <summary>
            Returns the hash code of the specified object.
            </summary>
            <param name="obj">The object for which the hash code is calculated.</param>
            <returns>The hash code of the specified object.</returns>
        </member>
        <member name="T:Ninject.Activation.Providers.CallbackProvider`1">
            <summary>
            A provider that delegates to a callback method to create instances.
            </summary>
            <typeparam name="T">The type of instances the provider creates.</typeparam>
        </member>
        <member name="T:Ninject.Activation.Provider`1">
            <summary>
            A simple abstract provider for instances of a specific type.
            </summary>
            <typeparam name="T">The type of instances the provider creates.</typeparam>
        </member>
        <member name="T:Ninject.Activation.IProvider`1">
            <summary>
            Provides instances ot the type T
            </summary>
            <typeparam name="T">The type provides by this implementation.</typeparam>
        </member>
        <member name="T:Ninject.Activation.IProvider">
            <summary>
            Creates instances of services.
            </summary>
        </member>
        <member name="M:Ninject.Activation.IProvider.Create(Ninject.Activation.IContext)">
            <summary>
            Creates an instance within the specified context.
            </summary>
            <param name="context">The context.</param>
            <returns>The created instance.</returns>
        </member>
        <member name="P:Ninject.Activation.IProvider.Type">
            <summary>
            Gets the type (or prototype) of instances the provider creates.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Provider`1.Create(Ninject.Activation.IContext)">
            <summary>
            Creates an instance within the specified context.
            </summary>
            <param name="context">The context.</param>
            <returns>The created instance.</returns>
        </member>
        <member name="M:Ninject.Activation.Provider`1.CreateInstance(Ninject.Activation.IContext)">
            <summary>
            Creates an instance within the specified context.
            </summary>
            <param name="context">The context.</param>
            <returns>The created instance.</returns>
        </member>
        <member name="P:Ninject.Activation.Provider`1.Type">
            <summary>
            Gets the type (or prototype) of instances the provider creates.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Providers.CallbackProvider`1.#ctor(System.Func{Ninject.Activation.IContext,`0})">
            <summary>
            Initializes a new instance of the CallbackProvider&lt;T&gt; class.
            </summary>
            <param name="method">The callback method that will be called to create instances.</param>
        </member>
        <member name="M:Ninject.Activation.Providers.CallbackProvider`1.CreateInstance(Ninject.Activation.IContext)">
            <summary>
            Invokes the callback method to create an instance.
            </summary>
            <param name="context">The context.</param>
            <returns>The created instance.</returns>
        </member>
        <member name="P:Ninject.Activation.Providers.CallbackProvider`1.Method">
            <summary>
            Gets the callback method used by the provider.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Providers.ConstantProvider`1">
            <summary>
            A provider that always returns the same constant value.
            </summary>
            <typeparam name="T">The type of value that is returned.</typeparam>
        </member>
        <member name="M:Ninject.Activation.Providers.ConstantProvider`1.#ctor(`0)">
            <summary>
            Initializes a new instance of the ConstantProvider&lt;T&gt; class.
            </summary>
            <param name="value">The value that the provider should return.</param>
        </member>
        <member name="M:Ninject.Activation.Providers.ConstantProvider`1.CreateInstance(Ninject.Activation.IContext)">
            <summary>
            Creates an instance within the specified context.
            </summary>
            <param name="context">The context.</param>
            <returns>The constant value this provider returns.</returns>
        </member>
        <member name="P:Ninject.Activation.Providers.ConstantProvider`1.Value">
            <summary>
            Gets the value that the provider will return.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Providers.StandardProvider">
            <summary>
            The standard provider for types, which activates instances via a <see cref="T:Ninject.Activation.IPipeline"/>.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Providers.StandardProvider.#ctor(System.Type,Ninject.Planning.IPlanner,Ninject.Selection.Heuristics.IConstructorScorer)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Providers.StandardProvider"/> class.
            </summary>
            <param name="type">The type (or prototype) of instances the provider creates.</param>
            <param name="planner">The planner component.</param>
            <param name="constructorScorer">The constructor scorer component.</param>
        </member>
        <member name="M:Ninject.Activation.Providers.StandardProvider.Create(Ninject.Activation.IContext)">
            <summary>
            Creates an instance within the specified context.
            </summary>
            <param name="context">The context.</param>
            <returns>The created instance.</returns>
        </member>
        <member name="M:Ninject.Activation.Providers.StandardProvider.GetValue(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Gets the value to inject into the specified target.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>The value to inject into the specified target.</returns>
        </member>
        <member name="M:Ninject.Activation.Providers.StandardProvider.GetImplementationType(System.Type)">
            <summary>
            Gets the implementation type that the provider will activate an instance of
            for the specified service.
            </summary>
            <param name="service">The service in question.</param>
            <returns>The implementation type that will be activated.</returns>
        </member>
        <member name="M:Ninject.Activation.Providers.StandardProvider.GetCreationCallback(System.Type)">
            <summary>
            Gets a callback that creates an instance of the <see cref="T:Ninject.Activation.Providers.StandardProvider"/>
            for the specified type.
            </summary>
            <param name="prototype">The prototype the provider instance will create.</param>
            <returns>The created callback.</returns>
        </member>
        <member name="M:Ninject.Activation.Providers.StandardProvider.GetCreationCallback(System.Type,System.Reflection.ConstructorInfo)">
            <summary>
            Gets a callback that creates an instance of the <see cref="T:Ninject.Activation.Providers.StandardProvider"/>
            for the specified type and constructor.
            </summary>
            <param name="prototype">The prototype the provider instance will create.</param>
            <param name="constructor">The constructor.</param>
            <returns>The created callback.</returns>
        </member>
        <member name="P:Ninject.Activation.Providers.StandardProvider.Type">
            <summary>
            Gets the type (or prototype) of instances the provider creates.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Providers.StandardProvider.Planner">
            <summary>
            Gets or sets the planner component.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Providers.StandardProvider.ConstructorScorer">
            <summary>
            Gets or sets the selector component.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Strategies.ActivationCacheStrategy">
            <summary>
            Adds all activated instances to the activation cache.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Strategies.IActivationStrategy">
            <summary>
            Contributes to a <see cref="T:Ninject.Activation.IPipeline"/>, and is called during the activation
            and deactivation of an instance.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.IActivationStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Contributes to the activation of the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.IActivationStrategy.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Contributes to the deactivation of the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being deactivated.</param>
        </member>
        <member name="F:Ninject.Activation.Strategies.ActivationCacheStrategy.activationCache">
            <summary>
            The activation cache.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.ActivationCacheStrategy.#ctor(Ninject.Activation.Caching.IActivationCache)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Strategies.ActivationCacheStrategy"/> class.
            </summary>
            <param name="activationCache">The activation cache.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.ActivationCacheStrategy.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.ActivationCacheStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Contributes to the activation of the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.ActivationCacheStrategy.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Contributes to the deactivation of the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being deactivated.</param>
        </member>
        <member name="P:Ninject.Activation.Strategies.ActivationCacheStrategy.Settings">
            <summary>
            Gets or sets the settings.
            </summary>
            <value>The ninject settings.</value>
        </member>
        <member name="T:Ninject.Activation.Strategies.ActivationStrategy">
            <summary>
            Contributes to a <see cref="T:Ninject.Activation.IPipeline"/>, and is called during the activation
            and deactivation of an instance.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.ActivationStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Contributes to the activation of the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.ActivationStrategy.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Contributes to the deactivation of the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being deactivated.</param>
        </member>
        <member name="T:Ninject.Activation.Strategies.BindingActionStrategy">
            <summary>
            Executes actions defined on the binding during activation and deactivation.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.BindingActionStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Calls the activation actions defined on the binding.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.BindingActionStrategy.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Calls the deactivation actions defined on the binding.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being deactivated.</param>
        </member>
        <member name="T:Ninject.Activation.Strategies.DisposableStrategy">
            <summary>
            During deactivation, disposes instances that implement <see cref="T:System.IDisposable"/>.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.DisposableStrategy.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Disposes the specified instance.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being deactivated.</param>
        </member>
        <member name="T:Ninject.Activation.Strategies.InitializableStrategy">
            <summary>
            During activation, initializes instances that implement <see cref="T:Ninject.IInitializable"/>.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.InitializableStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Initializes the specified instance.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="T:Ninject.Activation.Strategies.MethodInjectionStrategy">
            <summary>
            Injects methods on an instance during activation.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.MethodInjectionStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Injects values into the properties as described by <see cref="T:Ninject.Planning.Directives.MethodInjectionDirective"/>s
            contained in the plan.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="T:Ninject.Activation.Strategies.PropertyInjectionStrategy">
            <summary>
            Injects properties on an instance during activation.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.PropertyInjectionStrategy.#ctor(Ninject.Injection.IInjectorFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Strategies.PropertyInjectionStrategy"/> class.
            </summary>
            <param name="injectorFactory">The injector factory component.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.PropertyInjectionStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Injects values into the properties as described by <see cref="T:Ninject.Planning.Directives.PropertyInjectionDirective"/>s
            contained in the plan.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.PropertyInjectionStrategy.AssignProperyOverrides(Ninject.Activation.IContext,Ninject.Activation.InstanceReference,System.Collections.Generic.IList{Ninject.Parameters.IPropertyValue})">
            <summary>
            Applies user supplied override values to instance properties.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
            <param name="propertyValues">The parameter override value accessors.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.PropertyInjectionStrategy.GetValue(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Collections.Generic.IEnumerable{Ninject.Parameters.IPropertyValue})">
            <summary>
            Gets the value to inject into the specified target.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <param name="allPropertyValues">all property values of the current request.</param>
            <returns>The value to inject into the specified target.</returns>
        </member>
        <member name="P:Ninject.Activation.Strategies.PropertyInjectionStrategy.InjectorFactory">
            <summary>
            Gets the injector factory component.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Strategies.StartableStrategy">
            <summary>
            Starts instances that implement <see cref="T:Ninject.IStartable"/> during activation,
            and stops them during deactivation.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Strategies.StartableStrategy.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Starts the specified instance.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being activated.</param>
        </member>
        <member name="M:Ninject.Activation.Strategies.StartableStrategy.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Stops the specified instance.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">A reference to the instance being deactivated.</param>
        </member>
        <member name="T:Ninject.Activation.Context">
            <summary>
            Contains information about the activation of a single instance.
            </summary>
        </member>
        <member name="T:Ninject.Activation.IContext">
            <summary>
            Contains information about the activation of a single instance.
            </summary>
        </member>
        <member name="M:Ninject.Activation.IContext.GetProvider">
            <summary>
            Gets the provider that should be used to create the instance for this context.
            </summary>
            <returns>The provider that should be used.</returns>
        </member>
        <member name="M:Ninject.Activation.IContext.GetScope">
            <summary>
            Gets the scope for the context that "owns" the instance activated therein.
            </summary>
            <returns>The object that acts as the scope.</returns>
        </member>
        <member name="M:Ninject.Activation.IContext.Resolve">
            <summary>
            Resolves this instance for this context.
            </summary>
            <returns>The resolved instance.</returns>
        </member>
        <member name="P:Ninject.Activation.IContext.Kernel">
            <summary>
            Gets the kernel that is driving the activation.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IContext.Request">
            <summary>
            Gets the request.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IContext.Binding">
            <summary>
            Gets the binding.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IContext.Plan">
            <summary>
            Gets or sets the activation plan.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IContext.Parameters">
            <summary>
            Gets the parameters that were passed to manipulate the activation process.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IContext.GenericArguments">
            <summary>
            Gets the generic arguments for the request, if any.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IContext.HasInferredGenericArguments">
            <summary>
            Gets a value indicating whether the request involves inferred generic arguments.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Context.#ctor(Ninject.IKernel,Ninject.Activation.IRequest,Ninject.Planning.Bindings.IBinding,Ninject.Activation.Caching.ICache,Ninject.Planning.IPlanner,Ninject.Activation.IPipeline)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Context"/> class.
            </summary>
            <param name="kernel">The kernel managing the resolution.</param>
            <param name="request">The context's request.</param>
            <param name="binding">The context's binding.</param>
            <param name="cache">The cache component.</param>
            <param name="planner">The planner component.</param>
            <param name="pipeline">The pipeline component.</param>
        </member>
        <member name="M:Ninject.Activation.Context.GetScope">
            <summary>
            Gets the scope for the context that "owns" the instance activated therein.
            </summary>
            <returns>The object that acts as the scope.</returns>
        </member>
        <member name="M:Ninject.Activation.Context.GetProvider">
            <summary>
            Gets the provider that should be used to create the instance for this context.
            </summary>
            <returns>The provider that should be used.</returns>
        </member>
        <member name="M:Ninject.Activation.Context.Resolve">
            <summary>
            Resolves the instance associated with this hook.
            </summary>
            <returns>The resolved instance.</returns>
        </member>
        <member name="P:Ninject.Activation.Context.Kernel">
            <summary>
            Gets the kernel that is driving the activation.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.Request">
            <summary>
            Gets the request.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.Binding">
            <summary>
            Gets the binding.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.Plan">
            <summary>
            Gets or sets the activation plan.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.Parameters">
            <summary>
            Gets the parameters that were passed to manipulate the activation process.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.GenericArguments">
            <summary>
            Gets the generic arguments for the request, if any.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.HasInferredGenericArguments">
            <summary>
            Gets a value indicating whether the request involves inferred generic arguments.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.Cache">
            <summary>
            Gets or sets the cache component.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.Planner">
            <summary>
            Gets or sets the planner component.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Context.Pipeline">
            <summary>
            Gets or sets the pipeline component.
            </summary>
        </member>
        <member name="T:Ninject.Activation.InstanceReference">
            <summary>
            Holds an instance during activation or after it has been cached.
            </summary>
        </member>
        <member name="M:Ninject.Activation.InstanceReference.Is``1">
            <summary>
            Returns a value indicating whether the instance is of the specified type.
            </summary>
            <typeparam name="T">The type in question.</typeparam>
            <returns><see langword="True"/> if the instance is of the specified type, otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Ninject.Activation.InstanceReference.As``1">
            <summary>
            Returns the instance as the specified type.
            </summary>
            <typeparam name="T">The requested type.</typeparam>
            <returns>The instance.</returns>
        </member>
        <member name="M:Ninject.Activation.InstanceReference.IfInstanceIs``1(System.Action{``0})">
            <summary>
            Executes the specified action if the instance if of the specified type.
            </summary>
            <typeparam name="T">The type in question.</typeparam>
            <param name="action">The action to execute.</param>
        </member>
        <member name="P:Ninject.Activation.InstanceReference.Instance">
            <summary>
            Gets or sets the instance.
            </summary>
        </member>
        <member name="T:Ninject.Activation.IPipeline">
            <summary>
            Drives the activation (injection, etc.) of an instance.
            </summary>
        </member>
        <member name="M:Ninject.Activation.IPipeline.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Activates the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">The instance reference.</param>
        </member>
        <member name="M:Ninject.Activation.IPipeline.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Deactivates the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">The instance reference.</param>
        </member>
        <member name="P:Ninject.Activation.IPipeline.Strategies">
            <summary>
            Gets the strategies that contribute to the activation and deactivation processes.
            </summary>
        </member>
        <member name="T:Ninject.Activation.IRequest">
            <summary>
            Describes the request for a service resolution.
            </summary>
        </member>
        <member name="M:Ninject.Activation.IRequest.Matches(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Determines whether the specified binding satisfies the constraint defined on this request.
            </summary>
            <param name="binding">The binding.</param>
            <returns><c>True</c> if the binding satisfies the constraint; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Activation.IRequest.GetScope">
            <summary>
            Gets the scope if one was specified in the request.
            </summary>
            <returns>The object that acts as the scope.</returns>
        </member>
        <member name="M:Ninject.Activation.IRequest.CreateChild(System.Type,Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Creates a child request.
            </summary>
            <param name="service">The service that is being requested.</param>
            <param name="parentContext">The context in which the request was made.</param>
            <param name="target">The target that will receive the injection.</param>
            <returns>The child request.</returns>
        </member>
        <member name="P:Ninject.Activation.IRequest.Service">
            <summary>
            Gets the service that was requested.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.ParentRequest">
            <summary>
            Gets the parent request.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.ParentContext">
            <summary>
            Gets the parent context.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.Target">
            <summary>
            Gets the target that will receive the injection, if any.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.Constraint">
            <summary>
            Gets the constraint that will be applied to filter the bindings used for the request.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.Parameters">
            <summary>
            Gets the parameters that affect the resolution.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.ActiveBindings">
            <summary>
            Gets the stack of bindings which have been activated by either this request or its ancestors.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.Depth">
            <summary>
            Gets the recursive depth at which this request occurs.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.IsOptional">
            <summary>
            Gets or sets value indicating whether the request is optional.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.IsUnique">
            <summary>
            Gets or sets value indicating whether the request should return a unique result.
            </summary>
        </member>
        <member name="P:Ninject.Activation.IRequest.ForceUnique">
            <summary>
            Gets or sets value indicating whether the request should force to return a unique value even if the request is optional.
            If this value is set true the request will throw an ActivationException if there are multiple satisfying bingings rather
            than returning null for the request is optional. For none optional requests this parameter does not change anything.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Pipeline">
            <summary>
            Drives the activation (injection, etc.) of an instance.
            </summary>
        </member>
        <member name="F:Ninject.Activation.Pipeline.activationCache">
            <summary>
            The activation cache.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Pipeline.#ctor(System.Collections.Generic.IEnumerable{Ninject.Activation.Strategies.IActivationStrategy},Ninject.Activation.Caching.IActivationCache)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Pipeline"/> class.
            </summary>
            <param name="strategies">The strategies to execute during activation and deactivation.</param>
            <param name="activationCache">The activation cache.</param>
        </member>
        <member name="M:Ninject.Activation.Pipeline.Activate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Activates the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">The instance reference.</param>
        </member>
        <member name="M:Ninject.Activation.Pipeline.Deactivate(Ninject.Activation.IContext,Ninject.Activation.InstanceReference)">
            <summary>
            Deactivates the instance in the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="reference">The instance reference.</param>
        </member>
        <member name="P:Ninject.Activation.Pipeline.Strategies">
            <summary>
            Gets the strategies that contribute to the activation and deactivation processes.
            </summary>
        </member>
        <member name="T:Ninject.Activation.Request">
            <summary>
            Describes the request for a service resolution.
            </summary>
        </member>
        <member name="M:Ninject.Activation.Request.#ctor(System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},System.Collections.Generic.IEnumerable{Ninject.Parameters.IParameter},System.Func{System.Object},System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Request"/> class.
            </summary>
            <param name="service">The service that was requested.</param>
            <param name="constraint">The constraint that will be applied to filter the bindings used for the request.</param>
            <param name="parameters">The parameters that affect the resolution.</param>
            <param name="scopeCallback">The scope callback, if an external scope was specified.</param>
            <param name="isOptional"><c>True</c> if the request is optional; otherwise, <c>false</c>.</param>
            <param name="isUnique"><c>True</c> if the request should return a unique result; otherwise, <c>false</c>.</param>
        </member>
        <member name="M:Ninject.Activation.Request.#ctor(Ninject.Activation.IContext,System.Type,Ninject.Planning.Targets.ITarget,System.Func{System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Activation.Request"/> class.
            </summary>
            <param name="parentContext">The parent context.</param>
            <param name="service">The service that was requested.</param>
            <param name="target">The target that will receive the injection.</param>
            <param name="scopeCallback">The scope callback, if an external scope was specified.</param>
        </member>
        <member name="M:Ninject.Activation.Request.Matches(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Determines whether the specified binding satisfies the constraints defined on this request.
            </summary>
            <param name="binding">The binding.</param>
            <returns><c>True</c> if the binding satisfies the constraints; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Activation.Request.GetScope">
            <summary>
            Gets the scope if one was specified in the request.
            </summary>
            <returns>The object that acts as the scope.</returns>
        </member>
        <member name="M:Ninject.Activation.Request.CreateChild(System.Type,Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Creates a child request.
            </summary>
            <param name="service">The service that is being requested.</param>
            <param name="parentContext">The context in which the request was made.</param>
            <param name="target">The target that will receive the injection.</param>
            <returns>The child request.</returns>
        </member>
        <member name="P:Ninject.Activation.Request.Service">
            <summary>
            Gets the service that was requested.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.ParentRequest">
            <summary>
            Gets the parent request.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.ParentContext">
            <summary>
            Gets the parent context.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.Target">
            <summary>
            Gets the target that will receive the injection, if any.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.Constraint">
            <summary>
            Gets the constraint that will be applied to filter the bindings used for the request.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.Parameters">
            <summary>
            Gets the parameters that affect the resolution.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.ActiveBindings">
            <summary>
            Gets the stack of bindings which have been activated by either this request or its ancestors.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.Depth">
            <summary>
            Gets the recursive depth at which this request occurs.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.IsOptional">
            <summary>
            Gets or sets value indicating whether the request is optional.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.IsUnique">
            <summary>
            Gets or sets value indicating whether the request is for a single service.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.ForceUnique">
            <summary>
            Gets or sets value indicating whether the request should force to return a unique value even if the request is optional.
            If this value is set true the request will throw an ActivationException if there are multiple satisfying bingings rather
            than returning null for the request is optional. For none optional requests this parameter does not change anything.
            </summary>
        </member>
        <member name="P:Ninject.Activation.Request.ScopeCallback">
            <summary>
            Gets the callback that resolves the scope for the request, if an external scope was provided.
            </summary>
        </member>
        <member name="T:Ninject.ConstraintAttribute">
            <summary>
            Defines a constraint on the decorated member.
            </summary>
        </member>
        <member name="M:Ninject.ConstraintAttribute.Matches(Ninject.Planning.Bindings.IBindingMetadata)">
            <summary>
            Determines whether the specified binding metadata matches the constraint.
            </summary>
            <param name="metadata">The metadata in question.</param>
            <returns><c>True</c> if the metadata matches; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Ninject.InjectAttribute">
            <summary>
            Indicates that the decorated member should be injected.
            </summary>
        </member>
        <member name="T:Ninject.NamedAttribute">
            <summary>
            Indicates that the decorated member should only be injected using binding(s) registered
            with the specified name.
            </summary>
        </member>
        <member name="M:Ninject.NamedAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.NamedAttribute"/> class.
            </summary>
            <param name="name">The name of the binding(s) to use.</param>
        </member>
        <member name="M:Ninject.NamedAttribute.Matches(Ninject.Planning.Bindings.IBindingMetadata)">
            <summary>
            Determines whether the specified binding metadata matches the constraint.
            </summary>
            <param name="metadata">The metadata in question.</param>
            <returns><c>True</c> if the metadata matches; otherwise <c>false</c>.</returns>
        </member>
        <member name="P:Ninject.NamedAttribute.Name">
            <summary>
            Gets the binding name.
            </summary>
        </member>
        <member name="T:Ninject.OptionalAttribute">
            <summary>
            Indicates that the decorated member represents an optional dependency.
            </summary>
        </member>
        <member name="T:Ninject.Components.ComponentContainer">
            <summary>
            An internal container that manages and resolves components that contribute to Ninject.
            </summary>
        </member>
        <member name="T:Ninject.Components.IComponentContainer">
            <summary>
            An internal container that manages and resolves components that contribute to Ninject.
            </summary>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.Add``2">
            <summary>
            Registers a component in the container.
            </summary>
            <typeparam name="TComponent">The component type.</typeparam>
            <typeparam name="TImplementation">The component's implementation type.</typeparam>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.RemoveAll``1">
            <summary>
            Removes all registrations for the specified component.
            </summary>
            <typeparam name="T">The component type.</typeparam>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.RemoveAll(System.Type)">
            <summary>
            Removes all registrations for the specified component.
            </summary>
            <param name="component">The component's type.</param>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.Remove``2">
            <summary>
            Removes the specified registration.
            </summary>
            <typeparam name="T">The component type.</typeparam>
            <typeparam name="TImplementation">The implementation type.</typeparam>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.Get``1">
            <summary>
            Gets one instance of the specified component.
            </summary>
            <typeparam name="T">The component type.</typeparam>
            <returns>The instance of the component.</returns>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.GetAll``1">
            <summary>
            Gets all available instances of the specified component.
            </summary>
            <typeparam name="T">The component type.</typeparam>
            <returns>A series of instances of the specified component.</returns>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.Get(System.Type)">
            <summary>
            Gets one instance of the specified component.
            </summary>
            <param name="component">The component type.</param>
            <returns>The instance of the component.</returns>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.GetAll(System.Type)">
            <summary>
            Gets all available instances of the specified component.
            </summary>
            <param name="component">The component type.</param>
            <returns>A series of instances of the specified component.</returns>
        </member>
        <member name="M:Ninject.Components.IComponentContainer.AddTransient``2">
            <summary>
            Registers a transient component in the container.
            </summary>
            <typeparam name="TComponent">The component type.</typeparam>
            <typeparam name="TImplementation">The component's implementation type.</typeparam>
        </member>
        <member name="P:Ninject.Components.IComponentContainer.Kernel">
            <summary>
            Gets or sets the kernel that owns the component container.
            </summary>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.Dispose(System.Boolean)">
            <summary>
            Releases resources held by the object.
            </summary>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.Add``2">
            <summary>
            Registers a component in the container.
            </summary>
            <typeparam name="TComponent">The component type.</typeparam>
            <typeparam name="TImplementation">The component's implementation type.</typeparam>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.AddTransient``2">
            <summary>
            Registers a transient component in the container.
            </summary>
            <typeparam name="TComponent">The component type.</typeparam>
            <typeparam name="TImplementation">The component's implementation type.</typeparam>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.RemoveAll``1">
            <summary>
            Removes all registrations for the specified component.
            </summary>
            <typeparam name="T">The component type.</typeparam>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.Remove``2">
            <summary>
            Removes the specified registration.
            </summary>
            <typeparam name="T">The component type.</typeparam>
            <typeparam name="TImplementation">The implementation type.</typeparam>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.RemoveAll(System.Type)">
            <summary>
            Removes all registrations for the specified component.
            </summary>
            <param name="component">The component type.</param>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.Get``1">
            <summary>
            Gets one instance of the specified component.
            </summary>
            <typeparam name="T">The component type.</typeparam>
            <returns>The instance of the component.</returns>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.GetAll``1">
            <summary>
            Gets all available instances of the specified component.
            </summary>
            <typeparam name="T">The component type.</typeparam>
            <returns>A series of instances of the specified component.</returns>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.Get(System.Type)">
            <summary>
            Gets one instance of the specified component.
            </summary>
            <param name="component">The component type.</param>
            <returns>The instance of the component.</returns>
        </member>
        <member name="M:Ninject.Components.ComponentContainer.GetAll(System.Type)">
            <summary>
            Gets all available instances of the specified component.
            </summary>
            <param name="component">The component type.</param>
            <returns>A series of instances of the specified component.</returns>
        </member>
        <member name="P:Ninject.Components.ComponentContainer.Kernel">
            <summary>
            Gets or sets the kernel that owns the component container.
            </summary>
        </member>
        <member name="T:Ninject.Infrastructure.Introspection.ExceptionFormatter">
            <summary>
            Provides meaningful exception messages.
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.ModulesWithNullOrEmptyNamesAreNotSupported">
            <summary>
            Generates a message saying that modules without names are not supported.
            </summary>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.TargetDoesNotHaveADefaultValue(Ninject.Planning.Targets.ITarget)">
            <summary>
            Generates a message saying that modules without names are not supported.
            </summary>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.ModuleWithSameNameIsAlreadyLoaded(Ninject.Modules.INinjectModule,Ninject.Modules.INinjectModule)">
            <summary>
            Generates a message saying that a module with the same name is already loaded.
            </summary>
            <param name="newModule">The new module.</param>
            <param name="existingModule">The existing module.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.NoModuleLoadedWithTheSpecifiedName(System.String)">
            <summary>
            Generates a message saying that no module has been loaded with the specified name.
            </summary>
            <param name="name">The module name.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.CouldNotUniquelyResolveBinding(Ninject.Activation.IRequest,System.String[])">
            <summary>
            Generates a message saying that the binding could not be uniquely resolved.
            </summary>
            <param name="request">The request.</param>
            <param name="formattedMatchingBindings">The matching bindings, already formatted as strings</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.CouldNotResolveBinding(Ninject.Activation.IRequest)">
            <summary>
            Generates a message saying that the binding could not be resolved on the specified request.
            </summary>
            <param name="request">The request.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.CyclicalDependenciesDetected(Ninject.Activation.IContext)">
            <summary>
            Generates a message saying that the specified context has cyclic dependencies.
            </summary>
            <param name="context">The context.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.InvalidAttributeTypeUsedInBindingCondition(System.String,System.String,System.Type)">
            <summary>
            Generates a message saying that an invalid attribute type is used in the binding condition.
            </summary>
            <param name="serviceNames">The names of the services.</param>
            <param name="methodName">Name of the method.</param>
            <param name="type">The type.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.NoConstructorsAvailable(Ninject.Activation.IContext)">
            <summary>
            Generates a message saying that no constructors are available on the specified context.
            </summary>
            <param name="context">The context.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.NoConstructorsAvailableForComponent(System.Type,System.Type)">
            <summary>
            Generates a message saying that no constructors are available for the given component.
            </summary>
            <param name="component">The component.</param>
            <param name="implementation">The implementation.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.NoSuchComponentRegistered(System.Type)">
            <summary>
            Generates a message saying that the specified component is not registered.
            </summary>
            <param name="component">The component.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.CouldNotResolvePropertyForValueInjection(Ninject.Activation.IRequest,System.String)">
            <summary>
            Generates a message saying that the specified property could not be resolved on the specified request.
            </summary>
            <param name="request">The request.</param>
            <param name="propertyName">The property name.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.ProviderReturnedNull(Ninject.Activation.IContext)">
            <summary>
            Generates a message saying that the provider on the specified context returned null.
            </summary>
            <param name="context">The context.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.ConstructorsAmbiguous(Ninject.Activation.IContext,System.Linq.IGrouping{System.Int32,Ninject.Planning.Directives.ConstructorInjectionDirective})">
            <summary>
            Generates a message saying that the constructor is ambiguous.
            </summary>
            <param name="context">The context.</param>
            <param name="bestDirectives">The best constructor directives.</param>
            <returns>The exception message.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.FormatConstructor(System.Reflection.ConstructorInfo,System.IO.StringWriter)">
            <summary>
            Formats the constructor.
            </summary>
            <param name="constructor">The constructor.</param>
            <param name="sw">The string writer.</param>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.ExceptionFormatter.FormatAttribute(System.IO.StringWriter,System.Attribute)">
            <summary>
            Formats the attribute.
            </summary>
            <param name="sw">The string writer.</param>
            <param name="attribute">The attribute.</param>
        </member>
        <member name="T:Ninject.Infrastructure.Introspection.FormatExtensions">
            <summary>
            Provides extension methods for string formatting
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.FormatExtensions.FormatActivationPath(Ninject.Activation.IRequest)">
            <summary>
            Formats the activation path into a meaningful string representation.
            </summary>
            <param name="request">The request to be formatted.</param>
            <returns>The activation path formatted as string.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.FormatExtensions.Format(Ninject.Planning.Bindings.IBinding,Ninject.Activation.IContext)">
            <summary>
            Formats the given binding into a meaningful string representation. 
            </summary>
            <param name="binding">The binding to be formatted.</param>
            <param name="context">The context.</param>
            <returns>The binding formatted as string</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.FormatExtensions.Format(Ninject.Activation.IRequest)">
            <summary>
            Formats the specified request into a meaningful string representation.
            </summary>
            <param name="request">The request to be formatted.</param>
            <returns>The request formatted as string.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.FormatExtensions.Format(Ninject.Planning.Targets.ITarget)">
            <summary>
            Formats the specified target into a meaningful string representation..
            </summary>
            <param name="target">The target to be formatted.</param>
            <returns>The target formatted as string.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Introspection.FormatExtensions.Format(System.Type)">
            <summary>
            Formats the specified type into a meaningful string representation..
            </summary>
            <param name="type">The type to be formatted.</param>
            <returns>The type formatted as string.</returns>
        </member>
        <member name="T:Ninject.Infrastructure.Language.ExtensionsForIEnumerableOfT">
            <summary>
            Provides extension methods for see cref="IEnumerable{T}"/>
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForIEnumerableOfT.Map``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
            <summary>
            Executes the given action for each of the elements in the enumerable.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="series">The series.</param>
            <param name="action">The action.</param>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForIEnumerableOfT.ToEnumerable``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Converts the given enumerable type to prevent changed on the type behind.
            </summary>
            <typeparam name="T">The type of the enumerable.</typeparam>
            <param name="series">The series.</param>
            <returns>The input type as real enumerable not castable to the original type.</returns>
        </member>
        <member name="T:Ninject.Infrastructure.Language.ExtensionsForMemberInfo">
            <summary>
            Extensions for MemberInfo
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForMemberInfo.HasAttribute``1(System.Reflection.MemberInfo)">
            <summary>
            Determines whether the specified member has attribute.
            </summary>
            <typeparam name="T">The type of the attribute.</typeparam>
            <param name="member">The member.</param>
            <returns>
            	<c>true</c> if the specified member has attribute; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForMemberInfo.HasAttribute(System.Reflection.MemberInfo,System.Type)">
            <summary>
            Determines whether the specified member has attribute.
            </summary>
            <param name="member">The member.</param>
            <param name="type">The type of the attribute.</param>
            <returns>
            	<c>true</c> if the specified member has attribute; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForMemberInfo.GetPropertyFromDeclaredType(System.Reflection.MemberInfo,System.Reflection.PropertyInfo,System.Reflection.BindingFlags)">
            <summary>
            Gets the property info from its declared tpe.
            </summary>
            <param name="memberInfo">The member info.</param>
            <param name="propertyDefinition">The property definition.</param>
            <param name="flags">The flags.</param>
            <returns>The property info from the declared type of the property.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForMemberInfo.IsPrivate(System.Reflection.PropertyInfo)">
            <summary>
            Determines whether the specified property info is private.
            </summary>
            <param name="propertyInfo">The property info.</param>
            <returns>
            	<c>true</c> if the specified property info is private; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForMemberInfo.GetCustomAttributesExtended(System.Reflection.MemberInfo,System.Type,System.Boolean)">
            <summary>
            Gets the custom attributes.
            This version is able to get custom attributes for properties from base types even if the property is none public.
            </summary>
            <param name="member">The member.</param>
            <param name="attributeType">Type of the attribute.</param>
            <param name="inherited">if set to <c>true</c> [inherited].</param>
            <returns></returns>
        </member>
        <member name="T:Ninject.Infrastructure.Language.ExtensionsForType">
            <summary>
            Extension methods for type
            </summary>
            <remarks></remarks>
        </member>
        <member name="M:Ninject.Infrastructure.Language.ExtensionsForType.GetAllBaseTypes(System.Type)">
            <summary>
            Gets an enumerable containing the given type and all its base types
            </summary>
            <param name="type">The type.</param>
            <returns>An enumerable containing the given type and all its base types</returns>
        </member>
        <member name="T:Ninject.Infrastructure.BaseWeakReference">
            <summary>
            Inheritable weak reference base class for Silverlight
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.BaseWeakReference.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Infrastructure.ReferenceEqualWeakReference"/> class.
            </summary>
            <param name="target">The target.</param>
        </member>
        <member name="M:Ninject.Infrastructure.BaseWeakReference.#ctor(System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Infrastructure.ReferenceEqualWeakReference"/> class.
            </summary>
            <param name="target">The target.</param>
            <param name="trackResurrection">if set to <c>true</c> [track resurrection].</param>
        </member>
        <member name="P:Ninject.Infrastructure.BaseWeakReference.IsAlive">
            <summary>
            Gets a value indicating whether this instance is alive.
            </summary>
            <value><c>true</c> if this instance is alive; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Ninject.Infrastructure.BaseWeakReference.Target">
            <summary>
            Gets or sets the target of this weak reference.
            </summary>
            <value>The target of this weak reference.</value>
        </member>
        <member name="T:Ninject.Infrastructure.Future`1">
            <summary>
            Represents a future value.
            </summary>
            <typeparam name="T">The type of value.</typeparam>
        </member>
        <member name="M:Ninject.Infrastructure.Future`1.#ctor(System.Func{`0})">
            <summary>
            Initializes a new instance of the Future&lt;T&gt; class.
            </summary>
            <param name="callback">The callback that will be triggered to read the value.</param>
        </member>
        <member name="M:Ninject.Infrastructure.Future`1.op_Implicit(Ninject.Infrastructure.Future{`0})~`0">
            <summary>
            Gets the value from the future.
            </summary>
            <param name="future">The future.</param>
            <returns>The future value.</returns>
        </member>
        <member name="P:Ninject.Infrastructure.Future`1.Value">
            <summary>
            Gets the value, resolving it if necessary.
            </summary>
        </member>
        <member name="P:Ninject.Infrastructure.Future`1.Callback">
            <summary>
            Gets the callback that will be called to resolve the value.
            </summary>
        </member>
        <member name="T:Ninject.Infrastructure.IHaveBindingConfiguration">
            <summary>
            Indicates the object has a reference to a <see cref="T:Ninject.Planning.Bindings.IBinding"/>.
            </summary>
        </member>
        <member name="P:Ninject.Infrastructure.IHaveBindingConfiguration.BindingConfiguration">
            <summary>
            Gets the binding.
            </summary>
        </member>
        <member name="T:Ninject.Infrastructure.IHaveKernel">
            <summary>
            Indicates that the object has a reference to an <see cref="T:Ninject.IKernel"/>.
            </summary>
        </member>
        <member name="P:Ninject.Infrastructure.IHaveKernel.Kernel">
            <summary>
            Gets the kernel.
            </summary>
        </member>
        <member name="T:Ninject.Infrastructure.Multimap`2">
            <summary>
            A data structure that contains multiple values for a each key.
            </summary>
            <typeparam name="K">The type of key.</typeparam>
            <typeparam name="V">The type of value.</typeparam>
        </member>
        <member name="M:Ninject.Infrastructure.Multimap`2.Add(`0,`1)">
            <summary>
            Adds the specified value for the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Ninject.Infrastructure.Multimap`2.Remove(`0,`1)">
            <summary>
            Removes the specified value for the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <returns><c>True</c> if such a value existed and was removed; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Multimap`2.RemoveAll(`0)">
            <summary>
            Removes all values for the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns><c>True</c> if any such values existed; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Multimap`2.Clear">
            <summary>
            Removes all values.
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.Multimap`2.ContainsKey(`0)">
            <summary>
            Determines whether the multimap contains any values for the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns><c>True</c> if the multimap has one or more values for the specified key; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Multimap`2.ContainsValue(`0,`1)">
            <summary>
            Determines whether the multimap contains the specified value for the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <returns><c>True</c> if the multimap contains such a value; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Infrastructure.Multimap`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a the multimap.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the multimap.</returns>
        </member>
        <member name="P:Ninject.Infrastructure.Multimap`2.Item(`0)">
            <summary>
            Gets the collection of values stored under the specified key.
            </summary>
            <param name="key">The key.</param>
        </member>
        <member name="P:Ninject.Infrastructure.Multimap`2.Keys">
            <summary>
            Gets the collection of keys.
            </summary>
        </member>
        <member name="P:Ninject.Infrastructure.Multimap`2.Values">
            <summary>
            Gets the collection of collections of values.
            </summary>
        </member>
        <member name="T:Ninject.Infrastructure.ReferenceEqualWeakReference">
            <summary>
            Weak reference that can be used in collections. It is equal to the
            object it references and has the same hash code.
            </summary>
        </member>
        <member name="M:Ninject.Infrastructure.ReferenceEqualWeakReference.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Infrastructure.ReferenceEqualWeakReference"/> class.
            </summary>
            <param name="target">The target.</param>
        </member>
        <member name="M:Ninject.Infrastructure.ReferenceEqualWeakReference.#ctor(System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Infrastructure.ReferenceEqualWeakReference"/> class.
            </summary>
            <param name="target">The target.</param>
            <param name="trackResurrection">if set to <c>true</c> [track resurrection].</param>
        </member>
        <member name="M:Ninject.Infrastructure.ReferenceEqualWeakReference.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
                <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.NullReferenceException">
            The <paramref name="obj"/> parameter is null.
            </exception>
        </member>
        <member name="M:Ninject.Infrastructure.ReferenceEqualWeakReference.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="T:Ninject.Infrastructure.StandardScopeCallbacks">
            <summary>
            Scope callbacks for standard scopes.
            </summary>
        </member>
        <member name="F:Ninject.Infrastructure.StandardScopeCallbacks.Transient">
            <summary>
            Gets the callback for transient scope.
            </summary>
        </member>
        <member name="F:Ninject.Infrastructure.StandardScopeCallbacks.Singleton">
            <summary>
            Gets the callback for singleton scope.
            </summary>
        </member>
        <member name="F:Ninject.Infrastructure.StandardScopeCallbacks.Thread">
            <summary>
            Gets the callback for thread scope.
            </summary>
        </member>
        <member name="T:Ninject.Injection.ConstructorInjector">
            <summary>
            A delegate that can inject values into a constructor.
            </summary>
        </member>
        <member name="T:Ninject.Injection.DynamicMethodInjectorFactory">
            <summary>
            Creates injectors for members via <see cref="T:System.Reflection.Emit.DynamicMethod"/>s.
            </summary>
        </member>
        <member name="T:Ninject.Injection.IInjectorFactory">
            <summary>
            Creates injectors from members.
            </summary>
        </member>
        <member name="M:Ninject.Injection.IInjectorFactory.Create(System.Reflection.ConstructorInfo)">
            <summary>
            Gets or creates an injector for the specified constructor.
            </summary>
            <param name="constructor">The constructor.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="M:Ninject.Injection.IInjectorFactory.Create(System.Reflection.PropertyInfo)">
            <summary>
            Gets or creates an injector for the specified property.
            </summary>
            <param name="property">The property.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="M:Ninject.Injection.IInjectorFactory.Create(System.Reflection.MethodInfo)">
            <summary>
            Gets or creates an injector for the specified method.
            </summary>
            <param name="method">The method.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="M:Ninject.Injection.DynamicMethodInjectorFactory.Create(System.Reflection.ConstructorInfo)">
            <summary>
            Gets or creates an injector for the specified constructor.
            </summary>
            <param name="constructor">The constructor.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="M:Ninject.Injection.DynamicMethodInjectorFactory.Create(System.Reflection.PropertyInfo)">
            <summary>
            Gets or creates an injector for the specified property.
            </summary>
            <param name="property">The property.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="M:Ninject.Injection.DynamicMethodInjectorFactory.Create(System.Reflection.MethodInfo)">
            <summary>
            Gets or creates an injector for the specified method.
            </summary>
            <param name="method">The method.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="T:Ninject.Injection.MethodInjector">
            <summary>
            A delegate that can inject values into a method.
            </summary>
        </member>
        <member name="T:Ninject.Injection.PropertyInjector">
            <summary>
            A delegate that can inject values into a property.
            </summary>
        </member>
        <member name="T:Ninject.Injection.ReflectionInjectorFactory">
            <summary>
            Creates injectors from members via reflective invocation.
            </summary>
        </member>
        <member name="M:Ninject.Injection.ReflectionInjectorFactory.Create(System.Reflection.ConstructorInfo)">
            <summary>
            Gets or creates an injector for the specified constructor.
            </summary>
            <param name="constructor">The constructor.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="M:Ninject.Injection.ReflectionInjectorFactory.Create(System.Reflection.PropertyInfo)">
            <summary>
            Gets or creates an injector for the specified property.
            </summary>
            <param name="property">The property.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="M:Ninject.Injection.ReflectionInjectorFactory.Create(System.Reflection.MethodInfo)">
            <summary>
            Gets or creates an injector for the specified method.
            </summary>
            <param name="method">The method.</param>
            <returns>The created injector.</returns>
        </member>
        <member name="T:Ninject.Modules.AssemblyNameRetriever">
            <summary>
            Retrieves assembly names from file names using a temporary app domain.
            </summary>
        </member>
        <member name="T:Ninject.Modules.IAssemblyNameRetriever">
            <summary>
            Retrieves assembly names from file names using a temporary app domain.
            </summary>
        </member>
        <member name="M:Ninject.Modules.IAssemblyNameRetriever.GetAssemblyNames(System.Collections.Generic.IEnumerable{System.String},System.Predicate{System.Reflection.Assembly})">
            <summary>
            Gets all assembly names of the assemblies in the given files that match the filter.
            </summary>
            <param name="filenames">The filenames.</param>
            <param name="filter">The filter.</param>
            <returns>All assembly names of the assemblies in the given files that match the filter.</returns>
        </member>
        <member name="M:Ninject.Modules.AssemblyNameRetriever.GetAssemblyNames(System.Collections.Generic.IEnumerable{System.String},System.Predicate{System.Reflection.Assembly})">
            <summary>
            Gets all assembly names of the assemblies in the given files that match the filter.
            </summary>
            <param name="filenames">The filenames.</param>
            <param name="filter">The filter.</param>
            <returns>All assembly names of the assemblies in the given files that match the filter.</returns>
        </member>
        <member name="M:Ninject.Modules.AssemblyNameRetriever.CreateTemporaryAppDomain">
            <summary>
            Creates a temporary app domain.
            </summary>
            <returns>The created app domain.</returns>
        </member>
        <member name="T:Ninject.Modules.AssemblyNameRetriever.AssemblyChecker">
            <summary>
            This class is loaded into the temporary appdomain to load and check if the assemblies match the filter.
            </summary>
        </member>
        <member name="M:Ninject.Modules.AssemblyNameRetriever.AssemblyChecker.GetAssemblyNames(System.Collections.Generic.IEnumerable{System.String},System.Predicate{System.Reflection.Assembly})">
            <summary>
            Gets the assembly names of the assemblies matching the filter.
            </summary>
            <param name="filenames">The filenames.</param>
            <param name="filter">The filter.</param>
            <returns>All assembly names of the assemblies matching the filter.</returns>
        </member>
        <member name="T:Ninject.Modules.CompiledModuleLoaderPlugin">
            <summary>
            Loads modules from compiled assemblies.
            </summary>
        </member>
        <member name="T:Ninject.Modules.IModuleLoaderPlugin">
            <summary>
            Loads modules at runtime by searching external files.
            </summary>
        </member>
        <member name="M:Ninject.Modules.IModuleLoaderPlugin.LoadModules(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Loads modules from the specified files.
            </summary>
            <param name="filenames">The names of the files to load modules from.</param>
        </member>
        <member name="P:Ninject.Modules.IModuleLoaderPlugin.SupportedExtensions">
            <summary>
            Gets the file extensions that the plugin understands how to load.
            </summary>
        </member>
        <member name="F:Ninject.Modules.CompiledModuleLoaderPlugin.assemblyNameRetriever">
            <summary>
            The assembly name retriever.
            </summary>
        </member>
        <member name="F:Ninject.Modules.CompiledModuleLoaderPlugin.Extensions">
            <summary>
            The file extensions that are supported.
            </summary>
        </member>
        <member name="M:Ninject.Modules.CompiledModuleLoaderPlugin.#ctor(Ninject.IKernel,Ninject.Modules.IAssemblyNameRetriever)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Modules.CompiledModuleLoaderPlugin"/> class.
            </summary>
            <param name="kernel">The kernel into which modules will be loaded.</param>
            <param name="assemblyNameRetriever">The assembly name retriever.</param>
        </member>
        <member name="M:Ninject.Modules.CompiledModuleLoaderPlugin.LoadModules(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Loads modules from the specified files.
            </summary>
            <param name="filenames">The names of the files to load modules from.</param>
        </member>
        <member name="P:Ninject.Modules.CompiledModuleLoaderPlugin.Kernel">
            <summary>
            Gets the kernel into which modules will be loaded.
            </summary>
        </member>
        <member name="P:Ninject.Modules.CompiledModuleLoaderPlugin.SupportedExtensions">
            <summary>
            Gets the file extensions that the plugin understands how to load.
            </summary>
        </member>
        <member name="T:Ninject.Modules.IModuleLoader">
            <summary>
            Finds modules defined in external files.
            </summary>
        </member>
        <member name="M:Ninject.Modules.IModuleLoader.LoadModules(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Loads any modules found in the files that match the specified patterns.
            </summary>
            <param name="patterns">The patterns to search.</param>
        </member>
        <member name="T:Ninject.Modules.INinjectModule">
            <summary>
            A pluggable unit that can be loaded into an <see cref="T:Ninject.IKernel"/>.
            </summary>
        </member>
        <member name="M:Ninject.Modules.INinjectModule.OnLoad(Ninject.IKernel)">
            <summary>
            Called when the module is loaded into a kernel.
            </summary>
            <param name="kernel">The kernel that is loading the module.</param>
        </member>
        <member name="M:Ninject.Modules.INinjectModule.OnUnload(Ninject.IKernel)">
            <summary>
            Called when the module is unloaded from a kernel.
            </summary>
            <param name="kernel">The kernel that is unloading the module.</param>
        </member>
        <member name="M:Ninject.Modules.INinjectModule.OnVerifyRequiredModules">
            <summary>
            Called after loading the modules. A module can verify here if all other required modules are loaded.
            </summary>
        </member>
        <member name="P:Ninject.Modules.INinjectModule.Name">
            <summary>
            Gets the module's name.
            </summary>
        </member>
        <member name="T:Ninject.Modules.ModuleLoader">
            <summary>
            Automatically finds and loads modules from assemblies.
            </summary>
        </member>
        <member name="M:Ninject.Modules.ModuleLoader.#ctor(Ninject.IKernel)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Modules.ModuleLoader"/> class.
            </summary>
            <param name="kernel">The kernel into which modules will be loaded.</param>
        </member>
        <member name="M:Ninject.Modules.ModuleLoader.LoadModules(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Loads any modules found in the files that match the specified patterns.
            </summary>
            <param name="patterns">The patterns to search.</param>
        </member>
        <member name="P:Ninject.Modules.ModuleLoader.Kernel">
            <summary>
            Gets or sets the kernel into which modules will be loaded.
            </summary>
        </member>
        <member name="T:Ninject.Modules.NinjectModule">
            <summary>
            A loadable unit that defines bindings for your application.
            </summary>
        </member>
        <member name="T:Ninject.Syntax.BindingRoot">
            <summary>
            Provides a path to register bindings.
            </summary>
        </member>
        <member name="T:Ninject.Syntax.IBindingRoot">
            <summary>
            Provides a path to register bindings.
            </summary>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Bind``1">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T">The service to bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Bind``2">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T1">The first service to bind.</typeparam>
            <typeparam name="T2">The second service to bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Bind``3">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T1">The first service to bind.</typeparam>
            <typeparam name="T2">The second service to bind.</typeparam>
            <typeparam name="T3">The third service to bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Bind``4">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T1">The first service to bind.</typeparam>
            <typeparam name="T2">The second service to bind.</typeparam>
            <typeparam name="T3">The third service to bind.</typeparam>
            <typeparam name="T4">The fourth service to bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Bind(System.Type[])">
            <summary>
            Declares a binding from the service to itself.
            </summary>
            <param name="services">The services to bind.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Unbind``1">
            <summary>
            Unregisters all bindings for the specified service.
            </summary>
            <typeparam name="T">The service to unbind.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Unbind(System.Type)">
            <summary>
            Unregisters all bindings for the specified service.
            </summary>
            <param name="service">The service to unbind.</param>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Rebind``1">
            <summary>
            Removes any existing bindings for the specified service, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Rebind``2">
            <summary>
            Removes any existing bindings for the specified services, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <typeparam name="T2">The second service to re-bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Rebind``3">
            <summary>
            Removes any existing bindings for the specified services, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <typeparam name="T2">The second service to re-bind.</typeparam>
            <typeparam name="T3">The third service to re-bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Rebind``4">
            <summary>
            Removes any existing bindings for the specified services, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <typeparam name="T2">The second service to re-bind.</typeparam>
            <typeparam name="T3">The third service to re-bind.</typeparam>
            <typeparam name="T4">The fourth service to re-bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.Rebind(System.Type[])">
            <summary>
            Removes any existing bindings for the specified services, and declares a new one.
            </summary>
            <param name="services">The services to re-bind.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.AddBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Registers the specified binding.
            </summary>
            <param name="binding">The binding to add.</param>
        </member>
        <member name="M:Ninject.Syntax.IBindingRoot.RemoveBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Unregisters the specified binding.
            </summary>
            <param name="binding">The binding to remove.</param>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Bind``1">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T">The service to bind.</typeparam>
            <returns>The fluent syntax</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Bind``2">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T1">The first service to bind.</typeparam>
            <typeparam name="T2">The second service to bind.</typeparam>
            <returns>The fluent syntax</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Bind``3">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T1">The first service to bind.</typeparam>
            <typeparam name="T2">The second service to bind.</typeparam>
            <typeparam name="T3">The third service to bind.</typeparam>
            <returns>The fluent syntax</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Bind``4">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <typeparam name="T1">The first service to bind.</typeparam>
            <typeparam name="T2">The second service to bind.</typeparam>
            <typeparam name="T3">The third service to bind.</typeparam>
            <typeparam name="T4">The fourth service to bind.</typeparam>
            <returns>The fluent syntax</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Bind(System.Type[])">
            <summary>
            Declares a binding for the specified service.
            </summary>
            <param name="services">The services to bind.</param>
            <returns>The fluent syntax</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Unbind``1">
            <summary>
            Unregisters all bindings for the specified service.
            </summary>
            <typeparam name="T">The service to unbind.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Unbind(System.Type)">
            <summary>
            Unregisters all bindings for the specified service.
            </summary>
            <param name="service">The service to unbind.</param>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Rebind``1">
            <summary>
            Removes any existing bindings for the specified service, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <returns>The fluent syntax</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Rebind``2">
            <summary>
            Removes any existing bindings for the specified services, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <typeparam name="T2">The second service to re-bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Rebind``3">
            <summary>
            Removes any existing bindings for the specified services, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <typeparam name="T2">The second service to re-bind.</typeparam>
            <typeparam name="T3">The third service to re-bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Rebind``4">
            <summary>
            Removes any existing bindings for the specified services, and declares a new one.
            </summary>
            <typeparam name="T1">The first service to re-bind.</typeparam>
            <typeparam name="T2">The second service to re-bind.</typeparam>
            <typeparam name="T3">The third service to re-bind.</typeparam>
            <typeparam name="T4">The fourth service to re-bind.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.Rebind(System.Type[])">
            <summary>
            Removes any existing bindings for the specified service, and declares a new one.
            </summary>
            <param name="services">The services to re-bind.</param>
            <returns>The fluent syntax</returns>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.AddBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Registers the specified binding.
            </summary>
            <param name="binding">The binding to add.</param>
        </member>
        <member name="M:Ninject.Syntax.BindingRoot.RemoveBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Unregisters the specified binding.
            </summary>
            <param name="binding">The binding to remove.</param>
        </member>
        <member name="P:Ninject.Syntax.BindingRoot.KernelInstance">
            <summary>
            Gets the kernel.
            </summary>
            <value>The kernel.</value>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Modules.NinjectModule"/> class.
            </summary>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.OnLoad(Ninject.IKernel)">
            <summary>
            Called when the module is loaded into a kernel.
            </summary>
            <param name="kernel">The kernel that is loading the module.</param>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.OnUnload(Ninject.IKernel)">
            <summary>
            Called when the module is unloaded from a kernel.
            </summary>
            <param name="kernel">The kernel that is unloading the module.</param>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.OnVerifyRequiredModules">
            <summary>
            Called after loading the modules. A module can verify here if all other required modules are loaded.
            </summary>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.Load">
            <summary>
            Loads the module into the kernel.
            </summary>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.Unload">
            <summary>
            Unloads the module from the kernel.
            </summary>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.VerifyRequiredModulesAreLoaded">
            <summary>
            Called after loading the modules. A module can verify here if all other required modules are loaded.
            </summary>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.Unbind(System.Type)">
            <summary>
            Unregisters all bindings for the specified service.
            </summary>
            <param name="service">The service to unbind.</param>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.AddBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Registers the specified binding.
            </summary>
            <param name="binding">The binding to add.</param>
        </member>
        <member name="M:Ninject.Modules.NinjectModule.RemoveBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Unregisters the specified binding.
            </summary>
            <param name="binding">The binding to remove.</param>
        </member>
        <member name="P:Ninject.Modules.NinjectModule.Kernel">
            <summary>
            Gets the kernel that the module is loaded into.
            </summary>
        </member>
        <member name="P:Ninject.Modules.NinjectModule.Name">
            <summary>
            Gets the module's name. Only a single module with a given name can be loaded at one time.
            </summary>
        </member>
        <member name="P:Ninject.Modules.NinjectModule.Bindings">
            <summary>
            Gets the bindings that were registered by the module.
            </summary>
        </member>
        <member name="P:Ninject.Modules.NinjectModule.KernelInstance">
            <summary>
            Gets the kernel.
            </summary>
            <value>The kernel.</value>
        </member>
        <member name="T:Ninject.Parameters.ConstructorArgument">
            <summary>
            Overrides the injected value of a constructor argument.
            </summary>
        </member>
        <member name="T:Ninject.Parameters.Parameter">
            <summary>
            Modifies an activation process in some way.
            </summary>
        </member>
        <member name="T:Ninject.Parameters.IParameter">
            <summary>
            Modifies an activation process in some way.
            </summary>
        </member>
        <member name="M:Ninject.Parameters.IParameter.GetValue(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Gets the value for the parameter within the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>The value for the parameter.</returns>
        </member>
        <member name="P:Ninject.Parameters.IParameter.Name">
            <summary>
            Gets the name of the parameter.
            </summary>
        </member>
        <member name="P:Ninject.Parameters.IParameter.ShouldInherit">
            <summary>
            Gets a value indicating whether the parameter should be inherited into child requests.
            </summary>
        </member>
        <member name="M:Ninject.Parameters.Parameter.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.Parameter"/> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="value">The value of the parameter.</param>
            <param name="shouldInherit">Whether the parameter should be inherited into child requests.</param>
        </member>
        <member name="M:Ninject.Parameters.Parameter.#ctor(System.String,System.Func{Ninject.Activation.IContext,System.Object},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.Parameter"/> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="valueCallback">The callback that will be triggered to get the parameter's value.</param>
            <param name="shouldInherit">Whether the parameter should be inherited into child requests.</param>
        </member>
        <member name="M:Ninject.Parameters.Parameter.#ctor(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.Parameter"/> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="valueCallback">The callback that will be triggered to get the parameter's value.</param>
            <param name="shouldInherit">Whether the parameter should be inherited into child requests.</param>
        </member>
        <member name="M:Ninject.Parameters.Parameter.GetValue(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Gets the value for the parameter within the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>The value for the parameter.</returns>
        </member>
        <member name="M:Ninject.Parameters.Parameter.Equals(System.Object)">
            <summary>
            Determines whether the object equals the specified object.
            </summary>
            <param name="obj">An object to compare with this object.</param>
            <returns><c>True</c> if the objects are equal; otherwise <c>false</c></returns>
        </member>
        <member name="M:Ninject.Parameters.Parameter.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the object.</returns>
        </member>
        <member name="M:Ninject.Parameters.Parameter.Equals(Ninject.Parameters.IParameter)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns><c>True</c> if the objects are equal; otherwise <c>false</c></returns>
        </member>
        <member name="P:Ninject.Parameters.Parameter.Name">
            <summary>
            Gets the name of the parameter.
            </summary>
        </member>
        <member name="P:Ninject.Parameters.Parameter.ShouldInherit">
            <summary>
            Gets a value indicating whether the parameter should be inherited into child requests.
            </summary>
        </member>
        <member name="P:Ninject.Parameters.Parameter.ValueCallback">
            <summary>
            Gets or sets the callback that will be triggered to get the parameter's value.
            </summary>
        </member>
        <member name="T:Ninject.Parameters.IConstructorArgument">
            <summary>
            Defines the interface for constructor arguments.
            </summary>
        </member>
        <member name="M:Ninject.Parameters.IConstructorArgument.AppliesToTarget(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Determines if the parameter applies to the given target.
            </summary>
            <remarks>
            Only one parameter may return true.
            </remarks>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>Tre if the parameter applies in the specified context to the specified target.</returns>
        </member>
        <member name="M:Ninject.Parameters.ConstructorArgument.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="value">The value to inject into the property.</param>
        </member>
        <member name="M:Ninject.Parameters.ConstructorArgument.#ctor(System.String,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="valueCallback">The callback to invoke to get the value that should be injected.</param>
        </member>
        <member name="M:Ninject.Parameters.ConstructorArgument.#ctor(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="valueCallback">The callback to invoke to get the value that should be injected.</param>
        </member>
        <member name="M:Ninject.Parameters.ConstructorArgument.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="value">The value to inject into the property.</param>
            <param name="shouldInherit">Whether the parameter should be inherited into child requests.</param>
        </member>
        <member name="M:Ninject.Parameters.ConstructorArgument.#ctor(System.String,System.Func{Ninject.Activation.IContext,System.Object},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="valueCallback">The callback to invoke to get the value that should be injected.</param>
            <param name="shouldInherit">if set to <c>true</c> [should inherit].</param>
        </member>
        <member name="M:Ninject.Parameters.ConstructorArgument.#ctor(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="valueCallback">The callback to invoke to get the value that should be injected.</param>
            <param name="shouldInherit">if set to <c>true</c> [should inherit].</param>
        </member>
        <member name="M:Ninject.Parameters.ConstructorArgument.AppliesToTarget(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Determines if the parameter applies to the given target.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>
            Tre if the parameter applies in the specified context to the specified target.
            </returns>
            <remarks>
            Only one parameter may return true.
            </remarks>
        </member>
        <member name="T:Ninject.Parameters.IPropertyValue">
            <summary>
            Overrides the injected value of a property.
            </summary>
        </member>
        <member name="T:Ninject.Parameters.PropertyValue">
            <summary>
            Overrides the injected value of a property.
            </summary>
        </member>
        <member name="M:Ninject.Parameters.PropertyValue.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.PropertyValue"/> class.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="value">The value to inject into the property.</param>
        </member>
        <member name="M:Ninject.Parameters.PropertyValue.#ctor(System.String,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.PropertyValue"/> class.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="valueCallback">The callback to invoke to get the value that should be injected.</param>
        </member>
        <member name="M:Ninject.Parameters.PropertyValue.#ctor(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.PropertyValue"/> class.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="valueCallback">The callback to invoke to get the value that should be injected.</param>
        </member>
        <member name="T:Ninject.Parameters.TypeMatchingConstructorArgument">
            <summary>
            Overrides the injected value of a constructor argument.
            </summary>
        </member>
        <member name="M:Ninject.Parameters.TypeMatchingConstructorArgument.#ctor(System.Type,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.TypeMatchingConstructorArgument"/> class.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="valueCallback">The callback that will be triggered to get the parameter's value.</param>
        </member>
        <member name="M:Ninject.Parameters.TypeMatchingConstructorArgument.#ctor(System.Type,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object},System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.TypeMatchingConstructorArgument"/> class.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="valueCallback">The callback that will be triggered to get the parameter's value.</param>
            <param name="shouldInherit">Whether the parameter should be inherited into child requests.</param>
        </member>
        <member name="M:Ninject.Parameters.TypeMatchingConstructorArgument.AppliesToTarget(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Determines if the parameter applies to the given target.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>
            True if the parameter applies in the specified context to the specified target.
            </returns>
            <remarks>
            Only one parameter may return true.
            </remarks>
        </member>
        <member name="M:Ninject.Parameters.TypeMatchingConstructorArgument.GetValue(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Gets the value for the parameter within the specified context.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>The value for the parameter.</returns>
        </member>
        <member name="M:Ninject.Parameters.TypeMatchingConstructorArgument.Equals(Ninject.Parameters.IParameter)">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns><c>True</c> if the objects are equal; otherwise <c>false</c></returns>
        </member>
        <member name="M:Ninject.Parameters.TypeMatchingConstructorArgument.Equals(System.Object)">
            <summary>
            Determines whether the object equals the specified object.
            </summary>
            <param name="obj">An object to compare with this object.</param>
            <returns><c>True</c> if the objects are equal; otherwise <c>false</c></returns>
        </member>
        <member name="M:Ninject.Parameters.TypeMatchingConstructorArgument.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the object.</returns>
        </member>
        <member name="P:Ninject.Parameters.TypeMatchingConstructorArgument.Name">
            <summary>
            Gets the name of the parameter.
            </summary>
        </member>
        <member name="P:Ninject.Parameters.TypeMatchingConstructorArgument.ShouldInherit">
            <summary>
            Gets a value indicating whether the parameter should be inherited into child requests.
            </summary>
        </member>
        <member name="P:Ninject.Parameters.TypeMatchingConstructorArgument.ValueCallback">
            <summary>
            Gets or sets the callback that will be triggered to get the parameter's value.
            </summary>
        </member>
        <member name="T:Ninject.Parameters.WeakConstructorArgument">
            <summary>
            Overrides the injected value of a constructor argument.
            </summary>
        </member>
        <member name="F:Ninject.Parameters.WeakConstructorArgument.weakReference">
            <summary>
            A weak reference to the constructor argument value.
            </summary>
        </member>
        <member name="M:Ninject.Parameters.WeakConstructorArgument.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="value">The value to inject into the property.</param>
        </member>
        <member name="M:Ninject.Parameters.WeakConstructorArgument.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.ConstructorArgument"/> class.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="value">The value to inject into the property.</param>
            <param name="shouldInherit">Whether the parameter should be inherited into child requests.</param>
        </member>
        <member name="M:Ninject.Parameters.WeakConstructorArgument.AppliesToTarget(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Determines if the parameter applies to the given target.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>
            Tre if the parameter applies in the specified context to the specified target.
            </returns>
            <remarks>
            Only one parameter may return true.
            </remarks>
        </member>
        <member name="T:Ninject.Parameters.WeakPropertyValue">
            <summary>
            Overrides the injected value of a property.
            Keeps a weak reference to the value.
            </summary>
        </member>
        <member name="M:Ninject.Parameters.WeakPropertyValue.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Parameters.WeakPropertyValue"/> class.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="value">The value to inject into the property.</param>
        </member>
        <member name="T:Ninject.Planning.Bindings.Resolvers.DefaultValueBindingResolver">
            <summary>
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.Resolvers.IMissingBindingResolver">
            <summary>
             Contains logic about which bindings to use for a given service request
             when other attempts have failed.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.Resolvers.IMissingBindingResolver.Resolve(Ninject.Infrastructure.Multimap{System.Type,Ninject.Planning.Bindings.IBinding},Ninject.Activation.IRequest)">
            <summary>
            Returns any bindings from the specified collection that match the specified request.
            </summary>
            <param name="bindings">The multimap of all registered bindings.</param>
            <param name="request">The request in question.</param>
            <returns>The series of matching bindings.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.Resolvers.DefaultValueBindingResolver.Resolve(Ninject.Infrastructure.Multimap{System.Type,Ninject.Planning.Bindings.IBinding},Ninject.Activation.IRequest)">
            <summary>
            Returns any bindings from the specified collection that match the specified service.
            </summary>
            <param name="bindings">The multimap of all registered bindings.</param>
            <param name="request">The service in question.</param>
            <returns>The series of matching bindings.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.Resolvers.IBindingResolver">
            <summary>
            Contains logic about which bindings to use for a given service request.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.Resolvers.IBindingResolver.Resolve(Ninject.Infrastructure.Multimap{System.Type,Ninject.Planning.Bindings.IBinding},System.Type)">
            <summary>
            Returns any bindings from the specified collection that match the specified service.
            </summary>
            <param name="bindings">The multimap of all registered bindings.</param>
            <param name="service">The service in question.</param>
            <returns>The series of matching bindings.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.Resolvers.OpenGenericBindingResolver">
            <summary>
            Resolves bindings for open generic types.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.Resolvers.OpenGenericBindingResolver.Resolve(Ninject.Infrastructure.Multimap{System.Type,Ninject.Planning.Bindings.IBinding},System.Type)">
            <summary>
            Returns any bindings from the specified collection that match the specified service.
            </summary>
            <param name="bindings">The multimap of all registered bindings.</param>
            <param name="service">The service in question.</param>
            <returns>The series of matching bindings.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.Resolvers.SelfBindingResolver">
            <summary>
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.Resolvers.SelfBindingResolver.Resolve(Ninject.Infrastructure.Multimap{System.Type,Ninject.Planning.Bindings.IBinding},Ninject.Activation.IRequest)">
            <summary>
            Returns any bindings from the specified collection that match the specified service.
            </summary>
            <param name="bindings">The multimap of all registered bindings.</param>
            <param name="request">The service in question.</param>
            <returns>The series of matching bindings.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.Resolvers.SelfBindingResolver.TypeIsSelfBindable(System.Type)">
            <summary>
            Returns a value indicating whether the specified service is self-bindable.
            </summary>
            <param name="service">The service.</param>
            <returns><see langword="True"/> if the type is self-bindable; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.Resolvers.StandardBindingResolver">
            <summary>
            Resolves bindings that have been registered directly for the service.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.Resolvers.StandardBindingResolver.Resolve(Ninject.Infrastructure.Multimap{System.Type,Ninject.Planning.Bindings.IBinding},System.Type)">
            <summary>
            Returns any bindings from the specified collection that match the specified service.
            </summary>
            <param name="bindings">The multimap of all registered bindings.</param>
            <param name="service">The service in question.</param>
            <returns>The series of matching bindings.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.Binding">
            <summary>
            Contains information about a service registration.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.IBinding">
            <summary>
            Contains information about a service registration.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.IBindingConfiguration">
            <summary>
            The configuration of a binding.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.IBindingConfiguration.GetProvider(Ninject.Activation.IContext)">
            <summary>
            Gets the provider for the binding.
            </summary>
            <param name="context">The context.</param>
            <returns>The provider to use.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.IBindingConfiguration.GetScope(Ninject.Activation.IContext)">
            <summary>
            Gets the scope for the binding, if any.
            </summary>
            <param name="context">The context.</param>
            <returns>The object that will act as the scope, or <see langword="null"/> if the service is transient.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.IBindingConfiguration.Matches(Ninject.Activation.IRequest)">
            <summary>
            Determines whether the specified request satisfies the condition defined on the binding,
            if one was defined.
            </summary>
            <param name="request">The request.</param>
            <returns><c>True</c> if the request satisfies the condition; otherwise <c>false</c>.</returns>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.Metadata">
            <summary>
            Gets the binding's metadata.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.Target">
            <summary>
            Gets or sets the type of target for the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.IsImplicit">
            <summary>
            Gets or sets a value indicating whether the binding was implicitly registered.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.IsConditional">
            <summary>
            Gets a value indicating whether the binding has a condition associated with it.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.Condition">
            <summary>
            Gets or sets the condition defined for the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.ProviderCallback">
            <summary>
            Gets or sets the callback that returns the provider that should be used by the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.ScopeCallback">
            <summary>
            Gets or sets the callback that returns the object that will act as the binding's scope.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.Parameters">
            <summary>
            Gets the parameters defined for the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.ActivationActions">
            <summary>
            Gets the actions that should be called after instances are activated via the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingConfiguration.DeactivationActions">
            <summary>
            Gets the actions that should be called before instances are deactivated via the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBinding.BindingConfiguration">
            <summary>
            Gets the binding configuration.
            </summary>
            <value>The binding configuration.</value>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBinding.Service">
            <summary>
            Gets the service type that is controlled by the binding.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.Binding.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.Binding"/> class.
            </summary>
            <param name="service">The service that is controlled by the binding.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.Binding.#ctor(System.Type,Ninject.Planning.Bindings.IBindingConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.Binding"/> class.
            </summary>
            <param name="service">The service that is controlled by the binding.</param>
            <param name="configuration">The binding configuration.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.Binding.GetProvider(Ninject.Activation.IContext)">
            <summary>
            Gets the provider for the binding.
            </summary>
            <param name="context">The context.</param>
            <returns>The provider to use.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.Binding.GetScope(Ninject.Activation.IContext)">
            <summary>
            Gets the scope for the binding, if any.
            </summary>
            <param name="context">The context.</param>
            <returns>
            The object that will act as the scope, or <see langword="null"/> if the service is transient.
            </returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.Binding.Matches(Ninject.Activation.IRequest)">
            <summary>
            Determines whether the specified request satisfies the condition defined on the binding,
            if one was defined.
            </summary>
            <param name="request">The request.</param>
            <returns>
                <c>True</c> if the request satisfies the condition; otherwise <c>false</c>.
            </returns>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.BindingConfiguration">
            <summary>
            Gets or sets the binding configuration.
            </summary>
            <value>The binding configuration.</value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.Service">
            <summary>
            Gets the service type that is controlled by the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.Metadata">
            <summary>
            Gets the binding's metadata.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.Target">
            <summary>
            Gets or sets the type of target for the binding.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.IsImplicit">
            <summary>
            Gets or sets a value indicating whether the binding was implicitly registered.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.IsConditional">
            <summary>
            Gets a value indicating whether the binding has a condition associated with it.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.Condition">
            <summary>
            Gets or sets the condition defined for the binding.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.ProviderCallback">
            <summary>
            Gets or sets the callback that returns the provider that should be used by the binding.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.ScopeCallback">
            <summary>
            Gets or sets the callback that returns the object that will act as the binding's scope.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.Parameters">
            <summary>
            Gets the parameters defined for the binding.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.ActivationActions">
            <summary>
            Gets the actions that should be called after instances are activated via the binding.
            </summary>
            <value></value>
        </member>
        <member name="P:Ninject.Planning.Bindings.Binding.DeactivationActions">
            <summary>
            Gets the actions that should be called before instances are deactivated via the binding.
            </summary>
            <value></value>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingBuilder">
            <summary>
            Provides a root for the fluent syntax associated with an <see cref="P:Ninject.Planning.Bindings.BindingBuilder.BindingConfiguration"/>.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.#ctor(Ninject.Planning.Bindings.IBindingConfiguration,Ninject.IKernel,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.BindingBuilder"/> class.
            </summary>
            <param name="bindingConfiguration">The binding to build.</param>
            <param name="kernel">The kernel.</param>
            <param name="serviceNames">The names of the services.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.InternalTo``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.InternalTo``1(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="T">The type of the returned syntax.</typeparam>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.InternalToConfiguration``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.InternalToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.InternalToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.ToProviderInternal``2">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.ToProviderInternal``1(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="T">The type of the returned fleunt syntax</typeparam>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.InternalToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.AddConstructorArguments(System.Linq.Expressions.NewExpression,System.Linq.Expressions.ParameterExpression)">
            <summary>
            Adds the constructor arguments for the specified constructor expression.
            </summary>
            <param name="ctorExpression">The ctor expression.</param>
            <param name="constructorArgumentSyntaxParameterExpression">The constructor argument syntax parameter expression.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.AddConstructorArgument(System.Linq.Expressions.Expression,System.String,System.Linq.Expressions.ParameterExpression)">
            <summary>
            Adds a constructor argument for the specified argument expression.
            </summary>
            <param name="argument">The argument.</param>
            <param name="argumentName">Name of the argument.</param>
            <param name="constructorArgumentSyntaxParameterExpression">The constructor argument syntax parameter expression.</param>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingBuilder.BindingConfiguration">
            <summary>
            Gets the binding being built.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingBuilder.Kernel">
            <summary>
            Gets the kernel.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingBuilder.ServiceNames">
            <summary>
            Gets the names of the services.
            </summary>
            <value>The names of the services.</value>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingBuilder.ConstructorArgumentSyntax">
            <summary>
            Passed to ToConstructor to specify that a constructor value is Injected.
            </summary>
        </member>
        <member name="T:Ninject.Syntax.IConstructorArgumentSyntax">
            <summary>
            Passed to ToConstructor to specify that a constructor value is Injected.
            </summary>
        </member>
        <member name="M:Ninject.Syntax.IConstructorArgumentSyntax.Inject``1">
            <summary>
            Specifies that the argument is injected.
            </summary>
            <typeparam name="T">The type of the parameter</typeparam>
            <returns>Not used. This interface has no implementation.</returns>
        </member>
        <member name="P:Ninject.Syntax.IConstructorArgumentSyntax.Context">
            <summary>
            Gets the context.
            </summary>
            <value>The context.</value>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.ConstructorArgumentSyntax.#ctor(Ninject.Activation.IContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.BindingBuilder.ConstructorArgumentSyntax"/> class.
            </summary>
            <param name="context">The context.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder.ConstructorArgumentSyntax.Inject``1">
            <summary>
            Specifies that the argument is injected.
            </summary>
            <typeparam name="T1">The type of the parameter</typeparam>
            <returns>Not used. This interface has no implementation.</returns>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingBuilder.ConstructorArgumentSyntax.Context">
            <summary>
            Gets the context.
            </summary>
            <value>The context.</value>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingBuilder`4">
            <summary>
            Provides a root for the fluent syntax associated with an <see cref="P:Ninject.Planning.Bindings.BindingBuilder.BindingConfiguration"/>.
            </summary>
            <typeparam name="T1">The first service type.</typeparam>
            <typeparam name="T2">The second service type.</typeparam>
            <typeparam name="T3">The third service type.</typeparam>
            <typeparam name="T4">The fourth service type.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingToSyntax`4">
            <summary>
            Used to define the target of a binding.
            </summary>
            <typeparam name="T1">The first service type to be bound.</typeparam>
            <typeparam name="T2">The second service type to be bound.</typeparam>
            <typeparam name="T3">The third service type to be bound.</typeparam>
            <typeparam name="T4">The fourth service type to be bound.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingSyntax">
            <summary>
            Used to define a basic binding syntax builder.
            </summary>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.ToProvider``2">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`4.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.#ctor(Ninject.Planning.Bindings.IBindingConfiguration,Ninject.IKernel,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.BindingBuilder`4"/> class.
            </summary>
            <param name="bindingConfigurationConfiguration">The binding to build.</param>
            <param name="kernel">The kernel.</param>
            <param name="serviceNames">The names of the services.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.ToProvider``2">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`4.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingBuilder`3">
            <summary>
            Provides a root for the fluent syntax associated with an <see cref="P:Ninject.Planning.Bindings.BindingBuilder.BindingConfiguration"/>.
            </summary>
            <typeparam name="T1">The first service type.</typeparam>
            <typeparam name="T2">The second service type.</typeparam>
            <typeparam name="T3">The third service type.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingToSyntax`3">
            <summary>
            Used to define the target of a binding.
            </summary>
            <typeparam name="T1">The first service type to be bound.</typeparam>
            <typeparam name="T2">The second service type to be bound.</typeparam>
            <typeparam name="T3">The third service type to be bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.ToProvider``2">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`3.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.#ctor(Ninject.Planning.Bindings.IBindingConfiguration,Ninject.IKernel,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.BindingBuilder`3"/> class.
            </summary>
            <param name="bindingConfigurationConfiguration">The binding to build.</param>
            <param name="kernel">The kernel.</param>
            <param name="serviceNames">The names of the services.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.ToProvider``2">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`3.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingBuilder`2">
            <summary>
            Provides a root for the fluent syntax associated with an <see cref="P:Ninject.Planning.Bindings.BindingBuilder.BindingConfiguration"/>.
            </summary>
            <typeparam name="T1">The first service type.</typeparam>
            <typeparam name="T2">The second service type.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingToSyntax`2">
            <summary>
            Used to define the target of a binding.
            </summary>
            <typeparam name="T1">The first service type to be bound.</typeparam>
            <typeparam name="T2">The second service type to be bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.ToProvider``2">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`2.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.#ctor(Ninject.Planning.Bindings.IBindingConfiguration,Ninject.IKernel,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.BindingBuilder`2"/> class.
            </summary>
            <param name="bindingConfigurationConfiguration">The binding to build.</param>
            <param name="kernel">The kernel.</param>
            <param name="serviceNames">The names of the services.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.ToProvider``2">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`2.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingBuilder`1">
            <summary>
            Provides a root for the fluent syntax associated with an <see cref="P:Ninject.Planning.Bindings.BindingBuilder`1.Binding"/>.
            </summary>
            <typeparam name="T1">The service type.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingToSyntax`1">
            <summary>
            Used to define the target of a binding.
            </summary>
            <typeparam name="T1">The service being bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToSelf">
            <summary>
            Indicates that the service should be self-bound.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToMethod(System.Func{Ninject.Activation.IContext,`0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingToSyntax`1.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the specified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.#ctor(Ninject.Planning.Bindings.IBinding,Ninject.IKernel,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.BindingBuilder`1"/> class.
            </summary>
            <param name="binding">The binding to build.</param>
            <param name="kernel">The kernel.</param>
            <param name="serviceNames">The names of the services.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToSelf">
            <summary>
            Indicates that the service should be self-bound.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.To``1">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <typeparam name="TImplementation">The implementation type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.To(System.Type)">
            <summary>
            Indicates that the service should be bound to the specified implementation type.
            </summary>
            <param name="implementation">The implementation type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToConstructor``1(System.Linq.Expressions.Expression{System.Func{Ninject.Syntax.IConstructorArgumentSyntax,``0}})">
            <summary>
            Indicates that the service should be bound to the speecified constructor.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="newExpression">The expression that specifies the constructor.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToProvider``1">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <typeparam name="TProvider">The type of provider to activate.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToProvider(System.Type)">
            <summary>
            Indicates that the service should be bound to an instance of the specified provider type.
            The instance will be activated via the kernel when an instance of the service is activated.
            </summary>
            <param name="providerType">The type of provider to activate.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToProvider``1(Ninject.Activation.IProvider{``0})">
            <summary>
            Indicates that the service should be bound to the specified provider.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="provider">The provider.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToMethod(System.Func{Ninject.Activation.IContext,`0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToMethod``1(System.Func{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the service should be bound to the specified callback method.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="method">The method.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingBuilder`1.ToConstant``1(``0)">
            <summary>
            Indicates that the service should be bound to the specified constant value.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="value">The constant value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingBuilder`1.Binding">
            <summary>
            Gets the binding being built.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingConfiguration">
            <summary>
            The configuration of a binding.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfiguration.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Bindings.BindingConfiguration"/> class.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfiguration.GetProvider(Ninject.Activation.IContext)">
            <summary>
            Gets the provider for the binding.
            </summary>
            <param name="context">The context.</param>
            <returns>The provider to use.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfiguration.GetScope(Ninject.Activation.IContext)">
            <summary>
            Gets the scope for the binding, if any.
            </summary>
            <param name="context">The context.</param>
            <returns>The object that will act as the scope, or <see langword="null"/> if the service is transient.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfiguration.Matches(Ninject.Activation.IRequest)">
            <summary>
            Determines whether the specified request satisfies the conditions defined on this binding.
            </summary>
            <param name="request">The request.</param>
            <returns><c>True</c> if the request satisfies the conditions; otherwise <c>false</c>.</returns>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.Metadata">
            <summary>
            Gets the binding's metadata.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.IsImplicit">
            <summary>
            Gets or sets a value indicating whether the binding was implicitly registered.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.IsConditional">
            <summary>
            Gets a value indicating whether the binding has a condition associated with it.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.Target">
            <summary>
            Gets or sets the type of target for the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.Condition">
            <summary>
            Gets or sets the condition defined for the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.ProviderCallback">
            <summary>
            Gets or sets the callback that returns the provider that should be used by the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.ScopeCallback">
            <summary>
            Gets or sets the callback that returns the object that will act as the binding's scope.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.Parameters">
            <summary>
            Gets the parameters defined for the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.ActivationActions">
            <summary>
            Gets the actions that should be called after instances are activated via the binding.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfiguration.DeactivationActions">
            <summary>
            Gets the actions that should be called before instances are deactivated via the binding.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingConfigurationBuilder`1">
            <summary>
            Provides a root for the fluent syntax associated with an <see cref="P:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.BindingConfiguration"/>.
            </summary>
            <typeparam name="T">The implementation type of the built binding.</typeparam>
        </member>
        <member name="T:Ninject.Planning.Bindings.IBindingConfigurationSyntax`1">
            <summary>
            The syntax to define bindings.
            </summary>
            <typeparam name="T">The type of the service.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingWhenInNamedWithOrOnSyntax`1">
            <summary>
            Used to set the condition, scope, name, or add additional information or actions to a binding.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingWhenSyntax`1">
            <summary>
            Used to define the conditions under which a binding should be used.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.When(System.Func{Ninject.Activation.IRequest,System.Boolean})">
            <summary>
            Indicates that the binding should be used only for requests that support the specified condition.
            </summary>
            <param name="condition">The condition.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenInjectedInto``1">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            Types that derive from the specified type are considered as valid targets.
            </summary>
            <typeparam name="TParent">The type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenInjectedInto(System.Type)">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            Types that derive from the specified type are considered as valid targets.
            </summary>
            <param name="parent">The type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenInjectedInto(System.Type[])">
            <summary>
            Indicates that the binding should be used only for injections on the specified types.
            Types that derive from one of the specified types are considered as valid targets.
            Should match at lease one of the targets.
            </summary>
            <param name="parents">The types to match.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenInjectedExactlyInto``1">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            The type must match exactly the specified type. Types that derive from the specified type
            will not be considered as valid target.  
            </summary>
            <typeparam name="TParent">The type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenInjectedExactlyInto(System.Type)">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            The type must match exactly the specified type. Types that derive from the specified type
            will not be considered as valid target.  
            </summary>
            <param name="parent">The type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenInjectedExactlyInto(System.Type[])">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            The type must match one of the specified types exactly. Types that derive from one of the specified types
            will not be considered as valid target.  
            Should match at least one of the specified targets
            </summary>
            <param name="parents">The types.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenClassHas``1">
            <summary>
            Indicates that the binding should be used only when the class being injected has
            an attribute of the specified type.
            </summary>
            <typeparam name="TAttribute">The type of attribute.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenMemberHas``1">
            <summary>
            Indicates that the binding should be used only when the member being injected has
            an attribute of the specified type.
            </summary>
            <typeparam name="TAttribute">The type of attribute.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenTargetHas``1">
            <summary>
            Indicates that the binding should be used only when the target being injected has
            an attribute of the specified type.
            </summary>
            <typeparam name="TAttribute">The type of attribute.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenClassHas(System.Type)">
            <summary>
            Indicates that the binding should be used only when the class being injected has
            an attribute of the specified type.
            </summary>
            <param name="attributeType">The type of attribute.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenMemberHas(System.Type)">
            <summary>
            Indicates that the binding should be used only when the member being injected has
            an attribute of the specified type.
            </summary>
            <param name="attributeType">The type of attribute.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenTargetHas(System.Type)">
            <summary>
            Indicates that the binding should be used only when the target being injected has
            an attribute of the specified type.
            </summary>
            <param name="attributeType">The type of attribute.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenParentNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when the service is being requested
            by a service bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenAnyAnchestorNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when any ancestor is bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenAnyAncestorNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when any ancestor is bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenNoAncestorNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when no ancestor is bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenAnyAncestorMatches(System.Predicate{Ninject.Activation.IContext})">
            <summary>
            Indicates that the binding should be used only when any ancestor matches the specified predicate.
            </summary>
            <param name="predicate">The predicate to match.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWhenSyntax`1.WhenNoAncestorMatches(System.Predicate{Ninject.Activation.IContext})">
            <summary>
            Indicates that the binding should be used only when no ancestor matches the specified predicate.
            </summary>
            <param name="predicate">The predicate to match.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Syntax.IBindingInSyntax`1">
            <summary>
            Used to define the scope in which instances activated via a binding should be re-used.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingInSyntax`1.InSingletonScope">
            <summary>
            Indicates that only a single instance of the binding should be created, and then
            should be re-used for all subsequent requests.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingInSyntax`1.InTransientScope">
            <summary>
            Indicates that instances activated via the binding should not be re-used, nor have
            their lifecycle managed by Ninject.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingInSyntax`1.InThreadScope">
            <summary>
            Indicates that instances activated via the binding should be re-used within the same thread.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingInSyntax`1.InScope(System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that instances activated via the binding should be re-used as long as the object
            returned by the provided callback remains alive (that is, has not been garbage collected).
            </summary>
            <param name="scope">The callback that returns the scope.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Syntax.IBindingNamedSyntax`1">
            <summary>
            Used to define the name of a binding.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingNamedSyntax`1.Named(System.String)">
            <summary>
            Indicates that the binding should be registered with the specified name. Names are not
            necessarily unique; multiple bindings for a given service may be registered with the same name.
            </summary>
            <param name="name">The name to give the binding.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Syntax.IBindingWithSyntax`1">
            <summary>
            Used to add additional information to a binding.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithConstructorArgument(System.String,System.Object)">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="value">The value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithConstructorArgument(System.String,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithConstructorArgument(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithConstructorArgument``1(``0)">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <typeparam name="TValue">Specifies the argument type to override.</typeparam>
            <param name="value">The value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithConstructorArgument(System.Type,System.Object)">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="value">The value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithConstructorArgument(System.Type,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithConstructorArgument(System.Type,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>    
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithPropertyValue(System.String,System.Object)">
            <summary>
            Indicates that the specified property should be injected with the specified value.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="value">The value for the property.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithPropertyValue(System.String,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that the specified property should be injected with the specified value.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="callback">The callback to invoke to get the value for the property.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithPropertyValue(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Indicates that the specified property should be injected with the specified value.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="callback">The callback to invoke to get the value for the property.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithParameter(Ninject.Parameters.IParameter)">
            <summary>
            Adds a custom parameter to the binding.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingWithSyntax`1.WithMetadata(System.String,System.Object)">
            <summary>
            Sets the value of a piece of metadata on the binding.
            </summary>
            <param name="key">The metadata key.</param>
            <param name="value">The metadata value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Syntax.IBindingOnSyntax`1">
            <summary>
            Used to add additional actions to be performed during activation or deactivation of instances via a binding.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnActivation(System.Action{`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnActivation``1(System.Action{``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnActivation(System.Action{Ninject.Activation.IContext,`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnActivation``1(System.Action{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnDeactivation(System.Action{`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnDeactivation``1(System.Action{``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnDeactivation(System.Action{Ninject.Activation.IContext,`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Syntax.IBindingOnSyntax`1.OnDeactivation``1(System.Action{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="T:Ninject.Syntax.IBindingInNamedWithOrOnSyntax`1">
            <summary>
            Used to set the scope, name, or add additional information or actions to a binding.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingNamedWithOrOnSyntax`1">
            <summary>
            Used to set the name, or add additional information or actions to a binding.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="T:Ninject.Syntax.IBindingWithOrOnSyntax`1">
            <summary>
            Used to add additional information or actions to a binding.
            </summary>
            <typeparam name="T">The service being bound.</typeparam>
        </member>
        <member name="F:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.serviceNames">
            <summary>
            The names of the services added to the exceptions.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.#ctor(Ninject.Planning.Bindings.IBindingConfiguration,System.String,Ninject.IKernel)">
            <summary>
            Initializes a new instance of the BindingBuilder&lt;T&gt; class.
            </summary>
            <param name="bindingConfiguration">The binding configuration to build.</param>
            <param name="serviceNames">The names of the configured services.</param>
            <param name="kernel">The kernel.</param>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.When(System.Func{Ninject.Activation.IRequest,System.Boolean})">
            <summary>
            Indicates that the binding should be used only for requests that support the specified condition.
            </summary>
            <param name="condition">The condition.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenInjectedInto``1">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            Types that derive from the specified type are considered as valid targets.
            </summary>
            <typeparam name="TParent">The type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenInjectedInto(System.Type)">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            Types that derive from the specified type are considered as valid targets.
            </summary>
            <param name="parent">The type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenInjectedInto(System.Type[])">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            Types that derive from the specified type are considered as valid targets.
            </summary>
            <param name="parents">The type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenInjectedExactlyInto``1">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            The type must match exactly the specified type. Types that derive from the specified type
            will not be considered as valid target.  
            </summary>
            <typeparam name="TParent">The type.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenInjectedExactlyInto(System.Type)">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            The type must match exactly the specified type. Types that derive from the specified type
            will not be considered as valid target.  
            </summary>
            <param name="parent">The type.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenInjectedExactlyInto(System.Type[])">
            <summary>
            Indicates that the binding should be used only for injections on the specified type.
            The type must match exactly the specified type. Types that derive from the specified type
            will not be considered as valid target.  
            Should match at least one of the specified targets
            </summary>
            <param name="parents">The types.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenClassHas``1">
            <summary>
            Indicates that the binding should be used only when the class being injected has
            an attribute of the specified type.
            </summary>
            <typeparam name="TAttribute">The type of attribute.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenMemberHas``1">
            <summary>
            Indicates that the binding should be used only when the member being injected has
            an attribute of the specified type.
            </summary>
            <typeparam name="TAttribute">The type of attribute.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenTargetHas``1">
            <summary>
            Indicates that the binding should be used only when the target being injected has
            an attribute of the specified type.
            </summary>
            <typeparam name="TAttribute">The type of attribute.</typeparam>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenClassHas(System.Type)">
            <summary>
            Indicates that the binding should be used only when the class being injected has
            an attribute of the specified type.
            </summary>
            <param name="attributeType">The type of attribute.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenMemberHas(System.Type)">
            <summary>
            Indicates that the binding should be used only when the member being injected has
            an attribute of the specified type.
            </summary>
            <param name="attributeType">The type of attribute.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenTargetHas(System.Type)">
            <summary>
            Indicates that the binding should be used only when the target being injected has
            an attribute of the specified type.
            </summary>
            <param name="attributeType">The type of attribute.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenParentNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when the service is being requested
            by a service bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenAnyAnchestorNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when any ancestor is bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenAnyAncestorNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when any ancestor is bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenNoAncestorNamed(System.String)">
            <summary>
            Indicates that the binding should be used only when no ancestor is bound with the specified name.
            </summary>
            <param name="name">The name to expect.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenAnyAncestorMatches(System.Predicate{Ninject.Activation.IContext})">
            <summary>
            Indicates that the binding should be used only when any ancestor matches the specified predicate.
            </summary>
            <param name="predicate">The predicate to match.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WhenNoAncestorMatches(System.Predicate{Ninject.Activation.IContext})">
            <summary>
            Indicates that the binding should be used only when no ancestor matches the specified predicate.
            </summary>
            <param name="predicate">The predicate to match.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.Named(System.String)">
            <summary>
            Indicates that the binding should be registered with the specified name. Names are not
            necessarily unique; multiple bindings for a given service may be registered with the same name.
            </summary>
            <param name="name">The name to give the binding.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.InSingletonScope">
            <summary>
            Indicates that only a single instance of the binding should be created, and then
            should be re-used for all subsequent requests.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.InTransientScope">
            <summary>
            Indicates that instances activated via the binding should not be re-used, nor have
            their lifecycle managed by Ninject.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.InThreadScope">
            <summary>
            Indicates that instances activated via the binding should be re-used within the same thread.
            </summary>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.InScope(System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that instances activated via the binding should be re-used as long as the object
            returned by the provided callback remains alive (that is, has not been garbage collected).
            </summary>
            <param name="scope">The callback that returns the scope.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithConstructorArgument(System.String,System.Object)">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="value">The value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithConstructorArgument(System.String,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithConstructorArgument(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="name">The name of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithConstructorArgument``1(``0)">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <typeparam name="TValue">Specifies the argument type to override.</typeparam>
            <param name="value">The value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithConstructorArgument(System.Type,System.Object)">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="value">The value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithConstructorArgument(System.Type,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithConstructorArgument(System.Type,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Indicates that the specified constructor argument should be overridden with the specified value.
            </summary>
            <param name="type">The type of the argument to override.</param>
            <param name="callback">The callback to invoke to get the value for the argument.</param>    
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithPropertyValue(System.String,System.Object)">
            <summary>
            Indicates that the specified property should be injected with the specified value.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="value">The value for the property.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithPropertyValue(System.String,System.Func{Ninject.Activation.IContext,System.Object})">
            <summary>
            Indicates that the specified property should be injected with the specified value.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="callback">The callback to invoke to get the value for the property.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithPropertyValue(System.String,System.Func{Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget,System.Object})">
            <summary>
            Indicates that the specified property should be injected with the specified value.
            </summary>
            <param name="name">The name of the property to override.</param>
            <param name="callback">The callback to invoke to get the value for the property.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithParameter(Ninject.Parameters.IParameter)">
            <summary>
            Adds a custom parameter to the binding.
            </summary>
            <param name="parameter">The parameter.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.WithMetadata(System.String,System.Object)">
            <summary>
            Sets the value of a piece of metadata on the binding.
            </summary>
            <param name="key">The metadata key.</param>
            <param name="value">The metadata value.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnActivation(System.Action{`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnActivation``1(System.Action{``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnActivation(System.Action{Ninject.Activation.IContext,`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnActivation``1(System.Action{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are activated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnDeactivation(System.Action{`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnDeactivation``1(System.Action{``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnDeactivation(System.Action{Ninject.Activation.IContext,`0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.OnDeactivation``1(System.Action{Ninject.Activation.IContext,``0})">
            <summary>
            Indicates that the specified callback should be invoked when instances are deactivated.
            </summary>
            <typeparam name="TImplementation">The type of the implementation.</typeparam>
            <param name="action">The action callback.</param>
            <returns>The fluent syntax.</returns>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.BindingConfiguration">
            <summary>
            Gets the binding being built.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingConfigurationBuilder`1.Kernel">
            <summary>
            Gets the kernel.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingMetadata">
            <summary>
            Additional information available about a binding, which can be used in constraints
            to select bindings to use in activation.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.IBindingMetadata">
            <summary>
            Additional information available about a binding, which can be used in constraints
            to select bindings to use in activation.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.IBindingMetadata.Has(System.String)">
            <summary>
            Determines whether a piece of metadata with the specified key has been defined.
            </summary>
            <param name="key">The metadata key.</param>
            <returns><c>True</c> if such a piece of metadata exists; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.IBindingMetadata.Get``1(System.String)">
            <summary>
            Gets the value of metadata defined with the specified key, cast to the specified type.
            </summary>
            <typeparam name="T">The type of value to expect.</typeparam>
            <param name="key">The metadata key.</param>
            <returns>The metadata value.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.IBindingMetadata.Get``1(System.String,``0)">
            <summary>
            Gets the value of metadata defined with the specified key.
            </summary>
            <param name="key">The metadata key.</param>
            <param name="defaultValue">The value to return if the binding has no metadata set with the specified key.</param>
            <returns>The metadata value, or the default value if none was set.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.IBindingMetadata.Set(System.String,System.Object)">
            <summary>
            Sets the value of a piece of metadata.
            </summary>
            <param name="key">The metadata key.</param>
            <param name="value">The metadata value.</param>
        </member>
        <member name="P:Ninject.Planning.Bindings.IBindingMetadata.Name">
            <summary>
            Gets or sets the binding's name.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingMetadata.Has(System.String)">
            <summary>
            Determines whether a piece of metadata with the specified key has been defined.
            </summary>
            <param name="key">The metadata key.</param>
            <returns><c>True</c> if such a piece of metadata exists; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingMetadata.Get``1(System.String)">
            <summary>
            Gets the value of metadata defined with the specified key, cast to the specified type.
            </summary>
            <typeparam name="T">The type of value to expect.</typeparam>
            <param name="key">The metadata key.</param>
            <returns>The metadata value.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingMetadata.Get``1(System.String,``0)">
            <summary>
            Gets the value of metadata defined with the specified key.
            </summary>
            <param name="key">The metadata key.</param>
            <param name="defaultValue">The value to return if the binding has no metadata set with the specified key.</param>
            <returns>The metadata value, or the default value if none was set.</returns>
        </member>
        <member name="M:Ninject.Planning.Bindings.BindingMetadata.Set(System.String,System.Object)">
            <summary>
            Sets the value of a piece of metadata.
            </summary>
            <param name="key">The metadata key.</param>
            <param name="value">The metadata value.</param>
        </member>
        <member name="P:Ninject.Planning.Bindings.BindingMetadata.Name">
            <summary>
            Gets or sets the binding's name.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Bindings.BindingTarget">
            <summary>
            Describes the target of a binding.
            </summary>
        </member>
        <member name="F:Ninject.Planning.Bindings.BindingTarget.Self">
            <summary>
            Indicates that the binding is from a type to itself.
            </summary>
        </member>
        <member name="F:Ninject.Planning.Bindings.BindingTarget.Type">
            <summary>
            Indicates that the binding is from one type to another.
            </summary>
        </member>
        <member name="F:Ninject.Planning.Bindings.BindingTarget.Provider">
            <summary>
            Indicates that the binding is from a type to a provider.
            </summary>
        </member>
        <member name="F:Ninject.Planning.Bindings.BindingTarget.Method">
            <summary>
            Indicates that the binding is from a type to a callback method.
            </summary>
        </member>
        <member name="F:Ninject.Planning.Bindings.BindingTarget.Constant">
            <summary>
            Indicates that the binding is from a type to a constant value.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Directives.ConstructorInjectionDirective">
            <summary>
            Describes the injection of a constructor.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Directives.MethodInjectionDirectiveBase`2">
            <summary>
            Describes the injection of a method or constructor.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Directives.IDirective">
            <summary>
            A piece of information used in an <see cref="T:Ninject.Planning.IPlan"/>. (Just a marker.)
            </summary>
        </member>
        <member name="M:Ninject.Planning.Directives.MethodInjectionDirectiveBase`2.#ctor(`0,`1)">
            <summary>
            Initializes a new instance of the MethodInjectionDirectiveBase&lt;TMethod, TInjector&gt; class.
            </summary>
            <param name="method">The method this directive represents.</param>
            <param name="injector">The injector that will be triggered.</param>
        </member>
        <member name="M:Ninject.Planning.Directives.MethodInjectionDirectiveBase`2.CreateTargetsFromParameters(`0)">
            <summary>
            Creates targets for the parameters of the method.
            </summary>
            <param name="method">The method.</param>
            <returns>The targets for the method's parameters.</returns>
        </member>
        <member name="P:Ninject.Planning.Directives.MethodInjectionDirectiveBase`2.Injector">
            <summary>
            Gets or sets the injector that will be triggered.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Directives.MethodInjectionDirectiveBase`2.Targets">
            <summary>
            Gets or sets the targets for the directive.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Directives.ConstructorInjectionDirective.#ctor(System.Reflection.ConstructorInfo,Ninject.Injection.ConstructorInjector)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Directives.ConstructorInjectionDirective"/> class.
            </summary>
            <param name="constructor">The constructor described by the directive.</param>
            <param name="injector">The injector that will be triggered.</param>
        </member>
        <member name="P:Ninject.Planning.Directives.ConstructorInjectionDirective.Constructor">
            <summary>
            The base .ctor definition.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Directives.MethodInjectionDirective">
            <summary>
            Describes the injection of a method.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Directives.MethodInjectionDirective.#ctor(System.Reflection.MethodInfo,Ninject.Injection.MethodInjector)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Directives.MethodInjectionDirective"/> class.
            </summary>
            <param name="method">The method described by the directive.</param>
            <param name="injector">The injector that will be triggered.</param>
        </member>
        <member name="T:Ninject.Planning.Directives.PropertyInjectionDirective">
            <summary>
            Describes the injection of a property.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Directives.PropertyInjectionDirective.#ctor(System.Reflection.PropertyInfo,Ninject.Injection.PropertyInjector)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Directives.PropertyInjectionDirective"/> class.
            </summary>
            <param name="member">The member the directive describes.</param>
            <param name="injector">The injector that will be triggered.</param>
        </member>
        <member name="M:Ninject.Planning.Directives.PropertyInjectionDirective.CreateTarget(System.Reflection.PropertyInfo)">
            <summary>
            Creates a target for the property.
            </summary>
            <param name="propertyInfo">The property.</param>
            <returns>The target for the property.</returns>
        </member>
        <member name="P:Ninject.Planning.Directives.PropertyInjectionDirective.Injector">
            <summary>
            Gets or sets the injector that will be triggered.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Directives.PropertyInjectionDirective.Target">
            <summary>
            Gets or sets the injection target for the directive.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Strategies.ConstructorReflectionStrategy">
            <summary>
            Adds a directive to plans indicating which constructor should be injected during activation.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Strategies.IPlanningStrategy">
            <summary>
            Contributes to the generation of a <see cref="T:Ninject.Planning.IPlan"/>.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Strategies.IPlanningStrategy.Execute(Ninject.Planning.IPlan)">
            <summary>
            Contributes to the specified plan.
            </summary>
            <param name="plan">The plan that is being generated.</param>
        </member>
        <member name="M:Ninject.Planning.Strategies.ConstructorReflectionStrategy.#ctor(Ninject.Selection.ISelector,Ninject.Injection.IInjectorFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Strategies.ConstructorReflectionStrategy"/> class.
            </summary>
            <param name="selector">The selector component.</param>
            <param name="injectorFactory">The injector factory component.</param>
        </member>
        <member name="M:Ninject.Planning.Strategies.ConstructorReflectionStrategy.Execute(Ninject.Planning.IPlan)">
            <summary>
            Adds a <see cref="T:Ninject.Planning.Directives.ConstructorInjectionDirective"/> to the plan for the constructor
            that should be injected.
            </summary>
            <param name="plan">The plan that is being generated.</param>
        </member>
        <member name="P:Ninject.Planning.Strategies.ConstructorReflectionStrategy.Selector">
            <summary>
            Gets the selector component.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Strategies.ConstructorReflectionStrategy.InjectorFactory">
            <summary>
            Gets the injector factory component.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Strategies.MethodReflectionStrategy">
            <summary>
            Adds directives to plans indicating which methods should be injected during activation.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Strategies.MethodReflectionStrategy.#ctor(Ninject.Selection.ISelector,Ninject.Injection.IInjectorFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Strategies.MethodReflectionStrategy"/> class.
            </summary>
            <param name="selector">The selector component.</param>
            <param name="injectorFactory">The injector factory component.</param>
        </member>
        <member name="M:Ninject.Planning.Strategies.MethodReflectionStrategy.Execute(Ninject.Planning.IPlan)">
            <summary>
            Adds a <see cref="T:Ninject.Planning.Directives.MethodInjectionDirective"/> to the plan for each method
            that should be injected.
            </summary>
            <param name="plan">The plan that is being generated.</param>
        </member>
        <member name="P:Ninject.Planning.Strategies.MethodReflectionStrategy.Selector">
            <summary>
            Gets the selector component.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Strategies.MethodReflectionStrategy.InjectorFactory">
            <summary>
            Gets the injector factory component.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Strategies.PropertyReflectionStrategy">
            <summary>
            Adds directives to plans indicating which properties should be injected during activation.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Strategies.PropertyReflectionStrategy.#ctor(Ninject.Selection.ISelector,Ninject.Injection.IInjectorFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Strategies.PropertyReflectionStrategy"/> class.
            </summary>
            <param name="selector">The selector component.</param>
            <param name="injectorFactory">The injector factory component.</param>
        </member>
        <member name="M:Ninject.Planning.Strategies.PropertyReflectionStrategy.Execute(Ninject.Planning.IPlan)">
            <summary>
            Adds a <see cref="T:Ninject.Planning.Directives.PropertyInjectionDirective"/> to the plan for each property
            that should be injected.
            </summary>
            <param name="plan">The plan that is being generated.</param>
        </member>
        <member name="P:Ninject.Planning.Strategies.PropertyReflectionStrategy.Selector">
            <summary>
            Gets the selector component.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Strategies.PropertyReflectionStrategy.InjectorFactory">
            <summary>
            Gets the injector factory component.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Targets.ITarget">
            <summary>
            Represents a site on a type where a value will be injected.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Targets.ITarget.ResolveWithin(Ninject.Activation.IContext)">
            <summary>
            Resolves a value for the target within the specified parent context.
            </summary>
            <param name="parent">The parent context.</param>
            <returns>The resolved value.</returns>
        </member>
        <member name="P:Ninject.Planning.Targets.ITarget.Type">
            <summary>
            Gets the type of the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ITarget.Name">
            <summary>
            Gets the name of the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ITarget.Member">
            <summary>
            Gets the member that contains the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ITarget.Constraint">
            <summary>
            Gets the constraint defined on the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ITarget.IsOptional">
            <summary>
            Gets a value indicating whether the target represents an optional dependency.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ITarget.HasDefaultValue">
            <summary>
            Gets a value indicating whether the target has a default value.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ITarget.DefaultValue">
            <summary>
            Gets the default value for the target.
            </summary>
            <exception cref="T:System.InvalidOperationException">If the item does not have a default value.</exception>
        </member>
        <member name="T:Ninject.Planning.Targets.ParameterTarget">
            <summary>
            Represents an injection target for a <see cref="T:System.Reflection.ParameterInfo"/>.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Targets.Target`1">
            <summary>
            Represents a site on a type where a value can be injected.
            </summary>
            <typeparam name="T">The type of site this represents.</typeparam>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.#ctor(System.Reflection.MemberInfo,`0)">
            <summary>
            Initializes a new instance of the Target&lt;T&gt; class.
            </summary>
            <param name="member">The member that contains the target.</param>
            <param name="site">The site represented by the target.</param>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.GetCustomAttributes(System.Type,System.Boolean)">
            <summary>
            Returns an array of custom attributes of a specified type defined on the target.
            </summary>
            <param name="attributeType">The type of attribute to search for.</param>
            <param name="inherit">Whether to look up the hierarchy chain for inherited custom attributes.</param>
            <returns>An array of custom attributes of the specified type.</returns>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.GetCustomAttributes(System.Boolean)">
            <summary>
            Returns an array of custom attributes defined on the target.
            </summary>
            <param name="inherit">Whether to look up the hierarchy chain for inherited custom attributes.</param>
            <returns>An array of custom attributes.</returns>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.IsDefined(System.Type,System.Boolean)">
            <summary>
            Returns a value indicating whether an attribute of the specified type is defined on the target.
            </summary>
            <param name="attributeType">The type of attribute to search for.</param>
            <param name="inherit">Whether to look up the hierarchy chain for inherited custom attributes.</param>
            <returns><c>True</c> if such an attribute is defined; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.ResolveWithin(Ninject.Activation.IContext)">
            <summary>
            Resolves a value for the target within the specified parent context.
            </summary>
            <param name="parent">The parent context.</param>
            <returns>The resolved value.</returns>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.GetValues(System.Type,Ninject.Activation.IContext)">
            <summary>
            Gets the value(s) that should be injected into the target.
            </summary>
            <param name="service">The service that the target is requesting.</param>
            <param name="parent">The parent context in which the target is being injected.</param>
            <returns>A series of values that are available for injection.</returns>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.GetValue(System.Type,Ninject.Activation.IContext)">
            <summary>
            Gets the value that should be injected into the target.
            </summary>
            <param name="service">The service that the target is requesting.</param>
            <param name="parent">The parent context in which the target is being injected.</param>
            <returns>The value that is to be injected.</returns>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.ReadOptionalFromTarget">
            <summary>
            Reads whether the target represents an optional dependency.
            </summary>
            <returns><see langword="True"/> if it is optional; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Ninject.Planning.Targets.Target`1.ReadConstraintFromTarget">
            <summary>
            Reads the resolution constraint from target.
            </summary>
            <returns>The resolution constraint.</returns>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.Member">
            <summary>
            Gets the member that contains the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.Site">
            <summary>
            Gets or sets the site (property, parameter, etc.) represented by the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.Name">
            <summary>
            Gets the name of the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.Type">
            <summary>
            Gets the type of the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.Constraint">
            <summary>
            Gets the constraint defined on the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.IsOptional">
            <summary>
            Gets a value indicating whether the target represents an optional dependency.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.HasDefaultValue">
            <summary>
            Gets a value indicating whether the target has a default value.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.Target`1.DefaultValue">
            <summary>
            Gets the default value for the target.
            </summary>
            <exception cref="T:System.InvalidOperationException">If the item does not have a default value.</exception>
        </member>
        <member name="M:Ninject.Planning.Targets.ParameterTarget.#ctor(System.Reflection.MethodBase,System.Reflection.ParameterInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Targets.ParameterTarget"/> class.
            </summary>
            <param name="method">The method that defines the parameter.</param>
            <param name="site">The parameter that this target represents.</param>
        </member>
        <member name="P:Ninject.Planning.Targets.ParameterTarget.Name">
            <summary>
            Gets the name of the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ParameterTarget.Type">
            <summary>
            Gets the type of the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ParameterTarget.HasDefaultValue">
            <summary>
            Gets a value indicating whether the target has a default value.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.ParameterTarget.DefaultValue">
            <summary>
            Gets the default value for the target.
            </summary>
            <exception cref="T:System.InvalidOperationException">If the item does not have a default value.</exception>
        </member>
        <member name="T:Ninject.Planning.Targets.PropertyTarget">
            <summary>
            Represents an injection target for a <see cref="T:System.Reflection.PropertyInfo"/>.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Targets.PropertyTarget.#ctor(System.Reflection.PropertyInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Targets.PropertyTarget"/> class.
            </summary>
            <param name="site">The property that this target represents.</param>
        </member>
        <member name="P:Ninject.Planning.Targets.PropertyTarget.Name">
            <summary>
            Gets the name of the target.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Targets.PropertyTarget.Type">
            <summary>
            Gets the type of the target.
            </summary>
        </member>
        <member name="T:Ninject.Planning.IPlan">
            <summary>
            Describes the means by which a type should be activated.
            </summary>
        </member>
        <member name="M:Ninject.Planning.IPlan.Add(Ninject.Planning.Directives.IDirective)">
            <summary>
            Adds the specified directive to the plan.
            </summary>
            <param name="directive">The directive.</param>
        </member>
        <member name="M:Ninject.Planning.IPlan.Has``1">
            <summary>
            Determines whether the plan contains one or more directives of the specified type.
            </summary>
            <typeparam name="TDirective">The type of directive.</typeparam>
            <returns><c>True</c> if the plan has one or more directives of the type; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Planning.IPlan.GetOne``1">
            <summary>
            Gets the first directive of the specified type from the plan.
            </summary>
            <typeparam name="TDirective">The type of directive.</typeparam>
            <returns>The first directive, or <see langword="null"/> if no matching directives exist.</returns>
        </member>
        <member name="M:Ninject.Planning.IPlan.GetAll``1">
            <summary>
            Gets all directives of the specified type that exist in the plan.
            </summary>
            <typeparam name="TDirective">The type of directive.</typeparam>
            <returns>A series of directives of the specified type.</returns>
        </member>
        <member name="P:Ninject.Planning.IPlan.Type">
            <summary>
            Gets the type that the plan describes.
            </summary>
        </member>
        <member name="T:Ninject.Planning.IPlanner">
            <summary>
            Generates plans for how to activate instances.
            </summary>
        </member>
        <member name="M:Ninject.Planning.IPlanner.GetPlan(System.Type)">
            <summary>
            Gets or creates an activation plan for the specified type.
            </summary>
            <param name="type">The type for which a plan should be created.</param>
            <returns>The type's activation plan.</returns>
        </member>
        <member name="P:Ninject.Planning.IPlanner.Strategies">
            <summary>
            Gets the strategies that contribute to the planning process.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Plan">
            <summary>
            Describes the means by which a type should be activated.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Plan.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Plan"/> class.
            </summary>
            <param name="type">The type the plan describes.</param>
        </member>
        <member name="M:Ninject.Planning.Plan.Add(Ninject.Planning.Directives.IDirective)">
            <summary>
            Adds the specified directive to the plan.
            </summary>
            <param name="directive">The directive.</param>
        </member>
        <member name="M:Ninject.Planning.Plan.Has``1">
            <summary>
            Determines whether the plan contains one or more directives of the specified type.
            </summary>
            <typeparam name="TDirective">The type of directive.</typeparam>
            <returns><c>True</c> if the plan has one or more directives of the type; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.Planning.Plan.GetOne``1">
            <summary>
            Gets the first directive of the specified type from the plan.
            </summary>
            <typeparam name="TDirective">The type of directive.</typeparam>
            <returns>The first directive, or <see langword="null"/> if no matching directives exist.</returns>
        </member>
        <member name="M:Ninject.Planning.Plan.GetAll``1">
            <summary>
            Gets all directives of the specified type that exist in the plan.
            </summary>
            <typeparam name="TDirective">The type of directive.</typeparam>
            <returns>A series of directives of the specified type.</returns>
        </member>
        <member name="P:Ninject.Planning.Plan.Type">
            <summary>
            Gets the type that the plan describes.
            </summary>
        </member>
        <member name="P:Ninject.Planning.Plan.Directives">
            <summary>
            Gets the directives defined in the plan.
            </summary>
        </member>
        <member name="T:Ninject.Planning.Planner">
            <summary>
            Generates plans for how to activate instances.
            </summary>
        </member>
        <member name="M:Ninject.Planning.Planner.#ctor(System.Collections.Generic.IEnumerable{Ninject.Planning.Strategies.IPlanningStrategy})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Planning.Planner"/> class.
            </summary>
            <param name="strategies">The strategies to execute during planning.</param>
        </member>
        <member name="M:Ninject.Planning.Planner.GetPlan(System.Type)">
            <summary>
            Gets or creates an activation plan for the specified type.
            </summary>
            <param name="type">The type for which a plan should be created.</param>
            <returns>The type's activation plan.</returns>
        </member>
        <member name="M:Ninject.Planning.Planner.CreateEmptyPlan(System.Type)">
            <summary>
            Creates an empty plan for the specified type.
            </summary>
            <param name="type">The type for which a plan should be created.</param>
            <returns>The created plan.</returns>
        </member>
        <member name="M:Ninject.Planning.Planner.CreateNewPlan(System.Type)">
            <summary>
            Creates a new plan for the specified type.
            This method requires an active reader lock!
            </summary>
            <param name="type">The type.</param>
            <returns>The newly created plan.</returns>
        </member>
        <member name="P:Ninject.Planning.Planner.Strategies">
            <summary>
            Gets the strategies that contribute to the planning process.
            </summary>
        </member>
        <member name="T:Ninject.Selection.Heuristics.IConstructorScorer">
            <summary>
            Generates scores for constructors, to determine which is the best one to call during activation.
            </summary>
        </member>
        <member name="M:Ninject.Selection.Heuristics.IConstructorScorer.Score(Ninject.Activation.IContext,Ninject.Planning.Directives.ConstructorInjectionDirective)">
            <summary>
            Gets the score for the specified constructor.
            </summary>
            <param name="context">The injection context.</param>
            <param name="directive">The constructor.</param>
            <returns>The constructor's score.</returns>
        </member>
        <member name="T:Ninject.Selection.Heuristics.IInjectionHeuristic">
            <summary>
            Determines whether members should be injected during activation.
            </summary>
        </member>
        <member name="M:Ninject.Selection.Heuristics.IInjectionHeuristic.ShouldInject(System.Reflection.MemberInfo)">
            <summary>
            Returns a value indicating whether the specified member should be injected.
            </summary>
            <param name="member">The member in question.</param>
            <returns><c>True</c> if the member should be injected; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Ninject.Selection.Heuristics.SpecificConstructorSelector">
            <summary>
            Constructor selector that selects the constructor matching the one passed to the constructor.
            </summary>
        </member>
        <member name="M:Ninject.Selection.Heuristics.SpecificConstructorSelector.#ctor(System.Reflection.ConstructorInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Selection.Heuristics.SpecificConstructorSelector"/> class.
            </summary>
            <param name="constructorInfo">The constructor info of the constructor that shall be selected.</param>
        </member>
        <member name="M:Ninject.Selection.Heuristics.SpecificConstructorSelector.Score(Ninject.Activation.IContext,Ninject.Planning.Directives.ConstructorInjectionDirective)">
            <summary>
            Gets the score for the specified constructor.
            </summary>
            <param name="context">The injection context.</param>
            <param name="directive">The constructor.</param>
            <returns>The constructor's score.</returns>
        </member>
        <member name="T:Ninject.Selection.Heuristics.StandardConstructorScorer">
            <summary>
            Scores constructors by either looking for the existence of an injection marker
            attribute, or by counting the number of parameters.
            </summary>
        </member>
        <member name="M:Ninject.Selection.Heuristics.StandardConstructorScorer.Score(Ninject.Activation.IContext,Ninject.Planning.Directives.ConstructorInjectionDirective)">
            <summary>
            Gets the score for the specified constructor.
            </summary>
            <param name="context">The injection context.</param>
            <param name="directive">The constructor.</param>
            <returns>The constructor's score.</returns>
        </member>
        <member name="M:Ninject.Selection.Heuristics.StandardConstructorScorer.BindingExists(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Checkes whether a binding exists for a given target.
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>Whether a binding exists for the target in the given context.</returns>
        </member>
        <member name="M:Ninject.Selection.Heuristics.StandardConstructorScorer.BindingExists(Ninject.IKernel,Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Checkes whether a binding exists for a given target on the specified kernel.
            </summary>
            <param name="kernel">The kernel.</param>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>Whether a binding exists for the target in the given context.</returns>
        </member>
        <member name="M:Ninject.Selection.Heuristics.StandardConstructorScorer.ParameterExists(Ninject.Activation.IContext,Ninject.Planning.Targets.ITarget)">
            <summary>
            Checks whether any parameters exist for the geiven target..
            </summary>
            <param name="context">The context.</param>
            <param name="target">The target.</param>
            <returns>Whether a parameter exists for the target in the given context.</returns>
        </member>
        <member name="T:Ninject.Selection.Heuristics.StandardInjectionHeuristic">
            <summary>
            Determines whether members should be injected during activation by checking
            if they are decorated with an injection marker attribute.
            </summary>
        </member>
        <member name="M:Ninject.Selection.Heuristics.StandardInjectionHeuristic.ShouldInject(System.Reflection.MemberInfo)">
            <summary>
            Returns a value indicating whether the specified member should be injected.
            </summary>
            <param name="member">The member in question.</param>
            <returns><c>True</c> if the member should be injected; otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Ninject.Selection.ISelector">
            <summary>
            Selects members for injection.
            </summary>
        </member>
        <member name="M:Ninject.Selection.ISelector.SelectConstructorsForInjection(System.Type)">
            <summary>
            Selects the constructor to call on the specified type, by using the constructor scorer.
            </summary>
            <param name="type">The type.</param>
            <returns>The selected constructor, or <see langword="null"/> if none were available.</returns>
        </member>
        <member name="M:Ninject.Selection.ISelector.SelectPropertiesForInjection(System.Type)">
            <summary>
            Selects properties that should be injected.
            </summary>
            <param name="type">The type.</param>
            <returns>A series of the selected properties.</returns>
        </member>
        <member name="M:Ninject.Selection.ISelector.SelectMethodsForInjection(System.Type)">
            <summary>
            Selects methods that should be injected.
            </summary>
            <param name="type">The type.</param>
            <returns>A series of the selected methods.</returns>
        </member>
        <member name="P:Ninject.Selection.ISelector.ConstructorScorer">
            <summary>
            Gets or sets the constructor scorer.
            </summary>
        </member>
        <member name="P:Ninject.Selection.ISelector.InjectionHeuristics">
            <summary>
            Gets the heuristics used to determine which members should be injected.
            </summary>
        </member>
        <member name="T:Ninject.Selection.Selector">
            <summary>
            Selects members for injection.
            </summary>
        </member>
        <member name="M:Ninject.Selection.Selector.#ctor(Ninject.Selection.Heuristics.IConstructorScorer,System.Collections.Generic.IEnumerable{Ninject.Selection.Heuristics.IInjectionHeuristic})">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.Selection.Selector"/> class.
            </summary>
            <param name="constructorScorer">The constructor scorer.</param>
            <param name="injectionHeuristics">The injection heuristics.</param>
        </member>
        <member name="M:Ninject.Selection.Selector.SelectConstructorsForInjection(System.Type)">
            <summary>
            Selects the constructor to call on the specified type, by using the constructor scorer.
            </summary>
            <param name="type">The type.</param>
            <returns>The selected constructor, or <see langword="null"/> if none were available.</returns>
        </member>
        <member name="M:Ninject.Selection.Selector.SelectPropertiesForInjection(System.Type)">
            <summary>
            Selects properties that should be injected.
            </summary>
            <param name="type">The type.</param>
            <returns>A series of the selected properties.</returns>
        </member>
        <member name="M:Ninject.Selection.Selector.SelectMethodsForInjection(System.Type)">
            <summary>
            Selects methods that should be injected.
            </summary>
            <param name="type">The type.</param>
            <returns>A series of the selected methods.</returns>
        </member>
        <member name="P:Ninject.Selection.Selector.Flags">
            <summary>
            Gets the default binding flags.
            </summary>
        </member>
        <member name="P:Ninject.Selection.Selector.ConstructorScorer">
            <summary>
            Gets or sets the constructor scorer.
            </summary>
        </member>
        <member name="P:Ninject.Selection.Selector.InjectionHeuristics">
            <summary>
            Gets the property injection heuristics.
            </summary>
        </member>
        <member name="T:Ninject.ModuleLoadExtensions">
            <summary>
            Extension methods that enhance module loading.
            </summary>
        </member>
        <member name="M:Ninject.ModuleLoadExtensions.Load``1(Ninject.IKernel)">
            <summary>
            Creates a new instance of the module and loads it into the kernel.
            </summary>
            <typeparam name="TModule">The type of the module.</typeparam>
            <param name="kernel">The kernel.</param>
        </member>
        <member name="M:Ninject.ModuleLoadExtensions.Load(Ninject.IKernel,Ninject.Modules.INinjectModule[])">
            <summary>
            Loads the module(s) into the kernel.
            </summary>
            <param name="kernel">The kernel.</param>
            <param name="modules">The modules to load.</param>
        </member>
        <member name="M:Ninject.ModuleLoadExtensions.Load(Ninject.IKernel,System.String[])">
            <summary>
            Loads modules from the files that match the specified pattern(s).
            </summary>
            <param name="kernel">The kernel.</param>
            <param name="filePatterns">The file patterns (i.e. "*.dll", "modules/*.rb") to match.</param>
        </member>
        <member name="M:Ninject.ModuleLoadExtensions.Load(Ninject.IKernel,System.Reflection.Assembly[])">
            <summary>
            Loads modules defined in the specified assemblies.
            </summary>
            <param name="kernel">The kernel.</param>
            <param name="assemblies">The assemblies to search.</param>
        </member>
        <member name="T:Ninject.ResolutionExtensions">
            <summary>
            Extensions that enhance resolution of services.
            </summary>
        </member>
        <member name="M:Ninject.ResolutionExtensions.Get``1(Ninject.Syntax.IResolutionRoot,Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.Get``1(Ninject.Syntax.IResolutionRoot,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service by using the first binding with the specified name.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.Get``1(Ninject.Syntax.IResolutionRoot,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service by using the first binding that matches the specified constraint.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="constraint">The constraint to apply to the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGet``1(Ninject.Syntax.IResolutionRoot,Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGet``1(Ninject.Syntax.IResolutionRoot,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service by using the first binding with the specified name.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGet``1(Ninject.Syntax.IResolutionRoot,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service by using the first binding that matches the specified constraint.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="constraint">The constraint to apply to the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGetAndThrowOnInvalidBinding``1(Ninject.Syntax.IResolutionRoot,Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGetAndThrowOnInvalidBinding``1(Ninject.Syntax.IResolutionRoot,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service by using the first binding with the specified name.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGetAndThrowOnInvalidBinding``1(Ninject.Syntax.IResolutionRoot,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service by using the first binding that matches the specified constraint.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="constraint">The constraint to apply to the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.GetAll``1(Ninject.Syntax.IResolutionRoot,Ninject.Parameters.IParameter[])">
            <summary>
            Gets all available instances of the specified service.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>A series of instances of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.GetAll``1(Ninject.Syntax.IResolutionRoot,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Gets all instances of the specified service using bindings registered with the specified name.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>A series of instances of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.GetAll``1(Ninject.Syntax.IResolutionRoot,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Gets all instances of the specified service by using the bindings that match the specified constraint.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="constraint">The constraint to apply to the bindings.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>A series of instances of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.Get(Ninject.Syntax.IResolutionRoot,System.Type,Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.Get(Ninject.Syntax.IResolutionRoot,System.Type,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service by using the first binding with the specified name.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.Get(Ninject.Syntax.IResolutionRoot,System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service by using the first binding that matches the specified constraint.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="constraint">The constraint to apply to the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGet(Ninject.Syntax.IResolutionRoot,System.Type,Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGet(Ninject.Syntax.IResolutionRoot,System.Type,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service by using the first binding with the specified name.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.TryGet(Ninject.Syntax.IResolutionRoot,System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Tries to get an instance of the specified service by using the first binding that matches the specified constraint.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="constraint">The constraint to apply to the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service, or <see langword="null"/> if no implementation was available.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.GetAll(Ninject.Syntax.IResolutionRoot,System.Type,Ninject.Parameters.IParameter[])">
            <summary>
            Gets all available instances of the specified service.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>A series of instances of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.GetAll(Ninject.Syntax.IResolutionRoot,System.Type,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Gets all instances of the specified service using bindings registered with the specified name.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>A series of instances of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.GetAll(Ninject.Syntax.IResolutionRoot,System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Gets all instances of the specified service by using the bindings that match the specified constraint.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="constraint">The constraint to apply to the bindings.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>A series of instances of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.CanResolve``1(Ninject.Syntax.IResolutionRoot,Ninject.Parameters.IParameter[])">
            <summary>
            Evaluates if an instance of the specified service can be resolved.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.CanResolve``1(Ninject.Syntax.IResolutionRoot,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Evaluates if  an instance of the specified service by using the first binding with the specified name can be resolved.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.CanResolve``1(Ninject.Syntax.IResolutionRoot,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Evaluates if  an instance of the specified service by using the first binding that matches the specified constraint can be resolved.
            </summary>
            <typeparam name="T">The service to resolve.</typeparam>
            <param name="root">The resolution root.</param>
            <param name="constraint">The constraint to apply to the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.CanResolve(Ninject.Syntax.IResolutionRoot,System.Type,Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.CanResolve(Ninject.Syntax.IResolutionRoot,System.Type,System.String,Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service by using the first binding with the specified name.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="name">The name of the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="M:Ninject.ResolutionExtensions.CanResolve(Ninject.Syntax.IResolutionRoot,System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},Ninject.Parameters.IParameter[])">
            <summary>
            Gets an instance of the specified service by using the first binding that matches the specified constraint.
            </summary>
            <param name="root">The resolution root.</param>
            <param name="service">The service to resolve.</param>
            <param name="constraint">The constraint to apply to the binding.</param>
            <param name="parameters">The parameters to pass to the request.</param>
            <returns>An instance of the service.</returns>
        </member>
        <member name="T:Ninject.ActivationException">
            <summary>
            Indicates that an error occured during activation of an instance.
            </summary>
        </member>
        <member name="M:Ninject.ActivationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.ActivationException"/> class.
            </summary>
        </member>
        <member name="M:Ninject.ActivationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.ActivationException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Ninject.ActivationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.ActivationException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="M:Ninject.ActivationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.ActivationException"/> class.
            </summary>
            <param name="info">The serialized object data.</param>
            <param name="context">The serialization context.</param>
        </member>
        <member name="T:Ninject.GlobalKernelRegistration">
            <summary>
            Allows to register kernel globally to perform some tasks on all kernels.
            The registration is done by loading the GlobalKernelRegistrationModule to the kernel.
            </summary>
        </member>
        <member name="M:Ninject.GlobalKernelRegistration.MapKernels(System.Action{Ninject.IKernel})">
            <summary>
            Performs an action on all registered kernels.
            </summary>
            <param name="action">The action.</param>
        </member>
        <member name="T:Ninject.GlobalKernelRegistrationModule`1">
            <summary>
            Registers the kernel into which the module is loaded on the GlobalKernelRegistry using the
            type specified by TGlobalKernelRegistry.
            </summary>
            <typeparam name="TGlobalKernelRegistry">The type that is used to register the kernel.</typeparam>
        </member>
        <member name="M:Ninject.GlobalKernelRegistrationModule`1.Load">
            <summary>
            Loads the module into the kernel.
            </summary>
        </member>
        <member name="M:Ninject.GlobalKernelRegistrationModule`1.Unload">
            <summary>
            Unloads the module from the kernel.
            </summary>
        </member>
        <member name="T:Ninject.IInitializable">
            <summary>
            A service that requires initialization after it is activated.
            </summary>
        </member>
        <member name="M:Ninject.IInitializable.Initialize">
            <summary>
            Initializes the instance. Called during activation.
            </summary>
        </member>
        <member name="T:Ninject.IKernel">
            <summary>
            A super-factory that can create objects of all kinds, following hints provided by <see cref="T:Ninject.Planning.Bindings.IBinding"/>s.
            </summary>
        </member>
        <member name="M:Ninject.IKernel.GetModules">
            <summary>
            Gets the modules that have been loaded into the kernel.
            </summary>
            <returns>A series of loaded modules.</returns>
        </member>
        <member name="M:Ninject.IKernel.HasModule(System.String)">
            <summary>
            Determines whether a module with the specified name has been loaded in the kernel.
            </summary>
            <param name="name">The name of the module.</param>
            <returns><c>True</c> if the specified module has been loaded; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.IKernel.Load(System.Collections.Generic.IEnumerable{Ninject.Modules.INinjectModule})">
            <summary>
            Loads the module(s) into the kernel.
            </summary>
            <param name="m">The modules to load.</param>
        </member>
        <member name="M:Ninject.IKernel.Load(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Loads modules from the files that match the specified pattern(s).
            </summary>
            <param name="filePatterns">The file patterns (i.e. "*.dll", "modules/*.rb") to match.</param>
        </member>
        <member name="M:Ninject.IKernel.Load(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Loads modules defined in the specified assemblies.
            </summary>
            <param name="assemblies">The assemblies to search.</param>
        </member>
        <member name="M:Ninject.IKernel.Unload(System.String)">
            <summary>
            Unloads the plugin with the specified name.
            </summary>
            <param name="name">The plugin's name.</param>
        </member>
        <member name="M:Ninject.IKernel.Inject(System.Object,Ninject.Parameters.IParameter[])">
            <summary>
            Injects the specified existing instance, without managing its lifecycle.
            </summary>
            <param name="instance">The instance to inject.</param>
            <param name="parameters">The parameters to pass to the request.</param>
        </member>
        <member name="M:Ninject.IKernel.GetBindings(System.Type)">
            <summary>
            Gets the bindings registered for the specified service.
            </summary>
            <param name="service">The service in question.</param>
            <returns>A series of bindings that are registered for the service.</returns>
        </member>
        <member name="M:Ninject.IKernel.BeginBlock">
            <summary>
            Begins a new activation block, which can be used to deterministically dispose resolved instances.
            </summary>
            <returns>The new activation block.</returns>
        </member>
        <member name="P:Ninject.IKernel.Settings">
            <summary>
            Gets the kernel settings.
            </summary>
        </member>
        <member name="P:Ninject.IKernel.Components">
            <summary>
            Gets the component container, which holds components that contribute to Ninject.
            </summary>
        </member>
        <member name="T:Ninject.INinjectSettings">
            <summary>
            Contains configuration options for Ninject.
            </summary>
        </member>
        <member name="M:Ninject.INinjectSettings.Get``1(System.String,``0)">
            <summary>
            Gets the value for the specified key.
            </summary>
            <typeparam name="T">The type of value to return.</typeparam>
            <param name="key">The setting's key.</param>
            <param name="defaultValue">The value to return if no setting is available.</param>
            <returns>The value, or the default value if none was found.</returns>
        </member>
        <member name="M:Ninject.INinjectSettings.Set(System.String,System.Object)">
            <summary>
            Sets the value for the specified key.
            </summary>
            <param name="key">The setting's key.</param>
            <param name="value">The setting's value.</param>
        </member>
        <member name="P:Ninject.INinjectSettings.InjectAttribute">
            <summary>
            Gets the attribute that indicates that a member should be injected.
            </summary>
        </member>
        <member name="P:Ninject.INinjectSettings.CachePruningInterval">
            <summary>
            Gets the interval at which the cache should be pruned.
            </summary>
        </member>
        <member name="P:Ninject.INinjectSettings.DefaultScopeCallback">
            <summary>
            Gets the default scope callback.
            </summary>
        </member>
        <member name="P:Ninject.INinjectSettings.LoadExtensions">
            <summary>
            Gets a value indicating whether the kernel should automatically load extensions at startup.
            </summary>
        </member>
        <member name="P:Ninject.INinjectSettings.ExtensionSearchPatterns">
            <summary>
            Gets the paths that should be searched for extensions.
            </summary>
        </member>
        <member name="P:Ninject.INinjectSettings.UseReflectionBasedInjection">
            <summary>
            Gets a value indicating whether Ninject should use reflection-based injection instead of
            the (usually faster) lightweight code generation system.
            </summary>
        </member>
        <member name="P:Ninject.INinjectSettings.InjectNonPublic">
            <summary>
            Gets a value indicating whether Ninject should inject non public members.
            </summary>
        </member>
        <member name="P:Ninject.INinjectSettings.InjectParentPrivateProperties">
            <summary>
            Gets a value indicating whether Ninject should inject private properties of base classes.
            </summary>
            <remarks>
            Activating this setting has an impact on the performance. It is recomended not
            to use this feature and use constructor injection instead.
            </remarks>
        </member>
        <member name="P:Ninject.INinjectSettings.ActivationCacheDisabled">
            <summary>
            Gets or sets a value indicating whether the activation cache is disabled.
            If the activation cache is disabled less memory is used. But in some cases
            instances are activated or deactivated multiple times. e.g. in the following scenario:
            Bind{A}().ToSelf();
            Bind{IA}().ToMethod(ctx => kernel.Get{IA}();
            </summary>
            <value>
                <c>true</c> if activation cache is disabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Ninject.INinjectSettings.AllowNullInjection">
            <summary>
            Gets or sets a value indicating whether Null is a valid value for injection.
            By defuault this is disabled and whenever a provider returns null an exception is thrown.
            </summary>
            <value><c>true</c> if null is allowed as injected value otherwise false.</value>
        </member>
        <member name="T:Ninject.IStartable">
            <summary>
            A service that is started when activated, and stopped when deactivated.
            </summary>
        </member>
        <member name="M:Ninject.IStartable.Start">
            <summary>
            Starts this instance. Called during activation.
            </summary>
        </member>
        <member name="M:Ninject.IStartable.Stop">
            <summary>
            Stops this instance. Called during deactivation.
            </summary>
        </member>
        <member name="T:Ninject.KernelBase">
            <summary>
            The base implementation of an <see cref="T:Ninject.IKernel"/>.
            </summary>
        </member>
        <member name="F:Ninject.KernelBase.HandleMissingBindingLockObject">
            <summary>
            Lock used when adding missing bindings.
            </summary>
        </member>
        <member name="M:Ninject.KernelBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.KernelBase"/> class.
            </summary>
        </member>
        <member name="M:Ninject.KernelBase.#ctor(Ninject.Modules.INinjectModule[])">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.KernelBase"/> class.
            </summary>
            <param name="modules">The modules to load into the kernel.</param>
        </member>
        <member name="M:Ninject.KernelBase.#ctor(Ninject.INinjectSettings,Ninject.Modules.INinjectModule[])">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.KernelBase"/> class.
            </summary>
            <param name="settings">The configuration to use.</param>
            <param name="modules">The modules to load into the kernel.</param>
        </member>
        <member name="M:Ninject.KernelBase.#ctor(Ninject.Components.IComponentContainer,Ninject.INinjectSettings,Ninject.Modules.INinjectModule[])">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.KernelBase"/> class.
            </summary>
            <param name="components">The component container to use.</param>
            <param name="settings">The configuration to use.</param>
            <param name="modules">The modules to load into the kernel.</param>
        </member>
        <member name="M:Ninject.KernelBase.Dispose(System.Boolean)">
            <summary>
            Releases resources held by the object.
            </summary>
        </member>
        <member name="M:Ninject.KernelBase.Unbind(System.Type)">
            <summary>
            Unregisters all bindings for the specified service.
            </summary>
            <param name="service">The service to unbind.</param>
        </member>
        <member name="M:Ninject.KernelBase.AddBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Registers the specified binding.
            </summary>
            <param name="binding">The binding to add.</param>
        </member>
        <member name="M:Ninject.KernelBase.RemoveBinding(Ninject.Planning.Bindings.IBinding)">
            <summary>
            Unregisters the specified binding.
            </summary>
            <param name="binding">The binding to remove.</param>
        </member>
        <member name="M:Ninject.KernelBase.HasModule(System.String)">
            <summary>
            Determines whether a module with the specified name has been loaded in the kernel.
            </summary>
            <param name="name">The name of the module.</param>
            <returns><c>True</c> if the specified module has been loaded; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.KernelBase.GetModules">
            <summary>
            Gets the modules that have been loaded into the kernel.
            </summary>
            <returns>A series of loaded modules.</returns>
        </member>
        <member name="M:Ninject.KernelBase.Load(System.Collections.Generic.IEnumerable{Ninject.Modules.INinjectModule})">
            <summary>
            Loads the module(s) into the kernel.
            </summary>
            <param name="m">The modules to load.</param>
        </member>
        <member name="M:Ninject.KernelBase.Load(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Loads modules from the files that match the specified pattern(s).
            </summary>
            <param name="filePatterns">The file patterns (i.e. "*.dll", "modules/*.rb") to match.</param>
        </member>
        <member name="M:Ninject.KernelBase.Load(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Loads modules defined in the specified assemblies.
            </summary>
            <param name="assemblies">The assemblies to search.</param>
        </member>
        <member name="M:Ninject.KernelBase.Unload(System.String)">
            <summary>
            Unloads the plugin with the specified name.
            </summary>
            <param name="name">The plugin's name.</param>
        </member>
        <member name="M:Ninject.KernelBase.Inject(System.Object,Ninject.Parameters.IParameter[])">
            <summary>
            Injects the specified existing instance, without managing its lifecycle.
            </summary>
            <param name="instance">The instance to inject.</param>
            <param name="parameters">The parameters to pass to the request.</param>
        </member>
        <member name="M:Ninject.KernelBase.Release(System.Object)">
            <summary>
            Deactivates and releases the specified instance if it is currently managed by Ninject.
            </summary>
            <param name="instance">The instance to release.</param>
            <returns><see langword="True"/> if the instance was found and released; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Ninject.KernelBase.CanResolve(Ninject.Activation.IRequest)">
            <summary>
            Determines whether the specified request can be resolved.
            </summary>
            <param name="request">The request.</param>
            <returns><c>True</c> if the request can be resolved; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.KernelBase.CanResolve(Ninject.Activation.IRequest,System.Boolean)">
            <summary>
            Determines whether the specified request can be resolved.
            </summary>
            <param name="request">The request.</param>
            <param name="ignoreImplicitBindings">if set to <c>true</c> implicit bindings are ignored.</param>
            <returns>
                <c>True</c> if the request can be resolved; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Ninject.KernelBase.Resolve(Ninject.Activation.IRequest)">
            <summary>
            Resolves instances for the specified request. The instances are not actually resolved
            until a consumer iterates over the enumerator.
            </summary>
            <param name="request">The request to resolve.</param>
            <returns>An enumerator of instances that match the request.</returns>
        </member>
        <member name="M:Ninject.KernelBase.CreateRequest(System.Type,System.Func{Ninject.Planning.Bindings.IBindingMetadata,System.Boolean},System.Collections.Generic.IEnumerable{Ninject.Parameters.IParameter},System.Boolean,System.Boolean)">
            <summary>
            Creates a request for the specified service.
            </summary>
            <param name="service">The service that is being requested.</param>
            <param name="constraint">The constraint to apply to the bindings to determine if they match the request.</param>
            <param name="parameters">The parameters to pass to the resolution.</param>
            <param name="isOptional"><c>True</c> if the request is optional; otherwise, <c>false</c>.</param>
            <param name="isUnique"><c>True</c> if the request should return a unique result; otherwise, <c>false</c>.</param>
            <returns>The created request.</returns>
        </member>
        <member name="M:Ninject.KernelBase.BeginBlock">
            <summary>
            Begins a new activation block, which can be used to deterministically dispose resolved instances.
            </summary>
            <returns>The new activation block.</returns>
        </member>
        <member name="M:Ninject.KernelBase.GetBindings(System.Type)">
            <summary>
            Gets the bindings registered for the specified service.
            </summary>
            <param name="service">The service in question.</param>
            <returns>A series of bindings that are registered for the service.</returns>
        </member>
        <member name="M:Ninject.KernelBase.GetBindingPrecedenceComparer">
            <summary>
            Returns an IComparer that is used to determine resolution precedence.
            </summary>
            <returns>An IComparer that is used to determine resolution precedence.</returns>
        </member>
        <member name="M:Ninject.KernelBase.SatifiesRequest(Ninject.Activation.IRequest)">
            <summary>
            Returns a predicate that can determine if a given IBinding matches the request.
            </summary>
            <param name="request">The request/</param>
            <returns>A predicate that can determine if a given IBinding matches the request.</returns>
        </member>
        <member name="M:Ninject.KernelBase.AddComponents">
            <summary>
            Adds components to the kernel during startup.
            </summary>
        </member>
        <member name="M:Ninject.KernelBase.HandleMissingBinding(System.Type)">
            <summary>
            Attempts to handle a missing binding for a service.
            </summary>
            <param name="service">The service.</param>
            <returns><c>True</c> if the missing binding can be handled; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.KernelBase.HandleMissingBinding(Ninject.Activation.IRequest)">
            <summary>
            Attempts to handle a missing binding for a request.
            </summary>
            <param name="request">The request.</param>
            <returns><c>True</c> if the missing binding can be handled; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Ninject.KernelBase.TypeIsSelfBindable(System.Type)">
            <summary>
            Returns a value indicating whether the specified service is self-bindable.
            </summary>
            <param name="service">The service.</param>
            <returns><see langword="True"/> if the type is self-bindable; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:Ninject.KernelBase.CreateContext(Ninject.Activation.IRequest,Ninject.Planning.Bindings.IBinding)">
            <summary>
            Creates a context for the specified request and binding.
            </summary>
            <param name="request">The request.</param>
            <param name="binding">The binding.</param>
            <returns>The created context.</returns>
        </member>
        <member name="P:Ninject.KernelBase.Settings">
            <summary>
            Gets the kernel settings.
            </summary>
        </member>
        <member name="P:Ninject.KernelBase.Components">
            <summary>
            Gets the component container, which holds components that contribute to Ninject.
            </summary>
        </member>
        <member name="T:Ninject.NinjectSettings">
            <summary>
            Contains configuration options for Ninject.
            </summary>
        </member>
        <member name="M:Ninject.NinjectSettings.Get``1(System.String,``0)">
            <summary>
            Gets the value for the specified key.
            </summary>
            <typeparam name="T">The type of value to return.</typeparam>
            <param name="key">The setting's key.</param>
            <param name="defaultValue">The value to return if no setting is available.</param>
            <returns>The value, or the default value if none was found.</returns>
        </member>
        <member name="M:Ninject.NinjectSettings.Set(System.String,System.Object)">
            <summary>
            Sets the value for the specified key.
            </summary>
            <param name="key">The setting's key.</param>
            <param name="value">The setting's value.</param>
        </member>
        <member name="P:Ninject.NinjectSettings.InjectAttribute">
            <summary>
            Gets or sets the attribute that indicates that a member should be injected.
            </summary>
        </member>
        <member name="P:Ninject.NinjectSettings.CachePruningInterval">
            <summary>
            Gets or sets the interval at which the GC should be polled.
            </summary>
        </member>
        <member name="P:Ninject.NinjectSettings.DefaultScopeCallback">
            <summary>
            Gets or sets the default scope callback.
            </summary>
        </member>
        <member name="P:Ninject.NinjectSettings.LoadExtensions">
            <summary>
            Gets or sets a value indicating whether the kernel should automatically load extensions at startup.
            </summary>
        </member>
        <member name="P:Ninject.NinjectSettings.ExtensionSearchPatterns">
            <summary>
            Gets or sets the paths that should be searched for extensions.
            </summary>
        </member>
        <member name="P:Ninject.NinjectSettings.UseReflectionBasedInjection">
            <summary>
            Gets a value indicating whether Ninject should use reflection-based injection instead of
            the (usually faster) lightweight code generation system.
            </summary>
        </member>
        <member name="P:Ninject.NinjectSettings.InjectNonPublic">
            <summary>
            Gets a value indicating whether Ninject should inject non public members.
            </summary>
        </member>
        <member name="P:Ninject.NinjectSettings.InjectParentPrivateProperties">
            <summary>
            Gets a value indicating whether Ninject should inject private properties of base classes.
            </summary>
            <remarks>
            Activating this setting has an impact on the performance. It is recomended not
            to use this feature and use constructor injection instead.
            </remarks>
        </member>
        <member name="P:Ninject.NinjectSettings.ActivationCacheDisabled">
            <summary>
            Gets or sets a value indicating whether the activation cache is disabled.
            If the activation cache is disabled less memory is used. But in some cases
            instances are activated or deactivated multiple times. e.g. in the following scenario:
            Bind{A}().ToSelf();
            Bind{IA}().ToMethod(ctx =&gt; kernel.Get{IA}();
            </summary>
            <value>
            	<c>true</c> if activation cache is disabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Ninject.NinjectSettings.AllowNullInjection">
            <summary>
            Gets or sets a value indicating whether Null is a valid value for injection.
            By default this is disabled and whenever a provider returns null an exception is thrown.
            </summary>
            <value>
            	<c>true</c> if null is allowed as injected value otherwise false.
            </value>
        </member>
        <member name="T:Ninject.StandardKernel">
            <summary>
            The standard implementation of a kernel.
            </summary>
        </member>
        <member name="M:Ninject.StandardKernel.#ctor(Ninject.Modules.INinjectModule[])">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.StandardKernel"/> class.
            </summary>
            <param name="modules">The modules to load into the kernel.</param>
        </member>
        <member name="M:Ninject.StandardKernel.#ctor(Ninject.INinjectSettings,Ninject.Modules.INinjectModule[])">
            <summary>
            Initializes a new instance of the <see cref="T:Ninject.StandardKernel"/> class.
            </summary>
            <param name="settings">The configuration to use.</param>
            <param name="modules">The modules to load into the kernel.</param>
        </member>
        <member name="M:Ninject.StandardKernel.AddComponents">
            <summary>
            Adds components to the kernel during startup.
            </summary>
        </member>
        <member name="P:Ninject.StandardKernel.KernelInstance">
            <summary>
            Gets the kernel.
            </summary>
            <value>The kernel.</value>
        </member>
    </members>
</doc>
