<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IdentityModel.Tokens</name>
    </assembly>
    <members>
        <member name="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey">
            <summary>
            Base class for a Security Key that contains Asymmetric key material.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey.HasPrivateKey">
            <summary>
            This must be overridden to get a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider">
            <summary>
            Provides signing and verifying operations when working with an <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.DefaultMinimumAsymmetricKeySizeInBitsForSigningMap">
            <summary>
            Mapping from algorithm to minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize when creating signatures.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.DefaultMinimumAsymmetricKeySizeInBitsForVerifyingMap">
            <summary>
            Mapping from algorithm to minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize when verifying signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> class used to create and verify signatures.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.<see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/></param>
            <param name="algorithm">The signature algorithm to apply.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> class used to create and verify signatures.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <param name="willCreateSignatures">Whether this <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> is required to create signatures then set this to true.</param>
            <para>
            Creating signatures requires that the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> has access to a private key.
            Verifying signatures (the default), does not require access to the private key.
            </para>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            willCreateSignatures is true and <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.KeySize is less than the size corresponding to the given algorithm in <see cref="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForSigningMap"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.KeySize is less than the size corresponding to the algorithm in <see cref="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForVerifyingMap"/>. Note: this is always checked.
            </exception>
            <exception cref="T:System.ArgumentException">If <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">If the runtime is unable to create a suitable cryptographic provider.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForSigningMap">
            <summary>
            Gets the mapping from algorithm to the minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize for creating signatures.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForVerifyingMap">
            <summary>
            Gets the mapping from algorithm to the minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize for verifying signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.GetHashAlgorithmString(System.String)">
            <summary>
            Returns the algorithm name.
            </summary>
            <param name="algorithm">The hash algorithm to use to create the hash value.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.GetKeyByteCount(System.String)">
            <summary>
            Returns the size of key in bytes
            </summary>
            <param name="curveId">Represents ecdsa curve -P256, P384, P521</param>
            <returns>Size of the key in bytes</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.GetMagicValue(System.String,System.Boolean)">
            <summary>
            Returns the magic value representing the curve corresponding to the curve id.
            </summary>
            <param name="curveId">Represents ecdsa curve -P256, P384, P512</param>
            <param name="willCreateSignatures">Whether the provider will create signatures or not</param>
            <returns>Uint representing the magic number</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.KeyBlobMagicNumber">
            <summary>
            Magic numbers identifying ECDSA blob types
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Sign(System.Byte[])">
            <summary>
            Produces a signature over the 'input' using the <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> and algorithm passed to <see cref="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)"/>.
            </summary>
            <param name="input">The bytes to be signed.</param>
            <returns>A signature over the input.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null. </exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called. </exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> is null. This can occur if the constructor parameter 'willBeUsedforSigning' was not 'true'.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:System.Security.Cryptography.HashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            Verifies that a signature over the' input' matches the signature.
            </summary>
            <param name="input">The bytes to generate the signature over.</param>
            <param name="signature">The value to verify against.</param>
            <returns>true if signature matches, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0.</exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called. </exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> is null. This can occur if a derived type does not call the base constructor.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:System.Security.Cryptography.HashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.ValidateAsymmetricSecurityKeySize(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Validates that the asymmetric key size is more than the allowed minimum
            </summary>
            <param name="key">The asymmetric key to validate</param>
            <param name="algorithm">Algorithm for which this key will be used</param>
            <param name="willCreateSignatures">Whether they key will be used for creating signatures</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)">
            <summary>
            Calls <see cref="M:System.Security.Cryptography.HashAlgorithm.Dispose"/> to release this managed resources.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Base64UrlEncoder">
            <summary>
            Encodes and Decodes strings as Base64Url encoding.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.String)">
            <summary>
            The following functions perform base64url encoding which differs from regular base64 encoding as follows
            * padding is skipped so the pad character '=' doesn't have to be percent encoded
            * the 62nd and 63rd regular base64 encoding characters ('+' and '/') are replace with ('-' and '_')
            The changes make the encoding alphabet file and URL safe.
            </summary>
            <param name="arg">string to encode.</param>
            <returns>Base64Url encoding of the UTF8 bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Converts a subset of an array of 8-bit unsigned integers to its equivalent string representation that is encoded with base-64-url digits. Parameters specify
            the subset as an offset in the input array, and the number of elements in the array to convert.
            </summary>
            <param name="inArray">An array of 8-bit unsigned integers.</param>
            <param name="length">An offset in inArray.</param>
            <param name="offset">The number of elements of inArray to convert.</param>
            <returns>The string representation in base 64 url encodingof length elements of inArray, starting at position offset.</returns>
            <exception cref="T:System.ArgumentNullException">'inArray' is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or length is negative OR offset plus length is greater than the length of inArray.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.Byte[])">
            <summary>
            Converts a subset of an array of 8-bit unsigned integers to its equivalent string representation that is encoded with base-64-url digits. Parameters specify
            the subset as an offset in the input array, and the number of elements in the array to convert.
            </summary>
            <param name="inArray">An array of 8-bit unsigned integers.</param>
            <returns>The string representation in base 64 url encodingof length elements of inArray, starting at position offset.</returns>
            <exception cref="T:System.ArgumentNullException">'inArray' is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or length is negative OR offset plus length is greater than the length of inArray.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.DecodeBytes(System.String)">
            <summary>
             Converts the specified string, which encodes binary data as base-64-url digits, to an equivalent 8-bit unsigned integer array.</summary>
            <param name="str">base64Url encoded string.</param>
            <returns>UTF8 bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Decode(System.String)">
            <summary>
            Decodes the string from Base64UrlEncoded to UTF8.
            </summary>
            <param name="arg">string to decode.</param>
            <returns>UTF8 string.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory">
            <summary>
            Creates <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>s by specifying a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            <para>Supports both <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> and <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</para>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.Default">
            <summary>
            Returns the default <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider">
            <summary>
            Extensibility point for custom crypto support application wide.
            </summary>
            <remarks>By default, if set, <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> will be called before crypto operations.
            If true is returned, then this will be called for operations.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.#cctor">
            <summary>
            Static constructor that initializes the default <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.#ctor">
            <summary>
            Default constructor for <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.#ctor(Microsoft.IdentityModel.Tokens.CryptoProviderFactory)">
            <summary>
            Constructor that creates a deep copy of given <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/> object.
            </summary>
            <param name="other"><see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/> to copy from.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.IsSupportedAlgorithm(System.String)">
            <summary>
            Answers if an algorithm is supported
            </summary>
            <param name="algorithm">the name of the crypto algorithm</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.IsSupportedAlgorithm(System.String,Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Checks if an 'algorithm, key' pair is supported.
            </summary>
            <param name="algorithm">the algorithm to check.</param>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.</param>
            <returns>true if 'algorithm, key' pair is supported.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateAuthenticatedEncryptionProvider(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/> for a specific &lt;SecurityKey, Algorithm>.
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use.</param>
            <param name="algorithm">the algorithm to use.</param>
            <returns>an instance of <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/></returns>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">'key' is not a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <exception cref="T:System.ArgumentException">'algorithm, key' pair is not supported.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateForSigning(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that supports the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use for signing.</param>
            <param name="algorithm">The algorithm to use for signing.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or empty.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>' is too small.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> is too small.</exception>
            <exception cref="T:System.ArgumentException"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is not a <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> or a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <remarks>
            AsymmetricSignatureProviders require access to a PrivateKey for Signing.
            <para>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)"/>.</para>
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateForVerifying(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> instance supports the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use for signing.</param>
            <param name="algorithm">The algorithm to use for verifying.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or empty.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> is too small.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException"><see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> is too small.</exception>
            <exception cref="T:System.ArgumentException"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>' is not a <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> or a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <remarks>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)"/>.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            When finished with a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call this method for cleanup. The default behavior is to call <see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose"/>
            </summary>
            <param name="signatureProvider"><see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to be released.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseHashAlgorithm(System.Security.Cryptography.HashAlgorithm)">
            <summary>
            When finished with a <see cref="T:System.Security.Cryptography.HashAlgorithm"/> call this method for cleanup. The default behavior is to call <see cref="M:System.Security.Cryptography.HashAlgorithm.Dispose"/>
            </summary>
            <param name="hashAlgorithm"><see cref="T:System.Security.Cryptography.HashAlgorithm"/> to be released.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateHashAlgorithm(System.String)">
            <summary>
            Returns a <see cref="T:System.Security.Cryptography.HashAlgorithm"/> for a specific algorithm.
            </summary>
            <param name="algorithm">the name of the hash algorithm to create.</param>
            <returns>A <see cref="T:System.Security.Cryptography.HashAlgorithm"/></returns>
            <remarks>When finished with the <see cref="T:System.Security.Cryptography.HashAlgorithm"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseHashAlgorithm(System.Security.Cryptography.HashAlgorithm)"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or empty.</exception>
            <exception cref="T:System.InvalidOperationException">'algorithm' is not supported.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateKeyedHashAlgorithm(System.Byte[],System.String)">
            <summary>
            Returns a <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> for a specific algorithm.
            </summary>
            <param name="algorithm">the keyed hash algorithm to create.</param>
            <param name="keyBytes">bytes to use to create the Keyed Hash</param>
            <returns>A <see cref="T:System.Security.Cryptography.HashAlgorithm"/></returns>
            <remarks>When finished with the <see cref="T:System.Security.Cryptography.HashAlgorithm"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseHashAlgorithm(System.Security.Cryptography.HashAlgorithm)"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or empty.</exception>
            <exception cref="T:System.InvalidOperationException">'algorithm' is not supported.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.DateTimeUtil">
            <summary>
            Helper class for adding DateTimes and Timespans.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.Add(System.DateTime,System.TimeSpan)">
            <summary>
            Add a DateTime and a TimeSpan.
            The maximum time is DateTime.MaxTime.  It is not an error if time + timespan > MaxTime.
            Just return MaxTime.
            </summary>
            <param name="time">Initial <see cref="T:System.DateTime"/> value.</param>
            <param name="timespan"><see cref="T:System.TimeSpan"/> to add.</param>
            <returns><see cref="T:System.DateTime"/> as the sum of time and timespan.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.GetMaxValue(System.DateTimeKind)">
            <summary>
            Gets the Maximum value for a DateTime specifying kind.
            </summary>
            <param name="kind">DateTimeKind to use.</param>
            <returns>DateTime of specified kind.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.GetMinValue(System.DateTimeKind)">
            <summary>
            Gets the Minimum value for a DateTime specifying kind.
            </summary>
            <param name="kind">DateTimeKind to use.</param>
            <returns>DateTime of specified kind.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey">
            <summary>
            Represents a ECDsa security key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.#ctor(System.Security.Cryptography.ECDsa)">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey"/>.
            </summary>
            <param name="ecdsa"><see cref="T:System.Security.Cryptography.ECDsa"/></param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.ECDsa">
            <summary>
            <see cref="T:System.Security.Cryptography.ECDsa"/> instance used to initialize the key.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.KeySize">
            <summary>
            Gets <see cref="T:System.Security.Cryptography.ECDsa"/> key size.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials">
            <summary>
            A wrapper class for properties that are used for token encryption.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncryptingCredentials.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SigningCredentials"/> class.
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/></param>
            <param name="alg">The key encryption algorithm to apply.</param>
            <param name="enc">The encryption algorithm to apply.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.Alg">
            <summary>
            Gets the algorithm which used for token encryption.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.Enc">
            <summary>
            Gets the algorithm which used for token encryption.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.CryptoProviderFactory">
            <summary>
            Users can override the default <see cref="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.CryptoProviderFactory"/> with this property. This factory will be used for creating encryition providers.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> which used for signature valdiation.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.EpochTime">
            <summary>
            Returns the absolute DateTime or the Seconds since Unix Epoch, where Epoch is UTC 1970-01-01T0:0:0Z.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.EpochTime.UnixEpoch">
            <summary>
            DateTime as UTV for UnixEpoch
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EpochTime.GetIntDate(System.DateTime)">
            <summary>
            Per JWT spec:
            Gets the number of seconds from 1970-01-01T0:0:0Z as measured in UTC until the desired date/time.
            </summary>
            <param name="datetime">The DateTime to convert to seconds.</param>
            <remarks>if dateTimeUtc less than UnixEpoch, return 0</remarks>
            <returns>the number of seconds since Unix Epoch.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EpochTime.DateTime(System.Int64)">
            <summary>
            Creates a DateTime from epoch time.
            </summary>
            <param name="secondsSinceUnixEpoch">Number of seconds.</param>
            <returns>The DateTime in UTC.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ICryptoProvider">
            <summary>
            Crypto operations
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])">
            <summary>
            Called to determine if a cryptoType is supported.
            </summary>
            <param name="algorithm">the algorithm that defines the crypto operator.</param>
            <param name="args">the arguments required by the cryptoType. May be null.</param>
            <returns>true if supported</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])">
            <summary>
            returns an object of cryptoType.
            </summary>
            <param name="algorithm">the algorithm that defines the crypto operator.</param>
            <param name="args">the arguments required by the cryptoType. May be null.</param>
            <remarks>call <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Release(System.Object)"/> when finished with the object.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Release(System.Object)">
            <summary>
            called to release the object returned from <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/>
            </summary>
            <param name="cryptoInstance">the object returned from <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/>.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator">
            <summary>
            ISecurityTokenValidator
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.CanReadToken(System.String)">
            <summary>
            Returns true if the token can be read, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.CanValidateToken">
            <summary>
            Returns true if a token can be validated.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.MaximumTokenSizeInBytes">
            <summary>
            Gets and sets the maximum size in bytes, that a will be processed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.ValidateToken(System.String,Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            Validates a token passed as a string using <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ITokenReplayCache">
            <summary>
            Interface that defines a simple cache for tacking replaying of security tokens.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ITokenReplayCache.TryAdd(System.String,System.DateTime)">
            <summary>
            Try to add a securityToken.
            </summary>
            <param name="securityToken">the security token to add.</param>
            <param name="expiresOn">the time when security token expires.</param>
            <returns>true if the security token was successfully added.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ITokenReplayCache.TryFind(System.String)">
            <summary>
            Try to find securityToken
            </summary>
            <param name="securityToken">the security token to find.</param>
            <returns>true if the security token is found.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebAlgorithmsKeyTypes">
            <summary>
            Constants for JsonWebAlgorithms  "kty" Key Type (sec 6.1)
            http://tools.ietf.org/html/rfc7518#section-6.1
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKey">
            <summary>
            Represents a Json Web Key as defined in http://tools.ietf.org/html/rfc7517.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.Create(System.String)">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
            <param name="json">A string that contains JSON Web Key parameters in JSON format.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/></returns>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.#ctor">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.#ctor(System.String)">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> from a json string.
            </summary>
            <param name="json">A string that contains JSON Web Key parameters in JSON format.</param>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.AdditionalData">
            <summary>
            When deserializing from JSON any properties that are not defined will be placed here.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Alg">
            <summary>
            Gets or sets the 'alg' (KeyType)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Crv">
            <summary>
            Gets or sets the 'crv' (ECC - Curve)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.D">
            <summary>
            Gets or sets the 'd' (ECC - Private Key OR RSA - Private Exponent)..
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.DP">
            <summary>
            Gets or sets the 'dp' (RSA - First Factor CRT Exponent)..
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.DQ">
            <summary>
            Gets or sets the 'dq' (RSA - Second Factor CRT Exponent)..
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.E">
            <summary>
            Gets or sets the 'e' (RSA - Exponent)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.K">
            <summary>
            Gets or sets the 'k' (Symmetric - Key Value)..
            </summary>
            Base64urlEncoding
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.KeyOps">
            <summary>
            Gets the 'key_ops' (Key Operations)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kid">
            <summary>
            Gets or sets the 'kid' (Key ID)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kty">
            <summary>
            Gets or sets the 'kty' (Key Type)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.N">
            <summary>
            Gets or sets the 'n' (RSA - Modulus)..
            </summary>
            <remarks>Value is formated as: Base64urlEncoding</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Oth">
            <summary>
            Gets or sets the 'oth' (RSA - Other Primes Info)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.P">
            <summary>
            Gets or sets the 'p' (RSA - First Prime Factor)..
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Q">
            <summary>
            Gets or sets the 'q' (RSA - Second  Prime Factor)..
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.QI">
            <summary>
            Gets or sets the 'qi' (RSA - First CRT Coefficient)..
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Use">
            <summary>
            Gets or sets the 'use' (Public Key Use)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X">
            <summary>
            Gets or sets the 'x' (ECC - X Coordinate)..
            </summary>
            <remarks>Value is formated as: Base64urlEncoding</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5c">
            <summary>
            Gets the 'x5c' collection (X.509 Certificate Chain)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5t">
            <summary>
            Gets or sets the 'x5t' (X.509 Certificate SHA-1 thumbprint)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5tS256">
            <summary>
            Gets or sets the 'x5t#S256' (X.509 Certificate SHA-1 thumbprint)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5u">
            <summary>
            Gets or sets the 'x5u' (X.509 URL)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Y">
            <summary>
            Gets or sets the 'y' (ECC - Y Coordinate)..
            </summary>
            <remarks>Value is formated as: Base64urlEncoding</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.KeySize">
            <summary>
            Gets the key size of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyECTypes">
            <summary>
            Constants for JsonWebKey Elliptical Curve Types
            https://tools.ietf.org/html/rfc7518#section-*******
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyParameterNames">
            <summary>
            Names for Json Web Key Values
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet">
            <summary>
            Contains a collection of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> that can be populated from a json string.
            </summary>
            <remarks>provides support for http://tools.ietf.org/html/rfc7517.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.Create(System.String)">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/>.
            </summary>
            <param name="json">a string that contains JSON Web Key parameters in JSON format.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/></returns>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.#ctor">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.#ctor(System.String)">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/> from a json string.
            </summary>
            <param name="json">a json string containing values.</param>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKeySet.AdditionalData">
            <summary>
            When deserializing from JSON any properties that are not defined will be placed here.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKeySet.Keys">
            <summary>
            Gets the <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>       
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.GetSigningKeys">
            <summary>
            Returns the JsonWebKeys as a <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeySetParameterNames">
            <summary>
            Names for Json Web Key Set Values
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyUseNames">
            <summary>
            Constants for JsonWebKeyUse (sec 4.2)
            http://tools.ietf.org/html/rfc7517#section-4
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.LogMessages">
            <summary>
            Log messages and codes
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy">
            <summary>
            The purpose of this class is to ensure that we obtain an RsaCryptoServiceProvider that supports SHA-256 signatures.
            If the original RsaCryptoServiceProvider doesn't support SHA-256, we create a new one using the same KeyContainer.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.#ctor(System.Security.Cryptography.RSACryptoServiceProvider)">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy"/>.
            </summary>
            <param name="rsa"><see cref="T:System.Security.Cryptography.RSACryptoServiceProvider"/></param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.Finalize">
            <summary>
            Destructs the <see cref="T:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.Dispose">
            <summary>
            Releases all resources used by the current instance of the <see cref="T:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.SignData(System.Byte[],System.Object)">
            <summary>
            Computes the hash value of the specified byte array using the specified hash algorithm, and signs the resulting hash value.
            </summary>
            <param name="signingInput">The input byte array for which to compute the hash.</param>
            <param name="hash">The hash algorithm to use to create the hash value. </param>
            <returns>The <see cref="T:System.Security.Cryptography.RSA"/> Signature for the specified data.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.VerifyData(System.Byte[],System.Object,System.Byte[])">
            <summary>
            Verifies that a digital signature is valid by determining the hash value in the signature using the provided public key and comparing it to the hash value of the provided data.
            </summary>
            <param name="signingInput">The input byte array.</param>
            <param name="hash">The hash algorithm to use to create the hash value.</param>
            <param name="signature">The signature byte array to be verified.</param>
            <returns>true if the signature is valid; otherwise, false.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey">
            <summary>
            Represents a Rsa security key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaSecurityKey.#ctor(System.Security.Cryptography.RSAParameters)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> class.
            </summary>
            <param name="rsaParameters"><see cref="T:System.Security.Cryptography.RSAParameters"/></param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaSecurityKey.#ctor(System.Security.Cryptography.RSA)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> class.
            </summary>
            <param name="rsa"><see cref="T:System.Security.Cryptography.RSA"/></param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.KeySize">
            <summary>
            Gets RSA key size.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.Parameters">
            <summary>
            <see cref="T:System.Security.Cryptography.RSAParameters"/> used to initialize the key.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.Rsa">
            <summary>
            <see cref="T:System.Security.Cryptography.RSA"/> instance used to initialize the key.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityAlgorithms">
            <summary>
            Constants for Security Algorithm.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.EcdsaSha256">
            see: http://tools.ietf.org/html/rfc7518#section-3
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaPKCS1">
            see: https://tools.ietf.org/html/rfc7518#section-4.1
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityKey">
            <summary>
            Base class for Security Key.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeySize">
            <summary>
            This must be overridden to get the size of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeyId">
            <summary>
            Gets the key id of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityKey.CryptoProviderFactory">
            <summary>
            Gets or sets <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/>.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityKeyIdentifierClause">
            <summary>
            Contains information about the keys inside the tokens.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityToken">
            <summary>
            Base class for security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.Id">
            <summary>
            This must be overridden to get the Id of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.Issuer">
            <summary>
            This must be overridden to get the issuer of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey">
            <summary>
            This must be overridden to get the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.SigningKey">
            <summary>
            This must be overridden to get or set the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey"/> that signed this instance.
            </summary>
            <remarks><see cref="T:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator"/>.ValidateToken(...) can this value when a <see cref="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey"/> is used to successfully validate a signature.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.ValidFrom">
            <summary>
            This must be overridden to get the time when this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> was Valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.ValidTo">
            <summary>
            This must be overridden to get the time when this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> is no longer Valid.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor">
            <summary>
            Contains some information which used to create a security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Audience">
            <summary>
            Gets or sets the value of the 'audience' claim.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.EncryptingCredentials">
            <summary>
            Gets or sets the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.EncryptingCredentials"/> used to create a encrypted security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Expires">
            <summary>
            Gets or sets the value of the 'expiration' claim.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Issuer">
            <summary>
            Gets or sets the issuer of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.IssuedAt">
            <summary>
            Gets or sets the time the security token was issued.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.NotBefore">
            <summary>
            Gets or sets the notbefore time for the security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.SigningCredentials">
            <summary>
            Gets or sets the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.SigningCredentials"/> used to create a security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Subject">
            <summary>
            Gets or sets the <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenHandler">
            <summary>
            Defines the interface for a Security Token Handler.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.#ctor">
            <summary>
            Creates an instance of <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenHandler"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CreateSecurityTokenReference(Microsoft.IdentityModel.Tokens.SecurityToken,System.Boolean)">
            <summary>
            Returns <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>.
            </summary>
            <param name="token"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/></param>
            <param name="attached">true if attached; otherwise, false.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CreateToken(Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor)">
            <summary>
            Returns <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="tokenDescriptor"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor"/></param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CanValidateToken">
            <summary>
            Gets a value indicating whether this handler supports validation of tokens 
            handled by this instance.
            </summary>v
            <returns>'True' if the instance is capable of SecurityToken
            validation.</returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CanWriteToken">
            <summary>
            Gets a value indicating whether the class provides serialization functionality to serialize token handled 
            by this instance.
            </summary>
            <returns>true if the WriteToken method can serialize this token.</returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.TokenType">
            <summary>
            This must be overridden to get the System.Type of the SecurityToken this instance handles.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CanReadToken(System.String)">
            <summary>
            Indicates whether the current token string can be read as a token 
            of the type handled by this instance.
            </summary>
            <param name="tokenString">The token string thats needs to be read.</param>
            <returns>'True' if the ReadToken method can parse the token string.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.ReadToken(System.String)">
            <summary>
            Deserializes from string a token of the type handled by this instance.
            </summary>
            <param name="tokenString">The string to be deserialized.</param>
            <returns>SecurityToken instance which represents the serialized token.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.ReadToken(System.Xml.XmlReader)">
            <summary>
            Gets security token.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/>.</param>
            <remarks>SecurityToken instance which represents the serialized token.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.WriteToken(Microsoft.IdentityModel.Tokens.SecurityToken)">
            <summary>
            Serializes to string a token of the type handled by this instance.
            </summary>
            <param name="token">A token of type TokenType.</param>
            <returns>The serialized token.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.WriteToken(System.Xml.XmlWriter,Microsoft.IdentityModel.Tokens.SecurityToken)">
            <summary>
            This must be overridden to serialize to XML a token of the type handled by this instance.
            </summary>
            <param name="writer">The XML writer.</param>
            <param name="token">A token of type <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.TokenType"/>.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.ReadToken(System.Xml.XmlReader,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            This must be overridden to deserialize token with the provided <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/>.</param>
            <param name="validationParameters">the current <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.</param>
            <remarks>SecurityToken instance which represents the serialized token.</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SignatureProvider">
            <summary>
            Provides signature services, signing and verifying.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> class used to create and verify signatures.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Algorithm">
            <summary>
            Gets the signature algorithm.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Sign(System.Byte[])">
            <summary>
            This must be overridden to produce a signature over the 'input'.
            </summary>
            <param name="input">bytes to sign.</param>
            <returns>signed bytes</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            This must be overridden to verify a signature created over the 'input'.
            </summary>
            <param name="input">bytes to verify.</param>
            <param name="signature">signature to compare against.</param>
            <returns>true if the computed signature matches the signature parameter, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose">
            <summary>
            Calls <see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)"/> and <see cref="M:System.GC.SuppressFinalize(System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)">
            <summary>
            Can be over written in descendants to dispose of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer</param>     
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SigningCredentials">
            <summary>
            A wrapper class for properties that are used for signature valdiation.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SigningCredentials.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SigningCredentials"/> class.
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/></param>
            <param name="algorithm">The signature algorithm to apply.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.Algorithm">
            <summary>
            Gets the algorithm which used for signature valdiation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.CryptoProviderFactory">
            <summary>
            Users can override the default <see cref="P:Microsoft.IdentityModel.Tokens.SigningCredentials.CryptoProviderFactory"/> with this property. This factory will be used for creating signature providers.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> which used for signature valdiation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.Kid">
            <summary>
            Gets the key id which used for signature valdiation.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey">
            <summary>
            Represents a symmetric security key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.#ctor(System.Byte[])">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> instance.
            </summary>
            <param name="key">The byte array of the key.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.KeySize">
            <summary>
            Gets the key size.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.Key">
            <summary>
            Gets the byte array of the key.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider">
            <summary>
            Provides signing and verifying operations using a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and specifying an algorithm.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.DefaultMinimumSymmetricKeySizeInBits">
            <summary>
            This is the minimum <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.KeySize when creating and verifying signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider"/> class that uses an <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to create and / or verify signatures over a array of bytes.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.</param>
            <param name="algorithm">The signature algorithm to use.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentException">If <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">'<see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.KeySize' is smaller than <see cref="P:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.MinimumSymmetricKeySizeInBits"/>.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.GetKeyedHashAlgorithm(System.Byte[],System.String)"/> throws.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.GetKeyedHashAlgorithm(System.Byte[],System.String)"/> returns null.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.MinimumSymmetricKeySizeInBits">
            <summary>
            Gets or sets the minimum <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.KeySize"/>.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' is smaller than <see cref="F:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.DefaultMinimumSymmetricKeySizeInBits"/>.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.GetKeyBytes(Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Called to obtain the byte[] needed to create a <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>that will be used to obtain the byte[].</param>
            <returns><see cref="T:System.Byte"/>[] that is used to populated the KeyedHashAlgorithm.</returns>
            <exception cref="T:System.ArgumentNullException">if key is null.</exception>
            <exception cref="T:System.ArgumentException">if a byte[] can not be obtained from SecurityKey.</exception>
            <remarks><see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> are supported.
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> .Key is returned</para>
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>Base64UrlEncoder.DecodeBytes is called with <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.K"/> if <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kty"/> == JsonWebAlgorithmsKeyTypes.Octet</para>
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.GetKeyedHashAlgorithm(System.Byte[],System.String)">
            <summary>
            Returns the <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>.
            </summary>
            <param name="algorithm">The hash algorithm to use to create the hash value.</param>
            <param name="keyBytes">The byte array of the key.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Sign(System.Byte[])">
            <summary>
            Produces a signature over the 'input' using the <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)"/>.
            </summary>
            <param name="input">The bytes to sign.</param>
            <returns>Signed bytes</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null. </exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            Verifies that a signature created over the 'input' matches the signature. Using <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)"/>.
            </summary>
            <param name="input">The bytes to verify.</param>
            <param name="signature">signature to compare against.</param>
            <returns>true if computed signature matches the signature parameter, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Verify(System.Byte[],System.Byte[],System.Int32)">
            <summary>
            Verifies that a signature created over the 'input' matches the signature. Using <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)"/>.
            </summary>
            <param name="input">The bytes to verify.</param>
            <param name="signature">signature to compare against.</param>
            <param name="length">number of bytes of signature to use.</param>
            <returns>true if computed signature matches the signature parameter, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0. </exception>
            <exception cref="T:System.ArgumentException">'length &lt; 1'</exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)">
            <summary>
            Disposes of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AudienceValidator">
            <summary>
            Definition for AudienceValidator.
            </summary>
            <param name="audiences">The audiences found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>true if the audience is considered valid.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolver">
            <summary>
            Definition for IssuerSigningKeyResolver.
            </summary>
            <param name="token">The <see cref="T:System.String"/> representation of the token that is being validated.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated. It may be null.</param>
            <param name="kid">A key identifier. It may be null.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use when validating a signature.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyValidator">
            <summary>
            Definition for IssuerSigningKeyValidator.
            </summary>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerValidator">
            <summary>
            Definition for IssuerValidator.
            </summary>
            <param name="issuer">The issuer to validate.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <remarks>The delegate should return a non null string that represents the 'issuer'. If null a default value will be used.</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.LifetimeValidator">
            <summary>
            Definition for LifetimeValidator.
            </summary>
            <param name="notBefore">The 'notBefore' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="expires">The 'expiration' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SignatureValidator">
            <summary>
            Definition for SignatureValidator.
            </summary>
            <param name="token">A securityToken with a signature.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenDecryptionKeyResolver">
            <summary>
            Definition for TokenDecryptionKeyResolver.
            </summary>
            <param name="token">The <see cref="T:System.String"/> representation of the token to be decrypted.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> to be decrypted. The runtime by default passes null.</param>
            <param name="kid">A key identifier. It may be null.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use when decrypting the token.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters">
            <summary>
            Contains a set of parameters that are used by a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenHandler"/> when validating a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenValidationParameters.DefaultAuthenticationType">
            <summary>
            This is the fallback authenticationtype that a <see cref="T:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator"/> will use if nothing is set.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenValidationParameters.DefaultClockSkew">
            <summary>
            Default for the clock skew.
            </summary>
            <remarks>300 seconds (5 minutes).</remarks>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenValidationParameters.DefaultMaximumTokenSizeInBytes">
            <summary>
            Default for the maximm token size.
            </summary>
            <remarks>2 MB (mega bytes).</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.#ctor(Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Copy constructor for <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> class.
            </summary>        
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ActorValidationParameters">
            <summary>
            Gets or sets <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AudienceValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the audience.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the 'audience' instead of normal processing.
            If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience"/> is false, this delegate will not be called.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AuthenticationType">
            <summary>
            Gets or sets the AuthenticationType when creating a <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
            <exception cref="T:System.ArgumentNullException">If 'value' is null or whitespace.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ClockSkew">
            <summary>
            Gets or sets the clock skew to apply when validating a time.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">If 'value' is less than 0.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.Clone">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> with values copied from this object.
            </summary>
            <returns>A new <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> object copied from this object</returns>
            <remarks>This is a shallow Clone.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.CreateClaimsIdentity(Microsoft.IdentityModel.Tokens.SecurityToken,System.String)">
            <summary>
            Creates a <see cref="T:System.Security.Claims.ClaimsIdentity"/> using:
            <para><see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AuthenticationType"/></para>
            <para>'NameClaimType': If NameClaimTypeRetriever is set, call delegate, else call NameClaimType. If the result is a null or empty string, use <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultNameClaimType"/></para>.
            <para>'RoleClaimType': If RoleClaimTypeRetriever is set, call delegate, else call RoleClaimType. If the result is a null or empty string, use <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultRoleClaimType"/></para>.
            </summary>
            <returns>A <see cref="T:System.Security.Claims.ClaimsIdentity"/> with Authentication, NameClaimType and RoleClaimType set.</returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.CryptoProviderFactory">
            <summary>
            Users can override the default <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.CryptoProviderFactory"/> with this property. This factory will be used for creating signature providers.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenDecryptionKey">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is to be used for decryption.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenDecryptionKeyResolver">
            <summary>
            Gets or sets a delegate that will be called to retreive a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used for decryption.
            </summary>
            <remarks>
            This <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> will be used to decrypt the token. This can be helpful when the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> does not contain a key identifier.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidator">
            <summary>
            Gets or sets a delegate for validating the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the token.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the token, instead of normal processing.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKey">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is to be used for signature validation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolver">
            <summary>
            Gets or sets a delegate that will be called to retreive a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used for signature validation.
            </summary>
            <remarks>
            This <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> will be used to check the signature. This can be helpful when the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> does not contain a key identifier.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeys">
            <summary>
            Gets or sets an <see cref="T:System.Collections.Generic.IEnumerable`1"/> used for signature validation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the issuer of the token.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the 'issuer' of the token, instead of normal processing.
            If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is false, this delegate will not be called.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.LifetimeValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the lifetime of the token
            </summary>
            <remarks>
            If set, this delegate will be called to validate the lifetime of the token, instead of normal processing.
            If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateLifetime"/> is false, this delegate will not be called.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.NameClaimType">
            <summary>
            Gets or sets a <see cref="T:System.String"/> that defines the <see cref="P:System.Security.Claims.ClaimsIdentity.NameClaimType"/>.
            </summary>
            <remarks>
            Controls the value <see cref="P:System.Security.Claims.ClaimsIdentity.Name"/> returns. It will return the first <see cref="P:System.Security.Claims.Claim.Value"/> where the <see cref="P:System.Security.Claims.Claim.Type"/> equals <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.NameClaimType"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RoleClaimType">
            <summary>
            Gets or sets the <see cref="T:System.String"/> that defines the <see cref="P:System.Security.Claims.ClaimsIdentity.RoleClaimType"/>.
            </summary>
            <remarks>
            <para>Controls the results of <see cref="M:System.Security.Claims.ClaimsPrincipal.IsInRole(System.String)"/>.</para>
            <para>Each <see cref="T:System.Security.Claims.Claim"/> where <see cref="P:System.Security.Claims.Claim.Type"/> == <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RoleClaimType"/> will be checked for a match against the 'string' passed to <see cref="M:System.Security.Claims.ClaimsPrincipal.IsInRole(System.String)"/>.</para>
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.NameClaimTypeRetriever">
            <summary>
            Gets or sets a delegate that will be called to obtain the NameClaimType to use when creating a ClaimsIdentity
            after validating a token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RequireExpirationTime">
            <summary>
            Gets or sets a value indicating whether tokens must have an 'expiration' value.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RequireSignedTokens">
            <summary>
            Gets or sets a value indicating whether a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> can be considered valid if not signed.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RoleClaimTypeRetriever">
            <summary>
            Gets or sets a delegate that will be called to obtain the RoleClaimType to use when creating a ClaimsIdentity
            after validating a token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.SaveSigninToken">
            <summary>
            Gets or sets a boolean to control if the original token should be saved after the security token is validated.
            </summary>
            <remarks>The runtime will consult this value and save the orginal token that was validated.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.SignatureValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the signature of the token.
            </summary>
            <remarks>
            If set, this delegate will be called to signature of the token, instead of normal processing.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenDecryptionKeys">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that is to be used for decrypting inbound tokens.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache">
            <summary>
            Gets or set the <see cref="T:Microsoft.IdentityModel.Tokens.ITokenReplayCache"/> that store tokens that can be checked to help detect token replay.
            </summary>
            <remarks>If set, then tokens must have an expriation time or the runtime will fault.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateActor">
            <summary>
            Gets or sets a value indicating if an actor token is detected, whether it should be validated.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience">
            <summary>
            Gets or sets a boolean to control if the audience will be validated during token validation.
            </summary>
            <remarks>Validation of the audience, mitigates forwarding attacks. For example, a site that receives a token, could not replay it to another side.
            A forwarded token would contain the audience of the original site.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer">
            <summary>
            Gets or sets a boolean to control if the issuer will be validated during token validation.
            </summary>
            <remarks>
            Validation of the issuer mitigates forwarding attacks that can occur when an
            IdentityProvider represents multiple tenants and signs tokens with the same keys.
            It is possible that a token issued for the same audience could be from a different tenant. For example an application could accept users from
            contoso.onmicrosoft.com but not fabrikam.onmicrosoft.com, both valid tenants. A application that accepts tokens from fabrikam could forward them
            to the application that accepts tokens for contoso.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateLifetime">
            <summary>
            Gets or sets a boolean to control if the lifetime will be validated during token validation.
            </summary>                
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuerSigningKey">
            <summary>
            Gets or sets a boolean that controls if validation of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the securityToken is called.
            </summary>
            <remarks>It is possible for tokens to contain the public key needed to check the signature. For example, X509Data can be hydrated into an X509Certificate,
            which can be used to validate the signature. In these cases it is important to validate the SigningKey that was used to validate the signature. </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudience">
            <summary>
            Gets or sets a string that represents a valid audience that will be used to check against the token's audience.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that contains valid audiences that will be used to check against the token's audience.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer">
            <summary>
            Gets or sets a <see cref="T:System.String"/> that represents a valid issuer that will be used to check against the token's issuer.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that contains valid issuers that will be used to check against the token's issuer.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Utility">
            <summary>
            Contains some utility methods.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.Utility.Empty">
            <summary>
            A string with "empty" value.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.Utility.Null">
            <summary>
            A string with "null" value.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.CloneByteArray(System.Byte[])">
            <summary>
            Creates a copy of the byte array.
            </summary>
            <param name="src">The resource array.</param>
            <returns>A copy of the byte array.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.SerializeAsSingleCommaDelimitedString(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Serializes the list of strings into string as follows:
            'str1','str2','str3' ...
            </summary>
            <param name="strings">
            The strings used to build a comma delimited string.
            </param>
            <returns>
            The single <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.IsHttps(System.String)">
            <summary>
            Returns whether the input string is https.
            </summary>
            <param name="address">The input string.</param>
            <remarks>true if the input string is https; otherwise, false.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.IsHttps(System.Uri)">
            <summary>
            Returns whether the input uri is https.
            </summary>
            <param name="uri"><see cref="T:System.Uri"/>.</param>
            <returns>true if the input uri is https; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.AreEqual(System.Byte[],System.Byte[])">
            <summary>
            Compares two byte arrays for equality. Hash size is fixed normally it is 32 bytes.
            The attempt here is to take the same time if an attacker shortens the signature OR changes some of the signed contents.
            </summary>
            <param name="a">
            One set of bytes to compare.
            </param>
            <param name="b">
            The other set of bytes to compare with.
            </param>
            <returns>
            true if the bytes are equal, false otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.AreEqual(System.Byte[],System.Byte[],System.Int32)">
            <summary>
            Compares two byte arrays for equality. Hash size is fixed normally it is 32 bytes.
            The attempt here is to take the same time if an attacker shortens the signature OR changes some of the signed contents.
            </summary>
            <param name="a">
            One set of bytes to compare.
            </param>
            <param name="b">
            The other set of bytes to compare with.
            </param>
            <param name="length">length of array to check</param>
            <returns>
            true if the bytes are equal, false otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Validators">
            <summary>
            AudienceValidator
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateAudience(System.Collections.Generic.IEnumerable{System.String},Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if the audiences found in a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> are valid.
            </summary>
            <param name="audiences">The audiences found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If 'vaidationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException">If 'audiences' is null and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience"/> is true.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudience"/> is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences"/> is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException">If none of the 'audiences' matched either <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudience"/> or one of <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuer(System.String,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if an issuer found in a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> is valid.
            </summary>
            <param name="issuer">The issuer to validate</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <exception cref="T:System.ArgumentNullException">If 'vaidationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException">If 'issuer' is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is true.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/> is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If 'issuer' failed to matched either <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> or one of <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuerSecurityKey(Microsoft.IdentityModel.Tokens.SecurityKey,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException"> if 'securityKey' is null and ValidateIssuerSigningKey is true.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'securityToken' is null and ValidateIssuerSigningKey is true.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'vaidationParameters' is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(System.Nullable{System.DateTime},System.Nullable{System.DateTime},Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the lifetime of a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="notBefore">The 'notBefore' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="expires">The 'expiration' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If 'vaidationParameters' is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException">If 'expires.HasValue' is false and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RequireExpirationTime"/> is true.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException">If 'notBefore' is &gt; 'expires'.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException">If 'notBefore' is &gt; DateTime.UtcNow.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException">If 'expires' is &lt; DateTime.UtcNow.</exception>
            <remarks>All time comparisons apply <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ClockSkew"/>.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateTokenReplay(System.String,System.Nullable{System.DateTime},Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates if a token has been replayed.
            </summary>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="expirationTime">When does the security token expire.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If 'securityToken' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null or whitespace.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/> is not null and expirationTime.HasValue is false. When a TokenReplayCache is set, tokens require an expiration time.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException">If the 'securityToken' is found in the cache.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException">If the 'securityToken' could not be added to the <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/>.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.X509SecurityKey">
            <summary>
            Security key that allows access to cert
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Instantiates a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> using a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>
            </summary>
            <param name="certificate">The cert to use.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.KeySize">
            <summary>
            Gets the key size.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.X5t">
            <summary>
            Gets the X5t of this <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.PrivateKey">
            <summary>
            Returns the private key from the <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.PublicKey">
            <summary>
            Gets the public key from the <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.Certificate">
            <summary>
            Gets the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.Equals(System.Object)">
            <summary>
            Returns a bool indicating if this key is equivalent to another key.
            </summary>
            <return>true if the keys are equal; otherwise, false.</return>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.GetHashCode">
            <summary>
            Returns an int hash code.
            </summary>
            <return>An int hash code</return>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider">
            <summary>
            Provides authenticated encryption and decryption services.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/> class used for encryption and decryption.
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for crypto operations.</param>
            <param name="algorithm">The encryption algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">key size is not large enough.</exception>
            <exception cref="T:System.ArgumentException">'algorithm' is not supported.</exception>
            <exception cref="T:System.ArgumentException">a symmetricSignatureProvider is not created.</exception>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Algorithm">
            <summary>
            Gets the encryption algorithm that is being used.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/>.
            </summary>
            <remarks>This is null by default. This can be used by runtimes or for extensibility scenarios.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is being used.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[])">
            <summary>
            Encrypts the 'plaintext'
            </summary>
            <param name="plaintext">the data to be encrypted.</param>
            <param name="authenticatedData">will be combined with iv and ciphertext to create an authenticationtag.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult"/>containing ciphertext, iv, authenticationtag.</returns>
            <exception cref="T:System.ArgumentNullException">plaintext is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">authenticationData is null or empty.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException">AES crypto operation threw. See inner exception for details.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Encrypts the 'plaintext'
            </summary>
            <param name="plaintext">the data to be encrypted.</param>
            <param name="authenticatedData">will be combined with iv and ciphertext to create an authenticationtag.</param>
            <param name="iv">initialization vector for encryption.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult"/>containing ciphertext, iv, authenticationtag.</returns>
            <exception cref="T:System.ArgumentNullException">plaintext is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">authenticationData is null or empty.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException">AES crypto operation threw. See inner exception for details.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Decrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Decrypts ciphertext into plaintext
            </summary>
            <param name="ciphertext">the encrypted text to decrypt.</param>
            <param name="authenticatedData">the authenticateData that is used in verification.</param>
            <param name="iv">the initialization vector used when creating the ciphertext.</param>
            <param name="authenticationTag">the authenticationTag that was created during the encyption.</param>
            <returns>decrypted ciphertext</returns>
            <exception cref="T:System.ArgumentNullException">'ciphertext' is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">'authenticatedData' is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">'iv' is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">'authenticationTag' is null or empty.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException">signature over authenticationTag fails to verify.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException">AES crypto operation threw. See inner exception.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.IsSupportedAlgorithm(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Checks if an 'key, algorithm' pair is supported
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/></param>
            <param name="algorithm">the algorithm to check.</param>
            <returns>true if 'key, algorithm' pair is supported.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.GetKeyBytes(Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Called to obtain the byte[] needed to create a <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>that will be used to obtain the byte[].</param>
            <returns><see cref="T:System.Byte"/>[] that is used to populated the KeyedHashAlgorithm.</returns>
            <exception cref="T:System.ArgumentNullException">if key is null.</exception>
            <exception cref="T:System.ArgumentException">if a byte[] can not be obtained from SecurityKey.</exception>
            <remarks><see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> are supported.
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> .Key is returned</para>
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>Base64UrlEncoder.DecodeBytes is called with <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.K"/> if <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kty"/> == JsonWebAlgorithmsKeyTypes.Octet</para>
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.ValidateKeySize(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Checks that the key has sufficient length
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that contains bytes.</param>
            <param name="algorithm">the algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">if 'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'algorithm' is null or empty.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult">
            <summary>
            Contains the results of <see cref="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[])"/> operation.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Initializes a new <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult"/>
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used during <see cref="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[])"/></param>
            <param name="ciphertext">protected text.</param>
            <param name="iv">the initialization vector used.</param>
            <param name="authenticationTag">the bytes that need be passed to <see cref="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Decrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[])"/>.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.Ciphertext">
            <summary>
            Gets the Ciphertext.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.IV">
            <summary>
            Gets the initialization vector.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.AuthenticationTag">
            <summary>
            Gets the authentication tag
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException">
            <summary>
            Represents a security token exception when decryption failed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException">
            <summary>
            Represents a security token exception when encryption failed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException">
            <summary>
            This exception is thrown when a security token contained a key identifier but the key was not found by the runtime 
            when decrypting a token.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenException">
            <summary>
            Represents a security token exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException">
            <summary>
            Throw this exception when a received Security Token has expiration time in the past.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.Expires">
            <summary>
            Gets or sets the Expires value that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException"/>
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException">
            <summary>
            This exception is thrown when 'audience' of a token was not valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.InvalidAudience">
            <summary>
            Gets or sets the InvalidAudience that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">
            <summary>
            This exception is thrown when 'issuer' of a token was not valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.InvalidIssuer">
            <summary>
            Gets or sets the InvalidIssuer that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException">
            <summary>
            This exception is thrown when 'lifetime' of a token was not valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.NotBefore">
            <summary>
            Gets or sets the NotBefore value that created the validation exception.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.Expires">
            <summary>
            Gets or sets the Expires value that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException">
            <summary>
            This exception is thrown when 'signature' of a token was not valid.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException">
            <summary>
            Throw this exception when a received Security Token has an invalid issuer signing key.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.SigningKey">
            <summary>
            Gets or sets the SigningKey that was found invalid.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException"/>
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException">
            <summary>
            This exception is thrown when a security is missing an ExpirationTime.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException">
            <summary>
            Throw this exception when a received Security token has an effective time 
            in the future.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.NotBefore">
            <summary>
            Gets or sets the NotBefore value that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException"/>
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException">
            <summary>
            This exception is thrown when an add to the TokenReplayCache fails.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException">
            <summary>
            Throw this exception when a received Security Token has been replayed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException"/>
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException">
            <summary>
            This exception is thrown when a security token contained a key identifier but the key was not found by the runtime.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException">
            <summary>
            Represents a security token validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
    </members>
</doc>
