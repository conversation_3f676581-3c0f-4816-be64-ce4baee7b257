using System;
using System.Configuration;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using StackExchange.Redis;
using Telerik.Reporting.Cache;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Redis-based storage provider for Telerik Reporting that uses a separate Redis instance
    /// This prevents large report data from overwhelming the main session Redis store
    /// </summary>
    public class RedisReportStorage : IStorage
    {
        private readonly IDatabase _database;
        private readonly ConnectionMultiplexer _redis;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromHours(2);
        private readonly string _keyPrefix = "TelerikReport:";

        public RedisReportStorage()
        {
            var connectionString = GetReportRedisConnectionString();
            _redis = ConnectionMultiplexer.Connect(connectionString);
            _database = _redis.GetDatabase();
        }

        public RedisReportStorage(string connectionString)
        {
            _redis = ConnectionMultiplexer.Connect(connectionString);
            _database = _redis.GetDatabase();
        }

        public bool Contains(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                return _database.KeyExists(redisKey);
            }
            catch (Exception ex)
            {
                LogError($"Error checking if key exists: {key}", ex);
                return false;
            }
        }

        public object GetValue(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                var value = _database.StringGet(redisKey);
                
                if (value.HasValue)
                {
                    // Update expiration on access (sliding expiration)
                    _database.KeyExpire(redisKey, _defaultExpiration);
                    return DeserializeObject(value);
                }
            }
            catch (Exception ex)
            {
                LogError($"Error retrieving value for key: {key}", ex);
            }
            return null;
        }

        public void SetValue(string key, object value)
        {
            SetValue(key, value, _defaultExpiration);
        }

        public void SetValue(string key, object value, TimeSpan timeout)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                var serializedValue = SerializeObject(value);
                _database.StringSet(redisKey, serializedValue, timeout);
            }
            catch (Exception ex)
            {
                LogError($"Error setting value for key: {key}", ex);
                throw;
            }
        }

        public bool Remove(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                return _database.KeyDelete(redisKey);
            }
            catch (Exception ex)
            {
                LogError($"Error removing key: {key}", ex);
                return false;
            }
        }

        public void Clear()
        {
            try
            {
                // Get all keys with our prefix and delete them
                var server = _redis.GetServer(_redis.GetEndPoints()[0]);
                var keys = server.Keys(pattern: _keyPrefix + "*");
                
                foreach (var key in keys)
                {
                    _database.KeyDelete(key);
                }
            }
            catch (Exception ex)
            {
                LogError("Error clearing storage", ex);
            }
        }

        /// <summary>
        /// Get memory usage statistics for monitoring
        /// </summary>
        public RedisMemoryInfo GetMemoryInfo()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints()[0]);
                var info = server.Info("memory");
                
                var memoryInfo = new RedisMemoryInfo();
                foreach (var section in info)
                {
                    foreach (var item in section)
                    {
                        switch (item.Key.ToLower())
                        {
                            case "used_memory":
                                long.TryParse(item.Value, out memoryInfo.UsedMemory);
                                break;
                            case "used_memory_human":
                                memoryInfo.UsedMemoryHuman = item.Value;
                                break;
                            case "maxmemory":
                                long.TryParse(item.Value, out memoryInfo.MaxMemory);
                                break;
                        }
                    }
                }
                return memoryInfo;
            }
            catch (Exception ex)
            {
                LogError("Error getting memory info", ex);
                return new RedisMemoryInfo();
            }
        }

        private string GetRedisKey(string key)
        {
            return _keyPrefix + key;
        }

        private string GetReportRedisConnectionString()
        {
            // Try to get dedicated report Redis connection string first
            var reportRedisConnection = ConfigurationManager.AppSettings["SWReportRedisConnection"];
            if (!string.IsNullOrEmpty(reportRedisConnection))
            {
                return reportRedisConnection;
            }

            // Fall back to main Redis connection but with different database
            var mainRedisConnection = ConfigurationManager.AppSettings["SWRedisConnection"];
            if (!string.IsNullOrEmpty(mainRedisConnection))
            {
                // Use database 1 for reports (default session uses database 0)
                return mainRedisConnection + ",defaultDatabase=1";
            }

            throw new ConfigurationErrorsException("No Redis connection string found for report storage");
        }

        private byte[] SerializeObject(object obj)
        {
            if (obj == null) return null;
            
            using (var memoryStream = new MemoryStream())
            {
                var formatter = new BinaryFormatter();
                formatter.Serialize(memoryStream, obj);
                return memoryStream.ToArray();
            }
        }

        private object DeserializeObject(byte[] data)
        {
            if (data == null || data.Length == 0) return null;
            
            using (var memoryStream = new MemoryStream(data))
            {
                var formatter = new BinaryFormatter();
                return formatter.Deserialize(memoryStream);
            }
        }

        private void LogError(string message, Exception ex)
        {
            try
            {
                var log = log4net.LogManager.GetLogger(typeof(RedisReportStorage));
                log.Error($"RedisReportStorage: {message}", ex);
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"RedisReportStorage Error: {message} - {ex}");
            }
        }

        private void LogInfo(string message)
        {
            try
            {
                var log = log4net.LogManager.GetLogger(typeof(RedisReportStorage));
                log.Info($"RedisReportStorage: {message}");
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"RedisReportStorage Info: {message}");
            }
        }

        public void Dispose()
        {
            _redis?.Dispose();
        }
    }

    public class RedisMemoryInfo
    {
        public long UsedMemory { get; set; }
        public string UsedMemoryHuman { get; set; }
        public long MaxMemory { get; set; }
        
        public double MemoryUsagePercentage => MaxMemory > 0 ? (double)UsedMemory / MaxMemory * 100 : 0;
    }
}
