using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using StackExchange.Redis;
using Telerik.Reporting.Cache.Interfaces;

namespace SETWorksREPORTS.Cache
{
    /// <summary>
    /// Custom Redis storage for Telerik Reporting that uses a separate Redis database
    /// This prevents Telerik report cache from overwhelming the main session Redis
    /// </summary>
    public class TelerikReportRedisStorage : IStorage
    {
        private IDatabase _database;
        private ConnectionMultiplexer _redis;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromHours(2);
        private readonly string _keyPrefix = "TelerikReport:";
        private readonly string _lockPrefix = "TelerikLock:";
        private readonly string _setPrefix = "TelerikSet:";
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(TelerikReportRedisStorage));

        public TelerikReportRedisStorage()
        {
            try
            {
                Log.Info("TelerikReportRedisStorage constructor called");
                var connectionString = GetReportRedisConnectionString();
                Log.Info($"Attempting to connect to Redis with connection string: {connectionString}");

                var options = ConfigurationOptions.Parse(connectionString);
                options.AbortOnConnectFail = false; // Don't fail immediately if Redis is temporarily unavailable

                _redis = ConnectionMultiplexer.Connect(options);
                _database = _redis.GetDatabase();

                Log.Info("TelerikReportRedisStorage initialized successfully");
            }
            catch (Exception ex)
            {
                Log.Error("Failed to initialize TelerikReportRedisStorage", ex);
                // Don't throw - just log the error and continue
                // This will allow Telerik to instantiate the class even if Redis is unavailable
            }
        }

        // Legacy methods (for backward compatibility)
        public bool Contains(string key)
        {
            return Exists(key);
        }

        public object GetValue(string key)
        {
            var bytes = GetBytes(key);
            return bytes != null ? DeserializeObject(bytes) : null;
        }

        public void SetValue(string key, object value)
        {
            var bytes = SerializeObject(value);
            SetBytes(key, bytes);
        }

        public void SetValue(string key, object value, TimeSpan timeout)
        {
            var bytes = SerializeObject(value);
            SetBytes(key, bytes, timeout);
        }

        public void Remove(string key)
        {
            Delete(key);
        }

        // Helper methods for serialization
        private byte[] SerializeObject(object obj)
        {
            if (obj == null) return null;
            
            try
            {
                using (var stream = new MemoryStream())
                {
                    var formatter = new BinaryFormatter();
                    formatter.Serialize(stream, obj);
                    return stream.ToArray();
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error serializing object: {ex.Message}", ex);
                return null;
            }
        }

        private object DeserializeObject(byte[] data)
        {
            if (data == null || data.Length == 0) return null;
            
            try
            {
                using (var stream = new MemoryStream(data))
                {
                    var formatter = new BinaryFormatter();
                    return formatter.Deserialize(stream);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error deserializing object: {ex.Message}", ex);
                return null;
            }
        }

        // Required IStorage interface methods for version 12.1.18.620
        public IDisposable AcquireLock(string key)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot acquire lock for key: " + key);
                    return null;
                }
                var lockKey = _lockPrefix + key;
                var lockValue = Environment.MachineName + "_" + DateTime.UtcNow.Ticks;
                var acquired = _database.StringSet(lockKey, lockValue, TimeSpan.FromMinutes(5), When.NotExists);
                if (acquired)
                {
                    Log.Debug($"Lock acquired for key: {key}");
                    return new RedisLock(_database, lockKey);
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.Error($"Error acquiring lock for key: {key}", ex);
                return null;
            }
        }

        public bool Exists(string key)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, returning false for key exists: " + key);
                    return false;
                }
                var fullKey = GetFullKey(key);
                return _database.KeyExists(fullKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error checking if key exists: {key}", ex);
                return false;
            }
        }

        public void SetString(string key, string value)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot set string for key: " + key);
                    return;
                }
                var fullKey = GetFullKey(key);
                _database.StringSet(fullKey, value, _defaultExpiration);
                Log.Debug($"Set string value for key: {key}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error setting string for key: {key}", ex);
            }
        }

        public void SetBytes(string key, byte[] value)
        {
            SetBytes(key, value, _defaultExpiration);
        }

        public void SetBytes(string key, byte[] value, TimeSpan expiration)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot set bytes for key: " + key);
                    return;
                }
                var fullKey = GetFullKey(key);
                _database.StringSet(fullKey, value, expiration);
                Log.Debug($"Set byte array value for key: {key}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error setting bytes for key: {key}", ex);
            }
        }

        public string GetString(string key)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, returning null for key: " + key);
                    return null;
                }
                var fullKey = GetFullKey(key);
                var value = _database.StringGet(fullKey);
                Log.Debug($"Retrieved string value for key: {key}");
                return value.HasValue ? value.ToString() : null;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting string for key: {key}", ex);
                return null;
            }
        }

        public byte[] GetBytes(string key)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, returning null for key: " + key);
                    return null;
                }
                var fullKey = GetFullKey(key);
                var value = _database.StringGet(fullKey);
                Log.Debug($"Retrieved byte array value for key: {key}");
                return value.HasValue ? value : null;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting bytes for key: {key}", ex);
                return null;
            }
        }

        public void Delete(string key)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot delete key: " + key);
                    return;
                }
                var fullKey = GetFullKey(key);
                _database.KeyDelete(fullKey);
                Log.Debug($"Deleted key: {key}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error deleting key: {key}", ex);
            }
        }

        public void AddToSet(string key, string value)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot add to set for key: " + key);
                    return;
                }
                var setKey = GetSetKey(key);
                _database.SetAdd(setKey, value);
                _database.KeyExpire(setKey, _defaultExpiration);
                Log.Debug($"Added value to set for key: {key}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error adding to set for key: {key}", ex);
            }
        }

        public void RemoveFromSet(string key, string value)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot remove from set for key: " + key);
                    return;
                }
                var setKey = GetSetKey(key);
                _database.SetRemove(setKey, value);
                Log.Debug($"Removed value from set for key: {key}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error removing from set for key: {key}", ex);
            }
        }

        public IEnumerable<string> GetSetMembers(string key)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, returning empty set for key: " + key);
                    return new string[0];
                }
                var setKey = GetSetKey(key);
                var members = _database.SetMembers(setKey);
                Log.Debug($"Retrieved set members for key: {key}");
                return members.Select(m => m.ToString()).ToArray();
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting set members for key: {key}", ex);
                return new string[0];
            }
        }

        public void Dispose()
        {
            try
            {
                _redis?.Dispose();
                Log.Info("TelerikReportRedisStorage disposed");
            }
            catch (Exception ex)
            {
                Log.Error("Error disposing TelerikReportRedisStorage", ex);
            }
        }

        // Helper methods
        private string GetFullKey(string key)
        {
            return _keyPrefix + key;
        }

        private string GetSetKey(string key)
        {
            return _setPrefix + key;
        }

        // Set-based operations required by IStorage interface
        public bool ExistsInSet(string setKey, string value)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, returning false for set exists check: " + setKey);
                    return false;
                }
                var fullSetKey = GetFullKey(_setPrefix + setKey);
                return _database.SetContains(fullSetKey, value);
            }
            catch (Exception ex)
            {
                Log.Error($"Error checking if value exists in set: {setKey}", ex);
                return false;
            }
        }

        public long GetCountInSet(string setKey)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, returning 0 for set count: " + setKey);
                    return 0;
                }
                var fullSetKey = GetFullKey(_setPrefix + setKey);
                return _database.SetLength(fullSetKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting count in set: {setKey}", ex);
                return 0;
            }
        }

        public string[] GetAllMembersInSet(string setKey)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, returning empty array for set members: " + setKey);
                    return new string[0];
                }
                var fullSetKey = GetFullKey(_setPrefix + setKey);
                var members = _database.SetMembers(fullSetKey);
                return Array.ConvertAll(members, m => (string)m);
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting all members in set: {setKey}", ex);
                return new string[0];
            }
        }

        public void AddInSet(string setKey, string value)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot add to set: " + setKey);
                    return;
                }
                var fullSetKey = GetFullKey(_setPrefix + setKey);
                _database.SetAdd(fullSetKey, value);
                _database.KeyExpire(fullSetKey, _defaultExpiration);
                Log.Debug($"Added value to set: {setKey}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error adding to set: {setKey}", ex);
            }
        }

        public void DeleteInSet(string setKey, string value)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot delete from set: " + setKey);
                    return;
                }
                var fullSetKey = GetFullKey(_setPrefix + setKey);
                _database.SetRemove(fullSetKey, value);
                Log.Debug($"Deleted value from set: {setKey}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error deleting from set: {setKey}", ex);
            }
        }

        public void DeleteSet(string setKey)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis not available, cannot delete set: " + setKey);
                    return;
                }
                var fullSetKey = GetFullKey(_setPrefix + setKey);
                _database.KeyDelete(fullSetKey);
                Log.Debug($"Deleted set: {setKey}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error deleting set: {setKey}", ex);
            }
        }

        private string GetReportRedisConnectionString()
        {
            // Try to get dedicated report Redis connection string first
            var reportRedisConnection = ConfigurationManager.AppSettings["TelerikReportRedisConnection"];
            if (!string.IsNullOrEmpty(reportRedisConnection))
            {
                Log.Info("Using dedicated Redis instance for Telerik reports");
                return reportRedisConnection;
            }

            // Fall back to main Redis connection but with different database
            var mainRedisConnection = ConfigurationManager.AppSettings["SWRedisConnection"];
            if (!string.IsNullOrEmpty(mainRedisConnection))
            {
                Log.Info("Using main Redis instance with separate database for Telerik reports");
                // Use database 1 for reports (default session uses database 0)
                return mainRedisConnection + ",defaultDatabase=1";
            }

            // Default fallback
            Log.Warn("No Redis connection string found, using localhost default");
            return "127.0.0.1:6379,defaultDatabase=1";
        }
    }

    /// <summary>
    /// Redis lock implementation for distributed locking
    /// </summary>
    internal class RedisLock : IDisposable
    {
        private readonly IDatabase _database;
        private readonly string _lockKey;
        private bool _disposed = false;

        public RedisLock(IDatabase database, string lockKey)
        {
            _database = database;
            _lockKey = lockKey;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    _database.KeyDelete(_lockKey);
                }
                catch
                {
                    // Ignore errors when releasing lock
                }
                _disposed = true;
            }
        }
    }
}
