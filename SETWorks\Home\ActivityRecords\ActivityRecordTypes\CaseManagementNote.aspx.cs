﻿using System;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;
using BOs;
using SW.UI;
using Telerik.Web.UI;
using System.Configuration;
using SetWorks.DAO.Authorization;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using Ninject;
using SetWorks.Common.Tools.SETWorksAI;
using SETWorksDAO.DAO.AI_ASSISTANT_ACTIVITY_RECORD_CONFIGURATION;

public partial class Home_ActivityRecords_ActivityRecordTypes_CaseManagementNote : SWPage
{
    private String NO_CONSUMER_SELECTION = "0";
    private bool USER_HAS_AI_ASSISTANT_ACCESS = false;
    private DSAIAssistantActivityRecordConfiguration activityRecordsAIAssistantConfig;
    
    [Inject]
    public IAIConfiguration _aAiConfiguration { get; set; }
    [Inject]
    public IAISystemConfiguration _aiSystemConfiguration { get; set; }
    [Inject]
    public IAIClient _aiClient { get; set; }

    protected void Page_Init(object Sender, System.EventArgs e)
    {
        HiddenCURRENTWINDOWCLIENTID.Value = getClientID().ToString();
        HiddenCURRENTWINDOWUSERID.Value = getUserID();
        HiddenCURRENTWINDOWUSERNAME.Value = getUsername();
        USER_HAS_AI_ASSISTANT_ACCESS = BOAIAccessConfiguration.hasAIEnhancedAccess(getClientID(), getUserID());
        activityRecordsAIAssistantConfig = BOAIAssistantActivityRecordConfiguration.getAIAssistantActivityRecordConfiguration(getClientID());
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        long[] iAccess = getUserPrivs();
        
        String dictionaryPath = ConfigurationManager.AppSettings["RadSpell.CustomDictionaryPath"];
        if (dictionaryPath != null && dictionaryPath.Length > 0)
        {
            RadSpellNonBillable.DictionaryPath = dictionaryPath;
        }
        
        var canAccessAutoReviewButton = USER_HAS_AI_ASSISTANT_ACCESS && Privilege.isPrivTrue(iAccess, Privileges18.ENABLE_AI_ASSISTANT_AUTO_REVIEW_CASE_MANAGEMENT_NOTE);
        if (canAccessAutoReviewButton)
        {
            if (activityRecordsAIAssistantConfig.provideGeneralSuggestions || activityRecordsAIAssistantConfig.reviewGrammar || 
                activityRecordsAIAssistantConfig.reviewTone || activityRecordsAIAssistantConfig.reviewSubjectiveObjectiveLanguage || 
                activityRecordsAIAssistantConfig.useCustomInstructions)
            {
                CreateAutoReviewControls(autoReview);
            }
        }
        
        if (!IsPostBack)
        {
            lblActivityDetails.Text = "Activity Details";


            if (Request["ActivityRecordID"] != null)
            {
                HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value = Request["ActivityRecordID"];
                BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, 
                    "ACTIVITYRECORDS_ID", HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), Session.SessionID);
            }
            HiddenACTION_TYPEWAI.Value = Constants.CREATE;
            if (Request["Action"] != null)
            {
                String temp = Request["Action"];
                if (temp.CompareTo(Constants.CREATE) == 0 || temp.CompareTo(Constants.MODIFY) == 0)
                {
                    HiddenACTION_TYPEWAI.Value = Request["Action"];
                    BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, 
                        "ACTION", HiddenACTION_TYPEWAI.Value, getUsername(), Session.SessionID);
                }
            }
            if (Request["ConsumerID"] != null)
            {
                HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value = Request["ConsumerID"];
                BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, 
                    "CONSUMER_ID", HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value, getUsername(), Session.SessionID);
            }

            checkBoxDesignatedTherapist.Checked = true;

            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
            {
                initializeCreateView(false);
                arHistoryIcon.Visible = false;
            }
            else if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
            {
                initializeUpdateView();
            }

            HiddenARTIMESPANSTART.Value = DateTime.UtcNow.ToString();

            if (Privilege.isPrivTrue(iAccess, Privileges7.TRAVEL_NOTES_TEXT_SHOW))
            {
                divTravelNotes.Visible = true;
            }

            if(!Privilege.isPrivTrue(iAccess, Privileges9.CAN_ADD_ADDENDUMS_TO_CM_NOTES))
            {
                arAddendumIcon.Visible = false;
            }
            
            HiddenUSEUPDATEDMILEAGEWINDOW.Value = Privilege.isPrivTrue(iAccess, Privileges14.USE_THE_UPDATED_MILEAGE_WINDOW).ToString();
        }
        
        if (isMobile())
        {
            ActivityRecordTitle.Width = 150;
        }
        I18NHelper.I18NPage(this.Page);
        FieldStateManager.setFieldStates(getClientID(), "CASE_MANAGEMENT_NOTE", this.Page.Controls);

        var privs = getUserPrivs();
        if (Privilege.isPrivTrue(privs, Privileges17.CONSUMER_REQUIRED_FOR_CASE_MANAGEMENT_NOTES))
        {
            RadComboBoxConsumerWAI.EmptyMessage = "Select a Consumer";
        }
    }

    private void initializeCreateView(bool _isDuplicateMode)
    {
        lblActivityDetails.Text = "Activity Details";
        divStaffCreated.Visible = false;

        HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value = BORandom.getRandomIDTableIncrementer(getUsername()).ToString();
        ActivityRecordsMisc.setARDescriptionByTypeAndAction(DSActivityRecordType.ACTIVITYRECORD_BILLABLE_CM_NOTE, I18NHelper.I18NString("create") + " ", HiddenCURRENTWINDOWCLIENTID.Value, ActivityRecordTitle);

        disableARfields(false);
        LinkButtonComposeMessage.Visible = false;
        composeSpacer.Visible = false;
        LinkButtonUpdate.Visible = false;
        LinkButtonDuplicate.Visible = false;
        LinkButtonSave.Visible = true;

        if (!_isDuplicateMode)
        {
            long[] iAccess = getUserPrivs();

            if (Privilege.isPriv6True(iAccess, Privileges6.DEFAULT_CM_NOTE_TO_ONLY_PRIMARY_CONTACTS))
            {
				chkBoxPrimaryFilter.Checked = true;
            }

			if (Privilege.isPrivTrue(iAccess, Privileges10.DEFAULT_CM_NOTE_TO_SECONDARY_CONTACTS))
			{
				chkBoxSecondaryFilter.Checked = true;
			}

            if (!Privilege.isPrivTrue(iAccess, Privileges7.DISPLAY_SAVE_AND_DUPLICATE_FOR_CASE_MANAGEMENT_NOTE))
            {
                LinkButtonSaveAndDuplicate.Visible = false;
            }

            BODepartment.getDepartmentsByUserID(getUserID(), RadComboBoxDepartment, 1);
            if (Privilege.isPriv2True(iAccess, Privileges2.SHOW_ALL_DEPARTMENT_OPTION))
            {
                RadComboBoxDepartment.Items.Insert(0, new RadComboBoxItem("All", "0"));
            }
            if (RadComboBoxDepartment.FindItemByValue(getDefaultDepartment().ToString()) != null)
            {
                RadComboBoxDepartment.SelectedValue = getDefaultDepartment().ToString();
            }

            checkBoxMonthlyQuarterly.Checked = false;
            if (Privilege.isPriv5True(iAccess, Privileges5.TCM_MONTHLY_QUARTERLY_DEFAULT_CHECKED))
            {
                checkBoxMonthlyQuarterly.Checked = true;
            }

            if (Request["CreateActivityRecordAtDateTime"] != null && Request["CreateActivityRecordAtDateTime"].ToString().CompareTo("USESESSION") != 0)
            {
                // create a new activity record - then set the date time accordingly
                DateTime clickedDateTime = DateTime.Parse(Request["CreateActivityRecordAtDateTime"].ToString());
                Session[SessionDefinitions.AR_START_DATE_TIME] = clickedDateTime;
            }

            if (Session[SessionDefinitions.AR_START_DATE_TIME] != null && Session[SessionDefinitions.AR_START_DATE_TIME].ToString().Length > 0)
            {
                radDatePicker.SelectedDate = (DateTime)Session[SessionDefinitions.AR_START_DATE_TIME];
            }
            else
            {
                radDatePicker.SelectedDate = TimeZoneProcessor.getLocalDateTime(getClientID().ToString());
            }

            radDatePicker_SelectedDateChanged(null, null);

            if (!Privilege.isPriv6True(iAccess, Privileges6.DEFAULT_DATE_TIME_FOR_CASE_MANAGEMENT_NOTES))
            {
                radDatePicker.SelectedDate = null;
            }

            radNumericHours.Value = 0;
            radNumericMinutes.Value = Privilege.isPrivTrue(iAccess, Privileges16.DEFAULT_ZERO_MINUTES_CASE_MANAGEMENT_NOTE) ? 0 : 5;
            radNumericTravel.Value = 0;
            radNumericTravelHours.Value = 0;
            radNumericHours_TextChanged(null, null);

            if (getClientID() == 132)
            {
                RadTextBoxAdditionalComments.EmptyMessage = "Daily notes should include the following: the purpose of the meeting/task, activities the SC completed (include where documents are located if they were reviewed), the response from the person/team, plan (next steps, reference where the document can be located).";
            }
            else
            {
                RadTextBoxAdditionalComments.EmptyMessage = "Daily notes should include a complete accounting of the appointment including activities, goals worked on, places traveled, and any additional comments or concerns.";
            }

        }
        else
        {
            RadComboBoxConsumerWAI.SelectedValue = "";
            RadComboBoxConsumerWAI.Text = "";
            HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value = "";
            HiddenACTION_TYPEWAI.Value = "CREATE";
        }



    }

    private void initializeUpdateView()
    {
        divStaffCreated.Visible = true;
        lblActivityDetails.Text = "Activity Details #" + HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value + " ";

        LinkButtonComposeMessage.Visible = true;
        composeSpacer.Visible = true;
        LinkButtonUpdate.Visible = true;
        LinkButtonDuplicate.Visible = false;
        LinkButtonSaveAndDuplicate.Visible = false;
        LinkButtonSave.Visible = false; 
        arAddendumIcon.Visible = true;
        List<DSAddendum> addendums = BOAddendum.getAddendumsByActivityRecordID(getClientID(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

        if (addendums != null && addendums.Count > 0)
        {
            HtmlImage arAddendumImage = (HtmlImage)IconButtons.FindControl("arAddendumImage");
            if (arAddendumImage != null) arAddendumImage.Src = "~/Images/ExistingAddendum.png";
        }

        DSActivityRecord activityRecord2 = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
        long[] iAccess = getUserPrivs();
        Boolean userIsCreator = false;

        ActivityRecordsMisc.setARDescriptionByTypeAndAction(DSActivityRecordType.ACTIVITYRECORD_BILLABLE_CM_NOTE, I18NHelper.I18NString("modify") + " ", HiddenCURRENTWINDOWCLIENTID.Value, ActivityRecordTitle);
        openActivityRecordWAI(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        radNumericHours_TextChanged(null, null);

        if (activityRecord2.getCreatorUserID.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0 || HiddenTRANSFORMATION.Value == "TRUE")
        {
            userIsCreator = true;
        }
        if (userIsCreator || (Privilege.isPrivTrue(iAccess, Privileges8.ALLOW_NONCREATORS_TO_DUPLICATE_CM_NOTE)))
        {

            LinkButtonDuplicate.Visible = true;
        }

    }

    private void openActivityRecordWAI(String activityRecordID)
    {
        long[] iAccess = getUserPrivs();
        bool privAdministratorOverrideEnabled = false;
        RadComboBoxItem listItem = null;
        DSActivityRecord activityRecord2 = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));

        if (activityRecord2?.getActivityRecordType == null)
        {
            //activity record was deleted
            DSDeletedData deletedData = BODeletedData.getDeletedDataByEntityTypeAndEntityID(getClientID(), "ACTIVITY_RECORDS_CASE_MANAGEMENT_NOTE", activityRecordID);
            string user = BOUser.getUserDescriptionByUserID(deletedData.Deleted_Data_User_ID.ToString());
            Response.Redirect(PathHelper.getAbsoluteUrl("~/ErrorPages/RecordDeleted.aspx") + "?RecordDeletedBy=" + user + "&DateDeleted=" + TimeZoneProcessor.convertDateTimeToClientLocalDateTime(getClientID().ToString(), deletedData.Deleted_Data_DateTime));

        }

        Session.Add(SessionDefinitions.ACTIVITYRECORD_ID_DEBUG_ONLY, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);

        String consumerID = activityRecord2.getConsumerID.ToString();
        String staffID = activityRecord2.getCreatorUserID;
        String authID = activityRecord2.getAuth_ID.ToString();
        String departmentID = "";

        LabelARIDWAI.Text = activityRecord2.getActivityRecordID.ToString();

        BODepartment.getDepartmentsByUserID(getUserID(), RadComboBoxDepartment, 1);
        if (Privilege.isPriv2True(iAccess, Privileges2.SHOW_ALL_DEPARTMENT_OPTION))
        {
            RadComboBoxDepartment.Items.Insert(0, new RadComboBoxItem("All", "0"));
        }

        if ((activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_BILLABLE_CM_NOTE") == 0) ||
            (activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_NONBILLABLE_CM_NOTE") == 0) ||
            (activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_NONBILLABLE_WITH_CONSUMER_CM_NOTE") == 0)
            )
        {
            HiddenTRANSFORMATION.Value = "FALSE";
        }
        else
        {
            HiddenTRANSFORMATION.Value = "TRUE";
            // get the actual type, get any relevant data as well, this will be used to prepolate things when opening.

            radNumericTravel.Value = 0;
            radNumericTravelHours.Value = 0;

            if (activityRecord2.getActivityRecordType.getCode.CompareTo("APPOINTMENT") == 0 || activityRecord2.getActivityRecordType.getCode.CompareTo("APPOINTMENT_AUTH") == 0)
            {
                // on save any appointments nad appointment relations must be cleared out

                DSAppointment appointment = BOAppointment.getAppointmentByAppointmentIDAndClientID(Int32.Parse(activityRecord2.getAppointmentID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                DSAppointmentRelation[] appointmentRelations = BOAppointmentRelation.getAppointmentRelationsByAppointmentIDAndClientID(Int32.Parse(activityRecord2.getAppointmentID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                String employerID = "";
                for (int i = 0; i < appointmentRelations.Length; i++)
                {
                    if (appointmentRelations[i].getEntityCode.CompareTo("CONSUMER_ID") == 0)
                    {
                        consumerID = appointmentRelations[i].getEntityID;
                        HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value = consumerID;

                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("DEPARTMENT_ID") == 0)
                    {
                        departmentID = appointmentRelations[i].getEntityID;
                        if (RadComboBoxDepartment.FindItemByValue(departmentID) != null)
                        {
                            RadComboBoxDepartment.SelectedValue = departmentID;
                        }

                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("EMPLOYER_ID") == 0)
                    {
                        // ignore for now
                        employerID = appointmentRelations[i].getEntityID;
                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("STAFF_USER_ID") == 0)
                    {
                        // ignore for now
                        staffID = appointmentRelations[i].getEntityID;
                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("AUTHORIZATION_ID") == 0)
                    {
                        // ignore for now
                        authID = appointmentRelations[i].getEntityID;
                    }
                }
            }
        }

        if (consumerID.Length > 0)
        {
            RadComboBoxConsumerWAI.DataBind();
            setRadComboConsumerBoxLoadOnDemandInitialValue(consumerID);
        }

        if (activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_BILLABLE_CM_NOTE") == 0)
        {
            // billable note
            DSAuthorization authorization = BOAuthorization.GetAuthorizationByAuthIDAndClientID(activityRecord2.getAuth_ID, getClientID());
            if (RadComboBoxDepartment.FindItemByValue(authorization.getDepartmentID.ToString()) != null)
            {
                RadComboBoxDepartment.SelectedValue = authorization.getDepartmentID.ToString();
            }
            else
            {
                // couldn't find department, add it in here
                DSDepartment dsDepartment = BODepartment.getDepartmentByDepartmentID(authorization.getDepartmentID.ToString());
                RadComboBoxDepartment.Items.Add(new RadComboBoxItem(dsDepartment.Department_Description, dsDepartment.Department_ID));
                RadComboBoxDepartment.SelectedValue = authorization.getDepartmentID.ToString();
            }
            setRadComboConsumerBoxLoadOnDemandInitialValue(activityRecord2.getConsumerID.ToString());

            if (RadComboBoxService.Items.FindItemByValue(authorization.getServiceID.ToString()) == null)
            {
                // add billable service entry
                DSService dsService = BOService.getServiceByServiceIDAndClientID(authorization.getServiceID, getClientID());
                RadComboBoxService.DataBind();
                RadComboBoxService.SelectedValue = dsService.getServiceID;
                RadComboBoxService.Text = dsService.getDescription;
            }
            else
            {
                RadComboBoxService.SelectedValue = authorization.getServiceID.ToString();
            }
            radDatePicker.SelectedDate = activityRecord2.getStartDate;
            RadTextBoxAdditionalComments.Text = HttpUtility.HtmlDecode(activityRecord2.getComment);
            RadTextBoxTravelNotes.Text = HttpUtility.HtmlDecode(activityRecord2.getIntervention);

        }
        else if (activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_NONBILLABLE_CM_NOTE") == 0 || activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_NONBILLABLE_WITH_CONSUMER_CM_NOTE") == 0)
        {
            // non billable note

            BODepartment.getDepartmentsByUserID(getUserID(), RadComboBoxDepartment, 1);
            if (Privilege.isPriv2True(iAccess, Privileges2.SHOW_ALL_DEPARTMENT_OPTION))
            {
                RadComboBoxDepartment.Items.Insert(0, new RadComboBoxItem("All", "0"));
            }

            RadComboBoxDepartment.SelectedValue = "0";
            setRadComboConsumerBoxLoadOnDemandInitialValue(activityRecord2.getConsumerID.ToString());

            if (RadComboBoxService.Items.FindItemByValue(activityRecord2.getNonBillableID.ToString()) == null)
            {
                RadComboBoxService.DataBind();
                RadComboBoxService.SelectedValue = activityRecord2.getNonBillableID.ToString();
                RadComboBoxService.Text = BONonBillable.getNonBillableDescriptionByNonBillableID(activityRecord2.getNonBillableID);
            }
            else
            {
                RadComboBoxService.SelectedValue = activityRecord2.getNonBillableID.ToString();
            }
            radDatePicker.SelectedDate = activityRecord2.getStartDate;
            RadTextBoxAdditionalComments.Text = HttpUtility.HtmlDecode(activityRecord2.getComment);
            RadTextBoxTravelNotes.Text = HttpUtility.HtmlDecode(activityRecord2.getIntervention);
        }


        if (staffID != null && staffID.Length > 0 && staffID != "0")
        {
            HiddenBILLABLECAPAR_CREATOR_USER_ID.Value = staffID;
            lblStaffCreated.Text = BOUser.getUserDescriptionByUserID(staffID);
        }
        else
        {
            HiddenBILLABLECAPAR_CREATOR_USER_ID.Value = activityRecord2.getCreatorUserID;
        }

        radDatePicker.SelectedDate = activityRecord2.getStartDate;
        radDatePicker_SelectedDateChanged(null, null);
        RadComboBoxPlaceOfService.DataBind();

        /*
        DSActivityRecordPlaceOfService[] arPlaceOfServices = BOActivityRecordPlaceOfService.getActivityRecordPlaceOfService(activityRecord2.getActivityRecordID.ToString());
        if (arPlaceOfServices != null)
        {
            for (int i = 0; i < arPlaceOfServices.Length; i++)
            {
                if (RadComboBoxPlaceOfService.Items.FindItemByValue(arPlaceOfServices[i].PlaceOfServiceID) != null)
                {
                    RadComboBoxPlaceOfService.Items.FindItemByValue(arPlaceOfServices[i].PlaceOfServiceID).Checked = true;
                }
            }
        }*/
        DSActivityRecordPlaceOfService[] arPlaceOfServices = BOActivityRecordPlaceOfService.getActivityRecordPlaceOfService(activityRecord2.getActivityRecordID.ToString(), getClientID());
        if (arPlaceOfServices != null)
        {
            RadComboBoxPlaceOfService.SelectedValue = BOActivityRecordPlaceOfService.getActivityRecordPlaceOfService(activityRecord2.getActivityRecordID.ToString(), getClientID())[0].PlaceOfServiceID;
        }

        RadComboBoxPersonSeen.DataBind();
//        RadComboBoxPersonSeen.ClearCheckedItems();
        DSActivityRecordPersonSeen[] arPersonSeen = BOActivityRecordPersonSeen.getActivityRecordPersonSeen(activityRecord2.getActivityRecordID.ToString());
        if (arPersonSeen != null)
        {
            for (int i = 0; i < arPersonSeen.Length; i++)
            {
                if (RadComboBoxPersonSeen.Items.FindItemByValue(arPersonSeen[i].PersonSeenID) != null)
                {
                    RadComboBoxPersonSeen.Items.FindItemByValue(arPersonSeen[i].PersonSeenID).Checked = true;
                }
            }
        }

        // find minutes between times
        DateTime startTime = activityRecord2.getStartDate;
        DateTime endTime = activityRecord2.getEndDate;

        DSActivityRecordAdditional dsARAdditional = BOActivityRecordAdditional.getActivityRecordAdditional(activityRecord2.getActivityRecordID.ToString(), getClientID().ToString());
        if (dsARAdditional != null)
        {
            checkBoxDesignatedTherapist.Checked = dsARAdditional.DesignatedTherapist;
            checkBoxMonthlyQuarterly.Checked = dsARAdditional.IncludeOnMonthlyQuarterly;
            checkBoxDisclosure.Checked = dsARAdditional.Disclosure;
            checkBoxGenericBitFlag1.Checked = dsARAdditional.GenericBitFlag1;
            checkBoxGenericBitFlag2.Checked = dsARAdditional.GenericBitFlag2;

            ControlHelper.assignComboBoxValue(RadComboBoxDepartment, dsARAdditional.DepartmentID, departmentId => BODepartment.getDepartmentByDepartmentID(departmentId).Department_Description);
            //RadComboBoxTravel.SelectedValue = dsARAdditional.TravelTimeMinutes.ToString();

            DateTime startTimeTravel = DateTime.UtcNow;
            DateTime endTimeTravel = new DateTime();
            endTimeTravel = startTimeTravel;
            endTimeTravel = endTimeTravel.AddMinutes(dsARAdditional.TravelTimeMinutes);
            TimeSpan timeSpanTravel = endTimeTravel.Subtract(startTimeTravel);
            radNumericTravelHours.Value = timeSpanTravel.Hours;
            radNumericTravel.Value = timeSpanTravel.Minutes;

            endTime = endTime.AddMinutes(dsARAdditional.TravelTimeMinutes * -1);
            checkboxDocumentationAvailable.Checked = dsARAdditional.LateNoteHasDocumentation;
        }

        TimeSpan timeSpan = endTime.Subtract(startTime);
        radNumericHours.Value = timeSpan.Hours;
        radNumericMinutes.Value = timeSpan.Minutes;

        Boolean userIsManager = false;
        Boolean userIsCreator = false;

        if (activityRecord2.getCreatorUserID.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0 || HiddenTRANSFORMATION.Value == "TRUE")
        {
            userIsCreator = true;
        }

        if (Privilege.isPriv1True(iAccess, Privileges.SIGN_ARS_AS_MANAGER))
        {
            userIsManager = true;
        }

        Boolean disableARWindow = true;
        if (userIsManager)
        {
            if (activityRecord2.getManagerSign_ID == null)
            {
                // no manager signed, enable everything
                disableARWindow = false;
            }
            else
            {
                if (activityRecord2.getManagerSign_ID != null && activityRecord2.getManagerSign_ID.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0)
                {
                    disableARWindow = false;
                }
                else
                {
                    disableARWindow = true;
                }
            }

            if (privAdministratorOverrideEnabled)
            {
                disableARWindow = false;
            }

        }
        else
        {
            // user is staff or not manager
            if (userIsCreator)
            {
                if (activityRecord2.getManagerSign_ID == null)
                {
                    disableARWindow = false;
                }
            }
            else
            {
                if (HiddenTRANSFORMATION.Value == "TRUE")
                {
                    disableARWindow = false;
                }
                else
                {
                    disableARWindow = true;
                }
            }
        }

        if (disallowEditingARDueToBilledStatus(activityRecordID, getClientID().ToString()))
        {
            disableARWindow = true;
        }

        disableARfields(disableARWindow);
    }

    private void setRadComboConsumerBoxLoadOnDemandInitialValue(String _selectedValue)
    {

        if (RadComboBoxConsumerWAI.FindItemByValue(_selectedValue) == null && _selectedValue != null && _selectedValue.Length > 0 && _selectedValue.CompareTo("0") != 0)
        {

            DSConsumer consumer = BOConsumer.GetConsumerDetail(Int32.Parse(_selectedValue), getClientID());
            String consumerDescription = "";
            if (getClientID() == 95)
            {
				//consumerDescription = BOConsumer.formatName(consumer.getLastName, consumer.getSuffix, consumer.getFirstName, consumer.getNickName, consumer.getMiddleName, false);
				consumerDescription = consumer.getFirstName + " " + consumer.getLastName + " (" + consumer.getDMH + ")";
            }
            else
            {
                consumerDescription = BOConsumer.formatName(consumer.getLastName, consumer.getSuffix, consumer.getFirstName, consumer.getNickName, consumer.getMiddleName, true) + " (" + consumer.getDMH + ")";
            }

            RadComboBoxConsumerWAI.DataBind();
            RadComboBoxConsumerWAI.SelectedValue = _selectedValue;
            RadComboBoxConsumerWAI.Text = consumerDescription;
            HiddenRADCOMBOOPENCONSUMERID.Value = _selectedValue;
        }
    }

    private Boolean saveOrUpdateRecord(Boolean _isUpdate)
    {
        // Fix a bug with RadComboBoxPlaceOfService losing its SelectedValue
        // This appears to be a Telerik bug in certain scenarios. We tried adding an AjaxUpdatedControl and the issue still would occur, so this fix was needed.
        if (!string.IsNullOrWhiteSpace(RadComboBoxPlaceOfService.Text) && string.IsNullOrWhiteSpace(RadComboBoxPlaceOfService.SelectedValue))
        {
            RadComboBoxPlaceOfService.SelectedValue = RadComboBoxPlaceOfService.Items.ToArray().First(i => i.Text == RadComboBoxPlaceOfService.Text).Value;   
        }
            
        if (!isValidatorsValid())
            return false;

        long[] iAccess = getUserPrivs();

        setError("");

        int totalRecordMinutes = (Int32.Parse(radNumericHours.Value.ToString()) * 60) + Int32.Parse(radNumericMinutes.Value.ToString());
        int travelTimeMinutes = Int32.Parse(radNumericTravel.Value.ToString()) + (Int32.Parse(radNumericTravelHours.Value.ToString()) * 60);

        DSActivityRecord activityRecord = null;
        DateTime startTime;
        if (_isUpdate)
        {
            activityRecord = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getClientID());
            startTime = new DateTime(radDatePicker.SelectedDate.Value.Year, radDatePicker.SelectedDate.Value.Month, radDatePicker.SelectedDate.Value.Day, activityRecord.getStartDate.Hour, activityRecord.getStartDate.Minute, activityRecord.getStartDate.Second);
        }
        else
        {
            DateTime relTime = TimeZoneProcessor.getLocalDateTime(getClientID().ToString());
            startTime = new DateTime(radDatePicker.SelectedDate.Value.Year, radDatePicker.SelectedDate.Value.Month, radDatePicker.SelectedDate.Value.Day, relTime.Hour, relTime.Minute, relTime.Second);
            if (Session[SessionDefinitions.AR_START_DATE_TIME] != null && Session[SessionDefinitions.AR_START_DATE_TIME].ToString().Length > 0)
            {
                DateTime arStartTime = (DateTime)Session[SessionDefinitions.AR_START_DATE_TIME];
                startTime = new DateTime(radDatePicker.SelectedDate.Value.Year, radDatePicker.SelectedDate.Value.Month, radDatePicker.SelectedDate.Value.Day, arStartTime.Hour, arStartTime.Minute, arStartTime.Second);
            }
        }

        DateTime endTime = startTime.AddMinutes(totalRecordMinutes + travelTimeMinutes);

        // this is OK because the max record time for TCM is 11 hours and 59 minutes- this is to handle huge records created late at night
        if ((endTime.DayOfYear > startTime.DayOfYear) || (endTime.DayOfYear == 1 && startTime.DayOfYear >= 365))
        {
            endTime = new DateTime(startTime.Year, startTime.Month, startTime.Day, 23, 59, 0);
            startTime = endTime.AddMinutes( (totalRecordMinutes + travelTimeMinutes) * -1);
        }

        double hours = ActivityRecordsMisc.calculateHrs(startTime, endTime);
        String activityRecordIDOfNewItem = "";

        DSService dsService = BOService.getServiceByServiceIDAndClientID(Int32.Parse(RadComboBoxService.SelectedValue), getClientID());
        if (dsService != null && dsService.getDescription != null && dsService.getDescription.CompareTo(RadComboBoxService.Text) == 0)
        {
            DSDepartment defaultDepartment = null;

            int tcmDepCount = 0;
            DSDepartment[] departments = BODepartment.getDepartmentsArrayByConsumerID(getClientID(), RadComboBoxConsumerWAI.SelectedValue);
            for (int i = 0; i < departments.Length; i++)
            {
                if (departments[i].Department_TypeCode.CompareTo("TCM") == 0)
                {
                    tcmDepCount++;
                    defaultDepartment = departments[i];
                }
            }

            if (defaultDepartment == null)
            {
                setError("No valid Department for Case Management found.");
                return false;
            }

            // add check here - if Consumer has more than ONE TCM Type department, then check the current department dropdown - IF it is one of the TCM departments, then move forward with that as default
            // IF IF IS neither one of the tcm departments, then return error, stating - please select ONE of the following TCM deparments (list of all applicable ones) - 
            if (tcmDepCount > 1)
            {
                defaultDepartment = null;
                // get current department selection
                String currentDepartmentID = RadComboBoxDepartment.SelectedValue;
                if (currentDepartmentID.CompareTo("0") == 0)
                {
                    setError("This consumer is associated to more than one TCM Department. Please select the appropriate TCM department.");
                    return false;
                }
                else
                {

                    for (int i = 0; i < departments.Length; i++)
                    {
                        if (departments[i].Department_ID.CompareTo(currentDepartmentID) == 0)
                        {
                            // great, we have a match, use this deparment as the default
                            defaultDepartment = departments[i];
                            break;
                        }
                    }
                }

                if (defaultDepartment == null)
                {
                    setError("This consumer is associated to more than one TCM Department. Please select the appropriate TCM department.");
                    return false;
                }
            }


            // billable service
            DSContract contract = null;
            DSContract[] contracts = BOContract.getContractsArrayByClientIDAndDepartmentID(getClientID(), Int32.Parse(defaultDepartment.Department_ID));
            if (contracts.Length > 1)
            {
                setError("The selected TCM Department is associated to more than one contract. Please contact your Administrator to correct.");
                return false;
            }
            if (contracts.Length == 0)
            {
                setError("No valid Contract for Case Management found.");
                return false;
            }
            contract = contracts[0];
            if (contract == null)
            {
                setError("No valid Contract for Case Management found.");
                return false;
            }

            DSAuthorization validAuth = null;
            DSAuthorization[] authorizations = BOAuthorization.getAuthorizationsWithFrequencyByConsumerID(Int32.Parse(RadComboBoxConsumerWAI.SelectedValue));
            for (int i = 0; i < authorizations.Length; i++)
            {
                DSAuthorization authorization = authorizations[i];
                if (authorization.getServiceID.CompareTo(Int32.Parse(RadComboBoxService.SelectedValue)) == 0 && authorization.getDepartmentID.CompareTo(Int32.Parse(defaultDepartment.Department_ID)) == 0)
                {
                    if (authorization.getStartDate <= radDatePicker.SelectedDate && authorization.getEndDate >= radDatePicker.SelectedDate)
                    {
                        validAuth = authorization;
                        break;
                    }
                }
            }

            if (validAuth == null)
            {

                // create new auth
                String authID = BOAuthorization.createAuthorization(getClientID(), RadComboBoxConsumerWAI.SelectedValue, RadComboBoxService.Text, 40, RadComboBoxService.SelectedValue,
                     new DateTime(DateTime.UtcNow.Year, 1, 1), new DateTime(DateTime.UtcNow.Year, 12, 31), Int32.Parse(contract.ContractID), true, false, getUsername(), false,
                     BOAuthorization.getAuthTypeIDByAuthTypeCode(getClientID().ToString(), "HOURLY_BASED"), Int32.Parse(defaultDepartment.Department_ID),
                    0, 0, DateTime.MinValue, "");

                validAuth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(authID), getClientID());
            }
            else
            {
                // make sure the auth has enough hours, or ADD more
                double hoursUsed = BOAuthorization.GetTimesFromActivityRecordsByAuthIDAndClientIDGetHoursUsed(validAuth.getAuthID, getClientID(), false);
                if ((validAuth.getAuthHours - hoursUsed) < (totalRecordMinutes / 60))
                {
                    // add hours to the authorization
                    BOAuthorization.modifyAuthorization(getClientID(), validAuth.getAuthID.ToString(), validAuth.getAuthCode, validAuth.getAuthHours + 40, validAuth.getServiceID.ToString(),
                        validAuth.getStartDate, validAuth.getEndDate, Int32.Parse(validAuth.getContractID), validAuth.getActive, validAuth.getDummy, getUsername(), validAuth.getAuthFrequencyLimit,
                        validAuth.getAuthTypeID, validAuth.getDepartmentID, validAuth.getFundingSourceContactID, Int32.Parse(validAuth.getAreaProgramID), validAuth.getEndEffectiveDate);
                }
            }

            if (_isUpdate)
            {
                if (HiddenTRANSFORMATION.Value == "TRUE")
                {
                    BOActivityRecord.modifyActivityRecordAndCreatorUserID(getClientID(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, RadComboBoxConsumerWAI.SelectedValue, startTime,
                    endTime, hours, validAuth.getAuthID.ToString(), "", checkBoxDesignatedTherapist.Checked, "",
                    HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "",
                    BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_BILLABLE_CM_NOTE", getClientID()), "", "", "", "", true, "", getUserID(), (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
                }
                else
                {
                    BOActivityRecord.modifyActivityRecord(getClientID(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, RadComboBoxConsumerWAI.SelectedValue, startTime,
                    endTime, hours, validAuth.getAuthID.ToString(), "", checkBoxDesignatedTherapist.Checked, "",
                    HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "",
                    BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_BILLABLE_CM_NOTE", getClientID()), "", "", "", "", true, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
                }
            }
            else
            {
                Boolean duplicateAR = BOActivityRecord.isActivityRecordTCMDuplicate(getClientID().ToString(), startTime, endTime, RadComboBoxConsumerWAI.SelectedValue, getUserID(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text));
                if (!duplicateAR)
                {
                    activityRecordIDOfNewItem = BOActivityRecord.createActivityRecord(getClientID(), RadComboBoxConsumerWAI.SelectedValue, startTime,
                        endTime, hours, validAuth.getAuthID.ToString(), "", checkBoxDesignatedTherapist.Checked, "",
                        HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), getUserID(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "",
                        BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_BILLABLE_CM_NOTE", getClientID()), "", "", "", "", true, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, "");
                }
                else
                {
                    setError("Duplicate Note! It looks like you are trying to save the same record twice, please adjust to make this record unique or close this window.");
                    return false;
                }
            }
        }
        else
        {
            // non billable service
            DSNonBillable dsNonBillable = null;
            DSNonBillable[] dsNonBillables = BONonBillable.getDSNonBillableByClientID(getClientID().ToString());
            for (int i = 0; i < dsNonBillables.Length; i++)
            {
                if (dsNonBillables != null && dsNonBillables[i].getDescription.CompareTo(RadComboBoxService.Text) == 0)
                {
                    dsNonBillable = dsNonBillables[i];
                }
            }

            if (dsNonBillable == null)
            {
                setError("No non billable item found.");
                return false;
            }

            if (RadComboBoxConsumerWAI != null && RadComboBoxConsumerWAI.SelectedValue != null && RadComboBoxConsumerWAI.SelectedValue.Length > 0 && RadComboBoxConsumerWAI.SelectedValue != "0")
            {
                if (_isUpdate)
                {
                    if (HiddenTRANSFORMATION.Value == "TRUE")
                    {
                        BOActivityRecord.modifyActivityRecordAndCreatorUserID(getClientID(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, RadComboBoxConsumerWAI.SelectedValue, startTime, endTime, hours, "", dsNonBillable.getNonBillableID, false, "",
                            HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "", BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_NONBILLABLE_WITH_CONSUMER_CM_NOTE", getClientID()), "", "", "", "", true, "", getUserID(), (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
                    }
                    else
                    {
                        BOActivityRecord.modifyActivityRecord(getClientID(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, RadComboBoxConsumerWAI.SelectedValue, startTime, endTime, hours, "", dsNonBillable.getNonBillableID, false, "",
                            HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "", BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_NONBILLABLE_WITH_CONSUMER_CM_NOTE", getClientID()), "", "", "", "", true, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
                    }
                }
                else
                {
                    Boolean duplicateAR = BOActivityRecord.isActivityRecordTCMDuplicate(getClientID().ToString(), startTime, endTime, RadComboBoxConsumerWAI.SelectedValue, getUserID(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text));
                    if (!duplicateAR)
                    {

                        activityRecordIDOfNewItem = BOActivityRecord.createActivityRecord(getClientID(), RadComboBoxConsumerWAI.SelectedValue, startTime, endTime, hours, "", dsNonBillable.getNonBillableID, false, "",
                            HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), getUserID(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "",
                            BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_NONBILLABLE_WITH_CONSUMER_CM_NOTE", getClientID()), "", "", "", "", true, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, "");
                    }
                    else
                    {
                        setError("Duplicate Note! It looks like you are trying to save the same record twice, please adjust to make this record unique or close this window.");
                        return false;
                    }
                }
            }
            else
            {
                if (_isUpdate)
                {
                    if (HiddenTRANSFORMATION.Value == "TRUE")
                    {
                        BOActivityRecord.modifyActivityRecordAndCreatorUserID(getClientID(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, "", startTime,
                            endTime, hours, "", dsNonBillable.getNonBillableID, false, "",
                            HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "",
                            BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_NONBILLABLE_CM_NOTE", getClientID()), "", "", "", "", true, "", getUserID(), (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
                    }
                    else
                    {
                        BOActivityRecord.modifyActivityRecord(getClientID(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, "", startTime,
                            endTime, hours, "", dsNonBillable.getNonBillableID, false, "",
                            HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "",
                            BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_NONBILLABLE_CM_NOTE", getClientID()), "", "", "", "", true, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
                    }
                }
                else
                {
                    Boolean duplicateAR = BOActivityRecord.isActivityRecordTCMDuplicate(getClientID().ToString(), startTime, endTime, "", getUserID(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text));
                    if (!duplicateAR)
                    {
                        activityRecordIDOfNewItem = BOActivityRecord.createActivityRecord(getClientID(), "", startTime,
                            endTime, hours, "", dsNonBillable.getNonBillableID, false, "",
                            HttpUtility.HtmlEncode(RadTextBoxTravelNotes.Text), "", "", getUsername(), getUserID(), HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text), false, false, false, "",
                            BOActivityRecord.getActivityRecordTypeIDByActivityRecordTypeCode("ACTIVITYRECORD_NONBILLABLE_CM_NOTE", getClientID()), "", "", "", "", true, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, "");
                    }
                    else
                    {
                        setError("Duplicate Note! It looks like you are trying to save the same record twice, please adjust to make this record unique or close this window.");
                        return false;
                    }
                }
            }
        }

        if (_isUpdate)
        {
            BOActivityRecordPlaceOfService.deleteActivityRecordPlaceOfServiceByActivityRecordID(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        }
        else
        {
            BOActivityRecordPlaceOfService.deleteActivityRecordPlaceOfServiceByActivityRecordID(activityRecordIDOfNewItem);
        }

        if (RadComboBoxPlaceOfService.SelectedValue != null && RadComboBoxPlaceOfService.SelectedValue.Length > 0)
        {
            DSActivityRecordPlaceOfService dsARPlaceOfService = new DSActivityRecordPlaceOfService();
            if (_isUpdate)
            {
                dsARPlaceOfService.ActivityRecordID = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
            }
            else
            {
                dsARPlaceOfService.ActivityRecordID = activityRecordIDOfNewItem;
            }
            dsARPlaceOfService.LstUpdateUsername = getUsername();
            dsARPlaceOfService.PlaceOfServiceID = RadComboBoxPlaceOfService.SelectedValue;
            BOActivityRecordPlaceOfService.createActivityRecordPlaceOfService(dsARPlaceOfService, getClientID());
        }


        if (_isUpdate)
        {
            BOActivityRecordPersonSeen.deleteActivityRecordPersonSeenByActivityRecordID(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        }
        else
        {
            BOActivityRecordPersonSeen.deleteActivityRecordPersonSeenByActivityRecordID(activityRecordIDOfNewItem);
        }

        var collection = RadComboBoxPersonSeen.CheckedItems;
        if (collection.Count != 0)
        {
            foreach (var item in collection)
            {
                DSActivityRecordPersonSeen dsARPersonSeen = new DSActivityRecordPersonSeen();
                if (_isUpdate)
                {
                    dsARPersonSeen.ActivityRecordID = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
                }
                else
                {
                    dsARPersonSeen.ActivityRecordID = activityRecordIDOfNewItem;
                }
                dsARPersonSeen.LstUpdateUsername = getUsername();
                dsARPersonSeen.PersonSeenID = item.Value;
                BOActivityRecordPersonSeen.createActivityRecordPersonSeen(dsARPersonSeen, getClientID());
            }
        }

        DSActivityRecordAdditional dsARAdditional = new DSActivityRecordAdditional();
        if (_isUpdate)
        {
            dsARAdditional.ActivityRecordID = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
        }
        else
        {
            dsARAdditional.ActivityRecordID = activityRecordIDOfNewItem;
        }

        dsARAdditional.DesignatedTherapist = checkBoxDesignatedTherapist.Checked;
        dsARAdditional.IncludeOnMonthlyQuarterly = checkBoxMonthlyQuarterly.Checked;
        dsARAdditional.Disclosure = checkBoxDisclosure.Checked;
        dsARAdditional.GenericBitFlag1 = checkBoxGenericBitFlag1.Checked;
        dsARAdditional.GenericBitFlag2 = checkBoxGenericBitFlag2.Checked;
        dsARAdditional.DepartmentID = RadComboBoxDepartment.SelectedValue;
        dsARAdditional.LstUpdateUsername = getUsername();
        dsARAdditional.TravelTimeMinutes = travelTimeMinutes;
        dsARAdditional.LateNoteHasDocumentation = checkboxDocumentationAvailable.Checked;
        dsARAdditional.ClientID = getClientID().ToString();

        dsARAdditional.IsLateNote = false;
        if (_isUpdate && isLateNote())
        {
            // if it was late, keep it late, if I'm modifying and this action makes it late and I'm a different creator 
            if (Privilege.isPriv6True(iAccess, Privileges6.CASE_MANAGEMENT_SKIP_LATE_TRIGGER))
            {
                // need to make sure that creator and modifier is NOT the same
                if (HiddenBILLABLECAPAR_CREATOR_USER_ID.Value.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0)
                {
                    // same - trigger it to be late
                    dsARAdditional.IsLateNote = true;
                }
                else
                {
                    // so update & islate & skip late trigger & not the same user
                    // ok, so different user, but if it was late before then it must stay late
                    DSActivityRecordAdditional dsARAdditionalExisting = BOActivityRecordAdditional.getActivityRecordAdditional(dsARAdditional.ActivityRecordID, getClientID().ToString());
                    if (dsARAdditionalExisting.IsLateNote)
                    {
                        dsARAdditional.IsLateNote = true;
                    }
                }
            }
            else
            {
                dsARAdditional.IsLateNote = true;
            }
        }
        else
        {
            if (isLateNote())
            {
                dsARAdditional.IsLateNote = true;
            }
        }

        if (!dsARAdditional.IsLateNote)
        {
            BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Save Late Note Check - Fixed Late Note Check", "SAVE_LATE_NOTE_CHECK_FIX", getUsername(), Session.SessionID);
            dsARAdditional.LateNoteHasDocumentation = false;
        }

        BOActivityRecordAdditional.setActivityRecordAdditional(dsARAdditional);

        if (_isUpdate)
        {
            ActivityRecordsMisc.updateSignatures(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getClientID().ToString(), getUsername(), getUserID());
        }

        if (!_isUpdate)
        {
            BOExpense.setExpenseUpdateTemporary(HiddenCURRENTWINDOWCLIENTID.Value, HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, activityRecordIDOfNewItem, "TEMP_MILEAGE", "MILEAGE", getUsername());
            BOMileage.setMileageUpdateTemporary(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value),
                Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), 
                Int32.Parse(activityRecordIDOfNewItem),
                HiddenCURRENTWINDOWUSERID.Value);
        }

        HiddenARTIMESPANSTART.Value = DateTime.UtcNow.ToString();
        checkboxDocumentationAvailable.Checked = false;

        return true;
    }

    protected void LinkButtonSave_Click(object sender, EventArgs e)
    {
        BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Action", "SAVE", getUsername(), Session.SessionID);

        if (saveOrUpdateRecord(false))
        {
            if (!HiddenAUTOSAVEID.Value.Equals("False"))
                BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));


                RadAjaxManagerBillableWAI.ResponseScripts.Add("CloseNote();");
        }
    }

    protected void LinkButtonUpdate_Click(object sender, EventArgs e)
    {
        BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Action", "UPDATE", getUsername(), Session.SessionID);

        if (saveOrUpdateRecord(true))
        {
            if (!HiddenAUTOSAVEID.Value.Equals("False"))
                BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));


                RadAjaxManagerBillableWAI.ResponseScripts.Add("CloseNote();");
        }
    }

    private void setError(string error)
    {
        if (error == "")
        {
            ErrorDIV.Style.Add("display", "none");
            lblStatusUpdateWAI.Text = error;
        }
        else
        {
            ErrorDIV.Style.Add("display", "block");
            lblStatusUpdateWAI.Text = error;
        }
    }

    protected void RadComboBoxConsumers_ItemDataBound(object sender, RadComboBoxItemEventArgs e)
    {
        if (e != null && e.Item != null && e.Item is RadComboBoxItem)
        {
            RadComboBoxItem dataItem = (e.Item as RadComboBoxItem);
            if (dataItem == null)
            {
                return;
            }

            DataRowView v = (DataRowView)e.Item.DataItem;
            if (v == null)
            {
                return;
            }

            String consumerID = v["Consumer_ID"].ToString();
            if (((Image)dataItem.FindControl("consumerImage")) != null)
            {
                ((Image)dataItem.FindControl("consumerImage")).ImageUrl = "~/Home/Settings/Utilities/ProviderImageDownload2.ashx?Type=45&ConsumerID=" + consumerID + "&ClientID=" + getClientID().ToString();
            }
        }
    }

    protected void RadComboBoxDepartment_SelectedIndexChanged(object sender, RadComboBoxSelectedIndexChangedEventArgs e)
    {
        BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Action", "DEPARTMENT CHANGE", getUsername(), Session.SessionID);

        String selectedValue = RadComboBoxConsumerWAI.SelectedValue;
        RadComboBoxConsumerWAI.Text = "";
        RadComboBoxConsumerWAI.ClearSelection();
        RadComboBoxConsumerWAI.DataBind();
        setRadComboConsumerBoxLoadOnDemandInitialValue(selectedValue);
        RadComboBoxConsumerWAI.Focus();

    }

    private bool isValidatorsValid()
    {
        string error = "";
        long[] iAccess = getUserPrivs();

        if (RadComboBoxDepartment.SelectedValue == null || RadComboBoxDepartment.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxDepartment.SelectedValue) >= 0))
            error = error + I18NHelper.I18NString("Department is required") + "<br />";

        if (RadComboBoxService.SelectedValue == null || RadComboBoxService.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxService.SelectedValue) > 0))
            error = error + I18NHelper.I18NString("Service is required") + "<br />";

        if (RadTextBoxAdditionalComments.Visible && RadTextBoxAdditionalComments.Text.Length == 0)
            error = error + I18NHelper.I18NString("Notes are required") + "<br />";

        if (radDatePicker == null || radDatePicker.SelectedDate == null || radDatePicker.SelectedDate.Value == null || radDatePicker.SelectedDate.Value.Date == null)
        {
            error = error + I18NHelper.I18NString("Date of Service required") + "<br />";
        }

        if (radDatePicker != null || radDatePicker.SelectedDate != null || radDatePicker.SelectedDate.Value != null || radDatePicker.SelectedDate.Value.Date != null)
        {
            if (radDatePicker.SelectedDate.HasValue && (radDatePicker.SelectedDate.Value.CompareTo(DateTime.Now.AddYears(1)) > 0 || radDatePicker.SelectedDate.Value.CompareTo(DateTime.Parse("01/01/2000")) < 0))
            {
                error = error + I18NHelper.I18NString("Date of Service Out of Range") + "<br />";
            }
        }

        if (radNumericTravel == null || radNumericTravel.Value == null || !radNumericTravel.Value.HasValue)
        {
            error = error + I18NHelper.I18NString("You must specify a travel time") + "<br />";
        }

        if (radNumericTravelHours == null || radNumericTravelHours.Value == null || !radNumericTravelHours.Value.HasValue)
        {
            error = error + I18NHelper.I18NString("You must specify a travel time") + "<br />";
        }

        if (radNumericHours == null || radNumericHours.Value == null || !radNumericHours.Value.HasValue)
        {
            error = error + I18NHelper.I18NString("You must specify number of hours") + "<br />";
        }

        if (radNumericMinutes == null || radNumericMinutes.Value == null || !radNumericMinutes.Value.HasValue)
        {
            error = error + I18NHelper.I18NString("You must specify number of minutes") + "<br />";
        }

        if (RadComboBoxPlaceOfService.Text.Length != 0 && (RadComboBoxPlaceOfService.SelectedValue == null || RadComboBoxPlaceOfService.SelectedValue.Length == 0))
        {
            error = error + I18NHelper.I18NString("You must specify a valid place of service") + "<br />";
        }

        if (Privilege.isPriv6True(iAccess, Privileges6.CASE_MANAGEMENT_NOTE_REQUIRES_PLACE_OF_SERVICE))
        {
            if (RadComboBoxPlaceOfService.SelectedValue == null || RadComboBoxPlaceOfService.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxPlaceOfService.SelectedValue) >= 0))
                error = error + I18NHelper.I18NString("Place of service is required") + "<br />";
        }

        if (radDatePicker.SelectedDate!= null && !ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, radDatePicker.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID()))
        {
            error = error + lblStatusUpdateWAI.Text;
        }

        if (RadComboBoxService != null && RadComboBoxService.SelectedValue != null && RadComboBoxService.SelectedValue.CompareTo("") != 0)
        {
            DSService dsService = BOService.getServiceByServiceIDAndClientID(Int32.Parse(RadComboBoxService.SelectedValue), getClientID());
            if (dsService != null && dsService.getDescription != null && dsService.getDescription.CompareTo(RadComboBoxService.Text) == 0)
            {
                // billable service
                if (RadComboBoxConsumerWAI.SelectedValue == null || RadComboBoxConsumerWAI.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxConsumerWAI.SelectedValue) > 0))
                    error = error + I18NHelper.I18NString("Consumer is required") + "<br />";
            }
            else
            {
                // non billable - make sure that a department is selected and that is not the ALL department?
                if (RadComboBoxDepartment.SelectedValue == null || RadComboBoxDepartment.SelectedValue.CompareTo("0") == 0)
                    error = error + I18NHelper.I18NString("Department is required") + "<br />";

                // also make sure that the selected consumer is applicable to the current consumer, only applies of 'no consumer' isn't selected
                if (RadComboBoxConsumerWAI.SelectedValue != null && RadComboBoxConsumerWAI.SelectedValue.CompareTo("") != 0 && !RadComboBoxConsumerWAI.SelectedValue.Equals(NO_CONSUMER_SELECTION))
                {
                    DSDepartment[] dsDepartments = BODepartment.getDepartmentsArrayByConsumerID(getClientID(), RadComboBoxConsumerWAI.SelectedValue);
                    bool validDepartment = false;
                    for (int i = 0; i < dsDepartments.Length; i++)
                    {
                        if (dsDepartments[i].Department_ID.CompareTo(RadComboBoxDepartment.SelectedValue) == 0)
                        {
                            validDepartment = true;
                            break;
                        }
                    }
                    if (!validDepartment)
                    {
                        error = error + I18NHelper.I18NString("Department is not valid for Consumer selection") + "<br />";
                    }
                }
            }
        }

        if (radNumericHours.Value.HasValue && radNumericMinutes.Value.HasValue)
        {
            // Check time - make sure it's at least 5 minutes
            int totalRecordMinutes = (Int32.Parse(radNumericHours.Value.ToString()) * 60) + Int32.Parse(radNumericMinutes.Value.ToString());
            if (totalRecordMinutes == 0)
            {
                error = error + I18NHelper.I18NString("The record must have at least one minute!") + "<br />";
            }
        }

        bool billableService = false;
        if (RadComboBoxService != null && RadComboBoxService.SelectedValue != null && RadComboBoxService.SelectedValue.CompareTo("") != 0)
        {
            DSService dsServiceBillTest = BOService.getServiceByServiceIDAndClientID(Int32.Parse(RadComboBoxService.SelectedValue), getClientID());
            if (dsServiceBillTest != null && dsServiceBillTest.getDescription != null && dsServiceBillTest.getDescription.CompareTo(RadComboBoxService.Text) == 0)
            {
                billableService = true;
            }
        }

        if (radDatePicker.SelectedDate != null && !ActivityRecordsMisc.isActivityRecordWithinDayLimitRange(radDatePicker.SelectedDate.Value, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), billableService))
        {
            error = error + I18NHelper.I18NString("The record must be within the day limit range.");
        }

        if ( (radNumericTravel.Value != null && radNumericTravel.Value.HasValue) || (radNumericTravelHours.Value != null && radNumericTravelHours.Value.HasValue))
        {
            int totalRecordMinutes = 0;
            if (radNumericTravel.Value.HasValue && radNumericTravelHours.Value.HasValue)
            {
                // Check time - make sure it's at least 5 minutes
                totalRecordMinutes = (Int32.Parse(radNumericTravelHours.Value.ToString()) * 60) + Int32.Parse(radNumericTravel.Value.ToString());
            }
            if (totalRecordMinutes > 0)
            {
                if (Privilege.isPrivTrue(iAccess, Privileges7.TRAVEL_NOTES_TEXT_SHOW))
                {
                    if (Privilege.isPrivTrue(iAccess, Privileges7.TRAVEL_NOTES_TEXT_REQUIRED))
                    {
                        if (RadTextBoxTravelNotes.Text.Length == 0)
                        {
                            error = error + I18NHelper.I18NString("Travel notes are required if you have more than 0 minutes of travel time.") + "<br />";
                        }
                    }
                }
            }
        }
        
        
        var privs = getUserPrivs();
        if (Privilege.isPrivTrue(privs, Privileges17.CONSUMER_REQUIRED_FOR_CASE_MANAGEMENT_NOTES) && RadComboBoxConsumerWAI.SelectedValue == "" )
        {
            error = error + I18NHelper.I18NString("Consumer is required.") + "<br />";
        }



        setError(error);
        return error.CompareTo("") == 0;
    }

    protected void RadComboBoxConsumerWAI_ItemCreated(object sender, RadComboBoxItemEventArgs e)
    {

        if (e != null && e.Item != null && e.Item is RadComboBoxItem)
        {
            RadComboBoxItem dataItem = (e.Item as RadComboBoxItem);
            if (dataItem == null)
            {
                return;
            }

            String consumerID = dataItem.Value;
            if (dataItem.Value == null || dataItem.Value.Length == 0)
            {
                return;
            }

            if (((Image)dataItem.FindControl("consumerImage")) != null)
            {
                ((Image)dataItem.FindControl("consumerImage")).ImageUrl = "~/Home/Settings/Utilities/ProviderImageDownload2.ashx?Type=45&ConsumerID=" + consumerID + "&ClientID=" + getClientID().ToString();
            }
            if (((Label)dataItem.FindControl("lblFirstName")) != null && dataItem.Attributes["FirstName"] != null)
            {
                ((Label)dataItem.FindControl("lblFirstName")).Text = dataItem.Attributes["FirstName"].ToString();
            }
            if (((Label)dataItem.FindControl("lblLastName")) != null && dataItem.Attributes["LastName"] != null)
            {
                ((Label)dataItem.FindControl("lblLastName")).Text = dataItem.Attributes["LastName"].ToString();
            }
            if (((Label)dataItem.FindControl("lblDMH")) != null && dataItem.Attributes["DMH"] != null)
            {
                ((Label)dataItem.FindControl("lblDMH")).Text = dataItem.Attributes["DMH"].ToString();
            }
            if (((Label)dataItem.FindControl("lblStatus")) != null && dataItem.Attributes["Status"] != null)
            {
                ((Label)dataItem.FindControl("lblStatus")).Text = dataItem.Attributes["Status"].ToString();
            }
        }
    }

    protected void radDatePicker_SelectedDateChanged(object sender, Telerik.Web.UI.Calendar.SelectedDateChangedEventArgs e)
    {
        BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Action", "DATE CHANGE", getUsername(), Session.SessionID);
        long[] iAccess = getUserPrivs();
        if (radDatePicker == null || radDatePicker.SelectedDate == null || radDatePicker.SelectedDate.Value == null || radDatePicker.SelectedDate.Value.Date == null)
        {
            setError("Date of Service required");
            return;
        }
        else
        {
            setError("");
        }

        if (radDatePicker != null || radDatePicker.SelectedDate != null || radDatePicker.SelectedDate.Value != null || radDatePicker.SelectedDate.Value.Date != null)
        {
            if (radDatePicker.SelectedDate.HasValue && (radDatePicker.SelectedDate.Value.CompareTo(DateTime.Now.AddYears(1)) > 0 || radDatePicker.SelectedDate.Value.CompareTo(DateTime.Parse("01/01/2000")) < 0))
            {
                setError(I18NHelper.I18NString("Date of Service Out of Range"));
            }
        }

        if (isLateNote())
        {

            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
            {
                // creating a new note
                setLate("Appropriate documentation to support this record is available and has been filed.");
            }
            else
            {
                // updating a note
                if (Privilege.isPriv6True(iAccess, Privileges6.CASE_MANAGEMENT_SKIP_LATE_TRIGGER))
                {
                    // need to make sure that creator and modifier is NOT the same
                    if (HiddenBILLABLECAPAR_CREATOR_USER_ID.Value.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0)
                    {
                        // same user
                        setLate("Appropriate documentation to support this record is available and has been filed.");
                    }
                    else
                    {
                        // so update & islate & skip late trigger & not the same user
                        // ok, so different user, but if it was late before then it must stay late
                        DSActivityRecordAdditional dsARAdditionalExisting = BOActivityRecordAdditional.getActivityRecordAdditional(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getClientID().ToString());
                        if (dsARAdditionalExisting.IsLateNote)
                        {
                            setLate("Appropriate documentation to support this record is available and has been filed.");
                        }
                        else
                        {
                            setLate("");
                        }
                    }
                }
                else
                {
                    setLate("Appropriate documentation to support this record is available and has been filed.");
                }
            }

        }
        else
        {
            setLate("");
        }
    }

    private bool isLateNote()
    {

        bool retVal = false;
        long[] iAccess = getUserPrivs();
        string[] roles = BORoles.GetRolesByUserID(Guid.Parse(getUserID()));
        String userRole = roles[0];
        String cmDaysBeforeRecordIsLate = BOPrivilege.getPrivilegeValueByPrivilegeCodeAndClientID("CM_INT_DAYS_FOR_LATE_RECORD", getClientID().ToString(), userRole);

        if (cmDaysBeforeRecordIsLate != null && cmDaysBeforeRecordIsLate.Length > 0)
        {
            int daysLimit = 0;
            if (Int32.TryParse(cmDaysBeforeRecordIsLate, out daysLimit))
            {
                DateTime startTime = radDatePicker.SelectedDate.Value;
                DateTime relativeCurrentTime = TimeZoneProcessor.getLocalDateTime(getClientID().ToString());
                relativeCurrentTime = new DateTime(relativeCurrentTime.Year, relativeCurrentTime.Month, relativeCurrentTime.Day, 0, 0, 0);

                if (Privilege.isPrivTrue(iAccess, Privileges7.CM_INT_DAYS_FOR_LATE_RECORD_EXCLUDES_HOLIDAYS))
                {
                    daysLimit = BODateProcessor.getDateDiffHolidayOffset(getClientID().ToString(), relativeCurrentTime, daysLimit, true);
                }

                relativeCurrentTime = relativeCurrentTime.AddDays(daysLimit * -1);

                // staart time is less than relative current time
                if (startTime.CompareTo(relativeCurrentTime) < 0)
                {
                    BOTrace.setTrace(getClientID(), getUserID(), "CM_ISLATE_TRUE", "All relevant fields", radDatePicker.SelectedDate.Value.ToString() + "||||" + TimeZoneProcessor.getLocalDateTime(getClientID().ToString()).ToString() + "||||" +
                        relativeCurrentTime.ToString() + "||||" + startTime.ToString(), getUsername(), Session.SessionID);
                    retVal = true;
                }
                else
                {
                    retVal = false;
                    BOTrace.setTrace(getClientID(), getUserID(), "CM_ISLATE_FALSE", "All relevant fields", radDatePicker.SelectedDate.Value.ToString() + "||||" + TimeZoneProcessor.getLocalDateTime(getClientID().ToString()).ToString() + "||||" +
                        relativeCurrentTime.ToString() + "||||" + startTime.ToString(), getUsername(), Session.SessionID);
                }
            }
        }
        return retVal;
    }

    private void setLate(string _late)
    {
        if (_late == "")
        {
            lateRecordDiv.Style.Add("display", "none");
            checkboxDocumentationAvailable.Text = _late;
        }
        else
        {
            lateRecordDiv.Style.Add("display", "block");
            checkboxDocumentationAvailable.Text = _late;
        }
    }

    protected void radNumericHours_TextChanged(object sender, EventArgs e)
    {
        if (radNumericTravel == null || radNumericTravel.Value == null || !radNumericTravel.Value.HasValue)
        {
            return;
        }
        if (radNumericTravelHours == null || radNumericTravelHours.Value == null || !radNumericTravelHours.Value.HasValue)
        {
            return;
        }
        if (radNumericHours == null || radNumericHours.Value == null || !radNumericHours.Value.HasValue)
        {
            return;
        }
        if (radNumericMinutes == null || radNumericMinutes.Value == null || !radNumericMinutes.Value.HasValue)
        {
            return;
        }

        if ((radNumericMinutes.Value + radNumericTravel.Value) < 60)
        {
            lblDurationTotal.Text = (radNumericHours.Value + radNumericTravelHours.Value) + " hours and " + (radNumericMinutes.Value + radNumericTravel.Value) + " minutes.";
        }
        else
        {
            lblDurationTotal.Text = (radNumericHours.Value + radNumericTravelHours.Value  + 1) + " hours and " + ((radNumericMinutes.Value + radNumericTravel.Value) - 60) + " minutes.";
        }
    }
    protected void LinkButtonDuplicate_Click(object sender, EventArgs e)
    {
        BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Action", "DUPLICATE", getUsername(), Session.SessionID);
        initializeCreateView(true);

        if (!HiddenAUTOSAVEID.Value.Equals("False"))
        {
            BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
            HiddenAUTOSAVEID.Value = "False";
            autosaveData();
        }
    }
    protected void LinkButtonSaveAndDuplicate_Click(object sender, EventArgs e)
    {
        BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Action", "SAVE_AND_DUPLICATE", getUsername(), Session.SessionID);
        if (saveOrUpdateRecord(false))
        {
            initializeCreateView(true);

            if (!HiddenAUTOSAVEID.Value.Equals("False"))
            {
                BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
                HiddenAUTOSAVEID.Value = "False";
                autosaveData();
            }
        }
    }
    protected void RadComboBoxService_SelectedIndexChanged(object sender, RadComboBoxSelectedIndexChangedEventArgs e)
    {
        BOTrace.setTrace(getClientID(), getUserID(), "CaseManagementNote.aspx", "CM Note Action", "SERVICE CHANGE", getUsername(), Session.SessionID);

        if (RadTextBoxAdditionalComments.Text.Length == 0)
        {
            if (RadComboBoxService.SelectedValue != null && RadComboBoxService.SelectedValue.Length > 0)
            {
                DSService dsService = BOService.getServiceByServiceIDAndClientID(Int32.Parse(RadComboBoxService.SelectedValue), getClientID());
                if (dsService != null && dsService.getDefaultContent != null)
                {
                    if (!String.IsNullOrEmpty(dsService.getDefaultContent))
                    {
                        RadTextBoxAdditionalComments.Text = dsService.getDefaultContent;
                    }

                    if (!String.IsNullOrEmpty(dsService.GetEmptyContent))
                    {
                        RadTextBoxAdditionalComments.EmptyMessage = dsService.GetEmptyContent;
                    }
                }
                else
                {
                    // must be non billable
                    DSNonBillable2 nonBillable2 = BONonBillable.GetNonBillableByID(Int32.Parse(RadComboBoxService.SelectedValue), getClientID());
                    if (nonBillable2 != null && nonBillable2.DefaultContent != null && nonBillable2.DefaultContent.Length > 0)
                    {
                        RadTextBoxAdditionalComments.Text = nonBillable2.DefaultContent;
                    }
                }
            }
        }
    }
    protected string FormatUserName(object userName)
    {
        string sUserName = userName as string;
        return String.IsNullOrEmpty(sUserName) ? String.Empty : String.Join("<br />", sUserName.Split(new char[]{',', ' '}, StringSplitOptions.RemoveEmptyEntries).Reverse());
    }

    protected void RadAjaxManagerBillableWAI_AjaxRequest(object sender, AjaxRequestEventArgs e)
    {
        if (e.Argument == "Autosave")
        {
            autosaveData();
        }
    }

    private void autosaveData()
    {
        String blob = assembleAutosaveDataBlob();

        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
        {
            if (HiddenAUTOSAVEID.Value.Equals("False"))
            {
                int autosaveID = BOAutosavedData.CreateAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value), 0, "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername(), DateTime.UtcNow);
                HiddenAUTOSAVEID.Value = autosaveID.ToString();
            }
            else
            {
                BOAutosavedData.SetAutosavedData(Int32.Parse(HiddenAUTOSAVEID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value),
                    0, "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername());
            }
        }
        else
        {
            if (!HiddenAUTOSAVEID.Value.Equals("False"))
            {
                BOAutosavedData.SetAutosavedData(Int32.Parse(HiddenAUTOSAVEID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value),
                    Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername());
            }
            else
            {
                DSAutosavedData autosavedData = BOAutosavedData.GetAutosavedDataByEntityNameAndEntityID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), "ACTIVITY_RECORD_ID", Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

                if (autosavedData == null)
                {
                    int autosaveID = BOAutosavedData.CreateAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value), 0, "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername(), DateTime.UtcNow);
                    HiddenAUTOSAVEID.Value = autosaveID.ToString();
                }
                else
                {
                    HiddenAUTOSAVEID.Value = autosavedData.AutosavedDataID.ToString();

                    BOAutosavedData.SetAutosavedData(Int32.Parse(HiddenAUTOSAVEID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value),
                        Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername());
                }
            }
        }
    }

    private String assembleAutosaveDataBlob()
    {
        String blob = "<b>Case Management Note</b><br />";

        if (radDatePicker.SelectedDate != null)
            blob += "<u>Service Date:</u> " + radDatePicker.SelectedDate.Value.ToShortDateString() + "<br />";

        if (RadComboBoxConsumerWAI.SelectedItem != null)
            blob += "<u>Consumer:</u> " + RadComboBoxConsumerWAI.SelectedItem.Text + "<br />";

        blob += "<u>Duration:</u> " + lblDurationTotal.Text + "<br />";

        if (RadComboBoxService.SelectedItem != null)
            blob += "<u>Service:</u> " + RadComboBoxService.SelectedItem.Text + "<br />";

        if (RadComboBoxPersonSeen.CheckedItems.Count == 0)
            blob += "<u>Person Seen:</u> N/A <br />";
        else
        {
            blob += "<u>Person Seen:</u> ";

            foreach (RadComboBoxItem item in RadComboBoxPersonSeen.CheckedItems)
            {
                blob += item.Text + ", ";
            }

            blob += "<br />";
        }

        if (RadComboBoxPlaceOfService.SelectedItem != null)
            blob += "<u>Place of service:</u> " + RadComboBoxPlaceOfService.SelectedItem.Text + "<br />";

        blob += "<u>Designated Therapist:</u> " + checkBoxDesignatedTherapist.Checked + "<br />";
        blob += "<u>Include on Monthly / Quarterly:</u> " + checkBoxMonthlyQuarterly.Checked + "<br />";
        blob += "<u>Disclosure:</u> " + checkBoxDisclosure.Checked + "<br />";

        blob += "<u>GenericBitFlag1:</u> " + checkBoxGenericBitFlag1.Checked + "<br />";
        blob += "<u>GenericBitFlag2:</u> " + checkBoxGenericBitFlag2.Checked + "<br />";

        blob += "<br /><u>Notes:</u><br />" + RadTextBoxAdditionalComments.Text + "<br />";

        blob += "<br /><u>Travel Notes:</u><br />" + RadTextBoxTravelNotes.Text + "<br />";

        return blob;
    }
    protected void LinkButtonClose_Click(object sender, EventArgs e)
    {
        if (!HiddenAUTOSAVEID.Value.Equals("False"))
            BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
    }

    protected void disableARfields(bool disableARWindow)
    {
        RadSpellNonBillable.Visible = !disableARWindow;
        RadComboBoxDepartment.Enabled = !disableARWindow;
        RadComboBoxConsumerWAI.Enabled = !disableARWindow;
        radDatePicker.Enabled = !disableARWindow;
        radNumericHours.Enabled = !disableARWindow;
        radNumericMinutes.Enabled = !disableARWindow;
        radNumericTravel.Enabled = !disableARWindow;
        radNumericTravelHours.Enabled = !disableARWindow;
        RadComboBoxService.Enabled = !disableARWindow;
        RadComboBoxPersonSeen.Enabled = !disableARWindow;
        RadComboBoxPlaceOfService.Enabled = !disableARWindow;
        checkBoxDesignatedTherapist.Enabled = !disableARWindow;
        checkBoxMonthlyQuarterly.Enabled = !disableARWindow;
        checkBoxDisclosure.Enabled = !disableARWindow;
        checkboxDocumentationAvailable.Enabled = !disableARWindow;
        checkBoxGenericBitFlag1.Enabled = !disableARWindow;
        checkBoxGenericBitFlag2.Enabled = !disableARWindow;

        RadTextBoxAdditionalComments.ReadOnly = disableARWindow;
        RadTextBoxTravelNotes.ReadOnly = disableARWindow;
        LinkButtonUpdate.Visible = !disableARWindow;
        HiddenEXPENSEDISABLECAP.Value = disableARWindow.ToString();
    }
    
    private LinkButton createAutoReviewButton()
    {
        LinkButton autoReviewButton = new LinkButton();
        autoReviewButton.ID = "auto-review-button";
        autoReviewButton.Text = "Auto-review";
        autoReviewButton.CssClass = "auto-review-button";
        autoReviewButton.Click += AutoReviewButton_Click;
        autoReviewButton.CausesValidation = false;
        return autoReviewButton;
    }
    
    private LinkButton createCancelIcon()
    {
        LinkButton cancelIcon = new LinkButton();
        cancelIcon.ID = "auto-review-cancel-icon";
        cancelIcon.Text = "✖"; 
        cancelIcon.CssClass = "cancel-icon"; 
        cancelIcon.Style.Add("color", "#fb4040"); 
        cancelIcon.Style.Add("font-size", "15px");
        cancelIcon.Style.Add("text-decoration", "none"); 
        cancelIcon.Style.Add("border", "none"); 
        cancelIcon.Style.Add("background", "none"); 
        cancelIcon.Style.Add("cursor", "pointer"); 
        cancelIcon.Style.Add("outline", "none"); 
        cancelIcon.Click += CancelIcon_Click;
        cancelIcon.OnClientClick = "removeAutoReview()";
        return cancelIcon;
    }
    
    protected void CancelIcon_Click(object sender, EventArgs e)
    {
        LinkButton cancelIcon = (LinkButton)sender;
        var autoReviewID = "auto-review-container";
        var autoReviewContainer = (HtmlGenericControl)getControl(autoReview, autoReviewID);
        var autoReviewFooterLabel = (Label)getControl(autoReviewContainer,"auto-review-footer-label");
        
        if (autoReviewContainer != null)
        {
            autoReviewContainer.Style["display"] = "none";
        }

        if (autoReviewFooterLabel != null)
        {
            autoReviewFooterLabel.Style["display"] = "none";
        }
    }
    
    private Control getControl(HtmlGenericControl _parentControl, String _IDOfControlToFind)
    {
        foreach (Control control in _parentControl.Controls)
        {
            if (control.ID == _IDOfControlToFind)
            {
                return control;
            }
        }

        return null;
    }

    private HtmlGenericControl createAutoReviewContainer()
    {
        HtmlGenericControl autoReviewContainer = new HtmlGenericControl("div")
        {
            ID = $"auto-review-container"
        };
        autoReviewContainer.Attributes["class"] = "auto-review-container";
        autoReviewContainer.Attributes["review-container-id"] = autoReviewContainer.ID;
        return autoReviewContainer;
    }
    private RadLabel CreateAutoReviewRadLabel(string autoReviewID)
    {
        RadLabel autoReviewLabel = new RadLabel
        {
            ID = autoReviewID,
            Text = "", 
            EnableViewState = true
        };
        
        autoReviewLabel.CssClass = "auto-review-textbox";
        autoReviewLabel.Style["height"] = "300px";
        autoReviewLabel.Style["overflow-y"] = "auto";
        autoReviewLabel.Style["resize"] = "none";
        return autoReviewLabel;
    }

    private Label createAutoReviewHeaderLabel()
    {
        Label autoReviewLabel = new Label
        {
            ID = "auto-review-header-label",
            Text = "AI Assistant Review: Use this to improve your comment."
        };
        autoReviewLabel.Attributes["class"] = "auto-review-header-label";
        return autoReviewLabel;
    }
    
    private Label createAutoReviewFooterLabel()
    {
        Label autoReviewLabel = new Label
        {
            ID = "auto-review-footer-label",
            Text = "The AI Assistant may make mistakes. Please verify your work."
        };
        autoReviewLabel.Attributes["class"] = "auto-review-footer-label";
        autoReviewLabel.Attributes["originalID"] = "auto-review-footer-label"; 
        return autoReviewLabel;
    }
    
    protected void AutoReviewButton_Click(object sender, EventArgs e)
    {
        BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, 
            "AI_ASSISTANT_ACTIVITY_RECORDS_CASE_MANAGEMENT_NOTE_AUTO_REVIEW_START", 
            HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), "No Session");

        var autoReviewButton = (LinkButton) sender;
        if (autoReviewButton == null)
            return;
        
        var autoReviewID = "auto-review-container";
        var autoReviewContainer = (HtmlGenericControl)getControl(autoReview, autoReviewID);
        if (autoReviewContainer == null)
            return;

        var autoReviewTextBoxID = "auto-review-textbox";
        var autoReviewTextBox = (RadLabel)getControl(autoReviewContainer, autoReviewTextBoxID);
        if (autoReviewTextBox == null)
            return;
        
        var autoReviewFooter = (Label)getControl(autoReviewContainer, "auto-review-footer-label");
        autoReviewFooter.Attributes.Add("style", "max-width:546px;display:block;padding-left:5px;position:relative;margin-bottom:5px;");
        HiddenShowAIAssistantTextBox.Value = "True";
        autoReviewContainer.Style["display"] = "flex !important";
        autoReviewContainer.Style["width"] = "100% !important";
        autoReviewTextBox.Style["width"] = "100%";
        
        var consumerId = 0;
        var consumerName = "";
        var activityRecordsId = 0;
        if (!string.IsNullOrEmpty(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value))
        {
            activityRecordsId = int.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        }
                
        if (activityRecordsId == 0 && !string.IsNullOrEmpty(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value))
        {
            activityRecordsId = int.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        }
        
        var comment = RadTextBoxAdditionalComments.Text;
        
        if (!string.IsNullOrEmpty(comment))
        {
            try
            {
                _aiSystemConfiguration.ClientID = getClientID();
                _aiSystemConfiguration.ServiceID = string.IsNullOrEmpty(RadComboBoxService.SelectedValue) ? 0 : Convert.ToInt32(RadComboBoxService.SelectedValue);
                
                consumerId = !string.IsNullOrEmpty(RadComboBoxConsumerWAI.SelectedValue) ? int.Parse(RadComboBoxConsumerWAI.SelectedValue) : consumerId;
                if (consumerId != 0)
                    consumerName = BOConsumer.getConsumerNameById(getClientID(), consumerId, false, false, true);
                if (!string.IsNullOrEmpty(consumerName))
                {
                    comment += $". The name of the individual receiving the service is {consumerName}. ";
                }
                _aiSystemConfiguration.UserMessageContext = comment;
                _aiSystemConfiguration.Entity = SetWorks.Common.Tools.SETWorksAI.EntityType.ACTIVITYRECORDS_CASE_MANAGEMENT_NOTE_COMMENTS;  

                _aAiConfiguration.SystemConfiguration = _aiSystemConfiguration;
                _aiClient.Config = _aAiConfiguration;
                _aiClient.MakeChatCompletionRequest();
                autoReviewTextBox.Text = _aiClient.GetChatResponseHtml();
                
                if (activityRecordsId != 0)
                {
                    BOAIAssistantActivityRecordChatHistory.addOrUpdateAIAssistantActivityRecordChatHistory(
                        _aiSystemConfiguration.ClientID, 
                        Guid.Parse(getUserID()), 
                        _aiSystemConfiguration.Entity.ToString(), 
                        _aiSystemConfiguration.getAIAssistantSystemPrompt(),
                        _aiSystemConfiguration.getAIAssistantUserPrompt(), 
                        _aiClient.GetChatResponsePlainText(),
                        consumerId, 
                        0, 
                        0, 
                        activityRecordsId,
                        0,
                        0,
                        _aiSystemConfiguration.ServiceID);
                }
            }
            catch (Exception ex)
            {
                var errorDashboardContext = $"AI Assistant Activity Records Case Management Note Error: " + ex.Message;
                BOError.setError(getClientID(), getUserID().ToString(), "AI_ASSISTANT_ACTIVITY_RECORDS_CASE_MANAGEMENT_NOTE_AUTO_REVIEW_ERROR", 
                    ex.StackTrace, "No Session", $"Case Management Note: {activityRecordsId}, " +
                                                 $"Consumer: {consumerId}", errorDashboardContext, ex.GetType().Name);
                autoReviewTextBox.Text =  "AI Assistant encountered an issue while reviewing this comment.";
            }
        }
        else
        {
            autoReviewTextBox.Text =  "There is no comment for the AI Assistant to review.";                  
        }

        BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, 
            "AI_ASSISTANT_ACTIVITY_RECORDS_CASE_MANAGEMENT_NOTE_AUTO_REVIEW_END", HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, 
            getUsername(), "No Session");
    }
    
    private void CreateAutoReviewControls(Control parentControl)
    {
        LinkButton autoReviewButton = createAutoReviewButton();
        autoReviewButton.EnableViewState = false;
        autoReviewButton.Attributes["PARENT_CONTAINER_ID"] = parentControl.ID;
        parentControl.Controls.Add(autoReviewButton);
                
        var autoReviewContainer = createAutoReviewContainer();        
        var cancelIcon = createCancelIcon();
        autoReviewContainer.Controls.Add(cancelIcon);
        var aiReviewHeaderLabel = createAutoReviewHeaderLabel();
        autoReviewContainer.Controls.Add(aiReviewHeaderLabel);
                
        var autoReviewTextBoxID = "auto-review-textbox";
        var autoReviewTextBox = CreateAutoReviewRadLabel(autoReviewTextBoxID);
        autoReviewContainer.Controls.Add(autoReviewTextBox);
                
        var aiReviewFooterLabel = createAutoReviewFooterLabel();
        autoReviewContainer.Controls.Add(aiReviewFooterLabel);
        
        autoReviewContainer.Style["display"] = "none";
        
        parentControl.Controls.Add(autoReviewContainer);
        
        ajaxify(autoReviewButton.ID, parentControl.ID);
        ajaxify(autoReviewButton.ID, HiddenShowAIAssistantTextBox.ID);
        ajaxify(autoReviewButton.ID, autoReviewTextBox.ID);
        ajaxify(autoReviewButton.ID, autoReviewContainer.ID);
        ajaxify(autoReviewButton.ID, RadTextBoxAdditionalComments.ID);
        ajaxify(cancelIcon.ID, parentControl.ID);
        ajaxify(cancelIcon.ID, autoReviewTextBox.ID);
        ajaxify(cancelIcon.ID, autoReviewContainer.ID);
        ajaxify(autoReviewContainer.ID, parentControl.ID);
    }
    
    private void ajaxify(String _sourceControlID, params String[] _toAjaxifyControlIDs)
    {
        AjaxSetting setting = new AjaxSetting(_sourceControlID);

        foreach (String toAjaxifyControlID in _toAjaxifyControlIDs)
        {
            AjaxUpdatedControl ajaxifyControl = new AjaxUpdatedControl(toAjaxifyControlID, "RadAjaxLoadingPanel1WAI");
            ajaxifyControl.UpdatePanelRenderMode = UpdatePanelRenderMode.Block;
            setting.UpdatedControls.Add(ajaxifyControl);
        }

        RadAjaxManagerBillableWAI.AjaxSettings.Add(setting);
    }
}

