using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI.WebControls;
using BOs;
using SW.UI;
using Telerik.Web.UI;
using Telerik.Web.UI.Calendar;


public partial class Home_EDIBilling_ManageAccounting : SWPage
{
    protected void Page_Init(object sender, EventArgs e)
    {
    }
    
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            long[] iAccess = getUserPrivs();
            if (!Privilege.isPrivTrue(iAccess, Privileges18.ACCESS_MANAGE_ACCOUNTS_RECEIVABLE))
            {
                Response.Redirect("~/Home/Home.aspx", true);
            }

            int clientId = getClientID();
            HiddenCURRENTWINDOWCLIENTID.Value = clientId.ToString();
            HiddenCURRENTWINDOWUSERID.Value = getUserID();

            SetSiteLabels();
            SetDateRange(DateTime.UtcNow, out var fromDate, out var toDate);

            RadDatePickerFromDate.SelectedDate = fromDate;
            RadDatePickerToDate.SelectedDate = toDate;

            DateTime maxServiceEndDate = GetMaxServiceEndDate(clientId);
            ViewState["MaxServiceEndDate"] = maxServiceEndDate;
            ViewState["ShouldBindGrid"] = true;
            if (HasOutstandingAccountingPeriodRequest(clientId)) RadGridAccountingPeriod.MasterTableView.CommandItemSettings.ShowAddNewRecordButton = false;
        }
    }

    private void SetDateRange(DateTime baseDate, out DateTime fromDate, out DateTime toDate)
    {
        var iAccess = getUserPrivs();
        if (!String.IsNullOrEmpty(HiddenBATCHID.Value))
        {
            var batch = BOBatch.getBatchByBatchID(HiddenBATCHID.Value);
            if (batch != null)
            {
                var batchDate = batch.getUpdateTimestamp;
                fromDate = batchDate.AddMonths(-2);
                toDate = batchDate.AddDays(14);
                return;
            }
        } else if (Privilege.isPrivTrue(iAccess, Privileges17.DEFAULT_MANAGE_CLAIMS_TO_ONE_DAY))
        {
            fromDate = baseDate;
            toDate = baseDate;
            return;
        }
        fromDate = new DateTime(baseDate.Year, baseDate.Month, 1);
        toDate = fromDate.AddMonths(1).AddDays(-1);
    }
    
    protected void radDatePicker_EnforceMonthDateRange(object sender, SelectedDateChangedEventArgs e)
    {
        ViewState["ShouldBindGrid"] = false;
        if (!(sender is RadDatePicker datePicker)) return;

        RadDatePicker otherPicker = datePicker == RadDatePickerFromDate ? RadDatePickerToDate : RadDatePickerFromDate;

        DateTime? newDate = e.NewDate;
        DateTime? otherDate = otherPicker.SelectedDate;

        if (!newDate.HasValue || !otherDate.HasValue) return;

        int dateDiff = (newDate.Value - otherDate.Value).Days;

        if (Math.Abs(dateDiff) <= 30) return;

        otherPicker.SelectedDate = datePicker == RadDatePickerFromDate
            ? newDate.Value.AddMonths(1)
            : newDate.Value.AddMonths(-1);
    }

    
    protected void RadComboBoxConsumer_OnDataBound(object sender, EventArgs e)
    {
        ViewState["ShouldBindGrid"] = false;
        RadComboBoxConsumer.Items.Insert(0, new RadComboBoxItem("ALL", "0"));
        RadComboBoxConsumer.SelectedValue = "0";
    }
    
    private void SetSiteLabels()
    {
        var consumerSiteLabelSingular =
            BOSiteLabel.getSiteLabelSingularDescriptionByClientIDAndCode(HiddenCURRENTWINDOWCLIENTID.Value,
                "CONSUMER_DESCRIPTION"); 
        lblConsumerDropdown.Text = consumerSiteLabelSingular;
        lblConsumerStatus.Text = consumerSiteLabelSingular + " Status";
        RadComboBoxConsumer.EmptyMessage = "Select " + consumerSiteLabelSingular;
        RadComboBoxConsumer.ToolTip = "Select a " + consumerSiteLabelSingular;
        RadGridAccountsReceivable.Columns.FindByUniqueName("Consumer").HeaderText = consumerSiteLabelSingular;

    }

    protected void btnApplyFilters_OnClick(object sender, EventArgs e)
    {
        ViewState["ShouldBindGrid"] = true;
        RadGridAccountsReceivable.Rebind();
    }

    protected void RadGridClaims_OnNeedDataSource(object sender, GridNeedDataSourceEventArgs e)
    {
        if (ViewState["ShouldBindGrid"] != null && (bool)ViewState["ShouldBindGrid"])
        {
            // RadGridAccountsReceivable.DataSource = sqlClientAR;
        }
    }

    protected void RadGridAccountsReceivable_DetailTableDataBind(object sender, GridDetailTableDataBindEventArgs e)
    {
        GridDataItem dataItem = (GridDataItem)e.DetailTableView.ParentItem;
        GridDataItem parentItem = (GridDataItem)dataItem.OwnerTableView.ParentItem;
        switch (e.DetailTableView.Name)
        {
            case "ConsumerLevel":
                {
                    SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["SETPROFESSIONALConnectionString"].ConnectionString);
                    try
                    {
                        SqlDataAdapter adapter = new SqlDataAdapter();
                        SqlCommand cmd = new SqlCommand("[ACCOUNTS_RECEIVABLE.getARSummedByClientID_1.0.1]", conn);
                        cmd.CommandTimeout = 3600;
                        adapter.SelectCommand = cmd;

                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@Client_ID", SqlDbType.VarChar).Value = HiddenCURRENTWINDOWCLIENTID.Value;
                        cmd.Parameters.Add("@FromDate", SqlDbType.DateTime).Value = RadDatePickerFromDate.SelectedDate.Value;
                        cmd.Parameters.Add("@ToDate", SqlDbType.DateTime).Value = RadDatePickerToDate.SelectedDate.Value;
                        cmd.Parameters.Add("@ConsumerID", SqlDbType.VarChar).Value = dataItem.GetDataKeyValue("ConsumerID").ToString();
                        cmd.Parameters.Add("@AccountsFilterMode", SqlDbType.VarChar).Value = RadComboBoxARFilterMode.SelectedValue;
                        cmd.Parameters.Add("@Query", SqlDbType.VarChar).Value = RadSearchTextBoxQuery.Text;
                        cmd.Parameters.Add("@PaymentApplicationMode", SqlDbType.VarChar).Value = RadCheckBoxPaymentApplicationMode.SelectedValue;


                        DataTable myDataTable = new DataTable();
                        conn.Open();

                        adapter.Fill(myDataTable);
                        e.DetailTableView.DataSource = myDataTable;
                    }
                    finally
                    {
                        conn.Close();
                    }
                    break;
                }
            case "TransactionLevel":
                {
                    SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["SETPROFESSIONALConnectionString"].ConnectionString);
                    try
                    {
                        SqlDataAdapter adapter = new SqlDataAdapter();
                        SqlCommand cmd = new SqlCommand("[ACCOUNTS_RECEIVABLE.getTransactionsForAR_1.0.1]", conn);
                        cmd.CommandTimeout = 3600;
                        adapter.SelectCommand = cmd;

                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@Client_ID", SqlDbType.VarChar).Value = HiddenCURRENTWINDOWCLIENTID.Value;
                        cmd.Parameters.Add("@CombinedKey", SqlDbType.VarChar).Value = dataItem.GetDataKeyValue("CombinedKey").ToString();
                        cmd.Parameters.Add("@Date", SqlDbType.DateTime).Value = dataItem.GetDataKeyValue("Date").ToString();
                        cmd.Parameters.Add("@ConsumerID", SqlDbType.VarChar).Value = parentItem.GetDataKeyValue("ConsumerID").ToString();
                        cmd.Parameters.Add("@PaymentApplicationMode", SqlDbType.VarChar).Value = RadCheckBoxPaymentApplicationMode.SelectedValue;

                        DataTable myDataTable = new DataTable();
                        conn.Open();

                        adapter.Fill(myDataTable);
                        e.DetailTableView.DataSource = myDataTable;
                        
                    }
                    finally
                    {
                        conn.Close();
                    }
                    break;
                }
        }
    }
    
    protected void CustomValidatorServiceEndDate_ServerValidate(object source, ServerValidateEventArgs args)
    {
        DateTime selectedDate = DateTime.Parse(args.Value);
        DateTime maxServiceEndDate = (DateTime)ViewState["MaxServiceEndDate"];
        args.IsValid = selectedDate >= maxServiceEndDate;
    }
    
    private DateTime GetMaxServiceEndDate(int clientId)
    {
        DateTime maxServiceEndDate = DateTime.MinValue; // Default to the earliest possible date if no records are found

        string connectionString = ConfigurationManager.ConnectionStrings["READONLY_DSWPROD"].ConnectionString;
        string query = "SELECT MAX(ServiceEndDate) FROM AccountingPeriod WHERE Client_ID = @Client_ID";

        using (SqlConnection connection = new SqlConnection(connectionString))
        {
            SqlCommand command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@Client_ID", clientId);

            connection.Open();
            object result = command.ExecuteScalar();

            if (result != DBNull.Value && result != null)
            {
                maxServiceEndDate = Convert.ToDateTime(result);
            }
        }

        return maxServiceEndDate;
    }
    
    private bool HasOutstandingAccountingPeriodRequest(int clientId)
    {
        bool exists = false;

        string connectionString = ConfigurationManager.ConnectionStrings["READONLY_DSWPROD"].ConnectionString;
        string query = "SELECT COUNT(1) FROM AccountingPeriod WHERE Client_ID = @Client_ID AND Status = 'Requested'";

        using (SqlConnection connection = new SqlConnection(connectionString))
        {
            SqlCommand command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@Client_ID", clientId);

            connection.Open();
            int count = (int)command.ExecuteScalar();

            exists = count > 0;
        }

        return exists;
    }

    protected void RadGridAccountingPeriod_OnInsertCommand(object sender, GridCommandEventArgs e)
    {
        if (e.Item is GridEditableItem)
        {
            RadDatePicker radDatePickerServiceEndDate = (RadDatePicker)e.Item.FindControl("RadDatePickerServiceEndDate");
            RadTextBox radTextBoxDescription = (RadTextBox)e.Item.FindControl("RadTextBoxDescription");

            DateTime? serviceEndDate = radDatePickerServiceEndDate.SelectedDate;
            string description = radTextBoxDescription.Text.Trim();

            string clientId = HiddenCURRENTWINDOWCLIENTID.Value;
            Guid userId = Guid.Parse(HiddenCURRENTWINDOWUSERID.Value);

            using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["SETPROFESSIONALConnectionString"].ConnectionString))
            {
                using (SqlCommand cmd = new SqlCommand("[ACCOUNTING_PERIOD.insertIntoAccountingPeriod_1.0.0]", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Client_ID", clientId);
                    cmd.Parameters.AddWithValue("@User_ID", userId);
                    cmd.Parameters.AddWithValue("@Description", description);
                    cmd.Parameters.AddWithValue("@ServiceEndDate", serviceEndDate.HasValue ? (object)serviceEndDate.Value : DBNull.Value);

                    conn.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }
    }
}