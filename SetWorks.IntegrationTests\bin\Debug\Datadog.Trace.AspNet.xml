<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Datadog.Trace.AspNet</name>
    </assembly>
    <members>
        <member name="T:Datadog.Trace.AspNet.HttpApplicationStartup">
            <summary>
            Helper class use to register the <see cref="T:Datadog.Trace.AspNet.TracingHttpModule"/> into the ASP.NET pipeline.
            </summary>
        </member>
        <member name="M:Datadog.Trace.AspNet.HttpApplicationStartup.Register">
            <summary>
            Registers the <see cref="T:Datadog.Trace.AspNet.TracingHttpModule"/> into the ASP.NET pipeline.
            </summary>
            <remarks>This method replaces <see cref="P:Datadog.Trace.Tracer.Instance"/> with a new <see cref="T:Datadog.Trace.Tracer"/> instance.</remarks>
        </member>
        <member name="T:Datadog.Trace.AspNet.TracingHttpModule">
            <summary>
                IHttpModule used to trace within an ASP.NET HttpApplication request
            </summary>
        </member>
        <member name="M:Datadog.Trace.AspNet.TracingHttpModule.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Datadog.Trace.AspNet.TracingHttpModule" /> class.
            </summary>
        </member>
        <member name="M:Datadog.Trace.AspNet.TracingHttpModule.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Datadog.Trace.AspNet.TracingHttpModule" /> class.
            </summary>
            <param name="operationName">The operation name to be used for the trace/span data generated</param>
        </member>
        <member name="M:Datadog.Trace.AspNet.TracingHttpModule.Init(System.Web.HttpApplication)">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.AspNet.TracingHttpModule.Dispose">
            <inheritdoc />
        </member>
    </members>
</doc>
