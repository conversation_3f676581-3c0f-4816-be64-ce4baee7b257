<%@ Page Title="" Language="C#" MasterPageFile="~/Masters/MasterEmpty.master" AutoEventWireup="true" Inherits="Home_ActivityRecords_ActivityRecordTypes_ARUnitProductionPage"
    ValidateRequest="false" Codebehind="ARUnitProductionPage.aspx.cs" EnableSessionState="False" %>


<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolderEmpty" runat="Server">
    <telerik:RadCodeBlock ID="RadCodeBlockBillableWAI" runat="server">
        <script src="../../../../Scripts/radcomboboxfix.js" type="text/javascript"></script>
        <link rel="stylesheet" href="../../../../SetWorks.css" />
        <style type="text/css">
            body {
                background: #EEEEEE;
            }

            .rscLinkImg {
                background-image: url( "https://app.set-works.com/SW/images/spellcheck.png" );
                background-repeat: no-repeat;
                text-indent: -9999px;
                float: left;
                text-align: center;
                font: bold 12px Arial, Verdana, Sans-serif;
                line-height: 21px;
                text-decoration: none;
                width: 21px;
            }

            .sectionDIV {
                background: #CBCBCB;
                padding: 5px 5px 5px 5px;
                max-width: 448px;
                height: 100px;
                font-size: 20px;
                border: 0;
                margin: 0px -14px 15px -14px;
            }

            .windowFieldset {
                /*margin-left:0px !important;
            padding-left:0px !important;
            margin-right:0px !important;
            padding-right:0px !important;*/
            }

            .labelComment {
                display: block;
                margin-bottom: 5px;
            }

            .labelCommentValue, .labelGoalCommentValue {
                display: block;
                background: #F5f5f5;
                padding: 5px 5px 5px 5px;
                margin-right: 0px;
                min-height: 20px;
                border: 1px solid #DDDDDD;
                color: #555555;
                max-width: 540px;
                width: 100%;
                margin-bottom: 5px;
            }

            .labelGoalCommentValue {
                /*width:383px;*/
            }

            .comment-header-label {
                font-size: 16px;
                margin-bottom: 10px;
            }

            .comment-header-label, .comment-consumer-header-label {
                display: block;
                font-weight: normal;
                max-width: 550px;
            }

            .comment-consumer-header-label {
                max-width: 550px;
                font-size: 14px;
            }

            .comment-overall-goals-label {
                font-size: 18px;
                font-weight: bold;
                border-bottom: 1px solid #999999;
                margin-bottom: 5px;
                max-width: 550px;
            }

            .consumer-outcomegoal-header-label {
                font-size: 16px;
                font-weight: bold;
            }

            .consumer-outcomegoal-label {
                font-size: 14px;
                font-weight: bold;
            }

            .edit-button {
                padding-right: 10px;
                padding-top: 5px;
            }

            .add-button {
                text-align: left;
                float: left;
                padding-right: 10px;
            }

            .comment-by-label {
                font-size: 12px;
                background: #DDDDDD;
                padding-top: 5px;
                padding-bottom: 5px;
                padding-left: 5px;
                padding-right: 5px;
                border: 1px solid #DDDDDD;
                margin-bottom: -5px;
                max-width: 550px;
                /*border:1px solid #DDDDDD;*/
            }

            .comment-by-label, .labelCommentValue, .labelGoalCommentValue, .sectionDIV, .UserCommentDiv {
                min-width: 350px;
            }

            .no-comments-label {
                font-size: 12px;
                background: #EEEEEE;
                padding-top: 4px;
                padding-bottom: 4px;
                padding-left: 5px;
                margin-bottom: 4px;
                margin-left: 5px;
            }

            .ARKeyCheckbox label[type=checkbox] {
            }

            .ARKeyCheckbox {
                margin-right: 10px;
            }

            .spanSep {
                margin-right: 7px;
            }

            .ARKeyNumericTextBox {
                border: 1px solid #333333;
            }

                .ARKeyNumericTextBox label {
                    margin-right: 0px;
                    padding-right: 0px;
                    font-weight: bold;
                    padding-bottom: 5px;
                    border: 1px solid #333333;
                }

            .ARKeyCheckbox input {
                vertical-align: bottom;
                padding-left: 5px;
                margin-left: 5px;
                border: 1px solid #333333;
            }

            .UserCommentDIV {
                background: #EEEEEE;
            }

            .comment-by-add-button {
                /*border:1px #CCCCCC solid;*/
                border: 2px dotted #CCCCCC !important;
                /*background:#E2F1BC;*/ /*#D0E0EB;*/
                padding: 15px 5px 15px 5px;
                margin: 5px 0px 0px 0px;
                min-width: 120px;
                text-decoration: none;
                font-weight: normal;
                font-size: 13px;
                color: #444444;
            }

                .comment-by-add-button:hover {
                    border: 2px dotted #666666 !important;
                    color: #333333 !important;
                    cursor: pointer;
                    text-decoration: none;
                }
        </style>

        <script type="text/javascript" id="BillableJavaScriptWAI">

           
        </script>
        <script src='<%= PathHelper.getAbsoluteUrl("~/js/SWIFrameManager.js", true) %>' type="text/javascript">
        </script>
    </telerik:RadCodeBlock>
    <telerik:RadScriptManager ID="RadScriptManagerActivityRecords22" runat="server">
        <Scripts>
            <asp:ScriptReference Assembly="Telerik.Web.UI" Name="Telerik.Web.UI.Common.Core.js" />
            <asp:ScriptReference Assembly="Telerik.Web.UI" Name="Telerik.Web.UI.Common.jQuery.js" />
        </Scripts>
    </telerik:RadScriptManager>
    <telerik:RadAjaxManager ID="RadAjaxManagerARProxy" runat="server">
        <AjaxSettings>
            <telerik:AjaxSetting AjaxControlID="RadGridItems">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadGridItems" />
                    <telerik:AjaxUpdatedControl ControlID="ItemStepValidationSummary" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadGridTasks">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadGridTasks" LoadingPanelID="RadAjaxLoadingPanel222" UpdatePanelRenderMode="Inline" />
                    <telerik:AjaxUpdatedControl ControlID="TaskValidationSummary" LoadingPanelID="RadAjaxLoadingPanel222" UpdatePanelRenderMode="Inline" />
                </UpdatedControls>
            </telerik:AjaxSetting>
        </AjaxSettings>
    </telerik:RadAjaxManager>

    <div runat="server" id="DIVUserComment" class="UserCommentDIV" clientidmode="Static"></div>
    <telerik:RadAjaxLoadingPanel ID="RadAjaxLoadingPanel222" runat="server" Transparency="40"
        BackColor="#ffffff" BackgroundPosition="Center">
    </telerik:RadAjaxLoadingPanel>
    <div id="ProductionDIV">
        <div style="width: 100%;">
            <asp:Panel id="ARProductionPanelItems" runat="server" OnLoad="ARProductionPanelItems_Load">
                <fieldset id="Items" class="windowFieldset" style="border-color: transparent">
                <legend>
                    <asp:Label runat="server" ID="LabelNotesLegend" Text="Items/Steps completed:" /></legend>
                    <div class="ErrorDIV" ID="ItemStepValidationSummary" runat="server" Visible="False" style="margin-bottom: 30px; width: 600px"></div>
                <div>
                    <table>
                        <tr>
                            <td>
                                <telerik:RadGrid ID="RadGridItems" GridLines="None" AutoGenerateEditButton="true"   runat="server" DataSourceID="SQLDataSourceItems" AutoGenerateEditColumn="True" OnDataBound="RadGridItems_DataBound" OnUpdateCommand="RadGridItems_OnUpdateCommand" 
                                    AllowPaging="True" AutoGenerateColumns="false" AllowSorting="true" AllowAutomaticInserts="true" AllowAutomaticUpdates="true" AllowAutomaticDeletes="True"  Width="600px" Style="margin-top: -25px;" OnInsertCommand="RadGridItems_OnInsertCommand">
                                    <PagerStyle Mode="NextPrevAndNumeric" />
                                    <MasterTableView DataKeyNames="ActivityRecordsGroupAgencyItem_ID" CommandItemDisplay="Bottom" DataSourceID="SQLDataSourceItems" ClientDataKeyNames="ActivityRecordsGroupAgencyItem_ID"
                                        TableLayout="Auto" CellSpacing="-1" EditMode="InPlace" InsertItemDisplay="Bottom" ValidateRequestMode="Enabled">
                                        <Columns>
                                            <telerik:GridBoundColumn DataField="ActivityRecordsGroupAgencyItem_ID" Visible="false" ReadOnly="true" />
                                            <telerik:GridTemplateColumn DataField="Item" HeaderText="Item" SortExpression="Item">
                                                <ItemTemplate>
                                                    <%#DataBinder.Eval(Container.DataItem, "Description")%>
                                                </ItemTemplate>
                                                <EditItemTemplate>
                                                    <telerik:RadComboBox ID="RadComboBoxItem" runat="server" SelectedValue='<%# Bind("AgencyItem_ID") %>' Width="200px" Filter="Contains" DropDownWidth="600px"
                                                        DataSourceID="SQLDataSourceItem" DataValueField="AgencyItem_ID" DataTextField="Description" Enabled="<%# HiddenCAN_ADD_AND_EDIT.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>" />
                                                </EditItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridTemplateColumn DataField="AgencyItemCount" HeaderText="Count" SortExpression="AgencyItemCount">
                                                <ItemTemplate>
                                                    <%#DataBinder.Eval(Container.DataItem, "AgencyItemCount")%>
                                                </ItemTemplate>
                                                <EditItemTemplate>
                                                    <telerik:RadNumericTextBox ID="TextBoxAgencyItemCount" runat="server"
                                                        MinValue="0" IncrementSettings-InterceptArrowKeys="true" ShowSpinButtons="true"
                                                        Width="80px" NumberFormat-DecimalDigits="0" DbValue='<%# Bind("AgencyItemCount") %>'
                                                        Enabled="<%# HiddenCAN_ADD_AND_EDIT.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>"/>
                                                    <br />
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidatorItemCount" ControlToValidate="TextBoxAgencyItemCount" Text="*Item Count" runat="server"  Display="Dynamic" style="color: Red" />
                                                </EditItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridTemplateColumn  DataField="StartTime" HeaderText="Start Time" SortExpression="StartTime" ForceExtractValue="Always">
                                                <ItemTemplate>
                                                    <%#DataBinder.Eval(Container.DataItem, "StartTime", "{0:h:mm tt}")%>
                                                </ItemTemplate>
                                                 <InsertItemTemplate> 
                                                     <telerik:RadTimePicker ID="RadTimePickerStartTimeItemInsert" runat="server" TimePopupButton-Visible="true" 
                                                         OnLoad="RadTimePickerStartTimeItem_Load" 
                                                         Width="90px" EnableViewState="true" ShowPopupOnFocus="true" DbSelectedDate='<%# Bind("StartTime") %>' 
                                                         TimeView-Interval="15" TimeView-Columns="4" ClientEvents-OnPopupOpening="itemTimePopupOpenResize" 
                                                         ClientEvents-OnPopupClosing="timePopupCloseResize" Enabled="<%# HiddenCAN_ADD_AND_EDIT.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>"> 
                                                         <DateInput ID="DateInputStartTime" runat="server" DateFormat="h:mm tt" > 
                                                         </DateInput> 
                                                     </telerik:RadTimePicker> 
                                                     <br /> 
                                                 </InsertItemTemplate> 
                                                <EditItemTemplate>
                                                    <telerik:RadTimePicker ID="RadTimePickerStartTimeItem" runat="server" TimePopupButton-Visible="true" 
                                                        Width="90px" EnableViewState="true" ShowPopupOnFocus="true" DbSelectedDate='<%# Bind("StartTime") %>' 
                                                        TimeView-Interval="15" TimeView-Columns="4" ClientEvents-OnPopupOpening="itemTimePopupOpenResize" ClientEvents-OnPopupClosing="timePopupCloseResize"
                                                        Enabled="<%# HiddenCan_MODIFY_DATES.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>">
                                                        <DateInput ID="DateInputStartTime" runat="server" DateFormat="h:mm tt"  >
                                                        </DateInput>
                                                    </telerik:RadTimePicker>
                                                    <br /><asp:RequiredFieldValidator ID="RequiredFieldValidatorItemStartTimeItem" ControlToValidate="RadTimePickerStartTimeItem" Text="*Start Time" runat="server"  Display="Dynamic" style="color: Red"  />
                                                </EditItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridTemplateColumn DataField="EndTime" HeaderText="End Time" SortExpression="EndTime" ForceExtractValue="Always">
                                                <ItemTemplate>
                                                    <%#DataBinder.Eval(Container.DataItem, "EndTime", "{0:h:mm tt}")%>
                                                </ItemTemplate>
                                                <InsertItemTemplate>
                                                    <telerik:RadTimePicker ID="RadTimePickerEndTimeInsert" runat="server" TimePopupButton-Visible="true"  
                                                        Width="90px" EnableViewState="true" ShowPopupOnFocus="true" DbSelectedDate='<%# Bind("EndTime") %>'
                                                        TimeView-Interval="15" TimeView-Columns="4" ClientEvents-OnPopupOpening="itemTimePopupOpenResize" ClientEvents-OnPopupClosing="timePopupCloseResize">                                                        
                                                        <DateInput ID="DateInputEndTime" runat="server" DateFormat="h:mm tt" >
                                                        </DateInput>
                                                    </telerik:RadTimePicker>
                                                    <br /><asp:RequiredFieldValidator ID="RequiredFieldValidatorItemEndTimeItemInsert" ControlToValidate="RadTimePickerEndTimeInsert" Text="*End Time"  runat="server" Display="Dynamic" style="color: Red" />
                                                </InsertItemTemplate>
                                                <EditItemTemplate>
                                                    <telerik:RadTimePicker ID="RadTimePickerEndTime" runat="server" TimePopupButton-Visible="true"  
                                                        Width="90px" EnableViewState="true" ShowPopupOnFocus="true" DbSelectedDate='<%# Bind("EndTime") %>'
                                                        TimeView-Interval="15" TimeView-Columns="4" ClientEvents-OnPopupOpening="itemTimePopupOpenResize" ClientEvents-OnPopupClosing="timePopupCloseResize"
                                                        Enabled="<%# HiddenCan_MODIFY_DATES.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>">                                                        
                                                        <DateInput ID="DateInputEndTime" runat="server" DateFormat="h:mm tt" >
                                                        </DateInput>
                                                    </telerik:RadTimePicker>
                                                    <br /><asp:RequiredFieldValidator ID="RequiredFieldValidatorItemEndTimeItem" ControlToValidate="RadTimePickerEndTime" Text="*End Time"  runat="server" Display="Dynamic" style="color: Red" />
                                                </EditItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridTemplateColumn DataField="HoursTotal" HeaderText="Hours Total" SortExpression="HoursTotal">
                                                <ItemTemplate>
                                                    <%#DataBinder.Eval(Container.DataItem, "HoursTotal")%>
                                                </ItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridTemplateColumn DataField="ProductivityPercentage" HeaderText="Productivity Percentage" SortExpression="ProductivityPercentage">
                                                <ItemTemplate>
                                                    <%#DataBinder.Eval(Container.DataItem, "Productivity")%>
                                                </ItemTemplate>
                                            </telerik:GridTemplateColumn>
                                            <telerik:GridButtonColumn ButtonType="ImageButton" Text="Delete" CommandName="Delete" UniqueName="DeleteButton"
                                                ConfirmText="Are you sure you want to delete the item?" />
                                        </Columns>
                                    </MasterTableView>
                                </telerik:RadGrid>
                                <asp:SqlDataSource runat="server" ID="SQLDataSourceItems" 
                                    SelectCommand="[dbo].[ACTIVITY_RECORDS_GROUP_AGENCY_ITEM.getGroupAgencyItemByConsumerID_1.0.1]" SelectCommandType="StoredProcedure"
                                    ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>" UpdateCommand="[ACTIVITY_RECORDS_GROUP_AGENCY_ITEM.SetActivityRecordsGroupAgencyItem_1.0.1]" UpdateCommandType="StoredProcedure"
                                    InsertCommand="[ACTIVITY_RECORDS_GROUP_AGENCY_ITEM.CreateActivityRecordsGroupAgencyItem_1.0.0]" InsertCommandType="StoredProcedure"
                                    DeleteCommand="DELETE FROM [dbo].[ActivityRecordsGroupAgencyItem] WHERE ActivityRecordsGroupAgencyItem_ID = @ActivityRecordsGroupAgencyItem_ID">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenCONSUMER_ID" Type="String" />
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        <asp:ControlParameter Name="ActivityRecordsGroup_ID" ControlID="HiddenACTIVITY_RECORD_GROUP_ID" Type="String" />
                                    </SelectParameters>
                                    <UpdateParameters>
                                        <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME" Type="String" />
                                        <asp:ControlParameter Name="ActivityRecordDateTime" ControlID="HiddenUPDate" Type="DateTime" />
                                    </UpdateParameters>
                                    <InsertParameters>
                                        <asp:ControlParameter Name="ActivityRecordsGroup_ID" ControlID="HiddenACTIVITY_RECORD_GROUP_ID" Type="String" />
                                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                        <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenCONSUMER_ID" Type="String" />
                                        <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME" Type="String" />
                                        <asp:ControlParameter Name="ActivityRecordDateTime" ControlID="HiddenUPDate" Type="DateTime" />
                                    </InsertParameters>
                                </asp:SqlDataSource>
                                <asp:SqlDataSource runat="server" ID="SQLDataSourceItem"
                                                   SelectCommandType="StoredProcedure"
                                    SelectCommand="[dbo].[CONSUMER_AGENCY_ITEM.getAgencyItemByConsumerIDAndAr_1.0.0]"
                                    ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>">
                                    <SelectParameters>
                                        <asp:ControlParameter Name="ConsumerID" ControlID="HiddenCONSUMER_ID" Type="String" />
                                        <asp:ControlParameter Name="ActivityRecordDateTime" ControlID="HiddenUPDate" Type="String" />
                                        <asp:ControlParameter Name="ActivityRecordsGroup_Id" ControlID="HiddenACTIVITY_RECORD_GROUP_ID" Type="String"/>
                                    </SelectParameters>
                                </asp:SqlDataSource>
                            </td>
                        </tr>
                    </table>
                </div>
            </fieldset>
            </asp:Panel>
        </div>
        <div style="width: 100%;">
            <asp:Panel id="ARProductionPanelTasks" runat="server" OnLoad="ARProductionPanelTasks_Load">
                <fieldset id="Tasks" class="windowFieldset" style="border-color: transparent">
                    <legend>
                        <asp:Label runat="server" ID="Label1" Text="Tasks completed:" /></legend>
                        <div class="ErrorDIV" ID="TaskValidationSummary" runat="server" Visible="False" style="margin-bottom: 30px; width: 600px"></div>
                    <div>
                        <table>
                            <tr>
                                <td>
                                    <telerik:RadGrid ID="RadGridTasks" GridLines="None" AutoGenerateEditButton="true" runat="server" DataSourceID="SQLDataSourceTasks" AutoGenerateEditColumn="True" OnDataBound="RadGridTasks_DataBound" OnInsertCommand="RadGridTasks_OnInsertCommand" OnUpdateCommand="RadGridTasks_OnUpdateCommand"
                                        AllowPaging="True" AutoGenerateColumns="false" AllowSorting="true" AllowAutomaticInserts="true" AllowAutomaticUpdates="true" AllowAutomaticDeletes="True" Width="600px" Style="margin-top: -25px;">
                                        <PagerStyle Mode="NextPrevAndNumeric" />
                                        <MasterTableView DataKeyNames="ActivityRecordsGroupAgencyTask_ID" CommandItemDisplay="Bottom" DataSourceID="SQLDataSourceTasks" ClientDataKeyNames="ActivityRecordsGroupAgencyTask_ID"
                                            TableLayout="Auto" CellSpacing="-1" EditMode="InPlace" InsertItemDisplay="Bottom">
                                            <Columns>
                                                <telerik:GridBoundColumn DataField="ActivityRecordsGroupAgencyTask_ID" Visible="false" ReadOnly="true" />
                                                <telerik:GridTemplateColumn DataField="Task" HeaderText="Task" SortExpression="Task">
                                                    <ItemTemplate>
                                                        <%#DataBinder.Eval(Container.DataItem, "Description")%>
                                                    </ItemTemplate>
                                                    <EditItemTemplate>
                                                        <telerik:RadComboBox ID="RadComboBoxTask" runat="server" SelectedValue='<%# Bind("AgencyTask_ID") %>' Width="350px" Filter="Contains"
                                                            DataSourceID="SQLDataSourceTask" DataValueField="AgencyTask_ID" DataTextField="Description" Enabled="<%# HiddenCAN_ADD_AND_EDIT.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>" />
                                                    </EditItemTemplate>
                                                </telerik:GridTemplateColumn>
                                                <telerik:GridTemplateColumn DataField="StartTime" HeaderText="Start Time" SortExpression="StartTime">
                                                    <ItemTemplate>
                                                        <%#DataBinder.Eval(Container.DataItem, "StartTime", "{0:h:mm tt}")%>
                                                    </ItemTemplate>
                                                    <insertItemTemplate>
                                                        <telerik:RadTimePicker ID="RadTimePickerStartTimeTaskInsert" runat="server" TimePopupButton-Visible="true" OnLoad="RadTimePickerStartTimeTask_Load"
                                                            Width="90px" EnableViewState="true" ShowPopupOnFocus="true" DbSelectedDate='<%# Bind("StartTime") %>'
                                                            TimeView-Interval="15" TimeView-Columns="4" ClientEvents-OnPopupOpening="taskTimePopupOpenResize" ClientEvents-OnPopupClosing="timePopupCloseResize">
                                                            <DateInput ID="DateInputStartTime" runat="server" DateFormat="h:mm tt" />                                                        
                                                        </telerik:RadTimePicker>
                                                        <br/>                                                    
                                                    </insertItemTemplate>
                                                    <EditItemTemplate>
                                                        <telerik:RadTimePicker ID="RadTimePickerStartTimeTask" runat="server" TimePopupButton-Visible="true"
                                                            Width="90px" EnableViewState="true" ShowPopupOnFocus="true" DbSelectedDate='<%# Bind("StartTime") %>'
                                                            TimeView-Interval="15" TimeView-Columns="4" ClientEvents-OnPopupOpening="taskTimePopupOpenResize" ClientEvents-OnPopupClosing="timePopupCloseResize"
                                                            Enabled="<%# HiddenCan_MODIFY_DATES.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>">
                                                            <DateInput ID="DateInputStartTime" runat="server" DateFormat="h:mm tt"  >       
                                                            </DateInput>
                                                        </telerik:RadTimePicker>
                                                        <br /><asp:RequiredFieldValidator ID="RequiredFieldValidatorTaskStartTimeTask" ControlToValidate="RadTimePickerStartTimeTask" Text="*Start Time" runat="server"  Display="Dynamic" style="color: Red" />
                                                    </EditItemTemplate>
                                                </telerik:GridTemplateColumn>
                                                <telerik:GridTemplateColumn DataField="EndTime" HeaderText="End Time" SortExpression="EndTime">
                                                    <ItemTemplate>
                                                        <%#DataBinder.Eval(Container.DataItem, "EndTime", "{0:h:mm tt}")%>
                                                    </ItemTemplate>
                                                    <EditItemTemplate>
                                                        <telerik:RadTimePicker ID="RadTimePickerEndTime" runat="server" TimePopupButton-Visible="true" 
                                                            Width="90px" EnableViewState="true" ShowPopupOnFocus="true" DbSelectedDate='<%# Bind("EndTime") %>'
                                                            TimeView-Interval="15" TimeView-Columns="4" ClientEvents-OnPopupOpening="taskTimePopupOpenResize" ClientEvents-OnPopupClosing="timePopupCloseResize"
                                                            Enabled="<%# HiddenCan_MODIFY_DATES.Value.Equals(bool.TrueString, StringComparison.OrdinalIgnoreCase) %>">
                                                            <DateInput ID="DateInputEndTime" runat="server" DateFormat="h:mm tt" >
                                                            </DateInput>
                                                        </telerik:RadTimePicker>
                                                        <br /><asp:RequiredFieldValidator ID="RequiredFieldValidatorTaskEndTimeTask" ControlToValidate="RadTimePickerEndTime" Text="*End Time" runat="server"  Display="Dynamic" style="color: Red"  />
                                                    </EditItemTemplate>
                                                </telerik:GridTemplateColumn>
                                                <telerik:GridButtonColumn ButtonType="ImageButton" Text="Delete" CommandName="Delete" UniqueName="DeleteButton"
                                                    ConfirmText="Are you sure you want to delete the task?" />
                                            </Columns>
                                        </MasterTableView>
                                    </telerik:RadGrid>
                                    <asp:SqlDataSource runat="server" ID="SQLDataSourceTasks" SelectCommand="SELECT ARG.ActivityRecordsGroupAgencyTask_ID AS 'ActivityRecordsGroupAgencyTask_ID', ARG.AgencyTask_ID AS 'AgencyTask_ID', ARG.StartTime AS 'StartTime', ARG.EndTime AS 'EndTime', AI.Description AS 'Description' FROM ActivityRecordsGroupAgencyTask ARG (NOLOCK) INNER JOIN AgencyTask AI (NOLOCK) ON AI.AgencyTask_ID = ARG.AgencyTask_ID WHERE ARG.ActivityRecordsGroup_ID = @ActivityRecordsGroup_ID AND ARG.Client_ID = @Client_ID AND Consumer_ID = @Consumer_ID ORDER BY ARG.StartTime ASC"
                                        ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>" UpdateCommand="[ACTIVITY_RECORDS_GROUP_AGENCY_TASK.SetActivityRecordsGroupAgencyTask_1.0.1]" UpdateCommandType="StoredProcedure"
                                        InsertCommand="[ACTIVITY_RECORDS_GROUP_AGENCY_TASK.CreateActivityRecordsGroupAgencyTask_1.0.0]" InsertCommandType="StoredProcedure"
                                        DeleteCommand="DELETE FROM [dbo].[ActivityRecordsGroupAgencyTask] WHERE ActivityRecordsGroupAgencyTask_ID = @ActivityRecordsGroupAgencyTask_ID">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenCONSUMER_ID" Type="String" />
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                            <asp:ControlParameter Name="ActivityRecordsGroup_ID" ControlID="HiddenACTIVITY_RECORD_GROUP_ID" Type="String" />
                                            <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME" Type="String" />
                                        </SelectParameters>
                                        <UpdateParameters>
                                            <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME" Type="String" />
                                            <asp:ControlParameter Name="ActivityRecordDateTime" ControlID="HiddenUPDate" Type="DateTime" />
                                        </UpdateParameters>
                                        <InsertParameters>
                                            <asp:ControlParameter Name="ActivityRecordsGroup_ID" ControlID="HiddenACTIVITY_RECORD_GROUP_ID" Type="String" />
                                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                            <asp:ControlParameter Name="Consumer_ID" ControlID="HiddenCONSUMER_ID" Type="String" />
                                            <asp:ControlParameter Name="lst_update_username" ControlID="HiddenCURRENTWINDOWUSERNAME" Type="String" />
                                            <asp:ControlParameter Name="ActivityRecordDateTime" ControlID="HiddenUPDate" Type="DateTime" />
                                        </InsertParameters>
                                    </asp:SqlDataSource>
                                    <asp:SqlDataSource runat="server" ID="SQLDataSourceTask"
                                        SelectCommand="SELECT CAI.AgencyTask_ID AS 'AgencyTask_ID', AI.Description    AS 'Description' FROM Consumer_AgencyTask CAI (NOLOCK) INNER JOIN AgencyTask AI (NOLOCK) ON AI.AgencyTask_ID = CAI.AgencyTask_ID WHERE AI.Active = 1 AND (CAI.Active = 1 OR CAI.AgencyTask_ID IN (SELECT AgencyTask_ID From ActivityRecordsGroupAgencyTask ARGAT WHERE ARGAT.Client_ID = CAI.Client_ID AND ARGAT.ActivityRecordsGroup_ID = @ActivityRecordsGroup_Id)) AND CAI.Consumer_ID = @ConsumerID AND ((StartDate IS NULL AND EndDate IS NULL) OR (StartDate IS NULL AND EndDate >= @ActivityRecordDateTime) OR (EndDate IS NULL AND StartDate <= @ActivityRecordDateTime) OR (StartDate <= @ActivityRecordDateTime AND EndDate >= @ActivityRecordDateTime)) ORDER BY AI.Description"
                                        ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>">
                                        <SelectParameters>
                                            <asp:ControlParameter Name="ConsumerID" ControlID="HiddenCONSUMER_ID" Type="String" />
                                            <asp:ControlParameter Name="ActivityRecordDateTime" ControlID="HiddenUPDate" Type="String" />
                                            <asp:ControlParameter Name="ActivityRecordsGroup_ID" ControlID="HiddenACTIVITY_RECORD_GROUP_ID" Type="String"/>
                                        </SelectParameters>
                                    </asp:SqlDataSource>
                                </td>
                            </tr>
                        </table>
                    </div>
                </fieldset>
            </asp:Panel>
        </div>
        <div id="timeHeight" style="height: 0px" />
    </div>

    <asp:HiddenField ID="HiddenACTIVITY_RECORD_GROUP_ID" Value="0" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID" Value="0" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCONSUMER_ID" Value="0" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenOUTCOME_ID" Value="0" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenGOAL_ID" Value="0" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTWINDOWUSERID" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTWINDOWCLIENTID" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTWINDOWUSERNAME" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenEDIT_USER_COMMENT_ID" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCOMMENT_CREATOR" runat="server" Value=""></asp:HiddenField>
    <asp:HiddenField ID="HiddenACTION" runat="server" Value=""></asp:HiddenField>
    <asp:HiddenField ID="HiddenACTIVITY_RECORD_COMMENT_TYPE" runat="server" Value=""></asp:HiddenField>
    <asp:HiddenField ID="HiddenCAN_ADD_AND_EDIT" runat="server" Value="FALSE"></asp:HiddenField>
    <asp:HiddenField ID="HiddenPOST_SAVE_PARAMETERS" runat="server" Value=""></asp:HiddenField>
    <asp:HiddenField ID="HiddenUPDate" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCan_MODIFY_DATES" runat="server" Value="FALSE"></asp:HiddenField>

    <telerik:RadCodeBlock ID="RadCodeBlock1" runat="server">
        <script type="text/javascript" id="Script1222">

            function itemTimePopupOpenResize(sender, event) {
                $telerik.$("#timeHeight").css("height", "460px")
                resizeFrame("ProductionDIV");
            }

            function taskTimePopupOpenResize(sender, event) {
                $telerik.$("#timeHeight").css("height", "580px")
                resizeFrame("ProductionDIV");
            }

            function timePopupCloseResize(sender, event) {
                $telerik.$("#timeHeight").css("height", "0px")
                resizeFrame("ProductionDIV");
            }
        </script>
    </telerik:RadCodeBlock>

</asp:Content>
