﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
    <assembly>
        <name>GleamTech.Common</name>
    </assembly>
    <members>
        <member name="T:GleamTech.AspNet.FileResponse">
            <summary>
            Class for transmitting file streams in a performant way.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.FileResponse.#ctor(GleamTech.AspNet.IHttpContext,System.Nullable{System.Int64})">
            <summary>
            Initializes a new instance of FileResponse with the current http context.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.FileResponse.Transmit(System.IO.Stream,System.String,System.DateTime,System.Int64,System.Boolean,System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Transmits the given stream as a file to the http response stream.
            </summary>
            <param name="sourceStream">The source stream that will be transmitted as a file.</param>
            <param name="fileName" />
            <param name="lastModifiedTime" />
            <param name="fileSize" />
            <param name="forceSave" />
            <param name="contentType" />
            <param name="neverExpires" />
            <param name="allowGzip" />
            <param name="enableBuffer" />
        </member>
        <member name="T:GleamTech.AspNet.GleamTechWebConfiguration">
            <summary>
            Provides properties and methods for changing this library's configuration for web use.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.GleamTechWebConfiguration.CacheLocation">
            <summary>
            Gets or sets the location to store resource files like gzip-ed js and css files. 
            The default value is "[<see cref="P:GleamTech.GleamTechConfiguration.TemporaryFolder" />]\ResourceCache".
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.GleamTechWebConfiguration.CacheMaxAge">
            <summary>
            Gets or sets a value that specifies the maximum amount of time to consider a cached item fresh and keep it stored in the cache.
            Cached items older than this age will be removed when the cache trimming (clean up) runs (when auto trim is run or
            when Trim method is manually called).
            The default value is 90 days.
            In string representation, value in format "d" is considered days (e.g. "30" is 30 days) and value in format "hh:mm" is considered hours and minutes
            (e.g. "1:30" is 1 hour and 30 minutes). Note that "24:00" would mean 24 days as hour range is 0 to 23 and minute range is 0 to 59.
            See <see cref="M:System.TimeSpan.Parse(System.String)" /> documentation for details on possible string representations.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.GleamTechWebConfiguration.CacheWaitTimeout">
            <summary>
            Gets or sets a value that specifies the maximum amount of time to wait for a cached item to be available (wait for a file lock from other threads or
            processes to become available, i.e. wait for an ongoing caching of the same file to complete) before giving up on the cache request.
            The default value is 5 minutes.
            In string representation, value in format "d" is considered days (e.g. "30" is 30 days) and value in format "hh:mm" is considered hours and minutes
            (e.g. "1:30" is 1 hour and 30 minutes). Note that "24:00" would mean 24 days as hour range is 0 to 23 and minute range is 0 to 59.
            See <see cref="M:System.TimeSpan.Parse(System.String)" /> documentation for details on possible string representations.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.GleamTechWebConfiguration.CacheAutoTrimInterval">
            <summary>
            Gets or sets a value that specifies the interval to run automatic cache trimming (clean up).
            Cached items older than <see cref="P:GleamTech.AspNet.GleamTechWebConfiguration.CacheMaxAge" /> will be removed when the cache is trimmed.
            If the value is 0 or negative, the auto trim is disabled.
            The default value is 20 minutes.
            In string representation, value in format "d" is considered days (e.g. "30" is 30 days) and value in format "hh:mm" is considered hours and minutes
            (e.g. "1:30" is 1 hour and 30 minutes). Note that "24:00" would mean 24 days as hour range is 0 to 23 and minute range is 0 to 59.
            See <see cref="M:System.TimeSpan.Parse(System.String)" /> documentation for details on possible string representations.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.GleamTechWebConfiguration.GetCache">
            <summary>Gets the reource cache used by this web configuration instance.</summary>
            <returns>A <see cref="T:GleamTech.Caching.FileCache" /> instance.</returns>
        </member>
        <member name="M:GleamTech.AspNet.GleamTechWebConfiguration.InitCurrent">
            <summary>
            Initializes only the static Current instance.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.GleamTechWebConfiguration.Current">
            <summary>Gets current global configuration instance.</summary>
        </member>
        <member name="T:GleamTech.AspNet.SameSiteMode">
            <summary>Specifies constants that indicate the value for the SameSite attribute of the cookie.</summary>
        </member>
        <member name="F:GleamTech.AspNet.SameSiteMode.None">
            <summary>No mode is specified.</summary>
        </member>
        <member name="F:GleamTech.AspNet.SameSiteMode.Lax">
            <summary>The cookie will be sent with "same-site" requests, and with "cross-site" top level navigation.</summary>
        </member>
        <member name="F:GleamTech.AspNet.SameSiteMode.Strict">
            <summary> When the value is Strict, or if the value is invalid, the cookie will only be sent along with "same-site" requests.</summary>
        </member>
        <member name="T:GleamTech.AspNet.Multipart.BinaryStreamStack">
            <summary>
                Provides character based and byte based stream-like read operations over multiple
                streams and provides methods to add data to the front of the buffer.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.BinaryStreamStack.streams">
            <summary>
                Holds the streams to read from, the stream on the top of the
                stack will be read first.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.BinaryStreamStack" /> class with the default
                encoding of UTF8.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.#ctor(System.Text.Encoding)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.BinaryStreamStack" /> class.
            </summary>
            <param name="encoding">
            The encoding to use for character based operations.
            </param>
        </member>
        <member name="P:GleamTech.AspNet.Multipart.BinaryStreamStack.CurrentEncoding">
            <summary>
                Gets or sets the current encoding.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.HasData">
            <summary>
                Returns true if there is any data left to read.
            </summary>
            <returns>
                True or false.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.Peek">
            <summary>
                Returns the reader on the top of the stack but does not remove it.
            </summary>
            <returns>
                The <see cref="T:System.IO.BinaryReader" />.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.Pop">
            <summary>
                Returns the reader on the top of the stack and removes it
            </summary>
            <returns>
                The <see cref="T:System.IO.BinaryReader" />.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.Push(System.Byte[])">
            <summary>
            Pushes data to the front of the stack. The most recently pushed data will
                be read first.
            </summary>
            <param name="data">
            The data to add to the stack.
            </param>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.Read">
            <summary>
                Reads a single byte as an integer from the stack. Returns -1 if no
                data is left to read.
            </summary>
            <returns>
                The <see cref="T:System.Byte" /> that was read.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads the specified number of bytes from the stack, starting from a specified point in the byte array.
            </summary>
            <param name="buffer">
            The buffer to read data into.
            </param>
            <param name="index">
            The index of buffer to start reading into.
            </param>
            <param name="count">
            The number of bytes to read into the buffer.
            </param>
            <returns>
            The number of bytes read into buffer. This might be less than the number of bytes requested if that many bytes are not available,
                or it might be zero if the end of the stream is reached.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.Read(System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads the specified number of characters from the stack, starting from a specified point in the byte array.
            </summary>
            <param name="buffer">
            The buffer to read data into.
            </param>
            <param name="index">
            The index of buffer to start reading into.
            </param>
            <param name="count">
            The number of characters to read into the buffer.
            </param>
            <returns>
            The number of characters read into buffer. This might be less than the number of bytes requested if that many bytes are not available,
                or it might be zero if the end of the stream is reached.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.ReadByteLine">
            <summary>
                Reads the specified number of characters from the stack, starting from a specified point in the byte array.
            </summary>
            <returns>
                A byte array containing all the data up to but not including the next newline in the stack.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.ReadByteLine(System.Boolean@)">
            <summary>
            Reads a line from the stack delimited by the newline for this platform. The newline
                characters will not be included in the stream
            </summary>
            <param name="hitStreamEnd">
            This will be set to true if we did not end on a newline but instead found the end of
                our data.
            </param>
            <returns>
            The <see cref="T:System.String" /> containing the line.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.ReadLine">
            <summary>
                Reads a line from the stack delimited by the newline for this platform. The newline
                characters will not be included in the stream
            </summary>
            <returns>
                The <see cref="T:System.String" /> containing the line.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.ReadLine(System.Boolean@)">
            <summary>
            Reads a line from the stack delimited by the newline for this platform. The newline
                characters will not be included in the stream
            </summary>
            <param name="hitStreamEnd">
            This will be set to true if we did not end on a newline but instead found the end of
                our data.
            </param>
            <returns>
            The <see cref="T:System.String" /> containing the line.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.BinaryStreamStack.NextStream">
            <summary>
                Removes the current reader from the stack and ensures it is correctly
                destroyed and then returns the next available reader. If no reader
                is available this method returns null.
            </summary>
            <returns>
                The next <see cref="T:System.IO.BinaryReader">reader</see>.
            </returns>
        </member>
        <member name="T:GleamTech.AspNet.Multipart.MultipartParser">
            <summary>
                Provides methods to parse a
                <see href="http://www.ietf.org/rfc/rfc2388.txt"><c>multipart/form-data</c></see>
                stream into it's parameters and file data.
            </summary>
            <remarks>
                <para>
                    A parameter is defined as any non-file data passed in the multipart stream. For example
                    any form fields would be considered a parameter.
                </para>
                <para>
                    The parser determines if a section is a file or not based on the presence or absence
                    of the filename argument for the Content-Type header. If filename is set then the section
                    is assumed to be a file, otherwise it is assumed to be parameter data.
                </para>
            </remarks>
            <example>
                <code lang="C#"> 
                  Stream multipartStream = GetTheMultipartStream();
                  string boundary = GetTheBoundary();
                  var parser = new MultipartParser(multipartStream, boundary, Encoding.UTF8);
             
                  // Grab the parameters (non-file data). Key is based on the name field
                  var username = parser.Parameters["username"].Data;
                  var password = parser.parameters["password"].Data;
                  
                  // Grab the first files data
                  var file = parser.Files.First();
                  var filename = file.FileName;
                  var filestream = file.Data;
              </code>
                <code lang="C#">
                // In the context of WCF you can get the boundary from the HTTP
                // request
                public ResponseClass MyMethod(Stream multipartData)
                {
                    // First we need to get the boundary from the header, this is sent
                    // with the HTTP request. We can do that in WCF using the WebOperationConext:
                    var type = WebOperationContext.Current.IncomingRequest.Headers["Content-Type"];
            
                    // Now we want to strip the boundary out of the Content-Type, currently the string
                    // looks like: "multipart/form-data; boundary=---------------------124123qase124"
                    var boundary = type.Substring(type.IndexOf('=')+1);
            
                    // Now that we've got the boundary we can parse our multipart and use it as normal
                    var parser = new MultipartParser(data, boundary, Encoding.UTF8);
            
                    ...
                }
              </code>
            </example>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.MultipartParser.DefaultBufferSize">
            <summary>
            The default buffer size.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.MultipartParser.boundary">
            <summary>
                The boundary of the multipart message  as a string.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.MultipartParser.boundaryBinary">
            <summary>
                The boundary of the multipart message as a byte string
                encoded with CurrentEncoding
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.MultipartParser.endBoundary">
            <summary>
                The end boundary of the multipart message as a string.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.MultipartParser.endBoundaryBinary">
            <summary>
                The end boundary of the multipart message as a byte string
                encoded with CurrentEncoding
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.MultipartParser.readEndBoundary">
            <summary>
                Determines if we have consumed the end boundary binary and determines
                if we are done parsing.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.#ctor(System.IO.Stream,GleamTech.AspNet.Multipart.IMultipartHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.MultipartParser" /> class
                with an input stream. Boundary will be automatically detected based on the
                first line of input.
            </summary>
            <param name="stream">
            The stream containing the multipart data
            </param>
            <param name="multipartHandler" />
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.#ctor(System.IO.Stream,System.String,GleamTech.AspNet.Multipart.IMultipartHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.MultipartParser" /> class
                with the boundary and input stream.
            </summary>
            <param name="stream">
            The stream containing the multipart data
            </param>
            <param name="boundary">
            The multipart/form-data boundary. This should be the value
                returned by the request header.
            </param>
            <param name="multipartHandler" />
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.#ctor(System.IO.Stream,System.Text.Encoding,GleamTech.AspNet.Multipart.IMultipartHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.MultipartParser" /> class
                with the input stream and stream encoding. Boundary is automatically
                detected.
            </summary>
            <param name="stream">
            The stream containing the multipart data
            </param>
            <param name="encoding">
            The encoding of the multipart data
            </param>
            <param name="multipartHandler" />
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.#ctor(System.IO.Stream,System.String,System.Text.Encoding,GleamTech.AspNet.Multipart.IMultipartHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.MultipartParser" /> class
                with the boundary, input stream and stream encoding.
            </summary>
            <param name="stream">
            The stream containing the multipart data
            </param>
            <param name="boundary">
            The multipart/form-data boundary. This should be the value
                returned by the request header.
            </param>
            <param name="encoding">
            The encoding of the multipart data
            </param>
            <param name="multipartHandler" />
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.#ctor(System.IO.Stream,System.Text.Encoding,System.Int32,GleamTech.AspNet.Multipart.IMultipartHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.MultipartParser" /> class
                with the stream, input encoding and buffer size. Boundary is automatically
                detected.
            </summary>
            <param name="stream">
            The stream containing the multipart data
            </param>
            <param name="encoding">
            The encoding of the multipart data
            </param>
            <param name="binaryBufferSize">
            The size of the buffer to use for parsing the multipart form data. This must be larger
                then (size of boundary + 4 + # bytes in newline).
            </param>
            <param name="multipartHandler" />
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.#ctor(System.IO.Stream,System.String,System.Text.Encoding,System.Int32,GleamTech.AspNet.Multipart.IMultipartHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.MultipartParser" /> class
                with the boundary, stream, input encoding and buffer size.
            </summary>
            <param name="stream">
            The stream containing the multipart data
            </param>
            <param name="boundary">
            The multipart/form-data boundary. This should be the value
                returned by the request header.
            </param>
            <param name="encoding">
            The encoding of the multipart data
            </param>
            <param name="binaryBufferSize">
            The size of the buffer to use for parsing the multipart form data. This must be larger
                then (size of boundary + 4 + # bytes in newline).
            </param>
            <param name="multipartHandler" />
        </member>
        <member name="P:GleamTech.AspNet.Multipart.MultipartParser.BinaryBufferSize">
            <summary>
                Gets the binary buffer size.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.Multipart.MultipartParser.Encoding">
            <summary>
                Gets the encoding.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.DetectBoundary(GleamTech.AspNet.Multipart.RebufferableBinaryReader)">
            <summary>
            Detects the boundary from the input stream. Assumes that the
                current position of the reader is the start of the file and therefore
                the beginning of the boundary.
            </summary>
            <param name="reader">
            The binary reader to parse
            </param>
            <returns>
            The boundary string
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.FindNextNewline(System.Byte[]@,System.Int32,System.Int32)">
            <summary>
            Finds the next sequence of newlines in the input stream.
            </summary>
            <param name="data">The data to search</param>
            <param name="offset">The offset to start searching at</param>
            <param name="maxBytes">The maximum number of bytes (starting from offset) to search.</param>
            <returns>The offset of the next newline</returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.CalculateNewlineLength(System.Byte[]@,System.Int32)">
            <summary>
            Calculates the length of the next found newline.
                data[offset] is the start of the space to search.
            </summary>
            <param name="data">
            The data containing the newline
            </param>
            <param name="offset">
            The offset of the start of the newline
            </param>
            <returns>
            The length in bytes of the newline sequence
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.Parse(GleamTech.AspNet.Multipart.RebufferableBinaryReader)">
            <summary>
            Begins the parsing of the stream into objects.
            </summary>
            <param name="reader">
            The multipart/form-data binary reader to parse from.
            </param>
            <exception cref="T:GleamTech.AspNet.Multipart.MultipartParserException">
            thrown on finding unexpected data such as a boundary before we are ready for one.
            </exception>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.ParseSection(GleamTech.AspNet.Multipart.RebufferableBinaryReader)">
            <summary>
            Parses the header of the next section of the multipart stream and
                determines if it contains file data or parameter data.
            </summary>
            <param name="reader">
            The StreamReader to read data from.
            </param>
            <exception cref="T:GleamTech.AspNet.Multipart.MultipartParserException">
            thrown if unexpected data is hit such as end of stream.
            </exception>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.ParseFilePart(GleamTech.AspNet.Multipart.RebufferableBinaryReader)">
            <summary>
            Parses a section of the stream that is known to be file data.
            </summary>
            <param name="reader">
            The StreamReader to read the data from
            </param>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParser.ParseParameterPart(GleamTech.AspNet.Multipart.RebufferableBinaryReader)">
            <summary>
            Parses a section of the stream that is known to be parameter data.
            </summary>
            <param name="reader">
            The StreamReader to read the data from
            </param>
            <exception cref="T:GleamTech.AspNet.Multipart.MultipartParserException">
            thrown if unexpected data is found such as running out of stream before hitting the boundary.
            </exception>
        </member>
        <member name="T:GleamTech.AspNet.Multipart.MultipartParserException">
            <summary>
                Represents a parsing problem occurring within the MultipartParser
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.MultipartParserException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.MultipartParserException" /> class.
            </summary>
            <param name="message">
            The message.
            </param>
        </member>
        <member name="T:GleamTech.AspNet.Multipart.RebufferableBinaryReader">
            <summary>
                Provides methods to interpret and read a stream as either character or binary
                data similar to a <see cref="T:System.IO.BinaryReader" /> and provides the ability to push
                data onto the front of the stream.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.RebufferableBinaryReader.bufferSize">
            <summary>
                The size of the buffer to use when reading new data.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.RebufferableBinaryReader.encoding">
            <summary>
                The encoding to use for character based operations
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.RebufferableBinaryReader.stream">
            <summary>
                The stream to read raw data from.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.RebufferableBinaryReader.streamStack">
            <summary>
                The stream stack to store buffered data.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.Multipart.RebufferableBinaryReader.done">
            <summary>
                Determines if we have run out of data to read or not.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.RebufferableBinaryReader" /> class.
                Default encoding of UTF8 will be used.
            </summary>
            <param name="input">
            The input stream to read from.
            </param>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.#ctor(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.RebufferableBinaryReader" /> class.
            </summary>
            <param name="input">
            The input stream to read from.
            </param>
            <param name="encoding">
            The encoding to use for character based operations.
            </param>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.#ctor(System.IO.Stream,System.Text.Encoding,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.AspNet.Multipart.RebufferableBinaryReader" /> class.
            </summary>
            <param name="input">
            The input stream to read from.
            </param>
            <param name="encoding">
            The encoding to use for character based operations.
            </param>
            <param name="bufferSize">
            The buffer size to use for new buffers.
            </param>
        </member>
        <member name="P:GleamTech.AspNet.Multipart.RebufferableBinaryReader.Done">
            <summary>
                Determines if we have run out of data to read or not.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.Buffer(System.Byte[])">
            <summary>
            Adds data to the front of the stream. The most recently buffered data will
                be read first.
            </summary>
            <param name="data">
            The data to buffer.
            </param>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.Buffer(System.String)">
            <summary>
            Adds the string to the front of the stream. The most recently buffered data will
                be read first.
            </summary>
            <param name="data">
            The data.
            </param>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.Dispose">
            <summary>
                Closes the stream.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.Read">
            <summary>
                Reads a single byte as an integer from the stream. Returns -1 if no
                data is left to read.
            </summary>
            <returns>
                The <see cref="T:System.Byte" /> that was read.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads the specified number of bytes from the stream, starting from a
                specified point in the byte array.
            </summary>
            <param name="buffer">
            The buffer to read data into.
            </param>
            <param name="index">
            The index of buffer to start reading into.
            </param>
            <param name="count">
            The number of bytes to read into the buffer.
            </param>
            <returns>
            The number of bytes read into buffer. This might be less than the number of bytes requested if that many bytes are not available,
                or it might be zero if the end of the stream is reached.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.Read(System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads the specified number of characters from the stream, starting from a
                specified point in the byte array.
            </summary>
            <param name="buffer">
            The buffer to read data into.
            </param>
            <param name="index">
            The index of buffer to start reading into.
            </param>
            <param name="count">
            The number of characters to read into the buffer.
            </param>
            <returns>
            The number of characters read into buffer. This might be less than the number of
                characters requested if that many characters are not available,
                or it might be zero if the end of the stream is reached.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.ReadByteLine">
            <summary>
                Reads a series of bytes delimited by the byte encoding of newline for this platform.
                the newline bytes will not be included in the return data.
            </summary>
            <returns>
                A byte array containing all the data up to but not including the next newline in the stack.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.ReadLine">
            <summary>
                Reads a line from the stack delimited by the newline for this platform. The newline
                characters will not be included in the stream
            </summary>
            <returns>
                The <see cref="T:System.String" /> containing the line.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.GetBomOffset(System.Byte[])">
            <summary>
            Determines the byte order marking offset (if any) from the
                given buffer.
            </summary>
            <param name="buffer">
            The buffer to examine.
            </param>
            <returns>
            The <see cref="T:System.Int32" /> representing the length of the byte order marking.
            </returns>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.RebufferableBinaryReader.StreamData">
            <summary>
                Reads more data from the stream into the stream stack.
            </summary>
            <returns>
                The number of bytes read into the stream stack as an <see cref="T:System.Int32" /></returns>
        </member>
        <member name="T:GleamTech.AspNet.Multipart.SubsequenceFinder">
            <summary>
                Provides methods to find a subsequence within a
                sequence.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.Multipart.SubsequenceFinder.Search(System.Byte[],System.Byte[],System.Collections.Generic.HashSet{System.Byte},System.Int32)">
            <summary>
            Finds if a sequence exists within another sequence.
            </summary>
            <param name="haystack">
            The sequence to search
            </param>
            <param name="needle">
            The sequence to look for
            </param>
            <param name="needleHashset" />
            <param name="haystackLength" />
            <para name="haystackLength">
            The length of the haystack to consider for searching
            </para>
            <returns>
            The start position of the found sequence or -1 if nothing was found
            </returns>
        </member>
        <member name="T:GleamTech.AspNet.Mvc.WebViewPageExtensions">
            <summary>Adds GleamTech related extensions methods to <see cref="T:System.Web.Mvc.WebViewPage" /> class for ASP.NET MVC support.</summary>
        </member>
        <member name="M:GleamTech.AspNet.Mvc.WebViewPageExtensions.RenderHead(System.Web.Mvc.WebViewPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])">
            <summary>
            Renders the required resource tags (e.g. JS, CSS includes etc.) for one or more GleamTech components.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="webViewPage">The <see cref="T:System.Web.Mvc.WebViewPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the head for.</param>
            <param name="otherComponents">Other GleamTech component instances to render the head for which are on the same page.</param>
            <returns>The component resource tags that goes into &lt;head&gt; tag of the page.</returns>
        </member>
        <member name="M:GleamTech.AspNet.Mvc.WebViewPageExtensions.RenderHeadWithoutJs(System.Web.Mvc.WebViewPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])">
            <summary>
            Renders the required resource tags except JS includes (only CSS includes etc.) for one or more GleamTech components.
            This method can be preferred when you want to render only CSS includes inside &lt;head&gt; tag and
            render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag via calling
            <see cref="M:GleamTech.AspNet.Mvc.WebViewPageExtensions.RenderJs(System.Web.Mvc.WebViewPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="webViewPage">The <see cref="T:System.Web.Mvc.WebViewPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the head for.</param>
            <param name="otherComponents">Other GleamTech component instances to render the head for which are on the same page.</param>
            <returns>The component resource tags that goes into &lt;head&gt; tag of the page.</returns>
        </member>
        <member name="M:GleamTech.AspNet.Mvc.WebViewPageExtensions.RenderJs(System.Web.Mvc.WebViewPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])">
            <summary>
            Renders the required resource tags of JS includes (no CSS includes etc.) for one or more GleamTech components.
            This method can be preferred when you want to render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag 
            and render only CSS includes inside &lt;head&gt; tag via calling <see cref="M:GleamTech.AspNet.Mvc.WebViewPageExtensions.RenderHeadWithoutJs(System.Web.Mvc.WebViewPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called at the bottom of the page before before the closing &lt;/body&gt; tag.
            If you call this method at some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="webViewPage">The <see cref="T:System.Web.Mvc.WebViewPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the JS includes for.</param>
            <param name="otherComponents">Other GleamTech component instances to render the JS includes for which are on the same page.</param>
            <returns>The component resource tags that goes at the bottom of the page before before the closing &lt;/body&gt; tag.</returns>
        </member>
        <member name="M:GleamTech.AspNet.Mvc.WebViewPageExtensions.RenderBody(System.Web.Mvc.WebViewPage,GleamTech.AspNet.UI.Component)">
            <summary>
            Renders the actual body tags for a GleamTech component.
            This method should be called inside &lt;body&gt; tag (or child tags) of the page.
            If you call this method inside &lt;head&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, this method can be called separately
            for each component.
            </summary>
            <param name="webViewPage">The <see cref="T:System.Web.Mvc.WebViewPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the component body for.</param>
            <returns>The component body tags that goes into &lt;body&gt; tag (or child tags) of the page.</returns>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.Id">
            <summary>
            Gets or sets the HTML id of the component. 
            Also a javascript variable with the same name is automatically defined and it can be used to access 
            the client-side implementation of the component. 
            If omitted, Id will be  automatically set to the class name of this component (e.g camel-case className)
            and if there are other instances on the same page, to className2 ... classNameX.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.Hidden">
            <summary>
            Gets or sets a value indicating whether the component is displayed when page is loaded. 
            When set to false, the component can be displayed later on client-side manually. 
            For example, the component can be displayed via a button's click event.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.Width">
            <summary>Gets or sets the width of the component.</summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.Height">
            <summary>Gets or sets the height of the component.</summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.Resizable">
            <summary>
            Gets or sets a value that specifies if the component can be resized when the user drags sides. 
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.ResizeHandles">
            <summary>
            Gets or sets a value that specifies the handles which can be used for resizing the component.
            Default is South, East and SouthEast.
            When using <see cref="F:GleamTech.AspNet.UI.DisplayMode.Window" /> mode, set <see cref="P:GleamTech.AspNet.UI.WindowOptions.ResizeHandles" />
            which overrides this property.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.DisplayMode">
            <summary>Gets or sets a value indicating whether the component is rendered as InPlace (default), Panel, Window or Viewport.</summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.WindowOptions">
            <summary>Gets or sets the windows options used for <see cref="F:GleamTech.AspNet.UI.DisplayMode.Window" /> mode.</summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.PanelOptions">
            <summary>Gets or sets the panel options used for <see cref="F:GleamTech.AspNet.UI.DisplayMode.Panel" /> mode.</summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.PageIsolation">
            <exclude />
        </member>
        <member name="P:GleamTech.AspNet.UI.Component.ProductInfoRendered">
            <summary>Gets or sets a value indicating whether product info/copyright comment is rendered in html.</summary>
        </member>
        <member name="T:GleamTech.AspNet.UI.ComponentRenderer">
            <summary>Provides methods for rendering GleamTech components.</summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(System.IO.TextWriter,GleamTech.AspNet.UI.Component,System.Boolean)">
            <summary>
            Renders and writes to a TextWriter, the required resource tags (e.g. JS, CSS includes etc.) for a GleamTech component.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            Pass <paramref name="withoutJs" /> as true when you want to render only CSS includes inside &lt;head&gt; tag and
            render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag via calling
            <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(System.IO.TextWriter,GleamTech.AspNet.UI.Component)" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            </summary>
            <param name="textWriter">The TextWriter to write the component resource tags that goes into &lt;head&gt; tag of the page.</param>
            <param name="component">The GleamTech component instance to render the head for.</param>
            <param name="withoutJs">Whether to render JS includes or not.</param>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(System.IO.TextWriter,System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component},System.Boolean)">
            <summary>
            Renders and writes to a TextWriter, the required resource tags (e.g. JS, CSS includes etc.) for multiple GleamTech components.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            Pass <paramref name="withoutJs" /> as true when you want to render only CSS includes inside &lt;head&gt; tag and
            render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag via calling
            <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(System.IO.TextWriter,System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component})" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            </summary>
            <param name="textWriter">The TextWriter to write the component resource tags that goes into &lt;head&gt; tag of the page.</param>
            <param name="components">The GleamTech component instanceds to render the head for.</param>
            <param name="withoutJs">Whether to render JS includes or not.</param>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(System.IO.TextWriter,GleamTech.AspNet.UI.Component)">
            <summary>
            Renders and writes to a TextWriter, the required resource tags of JS includes (no CSS includes etc.) for a GleamTech component.
            This method can be preferred when you want to render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag 
            and render only CSS includes inside &lt;head&gt; tag via calling <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(System.IO.TextWriter,GleamTech.AspNet.UI.Component,System.Boolean)" /> with withoutJs parameter set to true.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called at the bottom of the page before before the closing &lt;/body&gt; tag.
            If you call this method at some random place, the browser would misbehave.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="textWriter">The TextWriter to write the component resource tags that goes at the bottom of the page before before the closing &lt;/body&gt; tag.</param>
            <param name="component">The GleamTech component instance to render the JS includes for.</param>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(System.IO.TextWriter,System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component})">
            <summary>
            Renders and writes to a TextWriter, the required resource tags of JS includes (no CSS includes etc.) for multiple GleamTech components.
            This method can be preferred when you want to render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag 
            and render only CSS includes inside &lt;head&gt; tag via calling <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(System.IO.TextWriter,System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component},System.Boolean)" /> with withoutJs parameter set to true.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called at the bottom of the page before before the closing &lt;/body&gt; tag.
            If you call this method at some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="textWriter">The TextWriter to write the component resource tags that goes at the bottom of the page before before the closing &lt;/body&gt; tag.</param>
            <param name="components">The GleamTech component instances to render the JS includes for which are on the same page.</param>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderBody(System.IO.TextWriter,GleamTech.AspNet.UI.Component)">
            <summary>
            Renders and returns a string containing the actual body tags for a GleamTech component.
            This method should be called inside &lt;body&gt; tag (or child tags) of the page.
            If you call this method inside &lt;head&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, this method can be called separately
            for each component.
            </summary>
            <param name="textWriter">The TextWriter to write the component body tags that goes into &lt;body&gt; tag (or child tags) of the page.</param>
            <param name="component">The GleamTech component instance to render the component body for.</param>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(GleamTech.AspNet.UI.Component,System.Boolean)">
            <summary>
            Renders and returns a string containing the required resource tags (e.g. JS, CSS includes etc.) for a GleamTech component.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            Pass <paramref name="withoutJs" /> as true when you want to render only CSS includes inside &lt;head&gt; tag and
            render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag via calling
            <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(GleamTech.AspNet.UI.Component)" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            </summary>
            <param name="component">The GleamTech component instance to render the head for.</param>
            <param name="withoutJs">Whether to render JS includes or not.</param>
            <returns>A string containing the component resource tags that goes into &lt;head&gt; tag of the page.</returns>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component},System.Boolean)">
            <summary>
            Renders and returns a string containing the required resource tags (e.g. JS, CSS includes etc.) for multiple GleamTech components.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            Pass <paramref name="withoutJs" /> as true when you want to render only CSS includes inside &lt;head&gt; tag and
            render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag via calling
            <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component})" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            </summary>
            <param name="components">The GleamTech component instanceds to render the head for.</param>
            <param name="withoutJs">Whether to render JS includes or not.</param>
            <returns>A string containing the component resource tags that goes into &lt;head&gt; tag of the page.</returns>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(GleamTech.AspNet.UI.Component)">
            <summary>
            Renders and returns a string containing the required resource tags of JS includes (no CSS includes etc.) for a GleamTech component.
            This method can be preferred when you want to render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag 
            and render only CSS includes inside &lt;head&gt; tag via calling <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(GleamTech.AspNet.UI.Component,System.Boolean)" /> with withoutJs parameter set to true.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called at the bottom of the page before before the closing &lt;/body&gt; tag.
            If you call this method at some random place, the browser would misbehave.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="component">The GleamTech component instance to render the JS includes for.</param>
            <returns>A string containing the component resource tags that goes at the bottom of the page before before the closing &lt;/body&gt; tag.</returns>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderJs(System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component})">
            <summary>
            Renders and returns a string containing the required resource tags of JS includes (no CSS includes etc.) for multiple GleamTech components.
            This method can be preferred when you want to render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag 
            and render only CSS includes inside &lt;head&gt; tag via calling <see cref="M:GleamTech.AspNet.UI.ComponentRenderer.RenderHead(System.Collections.Generic.IEnumerable{GleamTech.AspNet.UI.Component},System.Boolean)" /> with withoutJs parameter set to true.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called at the bottom of the page before before the closing &lt;/body&gt; tag.
            If you call this method at some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="components">The GleamTech component instances to render the JS includes for which are on the same page.</param>
            <returns>A string containing the component resource tags that goes at the bottom of the page before before the closing &lt;/body&gt; tag.</returns>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.RenderBody(GleamTech.AspNet.UI.Component)">
            <summary>
            Renders and returns a string containing the actual body tags for a GleamTech component.
            This method should be called inside &lt;body&gt; tag (or child tags) of the page.
            If you call this method inside &lt;head&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, this method can be called separately
            for each component.
            </summary>
            <param name="component">The GleamTech component instance to render the component body for.</param>
            <returns>A string containing the component body tags that goes into &lt;body&gt; tag (or child tags) of the page.</returns>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.EnsureId(GleamTech.AspNet.UI.Component,System.Func{System.String,System.Object},System.Action{System.String,System.Object})">
            <summary>
            Ensures unique client Id for a GleamTech component which is to be rendered on the same page with other
            GleamTech components.
            This method should be called before RenderBody.
            If the component has no Id, it will be  automatically set to the class name of this component (e.g camel-case className)
            and if there are other instances on the same page, to className2 ... classNameX.
            </summary>
            <param name="component">The GleamTech component instance to fix the client Id.</param>
            <param name="getData">Platform specific page context method for determining if an Id is already used.</param>
            <param name="setData">Platform specific page context method for saving the set Id.</param>
        </member>
        <member name="M:GleamTech.AspNet.UI.ComponentRenderer.SaveState(GleamTech.AspNet.UI.StatefulComponent,System.String)">
            <summary>
            Saves the state to the session for a GleamTech component which is to be rendered.
            This method should be called before RenderBody.
            If the component has no StateId, it will be  automatically generated via hashing its Id and
            the value given in <paramref name="requestPath" /> parameter.
            </summary>
            <param name="statefulComponent">The GleamTech component instance to save the state to the session for.</param>
            <param name="requestPath">The host page's path for uniquely generating this component's StateId.</param>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssLength.Empty">
            <summary>
              Specifies an empty css length.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.#ctor(System.Int32)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:GleamTech.AspNet.UI.CssLength" /> structure with the specified 32-bit signed integer as 
               the value and <see langword="Pixel" /> as the (default) unit.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.#ctor(System.Double)">
            <summary>
                <para> Initializes a new instance of the <see cref="T:GleamTech.AspNet.UI.CssLength" /> structure with the 
               specified double-precision
               floating point number as the value and <see langword="Pixel" />
               as the (default) unit.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.#ctor(System.Double,GleamTech.AspNet.UI.CssUnit)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:GleamTech.AspNet.UI.CssLength" /> structure with the specified 
               double-precision floating point number as the value and the specified
            <see cref="T:GleamTech.AspNet.UI.CssUnit" /> as the unit.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:GleamTech.AspNet.UI.CssLength" /> structure with the specified text 
               string that contains the value and unit. If the unit is not
               specified, the default is <see langword="Pixel" />
               . </para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.#ctor(System.String,System.Globalization.CultureInfo)">
            <summary>
                <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.CssLength.IsEmpty">
            <summary>
                <para>Gets a value indicating whether the <see cref="T:GleamTech.AspNet.UI.CssLength" /> is empty.</para>
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.CssLength.Unit">
            <summary>
                <para>Gets the unit of the <see cref="T:GleamTech.AspNet.UI.CssLength" /> .</para>
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.CssLength.Value">
            <summary>
                <para>Gets the value of the <see cref="T:GleamTech.AspNet.UI.CssLength" /> .</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.GetHashCode">
            <summary>
                <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.Equals(System.Object)">
            <summary>
                <para>Compares this <see cref="T:GleamTech.AspNet.UI.CssLength" /> with the specified object.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.op_Equality(GleamTech.AspNet.UI.CssLength,GleamTech.AspNet.UI.CssLength)">
            <summary>
                <para>Compares two units to find out if they have the same value and type.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.op_Inequality(GleamTech.AspNet.UI.CssLength,GleamTech.AspNet.UI.CssLength)">
            <summary>
                <para>Compares two units to find out if they have different
                  values and/or types.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.GetStringFromType(GleamTech.AspNet.UI.CssUnit)">
            <summary>
             Converts UnitType to persistence string.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.GetTypeFromString(System.String)">
            <summary>
             Converts persistence string to UnitType.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.Parse(System.String)">
            <summary>
                <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.Parse(System.String,System.Globalization.CultureInfo)">
            <summary>
                <para>[To be supplied.]</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.Percentage(System.Double)">
            <summary>
                <para>Creates a <see cref="T:GleamTech.AspNet.UI.CssLength" /> of unit <see langword="Percentage" /> from the specified 32-bit signed integer.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.Pixel(System.Int32)">
            <summary>
                <para>Creates a <see cref="T:GleamTech.AspNet.UI.CssLength" /> of unit <see langword="Pixel" /> from the specified 32-bit signed integer.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.Point(System.Int32)">
            <summary>
                <para>Creates a <see cref="T:GleamTech.AspNet.UI.CssLength" /> of unit <see langword="Point" /> from the 
               specified 32-bit signed integer.</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.ToString">
            <internalonly />
            <summary>
                <para>Converts a <see cref="T:GleamTech.AspNet.UI.CssLength" /> to a <see cref="T:System.String" qualify="true" /> .</para>
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UI.CssLength.op_Implicit(System.Int32)~GleamTech.AspNet.UI.CssLength">
            <summary>
                <para>Implicitly creates a <see cref="T:GleamTech.AspNet.UI.CssLength" /> of unit <see langword="Pixel" /> from the specified 32-bit unsigned integer.</para>
            </summary>
        </member>
        <member name="T:GleamTech.AspNet.UI.CssUnit">
            <summary>
                <para> Specifies the css units.</para>
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Pixel">
            <summary>
               A pixel.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Point">
            <summary>
               A point.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Pica">
            <summary>
               A pica.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Inch">
            <summary>
               An inch.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Mm">
            <summary>
               A millimeter.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Cm">
            <summary>
                <para>A centimeter.</para>
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Percentage">
            <summary>
               A percentage.
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Em">
            <summary>
                <para> 
                  A unit of font width relative to its parent element's font.</para>
                <para>For example, if the font size of a phrase is 2em and it is within a paragraph 
                  whose font size is 10px, then the font size of the phrase is 20px.</para>
                <para>Refer to the World Wide Web Consortium Website for more information. </para>
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.CssUnit.Ex">
            <summary>
                <para>A unit of font height relative to its parent 
                  element's font.</para>
                <para>Refer to the World Wide Web Consortium Website for more 
                  information. </para>
            </summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.DisplayMode.Viewport">
            <summary>
            The component should fit the browser's viewport 
            (interior of the browser window). When used, <see cref="P:GleamTech.AspNet.UI.Component.Width" /> and 
            <see cref="P:GleamTech.AspNet.UI.Component.Height" /> properties will be discarded.</summary>
        </member>
        <member name="T:GleamTech.AspNet.UI.ResizeHandles">
            <summary>Defines the handles which can be used for resizing the component.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.North">
            <summary>North handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.South">
            <summary>South handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.East">
            <summary>East handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.West">
            <summary>West handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.SouthEast">
            <summary>South-east handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.SouthWest">
            <summary>South-west handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.NorthWest">
            <summary>North-west handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.NorthEast">
            <summary>North-east handle.</summary>
        </member>
        <member name="F:GleamTech.AspNet.UI.ResizeHandles.All">
            <summary>All handles.</summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.StatefulComponent.StateId">
            <summary>
            Gets or sets the state id of the component. 
            This is determined automatically when state is saved to session.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.StatefulComponent.DebugMode">
            <summary>
            Gets or sets a value indicating whether to display detailed error messages for troubleshooting.
            Exceptions will be displayed with stack trace, inner exceptions and data.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UI.WindowOptions.ResizeHandles">
            <summary>
            Gets or sets a value that specifies the handles which can be used for resizing the window.
            Default is All.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UserAgentInfo.#ctor(System.String)">
            <summary>
            Construct a UserAgent instance
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UserAgentInfo.Family">
            <summary>
            The family of user agent
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UserAgentInfo.Major">
            <summary>
            Major version of the user agent, if available
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UserAgentInfo.Minor">
            <summary>
            Minor version of the user agent, if available
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.UserAgentInfo.Patch">
            <summary>
            Patch version of the user agent, if available
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.UserAgentInfo.ToString">
            <summary>
            The user agent as a readable string
            </summary>
            <returns />
        </member>
        <member name="T:GleamTech.AspNet.WebActivation">
            <summary>
            Handles ASP.NET application activation.
            </summary>
        </member>
        <member name="M:GleamTech.AspNet.WebActivation.PreApplicationStart">
            <summary>Runs automatically before the ASP.NET application is started.</summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.ID">
            <summary>
            Gets or sets the control id and HTML id of the control. 
            Also a javascript variable with the same name is automatically defined and it can be used to access 
            the client-side implementation of the control. 
            If omitted, Id will be  automatically set to the class name of this control (e.g camel-case className without Control suffix)
            and if there are other instances on the same page, to className2 ... classNameX.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.Hidden">
            <summary>
            Gets or sets a value indicating whether the control is displayed when page is loaded. 
            When set to false, the control can be displayed later on client-side manually. 
            For example, the control can be displayed via a button's click event.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.Width">
            <summary>Gets or sets the width of the control.</summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.Height">
            <summary>Gets or sets the height of the control.</summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.Resizable">
            <summary>
            Gets or sets a value that specifies if the control can be resized when the user drags sides. 
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.ResizeHandles">
            <summary>
            Gets or sets a value that specifies the handles which can be used for resizing the control.
            Default is South, East and SouthEast.
            When using <see cref="F:GleamTech.AspNet.UI.DisplayMode.Window" /> mode, set <see cref="P:GleamTech.AspNet.UI.WindowOptions.ResizeHandles" />
            which overrides this property.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.DisplayMode">
            <summary>Gets or sets a value indicating whether the control is rendered as InPlace (default), Panel, Window or Viewport.</summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.WindowOptions">
            <summary>Gets or sets the windows options used for <see cref="F:GleamTech.AspNet.UI.DisplayMode.Window" /> mode.</summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.PanelOptions">
            <summary>Gets or sets the panel options used for <see cref="F:GleamTech.AspNet.UI.DisplayMode.Panel" /> mode.</summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.PageIsolation">
            <exclude />
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.ProductInfoRendered">
            <summary>Gets or sets a value indicating whether product info/copyright comment is rendered in html.</summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.ComponentControl`1.HeadTagsRendered">
            <exclude />
        </member>
        <member name="P:GleamTech.AspNet.WebForms.StatefulComponentControl`1.StateId">
            <summary>
            Gets or sets the state id of the control. 
            This is determined automatically when state is saved to session.
            </summary>
        </member>
        <member name="P:GleamTech.AspNet.WebForms.StatefulComponentControl`1.DebugMode">
            <summary>
            Gets or sets a value indicating whether to display detailed error messages for troubleshooting.
            Exceptions will be displayed with stack trace, inner exceptions and data.
            </summary>
        </member>
        <member name="T:GleamTech.AspNet.WebPages.WebPageExtensions">
            <summary>Adds GleamTech related extensions methods to <see cref="T:System.Web.WebPages.WebPage" /> class for ASP.NET Web Pages (Razor, WebMatrix) support.</summary>
        </member>
        <member name="M:GleamTech.AspNet.WebPages.WebPageExtensions.RenderHead(System.Web.WebPages.WebPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])">
            <summary>
            Renders the required resource tags (e.g. JS, CSS includes etc.) for one or more GleamTech components.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="webPage">The <see cref="T:System.Web.WebPages.WebPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the head for.</param>
            <param name="otherComponents">Other GleamTech component instances to render the head for which are on the same page.</param>
            <returns>The component resource tags that goes into &lt;head&gt; tag of the page.</returns>
        </member>
        <member name="M:GleamTech.AspNet.WebPages.WebPageExtensions.RenderHeadWithoutJs(System.Web.WebPages.WebPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])">
            <summary>
            Renders the required resource tags except JS includes (only CSS includes etc.) for one or more GleamTech components.
            This method can be preferred when you want to render only CSS includes inside &lt;head&gt; tag and
            render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag via calling
            <see cref="M:GleamTech.AspNet.WebPages.WebPageExtensions.RenderJs(System.Web.WebPages.WebPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called inside &lt;head&gt; tag of the page.
            If you call this method inside &lt;body&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="webPage">The <see cref="T:System.Web.WebPages.WebPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the head for.</param>
            <param name="otherComponents">Other GleamTech component instances to render the head for which are on the same page.</param>
            <returns>The component resource tags that goes into &lt;head&gt; tag of the page.</returns>
        </member>
        <member name="M:GleamTech.AspNet.WebPages.WebPageExtensions.RenderJs(System.Web.WebPages.WebPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])">
            <summary>
            Renders the required resource tags of JS includes (no CSS includes etc.) for one or more GleamTech components.
            This method can be preferred when you want to render only JS includes at the bottom of the page before before the closing &lt;/body&gt; tag 
            and render only CSS includes inside &lt;head&gt; tag via calling <see cref="M:GleamTech.AspNet.WebPages.WebPageExtensions.RenderHeadWithoutJs(System.Web.WebPages.WebPage,GleamTech.AspNet.UI.Component,GleamTech.AspNet.UI.Component[])" />.
            This practice of having CSS includes &lt;head&gt; and JS includes at the bottom before the closing &lt;/body&gt; tag, 
            is for increasing browser loading and rendering of your whole page;
            i.e. the user sees your page with your styles at first glance while the JS files are being loaded in the background.
            This method should be called at the bottom of the page before before the closing &lt;/body&gt; tag.
            If you call this method at some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, call this method once by passing
            all the component instances at once, this way the resources shared by all the components will be merged 
            and included in the page once.
            Make sure you are not calling this method multiple times and adding the result to the same page HTML,
            for example including the same JS file multiple times in the same page would cause browser errors.
            </summary>
            <param name="webPage">The <see cref="T:System.Web.WebPages.WebPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the JS includes for.</param>
            <param name="otherComponents">Other GleamTech component instances to render the JS includes for which are on the same page.</param>
            <returns>The component resource tags that goes at the bottom of the page before before the closing &lt;/body&gt; tag.</returns>
        </member>
        <member name="M:GleamTech.AspNet.WebPages.WebPageExtensions.RenderBody(System.Web.WebPages.WebPage,GleamTech.AspNet.UI.Component)">
            <summary>
            Renders the actual body tags for a GleamTech component.
            This method should be called inside &lt;body&gt; tag (or child tags) of the page.
            If you call this method inside &lt;head&gt; tag or some random place, the browser would misbehave.
            If you are rendering multiple GleamTech components in the same page, this method can be called separately
            for each component.
            </summary>
            <param name="webPage">The <see cref="T:System.Web.WebPages.WebPage" /> instance.</param>
            <param name="component">The GleamTech component instance to render the component body for.</param>
            <returns>The component body tags that goes into &lt;body&gt; tag (or child tags) of the page.</returns>
        </member>
        <member name="M:GleamTech.Caching.FileCache.#ctor(GleamTech.FileSystems.Location)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.Caching.FileCache" /> class.
            </summary>
            <param name="location">
            The location to store cached files. 
            The location can be a plain physical/virtual path string or
            a <see cref="T:GleamTech.FileSystems.Location" /> instance for one of the supported file systems like Amazon S3 and Azure Blob.
            </param>
        </member>
        <member name="P:GleamTech.Caching.FileCache.LocationString">
            <summary>
            Gets the string representation of location to store cached files. 
            </summary>
        </member>
        <member name="P:GleamTech.Caching.FileCache.LocationId">
            <summary>
            Gets the unique identifier for the location to store cached files. 
            </summary>
        </member>
        <member name="P:GleamTech.Caching.FileCache.MaxAge">
            <summary>
            Gets or sets a value that specifies the maximum amount of time to consider a cached item fresh and keep it stored in the cache.
            Cached items older than this age will be removed when the cache trimming (clean up) runs (when auto trim is run or
            when <see cref="M:GleamTech.Caching.FileCache.Trim" /> is manually called).
            The default value is 90 days.
            </summary>
        </member>
        <member name="P:GleamTech.Caching.FileCache.WaitTimeout">
            <summary>
            Gets or sets a value that specifies the maximum amount of time to wait for a cached item to be available (wait for a file lock from other threads or
            processes to become available, i.e. wait for an ongoing caching of the same file to complete) before giving up on the cache request.
            The default value is 5 minutes.
            </summary>
        </member>
        <member name="P:GleamTech.Caching.FileCache.AutoTrimInterval">
            <summary>
            Gets or sets a value that specifies the interval to run automatic cache trimming (clean up).
            Cached items older than <see cref="P:GleamTech.Caching.FileCache.MaxAge" /> will be removed when the cache is trimmed.
            If the value is 0 or negative, the auto trim is disabled.
            The default value is 20 minutes.
            </summary>
        </member>
        <member name="M:GleamTech.Caching.FileCache.Trim">
            <summary>
            Trims the cache (cleans up the expired cache items, i.e. cached items older than <see cref="P:GleamTech.Caching.FileCache.MaxAge" />).
            </summary>
        </member>
        <member name="M:GleamTech.Caching.FileCacheKey.ToString">
            <summary>Converts this instance to a string containing the cache key.</summary>
        </member>
        <member name="M:GleamTech.Caching.FileCacheSourceKey.#ctor(System.String,System.Int64,System.DateTime,System.Collections.Generic.IEnumerable{System.String})">
            <param name="file">
            The file name or path or extension (with or without leading dot) of the source file.
            Only the extension part will be used for the cache key for uniquely identifying this source file.
            If the file name or path does not have an extension, then the whole string (excluding the leading dot) 
            will be considered as the identifier for the cache key.
            </param>
            <param name="fileSize">
            The size of the source file. 
            This is required for generating a proper cache key for uniquely identifying this source file.
            </param>
            <param name="fileDateModified">
            The last modified time of the source file. 
            This is required for generating a proper cache key for uniquely identifying this source file.
            </param>
            <param name="changeList">
            Additional list of custom properties that should be included for unique cache key calculation.
            </param>
        </member>
        <member name="M:GleamTech.Caching.FileCacheSourceKey.ToString">
            <summary>Converts this instance to a string containing the cache key.</summary>
        </member>
        <member name="M:GleamTech.Collections.UniqueKeyedCollection`1.OnChange">
            <summary>Called whenever the collection changes.</summary>
        </member>
        <member name="M:GleamTech.Configuration.ConfigurationBase`1.Load(System.Boolean)">
            <summary>Loads from &lt;appSettings&gt; element of the Web.config or application configuration file.</summary>
        </member>
        <member name="P:GleamTech.Configuration.ProductConfiguration`1.LicenseKey">
            <summary>
            Sets the purchased license key to register the product and remove any limitations.
            </summary>
        </member>
        <member name="P:GleamTech.Configuration.ProductConfiguration`1.TrialExtensionKey">
            <summary>
            Sets the trial extension key. If your trial has expired and you need more time for evaluation, you can request a trial extension key from support.
            </summary>
        </member>
        <member name="P:GleamTech.Configuration.ProductConfigurationAttribute.KnownAssemblies">
            <summary>
            Comma separated assembly names.
            </summary>
        </member>
        <member name="M:GleamTech.Cryptography.CryptoManager.Base36Encode(System.Int64)">
            <summary>Encodes fastly long number to lower case base36 string.</summary>
            <param name="number">The long number to encode.</param>
            <returns />
        </member>
        <member name="T:GleamTech.Cryptography.Hashids">
            <summary>
            Generate YouTube-like hashes from one or many numbers. Use hashids when you do not want to expose your database ids to the user.
            </summary>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.#ctor">
            <summary>
            Instantiates a new Hashids with the default setup.
            </summary>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.#ctor(System.String,System.Int32,System.String,System.String)">
            <summary>
            Instantiates a new Hashids en/de-coder.
            </summary>
            <param name="salt" />
            <param name="minHashLength" />
            <param name="alphabet" />
            <param name="seps" />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.Encode(System.Int32[])">
            <summary>
            Encodes the provided numbers into a hashed string
            </summary>
            <param name="numbers">the numbers to encode</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.Encode(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Encodes the provided numbers into a hashed string
            </summary>
            <param name="numbers">the numbers to encode</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.Decode(System.String)">
            <summary>
            Decodes the provided hash into
            </summary>
            <param name="hash">the hash</param>
            <exception cref="T:System.OverflowException">if the decoded number overflows integer</exception>
            <returns>the numbers</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.EncodeHex(System.String)">
            <summary>
            Encodes the provided hex string to a hashids hash.
            </summary>
            <param name="hex" />
            <returns />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.DecodeHex(System.String)">
            <summary>
            Decodes the provided hash into a hex-string
            </summary>
            <param name="hash" />
            <returns />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.DecodeLong(System.String)">
            <summary>
            Decodes the provided hashed string into an array of longs 
            </summary>
            <param name="hash">the hashed string</param>
            <returns>the numbers</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.EncodeLong(System.Int64[])">
            <summary>
            Encodes the provided longs to a hashed string
            </summary>
            <param name="numbers">the numbers</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.EncodeLong(System.Collections.Generic.IEnumerable{System.Int64})">
            <summary>
            Encodes the provided longs to a hashed string
            </summary>
            <param name="numbers">the numbers</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.Encrypt(System.Int32[])">
            <summary>
            Encodes the provided numbers into a string.
            </summary>
            <param name="numbers">the numbers</param>
            <returns>the hash</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.EncryptHex(System.String)">
            <summary>
            Encrypts the provided hex string to a hashids hash.
            </summary>
            <param name="hex" />
            <returns />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.Decrypt(System.String)">
            <summary>
            Decodes the provided numbers into a array of numbers
            </summary>
            <param name="hash">hash</param>
            <returns>array of numbers.</returns>
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.DecryptHex(System.String)">
            <summary>
            Decodes the provided hash to a hex-string
            </summary>
            <param name="hash" />
            <returns />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.SetupSeps">
            <summary />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.SetupGuards">
            <summary />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.GenerateHashFrom(System.Int64[])">
            <summary>
            Internal function that does the work of creating the hash
            </summary>
            <param name="numbers" />
            <returns />
        </member>
        <member name="M:GleamTech.Cryptography.Hashids.ConsistentShuffle(System.String,System.String)">
            <summary />
            <param name="alphabet" />
            <param name="salt" />
            <returns />
        </member>
        <member name="T:GleamTech.Cryptography.IHashids">
            <summary>
            Describes a Hashids provider
            </summary>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.Decode(System.String)">
            <summary>
            Decodes the provided hashed string.
            </summary>
            <param name="hash">the hashed string</param>
            <exception cref="T:System.OverflowException">if one or many of the numbers in the hash overflowing the integer storage</exception>
            <returns>the numbers</returns>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.DecodeLong(System.String)">
            <summary>
            Decodes the provided hashed string into longs
            </summary>
            <param name="hash">the hashed string</param>
            <returns>the numbers</returns>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.DecodeHex(System.String)">
            <summary>
            Decodes the provided hashed string into a hex string
            </summary>
            <param name="hash">the hashed string</param>
            <returns>the hex string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.Encode(System.Int32[])">
            <summary>
            Encodes the provided numbers into a hashed string
            </summary>
            <param name="numbers">the numbers</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.Encode(System.Collections.Generic.IEnumerable{System.Int32})">
            <summary>
            Encodes the provided numbers into a hashed string
            </summary>
            <param name="numbers">the numbers</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.EncodeLong(System.Int64[])">
            <summary>
            Encodes the provided numbers into a hashed string
            </summary>
            <param name="numbers">the numbers</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.EncodeLong(System.Collections.Generic.IEnumerable{System.Int64})">
            <summary>
            Encodes the provided numbers into a hashed string
            </summary>
            <param name="numbers">the numbers</param>
            <returns>the hashed string</returns>
        </member>
        <member name="M:GleamTech.Cryptography.IHashids.EncodeHex(System.String)">
            <summary>
            Encodes the provided hex string
            </summary>
            <param name="hex">the hex string</param>
            <returns>the hashed string</returns>
        </member>
        <member name="T:GleamTech.Cryptography.MD5Managed">
            <summary>
            MD5Managed: A HashAlgorithm implementation that acts as a thin wrapper
            around a C# translation of the MD5 reference implementation. The C code
            has been translated as closely as possible so that most of the original
            structure remains and comparisons between the two are straightforward.
            </summary>
            <remarks>
            Derived from the RSA Data Security, Inc. MD5 Message-Digest Algorithm.
            
            Specification:
            RFC1321 - The MD5 Message-Digest Algorithm
            http://www.faqs.org/rfcs/rfc1321.html
            
            Original license:
            Copyright (C) 1991-2, RSA Data Security, Inc. Created 1991. All
            rights reserved.
            
            License to copy and use this software is granted provided that it
            is identified as the "RSA Data Security, Inc. MD5 Message-Digest
            Algorithm" in all material mentioning or referencing this software
            or this function.
            
            License is also granted to make and use derivative works provided
            that such works are identified as "derived from the RSA Data
            Security, Inc. MD5 Message-Digest Algorithm" in all material
            mentioning or referencing the derived work.
            
            RSA Data Security, Inc. makes no representations concerning either
            the merchantability of this software or the suitability of this
            software for any particular purpose. It is provided "as is"
            without express or implied warranty of any kind.
            
            These notices must be retained in any copies of any part of this
            documentation and/or software.
            </remarks>
        </member>
        <member name="M:GleamTech.Cryptography.MD5Managed.#ctor">
            <summary>
            Initializes a new instance.
            </summary>
        </member>
        <member name="M:GleamTech.Cryptography.MD5Managed.Initialize">
            <summary>
            Initializes internal state.
            </summary>
        </member>
        <member name="M:GleamTech.Cryptography.MD5Managed.InitializeVariables">
            <summary>
            Initializes variables.
            </summary>
        </member>
        <member name="M:GleamTech.Cryptography.MD5Managed.HashCore(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Updates the hash code with the data provided.
            </summary>
            <param name="array">Data to hash.</param>
            <param name="ibStart">Start position.</param>
            <param name="cbSize">Number of bytes.</param>
        </member>
        <member name="M:GleamTech.Cryptography.MD5Managed.HashFinal">
            <summary>
            Finalizes the hash code and returns it.
            </summary>
        </member>
        <member name="P:GleamTech.Cryptography.MD5Managed.Hash">
            <summary>
            Returns the hash as an array of bytes.
            </summary>
        </member>
        <member name="T:GleamTech.DistributedLocking.DistributedLock">
            <summary>
            Represents an exclusive distributed lock.
            </summary>
            <remarks>To be valid, the distributed lock object must be obtained from the <see cref="T:GleamTech.DistributedLocking.DistributedLockManager">DistributedLockManager class</see>.
            When you're done with this lock, dispose it to release it.</remarks>
        </member>
        <member name="E:GleamTech.DistributedLocking.DistributedLock.Disposed">
            <summary>
            Raised when the lock is disposed.
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLock.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.Name">
            <summary>
            The name of the lock within the repository.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.Owner">
            <summary>
            The object that is currently holding the lock.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.OwningThread">
            <summary>
            The thread that created and waits on this request and owns the lock when this request is granted.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.OwningThreadId">
            <summary>
            The ManagedThreadId of the thread that owns this lock instance.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.WaitForLock">
            <summary>
            Whether this lock request is willing to wait (finite) for the lock or return immediately if not available.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.WaitTimeout">
            <summary>
            The clock time at which this lock request wants to stop waiting for the lock and give up.
            (MaxValue once the lock is granted, MinValue if the lock was denied.)
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.ActualLock">
            <summary>
            The actual holder of the lock if we are a secondary lock on the same thread, or ourselves if we hold the file lock.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.IsSecondaryLock">
            <summary>
            Reports if this lock object holds a secondary lock rather than the actual lock (or no lock).
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.IsExpired">
            <summary>
            Reports if this request instance has expired and should be skipped over because no thread is still waiting on it.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.IsDisposed">
            <summary>
            Whether this lock instance has been disposed (and thus does not hold any locks).
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.DisposeProxyOnClose">
            <summary>
            Gets or sets the dispose-on-close policy for the lock proxy associated with this lock instance.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLock.OurLockProxy">
            <summary>
            The proxy who will actually hold the file lock on our behalf.
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLock.Dispose(System.Boolean)">
            <summary>
            Performs the actual releasing of managed and unmanaged resources.
            Most usage should instead call Dispose(), which will call Dispose(true) for you
            and will suppress redundant finalization.
            </summary>
            <param name="releaseManaged">Indicates whether to release managed resources.
            This should only be called with true, except from the finalizer which should call Dispose(false).</param>
        </member>
        <member name="T:GleamTech.DistributedLocking.DistributedLockManager">
            <summary>
            A multiprocess lock manager for repositories
            </summary>
            <remarks>Manages locking first within the process and then extends the process lock to multiple processes
            by locking a file on disk.  Designed for use with the Using statement as opposed to the Lock statement.</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockManager.#ctor">
            <summary>
            Create a new distributed lock manager, denoting a scope of locks.
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockManager.Lock(System.Object,System.String,System.Int32,GleamTech.DistributedLocking.IDistributedLockProvider)">
            <summary>
            Acquire a named lock
            </summary>
            <param name="requester">The object that is requesting the lock (useful for debugging purposes)</param>
            <param name="name">The name of the lock to get within the current scope</param>
            <param name="timeoutSeconds">The maximum number of seconds to wait on the lock before giving up.</param>
            <param name="provider">The provider to use for the lock</param>
            <returns>A disposable Lock object if the lock was acquired.</returns>
            <exception cref="T:GleamTech.DistributedLocking.LockTimeoutException">Thrown if the lock can not be acquired within the timeout specified</exception>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockManager.TryLock(System.Object,System.String,System.Int32,GleamTech.DistributedLocking.IDistributedLockProvider,GleamTech.DistributedLocking.DistributedLock@)">
            <summary>
            Attempt to lock the repository with the provided index path.
            </summary>
            <param name="requester">The object that is requesting the lock (useful for debugging purposes)</param>
            <param name="name">The name of the lock to get within the current scope</param>
            <param name="timeoutSeconds">The maximum number of seconds to wait on the lock before giving up.</param>
            <param name="provider">The provider to use for the lock</param>
            <param name="grantedLock">The disposable Lock object if the lock was acquired.</param>
            <returns>True if the lock was acquired or false if the lock timed out.</returns>
        </member>
        <member name="T:GleamTech.DistributedLocking.DistributedLockProxy">
            <summary>
            A class to hold a file lock for this process (app domain) and pass it fairly to other waiting threads before release.
            </summary>
        </member>
        <member name="E:GleamTech.DistributedLocking.DistributedLockProxy.Disposed">
            <summary>
            Raised when the lock is disposed.
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockProxy.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.Name">
            <summary>
            The name of the lock within the scope.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.IsDisposed">
            <summary>
            Whether this lock instance has been disposed (and thus does not hold any locks).
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.WaitingCount">
            <summary>
            Reports how many threads are in the queue waiting on the lock (some may have timed out and given up already).
            (Reports -1 if the proxy is idle (no current turn).)
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.CurrentLockTurn">
            <summary>
            The lock request with the current turn to hold or wait for the lock.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.CurrentTurnOwner">
            <summary>
            The requesting owner of the current turn for the lock.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.CurrentTurnThread">
            <summary>
            The thread with the current turn for the lock.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.CurrentTurnThreadId">
            <summary>
            The ManagedThreadId of the thread with the current turn for the lock, or -1 if none.  (For debug convenience only.)
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.DistributedLockProxy.DisposeOnClose">
            <summary>
            Object persistence policy for this instance:  Whether to dispose this instance when file lock is released.
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockProxy.CheckCurrentTurnThread(GleamTech.DistributedLocking.DistributedLock)">
            <summary>
            Check the thread with the current turn for the lock and grant a secondary lock if applicable.
            </summary>
            <param name="candidateLock">An unexpired lock request on the current thread, or null to just check the turn thread.</param>
            <returns>The Thread with the current turn for the lock, or null if there are none holding or waiting.</returns>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockProxy.QueueRequest(GleamTech.DistributedLocking.DistributedLock)">
            <summary>
            Queue a lock request (RepositoryLock instance).  Must be followed by a call to AwaitOurTurnOrTimeout (which can block).
            </summary>
            <param name="lockRequest" />
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockProxy.AwaitOurTurnOrTimeout(GleamTech.DistributedLocking.DistributedLock,GleamTech.DistributedLocking.IDistributedLockProvider)">
            <summary>
            Wait for our turn to have the lock (and wait for the lock) up to our time limit
            </summary>
            <param name="lockRequest" />
            <param name="_provider" />
            <returns />
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockProxy.TryGetLock(GleamTech.DistributedLocking.DistributedLock,GleamTech.DistributedLocking.IDistributedLockProvider)">
            <summary>
            Try to get the actual file lock on behalf of the current request.
            </summary>
            <param name="currentRequest" />
            <param name="_provider" />
            <returns />
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockProxy.StartNextTurn(GleamTech.DistributedLocking.DistributedLock)">
            <summary>
            Find the next request still waiting and signal it to go.  Or return true if the current caller may proceed.
            </summary>
            <param name="currentRequest">The request the caller is waiting on, or null for none.</param>
            <returns>True if the caller's supplied request is the next turn, false otherwise.</returns>
        </member>
        <member name="M:GleamTech.DistributedLocking.DistributedLockProxy.Dispose(System.Boolean)">
            <summary>
            Performs the actual releasing of managed and unmanaged resources.
            Most usage should instead call Dispose(), which will call Dispose(true) for you
            and will suppress redundant finalization.
            </summary>
            <param name="releaseManaged">Indicates whether to release managed resources.
            This should only be called with true, except from the finalizer which should call Dispose(false).</param>
        </member>
        <member name="T:GleamTech.DistributedLocking.IDistributedLockProvider">
            <summary>
            Creates and manages distributed locks
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.IDistributedLockProvider.Name">
            <summary>
            A unique name for this lock provider and its scope
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.IDistributedLockProvider.GetLock(System.String)">
            <summary>
            Attempts to get an exclusive lock.
            </summary>
            <param name="name">The unique name of the lock.</param>
            <returns>A disposable object if locked, null otherwise</returns>
            <remarks>Callers should check the provided handle for null to ensure they got the lock on the file.
            If it is not null, it must be disposed to release the lock in a timely manner.</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.IDistributedLockProvider.GetLockRequest(System.String)">
            <summary>
            Attempts to request a turn at an exclusive lock.
            </summary>
            <param name="name">The unique name of the lock.</param>
            <returns>A disposable object holding a lock request if available, null otherwise</returns>
            <remarks>Callers should check the provided handle for null to ensure they got a valid lock request on the file.
            If it is not null, it must be disposed to release the request when expired or full lock is acquired.</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.IDistributedLockProvider.CheckLockRequest(System.String)">
            <summary>
            Check if a lock request is pending (without blocking).
            </summary>
            <param name="name">The unique name of the lock.</param>
            <returns>True if a lock request is pending, false otherwise.</returns>
        </member>
        <member name="T:GleamTech.DistributedLocking.LockException">
            <summary>
            Represents exceptions within the Distributed Locking system.
            </summary>
        </member>
        <member name="F:GleamTech.DistributedLocking.LockException.s_BreakPointGibraltarExceptions">
            <summary>
            A temporary flag to tell us whether to invoke a Debugger.Break() on all of our exceptions.
            </summary>
            <remarks>True enables breakpointing, false disables.  This should probably be replaced with an enum
            to support multiple modes, assuming the basic usage works out.</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockException.BreakPoint">
            <summary>
            Automatically stop debugger like a breakpoint, if enabled.
            </summary>
            <remarks>This will check the state of GibraltarExceptions.s_BreakPointGibraltarExceptions</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockException.#ctor">
            <summary>
            Initializes a new instance of the GibraltarException class.
            </summary>
            <remarks>This constructor initializes the Message property of the new instance to a system-supplied
            message that describes the error and takes into account the current system culture.
            For more information, see the base constructor in Exception.</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the LockException class with a specified error message.
            </summary>
            <param name="message">The error message string.</param>
            <remarks>This constructor initializes the Message property of the new instance using the
            message parameter.  The InnerException property is left as a null reference.
            For more information, see the base constructor in Exception.</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the LockException class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message string.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a
            null reference if no inner exception is specified.</param>
            <remarks>An exception that is thrown as a direct result of a previous exception should include
            a reference to the previous exception in the innerException parameter.
            For more information, see the base constructor in Exception.</remarks>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the LockException class with serialized data.
            </summary>
            <param name="info">The SerializationInfo that holds the serialized object data about
            the exception being thrown.</param>
            <param name="context">The StreamingContext that contains contextual information about
            the source or destination.</param>
            <remarks>This constructor is called during deserialization to reconstitute the exception object
            transmitted over a stream.  For more information, see the base constructor in Exception.</remarks>
        </member>
        <member name="T:GleamTech.DistributedLocking.LockTimeoutException">
            <summary>
            Thrown to indicate a failure to acquire the requested repository lock
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockTimeoutException.#ctor(System.String,System.String,System.Int32,System.String)">
            <summary>
            Create a new exception instance for a lock timeout
            </summary>
            <param name="providerName">The name of the distributed lock provider</param>
            <param name="lockName">The name of the lock to get (locks are a combination of index and this name)</param>
            <param name="timeoutSeconds">The maximum number of seconds to wait on the lock before giving up.</param>
            <param name="message">The error message string.</param>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockTimeoutException.#ctor(System.String,System.String,System.Int32,System.String,System.Exception)">
            <summary>
            Create a new exception instance for a lock timeout
            </summary>
            <param name="providerName">The name of the distributed lock provider</param>
            <param name="lockName">The name of the lock to get (locks are a combination of index and this name)</param>
            <param name="timeoutSeconds">The maximum number of seconds to wait on the lock before giving up.</param>
            <param name="message">The error message string.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a
            null reference if no inner exception is specified.</param>
        </member>
        <member name="P:GleamTech.DistributedLocking.LockTimeoutException.ProviderName">
            <summary>
            The unique name for the lock provider
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.LockTimeoutException.TimeoutSeconds">
            <summary>
            The number of seconds the lock waited before it timed out.
            </summary>
        </member>
        <member name="P:GleamTech.DistributedLocking.LockTimeoutException.LockName">
            <summary>
            The name of the lock being acquired
            </summary>
        </member>
        <member name="M:GleamTech.DistributedLocking.LockTimeoutException.ToString">
            <summary>
            Creates and returns a string representation of the current exception.
            </summary>
            <returns>
            A string representation of the current exception.
            </returns>
            <filterpriority>1</filterpriority>
            <PermissionSet>
                <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
            </PermissionSet>
        </member>
        <member name="T:GleamTech.Examples.ExampleExplorerState">
            <tocexclude />
        </member>
        <member name="P:GleamTech.Examples.ExampleFileSelector.SourceFolder">
            <summary>
            Gets or sets the folder for example files.
            The default value is "~/App_Data/ExampleFiles".
            </summary>
        </member>
        <member name="P:GleamTech.Examples.ExampleFileSelector.UploadFolder">
            <summary>
            Gets or sets the folder for uploading files to.
            The default value is "~/App_Data/Uploads".
            </summary>
        </member>
        <member name="P:GleamTech.Examples.ExampleFileSelectorControl.SourceFolder">
            <summary>
            Gets or sets the folder for example files.
            The default value is "~/App_Data/ExampleFiles".
            </summary>
        </member>
        <member name="P:GleamTech.Examples.ExampleFileSelectorControl.UploadFolder">
            <summary>
            Gets or sets the folder for uploading files to.
            The default value is "~/App_Data/Uploads".
            </summary>
        </member>
        <member name="M:GleamTech.Examples.ExamplesConfiguration.InitCurrent">
            <summary>Initializes only the static Current instance.</summary>
        </member>
        <member name="P:GleamTech.Examples.ExamplesConfiguration.Current">
            <summary>Gets current global configuration instance.</summary>
        </member>
        <member name="P:GleamTech.Examples.ExamplesConfiguration.AssemblyInfo">
            <summary>Gets information on this library's assembly.</summary>
        </member>
        <member name="M:GleamTech.FileSystems.FileSystem.GetPath(GleamTech.IO.BackSlashPath)">
            <param name="relativeName">The normalized relative name (without leading or trailing slash).</param>
        </member>
        <member name="T:GleamTech.GleamTechConfiguration">
            <summary>
            Provides properties and methods for changing this library's configuration.
            </summary>
        </member>
        <member name="M:GleamTech.GleamTechConfiguration.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.GleamTechConfiguration" /> class.
            </summary>
        </member>
        <member name="P:GleamTech.GleamTechConfiguration.TemporaryFolder">
            <summary>
            Gets or sets a value that specifies the temporary folder used as default by GleamTech components.
            The default value is "[System Temporary Folder]\GleamTech".
            Value can be a virtual or physical path.
            <para>[System Temporary Folder] can be different for different environments:</para><list type="bullet"><item><description><para>- For "ASP.NET Classic":</para><para>-- "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\Temporary ASP.NET Files\APPNAME\xxxxxxxx"</para><para>-- "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\Temporary ASP.NET Files\APPNAME\xxxxxxxx" (if 32-bit AppPool)</para><para>-- "%LOCALAPPDATA%\Temp\Temporary ASP.NET Files\vs\xxxxxxxx" (when debugging in Visual Studio)</para></description></item><item><description><para>- For "ASP.NET Core" the path set in "ASPNETCORE_TEMP" environment variable or result of Path.GetTempPath which depends on "TMP", "TEMP" and "USERPROFILE" environment variables:</para><para>-- "%WINDIR%\Temp"</para><para>-- "%LOCALAPPDATA%\Temp" (if "Load User Profile" is enabled for AppPool in IIS)</para></description></item><item><description><para>- For Azure WebApp Service:</para><para>-- "D:\home" (%HOME% environment variable)</para></description></item><item><description><para>- For Console or other non-web apps, result of Path.GetTempPath which depends on "TMP", "TEMP" and "USERPROFILE" environment variables:</para><para>-- "%LOCALAPPDATA%\Temp"</para><para>-- "%WINDIR%\Temp"</para></description></item></list></summary>
        </member>
        <member name="E:GleamTech.GleamTechConfiguration.TemporaryFolderChanged">
            <summary>
            This event is raised when <see cref="P:GleamTech.GleamTechConfiguration.TemporaryFolder" /> property is changed.
            </summary>
        </member>
        <member name="P:GleamTech.GleamTechConfiguration.AlphaFileSystemEnabled">
            <summary>
            Gets or sets a value that specifies whether AlphaFS should be used instead of System.IO
            for <see cref="T:GleamTech.FileSystems.Physical.PhysicalFileSystem" />.
            The default is true.
            </summary>
        </member>
        <member name="P:GleamTech.GleamTechConfiguration.LogEnabled">
            <summary>
            Gets or sets a value that specifies whether logging is enabled.
            The default is false.
            </summary>
        </member>
        <member name="P:GleamTech.GleamTechConfiguration.LogFile">
            <summary>
            Gets or sets a value that specifies the path of log file.
            The default value is "[<see cref="P:GleamTech.GleamTechConfiguration.TemporaryFolder" />]\GleamTech.log".
            Value can be a virtual or physical path.
            </summary>
        </member>
        <member name="M:GleamTech.GleamTechConfiguration.InitCurrent">
            <summary>
            Initializes only the static Current instance.
            </summary>
        </member>
        <member name="P:GleamTech.GleamTechConfiguration.Current">
            <summary>Gets current global configuration instance.</summary>
        </member>
        <member name="M:GleamTech.GleamTechConfiguration.EnsureAssemblies">
            <summary>
            Ensures GleamTech's assemblies are loaded and initialized.
            You may need to call this method in entry point/startup of your application, if you receive "Could not load file or assembly ..." errors.
            Some DLLs are embedded inside GleamTech's DLLs and they are resolved automatically when they are requested however
            internal AssemblyResolver should be initialized first and internal AssemblyResolver of each GleamTech DLL can be initialized
            only when a type inside that DLL is accessed first. So in some cases, even a GleamTech DLL is loaded, its AssemblyResolver
            may not be initialized yet (because none of its types is accessed yet). You can observe this behaviour if your application
            calls some methods which scans assemblies for reflection purpose (reflection-only type access does not trigger DLL's module initializer),
            for example ASP.NET Core's endpoints.MapControllers method and SimpleInjector’s RegisterMvcControllers method.
            So to prevent errors on those methods, you can call this method before such methods.
            Note that you should not need to call this method usually for web applications, because it is handled automatically
            as we can know when the application is started by hooking up (automatically in ASP.NET Classic and when you call AddGleamTech and
            UseGleamTech methods in ASP.NET Core).
            </summary>
        </member>
        <member name="T:GleamTech.Globalization.Unidecoder">
            <summary>
            ASCII transliterations of Unicode text
            </summary>
        </member>
        <member name="M:GleamTech.Globalization.Unidecoder.Unidecode(System.String,System.Nullable{System.Int32})">
            <summary>
            Transliterate Unicode string to ASCII string.
            </summary>
            <param name="input">String you want to transliterate into ASCII</param>
            <param name="tempStringBuilderCapacity">
                If you know the length of the result,
                pass the value for StringBuilder capacity.
                InputString.Length*2 is used by default.
            </param>
            <returns>
                ASCII string. There are [?] (3 characters) in places of some unknown(?) unicode characters.
                It is this way in Python code as well.
            </returns>
        </member>
        <member name="M:GleamTech.Globalization.Unidecoder.Unidecode(System.Char)">
            <summary>
            Transliterate Unicode character to ASCII string.
            </summary>
            <param name="c">Character you want to transliterate into ASCII</param>
            <returns>
                ASCII string. Unknown(?) unicode characters will return [?] (3 characters).
                It is this way in Python code as well.
            </returns>
        </member>
        <member name="T:GleamTech.IO.DelegatingStream">
            <summary> Stream that delegates to inner stream.</summary>
        </member>
        <member name="T:GleamTech.IO.NonClosingStream">
            <summary>Stream that doesn't close the inner stream when closed.</summary>
        </member>
        <member name="T:GleamTech.IO.StreamOptions">
            <summary>Represents stream options that can be used with a stream opening method or delegate.</summary>
        </member>
        <member name="P:GleamTech.IO.StreamOptions.LeaveOpen">
            <summary>
            Gets or sets a value that specifies whether to leave the stream open after reading or writing is complete.
            The default is false. 
            </summary>
        </member>
        <member name="P:GleamTech.IO.StreamOptions.RewindAfterUse">
            <summary>
            Gets or sets a value that specifies whether to seek stream to beginning after being used. 
            This is usually useful for streams where you write to and then read from, such as a MemoryStream.
            Effective only when <see cref="P:GleamTech.IO.StreamOptions.LeaveOpen" /> is also true.
            The default is false. 
            </summary>
        </member>
        <member name="T:GleamTech.IO.StreamWrapper">
            <summary>Wraps the result of a stream opening method or delegate, along with stream options.</summary>
        </member>
        <member name="M:GleamTech.IO.StreamWrapper.#ctor(System.IO.Stream,GleamTech.IO.StreamOptions)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.IO.StreamWrapper" /> class with a <see cref="T:System.IO.Stream" /> instance.</summary>
            <param name="stream">The stream to read from or write to.</param>
            <param name="streamOptions">The stream options.</param>
        </member>
        <member name="M:GleamTech.IO.StreamWrapper.GetStream">
            <summary>
            Gets the stream to read from or write to.
            </summary>
        </member>
        <member name="M:GleamTech.IO.TemporaryFolder.GetSystemTemporaryFolder">
            <summary>
            Gets the system temporary folder for GleamTech.
            The default value is "[System Temporary Folder]\GleamTech".
            <para>[System Temporary Folder] can be different for different environments:</para><list type="bullet"><item><description><para>- For "ASP.NET Classic" or "ASP.NET Core on .NET Framework":</para><para>-- "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\Temporary ASP.NET Files\APPNAME\xxxxxxxx"</para><para>-- "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\Temporary ASP.NET Files\APPNAME\xxxxxxxx" (if 32-bit AppPool)</para><para>-- "%LOCALAPPDATA%\Temp\Temporary ASP.NET Files\vs\xxxxxxxx" (when debugging in Visual Studio)</para></description></item><item><description><para>- For "ASP.NET Core on .NET Core":</para><para>-- "%WINDIR%\Temp" (or the path set in "ASPNETCORE_TEMP" environment variable)</para></description></item><item><description><para>- For Console or other non-web apps:</para><para>-- "%LOCALAPPDATA%\Temp" (result of Path.GetTempPath depends on "TMP", "TEMP" and "USERPROFILE" environment variables)</para></description></item><item><description><para>- For Azure WebApp Service:</para><para>-- "D:\home" (%HOME% environment variables)</para></description></item></list></summary>
        </member>
        <member name="M:GleamTech.IO.TemporaryFolder.GetSystemTemporaryPathContext">
            <summary>
            Reverts to Application Pool Identity only if required.
            If web mode, base temporary folder will be under "Temporary ASP.NET Files"
            so to allow accessing this folder, any impersonation will be undone.
            </summary>
        </member>
        <member name="T:GleamTech.Reflection.AssemblyResourceHelper">
            <summary>
            Class for handling resources embedded in assemblies. Allows case insensitive resource names.
            </summary>
        </member>
        <member name="M:GleamTech.Reflection.AssemblyResourceHelper.GetResourceStream(System.Reflection.Assembly,System.String,System.String)">
            <summary>
            Gets the stream of an embedded resource from a given assembly. Allows case insensitive resource names.
            </summary>
            <param name="resourceAssembly">Assembly which contains the embedded resource.</param>
            <param name="resourceNamespace">Namespace of the embedded resource in the assembly. Case insensitive values are allowed.</param>
            <param name="relativeFilePath">File path of the embedded resource in the assembly. Path should be in [Folder1]\[Folder2]\[FileName] format and case insensitive values are allowed.</param>
            <returns>The stream of the embedded resource or null if the given resource name does not exist</returns>
        </member>
        <member name="T:GleamTech.Reflection.PreApplicationStartMethodAttribute">
            <summary>
            Provides expanded support for application startup.
            This is a replacement for System.Web.PreApplicationStartMethodAttribute for GleamTech's ASP.NET Core on .NET Core support.
            </summary>
        </member>
        <member name="M:GleamTech.Reflection.PreApplicationStartMethodAttribute.#ctor(System.Type,System.String)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.Reflection.PreApplicationStartMethodAttribute" /> class.</summary>
            <param name="type">An object that describes the type of the startup method..</param>
            <param name="methodName">An empty parameter signature that has no return value. </param>
        </member>
        <member name="P:GleamTech.Reflection.PreApplicationStartMethodAttribute.Type">
            <summary>Gets the type that is returned by the associated startup method.</summary>
            <returns>An object that describes the type of the startup method.</returns>
        </member>
        <member name="P:GleamTech.Reflection.PreApplicationStartMethodAttribute.MethodName">
            <summary>Gets the associated startup method.</summary>
            <returns>A string that contains the name of the associated startup method.</returns>
        </member>
        <member name="T:GleamTech.Resources.AssemblyResourceLocator">
            <summary>Dummy type for locating the root namespace of the assembly resources.</summary>
        </member>
        <member name="T:GleamTech.Resources.Strings">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ArchiveFileSystem_InvalidArchive">
            <summary>
              Looks up a localized string similar to The archive "{0}" is either in unknown format or damaged..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ArchiveFileSystem_NotWritable">
            <summary>
              Looks up a localized string similar to The archive "{0}" is not supported for writing so this action is not allowed..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ArchiveFileSystem_UnknownExtension">
            <summary>
               Looks up a localized string similar to The archive "{0}" is not supported.
            Supported archive file extensions: {1}.
             </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.AspNetRequest_StreamNotAccessible">
            <summary>
              Looks up a localized string similar to Cannot access the request stream. Please make sure no HttpModule is intercepting the request before the upload handler..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.AssemblyResourceStore_OverrideFileOpenError">
            <summary>
              Looks up a localized string similar to Can not open resource override file '{0}'..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.AssemblyResourceStore_ResourceNotFound">
            <summary>
              Looks up a localized string similar to Resource '{1}' not found in assembly '{0}'..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.AssemblyResourceStore_ResourceOpenError">
            <summary>
              Looks up a localized string similar to Can not open resource '{1}' in assembly '{0}'..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ComponentControl_PageHeaderNotAvailable">
            <summary>
              Looks up a localized string similar to {0} requires the &lt;head&gt; element of the page to be marked with runat="server"..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ConfigurationFile_InvalidFileFormat">
            <summary>
              Looks up a localized string similar to Invalid configuration file format..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ConfigurationFile_InvalidVersionAttribute">
            <summary>
              Looks up a localized string similar to Version attribute is invalid..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ConfigurationFile_VersionNotSupported">
            <summary>
              Looks up a localized string similar to Configuration file version is not supported..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ExceptionUtil_ParameterEmptyArray">
            <summary>
              Looks up a localized string similar to Parameter cannot be empty array..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ExceptionUtil_ParameterEmptyString">
            <summary>
              Looks up a localized string similar to Parameter cannot be empty string..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ExceptionUtil_ParameterNull">
            <summary>
              Looks up a localized string similar to Parameter cannot be null..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileResponse_InvalidRange">
            <summary>
               Looks up a localized string similar to Download request refers to an invalid range.
            File size: {0}
            Requested range: {1}.
             </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileResponse_PreconditionFailed">
            <summary>
              Looks up a localized string similar to A precondition to the request has failed: [{0}]..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_AccessDenied">
            <summary>
              Looks up a localized string similar to Access to the path "{0}" is denied due to insufficient permissions. Please make sure the current windows identity "{1}" has the required permissions on the path..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_DestinationIsSameFile">
            <summary>
              Looks up a localized string similar to The destination file is the same as the source file "{0}"..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_DestinationIsSameFolder">
            <summary>
              Looks up a localized string similar to The destination folder is the same as the source folder "{0}"..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_DestinationIsSubfolder">
            <summary>
              Looks up a localized string similar to The destination folder "{1}" is a subfolder of the source folder "{0}"..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_FileAlreadyExists">
            <summary>
              Looks up a localized string similar to Cannot create "{0}" because a file with the same name already exists..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_FileNotFound">
            <summary>
              Looks up a localized string similar to Could not find file "{0}"..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_FolderAlreadyExists">
            <summary>
              Looks up a localized string similar to Cannot create "{0}" because a folder with the same name already exists..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_FolderNotFound">
            <summary>
              Looks up a localized string similar to Could not find folder "{0}"..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.FileSystem_InsufficientTrustLevel">
            <summary>
              Looks up a localized string similar to Trust level does not allow access to the path..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Hosting_InvalidInitialization">
            <summary>
              Looks up a localized string similar to {0} is null. Hosting is not initialized properly..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Hosting_NotFound">
            <summary>
              Looks up a localized string similar to {0} does not exist because this application is not hosted. If this application is actually hosted, then PreApplicationStart method was not automatically triggered for ASP.NET Classic or app.UseGleamTech method was not manually called for ASP.NET Core..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ImpersonationContext_FailedToImpersonateUser">
            <summary>
               Looks up a localized string similar to Failed to impersonate user "{0}".
            {1}.
             </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ImpersonationContext_FailedToRevertUser">
            <summary>
               Looks up a localized string similar to Failed to revert impersonation of user "{0}".
            {1}.
             </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.JsonMethodHandler_InvalidHttpMethod">
            <summary>
              Looks up a localized string similar to Only http method POST is accepted..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.JsonMethodHandler_InvalidMethodRequest">
            <summary>
              Looks up a localized string similar to Invalid method request: {0}.
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.JsonMethodHandler_InvalidParameter">
            <summary>
              Looks up a localized string similar to At least one parameter is invalid..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.JsonMethodHandler_MethodNotFound">
            <summary>
              Looks up a localized string similar to Method with name "{0}" not found..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.JsonMethodHandler_MissingParameter">
            <summary>
              Looks up a localized string similar to Parameter "{0}" is missing..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Language_EntryNotFound">
            <summary>
              Looks up a localized string similar to Language entry with key "{0}" not found..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Language_EntryValueMissing">
            <summary>
              Looks up a localized string similar to Value for language entry with key "{0}" is missing..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LanguageFile_InvalidFileFormat">
            <summary>
              Looks up a localized string similar to Invalid language file format..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LanguageFile_InvalidVersionAttribute">
            <summary>
              Looks up a localized string similar to Version attribute is invalid..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LanguageFile_VersionNotSupported">
            <summary>
              Looks up a localized string similar to Language file version is not supported..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LicenseBase_DesktopRestricted">
            <summary>
              Looks up a localized string similar to This license type can only be used with web applications and not with desktop/console applications. Please upgrade to a higher license to remove this restriction..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LicenseBase_DomainRestricted">
            <summary>
              Looks up a localized string similar to This license type can only be used on "{0}" domain and current host name does not match. Please upgrade to a higher license for being able to use {1} on multiple domains..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LicenseBase_KeyDisabled">
            <summary>
              Looks up a localized string similar to This license key is disabled, please contact GleamTech to obtain a new license key..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LicenseBase_KeyNotValid">
            <summary>
              Looks up a localized string similar to This license key is not valid..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LicenseBase_ProductNotApplicable">
            <summary>
              Looks up a localized string similar to This license key is not applicable to this product ({0})..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LicenseBase_VersionNotApplicableNew">
            <summary>
              Looks up a localized string similar to This license key is not applicable to this product version ({0}), it's for newer versions ({1}+). Please update to a newer version of the product or  acquire an old license key..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LicenseBase_VersionNotApplicableOld">
            <summary>
              Looks up a localized string similar to This license key is not applicable to this product version ({0}), it's for older versions ({1}+). Please go to https://www.gleamtech.com/upgrade and acquire a new license key. You may also be elligible for a free version upgrade..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Location_AlreadyRegistered">
            <summary>
              Looks up a localized string similar to Location type with name "{0}" is already registered..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Location_Invalid">
            <summary>
              Looks up a localized string similar to Invalid location string..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.Location_TypeNotFound">
            <summary>
              Looks up a localized string similar to Location type with name "{0}" is not found..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.LogonCredential_LogonUserFailed">
            <summary>
               Looks up a localized string similar to Logon failed for user "{0}".
            {1}.
             </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.PathSecurity_IllegalCharactersInPath">
            <summary>
              Looks up a localized string similar to Illegal characters in path..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ResourceHandler_ResourceNotFound">
            <summary>
              Looks up a localized string similar to Resource not found..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.ResourceHandler_ResourceOpenError">
            <summary>
              Looks up a localized string similar to Can not open resource..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.StateManager_SessionDisabled">
            <summary>
              Looks up a localized string similar to {0} requires ASP.NET SessionState module to be enabled. Please check &lt;sessionState&gt; element in web.config and EnableSessionState attribute on the page directive..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.StateManager_SessionReadOnly">
            <summary>
              Looks up a localized string similar to {0} requires ASP.NET SessionState module to be in Read-Write mode. Please set EnableSessionState attribute to True instead of ReadOnly on the page directive..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.TimeTrial_CanNotExtend">
            <summary>
              Looks up a localized string similar to Can not extend the trial due to system restrictions. When running on IIS, make sure "Load User Profile" option is set to "true" in "Advanced Settings" of the application pool. This allows your web application to access Windows Registry for storing trial information..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.TimeTrial_KeyNotValid">
            <summary>
              Looks up a localized string similar to This trial extension key is not valid..
            </summary>
        </member>
        <member name="P:GleamTech.Resources.Strings.TimeTrial_ProductNotApplicable">
            <summary>
              Looks up a localized string similar to This trial extension key is not applicable to {0}..
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonProvider.Default">
            <summary>
            Use the standard logon provider for the system.
            The default security provider is negotiate, unless you pass NULL for the domain name and the user name
            is not in UPN format. In this case, the default provider is NTLM.
            NOTE: Windows 2000/NT:   The default security provider is NTLM.
            </summary>
        </member>
        <member name="T:GleamTech.Security.LogonType">
            <summary>
            Specifies the type of login used.
            http://msdn.microsoft.com/en-us/library/windows/desktop/aa378184.aspx
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonType.Interactive">
            <summary>
            This logon type is intended for users who will be interactively using the computer, such as a user being logged
            on by a terminal server, remote shell, or similar process. This logon type has the additional expense of caching
            logon information for disconnected operations; therefore, it is inappropriate for some client/server applications,
            such as a mail server.
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonType.Network">
            <summary>
            This logon type is intended for high performance servers to authenticate plaintext passwords.
            The LogonUser function does not cache credentials for this logon type.
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonType.Batch">
            <summary>
            This logon type is intended for batch servers, where processes may be executing on behalf of a user
            without their direct intervention. This type is also for higher performance servers that process many
            plaintext authentication attempts at a time, such as mail or web servers.
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonType.Service">
            <summary>
            Indicates a service-type logon. The account provided must have the service privilege enabled. 
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonType.Unlock">
            <summary>
            GINAs are no longer supported.
            Windows Server 2003 and Windows XP:  This logon type is for GINA DLLs that log on users who will be
            interactively using the computer. This logon type can generate a unique audit record that shows when
            the workstation was unlocked.
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonType.NetworkClearText">
            <summary>
            This logon type preserves the name and password in the authentication package, which allows the server
            to make connections to other network servers while impersonating the client. A server can accept plaintext
            credentials from a client, call LogonUser, verify that the user can access the system across the network,
            and still communicate with other servers.
            </summary>
        </member>
        <member name="F:GleamTech.Security.LogonType.NewCredentials">
            <summary>
            This logon type allows the caller to clone its current token and specify new credentials for outbound connections.
            The new logon session has the same local identifier but uses different credentials for other network connections.
            This logon type is supported only by the LOGON32_PROVIDER_WINNT50 logon provider.
            </summary>
        </member>
        <member name="T:GleamTech.Util.Enum`1">
            <summary>
            Strongly typed version of Enum with Parsing and performance improvements.
            </summary>
            <typeparam name="T">Type of Enum</typeparam>
        </member>
        <member name="M:GleamTech.Util.ExpressionStringBuilder.ToString(System.Linq.Expressions.Expression,System.Boolean)">
            <summary>
            A nicely formatted ToString of an expression
            </summary>
            <param name="expression">The expression to format</param>
            <param name="trimLongArgumentList">If true will replace large (&gt;3) argument lists with an elipsis</param>
            <returns />
        </member>
        <member name="T:GleamTech.Util.NamedLock">
            <summary>
            Synchronization helper: a static lock collection associated with a key.
            NamedLock manages the lifetime of critical sections that can be accessed by a key (name) throughout the application. 
            It also have some helper methods to allow a maximum wait time (timeout) to aquire the lock and safelly release it.    
            Note: this nuget package contains c# source code and depends on System.Collections.Concurrent introduced in .Net 4.0.
            </summary>
            <example>
            // create a lock for this key
            using (var padlock = new NamedLock (key))
            {
                if (padlock.Enter (TimeSpan.FromMilliseconds (100)))
                {
                    // do something
                }
                else
                {
                    // do some other thing
                }
            }
            </example>
        </member>
        <member name="P:GleamTech.Util.NamedLock.IsLocked">
            <summary>
            Check if a lock was aquired.
            </summary>
        </member>
        <member name="P:GleamTech.Util.NamedLock.Key">
            <summary>
            Gets the lock key name.
            </summary>
        </member>
        <member name="P:GleamTech.Util.NamedLock.Lock">
            <summary>
            Gets the internal lock object.
            </summary>
        </member>
        <member name="M:GleamTech.Util.NamedLock.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.Util.NamedLock" /> class.
            </summary>
            <param name="key">The named lock key.</param>
        </member>
        <member name="M:GleamTech.Util.NamedLock.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing,
            or resetting unmanaged resources.
            Releases aquired lock and related resources.
            </summary>
        </member>
        <member name="M:GleamTech.Util.NamedLock.Enter">
            <summary>
            Tries to aquire a lock.
            </summary>
        </member>
        <member name="M:GleamTech.Util.NamedLock.Enter(System.Int32)">
            <summary>
            Tries to aquire a lock respecting the specified timeout.
            </summary>
            <param name="waitTimeoutMilliseconds">The wait timeout milliseconds.</param>
            <returns>If the lock was aquired in the specified timeout</returns>
        </member>
        <member name="M:GleamTech.Util.NamedLock.Enter(System.TimeSpan)">
            <summary>
            Tries to aquire a lock respecting the specified timeout.
            </summary>
            <param name="waitTimeout">The wait timeout.</param>
            <returns>If the lock was aquired in the specified timeout</returns>
        </member>
        <member name="M:GleamTech.Util.NamedLock.Exit">
            <summary>
            Releases the lock if it was already aquired.
            Called also at "Dispose".
            </summary>
        </member>
        <member name="M:GleamTech.Util.NamedLock.CreateAndEnter(System.String)">
            <summary>
            Creates a new instance and tries to aquire a lock.
            </summary>
            <param name="key">The named lock key.</param>
        </member>
        <member name="M:GleamTech.Util.NamedLock.CreateAndEnter(System.String,System.Int32)">
            <summary>
            Creates a new instance and tries to aquire a lock.
            </summary>
            <param name="key">The named lock key.</param>
            <param name="waitTimeoutMilliseconds">The wait timeout milliseconds.</param>
        </member>
        <member name="M:GleamTech.Util.NamedLock.CreateAndEnter(System.String,System.TimeSpan)">
            <summary>
            Creates a new instance and tries to aquire a lock.
            </summary>
            <param name="key">The name of the lock object.</param>
            <param name="waitTimeout">The wait timeout.</param>
        </member>
    </members>
</doc>