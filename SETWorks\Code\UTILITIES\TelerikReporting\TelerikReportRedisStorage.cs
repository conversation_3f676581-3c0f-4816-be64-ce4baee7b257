using System;
using System.Configuration;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using StackExchange.Redis;
using Telerik.Reporting.Cache;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Custom Redis storage for Telerik Reporting that uses a separate Redis database
    /// This prevents Telerik report cache from overwhelming the main session Redis
    /// </summary>
    public class TelerikReportRedisStorage : IStorage
    {
        private readonly IDatabase _database;
        private readonly ConnectionMultiplexer _redis;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromHours(2);
        private readonly string _keyPrefix = "TelerikReport:";
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(TelerikReportRedisStorage));

        public TelerikReportRedisStorage()
        {
            var connectionString = GetReportRedisConnectionString();
            _redis = ConnectionMultiplexer.Connect(connectionString);
            _database = _redis.GetDatabase();
            Log.Info("TelerikReportRedisStorage initialized");
        }

        public bool Contains(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                return _database.KeyExists(redisKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error checking if key exists: {key}", ex);
                return false;
            }
        }

        public object GetValue(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                var value = _database.StringGet(redisKey);
                
                if (value.HasValue)
                {
                    // Update expiration on access (sliding expiration)
                    _database.KeyExpire(redisKey, _defaultExpiration);
                    return DeserializeObject(value);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error retrieving value for key: {key}", ex);
            }
            return null;
        }

        public void SetValue(string key, object value)
        {
            SetValue(key, value, _defaultExpiration);
        }

        public void SetValue(string key, object value, TimeSpan timeout)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                var serializedValue = SerializeObject(value);
                _database.StringSet(redisKey, serializedValue, timeout);
            }
            catch (Exception ex)
            {
                Log.Error($"Error setting value for key: {key}", ex);
                throw;
            }
        }

        public bool Remove(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                return _database.KeyDelete(redisKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error removing key: {key}", ex);
                return false;
            }
        }

        public void Clear()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints()[0]);
                var keys = server.Keys(pattern: _keyPrefix + "*");
                
                foreach (var key in keys)
                {
                    _database.KeyDelete(key);
                }
                Log.Info("Cleared all Telerik report cache entries");
            }
            catch (Exception ex)
            {
                Log.Error("Error clearing storage", ex);
            }
        }

        private string GetRedisKey(string key)
        {
            return _keyPrefix + key;
        }

        private string GetReportRedisConnectionString()
        {
            // Try to get dedicated report Redis connection string first
            var reportRedisConnection = ConfigurationManager.AppSettings["TelerikReportRedisConnection"];
            if (!string.IsNullOrEmpty(reportRedisConnection))
            {
                Log.Info("Using dedicated Redis instance for Telerik reports");
                return reportRedisConnection;
            }

            // Fall back to main Redis connection but with different database
            var mainRedisConnection = ConfigurationManager.AppSettings["SWRedisConnection"];
            if (!string.IsNullOrEmpty(mainRedisConnection))
            {
                Log.Info("Using main Redis instance with separate database for Telerik reports");
                // Use database 1 for reports (default session uses database 0)
                return mainRedisConnection + ",defaultDatabase=1";
            }

            throw new ConfigurationErrorsException("No Redis connection string found for Telerik report storage");
        }

        private byte[] SerializeObject(object obj)
        {
            if (obj == null) return null;
            
            using (var memoryStream = new MemoryStream())
            {
                var formatter = new BinaryFormatter();
                formatter.Serialize(memoryStream, obj);
                return memoryStream.ToArray();
            }
        }

        private object DeserializeObject(byte[] data)
        {
            if (data == null || data.Length == 0) return null;
            
            using (var memoryStream = new MemoryStream(data))
            {
                var formatter = new BinaryFormatter();
                return formatter.Deserialize(memoryStream);
            }
        }

        public void Dispose()
        {
            _redis?.Dispose();
        }
    }
}
