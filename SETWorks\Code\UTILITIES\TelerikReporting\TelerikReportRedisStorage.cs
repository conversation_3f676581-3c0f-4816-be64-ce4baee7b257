using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using StackExchange.Redis;
using Telerik.Reporting.Cache.Interfaces;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Custom Redis storage for Telerik Reporting that uses a separate Redis database
    /// This prevents Telerik report cache from overwhelming the main session Redis
    /// </summary>
    public class TelerikReportRedisStorage : IStorage
    {
        private IDatabase _database;
        private ConnectionMultiplexer _redis;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromHours(2);
        private readonly string _keyPrefix = "TelerikReport:";
        private readonly string _lockPrefix = "TelerikLock:";
        private readonly string _setPrefix = "TelerikSet:";
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(TelerikReportRedisStorage));

        public TelerikReportRedisStorage()
        {
            try
            {
                Log.Info("TelerikReportRedisStorage constructor called");
                var connectionString = GetReportRedisConnectionString();
                Log.Info($"Attempting to connect to Redis with connection string: {connectionString}");

                var options = ConfigurationOptions.Parse(connectionString);
                options.AbortOnConnectFail = false; // Don't fail immediately if Redis is temporarily unavailable

                _redis = ConnectionMultiplexer.Connect(options);
                _database = _redis.GetDatabase();

                Log.Info("TelerikReportRedisStorage initialized successfully");
            }
            catch (Exception ex)
            {
                Log.Error("Failed to initialize TelerikReportRedisStorage", ex);
                // Don't throw - just log the error and continue
                // This will allow Telerik to instantiate the class even if Redis is unavailable
            }
        }

        // Legacy methods (for backward compatibility)
        public bool Contains(string key)
        {
            return Exists(key);
        }

        public object GetValue(string key)
        {
            var bytes = GetBytes(key);
            return bytes != null ? DeserializeObject(bytes) : null;
        }

        public void SetValue(string key, object value)
        {
            SetValue(key, value, _defaultExpiration);
        }

        public void SetValue(string key, object value, TimeSpan timeout)
        {
            var bytes = SerializeObject(value);
            SetBytes(key, bytes);
        }

        public bool Remove(string key)
        {
            try
            {
                if (_database == null)
                {
                    Log.Warn("Redis database not available, cannot remove key: " + key);
                    return false;
                }
                var redisKey = GetRedisKey(key);
                return _database.KeyDelete(redisKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error removing key: {key}", ex);
                return false;
            }
        }

        public void Clear()
        {
            try
            {
                var database = GetDatabase();
                var server = _redis.GetServer(_redis.GetEndPoints()[0]);
                var keys = server.Keys(pattern: _keyPrefix + "*");

                foreach (var key in keys)
                {
                    database.KeyDelete(key);
                }
                Log.Info("Cleared all Telerik report cache entries");
            }
            catch (Exception ex)
            {
                Log.Error("Error clearing storage", ex);
            }
        }

        // Required IStorage interface methods for version 12.1.18.620
        public IDisposable AcquireLock(string key)
        {
            try
            {
                var database = GetDatabase();
                var lockKey = _lockPrefix + key;
                var lockValue = Environment.MachineName + "_" + DateTime.UtcNow.Ticks;
                var acquired = database.StringSet(lockKey, lockValue, TimeSpan.FromMinutes(5), When.NotExists);
                if (acquired)
                {
                    Log.Debug($"Lock acquired for key: {key}");
                    return new RedisLock(database, lockKey);
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.Error($"Error acquiring lock for key: {key}", ex);
                return null;
            }
        }

        public void SetString(string key, string value)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                GetDatabase().StringSet(redisKey, value, _defaultExpiration);
            }
            catch (Exception ex)
            {
                Log.Error($"Error setting string value for key: {key}", ex);
                throw;
            }
        }

        public void SetBytes(string key, byte[] value)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                GetDatabase().StringSet(redisKey, value, _defaultExpiration);
            }
            catch (Exception ex)
            {
                Log.Error($"Error setting byte array for key: {key}", ex);
                throw;
            }
        }

        public string GetString(string key)
        {
            try
            {
                var database = GetDatabase();
                var redisKey = GetRedisKey(key);
                var value = database.StringGet(redisKey);

                if (value.HasValue)
                {
                    // Update expiration on access (sliding expiration)
                    database.KeyExpire(redisKey, _defaultExpiration);
                    return value;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error retrieving string value for key: {key}", ex);
            }
            return null;
        }

        public byte[] GetBytes(string key)
        {
            try
            {
                var database = GetDatabase();
                var redisKey = GetRedisKey(key);
                var value = database.StringGet(redisKey);

                if (value.HasValue)
                {
                    // Update expiration on access (sliding expiration)
                    database.KeyExpire(redisKey, _defaultExpiration);
                    return value;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error retrieving byte array for key: {key}", ex);
            }
            return null;
        }

        public void Delete(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                GetDatabase().KeyDelete(redisKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error deleting key: {key}", ex);
            }
        }

        public bool Exists(string key)
        {
            try
            {
                var redisKey = GetRedisKey(key);
                return GetDatabase().KeyExists(redisKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error checking if key exists: {key}", ex);
                return false;
            }
        }

        // Set-related methods for Redis sets
        public bool ExistsInSet(string setKey, string value)
        {
            try
            {
                var redisSetKey = GetSetKey(setKey);
                return GetDatabase().SetContains(redisSetKey, value);
            }
            catch (Exception ex)
            {
                Log.Error($"Error checking if value exists in set {setKey}: {value}", ex);
                return false;
            }
        }

        public long GetCountInSet(string setKey)
        {
            try
            {
                var redisSetKey = GetSetKey(setKey);
                return GetDatabase().SetLength(redisSetKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting count in set: {setKey}", ex);
                return 0;
            }
        }

        public IEnumerable<string> GetAllMembersInSet(string setKey)
        {
            try
            {
                var redisSetKey = GetSetKey(setKey);
                var members = GetDatabase().SetMembers(redisSetKey);
                return members.Select(m => m.ToString());
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting all members in set: {setKey}", ex);
                return new string[0];
            }
        }

        public void AddInSet(string setKey, string value)
        {
            try
            {
                var database = GetDatabase();
                var redisSetKey = GetSetKey(setKey);
                var added = database.SetAdd(redisSetKey, value);
                if (added)
                {
                    database.KeyExpire(redisSetKey, _defaultExpiration);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error adding value to set {setKey}: {value}", ex);
            }
        }

        public bool DeleteInSet(string setKey, string value)
        {
            try
            {
                var redisSetKey = GetSetKey(setKey);
                return GetDatabase().SetRemove(redisSetKey, value);
            }
            catch (Exception ex)
            {
                Log.Error($"Error deleting value from set {setKey}: {value}", ex);
                return false;
            }
        }

        public void DeleteSet(string setKey)
        {
            try
            {
                var redisSetKey = GetSetKey(setKey);
                GetDatabase().KeyDelete(redisSetKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error deleting set: {setKey}", ex);
            }
        }

        private string GetRedisKey(string key)
        {
            return _keyPrefix + key;
        }

        private string GetSetKey(string key)
        {
            return _setPrefix + key;
        }

        private string GetReportRedisConnectionString()
        {
            // Try to get dedicated report Redis connection string first
            var reportRedisConnection = ConfigurationManager.AppSettings["TelerikReportRedisConnection"];
            if (!string.IsNullOrEmpty(reportRedisConnection))
            {
                Log.Info("Using dedicated Redis instance for Telerik reports");
                return reportRedisConnection;
            }

            // Fall back to main Redis connection but with different database
            var mainRedisConnection = ConfigurationManager.AppSettings["SWRedisConnection"];
            if (!string.IsNullOrEmpty(mainRedisConnection))
            {
                Log.Info("Using main Redis instance with separate database for Telerik reports");
                // Use database 1 for reports (default session uses database 0)
                return mainRedisConnection + ",defaultDatabase=1";
            }

            throw new ConfigurationErrorsException("No Redis connection string found for Telerik report storage");
        }

        private byte[] SerializeObject(object obj)
        {
            if (obj == null) return null;
            
            using (var memoryStream = new MemoryStream())
            {
                var formatter = new BinaryFormatter();
                formatter.Serialize(memoryStream, obj);
                return memoryStream.ToArray();
            }
        }

        private object DeserializeObject(byte[] data)
        {
            if (data == null || data.Length == 0) return null;
            
            using (var memoryStream = new MemoryStream(data))
            {
                var formatter = new BinaryFormatter();
                return formatter.Deserialize(memoryStream);
            }
        }

        public void Dispose()
        {
            _redis?.Dispose();
        }
    }

    /// <summary>
    /// Disposable lock implementation for Redis
    /// </summary>
    internal class RedisLock : IDisposable
    {
        private readonly IDatabase _database;
        private readonly string _lockKey;
        private bool _disposed = false;

        public RedisLock(IDatabase database, string lockKey)
        {
            _database = database;
            _lockKey = lockKey;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    _database.KeyDelete(_lockKey);
                }
                catch
                {
                    // Ignore errors during disposal
                }
                _disposed = true;
            }
        }
    }
}
