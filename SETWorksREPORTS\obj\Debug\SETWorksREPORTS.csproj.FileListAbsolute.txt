C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\SETWorksREPORTS.dll.config
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\SETWorksREPORTS.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\SETWorksREPORTS.pdb
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\log4net.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\SETWorksDAO.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Barcode.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.DataExport.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.DataExport.ResourceMgr.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Doc.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.DocViewer.Forms.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Email.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.License.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.OfficeViewer.Forms.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Pdf.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.PdfViewer.Asp.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.PdfViewer.Forms.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Presentation.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Spreadsheet.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.XLS.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Reporting.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Reporting.OpenXmlRendering.2.7.2.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Reporting.XpsRendering.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.ReportViewer.WebForms.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Web.UI.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Web.UI.Skins.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.S3.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.Core.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.SimpleEmail.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\LazyCache.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Windows.Zip.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\RestSharp.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.mshtml.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\netstandard.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Memory.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\SETWorksDAO.pdb
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\SETWorksDAO.dll.config
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\log4net.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Barcode.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.DataExport.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Doc.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.DocViewer.Forms.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Email.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.License.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.OfficeViewer.Forms.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Pdf.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.PdfViewer.Asp.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.PdfViewer.Forms.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Presentation.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.Spreadsheet.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Spire.XLS.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Web.UI.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.ReportViewer.WebForms.pdb
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.ReportViewer.WebForms.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.S3.pdb
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.S3.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.Core.pdb
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.Core.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.SimpleEmail.pdb
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\AWSSDK.SimpleEmail.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Telerik.Windows.Zip.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\RestSharp.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Caching.Abstractions.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Primitives.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Caching.Memory.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.Options.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Memory.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Numerics.Vectors.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\bin\Debug\System.Buffers.xml
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AccidentReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordAppointmentConversionReportStaff.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordAppointmentConversionReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordAuditReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordNonBillableWithConsumerReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsNonBillableWithConsumerReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingReportDayRateExceptionStatus.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingReportDayRateNoMaxExtraColumnsUnrounded.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingReportDayRateExtraColumnsUnrounded.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingMonthlyInvoiceReportWithFundingSourceContact.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingInvoiceReportWithFundingSourceContact.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingReportExtendedExcel.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingReportExtendedExcelNEBAFIXED.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsGroupMultiMonthReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsWithPicturesStandAlone.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAssessmentKeyDateReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationOverviewSimpleReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\BenefitPlanningBillingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\BrokerageServicesReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CAGroupInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CAAdultInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgencyProductionReportGroupedByTaskWithRules.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.ConsumerProductionReport.ConsumerProductionEarningsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.ConsumerAgencyProductionReport.ConsumerAgencyProductionEarningsReportGroupedByConsumer.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.ConsumerProductionReport.ConsumerProductionPayStubsReportWithRounding.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.ConsumerProductionReport.ConsumerProductionPayStubsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgencyProductionReportGroupedByConsumerWithRules.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.ConsumerAgencyProductionReport.ConsumerAgencyProductionReportGroupedByDepartment.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgencyProductionReportWithRules.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgencyProductionReportGroupedByConsumer.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetBillableWithConsumerServiceExcelUngroupedReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMedicaidWaiverSummaryReportWithGoalsAndCommunityIntegrationLocations.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMedicaidWaiverSummaryReportWithGoals.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageReportExcel3.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsReport_V4.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsReport_V5.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsSummaryReport_V4.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsSummaryReport_V5.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NetworkContactAveryMailingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomesBasedPaymentsReportNDI.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PointInTimeRAReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\RequestDetailsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DistAndDemoEquipmentReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerCertificationsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerFinanceReportWithReceiptImages.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DepartmentFinanceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetBillableWithConsumerServiceExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NDIContactExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NDIDemographicsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EVVReconciliationReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsGenericMedicaidWaiverSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsRequiredReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithRulesNoStaffCIMOR.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AppointmentDepartmentCalendarReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationOverviewWithDatesAndFrequenciesAndUnitsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DDaPFile.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DDSEmergencyFactSheetReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ServiceAlternativesMonthlyProgressReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMedicaidWaiverSummaryReportWithSignature.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMedicaidWaiverSummaryReportBlankSignature.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SageIntacctAccRecInvoicesLimited.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\HealthCarePaymentKeystoneMSDynamicsInterfaceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MonthlyActivityRecordBillingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsReport_V2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\GenericStringReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CAIPInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsReport_V3.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsSummaryReport_V3.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OregonEXPRSEVV.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ProductionReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\QuickBooksAccountingInvoiceExportIIFReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\QuickBooksTimesheetNonbillableAndCostCenterReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NDIOutcomeBasedReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\TaskStaffPerformanceWithTaskListReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\Tier3ScreeningReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\Tier3ServiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\UserExpenseReportSubtotals.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\WarrantyReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetAllRecordsWithoutOverlapWithSumsWithPayrollCode.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\GompersQuarterlyReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportUnitSummaryWithClock.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsBillingWithGoalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsHistoryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTracking.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAndAssessmentMonthly.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAndAssessmentMonthlyAddComments.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAssessmentKeyPercentageReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.ActivityRecordsInterventionAssessmentKeyPercentageReport.ActivityRecordsInterventionAssessmentKeyPercentageReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAssessmentKeyPercentageReportHeaderRepeat.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAssessmentKeyReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsMultiGoalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsMultiGoalWithoutStrategiesReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsNonBillableReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsNonBillableReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportTelerik2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportReconciliationTelerikExcel.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportReconciliationTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportSpreadsheetWithCAPStaffAndService.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportUnitSummary.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportWithoutHoursAndMRVRTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsSumsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMStaffHoursReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReport2WithStaffAndService.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReport2WithStaffFocusAndService.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitContractReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithAuthAndPlaceOfService.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithAuthAndEmployerCHS.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithAuthCustomIDAndTotals.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithAuth.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithPlacement.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithRulesHours.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithRules.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithRulesAndInAndOutTimesGroupedByConsumer.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithRulesAndInAndOutTimes.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithRulesNoStaff.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithRulesNoStaffHours.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AnnualReviewDueReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AppointmentAuthAndConsumerWorkCalendarReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AppointmentCalendarReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AppointmentConsumerCalendarReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AppointmentScheduleReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AttendanceFTEReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationAggregateReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationExceededReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationGroupOverviewReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.AuthorizationLimitHoursUsedReport.AuthorizationLimitHoursUsedReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationLimitHoursUsedReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.AuthorizationLimitReport.AuthorizationLimitReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationLimitReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationLimitExtendedReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationLimitReport3Extended.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationLimitReport3.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationOverviewReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationOverviewWithDatesAndFrequenciesReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationPercentageLeftReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationReportTelerikBillable.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationReportTelerikBillable2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationReportTelerikGoal.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.AuthorizationReportGoals.CareerDiscoveryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\AuthorizationReportTemporaryTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.AuthorizationRequestSummaryReport.AuthorizationRequestSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CAPBiMonthlyTimesheetReport3.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CAPBiMonthlyTimesheetReport4.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CareerDiscoveryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConditionsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ContributionsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PreferencesReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SupportNeedsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CaseNoteAuditReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CaseNoteCoverageReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CaseNoteReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\CEOEmployeeIDReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ClientStatusReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ClinicalEventsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgencyProductionAggregateReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgencyProductionReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgencyProductionReportGroupedByTask.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAgeReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAtWorkCalendarReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAveryMailingReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerContactExtendedReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerContactExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerContactReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerDetailedContactReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerDisabilityReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerEquipmentOrderExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerEquipmentOrderStatisticsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerEarningsSpreadsheetReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerExpirationFieldReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerNetworkContactHistoryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerNetworkContactReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerOutcomeGoalStrategyExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerOutcomeGoalStrategyReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerOwnGuardianReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerPrimaryAndSecondaryContactReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerScaleAssessmentReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerScaleAssessmentWithGraphReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerServiceTotalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerServiceWithNonbillableTotalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerServiceWithOutcomesTotalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsStandAlone.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerPlacementBenefitReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTransportationServiceCountReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerMedsAdministrationCoverageAndTrainingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsGroupReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsGroupOverallAndConsumerCommentReportStandAlone.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsGroupDetailsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithAuthAndPhases.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerOutcomeGoalStrategyComprehensiveReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithConsumerTimeInStaffAndService.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsUnitReportWithAuthAndEmployer.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerAggregateDataReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerEquipmentOrderReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMBillingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMBillingReportByConsumer.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMBillingReportByConsumerAndDepartment.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMBillingReportExtendedExcel.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMBillingSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMBillingSummaryReportAndDepartment.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ActivityRecordsTCMRawDataReportExcel.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerDepartmentFinanceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerWeightAnalysisReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DailyJobSupportsTimeLogReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetAllRecordsWithoutOverlapNoDepartmentsWithSums.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\HealthCarePaymentAgeingTotalsOnly.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SageIntacctAccRecInvoices.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\HealthCarePaymentAgeingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobEarningsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageReportExcelByStaff.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.LogInternshipDevelopmentSupports.LogInternshipDevelopmentSupports.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NWCGreatPlainsInterface.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OregonEXPRS.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeBillingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\QuickBooksPayrollExportIIFReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffServiceWithNonBillableTotalFunderGroupingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\TaskListReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMonthlySummaryReportUpdated.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormSAServiceDeliveryPlanReportPage2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.FunderIndividualPlanBudgetComparisonReport.FunderIndividualPlanBudgetComparisonReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.FunderIndividualPlanBudgetReport.FunderIndividualPlanBudgetReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\HealthCarePaymentBatchExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.MarylandJobDevLog.MarylandJobDevLog.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageReportTelerikGroupedByCostCenter.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.NJ_DVRS_Activities.NJ_DVRS_Activities.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffEquipmentOrderExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsADVPQuarterlyReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PMLogsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffMonthlyHoursProductionWithoutTargetsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffMonthlyHoursProductionReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffMonthlyHoursSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.TaskStaffPerformanceReport.TaskStaffPerformanceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\UserExpenseReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\UserExpenseUngroupedReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\VAOutcomesAndActivities.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\VAPartVPlanOfSupports.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\VAPersonCenteredReview.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\HealthCarePaymentBatchReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DoctorOrderReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetAllRecordsWithoutOverlapWithSums.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\TCFSS_PayrollReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ConsumerNetworkCommunicationReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\TCFSS_AccountingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMedicaidWaiverSummaryReportOld.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormSAServiceDeliveryPlanReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\GenericReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MedicationReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormCHSROIReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffPrimaryContactWeightReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffBirthdayReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetBillableWithConsumerServiceWGroupReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PlacementProgramCountReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\TCMEmployeeTimesheetBillableWithConsumerServiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffServiceWithCodeGroupedTotalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffDobHireTenureReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageCompanyCarReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageMonthlyReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsSemiAnnualAssessmentReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobAnalysisReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NotificationReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeMeasurementDrilldownReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffExpirationFieldReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\GoalCompletionReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeMeasurementReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DemographicsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DistributionListReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EarlyClosingReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyReportAllergy.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyReportContacts.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyReportDoctor.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyReportHospital.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyReportMedication.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyReportSeizure.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyFactSheetBackside.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmergencyFactSheetReportReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetBillableReportTelerik2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetBillableWithConsumerServiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployeeTimesheetReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerAveryMailingReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerCreatedReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerDetailedContactReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerEmailAndPhoneReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerExportReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerJobAnalysisReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerJobContactSourceReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerNotesReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerWorkplaceRelationshipHistoryReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MostErrorsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ExpirationFieldReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\IWPQuestionnaireResultTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMedicaidWaiverSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMonthlyProgressReview.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsMonthlySummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormsRCDDSMonthlyProviderSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DynamicEmployerFormsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\DynamicFormsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FormStatusReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FundingSourceContactConsumerReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\FundingSourceContactHistoryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\InactiveConsumersReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\IncidentReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\InterventionReportWithOffSiteTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\InterventionReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\InterventionReportTelerik2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobContactsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobContactsReportTelerikExcel.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobDevelopmentContacts.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobDevProgressReportSQLDirect.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\EmployerCaseNotes.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobDevDesign.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobDevelopmentProgressReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobDevelopmentProgressReportSimple.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobDevTest.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\JobPlacementsReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ManagementAggregateReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ManagementDepartmentProductivityReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ManagementReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MedicaidReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MedicalRosterReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageReportSummarizedClockNumberTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageReportSummarizedTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageReportExcel2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MileageReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\MOSEMonthlyJobSupportsSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NCSEJobDevelopmentMilestonePaymentRequestForm.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NCWAJobDevelopmentMilestonePaymentRequestForm.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NonAspirinReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\NoResultsTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeBasedBillingReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeBasedBillingSubReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeBasedExportReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeBasedReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeBasedServiceReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeBasedTotalsExportReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\OutcomeMeasurementReportLOQWTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PayrollReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PhotoConsentReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.REPORTS.PhotoConsentReport.PhotoConsentTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PlacementFollowUpFormReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PlacementFollowUpFullFormReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\PlacementWagePartTimeReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\QuickBooksInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\QuickBooksInvoiceReport2.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\QuickBooksTimesheetOvertimeReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\QuickBooksTimesheetReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ResidentialRosterReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\RestrictionReportDiabetic.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\RestrictionReportDietaryNeeds.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\RestrictionReportEatingOversight.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\RestrictionReportNonAspirin.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\RestrictionReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\RestrictionReportTelerikRestrictionOnly.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SeizureReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\ServiceCountReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffAuditReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffAveryMailingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffCoverageReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffEducationReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffExpirationReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffHoursOverviewReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffNoteReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffPaymentReportCustomServiceRate.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffProductionReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffRoleDepartmentReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffServiceTotalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffServiceTotalWithOutcomesReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffServiceWithNonBillableTotalReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\StaffTimesheetExportReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\TextFieldReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\VisualVaultPreBillingReport.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\VRCBAInterventionTimesheet.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\VRSEInterventionTimesheet.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\VRWAInterventionTimesheet.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\WorkHistoryReportTelerik.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\UCP_Abila_BillingInterface.resources
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.csproj.GenerateResource.cache
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.dll.licenses
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.csproj.CopyComplete
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.dll
C:\Users\<USER>\Documents\SW\SETWorksREPORTS\obj\Debug\SETWorksREPORTS.pdb
