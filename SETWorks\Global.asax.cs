using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;
using System.Web.Mvc;
using System.Web.Http;
using SETWorks;
using SetWorks;
using System.Web.Optimization;
using SETWorks.Areas.MVC;
using SetWorks.Services;
using Datadog.Trace;
using GleamTech;
using System.Web.Routing;
using ComponentSpace.SAML2;
using ComponentSpace.SAML2.Data;
using SETWorks.Areas.SSO;
using SETWorks.Code.UTILITIES.TelerikReporting;
using Telerik.Reporting.Services;

public partial class Global : System.Web.HttpApplication
{
    private const string MainConnectionStringKey = "DSWPROD";
    
    private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(nameof(Global));

    protected void Application_Start()
    {
        InitializeLog4Net();

        LogSafeInfo("SETWorks Global Application_Start Begin");

        // Register MVC Areas
        RegisterArea(new MVCAreaRegistration(), null);
        RegisterArea(new SSOAreaRegistration(), null);

        ControllerBuilder.Current.SetControllerFactory(new CatchallControllerFactory());

        GlobalConfiguration.Configure(WebApiConfig.Register);

        BundleConfig.RegisterBundles(BundleTable.Bundles);

        ObserverNotifierConfig.RegisterObservers();

        InitializeGleamTech();

        InitializeSpirePdf();

        InitializeSpireXls();

        InitializeSpireDoc();

        InitializeSaml();

        InitializeTelerikReporting();

        LogSafeInfo("SETWorks Global Application_Start End");
    }

    protected void Application_Error(object sender, EventArgs e)
    {
        Exception ex = Server.GetLastError().GetBaseException();
        Application[SessionDefinitions.ERROR] = ex;
        var message = "MESSAGE: " + ex.Message + "\nSOURCE: " + ex.Source + "\nFORM: " + Request.Form.ToString() + "\nQUERYSTRING: " + Request.QueryString.ToString() + "\nTARGETSITE: " + ex.TargetSite + "\nSTACKTRACE: " + ex.StackTrace;

        LogSafeError("SetWorks Application_Error occurred:" + message);

        WriteSafeEventError(message);
    }

    protected void Application_PreRequestHandlerExecute(object sender, EventArgs e)
    {
        InjectDatadogTraceAndViewId();

        if (Request.Url.AbsolutePath.ToLowerInvariant().Contains("login") && !Request.Url.AbsolutePath.ToLowerInvariant().Contains("loginmessage"))
        {
            return;
        }
        
        //Only access session state if it is available
        if (Context.Handler is IRequiresSessionState || Context.Handler is IReadOnlySessionState)
        {
            var requestUrlLowered = Request.Url.AbsolutePath.ToLowerInvariant();
            
            //If we are authenticated AND we dont have a session here.. redirect to login page.
            HttpCookie authenticationCookie = Request.Cookies[FormsAuthentication.FormsCookieName];
            if (authenticationCookie != null)
            {
                FormsAuthenticationTicket authenticationTicket = FormsAuthentication.Decrypt(authenticationCookie.Value);
                if (!authenticationTicket.Expired)
                {
                    //check here to see if we have a valid session.
                    if (Session[SessionDefinitions.LOGGED_IN_USER_NAME] == null)
                    {
                        //This means for some reason the session expired before the authentication ticket. Force a login.
                        FormsAuthentication.SignOut();
                        
                        // If SSO SAML InitiateSingleSignOn, return and let the SSO Controller handle this
                        if (Request.HttpMethod.Equals("GET") && requestUrlLowered.Contains("sso/saml/initiatesinglesignon"))
                        {
                            return;
                        }

                        // Handle special case for FileUpload feature.
                        if (Request.HttpMethod.Equals("GET") && 
                            (requestUrlLowered.Contains("home/benefitplanning/fileuploaderror.aspx") ||
                             requestUrlLowered.Contains("home/benefitplanning/fileupload.aspx") ||
                             requestUrlLowered.Contains("home/benefitplanning/fileuploadthanks.aspx")||
                             requestUrlLowered.Contains("telerik.web.ui.webresource.axd")))
                        {
                            return;
                        }
                        
                        Response.Redirect(FormsAuthentication.LoginUrl, true);
                        return;
                    }
                }
            }
            /* Check if user's password has expired, at this point they shouldn't be in the login realm,
		  so we should avoid an infinite redirect loop */
            if (Request.HttpMethod.Equals("GET") && 
                !requestUrlLowered.Contains("api") &&
                !requestUrlLowered.Contains("sso/saml") &&
                !requestUrlLowered.Contains("home/benefitplanning/fileuploaderror.aspx")&&
                !requestUrlLowered.Contains("home/benefitplanning/fileupload.aspx") &&
                !requestUrlLowered.Contains("home/benefitplanning/fileuploadthanks.aspx")&&
                !requestUrlLowered.Contains("telerik.web.ui.webresource.axd")
                )
            {
                // TODO: Incorporate w/ Ninject instead of Factory
                IRedirectionService rs = new RedirectionServiceFactory().Create();
                Tuple<bool, Uri> forceNavigation = rs.CheckForceNavigation();
                if (forceNavigation.Item1)
                    Response.Redirect(forceNavigation.Item2.ToString(), true);
            }
        }
    }

    private static void RegisterArea(AreaRegistration registration, object state)
    {
        // Taken from: http://stackoverflow.com/questions/2496956/providing-or-filtering-assemblies-when-registering-areas-for-an-asp-net-mvc-2-0
        var context = new AreaRegistrationContext(registration.AreaName, RouteTable.Routes, state);
        var ns = registration.GetType().Namespace;
        if (ns != null)
            context.Namespaces.Add(string.Format("{0}.*", ns));
        registration.RegisterArea(context);
    }

    protected void Application_PostAuthorizeRequest()
    {
        if (IsTfaWebApiRequest())
        {
            HttpContext.Current.SetSessionStateBehavior(SessionStateBehavior.Required);
        }
    }

    private bool IsTfaWebApiRequest()
    {
        try
        {
            // ReSharper disable once PossibleNullReferenceException
            return HttpContext.Current.Request.AppRelativeCurrentExecutionFilePath.StartsWith("~/api/tfa");
        }
        catch (Exception ex)
        {
            LogSafeError("SETWorks Global IsTfaWebApiRequest() error occurred.", ex);
            return false;
        }
    }

    private void InjectDatadogTraceAndViewId()
    {
        try
        {
            if (Tracer.Instance.ActiveScope != null && Tracer.Instance.ActiveScope.Span != null)
            {
                // Trace ID is available, inject it as well.
                log4net.LogicalThreadContext.Properties["dd-trace-id"] = Tracer.Instance.ActiveScope.Span.TraceId;
            }
            else
            {
                // Trace ID not available, wipe any previous value.
                log4net.LogicalThreadContext.Properties["dd-trace-id"] = "-";
            }
        }
        catch (Exception ex)
        {
            LogSafeError("SETWorks Global InjectDatadogTraceAndViewId() error occurred.", ex);
        }
    }

    private void InitializeLog4Net()
    {
        try
        {
            log4net.Config.XmlConfigurator.Configure();
        }
        catch (Exception ex)
        {
            var errorMessage = "InitializeLog4Net error occurred:\r\n" + ex.ToString();
            WriteSafeEventError(errorMessage);
        }
    }

    private void InitializeGleamTech()
    {
        try
        {
            GleamTechConfiguration.EnsureAssemblies();
        }
        catch (Exception ex)
        {
            var errorMessage = "InitializeGleamTech error occurred:\r\n" + ex.ToString();
            WriteSafeEventError(errorMessage);
            LogSafeError(errorMessage);
        }
    }

    private void InitializeSpirePdf()
    {
        try
        {
            Spire.License.LicenseProvider.SetLicenseFileName("Spire.PDF.license.elic.xml");
            Spire.License.LicenseProvider.LoadLicense();
        }
        catch (Exception ex)
        {
            LogSafeError("Failed to initialize Spire.PDF. " + ex.Message);
        }
    }

    private void InitializeSpireXls()
    {
        try
        {
            Spire.License.LicenseProvider.SetLicenseFileName("Spire.XLS.license.elic.xml");
            Spire.License.LicenseProvider.LoadLicense();
        }
        catch (Exception ex)
        {
            LogSafeError("Failed to initialize Spire.XLS. " + ex.Message);
        }
    }
    
    private void InitializeSpireDoc()
    {
        try
        {
            Spire.License.LicenseProvider.SetLicenseFileName("Spire.DOC.license.elic.xml");
            Spire.License.LicenseProvider.LoadLicense();
        }
        catch (Exception ex)
        {
            LogSafeError("Failed to initialize Spire.DOC. " + ex.Message);
        }
    }

    private void InitializeSaml()
    {
        try
        {
            SAMLController.Initialize();

            // Setup SAML SSO Session and ID Cache (for WebFarm setup)
            SAMLController.SSOSessionStore = new DatabaseSSOSessionStore(MainConnectionStringKey);
            SAMLController.IDCache = new  DatabaseIDCache(MainConnectionStringKey);
        }
        catch (Exception ex)
        {
            LogSafeError("Failed to initialize ComponentSpace.SAMLController. " + ex.Message);
        }
    }

    private void InitializeTelerikReporting()
    {
        try
        {
            // Initialize custom Redis storage for Telerik Reports to prevent session overload
            var reportStorage = new SETWorks.Code.UTILITIES.TelerikReporting.TelerikReportRedisStorage();
            Telerik.Reporting.Services.ServiceContainer.ResolveStorage = () => reportStorage;

            LogSafeInfo("Telerik Reporting Redis storage initialized successfully");
        }
        catch (Exception ex)
        {
            LogSafeError("Failed to initialize Telerik Reporting Redis storage. " + ex.Message);
        }
    }
    
    private void LogSafeInfo(string message)
    {
        try
        {
            Log.Info(message);
        }
        catch (Exception ex)
        {
            var errorMessage = "LogSafeInfo (Log4Net) error occurred:\r\n" + ex.ToString();
            WriteSafeEventError(errorMessage);
        }
    }

    private void LogSafeError(string message)
    {
        try
        {
            Log.Error(message);
        }
        catch (Exception ex)
        {
            var errorMessage = "LogSafeError (Log4Net) error occurred:\r\n" + ex.ToString();
            WriteSafeEventError(errorMessage);
        }
    }

    private void LogSafeError(string message, Exception ex)
    {
        try
        {
            Log.Error(message, ex);
        }
        catch (Exception subEx)
        {
            var errorMessage = "LogSafeError (Log4Net) error occurred:\r\n" + subEx.ToString();
            WriteSafeEventError(errorMessage);
        }
    }

    private void WriteSafeEventError(string message)
    {
        try
        {
            if (message.Length > 1000)
            {
                message = message.Substring(0, 1000);
            }
            EventLog.WriteEntry("SETWORKS", message, EventLogEntryType.Error);
        }
        catch
        {
            // Developer: Catch the exception and disregard.
            // This is the lowest level exception, do not propagate it.
            // We have had multiple Application run time issues (multiple times) when Log4Net, DataDog, Ninject cause errors during Application_Start.
        }
    }
}
