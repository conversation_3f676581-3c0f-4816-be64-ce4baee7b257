<?xml version="1.0" encoding="utf-8" ?>
<appSettings>
  <add key="Telerik.Web.UI.DialogParametersEncryptionKey" value="2C23234234D4566DC4AD6A-63D6-4db9-A3FB-3adfasd6846D" />
  <add key="EDIFTPDestinationFolder" value="Inbound" />
  <add key="EDIFTPDestinationFolderOnRegenerate" value="InRegenerate" />
  <add key="AWSAccessKey" value="********************" />
  <add key="AWSSecretKey" value="XPc7gT/9yeUYBHEgl9QVkoXadDTLLby0qNsZJzqU" />
  <add key="AWSRegion" value="us-west-2" />
  <!-- Smarty streets configuration-->
  <add key="ApiUrl" value="https://api.smartystreets.com/street-address/" />
  <add key="AUTH-ID" value="HMc<PERSON><EMAIL>" />
  <add key="AUTH-TOKEN" value="2294284699280794777" />
  <add key="FreskDeskSharedSecret" value="1f15975dc012a9bcdee9cfd959ab7056" />
  <add key="ValidationSettings:UnobtrusiveValidationMode" value="None" />
  <add key="Stackify.ApiKey" value="5Md3Ou9Wj7Jw5Nk4Ty5Un3Yz2Oj7Xm2Nj4Zi5Uv" />
  <add key="Stackify.AppName" value="SW" />
  <add key="Stackify.Environment" value="SW-Andrew" />
  <add key="Telerik.Skin" value="Default" />
  <add key="Telerik.ScriptManager.TelerikCdn" value="Enabled" />
  <add key="Telerik.StyleSheetManager.TelerikCdn" value="Enabled" />
  <add key="Telerik.Web.UI.StyleSheetFolders" value="~/CSS;~/Content/AjaxControlToolkit/Styles;~/Scripts/AjaxControlToolkit/Bundle;~/" />
  <add key="vs:EnableBrowserLink" value="False" />
  <add key="Telerik.Web.UI.Button.RenderMode" value="lightweight" />
  <add key="IronPdf.LicenseKey" value="IRONPDF-1844234EB8-178311-D40AEE-6FEA631D1D-1935EC3D-UEx0D383033EE157D8-AGENCY.LICENSE.5.PROJECTS.SUPPORTED.UNTIL.16.OCT.2019"/>
  <add key="Sisense.InternalAdmin.Username" value="<EMAIL>" />
  <add key="Sisense.InternalAdmin.Password" value="hPTQzj2TFqbcydDTmSk2EKX6ebWb2w6AStYCbvjqPB84hxfE4stTWqWZZ8QCjG5wdm6myc&amp;&amp;&amp;" />
  <add key="Sisense.JWT.Secret" value="3a1ce9cfd058e1d0bb7c62dda59e2705559b58a0e91efafe6d3083454f9bb662" />
  <add key="log4net.Internal.Debug" value="true"/>
  
  <add key="PrivilegeCachingValueInMinutes" value="1" />
  <add key="GeneralCachingValueInMinutes" value="1" />
  <add key="SWContextCachingValueInMinutes" value="1" />
  <add key="GroupRecordCachingInSeconds" value="60" />
  <add key="BatchCountValue" value="50" />
  <add key="GeneralCachingValueInSeconds" value="1" />
  
  <add key="SmartyStreetsApiUrl" value="https://api.smartystreets.com/street-address/"/>
  <add key="SmartyStreetsAuthId" value="<EMAIL>" />
  <add key="SmartyStreetsAuthToken" value="2294284699280794777" />
  <!--Twilio Config-->
  <add key="TwilioAccountID" value="**********************************"/>
  <add key="TwilioAuthToken" value="ac4ba79611757f6a04480843458bfd7c" />
  <add key="TwilioBaseUrl" value="https://e6909b469ab5.ngrok.io"/>
  <add key="TwilioRoute" value="/SW/api/integrations/twilio"/>
  <!-- SSO Config -->
  <add key="SSODomainsAllowed" value="localhost,local.set-works.com" />
  <!-- HelpDesk Portal -->
  <add key="SupportPortalType" value="ZohoDesk" />
  <!-- SW FTP Config -->
  <add key="SFTPHost" value="************" />
  <add key="SFTPUserName" value="DSWAdmin" />
  <add key="SFTPPassword" value="nLjEHCaGrIYDy4SKzCs323423sdf2ipncvknkjsdfajwqeipfj" />
  <add key="SFTPPort" value="2121" />
</appSettings>