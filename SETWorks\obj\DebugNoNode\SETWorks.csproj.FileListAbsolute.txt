C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ActivityRecordsReportSpreadsheet.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ActivityRecordsReportSpreadsheetGrouped.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ActivityRecordsADPPayrollExport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ActivityRecordsADPPayrollExport2.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ActivityRecordsInterventionAssessmentKeySummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ActivityRecordsReportsPaylocityReconciliation.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\CaseManagementDuplicateExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\CaseManagementReportKS.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\CaseManagementStatisticsReportKS.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\CaseManagementReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\CaseManagementStatisticsReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\DynamicFormsReportTelerik2.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\AddendumReport.AddendumReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ConsumerIncidentReport.ConsumerIncidentMOReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ConsumerIncidentReport.ConsumerIncidentReport_GAAMHA.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ConsumersWithExpiredGoalsReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ConsumerWageHistoryReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.InterventionPlanAndServiceLogReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.DVRS_SE_TLJC_Intervention_Plan_And_Service_LogStandAlone_ARC.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.DVRS_SE_TLJC_Intervention_Plan_And_Service_LogStandAlone.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.Forms_DVRS_SE_TLJC_Intervention_Plan_And_Service_LogReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.Code.REPORTS.EmergencyFactSheets.EmergencyFactSheetReportReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\EmergencyFactSheetReport_Kaposia.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\EmergencyFactSheetReport_NH.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\EmergencyFactSheetReport_SpArc.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_IndividualSupportPlan.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_MN_245D_IAAP.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_MN_ProgressReportAndRecommendations.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_MN_ProgressReportAndRecommendationsPage2.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.Code.REPORTS.Forms_MOPlacementLetter.Forms_MOPlacement.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.Code.REPORTS.Forms_MOPlacementLetter.Forms_MOPlacementLetter.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_MOPlacementLetter_Page1.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_MOPlacementLetter_Page2.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.Forms_PA_MonthlyProgressNote.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.ChangeOfStatusReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.CHS_NoticeOfRevocation.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.DMH_Quarterly_ReviewReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.DMH_Quarterly_ReviewReportMP.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.DVR_SDORReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.EITAS_MedicaidWaiverProvider.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.Forms_CHS_ROI_Report_Updated040517.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.Kaposia_SelfManagementAssessment_Report.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.Master_Agent_FormReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.MN_SelfManagementAssessment_Report2.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.MN_SelfManagementAssessment_Report.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.PersonCenteredPlan.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.RiskManagementPlan.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.SA_6_Month_Review.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.ServiceDischargeSummary.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.Staff_Individualized_Supervision_Plan_Report.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\MOVRInvoiceExcelReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.MOVRInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.RoomAndBoardInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.OregonInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.OregonInvoiceReportWithAuthLineCode.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.SimpleInvoiceReport2.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.SimpleInvoiceReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.SimpleInvoiceReport2DeptLogo.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Invoice_Reports.SimpleInvoiceReportDeptLogo.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.Code.REPORTS.INVOICE_REPORTS.WashingtonEmployment.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\JobPlacementComprehensiveReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.Code.REPORTS.JobPlacementComprehensiveReport.JobPlacementListReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\NDIJobPlacementListReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\JobPlacementListReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\JobPlacementListReportExcel.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\JobPlacementListReportExtendedExcel.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\NC_IPS_SE_Roster.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\OregonVRProgressReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\PayrollARCComprehensive.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Payroll_ComprehensiveReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Payroll_ComprehensiveSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Payroll_PeachtreeExportReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\Forms_Reports.PreEmploymentServiceLog.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ReportFilterErrorReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\ResidentialVarianceReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorksPayrollExportReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\StaffingPatternVarianceSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\StaffingPatternVarianceReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\TCMEmployeeSummaryReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\TicketToWorkWageAuthReport.resources
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.csproj.GenerateResource.cache
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.dll.licenses
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\csc.exe
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\csc.exe.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\csc.rsp
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\csi.exe
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\csi.exe.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\csi.rsp
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.CSharp.Core.targets
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.VisualBasic.Core.targets
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.AppContext.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Console.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.IO.Compression.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.IO.Pipes.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Reflection.Metadata.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Security.AccessControl.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Security.Claims.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Security.Principal.Windows.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Threading.Thread.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Xml.XPath.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\vbc.exe
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\vbc.exe.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\vbc.rsp
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\VBCSCompiler.exe
C:\Users\<USER>\Documents\SW\SETWorks\bin\roslyn\VBCSCompiler.exe.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorks.dll.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorks.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorks.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\netstandard.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.AppContext.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Collections.Concurrent.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Collections.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Collections.NonGeneric.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Collections.Specialized.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ComponentModel.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Console.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Data.Common.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.Debug.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.Tools.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Drawing.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Dynamic.Runtime.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Globalization.Calendars.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Globalization.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Globalization.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.Compression.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.Pipes.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Linq.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Linq.Expressions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Linq.Parallel.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Linq.Queryable.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Http.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.NameResolution.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.NetworkInformation.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Ping.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Requests.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Security.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Sockets.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.WebSockets.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ObjectModel.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Reflection.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Reflection.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Reflection.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Resources.Reader.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Resources.ResourceManager.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Resources.Writer.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.Handles.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.InteropServices.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.Numerics.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Claims.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Principal.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.SecureString.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.Encoding.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.RegularExpressions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Overlapped.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Tasks.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Thread.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.ThreadPool.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Timer.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Xml.XDocument.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Xml.XPath.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\AjaxControlToolkit.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\AjaxControlToolkit.StaticResources.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Antlr3.Runtime.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.Core.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.EC2.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.AI.OpenAI.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.Core.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.Identity.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.Search.Documents.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\ComponentSpace.SAML2.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Datadog.Trace.AspNet.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Datadog.Trace.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\DocumentFormat.OpenXml.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\EntityFramework.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\EntityFramework.SqlServer.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\EPPlus.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Flee.Net45.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\FuzzyString.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\GleamTech.Common.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\GleamTech.DocumentUltimate.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\HtmlAgilityPack.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\IronPdf.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\LazyCache.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\log4net.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Markdig.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.AspNet.SessionState.SessionStateModule.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Web.Infrastructure.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Web.RedisSessionStateProvider.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\MsgPack.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.Common.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.Mvc.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.WebApi.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\OpenAI.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\RealSignatureWeb.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\RestSharp.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Common.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Core.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Infrastructure.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Reporting.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Services.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorksDAO.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorksREPORTS.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Sigil.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Barcode.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.DataExport.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.DataExport.ResourceMgr.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Doc.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.DocViewer.Forms.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Email.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.License.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.OfficeViewer.Forms.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Pdf.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.PdfViewer.Asp.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.PdfViewer.Forms.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Presentation.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Spreadsheet.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.XLS.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Buffers.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ClientModel.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.Packaging.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.Pipelines.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Memory.Data.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Memory.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Http.Formatting.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Reflection.Metadata.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.AccessControl.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Permissions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.Json.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Channels.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Helpers.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Http.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Http.WebHost.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Mvc.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Optimization.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Razor.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.WebPages.Deployment.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.WebPages.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.WebPages.Razor.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Documents.SpreadsheetStreaming.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.OpenAccess.35.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.OpenAccess.40.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.OpenAccess.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.OpenAccess.Runtime.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Reporting.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Reporting.OpenXmlRendering.2.7.2.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Reporting.XpsRendering.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.ReportViewer.WebForms.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Web.UI.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Web.UI.Skins.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Core.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Fixed.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Flow.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Spreadsheet.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Spreadsheet.FormatProviders.OpenXml.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Spreadsheet.FormatProviders.Pdf.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Zip.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Zip.Extensions.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Twilio.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\WebActivatorEx.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\WebGrease.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\WebSignature.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\QuikGraph.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\UAParser.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Renci.SshNet.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.S3.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.SimpleEmail.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.mshtml.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Common.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Common.dll.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Core.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Core.dll.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Infrastructure.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Infrastructure.dll.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Reporting.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Services.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\SetWorks.Services.dll.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorksDAO.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorksDAO.dll.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorksREPORTS.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\SETWorksREPORTS.dll.config
C:\Users\<USER>\Documents\SW\SETWorks\bin\Antlr3.Runtime.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.Core.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.Core.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.EC2.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.EC2.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.AI.OpenAI.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.Core.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.Identity.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Azure.Search.Documents.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\ComponentSpace.SAML2.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Datadog.Trace.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Datadog.Trace.AspNet.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\DocumentFormat.OpenXml.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\EntityFramework.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\EntityFramework.SqlServer.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\EPPlus.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\GleamTech.Common.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\GleamTech.DocumentUltimate.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\HtmlAgilityPack.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\HtmlAgilityPack.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\IronPdf.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\log4net.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Markdig.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.AspNet.SessionState.SessionStateModule.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Bcl.AsyncInterfaces.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.CSharp.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.CSharp.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.CSharp.Scripting.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.CSharp.Scripting.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.Scripting.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeAnalysis.Scripting.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Caching.Abstractions.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Caching.Memory.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.DependencyInjection.Abstractions.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Options.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.Extensions.Primitives.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Microsoft.IdentityModel.Tokens.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\MsgPack.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.Common.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.Mvc.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Ninject.Web.WebApi.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\OpenAI.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Pipelines.Sockets.Unofficial.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\RestSharp.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Sigil.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Barcode.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.DataExport.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Doc.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.DocViewer.Forms.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Email.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.License.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.OfficeViewer.Forms.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Pdf.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.PdfViewer.Asp.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.PdfViewer.Forms.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Presentation.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.Spreadsheet.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Spire.XLS.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Buffers.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ClientModel.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Collections.Immutable.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Configuration.ConfigurationManager.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.DiagnosticSource.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Diagnostics.PerformanceCounter.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IdentityModel.Tokens.Jwt.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.IO.Pipelines.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Memory.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Memory.Data.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Net.Http.Formatting.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Numerics.Vectors.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Reflection.Metadata.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.AccessControl.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Security.Permissions.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.Encodings.Web.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Text.Json.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Channels.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Threading.Tasks.Extensions.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.ValueTuple.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Helpers.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Http.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Http.WebHost.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Mvc.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Optimization.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.Razor.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.WebPages.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.WebPages.Deployment.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\System.Web.WebPages.Razor.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Documents.SpreadsheetStreaming.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.OpenAccess.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.OpenAccess.35.Extensions.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.OpenAccess.40.Extensions.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Web.UI.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Core.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Fixed.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Flow.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Spreadsheet.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Spreadsheet.FormatProviders.OpenXml.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Documents.Spreadsheet.FormatProviders.Pdf.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Zip.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.Windows.Zip.Extensions.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Twilio.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.ReportViewer.WebForms.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\Telerik.ReportViewer.WebForms.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\QuikGraph.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\UAParser.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\Renci.SshNet.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.S3.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.S3.xml
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.SimpleEmail.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\AWSSDK.SimpleEmail.xml
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.csproj.CopyComplete
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.dll
C:\Users\<USER>\Documents\SW\SETWorks\obj\DebugNoNode\SETWorks.pdb
C:\Users\<USER>\Documents\SW\SETWorks\bin\StackExchange.Redis.dll
C:\Users\<USER>\Documents\SW\SETWorks\bin\StackExchange.Redis.xml
