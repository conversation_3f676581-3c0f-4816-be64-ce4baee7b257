C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\SetWorks.Core.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\SetWorks.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\SetWorks.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Antlr3.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\log4net.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\MathParser.org-mXparser.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\SETWorksDAO.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Twilio.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\UAParser.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\WebGrease.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.Web.UI.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.S3.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.SimpleEmail.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.ReportViewer.WebForms.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.Reporting.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\LazyCache.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.Windows.Zip.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\RestSharp.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\netstandard.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\SETWorksDAO.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\SETWorksDAO.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Antlr3.Runtime.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\log4net.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.IdentityModel.Tokens.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.IdentityModel.Tokens.Jwt.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Twilio.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\UAParser.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.Web.UI.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.S3.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.S3.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.SimpleEmail.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\AWSSDK.SimpleEmail.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.ReportViewer.WebForms.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.ReportViewer.WebForms.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.Reporting.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.Reporting.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Telerik.Windows.Zip.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\RestSharp.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Caching.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Primitives.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Caching.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.Options.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Numerics.Vectors.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\bin\Debug\System.Buffers.xml
C:\Users\<USER>\Documents\SW\SetWorks.Core\obj\Debug\SetWorks.Core.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\SW\SetWorks.Core\obj\Debug\SetWorks.Core.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\SW\SetWorks.Core\obj\Debug\SetWorks.Core.csproj.CopyComplete
C:\Users\<USER>\Documents\SW\SetWorks.Core\obj\Debug\SetWorks.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Core\obj\Debug\SetWorks.Core.pdb
