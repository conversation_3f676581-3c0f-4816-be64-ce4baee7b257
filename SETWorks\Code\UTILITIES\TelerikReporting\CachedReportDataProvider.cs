using System;
using System.Configuration;
using System.Data;
using SetWorks.Core.Interfaces;
using SetWorks.Infrastructure;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Cached report data provider that uses the existing MemoryCacheProvider to cache report data
    /// This reduces database load and improves performance for frequently accessed reports
    /// </summary>
    public class CachedReportDataProvider
    {
        private readonly ICacheProvider _cacheProvider;
        private readonly TimeSpan _defaultCacheTimeout;
        private readonly string _cacheKeyPrefix = "TelerikReport:Data:";
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(CachedReportDataProvider));

        public CachedReportDataProvider(ICacheProvider cacheProvider = null)
        {
            _cacheProvider = cacheProvider ?? new MemoryCacheProvider();
            _defaultCacheTimeout = GetDefaultCacheTimeout();
        }

        /// <summary>
        /// Get cached report data or execute the data provider function if not cached
        /// </summary>
        public DataTable GetCachedReportData(string reportKey, Func<DataTable> dataProvider, 
            TimeSpan? cacheTimeout = null, bool useAbsoluteExpiration = true)
        {
            if (string.IsNullOrEmpty(reportKey))
                throw new ArgumentException("Report key cannot be null or empty", nameof(reportKey));

            if (dataProvider == null)
                throw new ArgumentNullException(nameof(dataProvider));

            var cacheKey = GetCacheKey(reportKey);
            var timeout = cacheTimeout ?? _defaultCacheTimeout;

            try
            {
                var cachedData = _cacheProvider.GetAndSet(
                    key: cacheKey,
                    timeout: timeout,
                    isAbsolute: useAbsoluteExpiration,
                    retriever: () => {
                        Log.Info($"Loading report data for key: {reportKey}");
                        var data = dataProvider();
                        Log.Info($"Loaded {data?.Rows.Count ?? 0} rows for report key: {reportKey}");
                        return data;
                    });

                if (cachedData != null)
                {
                    Log.Debug($"Retrieved cached report data for key: {reportKey} ({cachedData.Rows.Count} rows)");
                }

                return cachedData;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting cached report data for key: {reportKey}", ex);
                // Fall back to direct data provider call
                return dataProvider();
            }
        }

        /// <summary>
        /// Get cached paginated report data
        /// </summary>
        public PaginatedReportDataSource GetCachedPaginatedData(string reportKey, 
            Func<int, int, DataTable> dataProvider, int totalRecords, int pageSize = 1000,
            TimeSpan? cacheTimeout = null)
        {
            var cacheKey = GetCacheKey($"{reportKey}:Paginated:{totalRecords}:{pageSize}");
            var timeout = cacheTimeout ?? _defaultCacheTimeout;

            try
            {
                var cachedDataSource = _cacheProvider.GetAndSet(
                    key: cacheKey,
                    timeout: timeout,
                    isAbsolute: true,
                    retriever: () => {
                        Log.Info($"Creating paginated data source for key: {reportKey}");
                        return new PaginatedReportDataSource(dataProvider, totalRecords, pageSize);
                    });

                Log.Debug($"Retrieved cached paginated data source for key: {reportKey}");
                return cachedDataSource;
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting cached paginated data for key: {reportKey}", ex);
                // Fall back to creating new instance
                return new PaginatedReportDataSource(dataProvider, totalRecords, pageSize);
            }
        }

        /// <summary>
        /// Cache report metadata (record counts, schema info, etc.)
        /// </summary>
        public T GetCachedMetadata<T>(string reportKey, string metadataType, Func<T> metadataProvider,
            TimeSpan? cacheTimeout = null)
        {
            var cacheKey = GetCacheKey($"{reportKey}:Meta:{metadataType}");
            var timeout = cacheTimeout ?? TimeSpan.FromHours(1); // Shorter timeout for metadata

            try
            {
                return _cacheProvider.GetAndSet(
                    key: cacheKey,
                    timeout: timeout,
                    isAbsolute: true,
                    retriever: metadataProvider);
            }
            catch (Exception ex)
            {
                Log.Error($"Error getting cached metadata for key: {reportKey}, type: {metadataType}", ex);
                return metadataProvider();
            }
        }

        /// <summary>
        /// Invalidate cached data for a specific report
        /// </summary>
        public void InvalidateReportCache(string reportKey)
        {
            try
            {
                var cacheKey = GetCacheKey(reportKey);
                _cacheProvider.Remove<DataTable>(cacheKey);
                
                // Also remove paginated cache entries
                var paginatedKey = GetCacheKey($"{reportKey}:Paginated");
                _cacheProvider.Remove<PaginatedReportDataSource>(paginatedKey);
                
                Log.Info($"Invalidated cache for report key: {reportKey}");
            }
            catch (Exception ex)
            {
                Log.Error($"Error invalidating cache for report key: {reportKey}", ex);
            }
        }

        /// <summary>
        /// Check if report data is cached
        /// </summary>
        public bool IsReportCached(string reportKey)
        {
            try
            {
                var cacheKey = GetCacheKey(reportKey);
                return _cacheProvider.IsInCache(cacheKey);
            }
            catch (Exception ex)
            {
                Log.Error($"Error checking cache status for report key: {reportKey}", ex);
                return false;
            }
        }

        /// <summary>
        /// Get cache statistics for monitoring
        /// </summary>
        public ReportCacheStatistics GetCacheStatistics()
        {
            // Note: MemoryCacheProvider doesn't expose detailed statistics
            // This is a placeholder for future enhancement
            return new ReportCacheStatistics
            {
                CacheProvider = _cacheProvider.GetType().Name,
                DefaultTimeout = _defaultCacheTimeout,
                CacheKeyPrefix = _cacheKeyPrefix
            };
        }

        private string GetCacheKey(string reportKey)
        {
            return _cacheKeyPrefix + reportKey;
        }

        private TimeSpan GetDefaultCacheTimeout()
        {
            var configValue = ConfigurationManager.AppSettings["TelerikReportCacheTimeoutMinutes"];
            if (!string.IsNullOrEmpty(configValue) && int.TryParse(configValue, out var timeoutMinutes))
            {
                return TimeSpan.FromMinutes(timeoutMinutes);
            }
            
            return TimeSpan.FromMinutes(30); // Default 30 minutes
        }
    }

    /// <summary>
    /// Cache statistics for monitoring
    /// </summary>
    public class ReportCacheStatistics
    {
        public string CacheProvider { get; set; }
        public TimeSpan DefaultTimeout { get; set; }
        public string CacheKeyPrefix { get; set; }
    }

    /// <summary>
    /// Factory for creating cached report data providers with common configurations
    /// </summary>
    public static class CachedReportDataProviderFactory
    {
        private static readonly Lazy<CachedReportDataProvider> _defaultProvider = 
            new Lazy<CachedReportDataProvider>(() => new CachedReportDataProvider());

        /// <summary>
        /// Get the default cached report data provider
        /// </summary>
        public static CachedReportDataProvider Default => _defaultProvider.Value;

        /// <summary>
        /// Create a cached data provider with custom cache timeout
        /// </summary>
        public static CachedReportDataProvider CreateWithTimeout(TimeSpan cacheTimeout)
        {
            return new CachedReportDataProvider();
        }

        /// <summary>
        /// Create a cached data provider with custom cache provider
        /// </summary>
        public static CachedReportDataProvider CreateWithCacheProvider(ICacheProvider cacheProvider)
        {
            return new CachedReportDataProvider(cacheProvider);
        }
    }

    /// <summary>
    /// Extension methods for easier integration with existing report code
    /// </summary>
    public static class ReportCacheExtensions
    {
        /// <summary>
        /// Extension method to easily cache DataTable results
        /// </summary>
        public static DataTable WithCache(this Func<DataTable> dataProvider, string reportKey, 
            TimeSpan? cacheTimeout = null)
        {
            var cacheProvider = CachedReportDataProviderFactory.Default;
            return cacheProvider.GetCachedReportData(reportKey, dataProvider, cacheTimeout);
        }

        /// <summary>
        /// Extension method to easily cache paginated results
        /// </summary>
        public static PaginatedReportDataSource WithPaginatedCache(this Func<int, int, DataTable> dataProvider, 
            string reportKey, int totalRecords, int pageSize = 1000, TimeSpan? cacheTimeout = null)
        {
            var cacheProvider = CachedReportDataProviderFactory.Default;
            return cacheProvider.GetCachedPaginatedData(reportKey, dataProvider, totalRecords, pageSize, cacheTimeout);
        }
    }
}
