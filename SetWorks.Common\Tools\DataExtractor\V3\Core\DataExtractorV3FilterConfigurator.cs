﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity.Infrastructure.Pluralization;
using System.Linq;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using BOs;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SetWorks.Common.Tools.DataExtractor.Core;
using SetWorks.Common.Tools.DataExtractor.V3.DataExtractors;
using SetWorks.Common.Tools.DataExtractor.V3.DataExtractors.CONSUMER;
using SetWorks.Core.Extensions;
using Telerik.Web.UI;

namespace SetWorks.Common.Tools.DataExtractor.V3.Core;

public class DataExtractorV3FilterConfigurator
{
    private SetWorks.Common.Tools.DataExtractor.Core.DataExtractor _dataExtractor;
    private int _clientId;
    private Guid _userId;
    private dynamic _extractorJson;
    private string[] _joinModels;
    private Dictionary<string, Dictionary<string, bool>> _modelConfig;
    private Dictionary<string, string> _modelNames;
    private Dictionary<string, HashSet<string>> _modelSources;
    public List<InputParameter> _configuredInputParams { get; private set; }

    public DataExtractorV3FilterConfigurator(SetWorks.Common.Tools.DataExtractor.Core.DataExtractor dataExtractor, int clientId,
        string userId, dynamic extractorJson)
    {
        _dataExtractor = dataExtractor;
        _clientId = clientId;
        _extractorJson = extractorJson;
        _userId = new Guid(userId);
        _joinModels = DataExtractorV3Manager.GetModelsForV3Columns(_extractorJson.columns.ToObject<string[]>());
        _modelConfig = new Dictionary<string, Dictionary<string, bool>>();
        var numConfigFields = extractorJson?.configurationFields?.Count;
        var filterModelMapJson = numConfigFields <= 1 ? string.Empty : _extractorJson?.configurationFields?[1]?.FilterModelMap?.ToString();
        if (!string.IsNullOrEmpty(filterModelMapJson))
        {
            _modelConfig = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, bool>>>(filterModelMapJson);
        }
        foreach (var item in _modelConfig)
        {
            var invalidKeys = new List<string>(item.Value.Keys.Except(_joinModels));
            foreach (var key in invalidKeys)
            {
                item.Value.Remove(key);
            }
        }
        _modelNames = GetModelNames();
        _modelSources = new Dictionary<string, HashSet<string>>();
        var defaultParameters = _dataExtractor.getInputParameters();
        var defaultParamCodes = defaultParameters.Select(ip => ip.parameterCode);
        if (HasFilterConfig())
        {
            _configuredInputParams = new List<InputParameter>(JsonConvert.DeserializeObject<List<InputParameter>>(
                    _extractorJson.configurationFields[0]?.SelectedReportFilters?.ToString()))
                .Where(x => new List<bool>(_modelConfig[x.parameterCode].Values).Any(v => v) && defaultParamCodes.Contains(x.parameterCode)).ToList();
        }
    }
    
    public List<InputParameter> ApplyFilterConfig()
    {
        var defaultParameters = _dataExtractor.getInputParameters();
        var inputParameters = _dataExtractor.getInputParameters();
        bool hasJoinData = _extractorJson.transformations.Count > 0 &&
                           _extractorJson.transformations[0].JOIN_DATA != null;
        foreach (var cip in _configuredInputParams)
        {
            var paramConfig = _modelConfig[cip.parameterCode];
            var sourceModels = paramConfig.Keys.Where(k => paramConfig[k]);
            if (!_modelSources.ContainsKey(cip.parameterCode))
            {
                _modelSources[cip.parameterCode] = new HashSet<string>();
            }
            _modelSources[cip.parameterCode].UnionWith(sourceModels);
        }
        JArray joinData = !hasJoinData ? new JArray() : JsonConvert.DeserializeObject<JArray>(_extractorJson.transformations[0].JOIN_DATA.ToString());
        var formData = DataExtractorV3Manager.GetFormData(_extractorJson);
        InputParameter[] requiredParams = null;
        foreach(var ip in inputParameters)
        {
            if (!_modelSources.ContainsKey(ip.parameterCode))
            {
                _modelSources[ip.parameterCode] = new HashSet<string>();
            }
        }
        var relations = new List<ModelRelation>();
        if (_joinModels.Length > 1)
        {
            relations = DataExtractorRelations.GetRelationsForV3Columns(_joinModels, joinData, formData);
            requiredParams = GetParamsForJoinPath(relations, _clientId, _userId);
        }

        inputParameters = ConfigureInputParameters(inputParameters, _configuredInputParams.ToArray(), requiredParams);
        var consumerEventDateFilterModeParam = inputParameters.FirstOrDefault(ip => ip.parameterCode == ConsumerEventsFilters.CONSUMER_EVENT_DATE_FILTER_MODE);
        if (relations.Count > 0 && consumerEventDateFilterModeParam != null)
        {
            var consumerEventRelation = relations.First(rel => rel.SourceModel == DataExtractorType.CONSUMER_EVENTS ||
                                                               rel.DestinationModel == DataExtractorType.CONSUMER_EVENTS);
            consumerEventDateFilterModeParam.parameterValue = ConsumerEventsFilters.DateFilterModeCodeIndices.GetValue(consumerEventRelation.DateState, 0);

        }
        foreach (var ip in inputParameters.Except(defaultParameters))
        {
            _dataExtractor.addInputParameter(ip);
        }

        foreach (var ip in defaultParameters.Except(inputParameters))
        {
            _dataExtractor.setInputParameterValue(ip.parameterCode, null);
        }

        _dataExtractor.setFilterModelConfig(_modelConfig);
        return inputParameters;
    }

    public bool HasFilterConfig()
    {
        return _extractorJson.configurationFields != null &&
               _extractorJson.configurationFields is JArray &&
               _extractorJson.configurationFields.Count > 0 &&
               _extractorJson.configurationFields[0].SelectedReportFilters != null;
    }

    public bool ShouldGetFilterControl(Exception e)
    {
        return e.Message.ToLower().Contains("filter type") && HasFilterConfig();
    }

    public void ApplyTooltip(Control control, InputParameter inputParameter)
    {
        var disabledModels = new List<string>();
        if (_modelConfig.TryGetValue(inputParameter.parameterCode, out var paramConfig))
        {
            disabledModels = paramConfig.Keys.Where(k => paramConfig.ContainsKey(k) && !paramConfig[k])
                .Select(m => _modelNames.GetValue(m, m)).ToList();
        }
        
        var tooltipSuffix = "";
        if ((_modelConfig.ContainsKey(inputParameter.parameterCode) || inputParameter.required) && _modelConfig.Count > 0)
        {
            var modelNames = _modelSources[inputParameter.parameterCode].Except(disabledModels)
                .Where(m => paramConfig == null || paramConfig.GetValue(m, true))
                .Select(m => _modelNames.GetValue(m, m));
            tooltipSuffix = $" (applies to {string.Join(", ", modelNames)})";
        }
        var shouldHide = paramConfig != null && !paramConfig.Any(c => c.Value);
        if (shouldHide)
        {
            inputParameter.required = false;
            control.Visible = false;
        }
        if (control is RadDropDownList)
        {
            ((RadDropDownList)control).ToolTip += tooltipSuffix;
        }
        
        if (control is RadComboBox && inputParameter.parameterCode == ConsumerEventsFilters.CONSUMER_EVENT_DATE_FILTER_MODE
                                   && !string.IsNullOrEmpty(inputParameter.parameterValue?.ToString()))
        {
            var box = control as RadComboBox;
            box.SelectedValue = inputParameter.parameterValue.ToString();
        }

        var paramVal = inputParameter.parameterValue?.ToString();
        if (control is RadComboBox && !string.IsNullOrEmpty(paramVal))
        {
            var box = control as RadComboBox;
            if (box.Items.FindItemByValue(paramVal) != null)
            {
                box.SelectedValue = inputParameter.parameterValue.ToString();
            }
        }
        if (control is RadComboBox)
        {
            var box = control as RadComboBox;
            if (!string.IsNullOrEmpty(box.Attributes["Description"]))
            {
                FixComboBoxText(inputParameter, box);
            }
            box.ToolTip += tooltipSuffix;
            if (shouldHide)
            {
                box.SelectedIndex = 0;
            }
        }

        if (control is RadDatePicker)
        {
            (control as RadDatePicker).ToolTip += tooltipSuffix;
        }

        if (control is RadCheckBox)
        {
            (control as RadCheckBox).ToolTip += tooltipSuffix;
        }

        if (control is CheckBox)
        {
            (control as CheckBox).ToolTip = tooltipSuffix;
        }
    }

    public Control GetFilterControl(InputParameter inputParam)
    {
        var defaultValue = DataExtractorRelations.DefaultValues.GetValue(inputParam.filterType, inputParam.parameterValue);
        inputParam.parameterValue = defaultValue;
        DataExtractorRelations.DefaultStatuses.TryGetValue(inputParam.filterType, out var statuses);
        return DataExtractorV3Manager.GetFilterControl(inputParam.filterType, inputParam.parameterName, 
            defaultValue: defaultValue, data: DataExtractorRelations.FilterData.GetValueOrNull(inputParam.filterType),
            width: DataExtractorRelations.FilterWidths.GetValue(inputParam.filterType), statuses: statuses);
    }

    public void FilterInputParams(DataExtractor.Core.DataExtractor dataExtractor)
    {
        if (!HasFilterConfig())
        {
            return;
        }
        var inputParamsToRemove = new List<InputParameter>();
        foreach (var inputParam in dataExtractor.getInputParameters())
        {
            if (inputParam.filterType == SWFilterType.NonFilter || 
                inputParam.parameterType == InputParameter.Type.Custom
                || _configuredInputParams.Any(ip => ip.filterType == inputParam.filterType))
            {
                continue;
            }
            inputParamsToRemove.Add(inputParam); 
        }

        foreach (var inputParam in inputParamsToRemove)
        {
            dataExtractor.removeInputParameter(inputParam); 
        }
    }

    private void FixComboBoxText(InputParameter inputParameter, RadComboBox box)
    {
        var newDescription = new StringBuilder(box.Attributes["Description"]);
        foreach (var model in _modelNames.Keys)
        {
            var newName = _modelNames.GetValue(model, model);
            if (newName == model) { continue; }
            newDescription = newDescription.Replace(model, newName);
            foreach(RadComboBoxItem item in box.Items)
            {
                item.Text = item.Text.Replace(model, newName);
            }
        }
        box.Attributes["Description"] = newDescription.ToString();
    }

    private List<InputParameter> ConfigureInputParameters(List<InputParameter> inputParameters, InputParameter[] configuredInputParameters, 
        InputParameter[] requiredParams = null)
    {
        if (configuredInputParameters.Length == 0)
        {
            return inputParameters.Where(ip => ip.filterType == SWFilterType.NonFilter || ip.filterType == SWFilterType.Department ||
                                               ip.required).ToList();
        }

        var newCurrentFilters = inputParameters.Where(ip =>
            configuredInputParameters.Any(cip => 
                cip.parameterType == ip.parameterType && cip.parameterCode == ip.parameterCode) ||
            (ip.parameterType != InputParameter.Type.SWFilter || ip.filterType == SWFilterType.Department ||
             ip.filterType == SWFilterType.NonFilter || ip.required)).ToList();
        var missingFilters = inputParameters.Where(ip => ip.parameterType == InputParameter.Type.SWFilter && !
            _modelConfig.ContainsKey(ip.parameterCode) && !newCurrentFilters.Contains(ip));
        foreach (var filter in missingFilters)
        {
            newCurrentFilters.Add(filter);
        }
        var paramCodes = inputParameters.Select(ip => ip.parameterCode);
        var newFilters = configuredInputParameters.Where(cip => !paramCodes.Contains(cip.parameterCode)).ToList();
        foreach (var filter in newFilters)
        {
            _dataExtractor.addInputParameter(filter);
        }

        newCurrentFilters.AddRange(newFilters);
        AddMissingFilters(requiredParams, newCurrentFilters);
        if (requiredParams == null) { return newCurrentFilters; }
        
        var filterTypes = new HashSet<SWFilterType>(newCurrentFilters.Select(f => f.filterType));
        newCurrentFilters.AddRange(requiredParams.Where(reqFilter => reqFilter.required 
                                                                     || (!filterTypes.Contains(reqFilter.filterType) && configuredInputParameters.Contains(reqFilter)) 
                                                                     || (requiredParams.Contains(reqFilter) && _modelConfig.ContainsKey(reqFilter.parameterCode) 
                                                                         && _modelConfig[reqFilter.parameterCode].Any(v => v.Value))));
        return newCurrentFilters;
    }

    private void AddMissingFilters(InputParameter[] requiredParams, List<InputParameter> newCurrentFilters)
    {
        var filterTypes = new HashSet<SWFilterType>(newCurrentFilters.Concat(requiredParams ?? Array.Empty<InputParameter>())
            .Select(f => f.filterType));
        var deptRequiredFilters = new HashSet<SWFilterType> { SWFilterType.Staff, SWFilterType.Staff_Multi_Select, 
            SWFilterType.Staff_Primary_Contact, SWFilterType.Staff_Secondary_Contact, SWFilterType.Consumer };
        var consumerDateFiltersRequired = new HashSet<SWFilterType> { SWFilterType.Authorization, SWFilterType.Authorization_Multi_Select };
        var typesRequiringConsumerDateFilter = filterTypes.Intersect(consumerDateFiltersRequired).ToList();
        var needsConsumerDateFilters = typesRequiringConsumerDateFilter.Any();
        var needsFundingSourceFilter = filterTypes.Contains(SWFilterType.Funding_Source_Contact);

        if (needsFundingSourceFilter)
        {
            var newFundingSourceFilter = new InputParameter(SWFilterType.Funding_Source.ToString(),
                SWFilterType.Funding_Source.ToString(), true, SWFilterType.Funding_Source, SqlDbType.Int);
            filterTypes.Add(SWFilterType.Funding_Source);
            newCurrentFilters.Add(newFundingSourceFilter);
        }
        if (needsConsumerDateFilters && !filterTypes.Contains(SWFilterType.Consumer))
        {
            var newConsumerFilter = new InputParameter(SWFilterType.Consumer.ToString(), SWFilterType.Consumer.ToString(), true, SWFilterType.Consumer, SqlDbType.Int);
            newCurrentFilters.Add(newConsumerFilter);
            SetModelSources(new[]{newConsumerFilter}, typesRequiringConsumerDateFilter);
        }
        if (needsConsumerDateFilters && !filterTypes.Contains(SWFilterType.Date_Range_From))
        {
            var startDateFilter = new InputParameter(SWFilterType.Date_Range_From.ToString(), SWFilterType.Date_Range_From.ToString(), true, SWFilterType.Date_Range_From, SqlDbType.DateTime);
            newCurrentFilters.Add(startDateFilter);
            var endDateFilter = new InputParameter(SWFilterType.Date_Range_To.ToString(), SWFilterType.Date_Range_To.ToString(), true, SWFilterType.Date_Range_To, SqlDbType.DateTime);
            newCurrentFilters.Add(endDateFilter);
            filterTypes.Add(SWFilterType.Date_Range_From);
            filterTypes.Add(SWFilterType.Date_Range_To);
            SetModelSources(new[]{startDateFilter, endDateFilter}, typesRequiringConsumerDateFilter);
        }

        var typesRequiringDeptFilter = filterTypes.Intersect(deptRequiredFilters).ToList();
        if (!typesRequiringDeptFilter.Any() || filterTypes.Contains(SWFilterType.Department_Multi_Select)) { return; }

        var newDeptFilter = new InputParameter(SWFilterType.Department_Multi_Select.ToString(),
            SWFilterType.Department_Multi_Select.ToString(), true, SWFilterType.Department_Multi_Select, SqlDbType.Structured);
        newCurrentFilters.Add(newDeptFilter);
        SetModelSources(new[]{newDeptFilter}, typesRequiringDeptFilter);
    }

    private void SetModelSources(InputParameter[] addedFilters, List<SWFilterType> typesRequiringAddedFilter)
    {
        foreach (var filter in addedFilters)
        {
            if (!_modelSources.ContainsKey(filter.parameterCode))
            {
                _modelSources[filter.parameterCode] = new HashSet<string>();
            }
        }
        foreach (var filterType in typesRequiringAddedFilter)
        {
            foreach (var filter in addedFilters)
            {
                _modelSources[filter.parameterCode].UnionWith(_modelSources[filterType.ToString()]);
            }
        }
    }

    private InputParameter[] GetParamsForJoinPath(List<ModelRelation> relations, int clientId, Guid currentUser)
    {
        var models = new HashSet<DataExtractorType>();
        var requiredParams = new HashSet<InputParameter>();
        foreach (var rel in relations)
        {
            models.Add(rel.SourceModel);
            models.Add(rel.DestinationModel);
        }

        foreach (var model in models)
        {
            var extractor = DataExtractorV3Manager.getDataExtractorFromCode(model.ToString(), clientId, currentUser);
            var modelParams = extractor.getInputParameters().Where(ip => ip.parameterType == InputParameter.Type.SWFilter).ToList();
            foreach(var param in modelParams)
            {
                if (!_modelSources.ContainsKey(param.parameterCode))
                {
                    _modelSources[param.parameterCode] = new HashSet<string>();
                }
                _modelSources[param.parameterCode].Add(DataExtractorRelations.DataExtractorNames[model]);
            }
            requiredParams.UnionWith(modelParams);
        }
        return requiredParams.ToArray();
    }
    
    private Dictionary<string, string> GetModelNames(){
        
        var siteLabels = BOSiteLabel.getSiteLabelsByClientID(_clientId).
            Where(sl => sl.getActive && DataExtractorRelations.SiteLabelsToTables.ContainsKey(sl.getSiteLabelCode)).
            ToDictionary(item => DataExtractorRelations.SiteLabelsToTables[item.getSiteLabelCode], item => item.getSiteLabelDescription);
        var pluralService = new EnglishPluralizationService();
        Func<string, string> replaceSiteLabels = (col) =>
        {
            foreach (var label in siteLabels.Where(label => label.Key != label.Value))
            {
                col = col.Replace(pluralService.Pluralize(label.Key), label.Value);
                col = col.Replace(pluralService.Singularize(label.Key), label.Value);
            }

            return col;
        };
        var modelTranslations = DataExtractorRelations.DataExtractorCodes.ToDictionary(
            item => item.Key, item => siteLabels.GetValueOrNull(item.Key) ?? replaceSiteLabels(item.Key));
        foreach (var word in DataExtractorRelations.DataExtractorSiteLabels.Keys.Where(word => word != "Consumers"))
        {
            modelTranslations[word] = siteLabels.GetValueOrNull(word) ?? replaceSiteLabels(word);
        }
        modelTranslations["Consumer"] = pluralService.Singularize(modelTranslations["Consumers"]);
        return modelTranslations;
    }
    
}