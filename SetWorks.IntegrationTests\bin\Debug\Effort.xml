<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Effort</name>
    </assembly>
    <members>
        <member name="T:Effort.DataLoaders.CachingDataLoader">
            <summary>
                Represents a data loader that serves as a caching layer above another data loader.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.IDataLoader">
            <summary>
                Defines the required members of an Effort data loader.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.IDataLoader.CreateTableDataLoaderFactory">
            <summary>
                Creates a table data loader factory.
            </summary>
            <returns> A table data loader factory. </returns>
        </member>
        <member name="P:Effort.DataLoaders.IDataLoader.Argument">
            <summary>
                Gets or sets the argument that describes the complete state of the data loader.
            </summary>
            <value>
                The argument.
            </value>
        </member>
        <member name="F:Effort.DataLoaders.CachingDataLoader.WrappedType">
            <summary>
                The attribute name of the type of the wrapped data loader in the argument.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.CachingDataLoader.WrappedArgument">
            <summary>
                The attribute name of the argument of the wrapped data loader in the argument.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.CachingDataLoader.wrappedDataLoader">
            <summary>
                The wrapped data loader.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.CachingDataLoader.locking">
            <summary>
                Indicates if the wrapped data loader should be used only once at the same time.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CachingDataLoader.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CachingDataLoader"/> class.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CachingDataLoader.#ctor(Effort.DataLoaders.IDataLoader)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CachingDataLoader"/> class.
            </summary>
            <param name="wrappedDataLoader"> 
                The wrapped data loader. 
            </param>
        </member>
        <member name="M:Effort.DataLoaders.CachingDataLoader.#ctor(Effort.DataLoaders.IDataLoader,System.Boolean)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CachingDataLoader"/> class.
                Enabling the <paramref name="locking"/> flag makes the caching data loader 
                instances to work in a cooperative way. They ensure that only one of wrapped
                data loaders initialized with the same configuration is utilized at the same
                time. 
            </summary>
            <param name="wrappedDataLoader"> 
                The wrapped data loader. 
            </param>
            <param name="locking">
                Indicates if the wrapped data loader should be used only once at the same time.
            </param>
        </member>
        <member name="M:Effort.DataLoaders.CachingDataLoader.CreateTableDataLoaderFactory">
            <summary>
                Creates a table data loader factory.
            </summary>
            <returns>
                A table data loader factory.
            </returns>
        </member>
        <member name="P:Effort.DataLoaders.CachingDataLoader.WrappedDataLoader">
            <summary>
                Gets the wrapped data loader.
            </summary>
            <value>
                The wrapped data loader.
            </value>
        </member>
        <member name="P:Effort.DataLoaders.CachingDataLoader.Effort#DataLoaders#IDataLoader#Argument">
            <summary>
                Gets or sets the argument that describes the complete state of the data loader.
            </summary>
            <value>
                The argument.
            </value>
        </member>
        <member name="T:Effort.DataLoaders.CachingTableDataLoader">
            <summary>
                Represents a table data loader that returns cached data that was retrieved from
                another table data loader.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.ITableDataLoader">
            <summary>
                Provides functionality for creating initial data for a table.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.ITableDataLoader.GetData">
            <summary>
                Creates initial data for the table.
            </summary>
            <returns> The data created for the table. </returns>
        </member>
        <member name="F:Effort.DataLoaders.CachingTableDataLoader.data">
            <summary>
                The cached data.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoader.#ctor(Effort.DataLoaders.ITableDataLoader)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CachingTableDataLoader"/> class.
            </summary>
            <param name="tableDataLoader">
                The table data loader that is used to retrieve the data.
            </param>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoader.GetData">
            <summary>
                Creates initial data for the table.
            </summary>
            <returns>
                The data created for the table.
            </returns>
        </member>
        <member name="T:Effort.DataLoaders.CachingTableDataLoaderFactory">
            <summary>
                Represents a table data loader factory that creates 
                <see cref="T:Effort.DataLoaders.CachingTableDataLoader"/> instances for tables.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.ITableDataLoaderFactory">
            <summary>
                Defines functionality for creating data loaders for tables.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.ITableDataLoaderFactory.CreateTableDataLoader(Effort.DataLoaders.TableDescription)">
            <summary>
                Creates a data loader for the specified table.
            </summary>
            <param name="table"> The metadata of the table. </param>
            <returns> The data loader for the table. </returns>
        </member>
        <member name="F:Effort.DataLoaders.CachingTableDataLoaderFactory.wrappedDataLoader">
            <summary>
                The wrapped data loader.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.CachingTableDataLoaderFactory.wrappedTableDataLoaderFactory">
            <summary>
                The table data loader factory retrieved from the wrapped data loader if neeed.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.CachingTableDataLoaderFactory.latch">
            <summary>
                The latch that locks the entire configuration of the wrapped data loader in
                order to make it be used only once during the caching phase.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.CachingTableDataLoaderFactory.dataStore">
            <summary>
                The store that contains the cached table data.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderFactory.#ctor(Effort.DataLoaders.IDataLoader)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CachingTableDataLoaderFactory"/> 
                class.
            </summary>
            <param name="wrappedDataLoader"> 
                The wrapped data loader.
            </param>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderFactory.#ctor(Effort.DataLoaders.IDataLoader,System.Boolean)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CachingTableDataLoaderFactory"/> 
                class. 
                Enabling the <paramref name="locking"/> flag makes the caching factory 
                instances to work in a cooperative way. They ensure that only one of wrapped
                factory objects initialized with the same configuration is utilized at the same
                time. 
            </summary>
            <param name="wrappedDataLoader"> 
                The wrapped data loader. 
            </param>
            <param name="locking"> 
                Indicates if the wrapped data loader should be used only once at the same time.
            </param>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderFactory.#ctor(Effort.DataLoaders.IDataLoader,Effort.DataLoaders.IDataLoaderConfigurationLatch,Effort.DataLoaders.ICachingTableDataLoaderStore)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CachingTableDataLoaderFactory"/> 
                class.
            </summary>
            <param name="wrappedDataLoader"> The wrapped data loader. </param>
            <param name="latch"> The latch that locks the data loader configuration. </param>
            <param name="dataStore"> The store that contains the cached data. </param>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderFactory.CreateTableDataLoader(Effort.DataLoaders.TableDescription)">
            <summary>
                Creates a data loader for the specified table.
            </summary>
            <param name="table"> The metadata of the table. </param>
            <returns>
                The data loader for the table.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderFactory.Dispose">
            <summary>
                Disposes the wrapped data loader table factory and releases the latch on the
                wrapped data loader configuration.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderFactory.CreateLatch(Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates the default latch for the data loader configuration locking.
            </summary>
            <param name="dataLoader"> The data loader. </param>
            <returns> The latch. </returns>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderFactory.CreateCachedData(Effort.DataLoaders.TableDescription)">
            <summary>
                Creates a proxy for the global table data cache.
            </summary>
            <param name="table"> The table metadata. </param>
            <returns> The proxy for the cache. </returns>
        </member>
        <member name="T:Effort.DataLoaders.ColumnDescription">
            <summary>
                Stores the metadata of a table column.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.ColumnDescription.#ctor(System.String,System.Type)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.ColumnDescription"/> class.
            </summary>
            <param name="name"> The name of the column. </param>
            <param name="type"> The type of the column. </param>
        </member>
        <member name="P:Effort.DataLoaders.ColumnDescription.Name">
            <summary>
                Gets the name of the column.
            </summary>
            <value>
                The name of the column.
            </value>
        </member>
        <member name="P:Effort.DataLoaders.ColumnDescription.Type">
            <summary>
                Gets the type of the column.
            </summary>
            <value>
                The type of the colum.
            </value>
        </member>
        <member name="T:Effort.DataLoaders.CachingTableDataLoaderStoreProxy">
            <summary>
                Represents a proxy towards the global table data store.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.ICachingTableDataLoaderStore">
            <summary>
                Provides functionality to check or return cached table data.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.ICachingTableDataLoaderStore.GetCachedData(Effort.Internal.Caching.CachingTableDataLoaderKey,System.Func{Effort.DataLoaders.CachingTableDataLoader})">
            <summary>
                Returns the stored table data.
            </summary>
            <param name="key"> 
                The key that identifies the table data.
            </param>
            <param name="factoryMethod">
                The factory method that initilizes the table data if has not been added to the
                store yet.
            </param>
            <returns>
                The table data.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.ICachingTableDataLoaderStore.Contains(Effort.Internal.Caching.CachingTableDataLoaderKey)">
            <summary>
                Determines whether the desired table data is added to store.
            </summary>
            <param name="key">
                The key that identifies the table data.
            </param>
            <returns>
              <c>true</c> if the store contains the data, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderStoreProxy.GetCachedData(Effort.Internal.Caching.CachingTableDataLoaderKey,System.Func{Effort.DataLoaders.CachingTableDataLoader})">
            <summary>
                Returns the stored table data.
            </summary>
            <param name="key"> 
                The key that identifies the table data.
            </param>
            <param name="factoryMethod">
                The factory method that initilizes the table data if has not been added to the
                store yet.
            </param>
            <returns>
                The table data.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.CachingTableDataLoaderStoreProxy.Contains(Effort.Internal.Caching.CachingTableDataLoaderKey)">
            <summary>
                Determines whether the desired table data is added to store.
            </summary>
            <param name="key">
                The key that identifies the table data.
            </param>
            <returns>
              <c>true</c> if the store contains the data, otherwise <c>false</c>.
            </returns>
        </member>
        <member name="T:Effort.DataLoaders.CsvDataLoader">
            <summary>
                Represents a data loader that reads data from CSV files.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CsvDataLoader.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CsvDataLoader"/> class.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CsvDataLoader.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CsvDataLoader"/> class.
            </summary>
            <param name="path"> The path of the folder that contains the CSV files. </param>
        </member>
        <member name="M:Effort.DataLoaders.CsvDataLoader.CreateTableDataLoaderFactory">
            <summary>
                Creates a <see cref="T:Effort.DataLoaders.CsvTableDataLoaderFactory"/> instance.
            </summary>
            <returns>
                A <see cref="T:Effort.DataLoaders.CsvTableDataLoaderFactory"/> instance.
            </returns>
        </member>
        <member name="P:Effort.DataLoaders.CsvDataLoader.ContainerFolderPath">
            <summary>
                Gets path of the folder that contains the CSV files.
            </summary>
            <value>
                The path of the folder.
            </value>
        </member>
        <member name="P:Effort.DataLoaders.CsvDataLoader.Effort#DataLoaders#IDataLoader#Argument">
            <summary>
                Gets or sets the argument that contains the path of the folder where the CSV
                files are located.
            </summary>
            <value>
                The argument.
            </value>
        </member>
        <member name="T:Effort.DataLoaders.CsvValueConverter">
            <summary>
                Converts string values retrieved from Effort compatible CSV files to desired types.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.IValueConverter">
            <summary>
                Defines functionality for converting arbitrary values to a specified type.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.IValueConverter.ConvertValue(System.Object,System.Type)">
            <summary>
                Converts the specified value to comply with the expected type.
            </summary>
            <param name="value"> The current value. </param>
            <param name="type"> The expected type. </param>
            <returns> The expected value. </returns>
        </member>
        <member name="M:Effort.DataLoaders.CsvValueConverter.ConvertValue(System.Object,System.Type)">
            <summary>
                Converts the specified value to comply with the expected type.
            </summary>
            <param name="value"> The current value. </param>
            <param name="type"> The expected type. </param>
            <returns> The expected value. </returns>
        </member>
        <member name="T:Effort.DataLoaders.DataLoaderConfigurationLatchProxy">
            <summary>
                Represents a proxy towards the appropriate 
                <see cref="T:Effort.Internal.Caching.DataLoaderConfigurationLatch"/> object.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.IDataLoaderConfigurationLatch">
            <summary>
                Provides functionality to acquire or release a data loader configuration latch.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.IDataLoaderConfigurationLatch.Acquire">
            <summary>
                Acquires the configuration latch.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.IDataLoaderConfigurationLatch.Release">
            <summary>
                Releases the configuration latch.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.aquired">
            <summary>
                Indicates is the latch is acquired.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.key">
            <summary>
                The key that identifies the latch.
            </summary>
        </member>
        <member name="F:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.latch">
            <summary>
                The global configuration latch.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.#ctor(Effort.Internal.Caching.DataLoaderConfigurationKey)">
            <summary>
                Initializes a new instance of the 
                <see cref="T:Effort.DataLoaders.DataLoaderConfigurationLatchProxy"/> class.
            </summary>
            <param name="key"> The key that identifies the global latch. </param>
        </member>
        <member name="M:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.Finalize">
            <summary>
                Finalizes an instance of the <see cref="T:Effort.DataLoaders.DataLoaderConfigurationLatchProxy"/> 
                class.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.Acquire">
            <summary>
                Acquires the configuration latch.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.Release">
            <summary>
                Releases the configuration latch.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.DataLoaderConfigurationLatchProxy.System#IDisposable#Dispose">
            <summary>
                Releases the configuration latch.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.EmptyDataLoader">
            <summary>
                Represents a data loader that retrieves no data.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.EmptyDataLoader.CreateTableDataLoaderFactory">
            <summary>
                Creates a <see cref="T:Effort.DataLoaders.EmptyTableDataLoaderFactory"/> instance.
            </summary>
            <returns>
                A <see cref="T:Effort.DataLoaders.EmptyTableDataLoaderFactory"/> instance.
            </returns>
        </member>
        <member name="P:Effort.DataLoaders.EmptyDataLoader.Effort#DataLoaders#IDataLoader#Argument">
            <summary>
                Gets or sets the argument that does not effect anything.
            </summary>
            <value> 
                The argument. 
            </value>
        </member>
        <member name="T:Effort.DataLoaders.EntityDataLoader">
            <summary>
                Represents a data loader that loads data from a database that has an Entity 
                Framework provider registered.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.EntityDataLoader.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.EntityDataLoader"/> class.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.EntityDataLoader.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.EntityDataLoader"/> class.
            </summary>
            <param name="entityConnectionString"> The entity connection string. </param>
        </member>
        <member name="M:Effort.DataLoaders.EntityDataLoader.CreateTableDataLoaderFactory">
            <summary>
                Creates a <see cref="T:Effort.DataLoaders.EntityTableDataLoaderFactory"/> instance.
            </summary>
            <returns>
                The <see cref="T:Effort.DataLoaders.EntityTableDataLoaderFactory"/> instance.
            </returns>
        </member>
        <member name="P:Effort.DataLoaders.EntityDataLoader.Effort#DataLoaders#IDataLoader#Argument">
            <summary>
                Gets or sets the argument that contains the entity connection string that 
                references to the source database.
            </summary>
            <value>
                The argument.
            </value>
        </member>
        <member name="T:Effort.DataLoaders.FileSource">
            <summary>
                Represents a source of files.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.FileSource.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.FileSource"/> class.
            </summary>
            <param name="path"> The path representing the source. </param>
        </member>
        <member name="M:Effort.DataLoaders.FileSource.GetFile(System.String)">
            <summary>
                Returns the specified file contained by this soruce.
            </summary>
            <param name="name">The name of the file.</param>
            <returns> Reference for the requested file. </returns>
        </member>
        <member name="P:Effort.DataLoaders.FileSource.IsValid">
            <summary>
                Gets a value indicating whether the source is valid and containing CSV files.
            </summary>
            <value>
              <c>true</c> if valid; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Effort.DataLoaders.FileSource.Path">
            <summary>
                The path that represents the source.
            </summary>
            <value>
                The path.
            </value>
        </member>
        <member name="T:Effort.DataLoaders.IFileReference">
            <summary>
                Represents a file reference.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.IFileReference.Open">
            <summary>
                Opens the referenced file.
            </summary>
            <returns> The file stream. </returns>
        </member>
        <member name="P:Effort.DataLoaders.IFileReference.Exists">
            <summary>
                Gets a value indicating whether the file exists.
            </summary>
            <value>
              <c>true</c> if the file exists; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Effort.DataLoaders.ObjectLoader">
            <summary>
                Loads data from a table data loader and materializes it.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.ObjectLoader.Load(Effort.DataLoaders.ITableDataLoaderFactory,Effort.Internal.DbManagement.Schema.DbTableInfo)">
            <summary>
                Loads the table data from the specified table data loader and materializes it
                bases on the specified metadata.
            </summary>
            <param name="loaderFactory"> The loader factory. </param>
            <param name="table"> The table metadata. </param>
            <returns> The materialized data. </returns>
        </member>
        <member name="T:Effort.DataLoaders.TableDataLoaderBase">
            <summary>
                Provides an abstract base class for <see cref="T:System.Data.IDataReader"/> based
                table data loaders.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.TableDataLoaderBase.#ctor(Effort.DataLoaders.TableDescription)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.TableDataLoaderBase"/> class.
            </summary>
            <param name="table"> The metadata of the table. </param>
        </member>
        <member name="M:Effort.DataLoaders.TableDataLoaderBase.GetData">
            <summary>
                Creates initial data for the table.
            </summary>
            <returns>
                The data created for the table.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.TableDataLoaderBase.CreateDataReader">
            <summary>
                Creates a data reader that retrieves the initial data.
            </summary>
            <returns> The data reader. </returns>
        </member>
        <member name="M:Effort.DataLoaders.TableDataLoaderBase.ConvertValue(System.Object,System.Type)">
            <summary>
                Converts the value to comply with the expected type.
            </summary>
            <param name="value"> The current value. </param>
            <param name="type"> The expected type. </param>
            <returns> The expected value. </returns>
        </member>
        <member name="P:Effort.DataLoaders.TableDataLoaderBase.Table">
            <summary>
                Gets the metadata of the table.
            </summary>
            <value>
                The metadata of the table.
            </value>
        </member>
        <member name="T:Effort.DataLoaders.TableDescription">
            <summary>
                Stores the metadata of a table.
            </summary>
        </member>
        <member name="P:Effort.DataLoaders.TableDescription.Name">
            <summary>
                Gets the name of the table.
            </summary>
            <value>
                The name of the table.
            </value>
        </member>
        <member name="P:Effort.DataLoaders.TableDescription.Schema">
            <summary>
                Gets the schema of the table.
            </summary>
            <value>
                The schema of the table.
            </value>
        </member>
        <member name="P:Effort.DataLoaders.TableDescription.Columns">
            <summary>
                Gets the columns of the table.
            </summary>
            <value>
                The columns of the table.
            </value>
        </member>
        <member name="T:Effort.DbConnectionFactory">
            <summary>
                Provides factory methods that are able to create <see cref="T:DbConnection"/> 
                objects that rely on in-process and in-memory databases. All of the data operations
                initiated from these connection objects are executed by the appropriate in-memory 
                database, so using these connection objects does not require any external 
                dependency outside of the scope of the application.
            </summary>
        </member>
        <member name="M:Effort.DbConnectionFactory.#cctor">
            <summary>
                Initializes static members of the <see cref="T:Effort.DbConnectionFactory"/> class.
            </summary>
        </member>
        <member name="M:Effort.DbConnectionFactory.CreatePersistent(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a <see cref="T:DbConnection"/> object that rely on an in-memory 
                database instance that lives during the complete application lifecycle. If the
                database is accessed the first time, then its state will be initialized by the
                provided <see cref="T:IDataLoader"/> object.
            </summary>
            <param name="instanceId">
                The identifier of the in-memory database.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:DbConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.DbConnectionFactory.CreatePersistent(System.String)">
            <summary>
                Creates a <see cref="T:DbConnection"/> object that rely on an in-memory 
                database instance that lives during the complete application lifecycle.
            </summary>
            <param name="instanceId">
                The identifier of the in-memory database.</param>
            <returns>
                The <see cref="T:DbConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.DbConnectionFactory.CreateTransient(Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a <see cref="T:DbConnection"/> object that rely on an in-memory 
                database instance that lives during the connection object lifecycle. If the 
                connection object is disposed or garbage collected, then underlying database 
                will be garbage collected too. The initial state of the database is initialized
                by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that initializes the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:DbConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.DbConnectionFactory.CreateTransient">
            <summary>
                Creates a <see cref="T:DbConnection"/> object that rely on an in-memory
                database instance that lives during the connection object lifecycle. If the 
                connection object is disposed or garbage collected, then underlying database 
                will be garbage collected too.
            </summary>
            <returns>
                The <see cref="T:DbConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.DbConnectionFactory.Create(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates an EffortConnection object with a connection string that represents the 
                specified parameter values.
            </summary>
            <param name="instanceId"> The instance id. </param>
            <param name="dataLoader"> The data loader. </param>
            <returns> The EffortConnection object. </returns>
        </member>
        <member name="T:Effort.Exceptions.EffortException">
            <summary>
                Represents errors that occur in the Effort library.
            </summary>
        </member>
        <member name="M:Effort.Exceptions.EffortException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Effort.Exceptions.EffortException"/> class.
            </summary>
        </member>
        <member name="M:Effort.Exceptions.EffortException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Effort.Exceptions.EffortException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="M:Effort.Exceptions.EffortException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Effort.Exceptions.EffortException"/> class.
            </summary>
            <param name="message">The message that describes the error.</param>
            <param name="inner">The inner exception.</param>
        </member>
        <member name="M:Effort.Exceptions.EffortException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Effort.Exceptions.EffortException"/> class.
            </summary>
            <param name="info">
                The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> 
                that holds the serialized object data about the exception being thrown.
            </param>
            <param name="context">
                The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that 
                contains contextual information about the source or destination.
            </param>
        </member>
        <member name="T:Effort.Internal.Caching.DataLoaderConfigurationLatchStore">
            <summary>
                Represents a cache that stores <see cref="T:DataLoaderConfigurationLatch"/> 
                objects.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.DataLoaderConfigurationLatchStore.store">
            <summary>
                Internal collection.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationLatchStore.#cctor">
            <summary>
                Initializes static members of the the 
                <see cref="T:Effort.Internal.Caching.DataLoaderConfigurationLatchStore"/> class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationLatchStore.GetLatch(Effort.Internal.Caching.DataLoaderConfigurationKey)">
            <summary>
                Return the latch associated to specified data loader configuration
            </summary>
            <param name="key"> Identifies the data loader configuration. </param>
            <returns> The configuration latch. </returns>
        </member>
        <member name="T:Effort.Internal.Caching.DataLoaderConfigurationLatch">
            <summary>
                Represents a latch that locks data loader configurations.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.DataLoaderConfigurationLatch.semaphore">
            <summary>
                The semaphore that is used for locking.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationLatch.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Caching.DataLoaderConfigurationLatch"/>
                class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationLatch.Acquire">
            <summary>
                Acquires the configuration latch.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationLatch.Release">
            <summary>
                Releases the configuration latch.
            </summary>
        </member>
        <member name="T:Effort.Internal.Caching.DataLoaderConfigurationKey">
            <summary>
                Represents a key that identifies a data loader configuration.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.DataLoaderConfigurationKey.type">
            <summary>
                The type of the data loader.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.DataLoaderConfigurationKey.argument">
            <summary>
                The argument of the data loader that describes its complete state.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationKey.#ctor(Effort.DataLoaders.IDataLoader)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Caching.DataLoaderConfigurationKey"/> 
                class.
            </summary>
            <param name="loader"> The data loader. </param>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationKey.Equals(Effort.Internal.Caching.DataLoaderConfigurationKey)">
            <summary>
                Determines whether the specified <see cref="T:Effort.Internal.Caching.DataLoaderConfigurationKey"/> is 
                equal to this instance.
            </summary>
            <param name="other">
                The <see cref="T:Effort.Internal.Caching.DataLoaderConfigurationKey"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:Effort.Internal.Caching.DataLoaderConfigurationKey"/> is equal
                to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationKey.Equals(System.Object)">
            <summary>
                Determines whether the specified <see cref="T:System.Object"/> is equal to this 
                instance.
            </summary>
            <param name="obj">
                The <see cref="T:System.Object"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this 
                instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DataLoaderConfigurationKey.GetHashCode">
            <summary>
                Returns a hash code for this instance.
            </summary>
            <returns>
                A hash code for this instance, suitable for use in hashing algorithms and data 
                structures like a hash table. 
            </returns>
        </member>
        <member name="T:Effort.Internal.Caching.MetadataWorkspaceStore">
            <summary>
                Represents a cache that stores <see cref="T:System.Data.Entity.Core.Metadata.Edm.MetadataWorkspace"/> object.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.MetadataWorkspaceStore.store">
            <summary>
                Internal collection.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.MetadataWorkspaceStore.#cctor">
            <summary>
                Initializes static members the <see cref="T:Effort.Internal.Caching.MetadataWorkspaceStore"/> class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.MetadataWorkspaceStore.GetMetadataWorkspace(System.String,System.Func{System.String,System.Data.Entity.Core.Metadata.Edm.MetadataWorkspace})">
            <summary>
                Returns a <see cref="T:System.Data.Entity.Core.Metadata.Edm.MetadataWorkspace"/> object that derived from the 
                specified metadata in order to be compatible with the Effort provider. If no 
                such element exist, the specified factory method is used to create one.
            </summary>
            <param name="metadata">
                References the metadata resource.
            </param>
            <param name="workspaceFactoryMethod">
                The factory method that instantiates the desired element.
            </param>
            <returns>
                The MetadataWorkspace object.
            </returns>
        </member>
        <member name="T:Effort.Internal.Caching.ObjectContextTypeKey">
            <summary>
                Represents a key that identifies dynamically created Effort-ready DbContext types.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.ObjectContextTypeKey.entityConnectionString">
            <summary>
                The entity connection string that identifies the database instance.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.ObjectContextTypeKey.effortConnectionString">
            <summary>
                The effort connection string that containes the database configuration.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.ObjectContextTypeKey.objectContextType">
            <summary>
            The base type of the ObjectContext.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.ObjectContextTypeKey.#ctor(System.String,System.String,System.Type)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Caching.ObjectContextTypeKey"/> class.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that identifies the database instance.
            </param>
            <param name="effortConnectionString">
                The effort connection string that containes the database configuration.
            </param>
            <param name="objectContextType">
                The base type of the ObjectContext.
            </param>
        </member>
        <member name="M:Effort.Internal.Caching.ObjectContextTypeKey.Equals(Effort.Internal.Caching.ObjectContextTypeKey)">
            <summary>
                Determines whether the specified <see cref="T:Effort.Internal.Caching.ObjectContextTypeKey"/> is equal 
                to this instance.
            </summary>
            <param name="other">
                The <see cref="T:Effort.Internal.Caching.ObjectContextTypeKey"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:Effort.Internal.Caching.ObjectContextTypeKey"/> is equal to 
                this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.ObjectContextTypeKey.GetHashCode">
            <summary>
                Returns a hash code for this instance.
            </summary>
            <returns>
                A hash code for this instance, suitable for use in hashing algorithms and data 
                structures like a hash table. 
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.ObjectContextTypeKey.Equals(System.Object)">
            <summary>
                Determines whether the specified <see cref="T:System.Object"/> is equal to this 
                instance.
            </summary>
            <param name="obj">
                The <see cref="T:System.Object"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this 
                instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Effort.Internal.Caching.ObjectContextTypeStore">
            <summary>
                Represents a cache that stores <see cref="T:System.Type"/> objects that serves as 
                Effort-ready ObjectContext.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.ObjectContextTypeStore.store">
            <summary>
                Internal collection.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.ObjectContextTypeStore.#cctor">
            <summary>
                Initializes static members of the <see cref="T:Effort.Internal.Caching.ObjectContextTypeStore"/> class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.ObjectContextTypeStore.GetObjectContextType(System.String,System.String,System.Type,System.Func{System.Type})">
            <summary>
                Returns a ObjectContext type the satisfies the provided requirements. If no
                such element exists the provided factory method is used to create one.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that identifies the database instance.
            </param>
            <param name="effortConnectionString">
                The effort connection string that containes the database configuration.
            </param>
            <param name="objectContextType">
                The base type that result type is derived from.
            </param>
            <param name="objectContextTypeFactoryMethod">
                The factory method that instatiates the desired ObjectContext type.
            </param>
            <returns></returns>
        </member>
        <member name="T:Effort.Internal.Caching.DbSchemaKey">
            <summary>
                Represents a key that identifies <see cref="T:DbSchema"/> objects.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.DbSchemaKey.innerKey">
            <summary>
                Serialized form the StoreItemCollection, used as the key.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaKey.#ctor(System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Caching.DbSchemaKey"/> class.
            </summary>
            <param name="storeItemCollection">
                The store item collection that the corresponding <see cref="T:DbSchema"/> is 
                based on.
            </param>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaKey.#ctor">
            <summary>
                Prevents a default instance of the <see cref="T:Effort.Internal.Caching.DbSchemaKey"/> class from being
                created.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaKey.FromString(System.String)">
            <summary>
                Creates a <see cref="T:Effort.Internal.Caching.DbSchemaKey"/> object based on the specified string.
            </summary>
            <param name="value"> The string. </param>
            <returns> The <see cref="T:Effort.Internal.Caching.DbSchemaKey"/> object. </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaKey.Equals(Effort.Internal.Caching.DbSchemaKey)">
            <summary>
                Determines whether the specified <see cref="T:Effort.Internal.Caching.DbSchemaKey"/> is equal to this 
                instance.
            </summary>
            <param name="other">
                The <see cref="T:Effort.Internal.Caching.DbSchemaKey"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this 
                instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaKey.Equals(System.Object)">
            <summary>
                Determines whether the specified <see cref="T:System.Object"/> is equal to this 
                instance.
            </summary>
            <param name="obj">
                The <see cref="T:System.Object"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this 
                instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaKey.GetHashCode">
            <summary>
                Returns a hash code for this instance.
            </summary>
            <returns>
                A hash code for this instance, suitable for use in hashing algorithms and data 
                structures like a hash table. 
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaKey.ToString">
            <summary>
                Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
                A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="T:Effort.Internal.Caching.CachingTableDataLoaderKey">
            <summary>
                Represents a key the identifies data that was loaded by a data loader component.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.CachingTableDataLoaderKey.loaderConfiguration">
            <summary>
                Identifies the data loader configuration
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.CachingTableDataLoaderKey.tableName">
            <summary>
                The name of the table.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.CachingTableDataLoaderKey.#ctor(Effort.Internal.Caching.DataLoaderConfigurationKey,System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Caching.CachingTableDataLoaderKey"/> 
                class.
            </summary>
            <param name="loaderConfiguration">
                Identifies the data loader configuration.
            </param>
            <param name="tableName">
                The name of the table.
            </param>
        </member>
        <member name="M:Effort.Internal.Caching.CachingTableDataLoaderKey.Equals(Effort.Internal.Caching.CachingTableDataLoaderKey)">
            <summary>
                Determines whether the specified <see cref="T:Effort.Internal.Caching.CachingTableDataLoaderKey"/> is 
                equal to this instance.
            </summary>
            <param name="other">
                The <see cref="T:Effort.Internal.Caching.CachingTableDataLoaderKey"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:Effort.Internal.Caching.CachingTableDataLoaderKey"/> is equal
                to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.CachingTableDataLoaderKey.Equals(System.Object)">
            <summary>
                Determines whether the specified <see cref="T:System.Object"/> is equal to this 
                instance.
            </summary>
            <param name="obj">
                The <see cref="T:System.Object"/> to compare with this instance.
            </param>
            <returns>
                <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this 
                instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.CachingTableDataLoaderKey.GetHashCode">
            <summary>
                Returns a hash code for this instance.
            </summary>
            <returns>
                A hash code for this instance, suitable for use in hashing algorithms and data 
                structures like a hash table. 
            </returns>
        </member>
        <member name="T:Effort.Internal.Caching.CachingTableDataLoaderStore">
            <summary>
                Represents a cache that stores <see cref="T:CachedDataLoaderData"/> objects.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.CachingTableDataLoaderStore.store">
            <summary>
                Internal collection.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.CachingTableDataLoaderStore.#cctor">
            <summary>
                Initializes static members of the the 
                <see cref="T:Effort.Internal.Caching.CachingTableDataLoaderStore"/> class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.CachingTableDataLoaderStore.GetCachedData(Effort.Internal.Caching.CachingTableDataLoaderKey,System.Func{Effort.DataLoaders.CachingTableDataLoader})">
            <summary>
                Returns a <see cref="T:CachingTableDataLoader"/> object that satisfies the 
                specified arguments. If no such element exists the provided factory method is
                used to create one.
            </summary>
            <param name="key">
                Identifies the caching data loader.
            </param>
            <param name="factoryMethod">
                The factory method that instatiates the desired 
                <see cref="T:CachingTableDataLoader"/> object.
            </param>
            <returns>
                The <see cref="T:CachingTableDataLoader"/> object.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.CachingTableDataLoaderStore.Contains(Effort.Internal.Caching.CachingTableDataLoaderKey)">
            <summary>
                Determines whether the store containes an element associated to the specified
                key.
            </summary>
            <param name="key"> The key. </param>
            <returns>
                <c>true</c> if the store contains the appropriate element otherwise, 
                <c>false</c>.
            </returns>
        </member>
        <member name="T:Effort.Internal.Caching.DbSchemaStore">
            <summary>
                Represents a cache that stores <see cref="T:Effort.Internal.DbManagement.Schema.DbSchema"/> objects.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.DbSchemaStore.store">
            <summary>
                Internal collection.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaStore.#cctor">
            <summary>
                Initializes static members of the <see cref="T:Effort.Internal.Caching.DbSchemaStore"/> class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaStore.GetDbSchema(Effort.Internal.Caching.DbSchemaKey)">
            <summary>
                Returns a <see cref="T:Effort.Internal.DbManagement.Schema.DbSchema"/> object that is associated to the specified
                DbSchemaKey.
            </summary>
            <param name="schemaKey"> The DbSchemaKey object. </param>
            <returns> The DbSchema object. </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DbSchemaStore.GetDbSchema(System.Data.Entity.Core.Metadata.Edm.StoreItemCollection,System.Func{System.Data.Entity.Core.Metadata.Edm.StoreItemCollection,Effort.Internal.DbManagement.Schema.DbSchema})">
            <summary>
                Returns a <see cref="T:Effort.Internal.DbManagement.Schema.DbSchema"/> object that represents the metadata contained
                by the specified StoreItemCollection. If no such element exist, the specified 
                factory method is used to create one.
            </summary>
            <param name="metadata">
                The StoreItemCollection object that contains the metadata.
            </param>
            <param name="schemaFactoryMethod">
                The factory method that instantiates the desired element.
            </param>
            <returns>
                The DbSchema object.
            </returns>
        </member>
        <member name="T:Effort.Internal.Common.EdmHelper">
            <summary>
                Providers helper method for EDM types.
            </summary>
        </member>
        <member name="M:Effort.Internal.Common.EdmHelper.GetFullTableName(System.Data.Entity.Core.Metadata.Edm.EntitySetBase)">
            <summary>
                Returns the full name of the table that is represented by the specified entity set.
            </summary>
            <param name="entitySet"> The entity set. </param>
            <returns >The full name of the table represented by the entity set. </returns>
        </member>
        <member name="M:Effort.Internal.Common.EdmHelper.GetSchema(System.Data.Entity.Core.Metadata.Edm.EntitySetBase)">
            <summary>
                Returns the schema of the table that is represented by the specified entity set.
            </summary>
            <param name="entitySet"> The entity set. </param>
            <returns >The schema of the table represented by the entity set. </returns>
        </member>
        <member name="M:Effort.Internal.Common.EdmHelper.GetTableName(System.Data.Entity.Core.Metadata.Edm.EntitySetBase)">
            <summary>
                Returns the name of the table that is represented by the specified entity set.
            </summary>
            <param name="entitySet"> The entity set. </param>
            <returns >The name of the table represented by the entity set. </returns>
        </member>
        <member name="M:Effort.Internal.Common.EdmHelper.GetColumnName(System.Data.Entity.Core.Metadata.Edm.EdmMember)">
            <summary>
                Returns the name of the table column that is represented by the specified
                member.
            </summary>
            <param name="member"> The member. </param>
            <returns> The name of the table column represented by the member. </returns>
        </member>
        <member name="T:Effort.Internal.CommandActions.ActionContext">
            <summary>
                Containes information about a command execution environment.
            </summary>
        </member>
        <member name="F:Effort.Internal.CommandActions.ActionContext.container">
            <summary>
                The database container that the command is executed on.
            </summary>
        </member>
        <member name="F:Effort.Internal.CommandActions.ActionContext.parameters">
            <summary>
                The parameters of the command action.
            </summary>
        </member>
        <member name="M:Effort.Internal.CommandActions.ActionContext.#ctor(Effort.Internal.DbManagement.DbContainer)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.CommandActions.ActionContext"/> class.
            </summary>
            <param name="container"> The container. </param>
        </member>
        <member name="P:Effort.Internal.CommandActions.ActionContext.DbContainer">
            <summary>
                Gets the database container that the command should be executed on.
            </summary>
            <value>
                The db container.
            </value>
        </member>
        <member name="P:Effort.Internal.CommandActions.ActionContext.Parameters">
            <summary>
                Gets the collection of the parameters of the command action.
            </summary>
            <value>
                The collection of the command action parameters.
            </value>
        </member>
        <member name="P:Effort.Internal.CommandActions.ActionContext.Transaction">
            <summary>
                Gets or sets the transaction that the command action is executed within.
            </summary>
            <value>
                The transaction.
            </value>
        </member>
        <member name="T:Effort.Internal.Csv.CsvReader">
            <summary>
                Represents a reader that provides fast, non-cached, forward-only access to CSV 
                data.  
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DefaultBufferSize">
            <summary>
                Defines the default buffer size.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DefaultDelimiter">
            <summary>
                Defines the default delimiter character separating each field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DefaultQuote">
            <summary>
                Defines the default quote character wrapping every field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DefaultEscape">
            <summary>
                Defines the default escape character letting insert quotation characters inside
                a quoted field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DefaultComment">
            <summary>
                Defines the default comment character indicating that a line is commented out.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.fieldHeaderComparer">
            <summary>
                Contains the field header comparer.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.reader">
            <summary>
                Contains the <see cref="T:TextReader"/> pointing to the CSV file.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.bufferSize">
            <summary>
                Contains the buffer size.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.comment">
            <summary>
                Contains the comment character indicating that a line is commented out.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.escape">
            <summary>
                Contains the escape character letting insert quotation characters inside a
                quoted field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.delimiter">
            <summary>
                Contains the delimiter character separating each field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.quote">
            <summary>
                Contains the quotation character wrapping every field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.trimmingOptions">
            <summary>
                Determines which values should be trimmed.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.hasHeaders">
            <summary>
                Indicates if field names are located on the first non commented line.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.defaultParseErrorAction">
            <summary>
                Contains the default action to take when a parsing error has occured.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.missingFieldAction">
            <summary>
                Contains the action to take when a field is missing.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.supportsMultiline">
            <summary>
                Indicates if the reader supports multiline.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.skipEmptyLines">
            <summary>
                Indicates if the reader will skip empty lines.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.initialized">
            <summary>
                Indicates if the class is initialized.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.fieldHeaders">
            <summary>
                Contains the field headers.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.fieldHeaderIndexes">
            <summary>
                Contains the dictionary of field indexes by header. The key is the field name
                and the value is its index.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.currentRecordIndex">
            <summary>
                Contains the current record index in the CSV file.
                A value of <see cref="M:Int32.MinValue"/> means that the reader has not been 
                initialized yet. 
                Otherwise, a negative value means that no record has been read yet.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.nextFieldStart">
            <summary>
                Contains the starting position of the next unread field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.nextFieldIndex">
            <summary>
                Contains the index of the next unread field.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.fields">
            <summary>
                Contains the array of the field values for the current record.
                A null value indicates that the field have not been parsed.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.fieldCount">
            <summary>
                Contains the maximum number of fields to retrieve for each record.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.buffer">
            <summary>
                Contains the read buffer.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.bufferLength">
            <summary>
                Contains the current read buffer length.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.eof">
            <summary>
                Indicates if the end of the reader has been reached.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.eol">
            <summary>
                Indicates if the last read operation reached an EOL character.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.firstRecordInCache">
            <summary>
                Indicates if the first record is in cache.
                This can happen when initializing a reader with no headers because one record
                must be read to get the field count automatically
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.missingFieldFlag">
            <summary>
                Indicates if one or more field are missing for the current record.
                Resets after each successful record read.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.parseErrorFlag">
            <summary>
                Indicates if a parse error occured for the current record.
                Resets after each successful record read.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.isDisposed">
            <summary>
                Contains the disposed status flag.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.latch">
            <summary>
                Contains the locking object for multi-threading purpose.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.#ctor(System.IO.TextReader,System.Boolean)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Csv.CsvReader"/> class.
            </summary>
            <param name="reader">
                A <see cref="T:TextReader"/> pointing to the CSV file.
            </param>
            <param name="hasHeaders">
                <see langword="true"/> if field names are located on the first non commented 
                line, otherwise, <see langword="false"/>.
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="reader"/> is a <see langword="null"/>.
            </exception>
            <exception cref="T:ArgumentException">
            	Cannot read from <paramref name="reader"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.#ctor(System.IO.TextReader,System.Boolean,System.Int32)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Csv.CsvReader"/> class.
            </summary>
            <param name="reader">
                A <see cref="T:TextReader"/> pointing to the CSV file.
            </param>
            <param name="hasHeaders">
                <see langword="true"/> if field names are located on the first non commented 
                line, otherwise, <see langword="false"/>.
            </param>
            <param name="bufferSize">
                The buffer size in bytes.
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="reader"/> is a <see langword="null"/>.
            </exception>
            <exception cref="T:ArgumentException">
            	Cannot read from <paramref name="reader"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.#ctor(System.IO.TextReader,System.Boolean,System.Char)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Csv.CsvReader"/> class.
            </summary>
            <param name="reader">
                A <see cref="T:TextReader"/> pointing to the CSV file.
            </param>
            <param name="hasHeaders">
                <see langword="true"/> if field names are located on the first non commented 
                line, otherwise, <see langword="false"/>.
            </param>
            <param name="delimiter">
                The delimiter character separating each field (default is ',').
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="reader"/> is a <see langword="null"/>.
            </exception>
            <exception cref="T:ArgumentException">
            	Cannot read from <paramref name="reader"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.#ctor(System.IO.TextReader,System.Boolean,System.Char,System.Int32)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Csv.CsvReader"/> class.
            </summary>
            <param name="reader">
                A <see cref="T:TextReader"/> pointing to the CSV file.
            </param>
            <param name="hasHeaders">
                <see langword="true"/> if field names are located on the first non commented
                line, otherwise, <see langword="false"/>.
            </param>
            <param name="delimiter">
                The delimiter character separating each field (default is ',').
            </param>
            <param name="bufferSize">
                The buffer size in bytes.
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="reader"/> is a <see langword="null"/>.
            </exception>
            <exception cref="T:ArgumentException">
            	Cannot read from <paramref name="reader"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.#ctor(System.IO.TextReader,System.Boolean,System.Char,System.Char,System.Char,System.Char,Effort.Internal.Csv.ValueTrimmingOptions)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Csv.CsvReader"/> class.
            </summary>
            <param name="reader">
                A <see cref="T:TextReader"/> pointing to the CSV file.
            </param>
            <param name="hasHeaders">
                <see langword="true"/> if field names are located on the first non commented 
                line, otherwise, <see langword="false"/>.
            </param>
            <param name="delimiter">
                The delimiter character separating each field (default is ',').
            </param>
            <param name="quote">
                The quotation character wrapping every field (default is ''').
            </param>
            <param name="escape">
                The escape character letting insert quotation characters inside a quoted field 
                (default is '\').
                If no escape character, set to '\0' to gain some performance.
            </param>
            <param name="comment">
                The comment character indicating that a line is commented out (default is '#').
            </param>
            <param name="trimmingOptions">
                Determines which values should be trimmed.
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="reader"/> is a <see langword="null"/>.
            </exception>
            <exception cref="T:ArgumentException">
            	Cannot read from <paramref name="reader"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.#ctor(System.IO.TextReader,System.Boolean,System.Char,System.Char,System.Char,System.Char,Effort.Internal.Csv.ValueTrimmingOptions,System.Int32)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Csv.CsvReader"/> class.
            </summary>
            <param name="reader">
                A <see cref="T:TextReader"/> pointing to the CSV file.
            </param>
            <param name="hasHeaders">
                <see langword="true"/> if field names are located on the first non commented 
                line, otherwise, <see langword="false"/>.
            </param>
            <param name="delimiter">
                The delimiter character separating each field (default is ',').
            </param>
            <param name="quote">
                The quotation character wrapping every field (default is ''').
            </param>
            <param name="escape">
                The escape character letting insert quotation characters inside a quoted field 
                (default is '\').
                If no escape character, set to '\0' to gain some performance.
            </param>
            <param name="comment">
                The comment character indicating that a line is commented out (default is '#').
            </param>
            <param name="trimmingOptions">
                Determines which values should be trimmed.
            </param>
            <param name="bufferSize">
                The buffer size in bytes.
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="reader"/> is a <see langword="null"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="bufferSize"/> must be 1 or more.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.OnParseError(Effort.Internal.Csv.ParseErrorEventArgs)">
            <summary>
                Raises the <see cref="M:ParseError"/> event.
            </summary>
            <param name="e">
                The <see cref="T:Effort.Internal.Csv.ParseErrorEventArgs"/> that contains the event data.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.GetFieldHeaders">
            <summary>
                Gets the field headers.
            </summary>
            <returns>
                The field headers or an empty array if headers are not supported.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.EnsureInitialize">
            <summary>
                Ensures that the reader is initialized.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.GetFieldIndex(System.String)">
            <summary>
                Gets the field index for the provided header.
            </summary>
            <param name="header">
                The header to look for.
            </param>
            <returns>
                The field index for the provided header. -1 if not found.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.CopyCurrentRecordTo(System.String[],System.Int32)">
            <summary>
                Copies the field array of the current record to a one-dimensional array,
                starting at the beginning of the target array.
            </summary>
            <param name="array"> 
                The one-dimensional <see cref="T:Array"/> that is the destination of the fields 
                of the current record.
            </param>
            <param name="index">
                The zero-based index in <paramref name="array"/> at which copying begins.
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="array"/> is <see langword="null"/>.
            </exception>
            <exception cref="T:ArgumentOutOfRangeException">
            	<paramref name="index"/> is les than zero or is equal to or greater than the 
            	length <paramref name="array"/>. 
            </exception>
            <exception cref="T:System.InvalidOperationException">
                No current record.
            </exception>
            <exception cref="T:System.ArgumentException">
            	The number of fields in the record is greater than the available space from 
            	<paramref name="index"/> to the end of <paramref name="array"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.GetCurrentRawData">
            <summary>
                Gets the current raw CSV data.
            </summary>
            <remarks> Used for exception handling purpose. </remarks>
            <returns> The current raw CSV data. </returns>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.IsWhiteSpace(System.Char)">
            <summary>
                Indicates whether the specified Unicode character is categorized as white 
                space.
            </summary>
            <param name="c">
                A Unicode character.
            </param>
            <returns>
                <see langword="true"/> if <paramref name="c"/> is white space; otherwise, 
                <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.MoveTo(System.Int64)">
            <summary>
                Moves to the specified record index.
            </summary>
            <param name="record">
                The record index.
            </param>
            <returns>
                <c>true</c> if the operation was successful; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.ParseNewLine(System.Int32@)">
            <summary>
                Parses a new line delimiter.
            </summary>
            <param name="pos">
                The starting position of the parsing. Will contain the resulting end position.
            </param>
            <returns>
                <see langword="true"/> if a new line delimiter was found; otherwise, 
                <see langword="false"/>.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.IsNewLine(System.Int32)">
            <summary>
                Determines whether the character at the specified position is a new line
                delimiter.
            </summary>
            <param name="pos">
                The position of the character to verify.
            </param>
            <returns>
            	<see langword="true"/> if the character at the specified position is a new line
            	delimiter; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.ReadBuffer">
            <summary>
                Fills the buffer with data from the reader.
            </summary>
            <returns>
                <see langword="true"/> if data was successfully read; otherwise, 
                <see langword="false"/>.</returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.ReadField(System.Int32,System.Boolean,System.Boolean)">
            <summary>
                Reads the field at the specified index.
                Any unread fields with an inferior index will also be read as part of the
                required parsing.
            </summary>
            <param name="field">
                The field index.
            </param>
            <param name="initializing">
                Indicates if the reader is currently initializing.
            </param>
            <param name="discardValue">
                Indicates if the value(s) are discarded.
            </param>
            <returns>
                The field at the specified index. 
                A <see langword="null"/> indicates that an error occured or that the last field
                has been reached during initialization.
            </returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="field"/> is out of range.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            	There is no current record.
            </exception>
            <exception cref="T:Effort.Internal.Csv.MissingFieldCsvException">
            	The CSV data appears to be missing a field.
            </exception>
            <exception cref="T:Effort.Internal.Csv.MalformedCsvException">
            	The CSV data appears to be malformed.
            </exception>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.ReadNextRecord">
            <summary>
                Reads the next record.
            </summary>
            <returns>
                <see langword="true"/> if a record has been successfully reads; otherwise, 
                <see langword="false"/>.</returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.ReadNextRecord(System.Boolean,System.Boolean)">
            <summary>
                Reads the next record.
            </summary>
            <param name="onlyReadHeaders">
                Indicates if the reader will proceed to the next record after having read 
                headers.
                <see langword="true"/> if it stops after having read headers; otherwise, 
                <see langword="false"/>.
            </param>
            <param name="skipToNextLine">
                Indicates if the reader will skip directly to the next line without parsing the 
                current one. 
                To be used when an error occurs.
            </param>
            <returns>
                <see langword="true"/> if a record has been successfully reads; otherwise, 
                <see langword="false"/>.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.SkipEmptyAndCommentedLines(System.Int32@)">
            <summary>
                Skips empty and commented lines.
                If the end of the buffer is reached, its content be discarded and filled again
                from the reader.
            </summary>
            <param name="pos">
                The position in the buffer where to start parsing. 
                Will contains the resulting position after the operation.
            </param>
            <returns>
                <see langword="true"/> if the end of the reader has not been reached; 
                otherwise, <see langword="false"/>.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.DoSkipEmptyAndCommentedLines(System.Int32@)">
            <summary>
                <para>Worker method.</para>
                <para>Skips empty and commented lines.</para>
            </summary>
            <param name="pos">
                The position in the buffer where to start parsing. 
                Will contains the resulting position after the operation.
            </param>
                <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.SkipWhiteSpaces(System.Int32@)">
            <summary>
                Skips whitespace characters.
            </summary>
            <param name="pos">
                The starting position of the parsing. Will contain the resulting end position.
            </param>
            <returns>
                <see langword="true"/> if the end of the reader has not been reached; 
                otherwise, <see langword="false"/>.</returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.SkipToNextLine(System.Int32@)">
            <summary>
                Skips ahead to the next NewLine character.
                If the end of the buffer is reached, its content be discarded and filled again 
                from the reader.
            </summary>
            <param name="pos">
                The position in the buffer where to start parsing. 
                Will contains the resulting position after the operation.
            </param>
            <returns>
                <see langword="true"/> if the end of the reader has not been reached; 
                otherwise, <see langword="false"/>.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.HandleParseError(Effort.Internal.Csv.MalformedCsvException,System.Int32@)">
            <summary>
                Handles a parsing error.
            </summary>
            <param name="error">
                The parsing error that occured.
            </param>
            <param name="pos">
                The current position in the buffer.
            </param>
            <exception cref="T:System.ArgumentNullException">
                <paramref name="error"/> is <see langword="null"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.HandleMissingField(Effort.Internal.Csv.FieldValue,System.Int32,System.Int32@)">
            <summary>
                Handles a missing field error.
            </summary>
            <param name="value">
                The partially parsed value, if available.
            </param>
            <param name="fieldIndex">
                The missing field index.
            </param>
            <param name="currentPosition">
                The current position in the raw data.
            </param>
            <returns>
                The resulting value according to <see cref="M:MissingFieldAction"/>.
                If the action is set to <see cref="T:MissingFieldAction.TreatAsParseError"/>,
                then the parse error will be handled according to 
                <see cref="P:Effort.Internal.Csv.CsvReader.DefaultParseErrorAction"/>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.ValidateDataReader(Effort.Internal.Csv.CsvReader.DataReaderValidations)">
            <summary>
                Validates the state of the data reader.
            </summary>
            <param name="validations">
                The validations to accomplish.
            </param>
            <exception cref="T:System.InvalidOperationException">
                No current record.
            </exception>
            <exception cref="T:System.InvalidOperationException">
                This operation is invalid when the reader is closed.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.CopyFieldToArray(System.Int32,System.Int64,System.Array,System.Int32,System.Int32)">
            <summary>
                Copy the value of the specified field to an array.
            </summary>
            <param name="field">
                The index of the field.
            </param>
            <param name="fieldOffset">
                The offset in the field value.
            </param>
            <param name="destinationArray">
                The destination array where the field value will be copied.
            </param>
            <param name="destinationOffset">
                The destination array offset.
            </param>
            <param name="length">
                The number of characters to copy from the field value.
            </param>
            <returns>
                The length.
            </returns>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.GetEnumerator">
            <summary>
                Returns an <see cref="T:RecordEnumerator"/>  that can iterate through CSV 
                records.
            </summary>
            <returns>
                An <see cref="T:RecordEnumerator"/>  that can iterate through CSV records.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.System#Collections#Generic#IEnumerable{System#String[]}#GetEnumerator">
            <summary>
                Returns an <see cref="T:System.Collections.Generics.IEnumerator"/>  that can 
                iterate through CSV records.
            </summary>
            <returns>
                An <see cref="T:System.Collections.Generics.IEnumerator"/>  that can iterate 
                through CSV records.
            </returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an <see cref="T:System.Collections.IEnumerator"/>  that can iterate through CSV records.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator"/>  that can iterate through CSV records.</returns>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
            The instance has been disposed of.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.CheckDisposed">
            <summary>
                Checks if the instance has been disposed of, and if it has, throws an 
                <see cref="T:System.ComponentModel.ObjectDisposedException"/>; otherwise, does
                nothing.
            </summary>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
            	The instance has been disposed of.
            </exception>
            <remarks>
            	Derived classes should call this method at the start of all methods and 
            	properties that should not be accessed after a call to 
            	<see cref="M:Dispose()"/>.
            </remarks>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.Dispose">
            <summary>
                Releases all resources used by the instance.
            </summary>
            <remarks>
            	Calls <see cref="M:Dispose(Boolean)"/> with the disposing parameter set to 
            	<see langword="true"/> to free unmanaged and managed resources.
            </remarks>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.Dispose(System.Boolean)">
            <summary>
                Releases the unmanaged resources used by this instance and optionally releases
                the managed resources.
            </summary>
            <param name="disposing">
            	<see langword="true"/> to release both managed and unmanaged resources; 
            	<see langword="false"/> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.Finalize">
            <summary>
            Releases unmanaged resources and performs other cleanup operations before the 
            instance is reclaimed by garbage collection.
            </summary>
        </member>
        <member name="E:Effort.Internal.Csv.CsvReader.ParseError">
            <summary>
                Occurs when there is an error while parsing the CSV stream.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Comment">
            <summary>
                Gets the comment character indicating that a line is commented out.
            </summary>
            <value> The comment character indicating that a line is commented out. </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Escape">
            <summary>
                Gets the escape character letting insert quotation characters inside a quoted
                field.
            </summary>
            <value>
                The escape character letting insert quotation characters inside a quoted field.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Delimiter">
            <summary>
                Gets the delimiter character separating each field.
            </summary>
            <value>
                The delimiter character separating each field.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Quote">
            <summary>
                Gets the quotation character wrapping every field.
            </summary>
            <value>
                The quotation character wrapping every field.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.HasHeaders">
            <summary>
                Indicates if field names are located on the first non commented line.
            </summary>
            <value>
                <see langword="true"/> if field names are located on the first non commented
                line, otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.TrimmingOption">
            <summary>
                Indicates if spaces at the start and end of a field are trimmed.
            </summary>
            <value>
                <see langword="true"/> if spaces at the start and end of a field are trimmed, 
                otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.BufferSize">
            <summary>
                Gets the buffer size.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.DefaultParseErrorAction">
            <summary>
                Gets or sets the default action to take when a parsing error has occured.
            </summary>
            <value>
                The default action to take when a parsing error has occured.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.MissingFieldAction">
            <summary>
                Gets or sets the action to take when a field is missing.
            </summary>
            <value>
                The action to take when a field is missing.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.SupportsMultiline">
            <summary>
            Gets or sets a value indicating if the reader supports multiline fields.
            </summary>
            <value>
            A value indicating if the reader supports multiline field.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.SkipEmptyLines">
            <summary>
                Gets or sets a value indicating if the reader will skip empty lines.
            </summary>
            <value>
                A value indicating if the reader will skip empty lines.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.DefaultHeaderName">
            <summary>
                Gets or sets the default header name when it is an empty string or only 
                whitespaces.
                The header index will be appended to the specified name.
            </summary>
            <value>
                The default header name when it is an empty string or only whitespaces.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.FieldCount">
            <summary>
                Gets the maximum number of fields to retrieve for each record.
            </summary>
            <value>
                The maximum number of fields to retrieve for each record.
            </value>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.EndOfStream">
            <summary>
                Gets a value that indicates whether the current stream position is at the end
                of the stream.
            </summary>
            <value>
                <see langword="true"/> if the current stream position is at the end of the
                stream; otherwise <see langword="false"/>.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.CurrentRecordIndex">
            <summary>
                Gets the current record index in the CSV file.
            </summary>
            <value>
                The current record index in the CSV file.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.MissingFieldFlag">
            <summary>
                Indicates if one or more field are missing for the current record.
                Resets after each successful record read.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.ParseErrorFlag">
            <summary>
                Indicates if a parse error occured for the current record.
                Resets after each successful record read.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Item(System.Int32,System.String)">
            <summary>
                Gets the field with the specified name and record position. 
            <see cref="M:hasHeaders"/> must be <see langword="true"/>.
            </summary>
            <value>
                The field with the specified name and record position.
            </value>
            <exception cref="T:ArgumentNullException">
            	<paramref name="field"/> is <see langword="null"/> or an empty string.
            </exception>
            <exception cref="T:InvalidOperationException">
                The CSV does not have headers (<see cref="M:HasHeaders"/> property is 
                <see langword="false"/>).
            </exception>
            <exception cref="T:ArgumentException">
            	<paramref name="field"/> not found.
            </exception>
            <exception cref="T:ArgumentOutOfRangeException">
            	Record index must be > 0.
            </exception>
            <exception cref="T:InvalidOperationException">
            	Cannot move to a previous record in forward-only mode.
            </exception>
            <exception cref="T:EndOfStreamException">
            	Cannot read record at <paramref name="record"/>.
            </exception>
            <exception cref="T:MalformedCsvException">
            	The CSV appears to be corrupt at the current position.
            </exception>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Item(System.Int32,System.Int32)">
            <summary>
                Gets the field at the specified index and record position.
            </summary>
            <value>
                The field at the specified index and record position.
                A <see langword="null"/> is returned if the field cannot be found for the 
                record.
            </value>
            <exception cref="T:ArgumentOutOfRangeException">
            	<paramref name="field"/> must be included in [0, <see cref="M:FieldCount"/>[.
            </exception>
            <exception cref="T:ArgumentOutOfRangeException">
            	Record index must be > 0.
            </exception>
            <exception cref="T:InvalidOperationException">
            	Cannot move to a previous record in forward-only mode.
            </exception>
            <exception cref="T:EndOfStreamException">
            	Cannot read record at <paramref name="record"/>.
            </exception>
            <exception cref="T:MalformedCsvException">
            	The CSV appears to be corrupt at the current position.
            </exception>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Item(System.String)">
            <summary>
                Gets the field with the specified name. <see cref="M:hasHeaders"/> must be 
                <see langword="true"/>.
            </summary>
            <value>
                The field with the specified name.
            </value>
            <exception cref="T:ArgumentNullException">
            	<paramref name="field"/> is <see langword="null"/> or an empty string.
            </exception>
            <exception cref="T:InvalidOperationException">
                The CSV does not have headers (<see cref="M:HasHeaders"/> property is 
                <see langword="false"/>).
            </exception>
            <exception cref="T:ArgumentException">
            	<paramref name="field"/> not found.
            </exception>
            <exception cref="T:MalformedCsvException">
            	The CSV appears to be corrupt at the current position.
            </exception>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.Item(System.Int32)">
            <summary>
                Gets the field at the specified index.
            </summary>
            <value>
                The field at the specified index.
            </value>
            <exception cref="T:ArgumentOutOfRangeException">
            	<paramref name="field"/> must be included in [0, <see cref="M:FieldCount"/>[.
            </exception>
            <exception cref="T:InvalidOperationException">
            	No record read yet. Call ReadLine() first.
            </exception>
            <exception cref="T:MalformedCsvException">
            	The CSV appears to be corrupt at the current position.
            </exception>
            <exception cref="T:System.ComponentModel.ObjectDisposedException">
                The instance has been disposed of.
            </exception>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.IsDisposed">
            <summary>
                Gets a value indicating whether the instance has been disposed of.
            </summary>
            <value>
            	<see langword="true"/> if the instance has been disposed of; otherwise, 
            	<see langword="false"/>.
            </value>
        </member>
        <member name="T:Effort.Internal.Csv.CsvReader.DataReaderValidations">
            <summary>
                Defines the data reader validations.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DataReaderValidations.None">
            <summary>
                No validation.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DataReaderValidations.IsInitialized">
            <summary>
                Validate that the data reader is initialized.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.DataReaderValidations.IsNotClosed">
            <summary>
                Validate that the data reader is not closed.
            </summary>
        </member>
        <member name="T:Effort.Internal.Csv.CsvReader.RecordEnumerator">
            <summary>
                Supports a simple iteration over the records of a <see cref="T:CsvReader"/>.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.RecordEnumerator.reader">
            <summary>
                Contains the enumerated <see cref="T:CsvReader"/>.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.RecordEnumerator.current">
            <summary>
                Contains the current record.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.CsvReader.RecordEnumerator.currentRecordIndex">
            <summary>
                Contains the current record index.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.RecordEnumerator.#ctor(Effort.Internal.Csv.CsvReader)">
            <summary>
                Initializes a new instance of the <see cref="T:RecordEnumerator"/> class.
            </summary>
            <param name="reader">
                The <see cref="T:CsvReader"/> to iterate over.
            </param>
            <exception cref="T:ArgumentNullException">
            	<paramref name="reader"/> is a <see langword="null"/>.
            </exception>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.RecordEnumerator.MoveNext">
            <summary>
                Advances the enumerator to the next record of the CSV.
            </summary>
            <returns>
                <see langword="true"/> if the enumerator was successfully advanced to the 
                next record, <see langword="false"/> if the enumerator has passed the end
                of the CSV.
            </returns>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.RecordEnumerator.Reset">
            <summary>
                Sets the enumerator to its initial position, which is before the first 
                record in the CSV.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.CsvReader.RecordEnumerator.Dispose">
            <summary>
                Performs application-defined tasks associated with freeing, releasing, or 
                resetting unmanaged resources.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.RecordEnumerator.Current">
            <summary>
                Gets the current record.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.CsvReader.RecordEnumerator.System#Collections#IEnumerator#Current">
            <summary>
                Gets the current record.
            </summary>
        </member>
        <member name="T:Effort.Internal.Csv.ExceptionMessages">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.BufferSizeTooSmall">
            <summary>
              Looks up a localized string similar to Buffer size must be 1 or more..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.CannotMovePreviousRecordInForwardOnly">
            <summary>
              Looks up a localized string similar to Cannot move to a previous record in forward-only mode..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.CannotReadRecordAtIndex">
            <summary>
              Looks up a localized string similar to Cannot read record at index &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.EnumerationFinishedOrNotStarted">
            <summary>
              Looks up a localized string similar to Enumeration has either not started or has already finished..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.EnumerationVersionCheckFailed">
            <summary>
              Looks up a localized string similar to Collection was modified; enumeration operation may not execute..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.FieldHeaderNotFound">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; field header not found..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.FieldIndexOutOfRange">
            <summary>
              Looks up a localized string similar to Field index must be included in [0, FieldCount[. Specified field index was : &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.MalformedCsvException">
            <summary>
              Looks up a localized string similar to The CSV appears to be corrupt near record &apos;{0}&apos; field &apos;{1} at position &apos;{2}&apos;. Current raw data : &apos;{3}&apos;..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.MissingFieldActionNotSupported">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; is not a supported missing field action..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.NoCurrentRecord">
            <summary>
              Looks up a localized string similar to No current record..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.NoHeaders">
            <summary>
              Looks up a localized string similar to The CSV does not have headers (CsvReader.HasHeaders property is false)..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.NotEnoughSpaceInArray">
            <summary>
              Looks up a localized string similar to The number of fields in the record is greater than the available space from index to the end of the destination array..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.ParseErrorActionInvalidInsideParseErrorEvent">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; is not a valid ParseErrorAction while inside a ParseError event..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.ParseErrorActionNotSupported">
            <summary>
              Looks up a localized string similar to &apos;{0}&apos; is not a supported ParseErrorAction..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.ReaderClosed">
            <summary>
              Looks up a localized string similar to This operation is invalid when the reader is closed..
            </summary>
        </member>
        <member name="P:Effort.Internal.Csv.ExceptionMessages.RecordIndexLessThanZero">
            <summary>
              Looks up a localized string similar to Record index must be 0 or more..
            </summary>
        </member>
        <member name="T:Effort.Internal.Csv.FieldValue">
            <summary>
                Represent a parsed field value.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.FieldValue.hasValue">
            <summary>
                Indicates if the field has value.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.FieldValue.value">
            <summary>
                The value of the field.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.FieldValue.#ctor(System.String,System.Boolean)">
            <summary>
                Prevents a default instance of the <see cref="T:Effort.Internal.Csv.FieldValue"/> struct from being 
                created.
            </summary>
            <param name="value"> The field if not missing. </param>
            <param name="hasValue"> if set to <c>true</c> the field has value. </param>
        </member>
        <member name="F:Effort.Internal.Csv.FieldValue.Missing">
            <summary>
                Represents a missing value.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.FieldValue.op_Implicit(System.String)~Effort.Internal.Csv.FieldValue">
            <summary>
                Implicit conversion from <see cref="T:System.String"/> to 
                <see cref="T:Effort.Internal.Csv.FieldValue"/>.
            </summary>
            <param name="value"> The <see cref="T:System.String"/> value. </param>
            <returns> The <see cref="T:Effort.Internal.Csv.FieldValue"/> value. </returns>
        </member>
        <member name="M:Effort.Internal.Csv.FieldValue.op_Addition(Effort.Internal.Csv.FieldValue,System.String)">
            <summary>
                Concats a <see cref="T:Effort.Internal.Csv.FieldValue"/> value with a <see cref="T:System.String"/>
                value.
            </summary>
            <param name="left"> The <see cref="T:Effort.Internal.Csv.FieldValue"/> value. </param>
            <param name="right"> The <see cref="T:System.String"/> value. </param>
            <returns> The result of the concatenation. </returns>
        </member>
        <member name="M:Effort.Internal.Csv.FieldValue.op_Addition(System.String,Effort.Internal.Csv.FieldValue)">
            <summary>
                Concats a <see cref="T:System.String"/> value with a <see cref="T:Effort.Internal.Csv.FieldValue"/>
                value.
            </summary>
            <param name="left"> The <see cref="T:System.String"/> value. </param>
            <param name="right"> The <see cref="T:Effort.Internal.Csv.FieldValue"/> value. </param>
            <returns> The result of the concatenation. </returns>
        </member>
        <member name="M:Effort.Internal.Csv.FieldValue.ToString">
            <summary>
                Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
                A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:Effort.Internal.Csv.FieldValue.GetHashCode">
            <summary>
                Returns a hash code for this instance.
            </summary>
            <returns>
                A hash code for this instance, suitable for use in hashing algorithms and data 
                structures like a hash table. 
            </returns>
        </member>
        <member name="P:Effort.Internal.Csv.FieldValue.IsMissing">
            <summary>
                Gets a value indicating whether the field value is missing
            </summary>
            <value>
                <c>true</c> if the value is missing; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Effort.Internal.Csv.FieldValue.Value">
            <summary>
                Gets the field value.
            </summary>
            <value>
                The field value.
            </value>
            <exception cref="T:System.InvalidOperationException">
                The field value is missing.
            </exception>
        </member>
        <member name="T:Effort.Internal.Csv.ParseErrorEventArgs">
            <summary>
                Provides data for the <see cref="M:CsvReader.ParseError"/> event.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.ParseErrorEventArgs.error">
            <summary>
                Contains the error that occured.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.ParseErrorEventArgs.action">
            <summary>
                Contains the action to take.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.ParseErrorEventArgs.#ctor(Effort.Internal.Csv.MalformedCsvException,Effort.Internal.Csv.ParseErrorAction)">
            <summary>
                Initializes a new instance of the ParseErrorEventArgs class.
            </summary>
            <param name="error"> The error that occured. </param>
            <param name="defaultAction"> The default action to take. </param>
        </member>
        <member name="P:Effort.Internal.Csv.ParseErrorEventArgs.Error">
            <summary>
                Gets the error that occured.
            </summary>
            <value> The error that occured. </value>
        </member>
        <member name="P:Effort.Internal.Csv.ParseErrorEventArgs.Action">
            <summary>
                Gets or sets the action to take.
            </summary>
            <value> The action to take. </value>
        </member>
        <member name="T:Effort.Internal.Csv.MalformedCsvException">
            <summary>
                Represents the exception that is thrown when a CSV file is malformed.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MalformedCsvException.message">
            <summary>
                Contains the message that describes the error.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MalformedCsvException.rawData">
            <summary>
                Contains the raw data when the error occured.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MalformedCsvException.currentFieldIndex">
            <summary>
                Contains the current field index.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MalformedCsvException.currentRecordIndex">
            <summary>
                Contains the current record index.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MalformedCsvException.currentPosition">
            <summary>
                Contains the current position in the raw data.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.MalformedCsvException.#ctor">
            <summary>
                Initializes a new instance of the MalformedCsvException class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.MalformedCsvException.#ctor(System.String)">
            <summary>
                Initializes a new instance of the MalformedCsvException class.
            </summary>
            <param name="message">
                The message that describes the error.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MalformedCsvException.#ctor(System.String,System.Exception)">
            <summary>
                Initializes a new instance of the MalformedCsvException class.
            </summary>
            <param name="message">
                The message that describes the error.
            </param>
            <param name="innerException">
                The exception that is the cause of the current exception.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MalformedCsvException.#ctor(System.String,System.Int32,System.Int64,System.Int32)">
            <summary>
                Initializes a new instance of the MalformedCsvException class.
            </summary>
            <param name="rawData">
                The raw data when the error occured.
            </param>
            <param name="currentPosition">
                The current position in the raw data.
            </param>
            <param name="currentRecordIndex">
                The current record index.
            </param>
            <param name="currentFieldIndex">
                The current field index.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MalformedCsvException.#ctor(System.String,System.Int32,System.Int64,System.Int32,System.Exception)">
            <summary>
                Initializes a new instance of the MalformedCsvException class.
            </summary>
            <param name="rawData">
                The raw data when the error occured.
            </param>
            <param name="currentPosition">
                The current position in the raw data.
            </param>
            <param name="currentRecordIndex">
                The current record index.
            </param>
            <param name="currentFieldIndex">
                The current field index.
            </param>
            <param name="innerException">
                The exception that is the cause of the current exception.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MalformedCsvException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                Initializes a new instance of the MalformedCsvException class with serialized 
                data.
            </summary>
            <param name="info">
                The <see cref="T:SerializationInfo"/> that holds the serialized object data
                about the exception being thrown.
            </param>
            <param name="context">
                The <see cref="T:StreamingContext"/> that contains contextual information about
                the source or destination.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MalformedCsvException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                When overridden in a derived class, sets the <see cref="T:SerializationInfo"/>
                with information about the exception.
            </summary>
            <param name="info">
                The <see cref="T:SerializationInfo"/> that holds the serialized object data
                about the exception being thrown.
            </param>
            <param name="context">
                The <see cref="T:StreamingContext"/> that contains contextual information about
                the source or destination.
            </param>
        </member>
        <member name="P:Effort.Internal.Csv.MalformedCsvException.RawData">
            <summary>
                Gets the raw data when the error occured.
            </summary>
            <value> The raw data when the error occured. </value>
        </member>
        <member name="P:Effort.Internal.Csv.MalformedCsvException.CurrentPosition">
            <summary>
                Gets the current position in the raw data.
            </summary>
            <value> The current position in the raw data. </value>
        </member>
        <member name="P:Effort.Internal.Csv.MalformedCsvException.CurrentRecordIndex">
            <summary>
                Gets the current record index.
            </summary>
            <value> The current record index. </value>
        </member>
        <member name="P:Effort.Internal.Csv.MalformedCsvException.CurrentFieldIndex">
            <summary>
                Gets the current field index.
            </summary>
            <value> The current record index. </value>
        </member>
        <member name="P:Effort.Internal.Csv.MalformedCsvException.Message">
            <summary>
                Gets a message that describes the current exception.
            </summary>
            <value> A message that describes the current exception. </value>
        </member>
        <member name="T:Effort.Internal.Csv.MissingFieldCsvException">
            <summary>
                Represents the exception that is thrown when a there is a missing field in a record
                of the CSV file.
            </summary>
            <remarks>
                MissingFieldException would have been a better name, but there is already a 
                <see cref="T:System.MissingFieldException"/>.
            </remarks>
        </member>
        <member name="M:Effort.Internal.Csv.MissingFieldCsvException.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Csv.MissingFieldCsvException"/> 
                class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Csv.MissingFieldCsvException.#ctor(System.String)">
            <summary>
                Initializes a new instance of the MissingFieldCsvException class.
            </summary>
            <param name="message">
                The message that describes the error.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MissingFieldCsvException.#ctor(System.String,System.Exception)">
            <summary>
                Initializes a new instance of the MissingFieldCsvException class.
            </summary>
            <param name="message">
                The message that describes the error.
            </param>
            <param name="innerException">
                The exception that is the cause of the current exception.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MissingFieldCsvException.#ctor(System.String,System.Int32,System.Int64,System.Int32)">
            <summary>
                Initializes a new instance of the MissingFieldCsvException class.
            </summary>
            <param name="rawData">
                The raw data when the error occured.
            </param>
            <param name="currentPosition">
                The current position in the raw data.
            </param>
            <param name="currentRecordIndex">
                The current record index.
            </param>
            <param name="currentFieldIndex">
                The current field index.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MissingFieldCsvException.#ctor(System.String,System.Int32,System.Int64,System.Int32,System.Exception)">
            <summary>
                Initializes a new instance of the MissingFieldCsvException class.
            </summary>
            <param name="rawData">
                The raw data when the error occured.
            </param>
            <param name="currentPosition">
                The current position in the raw data.
            </param>
            <param name="currentRecordIndex">
                The current record index.
            </param>
            <param name="currentFieldIndex">
                The current field index.
            </param>
            <param name="innerException">
                The exception that is the cause of the current exception.
            </param>
        </member>
        <member name="M:Effort.Internal.Csv.MissingFieldCsvException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                Initializes a new instance of the MissingFieldCsvException class with
                serialized data.
            </summary>
            <param name="info">
                The <see cref="T:SerializationInfo"/> that holds the serialized object data
                about the exception being thrown.
            </param>
            <param name="context">
                The <see cref="T:StreamingContext"/> that contains contextual information about
                the source or destination.
            </param>
        </member>
        <member name="T:Effort.Internal.Csv.MissingFieldAction">
            <summary>
                Specifies the action to take when a field is missing.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MissingFieldAction.ParseError">
            <summary>
                Treat as a parsing error.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MissingFieldAction.ReplaceByEmpty">
            <summary>
                Replaces by an empty value.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.MissingFieldAction.ReplaceByNull">
            <summary>
                Replaces by a null value (<see langword="null"/>).
            </summary>
        </member>
        <member name="T:Effort.Internal.Csv.ParseErrorAction">
            <summary>
                Specifies the action to take when a parsing error has occured.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.ParseErrorAction.RaiseEvent">
            <summary>
                Raises the <see cref="M:CsvReader.ParseError"/> event.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.ParseErrorAction.AdvanceToNextLine">
            <summary>
                Tries to advance to next line.
            </summary>
        </member>
        <member name="F:Effort.Internal.Csv.ParseErrorAction.ThrowException">
            <summary>
                Throws an exception.
            </summary>
        </member>
        <member name="T:Effort.Provider.IDbManager">
            <summary>
                Provides functionality for managing the database.
            </summary>
        </member>
        <member name="M:Effort.Provider.IDbManager.SetIdentityFields(System.Boolean)">
            <summary>
                Enables or disables all the identity fields in the database.
            </summary>
            <param name="enabled">
                if set to <c>true</c> the identity fields will be disabled.
            </param>
        </member>
        <member name="M:Effort.Provider.IDbManager.ClearMigrationHistory">
            <summary>
                Clears Entity Framework migration history by deleting all records from the 
                appropriate tables.
            </summary>
        </member>
        <member name="M:Effort.Provider.IDbManager.ClearTables">
            <summary>
                Deletes all data from the database tables.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.CsvTableDataLoader">
            <summary>
                Represent a table data loader that retrieves data from a CSV file.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CsvTableDataLoader.#ctor(Effort.DataLoaders.IFileReference,Effort.DataLoaders.TableDescription)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CsvTableDataLoader"/> class.
            </summary>
            <param name="file"> The file reference to the CSV file. </param>
            <param name="table"> The metadata of the requested table. </param>
        </member>
        <member name="M:Effort.DataLoaders.CsvTableDataLoader.GetData">
            <summary>
                Creates initial data for the table.
            </summary>
            <returns>
                The data created for the table.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.CsvTableDataLoader.CreateDataReader">
            <summary>
                Creates a CSV data reader that retrieves the initial data from the appropriate
                CSV file.
            </summary>
            <returns>
                The CSV data reader.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.CsvTableDataLoader.ConvertValue(System.Object,System.Type)">
            <summary>
                Converts the string value to the appropriate type.
            </summary>
            <param name="value">
                The current string value.
            </param>
            <param name="type"> 
                The expected type. 
            </param>
            <returns>
                The expected value.
            </returns>
            <exception cref="T:System.FormatException">
                The string value is in wrong format.
            </exception>
        </member>
        <member name="T:Effort.DataLoaders.CsvTableDataLoaderFactory">
            <summary>
                Represents a table data loader factory that creates 
                <see cref="T:Effort.DataLoaders.CsvTableDataLoader"/> instances for tables.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.CsvTableDataLoaderFactory.#ctor(Effort.DataLoaders.FileSource)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.CsvTableDataLoaderFactory"/> 
                class.
            </summary>
            <param name="source"> The source of CSV files. </param>
            <exception cref="T:System.ArgumentException"> The path does not exists. </exception>
        </member>
        <member name="M:Effort.DataLoaders.CsvTableDataLoaderFactory.CreateTableDataLoader(Effort.DataLoaders.TableDescription)">
            <summary>
                Creates a <see cref="T:Effort.DataLoaders.CsvTableDataLoader"/> instance for the specified table.
            </summary>
            <param name="table">
                The metadata of the table. 
            </param>
            <returns>
                The <see cref="T:Effort.DataLoaders.CsvTableDataLoader"/> instance for the table.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.CsvTableDataLoaderFactory.Dispose">
            <summary>
                Does nothing.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.EntityTableDataLoader">
            <summary>
                Represents a table data loader that retrieves data from the specified table of the
                specified database.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.EntityTableDataLoader.#ctor(System.Data.Entity.Core.EntityClient.EntityConnection,Effort.DataLoaders.TableDescription)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.EntityTableDataLoader"/> class.
            </summary>
            <param name="connection"> The connection towards the database. </param>
            <param name="table"> The metadata of the table. </param>
        </member>
        <member name="M:Effort.DataLoaders.EntityTableDataLoader.CreateDataReader">
            <summary>
                Creates a data reader that retrieves the initial data from the database.
            </summary>
            <returns>
                The data reader.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.EntityTableDataLoader.ConvertValue(System.Object,System.Type)">
            <summary>
                Converts DBNull values to CLR null.
            </summary>
            <param name="value"> The current value. </param>
            <param name="type"> The expected type. </param>
            <returns>
                The expected value.
            </returns>
        </member>
        <member name="T:Effort.DataLoaders.EntityTableDataLoaderFactory">
            <summary>
                Represents a table data loader factory that creates 
                <see cref="T:Effort.DataLoaders.EntityTableDataLoader"/> instances for tables.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.EntityTableDataLoaderFactory.#ctor(System.Func{System.Data.Entity.Core.EntityClient.EntityConnection})">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.DataLoaders.EntityTableDataLoaderFactory"/> 
                class.
            </summary>
            <param name="connectionFactory">
                A delegate that creates a connection towards the appropriate database.
            </param>
        </member>
        <member name="M:Effort.DataLoaders.EntityTableDataLoaderFactory.CreateTableDataLoader(Effort.DataLoaders.TableDescription)">
            <summary>
                Ensures that a connection is established towards to appropriate database and 
                creates a <see cref="T:Effort.DataLoaders.EntityTableDataLoader"/> instance for the specified
                table.
            </summary>
            <param name="table"> 
                The metadata of the table.
            </param>
            <returns>
                The <see cref="T:Effort.DataLoaders.EntityTableDataLoader"/> instance for the table.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.EntityTableDataLoaderFactory.Dispose">
            <summary>
                Disposes the connection established towards the database.
            </summary>
        </member>
        <member name="T:Effort.DataLoaders.EmptyTableDataLoader">
            <summary>
                Represents a table data loader that retrieves no data.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.EmptyTableDataLoader.GetData">
            <summary>
                Creates no data for the table.
            </summary>
            <returns>
                An empty enumerable object.
            </returns>
        </member>
        <member name="T:Effort.DataLoaders.EmptyTableDataLoaderFactory">
            <summary>
                Represent a table data loader factory that creates 
                <see cref="T:Effort.DataLoaders.EmptyTableDataLoader"/> instances for tables.
            </summary>
        </member>
        <member name="M:Effort.DataLoaders.EmptyTableDataLoaderFactory.CreateTableDataLoader(Effort.DataLoaders.TableDescription)">
            <summary>
                Creates a <see cref="T:Effort.DataLoaders.EmptyTableDataLoader"/> instance.
            </summary>
            <param name="table">
                The metadata of the table.
            </param>
            <returns>
                The <see cref="T:Effort.DataLoaders.EmptyTableDataLoader"/> instance for the table.
            </returns>
        </member>
        <member name="M:Effort.DataLoaders.EmptyTableDataLoaderFactory.Dispose">
            <summary>
                Does nothing.
            </summary>
        </member>
        <member name="T:Effort.Internal.DbManagement.Engine.Modifiers.ExcrescentSingleResultCleanserVisitor">
            <summary>
                Transforms SingleResult&gt;&lt;(x).FirstOrDefault() to x
            </summary>
        </member>
        <member name="M:Effort.Internal.DbCommandTreeTransformation.LinqMethodProvider.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:Effort.Internal.DbCommandTreeTransformation.LinqMethodProvider"/> class from
            being created.
            </summary>
        </member>
        <member name="T:Effort.Internal.Caching.ConcurrentCache`2">
            <summary>
                Represents a thread-safe generic dictionary-like cache.
            </summary>
            <typeparam name="TKey"> The type of the key. </typeparam>
            <typeparam name="TElement"> The type of the elements. </typeparam>
        </member>
        <member name="F:Effort.Internal.Caching.ConcurrentCache`2.store">
            <summary>
                The internal store.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.ConcurrentCache`2.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.Caching.ConcurrentCache`2"/> 
                class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.ConcurrentCache`2.Get(`0)">
            <summary>
                Gets the element associated with the specified key.
            </summary>
            <param name="key"> The key that identifies the cached element. </param>
            <returns> The cached element. </returns>
        </member>
        <member name="M:Effort.Internal.Caching.ConcurrentCache`2.Get(`0,System.Func{`1})">
            <summary>
                Gets the element associated with the specified key. If no such element exists,
                it is initialized by the supplied factory method.
            </summary>
            <param name="key"> The key that identifies the cached element. </param>
            <param name="factory"> The element factory method. </param>
            <returns> The queried element. </returns>
        </member>
        <member name="M:Effort.Internal.Caching.ConcurrentCache`2.Contains(`0)">
            <summary>
                Determines whether the store containes an element associated to the specified
                key.
            </summary>
            <param name="key"> 
                The key that identifies the cached element. 
            </param>
            <returns>
                <c>true</c> if it contains the appropriate element otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Effort.Internal.Caching.ConcurrentCache`2.Remove(`0)">
            <summary>
                Removes the element associate to the specified key.
            </summary>
            <param name="key"> The key that identifies the cached element. </param>
        </member>
        <member name="T:Effort.EntityConnectionFactory">
            <summary>
                Provides factory methods that are able to create <see cref="T:EntityConnection"/>
                objects that rely on in-process and in-memory databases. All of the data operations 
                initiated from these connection objects are executed by the appropriate in-memory 
                database, so using these connection objects does not require any external 
                dependency outside of the scope of the application.
            </summary>
        </member>
        <member name="M:Effort.EntityConnectionFactory.#cctor">
            <summary>
                Initializes static members of the <see cref="T:Effort.EntityConnectionFactory"/> class.
            </summary>
        </member>
        <member name="M:Effort.EntityConnectionFactory.CreatePersistent(System.String,System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a <see cref="T:EntityConnection"/> object that rely on an in-memory 
                database instance that lives during the complete application lifecycle. If the 
                database is accessed the first time, then it will be constructed based on the 
                metadata referenced by the provided entity connection string and its state is 
                initialized by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <param name="instanceId">
                The identifier of the in-memory database.
            </param>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and
                references the metadata that is required for constructing the schema.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:EntityConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.CreatePersistent(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a <see cref="T:EntityConnection"/> object that rely on an in-memory 
                database instance that lives during the complete application lifecycle. If the 
                database is accessed the first time, then it will be constructed based on the 
                metadata referenced by the provided entity connection string and its state is 
                initialized by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and
                references the metadata that is required for constructing the schema.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:EntityConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.CreatePersistent(System.String)">
            <summary>
                Creates a <see cref="T:EntityConnection"/> object that rely on an in-memory 
                database instance that lives during the complete application lifecycle. If the 
                database is accessed the first time, then it will be constructed based on the 
                metadata referenced by the provided entity connection string.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and references
                the metadata that is required for constructing the schema.
            </param>
            <returns>
                The <see cref="T:SEntityConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.CreatePersistent(System.String,System.String)">
            <summary>
                Creates a <see cref="T:EntityConnection"/> object that rely on an in-memory 
                database instance that lives during the complete application lifecycle. If the 
                database is accessed the first time, then it will be constructed based on the 
                metadata referenced by the provided entity connection string.
            </summary>
            <param name="instanceId">
                The identifier of the in-memory database.
            </param>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and references
                the metadata that is required for constructing the schema.
            </param>
            <returns>
                The <see cref="T:SEntityConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.CreateTransient(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a <see cref="T:EntityConnection"/> object that rely on an in-memory
                database instance that lives during the connection object lifecycle. If the 
                connection object is disposed or garbage collected, then underlying database
                will be garbage collected too. The database is constructed based on the 
                metadata referenced by the provided entity connection string and its state is
                initialized by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that references the metadata that is required for 
                constructing the schema.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:EntityConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.CreateTransient(System.String)">
            <summary>
                Creates a <see cref="T:EntityConnection"/> object that rely on an in-memory 
                database instance that lives during the connection object lifecycle. If the 
                connection object is disposed or garbage collected, then underlying database 
                will be garbage collected too. The database is constructed based on the 
                metadata referenced by the provided entity connection string.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that references the metadata that is required for 
                constructing the schema.
            </param>
            <returns>
                The <see cref="T:DbConnection"/> object.
            </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.Create(System.String,System.String,System.Boolean)">
            <summary>
                Creates a new EntityConnection instance that wraps an EffortConnection object
                with the specified connection string.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that references the metadata and identifies the 
                persistent database.
            </param>
            <param name="effortConnectionString">
                The effort connection string that is passed to the EffortConnection object.
            </param>
            <param name="persistent">
                if set to <c>true</c> the ObjectContext uses a persistent database, otherwise 
                transient.
            </param>
            <returns>
                The EntityConnection object.
            </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.GetFullEntityConnectionString(System.String)">
            <summary>
                Returns the full entity connection string if it formed as 
                "name=connectionStringName".
            </summary>
            <param name="entityConnectionString"> The entity connection string. </param>
            <returns> The full entity connection string. </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.CreateEntityConnection(System.Data.Entity.Core.Metadata.Edm.MetadataWorkspace,System.Data.Common.DbConnection)">
            <summary>
                Creates a new EntityConnection object and initializes its underlying database.
            </summary>
            <param name="metadata"> The metadata of the database. </param>
            <param name="connection"> The wrapped connection object. </param>
            <returns> The EntityConnection object. </returns>
        </member>
        <member name="M:Effort.EntityConnectionFactory.GetEffortCompatibleMetadataWorkspace(System.String@)">
            <summary>
                Returns a metadata workspace that is rewritten in order to be compatible the
                Effort provider.
            </summary>
            <param name="entityConnectionString">
                The entity connection string that references the original metadata.
            </param>
            <returns>
                The rewritten metadata.
            </returns>
        </member>
        <member name="T:Effort.Internal.StorageSchema.ModificationFunctionMappingModifier">
            <summary>
            	Removes function mapping for Insert, Update, and Delete. This is required for being 
            	able to save changes to EFFORT context based on model with defined modification 
            	function mappings.
            </summary>
        </member>
        <member name="T:Effort.Internal.TypeConversion.FacetInfo">
            <summary>
                Contains EDM type facet information about a field.
            </summary>
        </member>
        <member name="P:Effort.Internal.TypeConversion.FacetInfo.Nullable">
            <summary>
                Gets or sets a value indicating whether the field is nullable.
            </summary>
            <value>
                <c>true</c> if nullable; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Effort.Internal.TypeConversion.FacetInfo.Identity">
            <summary>
                Gets or sets a value indicating whether the field is an identity field.
            </summary>
            <value>
                <c>true</c> if identity field; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Effort.Internal.TypeConversion.FacetInfo.Computed">
            <summary>
                Gets or sets a value indicating whether the field value is computed.
            </summary>
            <value>
                <c>true</c> if computed; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Effort.Internal.TypeConversion.FacetInfo.LimitedLength">
            <summary>
                Gets or sets a value indicating whether length of the field is limited.
            </summary>
            <value>
                <c>true</c> if the length of the field is limited; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Effort.Internal.TypeConversion.FacetInfo.MaxLength">
            <summary>
                Gets or sets the max lenght of the field.
            </summary>
            <value>
                The max lenght of the field.
            </value>
        </member>
        <member name="P:Effort.Internal.TypeConversion.FacetInfo.FixedLength">
            <summary>
                Gets or sets a value indicating whether the length of the field is fixed.
            </summary>
            <value>
                <c>true</c> if the length of the field is fixed; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Effort.Internal.TypeGeneration.LargeDataRowAttribute">
            <summary>
                When applied to a <see cref="T:Effort.Internal.TypeGeneration.DataRow"/> type, specifies that the type has so many
                properties that its single constructor has a single array parameter. 
            </summary>
        </member>
        <member name="F:Effort.Internal.TypeGeneration.LargeDataRowAttribute.LargePropertyCount">
            <summary>
                Determines the minimum amount of properties that an annotated 
                <see cref="T:Effort.Internal.TypeGeneration.DataRow"/> type should have.
            </summary>
        </member>
        <member name="T:Effort.Internal.TypeGeneration.DataRow">
            <summary>
                Represent an immutable data row.
            </summary>
        </member>
        <member name="M:Effort.Internal.TypeGeneration.DataRow.GetValue(System.Int32)">
            <summary>
                Returns the value of the specified property.
            </summary>
            <param name="index"> The index of the property. </param>
            <returns> The value of the property. </returns>
        </member>
        <member name="T:Effort.Internal.TypeGeneration.DataRowPropertyAttribute">
            <summary>
                When applied to the property of a <see cref="T:Effort.Internal.TypeGeneration.DataRow"/> type, specifies the index 
                of the property.
            </summary>
        </member>
        <member name="M:Effort.Internal.TypeGeneration.DataRowPropertyAttribute.#ctor(System.Int32)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Internal.TypeGeneration.DataRowPropertyAttribute"/> class.
            </summary>
            <param name="index"> The index of the property. </param>
        </member>
        <member name="P:Effort.Internal.TypeGeneration.DataRowPropertyAttribute.Index">
            <summary>
                Gets the index of the property.
            </summary>
            <value>
                The index.
            </value>
        </member>
        <member name="T:Effort.ObjectContextFactory">
            <summary>
                Provides factory methods that are able to create <see cref="T:ObjectContext"/>
                objects that rely on in-process and in-memory databases. All of the data operations
                initiated from these context objects are executed by the appropriate in-memory 
                database, so using these context objects does not require any external dependency
                outside of the scope of the application.
            </summary>
        </member>
        <member name="F:Effort.ObjectContextFactory.objectContextContainer">
            <summary>
                The dynamic CLI module that contains the dynamically created ObjectContext 
                classes.
            </summary>
        </member>
        <member name="F:Effort.ObjectContextFactory.objectContextCount">
            <summary>
                The count of the dynamically created ObjectContext classes.
            </summary>
        </member>
        <member name="M:Effort.ObjectContextFactory.#cctor">
            <summary>
                Initializes static members of the <see cref="T:Effort.ObjectContextFactory"/> class.
            </summary>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistentType``1(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Returns a new type that derives from the <see cref="T:ObjectContext"/> based
                class specified by the <typeparamref name="T"/> generic argument. This class
                relies on an in-memory database instance that lives during the complete 
                application lifecycle. If the database is accessed the first time, then it will
                be constructed based on the metadata referenced by the provided entity 
                connection string and its state is initialized by the provided
                <see cref="T:IDataLoader"/> object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and
                references the metadata that is required for constructing the schema.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistentType``1(System.String)">
            <summary>
                Returns a new type that derives from the <see cref="T:ObjectContext"/> based
                class specified by the <typeparamref name="T"/> generic argument. This class 
                relies on an in-memory database instance that lives during the complete 
                application lifecycle. If the database is accessed the first time, then it will
                be constructed based on the metadata referenced by the provided entity 
                connection string.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and 
                references the metadata that is required for constructing the schema.
            </param>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistentType``1">
            <summary>
                Returns a new type that derives from the <see cref="T:ObjectContext"/> based
                class specified by the <typeparamref name="T"/> generic argument. This class 
                relies on an in-memory database instance that lives during the complete 
                application lifecycle. If the database is accessed the first time, then it will
                be constructed based on the metadata referenced by the default entity 
                connection string of the provided <see cref="T:ObjectContext"/> type.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistentType``1(Effort.DataLoaders.IDataLoader)">
            <summary>
                Returns a new type that derives from the <see cref="T:ObjectContext"/> based
                class specified by the <typeparamref name="T"/> generic argument. This class
                relies on an in-memory database instance that lives during the complete 
                application lifecycle. If the database is accessed the first time, then it will
                be constructed based on the metadata referenced by the default entity 
                connection string of the provided <see cref="T:ObjectContext"/> type and its
                state is initialized by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistent``1(System.String)">
            <summary>
                Creates a new instance of the <see cref="T:ObjectContext"/> based class
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the complete application
                lifecycle. If the database is accessed the first time, then it will be 
                constructed based on the metadata referenced by the provided entity connection 
                string.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and 
                references the metadata that is required for constructing the schema.
            </param>
            <returns>The <see cref="T:ObjectContext"/> object.</returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistent``1(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a new instance of the <see cref="T:ObjectContext"/> based class
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the complete application
                lifecycle. If the database is accessed the first time, then it will be 
                constructed based on the metadata referenced by the provided entity connection
                string and its state is initialized by the provided <see cref="T:IDataLoader"/>
                object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and
                references the metadata that is required for constructing the schema.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:ObjectContext"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistent``1">
            <summary>
                Creates a new instance of the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the complete application
                lifecycle. If the database is accessed the first time, then it will be
                constructed based on the metadata referenced by the default entity connection
                string of the provided <see cref="T:ObjectContext"/> type.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <returns>
                The <see cref="T:ObjectContext"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreatePersistent``1(Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a instance of the <see cref="T:ObjectContext"/> based class specified 
                by the <typeparamref name="T"/> generic argument. This class relies on an 
                in-memory database instance that lives during the complete application 
                lifecycle. If the database is accessed the first time, then it will be 
                constructed based on the metadata referenced by the default entity connection
                string of the provided <see cref="T:ObjectContext"/> type and its state is
                initialized by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:ObjectContext"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransientType``1(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Returns a type that derives from the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context instance is disposed or garbage collected, 
                then the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the provided entity connection
                string and its state is initialized by the provided <see cref="T:IDataLoader"/>
                object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and 
                references the metadata that is required for constructing the schema.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransientType``1(System.String)">
            <summary>
                Returns a type that derives from the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context instance is disposed or garbage collected, 
                then the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the provided entity connection
                string.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and 
                references the metadata that is required for constructing the schema.
            </param>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransientType``1">
            <summary>
                Returns a type that derives from the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies 
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context instance is disposed or garbage collected, 
                then the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the default entity connection
                string of the provided <see cref="T:ObjectContext"/> type.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransientType``1(Effort.DataLoaders.IDataLoader)">
            <summary>
                Returns a type that derives from the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context object is disposed or garbage collected, then 
                the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the default entity connection 
                string of the provided <see cref="T:ObjectContext"/> type and its state is 
                initialized by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:Type"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransient``1(System.String,Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a new instance of the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context instance is disposed or garbage collected, 
                then the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the provided entity connection
                string and its state is initialized by the provided <see cref="T:IDataLoader"/>
                object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and 
                references the metadata that is required for constructing the schema.
            </param>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:ObjectContext"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransient``1(System.String)">
            <summary>
                Creates a new instance of the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context instance is disposed or garbage collected, 
                then the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the provided entity connection
                string.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that identifies the in-memory database and
                references the metadata that is required for constructing the schema.
            </param>
            <returns>
                The <see cref="T:ObjectContext"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransient``1(Effort.DataLoaders.IDataLoader)">
            <summary>
                Creates a new instance of the <see cref="T:ObjectContext"/> based class 
                specified by the <typeparamref name="T"/> generic argument. This class relies 
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context instance is disposed or garbage collected, 
                then the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the default entity connection
                string of the provided <see cref="T:ObjectContext"/> type and its state is 
                initialized by the provided <see cref="T:IDataLoader"/> object.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <param name="dataLoader">
                The <see cref="T:IDataLoader"/> object that might initialize the state of the 
                in-memory database.
            </param>
            <returns>
                The <see cref="T:ObjectContext"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateTransient``1">
            <summary>
                Creates of new instance of the <see cref="T:ObjectContext"/> based class
                specified by the <typeparamref name="T"/> generic argument. This class relies
                on an in-memory database instance that lives during the context object 
                lifecycle. If the object context object is disposed or garbage collected, then
                the underlying database will be garbage collected too. The database is 
                constructed based on the metadata referenced by the default entity connection
                string of the provided <see cref="T:ObjectContext"/> type.
            </summary>
            <typeparam name="T">
                The concrete <see cref="T:ObjectContext"/> based class.
            </typeparam>
            <returns>
                The <see cref="T:ObjectContext"/> object.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateType``1(System.String,System.Boolean,Effort.DataLoaders.IDataLoader)">
            <summary>
                Returns the appropriate dynamic ObjectContext type.
            </summary>
            <typeparam name="T">
                The ObjectContext type that the result type should derive from.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that references the metadata and identifies the 
                persistent database.
            </param>
            <param name="persistent">
                if set to <c>true</c> the ObjectContext uses a persistent database, otherwise 
                transient.
            </param>
            <param name="dataLoader">
                The data loader that initializes the state of the database.
            </param>
            <returns>
                The ObjectContext type.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.GetDefaultConnectionString``1">
            <summary>
                Returns the default entity connection string of the specified ObjectContext
                type.
            </summary>
            <typeparam name="T">
                The type of the ObjectContext.
            </typeparam>
            <returns>
                The entity connection string.
            </returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.CreateType``1(System.String,System.String,System.Boolean)">
            <summary>
                Creates a ObjectContext type during dynamically.
            </summary>
            <typeparam name="T">
                The type of the ObjectContext.
            </typeparam>
            <param name="entityConnectionString">
                The entity connection string that references the metadata and identifies the 
                persistent database.
            </param>
            <param name="effortConnectionString">
                The effort connection string that is passed to the EffortConnection object.
            </param>
            <param name="persistent">
                if set to <c>true</c> the ObjectContext uses a persistent database, otherwise 
                transient.
            </param>
            <returns>The ObjectContext type.</returns>
        </member>
        <member name="M:Effort.ObjectContextFactory.FindDefaultConnectionStringByConvention``1">
            <summary>
                Returns the default connection string by convention.
            </summary>
            <typeparam name="T">
                The type of the ObjectContext.
            </typeparam>
            <returns>
                The default connection string based on the name of the ObjectContext
            </returns>
        </member>
        <member name="T:Effort.Internal.Common.CommandTreeBuilder">
            <summary>
                Create DbCommandTree objects.
            </summary>
        </member>
        <member name="M:Effort.Internal.Common.CommandTreeBuilder.CreateSelectAll(System.Data.Entity.Core.Metadata.Edm.MetadataWorkspace,System.Data.Entity.Core.Metadata.Edm.EntitySet)">
            <summary>
                Creates the full database scan expression.
            </summary>
            <param name="workspace">
                The workspace that contains the metadata of the database
            </param>
            <param name="entitySet">
                The entity set that is being scanned.
            </param>
            <returns>
                The DbCommandTree object.
            </returns>
        </member>
        <member name="T:Effort.Internal.Caching.DbContainerStore">
            <summary>
                Represents a cache that stores <see cref="T:Effort.Internal.DbManagement.DbContainer"/> objects.
            </summary>
        </member>
        <member name="F:Effort.Internal.Caching.DbContainerStore.store">
            <summary>
                Internal collection.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DbContainerStore.#cctor">
            <summary>
                Initializes static members of the <see cref="T:Effort.Internal.Caching.DbContainerStore"/> class.
            </summary>
        </member>
        <member name="M:Effort.Internal.Caching.DbContainerStore.GetDbContainer(System.String,System.Func{Effort.Internal.DbManagement.DbContainer})">
            <summary>
                Returns a <see cref="T:Effort.Internal.DbManagement.DbContainer"/> object identified by the specified instance
                identifier. If no such element exist, the specified factory method is used to
                create one.
            </summary>
            <param name="instanceId"> The instance id. </param>
            <param name="databaseFactoryMethod"> The database factory method. </param>
            <returns> The <see cref="T:Effort.Internal.DbManagement.DbContainer"/> object. </returns>
        </member>
        <member name="M:Effort.Internal.Caching.DbContainerStore.RemoveDbContainer(System.String)">
            <summary>
                Removes the DbContainer associated to the specified identifier from the cache.
            </summary>
            <param name="instanceId"> The instance identifier. </param>
        </member>
        <member name="T:Effort.Provider.EffortCommand">
            <summary>
                Represents an Effort command that realizes text representations.
            </summary>
        </member>
        <member name="T:Effort.Provider.EffortCommandBase">
            <summary>
                Provides a base class for Effort-specific classes that represent commands.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortCommandBase"/> class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.AddParameter(System.String)">
            <summary>
                Adds a new parameter with the supplied name.
            </summary>
            <param name="name"> The name of the parameter. </param>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.ExecuteNonQuery">
            <summary>
                Executes the query.
            </summary>
            <returns>
                The number of rows affected.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.ExecuteScalar">
            <summary>
                Executes the query and returns the first column of the first row in the result
                set returned by the query. All other columns and rows are ignored.
            </summary>
            <returns>
                The first column of the first row in the result set.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.Prepare">
            <summary>
                Creates a prepared (or compiled) version of the command on the data source.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.Cancel">
            <summary>
                Attempts to cancels the execution of a 
                <see cref="T:System.Data.Common.DbCommand" />.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.CreateDbParameter">
            <summary>
                Creates a new instance of a <see cref="T:Effort.Provider.EffortParameter"/> object.
            </summary>
            <returns>
                A <see cref="T:Effort.Provider.EffortParameter"/> object.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.Clone">
            <summary>
                Creates a new object that is a copy of the current instance.
            </summary>
            <returns>
                A new object that is a copy of this instance.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommandBase.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
                Executes the command text against the connection.
            </summary>
            <param name="behavior">
                An instance of <see cref="T:System.Data.CommandBehavior" />.
            </param>
            <returns>
                A <see cref="T:System.Data.Common.DbDataReader" />.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.CommandText">
            <summary>
                Gets or sets the text command to run against the data source.
            </summary>
            <returns>
                The text command to execute. The default value is an empty string ("").
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.CommandTimeout">
            <summary>
                Gets or sets the wait time before terminating the attempt to execute a command 
                and generating an error.
            </summary>
            <returns>
                The time in seconds to wait for the command to execute.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.CommandType">
            <summary>
                Indicates or specifies how the command is interpreted.
            </summary>
            <returns>
                One of the <see cref="T:System.Data.CommandType" /> values. The default is 
                Text.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.DbParameterCollection">
            <summary>
                Gets the collection of <see cref="T:Effort.Provider.EffortParameter"/> objects.
            </summary>
            <returns> The parameters of the SQL statement or stored procedure. </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.DbConnection">
            <summary>
                Gets or sets the <see cref="P:Effort.Provider.EffortCommandBase.EffortConnection"/> used by this command.
            </summary>
            <returns>
                The connection to the data source.
            </returns>
            <exception cref="T:System.ArgumentException">
                Provided connection object is incompatible
            </exception>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.DbTransaction">
            <summary>
                Gets or sets the <see cref="P:Effort.Provider.EffortCommandBase.EffortTransaction"/> within which this command 
                executes.
            </summary>
            <returns>
                The transaction within which a Command object of a .NET Framework data provider 
                executes. The default value is a null reference (Nothing in Visual Basic).
            </returns>
            <exception cref="T:System.ArgumentException">
                Provided transaction object is incompatible
            </exception>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.DesignTimeVisible">
            <summary>
                Gets or sets a value indicating whether the command object should be visible in
                a customized interface control.
            </summary>
            <returns>
                true, if the command object should be visible in a control; otherwise false.
                The default is true.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.EffortConnection">
            <summary>
                Gets the strongly typed <see cref="P:Effort.Provider.EffortCommandBase.EffortConnection"/> used by this command.
            </summary>
            <returns>
                The connection to the data source.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.EffortTransaction">
            <summary>
                Gets the strongly typed <see cref="P:Effort.Provider.EffortCommandBase.EffortTransaction"/> within which this
                command executes.
            </summary>
            <returns>
                The transaction within which a Command object of a .NET Framework data provider
                executes. The default value is a null reference (Nothing in Visual Basic).
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortCommandBase.UpdatedRowSource">
            <summary>
                Gets or sets how command results are applied to the 
                <see cref="T:System.Data.DataRow" /> when used by the Update method of a 
                <see cref="T:System.Data.Common.DbDataAdapter" />.
            </summary>
            <returns>
                One of the <see cref="T:System.Data.UpdateRowSource" /> values. The default is
                Both unless the command is automatically generated. Then the default is None.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
                Executes the command text against the connection.
            </summary>
            <param name="behavior">
                An instance of <see cref="T:System.Data.CommandBehavior" />.
            </param>
            <returns>
                A <see cref="T:System.Data.Common.DbDataReader" />.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommand.ExecuteNonQuery">
            <summary>
                Executes the query.
            </summary>
            <returns>
                The number of rows affected.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommand.ExecuteScalar">
            <summary>
                Executes the query and returns the first column of the first row in the result
                set returned by the query. All other columns and rows are ignored.
            </summary>
            <returns>
                The first column of the first row in the result set.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortCommand.Clone">
            <summary>
                Creates a new object that is a copy of the current instance.
            </summary>
            <returns>
                A new object that is a copy of this instance.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortCommandDefinition">
            <summary>
                Defines a cacheable command plan.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortCommandDefinition.#ctor(Effort.Provider.EffortCommandBase)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortCommandDefinition"/> class 
                using the supplied <see cref="T:Effort.Provider.EffortCommandBase"/>.
            </summary>
            <param name="prototype">
                The supplied <see cref="T:Effort.Provider.EffortCommandDefinition"/>.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortCommandDefinition.CreateCommand">
            <summary>
                Creates and returnds a <see cref="T:System.Data.Common.DbCommand"/> object that can be executed.
            </summary>
            <returns>
                The command for database.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortConnection">
            <summary>
                Represents a virtual connection towards an in-memory fake database.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortConnection.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortConnection"/> class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortConnection.ChangeDatabase(System.String)">
            <summary>
                Changes the current database for an open connection.
            </summary>
            <param name="databaseName">
                Specifies the name of the database for the connection to use.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortConnection.Open">
            <summary>
                Opens a database connection with the settings specified by the 
                <see cref="P:System.Data.Common.DbConnection.ConnectionString" />.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortConnection.Close">
            <summary>
                Closes the connection to the database. This is the preferred method of closing
                any open connection.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortConnection.MarkAsPrimaryTransient">
            <summary>
                Marks the connection object as transient, so the underlying database instance
                will be disposed when this connection object is disposed or garbage collected.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortConnection.CreateDbCommand">
            <summary>
                Creates and returns a <see cref="T:System.Data.Common.DbCommand" /> object 
                associated with the current connection.
            </summary>
            <returns>
                A <see cref="T:System.Data.Common.DbCommand" /> object.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortConnection.BeginDbTransaction(System.Data.IsolationLevel)">
            <summary>
                Starts a database transaction.
            </summary>
            <param name="isolationLevel">
                Specifies the isolation level for the transaction.
            </param>
            <returns>
                An object representing the new transaction.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortConnection.EnlistTransaction(System.Transactions.Transaction)">
            <summary>
                Enlists in the specified transaction.
            </summary>
            <param name="transaction">
                A reference to an existing <see cref="T:System.Transactions.Transaction" /> in
                which to enlist.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortConnection.Dispose(System.Boolean)">
            <summary>
                Releases the unmanaged resources used by the 
                <see cref="T:System.ComponentModel.Component" /> and optionally releases the 
                managed resources.
            </summary>
            <param name="disposing">
                true to release both managed and unmanaged resources; false to release only 
                unmanaged resources.
            </param>
        </member>
        <member name="P:Effort.Provider.EffortConnection.ConnectionString">
            <summary>
                Gets or sets the string used to open the connection.
            </summary>
            <returns>
                The connection string used to establish the initial connection. The exact
                contents of the connection string depend on the specific data source for this
                connection. The default value is an empty string.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortConnection.DataSource">
            <summary>
                Gets the name of the database server to which to connect.
            </summary>
            <returns>
                The name of the database server to which to connect. The default value is an
                empty string.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortConnection.ServerVersion">
            <summary>
                Gets a string that represents the version of the server to which the object is 
                connected.
            </summary>
            <returns>
                The version of the database. The format of the string returned depends on the 
                specific type of connection you are using.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortConnection.State">
            <summary>
                Gets a string that describes the state of the connection.
            </summary>
            <returns>
                The state of the connection. The format of the string returned depends on the 
                specific type of connection you are using.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortConnection.DbContainer">
            <summary>
                Gets the internal <see cref="P:Effort.Provider.EffortConnection.DbContainer"/> instance.
            </summary>
            <value>
                The internal <see cref="P:Effort.Provider.EffortConnection.DbContainer"/> instance.
            </value>
        </member>
        <member name="P:Effort.Provider.EffortConnection.DbProviderFactory">
            <summary>
                Gets the <see cref="T:System.Data.Common.DbProviderFactory" /> for this 
                <see cref="T:System.Data.Common.DbConnection" />.
            </summary>
            <returns> 
                A <see cref="T:System.Data.Common.DbProviderFactory" />.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortConnection.Database">
            <summary>
                Gets the name of the current database after a connection is opened, or the 
                database name specified in the connection string before the connection is 
                opened.
            </summary>
            <returns>
                The name of the current database or the name of the database to be used after a 
                connection is opened. The default value is an empty string.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortConnection.DbManager">
            <summary>
                Gets the configuration object that allows to alter the current configuration
                of the database.
            </summary>
            <returns>
                The configuration object.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortEntityCommand">
            <summary>
                Represent an Effort command that realizes Entity Framework command tree 
                representations.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortEntityCommand.#ctor(System.Data.Entity.Core.Common.CommandTrees.DbCommandTree)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortEntityCommand"/> class 
                based on  a provided command tree.
            </summary>
            <param name="commandtree">
                The command tree that describes the operation.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortEntityCommand.#ctor(Effort.Provider.EffortEntityCommand)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortEntityCommand"/> class 
                based on a prototype instance.
            </summary>
            <param name="prototype">
                The prototype <see cref="T:Effort.Provider.EffortEntityCommand"/> object.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortEntityCommand.ExecuteNonQuery">
            <summary>
                Executes the query.
            </summary>
            <returns>
                The number of rows affected.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortEntityCommand.ExecuteScalar">
            <summary>
                Executes the query and returns the first column of the first row in the result
                set returned by the query. All other columns and rows are ignored.
            </summary>
            <returns>
                The first column of the first row in the result set.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortEntityCommand.Clone">
            <summary>
                Creates a new object that is a copy of the current instance.
            </summary>
            <returns>
                A new object that is a copy of this instance.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortEntityCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
                Executes the command text against the connection.
            </summary>
            <param name="behavior">
                An instance of <see cref="T:System.Data.CommandBehavior"/>.
            </param>
            <returns>
                A <see cref="T:Effort.Provider.EffortDataReader"/>.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortConnectionStringBuilder">
            <summary>
                Providers a simple way to manage the contents of connection string used by the
                <see cref="T:Effort.Provider.EffortConnection"/> class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortConnectionStringBuilder.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortConnectionStringBuilder"/> 
                class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortConnectionStringBuilder.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortConnectionStringBuilder"/> 
                class. The provided connection string provides the data for the internal
                connection information of the instance.
            </summary>
            <param name="connectionString">
                The basis for the object's internal connection information.
            </param>
        </member>
        <member name="P:Effort.Provider.EffortConnectionStringBuilder.InstanceId">
            <summary>
                Gets or sets the string that identifies the database instance.
            </summary>
            <value>
                The identifier of the database instance.
            </value>
        </member>
        <member name="P:Effort.Provider.EffortConnectionStringBuilder.IsTransient">
            <summary>
                Gets or sets the value indicating whether the database instance should be
                transient. Transient databases live only during the lifetime of the connection
                object.
            </summary>
            <value>
            <c>true</c> if the database instance is transient; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Effort.Provider.EffortConnectionStringBuilder.DataLoaderType">
            <summary>
                Gets or sets the type of the data loader that is used to initialize the state
                of the database instance. It has to implement the 
                <see cref="T:Effort.DataLoaders.IDataLoader"/> interface.
            </summary>
            <value>
                The type of the data loader. 
            </value>
            <exception cref="T:System.InvalidOperationException">
                Cannot set data loader.
            </exception>
        </member>
        <member name="P:Effort.Provider.EffortConnectionStringBuilder.DataLoaderArgument">
            <summary>
                Gets or sets the data loader argument that is used by the data loader to
                initialize the state of the database.
            </summary>
            <value>
                The data loader argument.
            </value>
        </member>
        <member name="T:Effort.Provider.EffortDataReader">
            <summary>
                Reads a forward-only stream of rows from a data source.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetBoolean(System.Int32)">
            <summary>
                Gets the value of the specified column as a Boolean.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetByte(System.Int32)">
            <summary>
                Gets the value of the specified column as a byte.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
                Reads a stream of bytes from the specified column, starting at location
                indicated by <paramref name="dataOffset" />, into the buffer, starting at the
                location indicated by <paramref name="bufferOffset" />.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <param name="dataOffset">
                The index within the row from which to begin the read operation.
            </param>
            <param name="buffer">
                The buffer into which to copy the data.
            </param>
            <param name="bufferOffset">
                The index with the buffer to which the data will be copied.
            </param>
            <param name="length">
                The maximum number of characters to read.
            </param>
            <returns>
                The actual number of bytes read.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetChar(System.Int32)">
            <summary>
                Gets the value of the specified column as a single character.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
                Reads a stream of characters from the specified column, starting at location 
                indicated by <paramref name="dataOffset" />, into the buffer, starting at the 
                location indicated by <paramref name="bufferOffset" />.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <param name="dataOffset">
                The index within the row from which to begin the read operation.
            </param>
            <param name="buffer">
                The buffer into which to copy the data.
            </param>
            <param name="bufferOffset">
                The index with the buffer to which the data will be copied.
            </param>
            <param name="length">
                The maximum number of characters to read.
            </param>
            <returns>
                The actual number of characters read.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetDataTypeName(System.Int32)">
            <summary>
                Gets name of the data type of the specified column.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                A string representing the name of the data type.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetDateTime(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.DateTime" /> 
                object.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetDecimal(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Decimal" />
                object.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetDouble(System.Int32)">
            <summary>
                Gets the value of the specified column as a double-precision floating point
                number.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetFieldType(System.Int32)">
            <summary>
                Gets the data type of the specified column.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The data type of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetFloat(System.Int32)">
            <summary>
                Gets the value of the specified column as a single-precision floating point
                number.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetGuid(System.Int32)">
            <summary>
                Gets the value of the specified column as a globally-unique identifier (GUID).
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetInt16(System.Int32)">
            <summary>
                Gets the value of the specified column as a 16-bit signed integer.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
            The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetInt32(System.Int32)">
            <summary>
                Gets the value of the specified column as a 32-bit signed integer.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetInt64(System.Int32)">
            <summary>
                Gets the value of the specified column as a 64-bit signed integer.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
             </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetName(System.Int32)">
            <summary>
                Gets the name of the column, given the zero-based column ordinal.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
             </param>
            <returns>
                The name of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetOrdinal(System.String)">
            <summary>
                Gets the column ordinal given the name of the column.
            </summary>
            <param name="name">
                The name of the column.
            </param>
            <returns>
                The zero-based column ordinal.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetEnumerator">
            <summary>
                Returns an <see cref="T:System.Collections.IEnumerator" /> that can be used to 
                iterate through the rows in the data reader.
            </summary>
            <returns>
                An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate 
                through the rows in the data reader.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetSchemaTable">
            <summary>
                Returns a <see cref="T:System.Data.DataTable" /> that describes the column
                metadata of the <see cref="T:System.Data.Common.DbDataReader" />.
            </summary>
            <returns>
                A <see cref="T:System.Data.DataTable" /> that describes the column metadata.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetString(System.Int32)">
            <summary>
                Gets the value of the specified column as an instance of 
                <see cref="T:System.String" />.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetValue(System.Int32)">
            <summary>
                Gets the value of the specified column as an instance of 
                <see cref="T:System.Object" />.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.GetValues(System.Object[])">
            <summary>
                Populates an array of objects with the column values of the current row.
            </summary>
            <param name="values">
                An array of <see cref="T:System.Object" /> into which to copy the attribute 
                columns.
            </param>
            <returns>
                The number of instances of <see cref="T:System.Object" /> in the array.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.IsDBNull(System.Int32)">
            <summary>
                Gets a value that indicates whether the column contains nonexistent or missing 
                values.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
            </param>
            <returns>
                true if the specified column is equivalent to <see cref="T:System.DBNull" />; 
                otherwise false.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.NextResult">
            <summary>
                Advances the reader to the next result when reading the results of a batch of 
                statements.
            </summary>
            <returns>
                true if there are more result sets; otherwise false.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.Read">
            <summary>
                Advances the reader to the next record in a result set.
            </summary>
            <returns>
                true if there are more rows; otherwise false.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.Close">
            <summary>
                Closes the <see cref="T:Effort.Provider.EffortDataReader"/> object.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortDataReader.Dispose(System.Boolean)">
            <summary>
                Releases the managed resources used by the <see cref="T:Effort.Provider.EffortDataReader"/> and 
                optionally releases the unmanaged resources.
            </summary>
            <param name="disposing">
                true to release managed and unmanaged resources; false to release only 
                unmanaged resources.
            </param>
        </member>
        <member name="P:Effort.Provider.EffortDataReader.Depth">
            <summary>
                Gets a value indicating the depth of nesting for the current row.
            </summary>
        </member>
        <member name="P:Effort.Provider.EffortDataReader.RecordsAffected">
            <summary>
                Gets the number of rows changed, inserted, or deleted by execution of the 
                command.
            </summary>
            <returns>
                The number of rows changed, inserted, or deleted. -1 for SELECT statements; 0
                if no rows were affected or the statement failed.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortDataReader.FieldCount">
            <summary>
                Gets the number of columns in the current row.
            </summary>
            <returns>
                The number of columns in the current row.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortDataReader.HasRows">
            <summary>
                Gets a value that indicates whether this 
                <see cref="T:System.Data.Common.DbDataReader" /> contains one or more rows.
            </summary>
            <returns>
                true if the <see cref="T:System.Data.Common.DbDataReader" /> contains one or 
                more rows; otherwise false.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortDataReader.IsClosed">
            <summary>
                Gets a value indicating whether the 
                <see cref="T:System.Data.Common.DbDataReader" /> is closed.
            </summary>
            <returns>
                true if the <see cref="T:System.Data.Common.DbDataReader" /> is closed; 
                otherwise false.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortDataReader.Item(System.String)">
            <summary>
                Gets the value of the specified column as an instance of 
            <see cref="T:System.Object" />.
            </summary>
            <param name="name">
                The name of the column.
            </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortDataReader.Item(System.Int32)">
            <summary>
                Gets the value of the specified column as an instance of 
            <see cref="T:System.Object" />.
            </summary>
            <param name="ordinal">
                The zero-based column ordinal.
             </param>
            <returns>
                The value of the specified column.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortParameter">
            <summary>
                Represents a parameter to a <see cref="T:EffortCommand"/>.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortParameter.ResetDbType">
            <summary>
                Resets the <see cref="P:DbType" /> property to its original settings.
            </summary>
        </member>
        <member name="P:Effort.Provider.EffortParameter.DbType">
            <summary>
                Gets or sets the <see cref="T:System.Data.DbType" /> of the parameter.
            </summary>
            <returns>
                One of the <see cref="T:System.Data.DbType" /> values. The default is 
                <see cref="F:System.Data.DbType.String" />.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.Direction">
            <summary>
                Gets or sets a value that indicates whether the parameter is input-only, 
                output-only, bidirectional, or a stored procedure return value parameter.
            </summary>
            <returns>
                One of the <see cref="T:System.Data.ParameterDirection" /> values. The default
                is Input.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.IsNullable">
            <summary>
                Gets or sets a value that indicates whether the parameter accepts null values.
            </summary>
            <returns>
                true if null values are accepted; otherwise false. The default is false.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.ParameterName">
            <summary>
                Gets or sets the name of the <see cref="T:System.Data.Common.DbParameter" />.
            </summary>
                <returns>The name of the <see cref="T:System.Data.Common.DbParameter" />. The 
                default is an empty string ("").
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.Size">
            <summary>
                Gets or sets the maximum size, in bytes, of the data within the column.
            </summary>
            <returns>
                The maximum size, in bytes, of the data within the column. The default value is 
                inferred from the parameter value.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.SourceColumn">
            <summary>
                Gets or sets the name of the source column mapped to the 
                <see cref="T:System.Data.DataSet" /> and used for loading or returning the 
                <see cref="P:System.Data.Common.DbParameter.Value" />.
            </summary>
            <returns>
                The name of the source column mapped to the 
                <see cref="T:System.Data.DataSet" />. The default is an empty string.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.SourceColumnNullMapping">
            <summary>
                Sets or gets a value which indicates whether the source column can be null.
                This allows <see cref="T:System.Data.Common.DbCommandBuilder" /> to correctly
                generate Update statements for columns that can be null.
            </summary>
            <returns>
                true if the source column can be null; false if it is not.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.SourceVersion">
            <summary>
                Gets or sets the <see cref="T:System.Data.DataRowVersion" /> to use when you
                load <see cref="P:System.Data.Common.DbParameter.Value" />.
            </summary>
            <returns>
                One of the <see cref="T:System.Data.DataRowVersion" /> values. The default is 
                Current.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameter.Value">
            <summary>
                Gets or sets the value of the parameter.
            </summary>
            <returns>
                An <see cref="T:System.Object" /> that is the value of the parameter. The 
                default value is null.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortParameterCollection">
            <summary>
                Represents a collection of <see cref="T:Effort.Provider.EffortParameter"/> associated with a 
                <see cref="T:Effort.Provider.EffortCommand"/>.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortParameterCollection"/> 
                class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.Add(System.Object)">
            <summary>
                Adds a <see cref="T:EffortParameter"/> item with the specified value to the 
                <see cref="T:EffortParameterCollection"/>.
            </summary>
            <param name="value">
                The <see cref="P:EffortParameter.Value"/> of the 
                <see cref="T:EffortParameter"/> to add to the collection.
            </param>
            <returns>
                The index of the <see cref="T:EffortParameter"/> object in the collection.
            </returns>
            <exception cref="T:System.ArgumentException">
                The provided parameter object is incompatible
            </exception>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.AddRange(System.Array)">
            <summary>
                Adds an array of items with the specified values to the 
            <see cref="T:EffortParameterCollection"/>.
            </summary>
                <param name="values">An array of values of type 
                <see cref="T:EffortParameter"/> to add to the collection.
            </param>
            <exception cref="T:System.ArgumentException">
                The provided parameter object is incompatible
            </exception>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.Clear">
            <summary>
                Removes all <see cref="T:EffortParameter" /> values from the
                <see cref="T:EffortParameterCollection" />.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.Contains(System.String)">
            <summary>
                Indicates whether a <see cref="T:EffortParameter" /> with the specified name
                exists in the collection.
            </summary>
            <param name="value">
                The name of the <see cref="T:EffortParameterr" /> to look for in the 
                collection.
            </param>
            <returns>
                true if the <see cref="T:EffortParameter" /> is in the collection; otherwise
                false.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.Contains(System.Object)">
            <summary>
                Indicates whether a <see cref="T:EffortParameter" /> with the specified 
                <see cref="P:EffortParameter.Value" /> is contained in the collection.
            </summary>
            <param name="value">
                The <see cref="P:EffortParameter.Value" /> of the 
                <see cref="T:EffortParameter" /> to look for in the collection.</param>
            <returns>
                true if the <see cref="T:EffortParameter" /> is in the collection; otherwise
                false.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.CopyTo(System.Array,System.Int32)">
            <summary>
                Copies an array of items to the collection starting at the specified index.
            </summary>
            <param name="array">
                The array of items to copy to the collection.
            </param>
            <param name="index">
                The index in the collection to copy the items.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.GetEnumerator">
            <summary>
                Exposes the <see cref="M:System.Collections.IEnumerable.GetEnumerator" />
                method, which supports a simple iteration over a collection by a .NET Framework
                data provider.
            </summary>
            <returns>
                An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate
                through the collection.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.GetParameter(System.String)">
            <summary>
                Returns <see cref="T:EffortParameter" /> the object with the specified name.
            </summary>
            <param name="parameterName">
                The name of the <see cref="T:EffortParameter" /> in the collection.
            </param>
            <returns>
                The <see cref="T:EffortParameter" /> the object with the specified name.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.GetParameter(System.Int32)">
            <summary>
                Returns the <see cref="T:EffortParameterr" /> object at the specified index in 
                the collection.
            </summary>
            <param name="index">
                The index of the <see cref="T:EffortParameter" /> in the collection.
            </param>
            <returns>
                The <see cref="T:EffortParameter" /> object at the specified index in the 
                collection.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.IndexOf(System.String)">
            <summary>
                Returns the index of the <see cref="T:EffortParameter" /> object with the 
                specified name.
            </summary>
            <param name="parameterName">
                The name of the <see cref="T:EffortParameter" /> object in the collection.
            </param>
            <returns>
                The index of the <see cref="T:EffortParameter" /> object with the specified 
                name.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.IndexOf(System.Object)">
            <summary>
                Returns the index of the specified <see cref="T:EffortParameter" /> object.
            </summary>
            <param name="value">
                The <see cref="T:EffortParameter" /> object in the collection.
            </param>
            <returns>
                The index of the specified <see cref="T:EffortParameter" /> object.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.Insert(System.Int32,System.Object)">
            <summary>
                Inserts the specified index of the <see cref="T:EffortParameter"/> object with
                the specified name into the collection at the specified index.
            </summary>
            <param name="index">
                The index at which to insert the <see cref="T:EffortParameter"/> object.
            </param>
            <param name="value">
                The <see cref="T:EffortParameter"/> object to insert into the collection.
            </param>
            <exception cref="T:System.ArgumentException">
                The provided parameter object is incompatible
            </exception>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.Remove(System.Object)">
            <summary>
                Removes the specified <see cref="T:EffortParameter" /> object from the 
                collection.
            </summary>
            <param name="value">
                The <see cref="T:EffortParameter" /> object to remove.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.RemoveAt(System.String)">
            <summary>
                Removes the <see cref="T:EffortParameter" /> object with the specified name 
                from the collection.
            </summary>
            <param name="parameterName">
                The name of the <see cref="T:EffortParameter" /> object to remove.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.RemoveAt(System.Int32)">
            <summary>
                Removes the <see cref="T:EffortParameter" /> object at the specified from the 
                collection.
            </summary>
            <param name="index">
                The index where the <see cref="T:EffortParameter" /> object is located.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
            <summary>
                Sets the <see cref="T:EffortParameter" /> object with the specified name to 
                new value.
            </summary>
            <param name="parameterName">
                The name of the <see cref="T:EffortParameter" /> object in the collection.
            </param>
            <param name="value">
                The new <see cref="T:EffortParameter" /> value.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
            <summary>
                Sets the <see cref="T:System.Data.Common.DbParameter" /> object at the
                specified index to a new value.
            </summary>
            <param name="index">
                The index where the <see cref="T:System.Data.Common.DbParameter" /> object is 
                located.
            </param>
            <param name="value">
                The new <see cref="T:System.Data.Common.DbParameter" /> value.
            </param>
        </member>
        <member name="P:Effort.Provider.EffortParameterCollection.Count">
            <summary>
                Specifies the number of items in the collection.
            </summary>
            <returns>
                The number of items in the collection.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameterCollection.IsFixedSize">
            <summary>
                Specifies whether the collection is a fixed size.
            </summary>
            <returns>
                true if the collection is a fixed size; otherwise false.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameterCollection.IsReadOnly">
            <summary>
                Specifies whether the collection is read-only.
            </summary>
            <returns>
                true if the collection is read-only; otherwise false.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameterCollection.IsSynchronized">
            <summary>
                Specifies whether the collection is synchronized.
            </summary>
            <returns>
                true if the collection is synchronized; otherwise false.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortParameterCollection.SyncRoot">
            <summary>
                Specifies the <see cref="T:System.Object" /> to be used to synchronize access
                to the collection.
            </summary>
            <returns>
                A <see cref="T:System.Object" /> to be used to synchronize access to the 
                <see cref="T:EffortParameterrCollection" />.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortProviderConfiguration">
            <summary>
                Configuration module for the Effort provider.
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortProviderConfiguration.ProviderInvariantName">
            <summary>
                The provider invariant name of the Effort provider.
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortProviderConfiguration.isRegistered">
            <summary>
                Indicates if the Effort provider is registered.
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortProviderConfiguration.latch">
            <summary>
                Latch object that is used to avoid double registration.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortProviderConfiguration.RegisterProvider">
            <summary>
                Registers the provider factory.
            </summary>
        </member>
        <member name="T:Effort.Provider.EffortProviderFactory">
            <summary>
                Represents a set of methods for creating instances of the 
            <see cref="N:Effort.Provider"/> provider's implementation of the data source classes.
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortProviderFactory.Instance">
            <summary>
                Provides a singleton instance of the <see cref="T:Effort.Provider.EffortProviderFactory"/> class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortProviderFactory.#ctor">
            <summary>
                Prevents a default instance of the <see cref="T:Effort.Provider.EffortProviderFactory"/> class
                from being created.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortProviderFactory.CreateConnection">
            <summary>
                Returns a new instance of the <see cref="T:EffortConnection" /> class.
            </summary>
            <returns>
                A new instance of <see cref="T:EffortConnection" />.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderFactory.GetService(System.Type)">
            <summary>
                Gets the service object of the specified type.
            </summary>
            <param name="serviceType">
                An object that specifies the type of service object to get.
            </param>
            <returns>
                A service object of type <paramref name="serviceType" />.-or- null if there is
                no service object of type <paramref name="serviceType" />.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortProviderManifest">
            <summary>
                Metadata interface for all CLR types types.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortProviderManifest.#ctor(Effort.Provider.EffortVersion)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortProviderManifest"/> class.
            </summary>
            <param name="version">The version of manifest metadata.</param>
        </member>
        <member name="M:Effort.Provider.EffortProviderManifest.GetEdmType(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
                This method maps the specified storage type and a set of facets for that type
                to an EDM type.
            </summary>
            <param name="storeType">
                The <see cref="T:System.Data.Metadata.Edm.TypeUsage" /> instance that describes
                a storage type and a set of facets for that type to be mapped to the EDM type.
            </param>
            <returns>
                The <see cref="T:System.Data.Metadata.Edm.TypeUsage" /> instance that describes
                an EDM type and a set of facets for that type.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderManifest.GetStoreType(System.Data.Entity.Core.Metadata.Edm.TypeUsage)">
            <summary>
                This method maps the specified EDM type and a set of facets for that type to a
                storage type.
            </summary>
            <param name="edmType">
                The <see cref="T:System.Data.Metadata.Edm.TypeUsage" /> instance that describes
                the EDM type and a set of facets for that type to be mapped to a storage type.
            </param>
            <returns>
                The <see cref="T:System.Data.Metadata.Edm.TypeUsage" /> instance that describes
                a storage type and a set of facets for that type.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderManifest.GetDbInformation(System.String)">
            <summary>
                When overridden in a derived class, this method returns provider-specific 
                information. This method should never return null.
            </summary>
            <param name="informationType">
                The type of the information to return.
            </param>
            <returns>
                The <see cref="T:System.Xml.XmlReader"/> object that contains the requested 
                information.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortProviderServices">
            <summary>
                The factory for building command definitions; use the type of this object as the 
                argument to the IServiceProvider.GetService method on the provider factory; 
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortProviderServices.Instance">
            <summary>
                Provides a singleton instance of the <see cref="T:Effort.Provider.EffortProviderServices"/> 
                class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.CreateCommandDefinition(System.Data.Common.DbCommand)">
            <summary>
                Creates a <see cref="T:System.Data.Common.DbCommandDefinition" /> that uses the 
                specified <see cref="T:System.Data.Common.DbCommand" />.
            </summary>
            <param name="prototype">
                A <see cref="T:System.Data.Common.DbCommand" /> used to create the 
                <see cref="T:System.Data.Common.DbCommandDefinition" />.
            </param>
            <returns>
                A <see cref="T:System.Data.Common.DbCommandDefinition" /> object that
                represents the executable command definition object.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.CreateDbCommandDefinition(System.Data.Entity.Core.Common.DbProviderManifest,System.Data.Entity.Core.Common.CommandTrees.DbCommandTree)">
            <summary>
                Creates a command definition object for the specified provider manifest and 
                command tree.
            </summary>
            <param name="providerManifest">
                Provider manifest previously retrieved from the store provider.
            </param>
            <param name="commandTree">
                Command tree for the statement.
            </param>
            <returns>
                An executable command definition object.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.GetDbProviderManifest(System.String)">
            <summary>
                When overridden in a derived class, returns an instance of a class that derives 
                from the <see cref="T:System.Data.Common.DbProviderManifest" />.
            </summary>
            <param name="manifestToken">
                The token information associated with the provider manifest.
            </param>
            <returns>
                A <see cref="T:System.Data.Common.DbProviderManifest" /> object that represents
                the provider manifest.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.GetDbProviderManifestToken(System.Data.Common.DbConnection)">
            <summary>
                Returns provider manifest token given a connection.
            </summary>
            <param name="connection">
                Connection to provider.
            </param>
            <returns>
                The provider manifest token for the specified connection.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.DbDatabaseExists(System.Data.Common.DbConnection,System.Nullable{System.Int32},System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
                Returns a value indicating whether a given database exists on the server and 
                whether schema objects contained in the storeItemCollection have been created.
            </summary>
            <param name="connection">
                Connection to a database whose existence is verified by this method.
            </param>
            <param name="commandTimeout">
                Execution timeout for any commands needed to determine the existence of the 
                database.
            </param>
            <param name="storeItemCollection">
                The structure of the database whose existence is determined by this method.
            </param>
            <returns>
                true if the database indicated by the connection and the 
                <paramref name="storeItemCollection" /> parameter exists.
            </returns>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.DbCreateDatabase(System.Data.Common.DbConnection,System.Nullable{System.Int32},System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
                Creates a database indicated by connection and creates schema objects (tables, 
                primary keys, foreign keys) based on the contents of a 
                <see cref="T:System.Data.Metadata.Edm.StoreItemCollection" />.
            </summary>
            <param name="connection">
                Connection to a non-existent database that needs to be created and populated 
                with the store objects indicated with the storeItemCollection parameter.
            </param>
            <param name="commandTimeout">
                Execution timeout for any commands needed to create the database.
            </param>
            <param name="storeItemCollection">
                The collection of all store items based on which the script should be created.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.DbDeleteDatabase(System.Data.Common.DbConnection,System.Nullable{System.Int32},System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
                Deletes all store objects specified in the store item collection from the 
                database and the database itself.
            </summary>
            <param name="connection">
                Connection to an existing database that needs to be deleted.
            </param>
            <param name="commandTimeout">
                Execution timeout for any commands needed to delete the database.
            </param>
            <param name="storeItemCollection">
                The structure of the database to be deleted.
            </param>
        </member>
        <member name="M:Effort.Provider.EffortProviderServices.DbCreateDatabaseScript(System.String,System.Data.Entity.Core.Metadata.Edm.StoreItemCollection)">
            <summary>
                Generates a data definition language (DDL0 script that creates schema objects 
                (tables, primary keys, foreign keys) based on the contents of the 
                <see cref="T:System.Data.Metadata.Edm.StoreItemCollection" /> parameter and 
                targeted for the version of the database corresponding to the provider manifest 
                token.
            </summary>
            <param name="providerManifestToken">
                The provider manifest token identifying the target version.
            </param>
            <param name="storeItemCollection">
                The structure of the database.
            </param>
            <returns>
                A DDL script that creates schema objects based on the contents of the 
                <see cref="T:System.Data.Metadata.Edm.StoreItemCollection" /> parameter and 
                targeted for the version of the database corresponding to the provider manifest 
                token.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortTransaction">
            <summary>
                Represents an Effort transaction. This class cannot be inherited.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortTransaction.#ctor(Effort.Provider.EffortConnection,System.Data.IsolationLevel)">
            <summary>
                Initializes a new instance of the <see cref="T:Effort.Provider.EffortTransaction"/> class.
            </summary>
            <param name="connection">
                The <see cref="T:Effort.Provider.EffortTransaction"/> object.
            </param>
            <param name="isolationLevel">
                The isolation level.
            </param>
            <exception cref="T:System.InvalidOperationException">
                Ambient transaction is already set.
            </exception>
        </member>
        <member name="M:Effort.Provider.EffortTransaction.Commit">
            <summary>
                Commits the database transaction.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortTransaction.Rollback">
            <summary>
                Rolls back a transaction from a pending state.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortTransaction.Dispose(System.Boolean)">
            <summary>
                Releases the unmanaged resources used by the <see cref="T:EffortTransaction" />
                and optionally releases the managed resources.
            </summary>
            <param name="disposing">
                If true, this method releases all resources held by any managed objects that 
                this <see cref="T:EffortTransaction" /> references.
            </param>
        </member>
        <member name="P:Effort.Provider.EffortTransaction.IsolationLevel">
            <summary>
                Specifies the <see cref="T:System.Data.IsolationLevel" /> for this transaction.
            </summary>
            <returns>
                The <see cref="T:System.Data.IsolationLevel" /> for this transaction.
            </returns>
        </member>
        <member name="P:Effort.Provider.EffortTransaction.InternalTransaction">
            <summary>
                Gets the internal NMemory transaction object.
            </summary>
            <value>
                The NMemory transaction object.
            </value>
        </member>
        <member name="P:Effort.Provider.EffortTransaction.DbConnection">
            <summary>
                Gets the <see cref="T:EffortConnection" /> object associated with the 
                transaction.
            </summary>
            <returns>
                The <see cref="T:EffortConnection" /> object associated with the transaction.
            </returns>
        </member>
        <member name="T:Effort.Provider.EffortVersion">
            <summary>
                Specifies a supported available provider manifest token value.
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortVersion.Version1">
            <summary>
                Value that represents the "Version1" provider manifest token value.
            </summary>
        </member>
        <member name="T:Effort.Provider.EffortProviderManifestTokens">
            <summary>
                Provides the supported Effort provider manifest token values.
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortProviderManifestTokens.Version1">
            <summary>
                The Version1 provider manifest token.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortProviderManifestTokens.GetVersion(System.String)">
            <summary>
                Gets the <see cref="T:Effort.Provider.EffortVersion"/> enumeration value that represents the 
                provided manifest token value.
            </summary>
            <param name="manifestToken">
                The value of the manifest token.
            </param>
            <returns>
                The <see cref="T:Effort.Provider.EffortVersion"/> value.
            </returns>
            <exception cref="T:System.NotSupportedException">
                The manifest token is not supported
            </exception>
        </member>
        <member name="T:Effort.Provider.EffortProviderInvariantName">
            <summary>
            Provides the invariant name of the Effort provider.
            </summary>
        </member>
        <member name="F:Effort.Provider.EffortProviderInvariantName.Instance">
            <summary>
                Provides a singleton instance of the <see cref="T:Effort.Provider.EffortProviderInvariantName"/>
                class.
            </summary>
        </member>
        <member name="M:Effort.Provider.EffortProviderInvariantName.#ctor">
            <summary>
            Prevents a default instance of the <see cref="T:Effort.Provider.EffortProviderInvariantName"/> class 
            from being created.
            </summary>
        </member>
        <member name="P:Effort.Provider.EffortProviderInvariantName.Name">
            <summary>
            Gets the invariant name of the Effort provider.
            </summary>
            <value>
            The invariant name.
            </value>
        </member>
    </members>
</doc>
