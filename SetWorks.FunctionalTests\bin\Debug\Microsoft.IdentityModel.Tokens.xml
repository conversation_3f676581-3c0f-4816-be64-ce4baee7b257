<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.IdentityModel.Tokens</name>
    </assembly>
    <members>
        <member name="T:Microsoft.IdentityModel.Tokens.AsymmetricAdapter">
            <summary>
            This adapter abstracts the 'RSA' differences between versions of .Net targets.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricAdapter.Dispose">
            <summary>
            Calls <see cref="M:Microsoft.IdentityModel.Tokens.AsymmetricAdapter.Dispose(System.Boolean)"/> and <see cref="M:System.GC.SuppressFinalize(System.Object)"/>
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey">
            <summary>
            Base class for a Security Key that contains Asymmetric key material.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey.HasPrivateKey">
            <summary>
            This must be overridden to get a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey.PrivateKeyStatus">
            <summary>
            Gets the status of the private key.
            </summary>
            <return>'Exists' if private key exists for sure; 'DoesNotExist' if private key doesn't exist for sure; 'Unknown' if we cannot determine.</return>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.PrivateKeyStatus">
            <summary>
            Enum for the existence of private key
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.PrivateKeyStatus.Exists">
            <summary>
            private key exists for sure
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.PrivateKeyStatus.DoesNotExist">
            <summary>
            private key doesn't exist for sure
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.PrivateKeyStatus.Unknown">
            <summary>
            unable to determine the existence of private key
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider">
            <summary>
            Provides signature and verification operations for Asymmetric Algorithms using a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.DefaultMinimumAsymmetricKeySizeInBitsForSigningMap">
            <summary>
            Mapping from algorithm to minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize when creating signatures.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.DefaultMinimumAsymmetricKeySizeInBitsForVerifyingMap">
            <summary>
            Mapping from algorithm to minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize when verifying signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> class used to create and verify signatures.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.<see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/></param>
            <param name="algorithm">The signature algorithm to apply.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> class used to create and verify signatures.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <param name="willCreateSignatures">If this <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider"/> is required to create signatures then set this to true.</param>
            <para>
            Creating signatures requires that the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> has access to a private key.
            Verifying signatures (the default), does not require access to the private key.
            </para>
            <exception cref="T:System.ArgumentNullException"><paramref name="key"/>is null.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="algorithm"/>is null or empty.</exception>
            <exception cref="T:System.InvalidOperationException"><paramref name="willCreateSignatures"/>is true and there is no private key.</exception>
            <exception cref="T:System.NotSupportedException">If <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            willCreateSignatures is true and <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.KeySize is less than the size corresponding to the given algorithm in <see cref="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForSigningMap"/>.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.KeySize is less than the size corresponding to the algorithm in <see cref="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForVerifyingMap"/>. Note: this is always checked.
            </exception>
            <exception cref="T:System.InvalidOperationException">If the runtime is unable to create a suitable cryptographic provider.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForSigningMap">
            <summary>
            Gets the mapping from algorithm to the minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize for creating signatures.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForVerifyingMap">
            <summary>
            Gets the mapping from algorithm to the minimum <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/>.KeySize for verifying signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.GetHashAlgorithmName(System.String)">
            <summary>
            Creating a Signature requires the use of a <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.
            This method returns the <see cref="T:System.Security.Cryptography.HashAlgorithmName"/>
            that describes the <see cref="T:System.Security.Cryptography.HashAlgorithm"/>to use when generating a Signature.
            </summary>
            <param name="algorithm">The SignatureAlgorithm in use.</param>
            <returns>The <see cref="T:System.Security.Cryptography.HashAlgorithmName"/> to use.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="algorithm"/> is null or whitespace.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">if <paramref name="algorithm"/> is not supported.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.ObjectPoolSize">
            <summary>
            For testing purposes
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Sign(System.Byte[])">
            <summary>
            Produces a signature over the 'input' using the <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> and algorithm passed to <see cref="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)"/>.
            </summary>
            <param name="input">The bytes to be signed.</param>
            <returns>A signature over the input.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/>.Length == 0.</exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called. </exception>
            <remarks>Sign is thread safe.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.ValidateAsymmetricSecurityKeySize(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Validates that an asymmetric key size is of sufficient size for a SignatureAlgorithm.
            </summary>
            <param name="key">The asymmetric key to validate.</param>
            <param name="algorithm">Algorithm for which this key will be used.</param>
            <param name="willCreateSignatures">Whether they key will be used for creating signatures.</param>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/>is null.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="algorithm"/>is null or empty.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">if <paramref name="key"/>.KeySize is less than the minimum
            acceptable size.</exception>
            <remarks>
            <seealso cref="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForSigningMap"/> for minimum signing sizes.
            <seealso cref="P:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.MinimumAsymmetricKeySizeInBitsForVerifyingMap"/> for minimum verifying sizes.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            Verifies that the <paramref name="signature"/> over <paramref name="input"/> using the
            <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and <see cref="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Algorithm"/> specified by this
            <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> are consistent.
            </summary>
            <param name="input">The bytes to generate the signature over.</param>
            <param name="signature">The value to verify against.</param>
            <returns>true if signature matches, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="input"/> is null or has length == 0.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="signature"/> is null or has length == 0.</exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called. </exception>
            <remarks>Verify is thread safe.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Verify(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AsymmetricSignatureProvider.Dispose(System.Boolean)">
            <summary>
            Calls to release managed resources.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Base64UrlEncoder">
            <summary>
            Encodes and Decodes strings as Base64Url encoding.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.String)">
            <summary>
            The following functions perform base64url encoding which differs from regular base64 encoding as follows
            * padding is skipped so the pad character '=' doesn't have to be percent encoded
            * the 62nd and 63rd regular base64 encoding characters ('+' and '/') are replace with ('-' and '_')
            The changes make the encoding alphabet file and URL safe.
            </summary>
            <param name="arg">string to encode.</param>
            <returns>Base64Url encoding of the UTF8 bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Converts a subset of an array of 8-bit unsigned integers to its equivalent string representation which is encoded with base-64-url digits. Parameters specify
            the subset as an offset in the input array, and the number of elements in the array to convert.
            </summary>
            <param name="inArray">An array of 8-bit unsigned integers.</param>
            <param name="length">An offset in inArray.</param>
            <param name="offset">The number of elements of inArray to convert.</param>
            <returns>The string representation in base 64 url encoding of length elements of inArray, starting at position offset.</returns>
            <exception cref="T:System.ArgumentNullException">'inArray' is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or length is negative OR offset plus length is greater than the length of inArray.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Encode(System.Byte[])">
            <summary>
            Converts a subset of an array of 8-bit unsigned integers to its equivalent string representation which is encoded with base-64-url digits.
            </summary>
            <param name="inArray">An array of 8-bit unsigned integers.</param>
            <returns>The string representation in base 64 url encoding of length elements of inArray, starting at position offset.</returns>
            <exception cref="T:System.ArgumentNullException">'inArray' is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or length is negative OR offset plus length is greater than the length of inArray.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.DecodeBytes(System.String)">
            <summary>
            Converts the specified string, base-64-url encoded to utf8 bytes.</summary>
            <param name="str">base64Url encoded string.</param>
            <returns>UTF8 bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoder.Decode(System.String)">
            <summary>
            Decodes the string from Base64UrlEncoded to UTF8.
            </summary>
            <param name="arg">string to decode.</param>
            <returns>UTF8 string.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Base64UrlEncoding">
            <summary>
            Base64 encode/decode implementation for as per https://tools.ietf.org/html/rfc4648#section-5.
            Uses ArrayPool[T] to minimize memory usage.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Decode(System.String)">
            <summary>
            Decodes a Base64UrlEncoded string into a byte array.
            </summary>
            <param name="inputString">The string to decode.</param>
            <returns>Decoded bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Decode(System.String,System.Int32,System.Int32)">
            <summary>
            Decodes a Base64UrlEncoded string into a byte array.
            </summary>
            <param name="input">String to decode.</param>
            <param name="offset">Index of char in <paramref name="input"/> to start decode operation.</param>
            <param name="length">Number of chars in <paramref name="input"/> to decode.</param>
            <returns>Decoded bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Decode``2(System.String,System.Int32,System.Int32,``1,System.Func{System.Byte[],System.Int32,``1,``0})">
            <summary>
            Decodes a Base64UrlEncoded string and then performs an action.
            </summary>
            <param name="input">String to decode.</param>
            <param name="offset">Index of char in <paramref name="input"/> to start decode operation.</param>
            <param name="length">Number of chars in <paramref name="input"/> to decode from <paramref name="offset"/>.</param>
            <param name="argx">Input parameter to action.</param>
            <param name="action">Action to perform on decoded bytes.</param>
            <typeparam name="T">Output type of decoding action.</typeparam>
            <typeparam name="TX">Type of Input parameter to action.</typeparam>
            <returns>Instance of {T}.</returns>
            <remarks>
            The buffer for the decode operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            The result of <see cref="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.ValidateAndGetOutputSize(System.String,System.Int32,System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Decode``1(System.String,System.Int32,System.Int32,System.Func{System.Byte[],System.Int32,``0})">
            <summary>
            Decodes a Base64UrlEncoded string and then performs an action.
            </summary>
            <param name="input">The string to decode.</param>
            <param name="offset">Index of char in <paramref name="input"/> to start decode operation from.</param>
            <param name="length">Count of char in <paramref name="input"/> to decode.</param>
            <param name="action">Action to perform on decoded bytes.</param>
            <typeparam name="T">Return type of operation.</typeparam>
            <returns>Instance of {T}.</returns>
            <remarks>
            The buffer for the decode operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            The result of <see cref="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.ValidateAndGetOutputSize(System.String,System.Int32,System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Decode``4(System.String,System.Int32,System.Int32,``1,``2,``3,System.Func{System.Byte[],System.Int32,``1,``2,``3,``0})">
            <summary>
            Decodes a Base64UrlEncoded string and then performs an action.
            </summary>
            <param name="input">The string to decode.</param>
            <param name="offset">Index of char in <paramref name="input"/> to start decode operation from.</param>
            <param name="length">Count of char in <paramref name="input"/> to decode.</param>
            <param name="argx">Input parameter 1 to action.</param>
            <param name="argy">Input parameter 2 to action.</param>
            <param name="argz">Input parameter 3 to action.</param>
            <param name="action">Action to perform on decoded bytes.</param>
            <typeparam name="T">Output type of decoding action.</typeparam>
            <typeparam name="TX">Type of Input parameter 1 to action.</typeparam>
            <typeparam name="TY">Type of Input parameter 2 to action.</typeparam>
            <typeparam name="TZ">Type of Input parameter 3 to action.</typeparam>
            <returns>Instance of {T}.</returns>
            <remarks>
            The buffer for the decode operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            The result of <see cref="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.ValidateAndGetOutputSize(System.String,System.Int32,System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Decode(System.String,System.Int32,System.Int32,System.Byte[])">
            <summary>
            Decodes a Base64UrlEncoded string into a byte array.
            </summary>
            <param name="input">String to decode.</param>
            <param name="offset">Index of char in <paramref name="input"/> to start decode operation.</param>
            <param name="length">Number of chars in <paramref name="input"/> to decode.</param>
            <param name="output">byte array to place results.</param>
            <remarks>
            Changes from Base64UrlEncoder implementation
            1. Padding is optional.
            2. '+' and '-' are treated the same.
            3. '/' and '_' are treated the same.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Encode(System.Byte[])">
            <summary>
            Encode byte array to Base64UrlEncoded string.
            </summary>
            <param name="bytes">Bytes to encode.</param>
            <returns>Base64Url encoded string.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.Encode(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Encode byte array to Base64UrlEncoded string.
            </summary>
            <param name="input">Bytes to encode.</param>
            <param name="offset">Index into <paramref name="input"/> to start encode operation.</param>
            <param name="length">Number of bytes in <paramref name="input"/> to encode, starting from offset.</param>
            <returns>Base64Url encoded string.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Base64UrlEncoding.ValidateAndGetOutputSize(System.String,System.Int32,System.Int32)">
            <summary>
            Validates the input string for decode operation.
            </summary>
            <param name="inputString">String to validate.</param>
            <param name="offset">Index of char in <paramref name="inputString"/> to start decode operation.</param>
            <param name="length">Number of chars in <paramref name="inputString"/> to decode, starting from offset.</param>
            <returns>Size of the decoded bytes arrays.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.BaseConfiguration">
            <summary>
             Represents a generic metadata configuration which is applicable for both XML and JSON based configurations.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.Issuer">
            <summary>
            Gets the issuer specified via the metadata endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.SigningKeys">
            <summary>
            Gets the <see cref="T:System.Collections.Generic.ICollection`1"/> that the IdentityProvider indicates are to be used in order to sign tokens.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.TokenEndpoint">
            <summary>
            Gets or sets the token endpoint specified via the metadata endpoint.
            This is the fed:PassiveRequestorEndpoint in WS-Federation, https://docs.oasis-open.org/wsfed/federation/v1.2/os/ws-federation-1.2-spec-os.html#:~:text=fed%3ASecurityTokenServiceType/fed%3APassiveRequestorEndpoint
            Or the token_endpoint in the OIDC metadata.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.ActiveTokenEndpoint">
            <summary>
            Gets or sets the token endpoint specified via the metadata endpoint.
            This is the fed:SecurityTokenServiceType in WS-Federation, http://docs.oasis-open.org/wsfed/federation/v1.2/os/ws-federation-1.2-spec-os.html#:~:text=fed%3ASecurityTokenSerivceEndpoint
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.TokenDecryptionKeys">
            <summary>
            Gets the <see cref="T:System.Collections.Generic.ICollection`1"/> that the IdentityProvider indicates are to be used in order to decrypt tokens.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.BaseConfigurationComparer">
            <summary>
            Comparison class for a <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/>.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.BaseConfigurationManager">
            <summary>
            Represents a generic configuration manager.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.AutomaticRefreshInterval">
            <summary>
            Gets or sets the <see cref="T:System.TimeSpan"/> that controls how often an automatic metadata refresh should occur.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.DefaultAutomaticRefreshInterval">
            <summary>
            12 hours is the default time interval that afterwards will obtain new configuration.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.DefaultLastKnownGoodConfigurationLifetime">
            <summary>
            1 hour is the default time interval that a last known good configuration will last for.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.DefaultRefreshInterval">
            <summary>
            5 minutes is the default time interval that must pass for <see cref="M:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.RequestRefresh"/> to obtain a new configuration.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.#ctor">
            <summary>
            The default constructor.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.#ctor(Microsoft.IdentityModel.Tokens.Configuration.LKGConfigurationCacheOptions)">
            <summary>
            Constructor.
            </summary>
            <param name="options">The event queue task creation option.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.GetBaseConfigurationAsync(System.Threading.CancellationToken)">
            <summary>
            Obtains an updated version of <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> if the appropriate refresh interval has passed.
            This method may return a cached version of the configuration.
            </summary>
            <param name="cancel">CancellationToken</param>
            <returns>Configuration of type Configuration.</returns>
            <remarks>This method on the base class throws a <see cref="T:System.NotImplementedException"/> as it is meant to be
            overridden by the class that extends it.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.GetValidLkgConfigurations">
            <summary>
            Gets all valid last known good configurations.
            </summary>
            <returns>A collection of all valid last known good configurations.</returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.LastKnownGoodConfiguration">
            <summary>
            The last known good configuration or LKG (a configuration retrieved in the past that we were able to successfully validate a token against).
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.LastKnownGoodLifetime">
            <summary>
            The length of time that a last known good configuration is valid for.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.MetadataAddress">
            <summary>
            The metadata address to retrieve the configuration from.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.MinimumAutomaticRefreshInterval">
            <summary>
            5 minutes is the minimum value for automatic refresh. <see cref="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.AutomaticRefreshInterval"/> can not be set less than this value.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.MinimumRefreshInterval">
            <summary>
            1 second is the minimum time interval that must pass for <see cref="M:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.RequestRefresh"/> to  obtain new configuration.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.RefreshInterval">
            <summary>
            The minimum time between retrievals, in the event that a retrieval failed, or that a refresh was explicitly requested.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.UseLastKnownGoodConfiguration">
            <summary>
            Indicates whether the last known good feature should be used, true by default.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.IsLastKnownGoodValid">
            <summary>
            Indicates whether the last known good configuration is still fresh, depends on when the LKG was first used and it's lifetime.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.BaseConfigurationManager.RequestRefresh">
            <summary>
            Indicate that the configuration may be stale (as indicated by failing to process incoming tokens).
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CallContext">
            <summary>
            An opaque context used to store work when working with authentication artifacts.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CallContext.#ctor">
            <summary>
            Instantiates a new <see cref="T:Microsoft.IdentityModel.Tokens.CallContext"/> with a default activityId.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CallContext.#ctor(System.Guid)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.IdentityModel.Tokens.CallContext"/> with an activityId.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CollectionUtilities">
            <summary>
            A class which contains useful methods for processing collections.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CollectionUtilities.IsNullOrEmpty``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Checks whether <paramref name="enumerable"/> is null or empty.
            </summary>
            <typeparam name="T">The type of the <paramref name="enumerable"/>.</typeparam>
            <param name="enumerable">The <see cref="T:System.Collections.Generic.IEnumerable`1"/> to be checked.</param>
            <returns>True if <paramref name="enumerable"/> is null or empty, false otherwise.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CompressionAlgorithms">
            <summary>
            Constants for compression algorithms.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CompressionProviderFactory">
            <summary>
            Compression provider factory for compression and decompression.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CompressionProviderFactory.#cctor">
            <summary>
            Static constructor that initializes the default <see cref="T:Microsoft.IdentityModel.Tokens.CompressionProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CompressionProviderFactory.#ctor">
            <summary>
            Default constructor for <see cref="T:Microsoft.IdentityModel.Tokens.CompressionProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CompressionProviderFactory.#ctor(Microsoft.IdentityModel.Tokens.CompressionProviderFactory)">
            <summary>
            Constructor that creates a deep copy of given <see cref="T:Microsoft.IdentityModel.Tokens.CompressionProviderFactory"/> object.
            </summary>
            <param name="other"><see cref="T:Microsoft.IdentityModel.Tokens.CompressionProviderFactory"/> to copy from.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CompressionProviderFactory.Default">
            <summary>
            Returns the default <see cref="T:Microsoft.IdentityModel.Tokens.CompressionProviderFactory"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CompressionProviderFactory.CustomCompressionProvider">
            <summary>
            Extensibility point for custom compression support application wide.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CompressionProviderFactory.IsSupportedAlgorithm(System.String)">
            <summary>
            Answers if an algorithm is supported.
            </summary>
            <param name="algorithm">the name of the crypto algorithm.</param>
            <returns>true if the algorithm is supported, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CompressionProviderFactory.CreateCompressionProvider(System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.IdentityModel.Tokens.ICompressionProvider"/> for a specific algorithm.
            </summary>
            <param name="algorithm">the decompression algorithm.</param>
            <returns>a <see cref="T:Microsoft.IdentityModel.Tokens.ICompressionProvider"/>.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Configuration.LKGConfigurationCacheOptions">
            <summary>
            Defines the options which can be used to configure the internal LKG configuration cache.
            See <see cref="T:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2"/> for more details.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.Configuration.LKGConfigurationCacheOptions.DefaultLKGConfigurationSizeLimit">
            <summary>
            10 is the default size limit of the cache (in number of items) for last known good configuration.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.Configuration.LKGConfigurationCacheOptions.BaseConfigurationComparer">
            <summary>
            Gets or sets the BaseConfgiurationComparer that to compare <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.Configuration.LKGConfigurationCacheOptions.LastKnownGoodConfigurationSizeLimit">
            <summary>
            The size limit of the cache (in number of items) for last known good configuration.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.Configuration.LKGConfigurationCacheOptions.TaskCreationOptions">
            <summary>
            The event queue task creation option, default to None instead of LongRunning as LongRunning will always start a task on a new thread instead of a thread from ThreadPool.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.Configuration.LKGConfigurationCacheOptions.RemoveExpiredValues">
            <summary>
            Whether or not to remove expired items.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CryptoProviderCache">
            <summary>
            Definition of cache for crypto providers
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderCache.GetCacheKey(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Returns the cache key to use when looking up an entry into the cache for a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider" />
            </summary>
            <param name="signatureProvider">the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to create the key for.</param>
            <returns>the cache key to use for finding a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderCache.GetCacheKey(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.String)">
            <summary>
            Returns the 'key' that will be used to find a crypto provider in this cache.
            </summary>
            <param name="securityKey">the key that is used to by the crypto provider.</param>
            <param name="algorithm">the algorithm that is used by the crypto provider.</param>
            <param name="typeofProvider">the typeof the crypto provider obtained by calling object.GetType().</param>
            <returns>the cache key to use for finding a crypto provider.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderCache.TryAdd(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Trys to adds a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to this cache.
            </summary>
            <param name="signatureProvider"><see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to cache.</param>
            <returns>true if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was added, false if the cache already contained the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderCache.TryGetSignatureProvider(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.String,System.Boolean,Microsoft.IdentityModel.Tokens.SignatureProvider@)">
            <summary>
            Trys to find a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> in this cache.
            </summary>
            <param name="securityKey">the key that is used to by the crypto provider.</param>
            <param name="algorithm">the algorithm that is used by the crypto provider.</param>
            <param name="typeofProvider">the typeof the crypto provider obtained by calling object.GetType().</param>
            <param name="willCreateSignatures">a bool to indicate if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> will be used to sign.</param>
            <param name="signatureProvider">the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> if found.</param>
            <returns>true if a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was found, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderCache.TryRemove(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Trys to remove a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> from this cache.
            </summary>
            <param name="signatureProvider"><see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to remove.</param>
            <returns>true if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was removed, false if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was not found.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CryptoProviderCacheOptions">
            <summary>
            Specifies the CryptoProviderCacheOptions which can be used to configure the internal cryptoprovider cache.
            We are using our own simple LRU caching implementation across all targets. 
            See <see cref="T:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2"/> for more details.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.CryptoProviderCacheOptions.DefaultSizeLimit">
            <summary>
            Default value for <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderCacheOptions.SizeLimit"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderCacheOptions.SizeLimit">
            <summary>
            Gets or sets the size of the cache (in number of items). 
            20% of the cache will be evicted whenever the cache gets to 95% of this size.
            Items will be evicted from least recently used to most recently used.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory">
            <summary>
            Creates cryptographic operators by specifying a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>'s and algorithms.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.Default">
            <summary>
            Returns the default <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.DefaultCacheSignatureProviders">
            <summary>
            Gets or sets the default value for caching of <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>'s.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.DefaultSignatureProviderObjectPoolCacheSize">
            <summary>
            Gets or sets the maximum size of the object pool used by the SignatureProvider that are used for crypto objects.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.#cctor">
            <summary>
            Static constructor that initializes the default <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.#ctor">
            <summary>
            Default constructor for <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.#ctor(Microsoft.IdentityModel.Tokens.CryptoProviderCache)">
            <summary>
            Initializes an instance of a <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/>.
            </summary>
            <param name="cache">
            The cache to use for caching CryptoProviders
            </param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.#ctor(Microsoft.IdentityModel.Tokens.CryptoProviderFactory)">
            <summary>
            Constructor that creates a deep copy of given <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/> object.
            </summary>
            <param name="other"><see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/> to copy from.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CryptoProviderCache">
            <summary>
            Gets the <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CryptoProviderCache"/>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider">
            <summary>
            Extensibility point for creating custom cryptographic operators.
            </summary>
            <remarks>By default, if set, <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> will be called before creating cryptographic operators.
            If true is returned, then <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> will be called. The <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/> will throw if the
            Cryptographic operator returned is not of the correct type.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CacheSignatureProviders">
            <summary>
            Gets or sets a bool controlling if <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> should be cached.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.SignatureProviderObjectPoolCacheSize">
            <summary>
            Gets or sets the maximum size of the object pool used by the SignatureProvider that are used for crypto objects.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateAuthenticatedEncryptionProvider(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/> for a specific &lt;SecurityKey, Algorithm>.
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use.</param>
            <param name="algorithm">the algorithm to use.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentException">thrown if <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/>.</exception>
            <remarks>
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/>.
            </para>
            <para>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseKeyWrapProvider(Microsoft.IdentityModel.Tokens.KeyWrapProvider)"/>.</para>
            </remarks>
            <returns>an instance of <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateKeyWrapProvider(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> for a specific &lt;SecurityKey, Algorithm>.
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use.</param>
            <param name="algorithm">the algorithm to use.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.NotSupportedException">thrown if <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/>.</exception>
            <remarks>
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/>.
            </para>
            <para>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseKeyWrapProvider(Microsoft.IdentityModel.Tokens.KeyWrapProvider)"/>.</para>
            </remarks>
            <returns>an instance of <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateKeyWrapProviderForUnwrap(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> for a specific &lt;SecurityKey, Algorithm>.
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use.</param>
            <param name="algorithm">the algorithm to use.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentException">thrown if <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/>.</exception>
            <remarks>
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/>.
            </para>
            <para>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseKeyWrapProvider(Microsoft.IdentityModel.Tokens.KeyWrapProvider)"/>.</para>
            </remarks>
            <returns>an instance of <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateForSigning(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that creates a signature with the algorithm and <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use for signing.</param>
            <param name="algorithm">the algorithm to use for signing.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">thrown if <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeySize"/> is too small.</exception>
            <exception cref="T:System.NotSupportedException">thrown if <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> or <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <exception cref="T:System.NotSupportedException">thrown if the key / algorithm is not supported.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.</exception>
            <remarks>
            <para>AsymmetricSignatureProviders require access to a PrivateKey for Signing.</para>
            <para>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)"/>.</para>
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.
            </para>
            </remarks>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that can be used to create a signature using the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateForSigning(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Creates a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that creates a signature with the algorithm and <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use for signing.</param>
            <param name="algorithm">the algorithm to use for signing.</param>
            <param name="cacheProvider">indicates if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> should be cached for reuse.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">thrown if <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeySize"/> is too small.</exception>
            <exception cref="T:System.NotSupportedException">thrown if <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> or <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <exception cref="T:System.NotSupportedException">thrown if the key / algorithm is not supported.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.</exception>
            <remarks>
            <para>AsymmetricSignatureProviders require access to a PrivateKey for Signing.</para>
            <para>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)"/>.</para>
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.
            </para>
            </remarks>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that can be used to create a signature using the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateForVerifying(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that supports the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use for signature verification.</param>
            <param name="algorithm">The algorithm to use for verifying.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">thrown if <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeySize"/> is too small.</exception>
            <exception cref="T:System.NotSupportedException">thrown if <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> or <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <exception cref="T:System.NotSupportedException">thrown if the key / algorithm is not supported.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.</exception>
            <remarks>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)"/>.
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.
            </para>
            </remarks>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that can be used to validate a signature using the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateForVerifying(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Creates a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that supports the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use for signature verification.</param>
            <param name="algorithm">The algorithm to use for verifying.</param>
            <param name="cacheProvider">should the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> be cached.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">thrown if <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeySize"/> is too small.</exception>
            <exception cref="T:System.NotSupportedException">thrown if <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> or <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <exception cref="T:System.NotSupportedException">thrown if the key / algorithm is not supported.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.</exception>
            <remarks>When finished with the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)"/>.
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.
            </para>
            </remarks>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> that can be used to validate a signature using the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateHashAlgorithm(System.Security.Cryptography.HashAlgorithmName)">
            <summary>
            Creates a <see cref="T:System.Security.Cryptography.HashAlgorithm"/> for a specific algorithm.
            </summary>
            <param name="algorithm">the name of the hash algorithm to create.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.</exception>
            <exception cref="T:System.NotSupportedException">thrown if <paramref name="algorithm"/> is not supported.</exception>
            <remarks>When finished with the <see cref="T:System.Security.Cryptography.HashAlgorithm"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseHashAlgorithm(System.Security.Cryptography.HashAlgorithm)"/>.
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.
            </para>
            </remarks>
            <returns>A <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateHashAlgorithm(System.String)">
            <summary>
            Creates a <see cref="T:System.Security.Cryptography.HashAlgorithm"/> for a specific algorithm.
            </summary>
            <param name="algorithm">the name of the hash algorithm to create.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.</exception>
            <exception cref="T:System.NotSupportedException">thrown if <paramref name="algorithm"/> is not supported.</exception>
            <remarks>When finished with the <see cref="T:System.Security.Cryptography.HashAlgorithm"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseHashAlgorithm(System.Security.Cryptography.HashAlgorithm)"/>.
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.
            </para>
            </remarks>
            <returns>A <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CreateKeyedHashAlgorithm(System.Byte[],System.String)">
            <summary>
            Returns a <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> for a specific algorithm.
            </summary>
            <param name="keyBytes">bytes to use to create the Keyed Hash.</param>
            <param name="algorithm">the name of the keyed hash algorithm to create.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="keyBytes"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.InvalidOperationException">thrown if <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> returns a type that is not assignable from <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>.</exception>
            <exception cref="T:System.NotSupportedException"><paramref name="algorithm"/> is not supported.</exception>
            <remarks>When finished with the <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> call <see cref="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseHashAlgorithm(System.Security.Cryptography.HashAlgorithm)"/>.
            <para>If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/> is called to obtain the <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>.
            </para>
            </remarks>
            <returns>A <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ShouldCacheSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            For some security key types, in some runtimes, it's not possible to extract public key material and create an <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.InternalId"/>.
            In these cases, <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.InternalId"/> will be an empty string, and these keys should not be cached.
            </summary>
            <param name="signatureProvider"><see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to be examined.</param>
            <returns><c>True</c> if <paramref name="signatureProvider"/> should be cached, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.IsSupportedAlgorithm(System.String)">
            <summary>
            Checks if an algorithm is supported.
            </summary>
            <param name="algorithm">the name of the Hash algorithm.</param>
            <remarks>Only considers known Hash algorithms.</remarks>
            <returns>true if:
            <para>
            If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            </para>
            <para>The algorithm is supported.
            </para>
            </returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.IsSupportedAlgorithm(System.String,Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Checks if the algorithm and <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is supported.
            </summary>
            <param name="algorithm">the security algorithm to apply.</param>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.</param>
            <remarks>Algorithms are supported for specific key types.
            For example:
            <para><see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/> and <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> will return true.</para>
            <para><see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/> and <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> will return false.</para>
            </remarks>
            <returns>true if:
            <para>
            If <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> is set and <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])"/> returns true.
            </para>
            <para>The algorithm / key pair is supported.
            </para>
            </returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseHashAlgorithm(System.Security.Cryptography.HashAlgorithm)">
            <summary>
            When finished with a <see cref="T:System.Security.Cryptography.HashAlgorithm"/> call this method for cleanup. The default behavior is to call <see cref="M:System.Security.Cryptography.HashAlgorithm.Dispose"/>
            </summary>
            <param name="hashAlgorithm"><see cref="T:System.Security.Cryptography.HashAlgorithm"/> to be released.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="hashAlgorithm"/> is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseKeyWrapProvider(Microsoft.IdentityModel.Tokens.KeyWrapProvider)">
            <summary>
            When finished with a <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> call this method for cleanup."/>
            </summary>
            <param name="provider"><see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> to be released.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="provider"/> is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseRsaKeyWrapProvider(Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider)">
            <summary>
            When finished with a <see cref="T:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider"/> call this method for cleanup."/>
            </summary>
            <param name="provider"><see cref="T:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider"/> to be released.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="provider"/> is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.ReleaseSignatureProvider(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            When finished with a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> call this method for cleanup. The default behavior is to call <see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose"/>
            </summary>
            <param name="signatureProvider"><see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to be released.</param>
            <exception cref="T:System.ArgumentNullException">thrown if <paramref name="signatureProvider"/> is null.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.DateTimeUtil">
            <summary>
            Helper class for adding DateTimes and Timespans.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.Add(System.DateTime,System.TimeSpan)">
            <summary>
            Add a DateTime and a TimeSpan.
            The maximum time is DateTime.MaxTime.  It is not an error if time + timespan > MaxTime.
            Just return MaxTime.
            </summary>
            <param name="time">Initial <see cref="T:System.DateTime"/> value.</param>
            <param name="timespan"><see cref="T:System.TimeSpan"/> to add.</param>
            <returns><see cref="T:System.DateTime"/> as the sum of time and timespan.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.GetMaxValue(System.DateTimeKind)">
            <summary>
            Gets the Maximum value for a DateTime specifying kind.
            </summary>
            <param name="kind">DateTimeKind to use.</param>
            <returns>DateTime of specified kind.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.GetMinValue(System.DateTimeKind)">
            <summary>
            Gets the Minimum value for a DateTime specifying kind.
            </summary>
            <param name="kind">DateTimeKind to use.</param>
            <returns>DateTime of specified kind.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.ToUniversalTime(System.Nullable{System.DateTime})">
            <summary>
            Ensures that DataTime is UTC.
            </summary>
            <param name="value"><see cref="T:System.DateTime"/>to convert.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DateTimeUtil.ToUniversalTime(System.DateTime)">
            <summary>
            Ensures that DateTime is UTC.
            </summary>
            <param name="value"><see cref="T:System.DateTime"/>to convert.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider">
            <summary>
            A compression provider that supports compression and decompression using the <see cref="F:Microsoft.IdentityModel.Tokens.CompressionAlgorithms.Deflate"/> algorithm.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider"/> class used to compress and decompress used the <see cref="F:Microsoft.IdentityModel.Tokens.CompressionAlgorithms.Deflate"/> algorithm.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider.#ctor(System.IO.Compression.CompressionLevel)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider"/> class used to compress and decompress used the <see cref="F:Microsoft.IdentityModel.Tokens.CompressionAlgorithms.Deflate"/> algorithm.
            <param name="compressionLevel">The compression level to use when compressing.</param>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider.Algorithm">
            <summary>
            Gets the compression algorithm.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider.CompressionLevel">
            <summary>
            Specifies whether compression should emphasize speed or compression size.
            Set to <see cref="F:System.IO.Compression.CompressionLevel.Optimal"/> by default.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider.Decompress(System.Byte[])">
            <summary>
            Decompress the value using DEFLATE algorithm.
            </summary>
            <param name="value">the bytes to decompress.</param>
            <returns>the decompressed bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider.Compress(System.Byte[])">
            <summary>
            Compress the value using the DEFLATE algorithm.
            </summary>
            <param name="value">the bytes to compress.</param>
            <returns>the compressed bytes.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DeflateCompressionProvider.IsSupportedAlgorithm(System.String)">
            <summary>
            Answers if a compression algorithm is supported.
            </summary>
            <param name="algorithm">the name of the compression algorithm.</param>
            <returns>true if the compression algorithm is supported, false otherwise.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ECDsaAdapter">
            <summary>
            This adapter abstracts the <see cref="T:System.Security.Cryptography.ECDsa"/> differences between versions of .Net targets.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaAdapter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.ECDsaAdapter"/> class.
            </summary>
            <exception cref="T:System.PlatformNotSupportedException">
            <see cref="T:System.Security.Cryptography.ECDsa"/> creation is not supported by some platforms.
            For more details, see https://aka.ms/IdentityModel/create-ecdsa.
            </exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaAdapter.CreateECDsa(Microsoft.IdentityModel.Tokens.JsonWebKey,System.Boolean)">
            <summary>
            Creates an ECDsa object using the <paramref name="jsonWebKey"/> and <paramref name="usePrivateKey"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaAdapter.CreateECDsaUsingCNGKey(Microsoft.IdentityModel.Tokens.JsonWebKey,System.Boolean)">
            <summary>
            Creates an ECDsa object using the <paramref name="jsonWebKey"/> and <paramref name="usePrivateKey"/>.
            'ECParameters' structure is available in .NET Framework 4.7+, .NET Standard 1.6+, and .NET Core 1.0+.
            This method is supported only on Windows as other platforms don't support operations with <see cref="T:System.Security.Cryptography.CngKey"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaAdapter.GetKeyByteCount(System.String)">
            <summary>
            Returns the size of key in bytes
            </summary>
            <param name="curveId">Represents ecdsa curve -P256, P384, P521</param>
            <returns>Size of the key in bytes</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ECDsaAdapter.KeyBlobMagicNumber">
            <summary>
            Magic numbers identifying ECDSA blob types
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaAdapter.GetMagicValue(System.String,System.Boolean)">
            <summary>
            Returns the magic value representing the curve corresponding to the curve id.
            </summary>
            <param name="curveId">Represents ecdsa curve -P256, P384, P512</param>
            <param name="willCreateSignatures">Whether the provider will create signatures or not</param>
            <returns>Uint representing the magic number</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaAdapter.SupportsCNGKey">
            <summary>
            Tests if user's runtime platform supports operations using <see cref="T:System.Security.Cryptography.CngKey"/>.
            </summary>
            <returns>True if operations using <see cref="T:System.Security.Cryptography.CngKey"/> are supported on user's runtime platform, false otherwise.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey">
            <summary>
            Represents a ECDsa security key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.#ctor(System.Security.Cryptography.ECDsa)">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey"/>.
            </summary>
            <param name="ecdsa"><see cref="T:System.Security.Cryptography.ECDsa"/></param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.ECDsa">
            <summary>
            <see cref="T:System.Security.Cryptography.ECDsa"/> instance used to initialize the key.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.PrivateKeyStatus">
            <summary>
            Gets an enum indicating if a private key exists.
            </summary>
            <return>'Exists' if private key exists for sure; 'DoesNotExist' if private key doesn't exist for sure; 'Unknown' if we cannot determine.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.KeySize">
            <summary>
            Gets <see cref="T:System.Security.Cryptography.ECDsa"/> key size.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.CanComputeJwkThumbprint">
            <summary>
            Determines whether the <see cref="T:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey"/> can compute a JWK thumbprint.
            </summary>
            <returns><c>true</c> if JWK thumbprint can be computed; otherwise, <c>false</c>.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey.ComputeJwkThumbprint">
            <summary>
            Computes a sha256 hash over the <see cref="T:Microsoft.IdentityModel.Tokens.ECDsaSecurityKey"/>.
            </summary>
            <returns>A JWK thumbprint.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.EncodingUtils">
            <summary>
            Collection of text encoding related helper methods.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncodingUtils.PerformEncodingDependentOperation``1(System.String,System.Text.Encoding,System.Func{System.Byte[],System.Int32,``0})">
            <summary>
            Obtains bytes from a string using the Encoding and then performs an action.
            </summary>
            <param name="input">String to process.</param>
            <param name="encoding">Encoding used to obtain bytes.</param>
            <param name="action">Operation to invoke with result which is byte array and length of useful data in array with offset as 0.</param>
            <typeparam name="T">Return type of operation.</typeparam>
            <returns>Instance of {T}.</returns>
            <remarks>
            The encoding operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            <see cref="M:System.Text.Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncodingUtils.PerformEncodingDependentOperation``1(System.String,System.Int32,System.Int32,System.Text.Encoding,System.Func{System.Byte[],System.Int32,``0})">
            <summary>
            Obtains bytes from a string using the Encoding and then performs an action.
            </summary>
            <param name="input">String to process.</param>
            <param name="offset">Index to start from in <paramref name="input"/>.</param>
            <param name="length">Length of characters to operate in <paramref name="input"/> from <paramref name="offset"/>.</param>
            <param name="encoding">Encoding used to obtain bytes.</param>
            <param name="action">Operation to invoke with result which is byte array and length of useful data in array with offset as 0.</param>
            <typeparam name="T">Return type of operation.</typeparam>
            <returns>Instance of {T}.</returns>
            <remarks>
            The encoding operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            <see cref="M:System.Text.Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncodingUtils.PerformEncodingDependentOperation``4(System.String,System.Int32,System.Int32,System.Text.Encoding,``1,``2,``3,System.Func{System.Byte[],System.Int32,``1,``2,``3,``0})">
            <summary>
            Obtains bytes from a string using the Encoding and then performs an action.
            </summary>
            <param name="input">String to process.</param>
            <param name="offset">Index to start from in <paramref name="input"/>.</param>
            <param name="length">Length of characters to operate in <paramref name="input"/> from <paramref name="offset"/>.</param>
            <param name="encoding">Encoding used to obtain bytes.</param>
            <param name="argx">Input parameter 1 to action.</param>
            <param name="argy">Input parameter 2 to action.</param>
            <param name="argz">Input parameter 3 to action.</param>
            <param name="action">Action to perform with bytes.</param>
            <typeparam name="T">Return type of operation.</typeparam>
            <typeparam name="TX">Type of Input parameter 1 to action.</typeparam>
            <typeparam name="TY">Type of Input parameter 2 to action.</typeparam>
            <typeparam name="TZ">Type of Input parameter 3 to action.</typeparam>
            <returns>Instance of {T}.</returns>
            <remarks>
            The encoding operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            <see cref="M:System.Text.Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncodingUtils.PerformEncodingDependentOperation``2(System.String,System.Text.Encoding,``1,System.Func{System.Byte[],System.Int32,``1,``0})">
            <summary>
            Encodes the string using given Encoding, and invokes the operation with the result.
            </summary>
            <typeparam name="T">Return type of operation.</typeparam>
            <typeparam name="TX">Input parameter to operation.</typeparam>
            <param name="input">String to process.</param>
            <param name="encoding">Encoding used to obtain bytes.</param>
            <param name="parameter">Additional operation parameter.</param>
            <param name="action">Operation to invoke with result which is byte array and length of useful data in array with offset as 0.</param>
            <returns>Result of operation.</returns>
            <remarks>
            The encoding operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            <see cref="M:System.Text.Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncodingUtils.PerformEncodingDependentOperation``2(System.String,System.Int32,System.Int32,System.Text.Encoding,``1,System.Func{System.Byte[],System.Int32,``1,``0})">
            <summary>
            Obtains bytes from a string using the Encoding and then performs an action.
            </summary>
            <param name="input">String to process.</param>
            <param name="offset">Index to start from in <paramref name="input"/>.</param>
            <param name="length">Length of characters to operate in <paramref name="input"/> from <paramref name="offset"/>.</param>
            <param name="encoding">Encoding used to obtain bytes.</param>
            <param name="parameter">Additional operation parameter.</param>
            <param name="action">Operation to invoke with result which is byte array and length of useful data in array with offset as 0.</param>
            <typeparam name="T">Return type of operation.</typeparam>
            <typeparam name="TX">Input parameter to operation.</typeparam>
            <returns>Instance of {T}.</returns>
            <remarks>
            The encoding operation uses shared memory pool to avoid allocations.
            The length of the rented array of bytes may be larger than the decoded bytes, therefore the action needs to know the actual length to use.
            <see cref="M:System.Text.Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)"/> is passed to the action.
            </remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials">
            <summary>
            A class for properties that are used for token encryption.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncryptingCredentials.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials"/> class.
            </summary>
            <param name="certificate"><see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>.</param>
            <param name="alg">A key wrap algorithm to use when encrypting a session key.</param>
            <param name="enc">Data encryption algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">if 'certificate' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'alg' is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">if 'enc' is null or empty.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncryptingCredentials.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials"/> class.
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use when encrypting a session key.</param>
            <param name="alg">A key wrap algorithm to use when encrypting a session key.</param>
            <param name="enc">Data encryption algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">if 'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'alg' is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">if 'enc' is null or empty.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EncryptingCredentials.#ctor(Microsoft.IdentityModel.Tokens.SymmetricSecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials"/> class.
            </summary>
            <remarks> Used in scenarios when a key represents a 'shared' symmetric key.
            For example, SAML 2.0 Assertion will be encrypted using a provided symmetric key
            which won't be serialized to a SAML token.
            </remarks>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> to apply.</param>
            <param name="enc">Data encryption algorithm to apply.</param>
            <exception cref="T:System.ArgumentException">If the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is not a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</exception>
            <exception cref="T:System.ArgumentNullException">if 'enc' is null or empty.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.Alg">
            <summary>
            Gets the key wrap algorithm used for session key encryption.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.Enc">
            <summary>
            Gets the data encryption algorithm.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.KeyExchangePublicKey">
            <summary>
            Public key used in Key Agreement Algorithms
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.CryptoProviderFactory">
            <summary>
            Users can override the default <see cref="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.CryptoProviderFactory"/> with this property. This factory will be used for creating encryption providers.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.SetDefaultCtyClaim">
            <summary>
            Gets or sets a bool that controls if the encrypted token creation will set default 'cty' if not specified.
            <remarks>
            Applies to only JWT tokens.
            </remarks>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EncryptingCredentials.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used for encryption.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider">
            <summary>
            Provides authenticated encryption and decryption services.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/> class used for encryption and decryption.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for crypto operations.</param>
            <param name="algorithm">The encryption algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">key size is not large enough.</exception>
            <exception cref="T:System.ArgumentException">'algorithm' is not supported.</exception>
            <exception cref="T:System.ArgumentException">a symmetricSignatureProvider is not created.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Algorithm">
            <summary>
            Gets the encryption algorithm that is being used.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider"/>.
            </summary>
            <remarks>This is null by default. This can be used by applications for extensibility scenarios.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is being used.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[])">
            <summary>
            Encrypts the 'plaintext'
            </summary>
            <param name="plaintext">the data to be encrypted.</param>
            <param name="authenticatedData">will be combined with iv and ciphertext to create an authenticationtag.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult"/>containing ciphertext, iv, authenticationtag.</returns>
            <exception cref="T:System.ArgumentNullException">plaintext is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">authenticationData is null or empty.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException">AES crypto operation threw. See inner exception for details.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Encrypts the 'plaintext'
            </summary>
            <param name="plaintext">the data to be encrypted.</param>
            <param name="authenticatedData">will be combined with iv and ciphertext to create an authenticationtag.</param>
            <param name="iv">initialization vector for encryption.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult"/>containing ciphertext, iv, authenticationtag.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="plaintext"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="authenticatedData"/> is null or empty.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException">Thrown if the AES crypto operation threw. See inner exception for details.</exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if the internal <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> is disposed.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Decrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Decrypts ciphertext into plaintext
            </summary>
            <param name="ciphertext">the encrypted text to decrypt.</param>
            <param name="authenticatedData">the authenticateData that is used in verification.</param>
            <param name="iv">the initialization vector used when creating the ciphertext.</param>
            <param name="authenticationTag">the authenticationTag that was created during the encyption.</param>
            <returns>decrypted ciphertext</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="ciphertext"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="authenticatedData"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="iv"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="authenticationTag"/> is null or empty.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException">Thrown if the signature over the authenticationTag fails to verify.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException">Thrown if the AES crypto operation threw. See inner exception.</exception>
            <exception cref="T:System.ObjectDisposedException">Thrown if the internal <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> is disposed.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Dispose">
            <summary>
            Calls <see cref="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Dispose(System.Boolean)"/> and <see cref="M:System.GC.SuppressFinalize(System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Dispose(System.Boolean)">
            <summary>
            Releases managed resources.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.IsSupportedAlgorithm(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Checks if an 'key, algorithm' pair is supported
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/></param>
            <param name="algorithm">the algorithm to check.</param>
            <returns>true if 'key, algorithm' pair is supported.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.GetHmacAlgorithm(System.String)">
            <summary>
            The algorithm parameter logically defines a HMAC algorithm.
            This method returns the HMAC to use.
            </summary>
            <param name="algorithm"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.GetKeyBytes(Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Called to obtain the byte[] needed to create a <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>that will be used to obtain the byte[].</param>
            <returns><see cref="T:System.Byte"/>[] that is used to populated the KeyedHashAlgorithm.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentException">if a byte[] can not be obtained from SecurityKey.</exception>
            <remarks><see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> are supported.
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> .Key is returned</para>
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>Base64UrlEncoder.DecodeBytes is called with <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.K"/> if <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kty"/> == JsonWebAlgorithmsKeyTypes.Octet</para>
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.ValidateKeySize(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Checks that the key has sufficient length
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that contains bytes.</param>
            <param name="algorithm">the algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="algorithm"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentException">if <paramref name="algorithm"/> is not a supported algorithm.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult">
            <summary>
            Contains the results of <see cref="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[])"/> operation.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Initializes a new <see cref="T:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult"/>
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used during <see cref="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Encrypt(System.Byte[],System.Byte[])"/></param>
            <param name="ciphertext">protected text.</param>
            <param name="iv">the initialization vector used.</param>
            <param name="authenticationTag">the bytes that need be passed to <see cref="M:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionProvider.Decrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[])"/>.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.Ciphertext">
            <summary>
            Gets the Ciphertext.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.IV">
            <summary>
            Gets the initialization vector.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.AuthenticatedEncryptionResult.AuthenticationTag">
            <summary>
            Gets the authentication tag
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider">
            <summary>
            Provides Wrap key and Unwrap key services.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Algorithm">
            <summary>
            Gets the KeyWrap algorithm that is being used.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/>.
            </summary>
            <remarks>This is null by default. This can be used by runtimes or for extensibility scenarios.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is being used.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Dispose">
            <summary>
            Calls <see cref="M:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Dispose(System.Boolean)"/> and <see cref="M:System.GC.SuppressFinalize(System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Dispose(System.Boolean)">
            <summary>
            Can be over written in descendants to dispose of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer</param>     
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.KeyWrapProvider.UnwrapKey(System.Byte[])">
            <summary>
            Unwrap a key.
            </summary>
            <param name="keyBytes">key to unwrap.</param>
            <returns>Unwrapped key.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.KeyWrapProvider.WrapKey(System.Byte[])">
            <summary>
            Wrap a key.
            </summary>
            <param name="keyBytes">the key to be wrapped</param>
            <returns>wrapped key.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider">
            <summary>
            Provides RSA Wrap key and Unwrap key services.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider"/> used for wrapping and un-wrappping keys.
            These keys are usually symmetric session keys that are wrapped using the recipients public key.
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for cryptographic operations.</param>
            <param name="algorithm">The KeyWrap algorithm to apply.</param>
            <param name="willUnwrap">Whether this <see cref="T:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider"/> is required to un-wrap keys. If true, the private key is required.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null.</exception>
            <exception cref="T:System.ArgumentException">The key size doesn't match the algorithm.</exception>
            <exception cref="T:System.ArgumentException">If <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.NotSupportedException">Failed to create RSA algorithm with provided key and algorithm.</exception>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.Algorithm">
            <summary>
            Gets the KeyWrap algorithm that is being used.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/>.
            </summary>
            <remarks>This is null by default. This is for use by the application and not used by this SDK.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is being used.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.Dispose(System.Boolean)">
            <summary>
            Disposes of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.IsSupportedAlgorithm(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Checks if an algorithm is supported.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for crypto operations.</param>
            <param name="algorithm">The KeyWrap algorithm to apply.</param>
            <returns>true if the algorithm is supported; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.UnwrapKey(System.Byte[])">
            <summary>
            Unwrap a key using RSA decryption.
            </summary>
            <param name="keyBytes">the bytes to unwrap.</param>
            <returns>Unwrapped key</returns>
            <exception cref="T:System.ArgumentNullException">'keyBytes' is null or length == 0.</exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException">Failed to unwrap the wrappedKey.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal RSA algorithm is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.WrapKey(System.Byte[])">
            <summary>
            Wrap a key using RSA encryption.
            </summary>
            <param name="keyBytes">the key to be wrapped</param>
            <returns>A wrapped key</returns>
            <exception cref="T:System.ArgumentNullException">'keyBytes' is null or has length == 0.</exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.RsaKeyWrapProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException">Failed to wrap the 'keyBytes'.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal RSA algorithm is null.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider">
            <summary>
            Provides Wrap key and Unwrap key services.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/> class used for wrap key and unwrap key.
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for crypto operations.</param>
            <param name="algorithm">The KeyWrap algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null.</exception>
            <exception cref="T:System.ArgumentException">If <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.ArgumentException">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> cannot be converted to byte array</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The keysize doesn't match the algorithm.</exception>
            <exception cref="T:System.InvalidOperationException">Failed to create symmetric algorithm with provided key and algorithm.</exception>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.Algorithm">
            <summary>
            Gets the KeyWrap algorithm that is being used.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:Microsoft.IdentityModel.Tokens.KeyWrapProvider"/>.
            </summary>
            <remarks>This is null by default. This can be used by runtimes or for extensibility scenarios.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is being used.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.Dispose(System.Boolean)">
            <summary>
            Disposes of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.GetSymmetricAlgorithm(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Returns the <see cref="T:System.Security.Cryptography.SymmetricAlgorithm"/>.
            </summary>
            <returns></returns>
            <exception cref="T:System.ArgumentException">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> cannot be converted to byte array</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The keysize doesn't match the algorithm.</exception>
            <exception cref="T:System.InvalidOperationException">Failed to create symmetric algorithm with provided key and algorithm.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.IsSupportedAlgorithm(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Answers if an algorithm is supported
            </summary>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/></param>
            <param name="algorithm">the algorithm to use</param>
            <returns>true if the algorithm is supported; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.UnwrapKey(System.Byte[])">
            <summary>
            Unwrap a key using Symmetric decryption.
            </summary>
            <param name="keyBytes">bytes to unwrap</param>
            <returns>Unwraped key</returns>
            <exception cref="T:System.ArgumentNullException">'keyBytes' is null or length == 0.</exception>
            <exception cref="T:System.ArgumentException">'keyBytes' is not a multiple of 8.</exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException">Failed to unwrap the wrappedKey.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricKeyWrapProvider.WrapKey(System.Byte[])">
            <summary>
            Wrap a key using Symmetric encryption.
            </summary>
            <param name="keyBytes">the key to be wrapped</param>
            <returns>The wrapped key result</returns>
            <exception cref="T:System.ArgumentNullException">'keyBytes' is null or has length 0.</exception>
            <exception cref="T:System.ArgumentException">'keyBytes' is not a multiple of 8.</exception>
            <exception cref="T:System.ObjectDisposedException">If <see cref="M:Microsoft.IdentityModel.Tokens.KeyWrapProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException">Failed to wrap 'keyBytes'.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.EpochTime">
            <summary>
            Returns the absolute DateTime or the Seconds since Unix Epoch, where Epoch is UTC 1970-01-01T0:0:0Z.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.EpochTime.UnixEpoch">
            <summary>
            DateTime as UTV for UnixEpoch
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EpochTime.GetIntDate(System.DateTime)">
            <summary>
            Per JWT spec:
            Gets the number of seconds from 1970-01-01T0:0:0Z as measured in UTC until the desired date/time.
            </summary>
            <param name="datetime">The DateTime to convert to seconds.</param>
            <remarks>if dateTimeUtc less than UnixEpoch, return 0</remarks>
            <returns>the number of seconds since Unix Epoch.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EpochTime.DateTime(System.Int64)">
            <summary>
            Creates a DateTime from epoch time.
            </summary>
            <param name="secondsSinceUnixEpoch">Number of seconds.</param>
            <returns>The DateTime in UTC.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2">
            <summary>
            This is an LRU cache implementation that relies on an event queue rather than locking to achieve thread safety.
            This approach has been decided on in order to optimize the performance of the get and set operations on the cache.
            This cache contains a doubly linked list in order to maintain LRU order, as well as a dictionary (map) to keep track of
            keys and expiration times. The linked list (a structure which is not thread-safe) is NEVER modified directly inside
            an API call (e.g. get, set, remove); it is only ever modified sequentially by a background thread. On the other hand,
            the map is a <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2"/> which may be modified directly inside an API call or
            through eventual processing of the event queue. This implementation relies on the principle of 'eventual consistency':
            though the map and it's corresponding linked list may be out of sync at any given point in time, they will eventually line up.
            See here for more details:
            https://aka.ms/identitymodel/caching
            </summary>
            <typeparam name="TKey">The key type to be used by the cache.</typeparam>
            <typeparam name="TValue">The value type to be used by the cache</typeparam>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.#ctor(System.Int32,System.Threading.Tasks.TaskCreationOptions,System.Collections.Generic.IEqualityComparer{`0},System.Boolean,System.Int32,System.Boolean)">
            <summary>
            Constructor.
            </summary>
            <param name="capacity">The capacity of the cache, used to determine if experiencing overflow.</param>
            <param name="options">The event queue task creation option, default to None instead of LongRunning as LongRunning will always start a task on a new thread instead of ThreadPool.</param>
            <param name="comparer">The equality comparison implementation to be used by the map when comparing keys.</param>
            <param name="removeExpiredValues">Whether or not to remove expired items.</param>
            <param name="removeExpiredValuesIntervalInSeconds">The period to wait to remove expired items, in seconds.</param>
            <param name="maintainLRU">Whether or not to maintain items in a LRU fashion, moving to front of list when accessed in the cache.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.DomainProcessExit(System.Object,System.EventArgs)">
            <summary>
            Occurs when the application is ready to exit.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event argument.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.DomainUnload(System.Object,System.EventArgs)">
            <summary>
            Occurs when an AppDomain is about to be unloaded.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event argument.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.StopEventQueueTask">
            <summary>
            Stop the event queue task.
            This is provided mainly for users who have unit tests that check for running task(s) to stop the task at the end of each test.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.StopEventQueueTaskImmediately">
            <summary>
            Stop the event queue task immediately if it is running. This allows the task/thread to terminate gracefully.
            Currently there is no unmanaged resource, if any is added in the future it should be disposed of in this method.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.EventQueueTaskAction">
            <summary>
            This is the delegate for the event queue task.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.RemoveExpiredValuesLRU">
            <summary>
            Remove all expired cache items from _doubleLinkedList and _map.
            </summary>
            <returns>Number of items removed.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.RemoveExpiredValues">
            <summary>
            Remove all expired cache items from the _map ONLY. This is called for the non-LRU (_maintainLRU = false) scenaro.
            The enumerator returned from the dictionary is safe to use concurrently with reads and writes to the dictionary, according to the MS document.
            </summary>
            <returns>Number of items removed.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.CompactLRU">
            <summary>
            Remove items from the LinkedList by the desired compaction percentage.
            This should be a private method.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.Compact">
            <summary>
            Remove items from the Dictionary by the desired compaction percentage.
            Since _map does not have LRU order, items are simply removed from using FirstOrDefault(). 
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.CalculateNewCacheSize">
            <summary>
            When the cache is at _maxCapacityPercentage, it needs to be compacted by _compactionPercentage.
            This method calculates the new size of the cache after being compacted.
            </summary>
            <returns>The new target cache size after compaction.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.SetTaskEndTime">
            <summary>
            This is the method that determines the end time for the event queue task.
            The goal is to be able to track the incoming events and predict how long the task should run in order to
            avoid a long running task and reduce the overhead costs of restarting tasks.
            For example, maybe we can track the last three events' time and set the _eventQueueRunDurationInSeconds = 2 * average_time_between_events.
            Note: tasks are based on thread pool so the overhead should not be huge but we should still try to minimize it.
            </summary>
            <returns>the time when the event queue task should end</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.StartEventQueueTaskIfNotRunning">
            <summary>
            This method is called after an item is added to the event queue. It will start the event queue task if one is not already running (_eventQueueTaskState != EventQueueTaskRunning).
            Using CompareExchange to set the _eventQueueTaskState prevents multiple tasks from being started.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.TryGetValue(`0,`1@)">
            Each time a node gets accessed, it gets moved to the beginning (head) of the list if the _maintainLRU == true
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.TryRemove(`0,`1@)">
            Removes a particular key from the cache.
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.LinkedList">
            <summary>
            FOR TESTING ONLY.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.LinkedListCount">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.MapCount">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.MapValues">
            <summary>
            FOR TESTING ONLY.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.EventQueueCount">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.TaskCount">
            <summary>
            FOR TESTING PURPOSES ONLY.
            This is for tests to verify all tasks exit at the end of tests if the queue is empty.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.EventBasedLRUCache`2.WaitForProcessing">
            <summary>
            FOR TESTING PURPOSES ONLY.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenArgumentException">
            <summary>
            Throw this exception when a received <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> has invalid arguments.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenArgumentException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenArgumentException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenArgumentException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenArgumentException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenArgumentException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException">
            <summary>
            Thrown when JWE compression fails.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenCompressionFailedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException">
            <summary>
            Thrown when JWE decompression fails.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecompressionFailedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException">
            <summary>
            Represents a security token exception when decryption failed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDecryptionFailedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException">
            <summary>
            Represents a security token exception when encryption failed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionFailedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException">
            <summary>
            This exception is thrown when a security token contained a key identifier but the key was not found by the runtime 
            when decrypting a token.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenEncryptionKeyNotFoundException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenException">
            <summary>
            Represents a security token exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException">
            <summary>
            Throw this exception when a received Security Token has expiration time in the past.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.Expires">
            <summary>
            Gets or sets the Expires value that created the validation exception. This value is always in UTC.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException">
            <summary>
            This exception is thrown when a cryptographic algorithm is invalid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException.InvalidAlgorithm">
            <summary>
            Gets or sets the invalid algorithm that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException"/> class.
            </summary>
            <param name="message">Additional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException"/> class.
            </summary>
            <param name="message">Additional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAlgorithmException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException">
            <summary>
            This exception is thrown when 'audience' of a token was not valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.InvalidAudience">
            <summary>
            Gets or sets the InvalidAudience that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">
            <summary>
            This exception is thrown when 'issuer' of a token was not valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.InvalidIssuer">
            <summary>
            Gets or sets the InvalidIssuer that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException">
            <summary>
            This exception is thrown when 'lifetime' of a token was not valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.NotBefore">
            <summary>
            Gets or sets the NotBefore value that created the validation exception. This value is always in UTC.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.Expires">
            <summary>
            Gets or sets the Expires value that created the validation exception. This value is always in UTC.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException">
            <summary>
            This exception is thrown when 'signature' of a token was not valid.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException">
            <summary>
            Throw this exception when a received Security Token has an invalid issuer signing key.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.SigningKey">
            <summary>
            Gets or sets the SigningKey that was found invalid.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSigningKeyException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException">
            <summary>
            This exception is thrown when the token type ('typ' header claim) of a JWT token is invalid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException.InvalidType">
            <summary>
            Gets or sets the invalid type that created the validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException"/> class.
            </summary>
            <param name="message">Additional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException"/> class.
            </summary>
            <param name="message">Additional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException">
            <summary>
            Represents a key wrap exception when encryption failed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenKeyWrapException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException">
            <summary>
            Represents a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> exception when the token is malformed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException">
            <summary>
            This exception is thrown when a security is missing an ExpirationTime.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException">
            <summary>
            Throw this exception when a received Security token has an effective time 
            in the future.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.NotBefore">
            <summary>
            Gets or sets the NotBefore value that created the validation exception. This value is always in UTC.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException">
            <summary>
            This exception is thrown when an add to the TokenReplayCache fails.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException">
            <summary>
            Throw this exception when a received Security Token has been replayed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException.#ctor">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of  <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException">
            <summary>
            This exception is thrown when a security token contained a key identifier but the key was not found by the runtime.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException">
            <summary>
            This exception is thrown when a security token contained a key identifier but the key was not found by the runtime
            and when validation errors exist over the security token. This exception is not intended to be used as a signal
            to refresh keys.
            </summary>
            <remarks>
            This exception type is now considered obsolete and will be removed in the next major version (7.0.0).
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException.ValidationFailure">
            <summary>
            Indicates the type of the validation failure.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException.#ctor(Microsoft.IdentityModel.Tokens.ValidationFailure,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="validationFailure">The validation failures.</param>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="message">Addtional information to be included in the exception and displayed to user.</param>
            <param name="innerException">A <see cref="T:System.Exception"/> that represents the root cause of the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenSignatureKeyNotFoundException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenUnableToValidateException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException">
            <summary>
            Represents a security token validation exception.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException"/> class with a specified error message.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException"/> class with a specified error message
            and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The <see cref="T:System.Exception"/> that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenValidationException"/> class.
            </summary>
            <param name="info">the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ValidationFailure">
            <summary>
            The reason for being unable to validate
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.ValidationFailure.None">
            <summary>
            Indicates no validation failures
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.ValidationFailure.InvalidLifetime">
            <summary>
            Indicates that the lifetime was invalid
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.ValidationFailure.InvalidIssuer">
            <summary>
            Indicates that the issuer was invalid
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ICompressionProvider">
            <summary>
            Compression provider interface.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ICompressionProvider.Algorithm">
            <summary>
            Gets the compression algorithm.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICompressionProvider.IsSupportedAlgorithm(System.String)">
            <summary>
            Called to determine if an algorithm is supported.
            </summary>
            <param name="algorithm">the algorithm that defines the compression method.</param>
            <returns>true if supported</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICompressionProvider.Decompress(System.Byte[])">
            <summary>
            Decompress.
            </summary>
            <param name="value">the value to decompress.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICompressionProvider.Compress(System.Byte[])">
            <summary>
            Compress.
            </summary>
            <param name="value">the value to decompress.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ICryptoProvider">
            <summary>
            Provides extensibility for cryptographic operators.
            If custom operators are needed for then <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> can be set to
            return these operators. <see cref="P:Microsoft.IdentityModel.Tokens.CryptoProviderFactory.CustomCryptoProvider"/> will be before each creation.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.IsSupportedAlgorithm(System.String,System.Object[])">
            <summary>
            Called to determine if a cryptographic operation is supported.
            </summary>
            <param name="algorithm">the algorithm that defines the cryptographic operator.</param>
            <param name="args">the arguments required by the cryptographic operator. May be null.</param>
            <returns>true if supported</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])">
            <summary>
            returns a cryptographic operator that supports the algorithm.
            </summary>
            <param name="algorithm">the algorithm that defines the cryptographic operator.</param>
            <param name="args">the arguments required by the cryptographic operator. May be null.</param>
            <remarks>call <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Release(System.Object)"/> when finished with the object.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Release(System.Object)">
            <summary>
            called to release the object returned from <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/>
            </summary>
            <param name="cryptoInstance">the object returned from <see cref="M:Microsoft.IdentityModel.Tokens.ICryptoProvider.Create(System.String,System.Object[])"/>.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache">
            <summary>
            Defines a cache for crypto providers.
            Current support is limited to <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> only.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache"/> using the default <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderCacheOptions"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.#ctor(Microsoft.IdentityModel.Tokens.CryptoProviderCacheOptions)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache"/> using the specified <paramref name="cryptoProviderCacheOptions"/>.
            </summary>
            <param name="cryptoProviderCacheOptions">The options used to configure the <see cref="T:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache"/>.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.#ctor(Microsoft.IdentityModel.Tokens.CryptoProviderCacheOptions,System.Threading.Tasks.TaskCreationOptions,System.Int32)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache"/> using the specified <paramref name="cryptoProviderCacheOptions"/>.
            </summary>
            <param name="cryptoProviderCacheOptions">The options used to configure the <see cref="T:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache"/>.</param>
            <param name="options">Options used to create the event queue thread.</param>
            <param name="tryTakeTimeout">The time used in ms for the timeout interval of the event queue. Defaults to 500 ms.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.GetCacheKey(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Returns the cache key to use when looking up an entry into the cache for a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider" />
            </summary>
            <param name="signatureProvider">the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to create the key for.</param>
            <exception cref="T:System.ArgumentNullException">if signatureProvider is null.</exception>
            <returns>the cache key to use for finding a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.GetCacheKey(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.String)">
            <summary>
            Returns the 'key' that will be used to find a crypto provider in this cache.
            </summary>
            <param name="securityKey">the key that is used to by the crypto provider.</param>
            <param name="algorithm">the algorithm that is used by the crypto provider.</param>
            <param name="typeofProvider">the typeof the crypto provider obtained by calling object.GetType().</param>
            <exception cref="T:System.ArgumentNullException">if securityKey is null.</exception>
            <exception cref="T:System.ArgumentNullException">if algorithm is null or empty string.</exception>
            <exception cref="T:System.ArgumentNullException">if typeofProvider is null or empty string.</exception>
            <returns>the cache key to use for finding a crypto provider.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.TryAdd(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Trys to adds a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to this cache.
            </summary>
            <param name="signatureProvider"><see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to cache.</param>
            <exception cref="T:System.ArgumentNullException">if signatureProvider is null.</exception>
            <returns>
            <c>true</c> if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was added, <c>false</c> if the cache already contained the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> or if <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> should not be cached.
            </returns>
            <remarks>if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> is added <see cref="P:Microsoft.IdentityModel.Tokens.SignatureProvider.CryptoProviderCache"/> will be set to 'this'.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.TryGetSignatureProvider(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.String,System.Boolean,Microsoft.IdentityModel.Tokens.SignatureProvider@)">
            <summary>
            Trys to find a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to this cache.
            </summary>
            <param name="securityKey">the key that is used to by the crypto provider.</param>
            <param name="algorithm">the algorithm that is used by the crypto provider.</param>
            <param name="typeofProvider">the typeof the crypto provider obtained by calling object.GetType().</param>
            <param name="willCreateSignatures">a bool to indicate if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> will be used to sign.</param>
            <param name="signatureProvider">the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> if found.</param>
            <exception cref="T:System.ArgumentNullException">if securityKey is null.</exception>
            <exception cref="T:System.ArgumentNullException">if algorithm is null or empty string.</exception>
            <exception cref="T:System.ArgumentNullException">if typeofProvider is null or empty string.</exception>
            <returns>true if a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was found, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.TryRemove(Microsoft.IdentityModel.Tokens.SignatureProvider)">
            <summary>
            Trys to remove a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> from this cache.
            </summary>
            <param name="signatureProvider"><see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> to remove.</param>
            <exception cref="T:System.ArgumentNullException">if signatureProvider is null.</exception>
            <returns>true if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was removed, false if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> was not found.</returns>
            <remarks>if the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> is removed <see cref="P:Microsoft.IdentityModel.Tokens.SignatureProvider.CryptoProviderCache"/> will be set to null.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.Dispose">
            <summary>
            Calls <see cref="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.Dispose(System.Boolean)"/> and <see cref="M:System.GC.SuppressFinalize(System.Object)"/>
            Note: the EventBasedLRUCache is no longer being disposed of, but since this is a public class and can be used as base class of
            custom cache implementations, we need to keep it as some implementations may override Dispose().
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.Dispose(System.Boolean)">
            <summary>
            If <paramref name="disposing"/> is true, this method disposes of <see cref="F:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache._signingSignatureProviders"/> and <see cref="F:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache._verifyingSignatureProviders"/>.
            </summary>
            <param name="disposing">True if called from the <see cref="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.Dispose"/> method, false otherwise.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.LinkedListCountSigning">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.LinkedListCountVerifying">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.MapCountSigning">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.MapCountVerifying">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.EventQueueCountSigning">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.EventQueueCountVerifying">
            <summary>
            FOR TESTING ONLY.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.InMemoryCryptoProviderCache.TaskCount">
            <summary>
            FOR TESTING PURPOSES ONLY.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.InternalValidators">
            <summary>
            Validators meant to be kept internal
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.InternalValidators.ValidateAfterSignatureFailed(Microsoft.IdentityModel.Tokens.SecurityToken,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Collections.Generic.IEnumerable{System.String},Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.BaseConfiguration)">
            <summary>
            Called after signature validation has failed to avoid a metadata refresh
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator">
            <summary>
            ISecurityTokenValidator
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.CanReadToken(System.String)">
            <summary>
            Returns true if the token can be read, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.CanValidateToken">
            <summary>
            Returns true if a token can be validated.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.MaximumTokenSizeInBytes">
            <summary>
            Gets and sets the maximum size in bytes, that a will be processed.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator.ValidateToken(System.String,Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            Validates a token passed as a string using <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.ITokenReplayCache">
            <summary>
            Interface that defines a simple cache for tacking replaying of security tokens.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ITokenReplayCache.TryAdd(System.String,System.DateTime)">
            <summary>
            Try to add a securityToken.
            </summary>
            <param name="securityToken">the security token to add.</param>
            <param name="expiresOn">the time when security token expires.</param>
            <returns>true if the security token was successfully added.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.ITokenReplayCache.TryFind(System.String)">
            <summary>
            Try to find securityToken
            </summary>
            <param name="securityToken">the security token to find.</param>
            <returns>true if the security token is found.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebAlgorithmsKeyTypes">
            <summary>
            Constants for JsonWebAlgorithms  "kty" Key Type (sec 6.1)
            https://datatracker.ietf.org/doc/html/rfc7518#section-6.1
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKey">
            <summary>
            Represents a JSON Web Key as defined in https://datatracker.ietf.org/doc/html/rfc7517.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.#ctor">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.Create(System.String)">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
            <param name="json">A string that contains JSON Web Key parameters in JSON format.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/></returns>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.#ctor(System.String)">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> from a json string.
            </summary>
            <param name="json">A string that contains JSON Web Key parameters in JSON format.</param>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.ConvertedSecurityKey">
            <summary>
            If this was converted to or from a SecurityKey, this field will be set.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.ConvertKeyInfo">
            <summary>
            If this was failed converted to a SecurityKey, this field will be set.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.AdditionalData">
            <summary>
            When deserializing from JSON any properties that are not defined will be placed here.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Alg">
            <summary>
            Gets or sets the 'alg' (KeyType).
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Crv">
            <summary>
            Gets or sets the 'crv' (ECC - Curve).
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.D">
            <summary>
            Gets or sets the 'd' (ECC - Private Key OR RSA - Private Exponent).
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.DP">
            <summary>
            Gets or sets the 'dp' (RSA - First Factor CRT Exponent).
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.DQ">
            <summary>
            Gets or sets the 'dq' (RSA - Second Factor CRT Exponent).
            </summary>
            <remarks>Value is formated as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.E">
            <summary>
            Gets or sets the 'e' (RSA - Exponent).
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.K">
            <summary>
            Gets or sets the 'k' (Symmetric - Key Value).
            </summary>
            Base64urlEncoding
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.KeyId">
            <summary>
            Gets the key id of this <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.KeyOps">
            <summary>
            Gets the 'key_ops' (Key Operations).
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kid">
            <summary>
            Gets or sets the 'kid' (Key ID)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kty">
            <summary>
            Gets or sets the 'kty' (Key Type).
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.N">
            <summary>
            Gets or sets the 'n' (RSA - Modulus).
            </summary>
            <remarks>Value is formatted as: Base64urlEncoding</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Oth">
            <summary>
            Gets or sets the 'oth' (RSA - Other Primes Info).
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.P">
            <summary>
            Gets or sets the 'p' (RSA - First Prime Factor)..
            </summary>
            <remarks>Value is formatted as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Q">
            <summary>
            Gets or sets the 'q' (RSA - Second  Prime Factor)..
            </summary>
            <remarks>Value is formatted as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.QI">
            <summary>
            Gets or sets the 'qi' (RSA - First CRT Coefficient)..
            </summary>
            <remarks>Value is formatted as: Base64urlUInt</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Use">
            <summary>
            Gets or sets the 'use' (Public Key Use)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X">
            <summary>
            Gets or sets the 'x' (ECC - X Coordinate)..
            </summary>
            <remarks>Value is formatted as: Base64urlEncoding</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5c">
            <summary>
            Gets the 'x5c' collection (X.509 Certificate Chain)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5t">
            <summary>
            Gets or sets the 'x5t' (X.509 Certificate SHA-1 thumbprint)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5tS256">
            <summary>
            Gets or sets the 'x5t#S256' (X.509 Certificate SHA-256 thumbprint)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.X5u">
            <summary>
            Gets or sets the 'x5u' (X.509 URL)..
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Y">
            <summary>
            Gets or sets the 'y' (ECC - Y Coordinate)..
            </summary>
            <remarks>Value is formatted as: Base64urlEncoding</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.KeySize">
            <summary>
            Gets the key size of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.CanComputeJwkThumbprint">
            <summary>
            Determines whether the <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> can compute a JWK thumbprint.
            </summary>
            <returns><c>true</c> if JWK thumbprint can be computed; otherwise, <c>false</c>.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.ComputeJwkThumbprint">
            <summary>
            Computes the JWK thumprint per spec: https://datatracker.ietf.org/doc/html/rfc7638 />.
            </summary>
            <returns>A the JWK thumbprint.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.RepresentAsAsymmetricPublicJwk">
            <summary>
            Creates a JsonWebKey representation of an asymmetric public key.
            </summary>
            <returns>JsonWebKey representation of an asymmetric public key.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7800#section-3.2</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKey.ToString">
            <summary>
            Returns the formatted string: GetType(), Use: 'value', Kid: 'value', Kty: 'value', InternalId: 'value'.
            </summary>
            <returns>string</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyConverter">
            <summary>
            Converts a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> into a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>
            Supports: converting to a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> from one of: <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/>, <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>, and <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeyConverter.ConvertFromSecurityKey(Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Converts a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> into a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>
            </summary>
            <param name="key">a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to convert.</param>
            <returns>a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/></returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/>is null.</exception>
            <exception cref="T:System.NotSupportedException">if <paramref name="key"/>is not a supported type.</exception>
            <remarks>Supports: <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/>, <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> and <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeyConverter.ConvertFromRSASecurityKey(Microsoft.IdentityModel.Tokens.RsaSecurityKey)">
            <summary>
            Converts a <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> into a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>
            </summary>
            <param name="key">a <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> to convert.</param>
            <returns>a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/></returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/>is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeyConverter.ConvertFromX509SecurityKey(Microsoft.IdentityModel.Tokens.X509SecurityKey)">
            <summary>
            Converts a <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> into a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>
            </summary>
            <param name="key">a <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> to convert.</param>
            <returns>a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/></returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/>is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeyConverter.ConvertFromX509SecurityKey(Microsoft.IdentityModel.Tokens.X509SecurityKey,System.Boolean)">
            <summary>
            Converts a <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> into a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.
            </summary>
            <param name="key">a <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> to convert.</param>
            <param name="representAsRsaKey">
            <c>true</c> to represent the <paramref name="key"/> as an <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/>, 
            <c>false</c> to represent the <paramref name="key"/> as an <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>, using the "x5c" parameter.
            </param>
            <returns>a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/>is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeyConverter.ConvertFromSymmetricSecurityKey(Microsoft.IdentityModel.Tokens.SymmetricSecurityKey)">
            <summary>
            Converts a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> into a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>
            </summary>
            <param name="key">a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> to convert.</param>
            <returns>a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/></returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="key"/>is null.</exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyECTypes">
            <summary>
            Constants for JsonWebKey Elliptical Curve Types
            https://datatracker.ietf.org/doc/html/rfc7518#section-*******
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyParameterNames">
            <summary>
            JsonWebKey parameter names
            see: https://datatracker.ietf.org/doc/html/rfc7517
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyParameterUtf8Bytes">
            <summary>
            JsonWebKey parameter names as UTF8 bytes
            Used by UTF8JsonReader/Writer for performance gains.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet">
            <summary>
            Contains a collection of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> that can be populated from a json string.
            </summary>
            <remarks>provides support for https://datatracker.ietf.org/doc/html/rfc7517.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.Create(System.String)">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/>.
            </summary>
            <param name="json">a string that contains JSON Web Key parameters in JSON format.</param>
            <returns><see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/></returns>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.#ctor">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.#ctor(System.String)">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/> from a json string.
            </summary>
            <param name="json">a json string containing values.</param>
            <exception cref="T:System.ArgumentNullException">If 'json' is null or empty.</exception>
            <exception cref="T:System.ArgumentException">If 'json' fails to deserialize.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKeySet.AdditionalData">
            <summary>
            When deserializing from JSON any properties that are not defined will be placed here.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKeySet.Keys">
            <summary>
            Gets the <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.JsonWebKeySet.DefaultSkipUnresolvedJsonWebKeys">
            <summary>
            Default value for the flag that controls whether unresolved JsonWebKeys will be included in the resulting collection of <see cref="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.GetSigningKeys"/> method.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.JsonWebKeySet.SkipUnresolvedJsonWebKeys">
            <summary>
            Flag that controls whether unresolved JsonWebKeys will be included in the resulting collection of <see cref="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.GetSigningKeys"/> method.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.JsonWebKeySet.GetSigningKeys">
            <summary>
            Returns the JsonWebKeys as a <see cref="T:System.Collections.Generic.IList`1"/>.
            </summary>
            <remarks>
            To include unresolved JsonWebKeys in the resulting <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> collection, set <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKeySet.SkipUnresolvedJsonWebKeys"/> to <c>false</c>.
            </remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeySetParameterNames">
            <summary>
            Names for Json Web Key Set Values
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.JsonWebKeyUseNames">
            <summary>
            Constants for JsonWebKeyUse (sec 4.2)
            https://datatracker.ietf.org/doc/html/rfc7517#section-4.2
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Json.JsonSerializerPrimitives.CreateJsonReaderException(System.Text.Json.Utf8JsonReader@,System.String,System.String,System.String,System.Exception)">
            <summary>
            Creates a JsonException that provides information on what went wrong
            </summary>
            <param name="reader">the <see cref="T:System.Text.Json.Utf8JsonReader"/>.</param>
            <param name="expectedType">the type the reader was expecting to find.</param>
            <param name="className">the name of the type being read.</param>
            <param name="propertyName">the property name being read.</param>
            <param name="innerException">inner exception if any.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Json.JsonSerializerPrimitives.ReadPropertyValueAsObject(System.Text.Json.Utf8JsonReader@,System.String,System.String,System.Boolean)">
            <summary>
            This method is called when deserializing a property value as an object.
            Normally we put the object into a Dictionary[string, object].
            </summary>
            <param name="reader">the <see cref="T:System.Text.Json.Utf8JsonReader"/></param>
            <param name="propertyName">the property name that is being read</param>
            <param name="className">the type that is being deserialized</param>
            <param name="read">if true reader.Read() will be called.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Json.JsonSerializerPrimitives.WriteObject(System.Text.Json.Utf8JsonWriter@,System.String,System.Object)">
            <summary>
            Writes an 'object' as a JsonProperty.
            This was written to support what IdentityModel6x supported and is not meant to be a
            general object serializer.
            If a user needs to serialize a special value, then serialize the value into a JsonElement.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Json.JsonSerializerPrimitives.WriteObjectValue(System.Text.Json.Utf8JsonWriter@,System.Object)">
            <summary>
            Writes values into an array.
            Assumes the writer.StartArray() has been called.
            </summary>
            <param name="writer"></param>
            <param name="obj"></param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Json.JsonWebKeySerializer.Read(System.Text.Json.Utf8JsonReader@,Microsoft.IdentityModel.Tokens.JsonWebKey)">
            <summary>
            Reads a JsonWebKey. see: https://datatracker.ietf.org/doc/html/rfc7517
            </summary>
            <param name="reader">a <see cref="T:System.Text.Json.Utf8JsonReader"/> pointing at a StartObject.</param>
            <param name="jsonWebKey"></param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Json.JsonWebKeySetSerializer.Read(System.Text.Json.Utf8JsonReader@,Microsoft.IdentityModel.Tokens.JsonWebKeySet)">
            <summary>
            Reads a JsonWebKey. see: https://datatracker.ietf.org/doc/html/rfc7517
            </summary>
            <param name="reader">a <see cref="T:System.Text.Json.Utf8JsonReader"/> pointing at a StartObject.</param>
            <param name="jsonWebKeySet"></param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKeySet"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Json.JsonWebKeySetSerializer.Write(System.Text.Json.Utf8JsonWriter@,Microsoft.IdentityModel.Tokens.JsonWebKeySet)">
            <summary>
            This method will be used when reading OIDC metadata
            </summary>
            <param name="writer"></param>
            <param name="jsonWebKeySet"></param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.LogMessages">
            <summary>
            Log messages and codes
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.DisposableObjectPool`1">
             <summary>
             Generic implementation of object pooling pattern with predefined pool size limit. The main
             purpose is that limited number of frequently used objects can be kept in the pool for
             further recycling.
            
             Notes: 
             1) it is not the goal to keep all returned objects. Pool is not meant for storage. If there
                is no space in the pool, extra returned objects will be dropped.
            
             2) it is implied that if object was obtained from a pool, the caller will return it back in
                a relatively short time. Keeping checked out objects for long durations is ok, but
                reduces usefulness of pooling. Just new up your own.
            
             Not returning objects to the pool in not detrimental to the pool's work, but is a bad practice.
             Rationale:
                If there is no intent for reusing the object, do not use pool - just use "new".
             </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DisposableObjectPool`1.Allocate">
            <summary>
            Produces an instance.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically
            reducing how far we will typically search.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.DisposableObjectPool`1.Free(`0)">
            <summary>
            Returns objects to the pool.
            </summary>
            <remarks>
            Search strategy is a simple linear probing which is chosen for it cache-friendliness.
            Note that Free will try to store recycled objects close to the start thus statistically
            reducing how far we will typically search in Allocate.
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Interop.Kernel32.GetMessage(System.Int32)">
            <summary>
                Returns a string message for the specified Win32 error code.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy">
            <summary>
            The purpose of this class is to ensure that we obtain an RsaCryptoServiceProvider that supports SHA-256 signatures.
            If the original RsaCryptoServiceProvider doesn't support SHA-256, we create a new one using the same KeyContainer.
            </summary>
            <remarks>
            There is no support for <see cref="T:System.Security.Cryptography.CspParameters"/> and <see cref="T:System.Security.Cryptography.CspKeyContainerInfo"/> on non-Windows platforms which makes <see cref="T:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy"/> a Windows-specific class.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.SignatureAlgorithm">
            <summary>
            Gets the SignatureAlgorithm
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.KeyExchangeAlgorithm">
            <summary>
            Gets the KeyExchangeAlgorithm
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.#ctor(System.Security.Cryptography.RSACryptoServiceProvider)">
            <summary>
            Initializes an new instance of <see cref="T:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy"/>.
            </summary>
            <param name="rsa"><see cref="T:System.Security.Cryptography.RSACryptoServiceProvider"/></param>
            <exception cref="T:System.ArgumentNullException">if <paramref name="rsa"/> is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.Decrypt(System.Byte[],System.Boolean)">
            <summary>
            Decrypts data with the System.Security.Cryptography.RSA algorithm.
            </summary>
            <param name="input">The data to be decrypted.</param>
            <param name="fOAEP">true to perform direct System.Security.Cryptography.RSA decryption using OAEP padding
            (only available on a computer running Microsoft Windows XP or later) otherwise, false to use PKCS#1 v1.5 padding.</param>
            <returns>decrypted bytes.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null or has Length == 0.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.DecryptValue(System.Byte[])">
            <summary>
            Decrypts the input.
            </summary>
            <param name="input">the bytes to decrypt.</param>
            <returns>decrypted bytes</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null or Length == 0.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.Encrypt(System.Byte[],System.Boolean)">
            <summary>
             Encrypts data with the System.Security.Cryptography.RSA algorithm.
            </summary>
            <param name="input">The data to be encrypted.</param>
            <param name="fOAEP">true to perform direct System.Security.Cryptography.RSA encryption using OAEP padding (only available on a computer running Microsoft Windows XP or later); 
            otherwise, false to use PKCS#1 v1.5 padding.</param>
            <returns>encrypted bytes.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null or has Length == 0.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.EncryptValue(System.Byte[])">
            <summary>
            Encrypts the input.
            </summary>
            <param name="input">the bytes to encrypt.</param>
            <returns>encrypted bytes.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null or Length == 0.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.SignData(System.Byte[],System.Object)">
            <summary>
            Computes the hash value of the specified byte array using the specified hash algorithm, and signs the resulting hash value.
            </summary>
            <param name="input">The input byte array for which to compute the hash.</param>
            <param name="hash">The hash algorithm to use to create the hash value. </param>
            <returns>The <see cref="T:System.Security.Cryptography.RSA"/> Signature for the specified data.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null or Length == 0.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="hash"/> is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.VerifyData(System.Byte[],System.Object,System.Byte[])">
            <summary>
            Verifies that a digital signature is valid by determining the hash value in the signature using the provided public key and comparing it to the hash value of the provided data.
            </summary>
            <param name="input">The input byte array.</param>
            <param name="hash">The hash algorithm to use to create the hash value.</param>
            <param name="signature">The signature byte array to be verified.</param>
            <returns>true if the signature is valid; otherwise, false.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null or Length == 0.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="hash"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="signature"/> is null or Length == 0.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.VerifyDataWithLength(System.Byte[],System.Int32,System.Int32,System.Object,System.Security.Cryptography.HashAlgorithmName,System.Byte[])">
            <summary>
            Verifies that a digital signature is valid by determining the hash value in the signature using the provided public key and comparing it to the hash value of the provided data.
            </summary>
            <param name="input">The input byte array.</param>
            <param name="offset"></param>
            <param name="length"></param>
            <param name="hash">The hash algorithm to use to create the hash value.</param>
            <param name="hashAlgorithmName"></param>
            <param name="signature">The signature byte array to be verified.</param>
            <returns>true if the signature is valid; otherwise, false.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="input"/> is null or Length == 0.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="hash"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="signature"/> is null or Length == 0.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.ExportParameters(System.Boolean)">
            <summary>
            Exports rsa parameters as <see cref="T:System.Security.Cryptography.RSAParameters"/>
            </summary>
            <param name="includePrivateParameters">flag to control is private parameters are included.</param>
            <returns><see cref="T:System.Security.Cryptography.RSAParameters"/></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.ImportParameters(System.Security.Cryptography.RSAParameters)">
            <summary>
            Imports rsa parameters as <see cref="T:System.Security.Cryptography.RSAParameters"/>
            </summary>
            <param name="parameters">to import.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RSACryptoServiceProviderProxy.Dispose(System.Boolean)">
            <summary>
            Calls to release managed resources.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey">
            <summary>
            Represents a Rsa security key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaSecurityKey.#ctor(System.Security.Cryptography.RSAParameters)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> class.
            </summary>
            <param name="rsaParameters"><see cref="T:System.Security.Cryptography.RSAParameters"/></param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaSecurityKey.#ctor(System.Security.Cryptography.RSA)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> class.
            </summary>
            <param name="rsa"><see cref="T:System.Security.Cryptography.RSA"/></param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.PrivateKeyStatus">
            <summary>
            Gets an enum indicating if a private key exists.
            </summary>
            <return>'Exists' if private key exists for sure; 'DoesNotExist' if private key doesn't exist for sure; 'Unknown' if we cannot determine.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.KeySize">
            <summary>
            Gets RSA key size.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.Parameters">
            <summary>
            <see cref="T:System.Security.Cryptography.RSAParameters"/> used to initialize the key.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.RsaSecurityKey.Rsa">
            <summary>
            <see cref="T:System.Security.Cryptography.RSA"/> instance used to initialize the key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaSecurityKey.CanComputeJwkThumbprint">
            <summary>
            Determines whether the <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/> can compute a JWK thumbprint.
            </summary>
            <returns><c>true</c> if JWK thumbprint can be computed; otherwise, <c>false</c>.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.RsaSecurityKey.ComputeJwkThumbprint">
            <summary>
            Computes a sha256 hash over the <see cref="T:Microsoft.IdentityModel.Tokens.RsaSecurityKey"/>.
            </summary>
            <returns>A JWK thumbprint.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityAlgorithms">
            <summary>
            Constants for Security Algorithm.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityKey">
            <summary>
            Base class for Security Key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityKey.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeySize">
            <summary>
            This must be overridden to get the size of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityKey.KeyId">
            <summary>
            Gets the key id of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityKey.CryptoProviderFactory">
            <summary>
            Gets or sets <see cref="T:Microsoft.IdentityModel.Tokens.CryptoProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityKey.ToString">
            <summary>
            Returns the formatted string: GetType(), KeyId: 'value', InternalId: 'value'.
            </summary>
            <returns>string</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityKey.CanComputeJwkThumbprint">
            <summary>
            Determines whether the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> can compute a JWK thumbprint.
            </summary>
            <returns><c>true</c> if JWK thumbprint can be computed; otherwise, <c>false</c>.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityKey.ComputeJwkThumbprint">
            <summary>
            Computes a sha256 hash over the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
            <returns>A JWK thumbprint.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityKey.IsSupportedAlgorithm(System.String)">
            <summary>
            Checks if <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.CryptoProviderFactory"/> can perform the cryptographic operation specified by the <paramref name="algorithm"/> with this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
            <param name="algorithm">the algorithm to apply.</param>
            <returns>true if <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.CryptoProviderFactory"/> can perform the cryptographic operation sepecified by the <paramref name="algorithm"/> with this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityKeyIdentifierClause">
            <summary>
            Contains information about the keys inside the tokens.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityToken">
            <summary>
            Base class for security token.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityToken.UnsafeToString">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.Id">
            <summary>
            This must be overridden to get the Id of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.Issuer">
            <summary>
            This must be overridden to get the issuer of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey">
            <summary>
            This must be overridden to get the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.SigningKey">
            <summary>
            This must be overridden to get or set the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey"/> that signed this instance.
            </summary>
            <remarks><see cref="T:Microsoft.IdentityModel.Tokens.ISecurityTokenValidator"/>.ValidateToken(...) can this value when a <see cref="P:Microsoft.IdentityModel.Tokens.SecurityToken.SecurityKey"/> is used to successfully validate a signature.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.ValidFrom">
            <summary>
            This must be overridden to get the time when this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> was Valid.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityToken.ValidTo">
            <summary>
            This must be overridden to get the time when this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> is no longer Valid.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor">
            <summary>
            Contains some information which used to create a security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Audience">
            <summary>
            Gets or sets the value of the 'audience' claim.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.CompressionAlgorithm">
            <summary>
            Defines the compression algorithm that will be used to compress the JWT token payload.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.EncryptingCredentials">
            <summary>
            Gets or sets the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.EncryptingCredentials"/> used to create a encrypted security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Expires">
            <summary>
            Gets or sets the value of the 'expiration' claim. This value should be in UTC.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Issuer">
            <summary>
            Gets or sets the issuer of this <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.IssuedAt">
            <summary>
            Gets or sets the time the security token was issued. This value should be in UTC.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.NotBefore">
            <summary>
            Gets or sets the notbefore time for the security token. This value should be in UTC.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.TokenType">
            <summary>
            Gets or sets the token type.
            <remarks> If provided, this will be added as the value for the 'typ' header parameter. In the case of a JWE, this will be added to both the inner (JWS) and the outer token (JWE) header. By default, the value used is 'JWT'.
            If <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.AdditionalHeaderClaims"/> also contains 'typ' header claim value, it will override the TokenType provided here.
            This value is used only for JWT tokens and not for SAML/SAML2 tokens</remarks>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Claims">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.Dictionary`2"/> which represents the claims that will be used when creating a security token.
            If both <cref see="Claims"/> and <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Subject"/> are set, the claim values in Subject will be combined with the values
            in Claims. The values found in Claims take precedence over those found in Subject, so any duplicate
            values will be overridden.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.AdditionalHeaderClaims">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.Dictionary`2"/> which contains any custom header claims that need to be added to the JWT token header.
            The 'alg', 'kid', 'x5t', 'enc', and 'zip' claims are added by default based on the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.SigningCredentials"/>,
            <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.EncryptingCredentials"/>, and/or <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.CompressionAlgorithm"/> provided and SHOULD NOT be included in this dictionary as this
            will result in an exception being thrown. 
            <remarks> These claims are only added to the outer header (in case of a JWE).</remarks>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.AdditionalInnerHeaderClaims">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.Dictionary`2"/> which contains any custom header claims that need to be added to the inner JWT token header.
            The 'alg', 'kid', 'x5t', 'enc', and 'zip' claims are added by default based on the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.SigningCredentials"/>,
            <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.EncryptingCredentials"/>, and/or <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.CompressionAlgorithm"/> provided and SHOULD NOT be included in this dictionary as this
            will result in an exception being thrown. 
            <remarks>
            For JsonWebTokenHandler, these claims are merged with <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.AdditionalHeaderClaims"/> while adding to the inner JWT header.
            </remarks>
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.SigningCredentials">
            <summary>
            Gets or sets the <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.SigningCredentials"/> used to create a security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Subject">
            <summary>
            Gets or sets the <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            If both <cref see="Claims"/> and <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor.Subject"/> are set, the claim values in Subject will be combined with the values
            in Claims. The values found in Claims take precedence over those found in Subject, so any duplicate
            values will be overridden.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SecurityTokenHandler">
            <summary>
            Defines the interface for a Security Token Handler.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.#ctor">
            <summary>
            Creates an instance of <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenHandler"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CreateSecurityTokenReference(Microsoft.IdentityModel.Tokens.SecurityToken,System.Boolean)">
            <summary>
            Returns <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKeyIdentifierClause"/>.
            </summary>
            <param name="token"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/></param>
            <param name="attached">true if attached; otherwise, false.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CreateToken(Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor)">
            <summary>
            Returns <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="tokenDescriptor"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenDescriptor"/></param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CanValidateToken">
            <summary>
            Gets a value indicating whether this handler supports validation of tokens 
            handled by this instance.
            </summary>v
            <returns>'True' if the instance is capable of SecurityToken
            validation.</returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CanWriteToken">
            <summary>
            Gets a value indicating whether the class provides serialization functionality to serialize token handled 
            by this instance.
            </summary>
            <returns>true if the WriteToken method can serialize this token.</returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.TokenType">
            <summary>
            This must be overridden to get the System.Type of the SecurityToken this instance handles.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CanReadToken(System.Xml.XmlReader)">
            <summary>
            Indicates whether the <see cref="T:System.Xml.XmlReader"/> is positioned at an element that can be read.
            </summary>
            <param name="reader">An <see cref="T:System.Xml.XmlReader"/> reader positioned at a start element. The reader should not be advanced.</param>
            <returns>'true' if the token can be read.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.CanReadToken(System.String)">
            <summary>
            Indicates whether the current token string can be read as a token 
            of the type handled by this instance.
            </summary>
            <param name="tokenString">The token string thats needs to be read.</param>
            <returns>'True' if the ReadToken method can parse the token string.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.ReadToken(System.Xml.XmlReader)">
            <summary>
            Gets security token.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/>.</param>
            <remarks>SecurityToken instance which represents the serialized token.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.WriteToken(Microsoft.IdentityModel.Tokens.SecurityToken)">
            <summary>
            Serializes to string a token of the type handled by this instance.
            </summary>
            <param name="token">A token of type TokenType.</param>
            <returns>The serialized token.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.WriteToken(System.Xml.XmlWriter,Microsoft.IdentityModel.Tokens.SecurityToken)">
            <summary>
            This must be overridden to serialize to XML a token of the type handled by this instance.
            </summary>
            <param name="writer">The XML writer.</param>
            <param name="token">A token of type <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.TokenType"/>.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.ReadToken(System.Xml.XmlReader,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            This must be overridden to deserialize token with the provided <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.
            </summary>
            <param name="reader"><see cref="T:System.Xml.XmlReader"/>.</param>
            <param name="validationParameters">the current <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.</param>
            <remarks>SecurityToken instance which represents the serialized token.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.ValidateToken(System.String,Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            This must be overridden to validate a token passed as a string using <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>
            </summary>
            <param name="securityToken">A token of type <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.TokenType"/>.</param>
            <param name="validationParameters">the current <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.</param>
            <param name="validatedToken">The token of type <see cref="P:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.TokenType"/> that was validated.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SecurityTokenHandler.ValidateToken(System.Xml.XmlReader,Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.SecurityToken@)">
            <summary>
            Reads and validates a token using a xmlReader and <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>
            </summary>
            <param name="reader">A <see cref="T:System.Xml.XmlReader"/> pointing at the start element of the token.</param>
            <param name="validationParameters">Contains data and information needed for validation.</param>
            <param name="validatedToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that was validated.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SignatureProvider">
            <summary>
            Provides signature services, signing and verifying.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.SignatureProvider._referenceCount">
            <summary>
            Maintains the number of external references
            see: <see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.AddRef"/>, <see cref="P:Microsoft.IdentityModel.Tokens.SignatureProvider.RefCount"/>, <see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Release"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> class used to create and verify signatures.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="key"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="algorithm"/> is null or empty.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Algorithm">
            <summary>
            Gets the signature algorithm.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Context">
            <summary>
            Gets or sets a user context for a <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>.
            </summary>
            <remarks>This is null by default. This is for use by the application and not used by this SDK.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.CryptoProviderCache">
            <summary>
            Gets or sets the <see cref="P:Microsoft.IdentityModel.Tokens.SignatureProvider.CryptoProviderCache"/> that is associated with this <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose">
            <summary>
            Calls <see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)"/> and <see cref="M:System.GC.SuppressFinalize(System.Object)"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)">
            <summary>
            Can be over written in descendants to dispose of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.ObjectPoolSize">
            <summary>
            For testing purposes
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Sign(System.Byte[])">
            <summary>
            This must be overridden to produce a signature over the 'input'.
            </summary>
            <param name="input">bytes to sign.</param>
            <returns>signed bytes</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Verify(System.Byte[],System.Byte[])">
            Verifies that the <paramref name="signature"/> over <paramref name="input"/> using the
            <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and <see cref="P:Microsoft.IdentityModel.Tokens.SignatureProvider.Algorithm"/> specified by this
            <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> are consistent.
            <param name="input">the bytes that were signed.</param>
            <param name="signature">signature to compare against.</param>
            <returns>true if the computed signature matches the signature parameter, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Verify(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Verifies that a signature created over the 'input' matches the signature. Using <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and 'algorithm' passed to <see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)"/>.
            </summary>
            <param name="input">The bytes to verify.</param>
            <param name="inputOffset">offset in to input bytes to caculate hash.</param>
            <param name="inputLength">number of bytes of signature to use.</param>
            <param name="signature">signature to compare against.</param>
            <param name="signatureOffset">offset into signature array.</param>
            <param name="signatureLength">how many bytes to verfiy.</param>
            <returns>true if computed signature matches the signature parameter, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0. </exception>
            <exception cref="T:System.ArgumentException">'length &lt; 1'</exception>
            <exception cref="T:System.ArgumentException">'offset + length &gt; input.Length'</exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:Microsoft.IdentityModel.Tokens.SignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SignatureProvider.WillCreateSignatures">
            <summary>
            Gets or sets a bool indicating if this <see cref="T:Microsoft.IdentityModel.Tokens.SignatureProvider"/> is expected to create signatures.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SigningCredentials">
            <summary>
            Defines the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>, algorithm and digest for digital signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SigningCredentials.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SigningCredentials"/> class.
            </summary>
            <param name="certificate"><see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> that will be used for signing.</param>
            <remarks>Algorithm will be set to <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/>.
            the 'digest method' if needed may be implied from the algorithm. For example <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/> implies Sha256.</remarks>
            <exception cref="T:System.ArgumentNullException">if 'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'algorithm' is null or empty.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SigningCredentials.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SigningCredentials"/> class.
            </summary>
            <param name="certificate"><see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> that will be used for signing.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <remarks>the 'digest method' if needed may be implied from the algorithm. For example <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/> implies Sha256.</remarks>
            <exception cref="T:System.ArgumentNullException">if 'certificate' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'algorithm' is null or empty.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SigningCredentials.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SigningCredentials"/> class.
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <remarks>the 'digest method' if needed may be implied from the algorithm. For example <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.HmacSha256Signature"/> implies Sha256.</remarks>
            <exception cref="T:System.ArgumentNullException">if 'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'algorithm' is null or empty.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SigningCredentials.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SigningCredentials"/> class.
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <param name="digest">The digest algorithm to apply.</param>
            <exception cref="T:System.ArgumentNullException">if 'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'algorithm' is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">if 'digest' is null or empty.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.Algorithm">
            <summary>
            Gets the signature algorithm.
            </summary>
            <exception cref="T:System.ArgumentNullException">if 'value' is null or empty.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.Digest">
            <summary>
            Gets the digest algorithm.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.CryptoProviderFactory">
            <summary>
            Users can override the default <see cref="P:Microsoft.IdentityModel.Tokens.SigningCredentials.CryptoProviderFactory"/> with this property. This factory will be used for creating signature providers.
            </summary>
            <remarks>This will have precedence over <see cref="P:Microsoft.IdentityModel.Tokens.SecurityKey.CryptoProviderFactory"/></remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.Key">
            <summary>
            Gets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used for signature creation or validation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SigningCredentials.Kid">
            <summary>
            Gets the key id associated with <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SupportedAlgorithms">
            <summary>
            Defines the default set of algorithms this library supports
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SupportedAlgorithms.GetHashAlgorithmName(System.String)">
            <summary>
            Creating a Signature requires the use of a <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.
            This method returns the <see cref="T:System.Security.Cryptography.HashAlgorithmName"/>
            that describes the <see cref="T:System.Security.Cryptography.HashAlgorithm"/>to use when generating a Signature.
            </summary>
            <param name="algorithm">The SignatureAlgorithm in use.</param>
            <returns>The <see cref="T:System.Security.Cryptography.HashAlgorithmName"/> to use.</returns>
            <exception cref="T:System.ArgumentNullException">if <paramref name="algorithm"/> is null or whitespace.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">if <paramref name="algorithm"/> is not supported.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SupportedAlgorithms.GetDigestFromSignatureAlgorithm(System.String)">
            <summary>
            Creating a Signature requires the use of a <see cref="T:System.Security.Cryptography.HashAlgorithm"/>.
            This method returns the HashAlgorithm string that is associated with a SignatureAlgorithm.
            </summary>
            <param name="algorithm">The SignatureAlgorithm of interest.</param>
            <exception cref="T:System.ArgumentNullException">if <paramref name="algorithm"/>is null or whitespace.</exception>
            <exception cref="T:System.ArgumentException">if <paramref name="algorithm"/> is not supported.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SupportedAlgorithms.IsSupportedAlgorithm(System.String,Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Checks if an 'algorithm, key' pair is supported.
            </summary>
            <param name="algorithm">the algorithm to check.</param>
            <param name="key">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.</param>
            <returns>true if 'algorithm, key' pair is supported.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey">
            <summary>
            Represents a symmetric security key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.#ctor(System.Byte[])">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> instance.
            </summary>
            <param name="key">The byte array of the key.</param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.KeySize">
            <summary>
            Gets the key size.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.Key">
            <summary>
            Gets the byte array of the key.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.CanComputeJwkThumbprint">
            <summary>
            Determines whether the <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> can compute a JWK thumbprint.
            </summary>
            <returns><c>true</c> if JWK thumbprint can be computed; otherwise, <c>false</c>.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey.ComputeJwkThumbprint">
            <summary>
            Computes a sha256 hash over the <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.
            </summary>
            <returns>A JWK thumbprint.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider">
            <summary>
            Provides signing and verifying operations using a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and specifying an algorithm.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider._expectedSignatureSizeInBytes">
            <summary>
            Mapping from algorithm to the expected signature size in bytes.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.DefaultMinimumSymmetricKeySizeInBits">
            <summary>
            This is the minimum <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.KeySize when creating and verifying signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider"/> class that uses an <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to create and / or verify signatures over a array of bytes.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.</param>
            <param name="algorithm">The signature algorithm to use.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or empty.</exception>
            <exception cref="T:System.NotSupportedException">If <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">'<see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.KeySize' is smaller than <see cref="P:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.MinimumSymmetricKeySizeInBits"/>.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider"/> class that uses an <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to create and / or verify signatures over a array of bytes.
            </summary>
            <param name="key">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that will be used for signature operations.</param>
            <param name="algorithm">The signature algorithm to use.</param>
            <param name="willCreateSignatures">indicates if this <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider"/> will be used to create signatures.</param>
            <exception cref="T:System.ArgumentNullException">'key' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'algorithm' is null or empty.</exception>
            <exception cref="T:System.NotSupportedException">If <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> and algorithm pair are not supported.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">'<see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>.KeySize' is smaller than <see cref="P:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.MinimumSymmetricKeySizeInBits"/>.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.MinimumSymmetricKeySizeInBits">
            <summary>
            Gets or sets the minimum <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/>.KeySize"/>.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' is smaller than <see cref="F:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.DefaultMinimumSymmetricKeySizeInBits"/>.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.GetKeyBytes(Microsoft.IdentityModel.Tokens.SecurityKey)">
            <summary>
            Called to obtain the byte[] needed to create a <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>
            </summary>
            <param name="key"><see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/>that will be used to obtain the byte[].</param>
            <returns><see cref="T:System.Byte"/>[] that is used to populated the KeyedHashAlgorithm.</returns>
            <exception cref="T:System.ArgumentNullException">if key is null.</exception>
            <exception cref="T:System.ArgumentException">if a byte[] can not be obtained from SecurityKey.</exception>
            <remarks><see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/> are supported.
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> .Key is returned</para>
            <para>For a <see cref="T:Microsoft.IdentityModel.Tokens.JsonWebKey"/>Base64UrlEncoder.DecodeBytes is called with <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.K"/> if <see cref="P:Microsoft.IdentityModel.Tokens.JsonWebKey.Kty"/> == JsonWebAlgorithmsKeyTypes.Octet</para>
            </remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.GetKeyedHashAlgorithm(System.Byte[],System.String)">
            <summary>
            Returns a <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>.
            This method is called just before a cryptographic operation.
            This provides the opportunity to obtain the <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> from an object pool.
            If this method is overridden, it is importont to override <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.ReleaseKeyedHashAlgorithm(System.Security.Cryptography.KeyedHashAlgorithm)"/>
            if custom releasing of the <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is desired.
            </summary>
            <param name="algorithm">The hash algorithm to use to create the hash value.</param>
            <param name="keyBytes">The byte array of the key.</param>
            <returns>An instance of <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/></returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.ObjectPoolSize">
            <summary>
            For testing purposes
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.ReleaseKeyedHashAlgorithm(System.Security.Cryptography.KeyedHashAlgorithm)">
            <summary>
            This method is called just after the cryptographic operation.
            If <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.GetKeyedHashAlgorithm(System.Byte[],System.String)"/> was overridden this method can be overridden for
            any custom handling such as returning the <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> to an object pool.
            </summary>
            <param name="keyedHashAlgorithm">The <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/>" in use.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Sign(System.Byte[])">
            <summary>
            Produces a signature over the 'input' using the <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)"/>.
            </summary>
            <param name="input">The bytes to sign.</param>
            <returns>Signed bytes</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null. </exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException"><see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
            <remarks>Sign is thread safe.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Verify(System.Byte[],System.Byte[])">
            <summary>
            Verifies that a signature created over the 'input' matches the signature. Using <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)"/>.
            </summary>
            <param name="input">The bytes to verify.</param>
            <param name="signature">signature to compare against.</param>
            <returns>true if computed signature matches the signature parameter, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0. </exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
            <remarks>Verify is thread safe.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Verify(System.Byte[],System.Byte[],System.Int32)">
            <summary>
            Verifies that a signature created over the 'input' matches the signature. Using <see cref="T:Microsoft.IdentityModel.Tokens.SymmetricSecurityKey"/> and 'algorithm' passed to <see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.#ctor(Microsoft.IdentityModel.Tokens.SecurityKey,System.String)"/>.
            </summary>
            <param name="input">The bytes to verify.</param>
            <param name="signature">signature to compare against.</param>
            <param name="length">number of bytes of signature to use.</param>
            <returns>true if computed signature matches the signature parameter, false otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">'input' is null.</exception>
            <exception cref="T:System.ArgumentNullException">'signature' is null.</exception>
            <exception cref="T:System.ArgumentException">'input.Length' == 0.</exception>
            <exception cref="T:System.ArgumentException">'signature.Length' == 0. </exception>
            <exception cref="T:System.ArgumentException">'length &lt; 1'</exception>
            <exception cref="T:System.ObjectDisposedException"><see cref="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)"/> has been called.</exception>
            <exception cref="T:System.InvalidOperationException">If the internal <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm"/> is null. This can occur if a derived type deletes it or does not create it.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Verify(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Verify(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.String)">
            <summary>
            This internal method is called from the AuthenticatedEncryptionProvider which passes in the algorithm that defines the size expected for the signature.
            The reason is the way the AuthenticationTag is validated.
            For example when "A128CBC-HS256" is specified, SHA256 will used to create the HMAC and 32 bytes will be generated, but only the first 16 will be validated.
            </summary>
            <param name="input">The bytes to verify.</param>
            <param name="inputOffset">offset in to input bytes to caculate hash.</param>
            <param name="inputLength">number of bytes of signature to use.</param>
            <param name="signature">signature to compare against.</param>
            <param name="signatureOffset">offset into signature array.</param>
            <param name="signatureLength">how many bytes to verfiy.</param>
            <param name="algorithm">algorithm passed by AuthenticatedEncryptionProvider.</param>
            <returns>true if computed signature matches the signature parameter, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.SymmetricSignatureProvider.Dispose(System.Boolean)">
            <summary>
            Disposes of internal components.
            </summary>
            <param name="disposing">true, if called from Dispose(), false, if invoked inside a finalizer.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenContext">
            <summary>
            An opaque context used to store work when working with authentication artifacts.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenContext.#ctor">
            <summary>
            Instantiates a new <see cref="T:Microsoft.IdentityModel.Tokens.TokenContext"/> with a default activity ID.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenContext.#ctor(System.Guid)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.IdentityModel.Tokens.TokenContext"/> with an activity ID.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenHandler">
            <summary>
            Defines properties shared across all security token handlers.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenHandler.DefaultTokenLifetimeInMinutes">
            <summary>
            Default lifetime of tokens created. When creating tokens, if 'expires', 'notbefore' or 'issuedat' are null, 
            then a default will be set to: issuedat = DateTime.UtcNow, notbefore = DateTime.UtcNow, expires = DateTime.UtcNow + TimeSpan.FromMinutes(TokenLifetimeInMinutes).
            </summary>
            <remarks>See: <see cref="P:Microsoft.IdentityModel.Tokens.TokenHandler.SetDefaultTimesOnTokenCreation"/> for configuration.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenHandler.MaximumTokenSizeInBytes">
            <summary>
            Gets and sets the maximum token size in bytes that will be processed.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' less than 1.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenHandler.SetDefaultTimesOnTokenCreation">
            <summary>
            Gets or sets a bool that controls if token creation will set default 'exp', 'nbf' and 'iat' if not specified.
            </summary>
            <remarks>See: <see cref="P:Microsoft.IdentityModel.Tokens.TokenHandler.TokenLifetimeInMinutes"/> for configuration.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenHandler.TokenLifetimeInMinutes">
            <summary>
            Gets or sets the token lifetime in minutes.
            </summary>
            <remarks>Used during token creation to set the default expiration ('exp'). </remarks>
            <exception cref="T:System.ArgumentOutOfRangeException">'value' less than 1.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenHandler.ValidateTokenAsync(System.String,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates a token.
            On a validation failure, no exception will be thrown; instead, the exception will be set in the returned TokenValidationResult.Exception property.
            Callers should always check the TokenValidationResult.IsValid property to verify the validity of the result.
            </summary>
            <param name="token">The token to be validated.</param>
            <param name="validationParameters">A <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationResult"/></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenHandler.ValidateTokenAsync(Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates a token.
            On a validation failure, no exception will be thrown; instead, the exception will be set in the returned TokenValidationResult.Exception property.
            Callers should always check the TokenValidationResult.IsValid property to verify the validity of the result.
            </summary>
            <param name="token">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> to be validated.</param>
            <param name="validationParameters">A <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationResult"/></returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenHandler.ReadToken(System.String)">
            <summary>
            Converts a string into an instance of <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="token">The string to be deserialized.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="token"/> is null or empty.</exception>
            <exception cref="T:System.ArgumentException">'token.Length' is greater than <see cref="P:Microsoft.IdentityModel.Tokens.TokenHandler.MaximumTokenSizeInBytes"/>.</exception>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenHandler.CreateClaimsIdentityInternal(Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters,System.String)">
            <summary>
            Called by base class to create a <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            Currently only used by the JsonWebTokenHandler to allow for a Lazy creation.
            </summary>
            <param name="securityToken">the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that has the Claims.</param>
            <param name="tokenValidationParameters">the <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> that was used to validate the token.</param>
            <param name="issuer">the 'issuer' to use by default when creating a Claim.</param>
            <returns>A <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenUtilities">
            <summary>
            A class which contains useful methods for processing tokens.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenUtilities.Json">
            <summary>
            A URI that represents the JSON XML data type.
            </summary>
            <remarks>When mapping json to .Net Claim(s), if the value was not a string (or an enumeration of strings), the ClaimValue will serialized using the current JSON serializer, a property will be added with the .Net type and the ClaimTypeValue will be set to 'JsonClaimValueType'.</remarks>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenUtilities.JsonArray">
            <summary>
            A URI that represents the JSON array XML data type.
            </summary>
            <remarks>When mapping json to .Net Claim(s), if the value was not a string (or an enumeration of strings), the ClaimValue will serialized using the current JSON serializer, a property will be added with the .Net type and the ClaimTypeValue will be set to 'JsonClaimValueType'.</remarks>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenUtilities.JsonNull">
            <summary>
            A URI that represents the JSON null data type
            </summary>
            <remarks>When mapping json to .Net Claim(s), we use empty string to represent the claim value and set the ClaimValueType to JsonNull</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenUtilities.CreateDictionaryFromClaims(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Creates a dictionary from a list of Claim's.
            </summary>
            <param name="claims"> A list of claims.</param>
            <returns> A Dictionary representing claims.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenUtilities.GetAllSigningKeys(Microsoft.IdentityModel.Tokens.BaseConfiguration,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Returns all <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> provided in <paramref name="configuration"/> and <paramref name="validationParameters"/>.
            </summary>
            <param name="configuration">The <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> that contains signing keys used for validation.</param>
            <param name="validationParameters">A <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>Returns all <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> provided in provided in <paramref name="configuration"/> and <paramref name="validationParameters"/>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenUtilities.MergeClaims(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.Collections.Generic.IEnumerable{System.Security.Claims.Claim})">
            <summary>
            Merges claims. If a claim with same type exists in both <paramref name="claims"/> and <paramref name="subjectClaims"/>, the one in claims will be kept.
            </summary>
            <param name="claims"> Collection of <see cref="T:System.Security.Claims.Claim"/>'s.</param>
            <param name="subjectClaims"> Collection of <see cref="T:System.Security.Claims.Claim"/>'s.</param>
            <returns> A Merged list of <see cref="T:System.Security.Claims.Claim"/>'s.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenUtilities.IsRecoverableException(System.Exception)">
            <summary>
            Check whether the given exception type is recoverable by LKG.
            </summary>
            <param name="exception">The exception to check.</param>
            <returns><c>true</c> if the exception is certain types of exceptions otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenUtilities.IsRecoverableConfiguration(System.String,Microsoft.IdentityModel.Tokens.BaseConfiguration,Microsoft.IdentityModel.Tokens.BaseConfiguration,System.Exception)">
            <summary>
            Check whether the given configuration is recoverable by LKG.
            </summary>
            <param name="kid">The kid from token."/></param>
            <param name="currentConfiguration">The <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> to check.</param>
            <param name="lkgConfiguration">The LKG exception to check.</param>
            <param name="currentException">The exception to check.</param>
            <returns><c>true</c> if the configuration is recoverable otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AlgorithmValidator">
            <summary>
            Definition for AlgorithmValidator
            </summary>
            <param name="algorithm">The algorithm to validate.</param>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns><c>true</c> if the algorithm is considered valid</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.AudienceValidator">
            <summary>
            Definition for AudienceValidator.
            </summary>
            <param name="audiences">The audiences found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>true if the audience is considered valid.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolver">
            <summary>
            Definition for IssuerSigningKeyResolver.
            </summary>
            <param name="token">The <see cref="T:System.String"/> representation of the token that is being validated.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated. It may be null.</param>
            <param name="kid">A key identifier. It may be null.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use when validating a signature.</returns>
            <remarks> If both <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolverUsingConfiguration"/> and <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolver"/> are set, IssuerSigningKeyResolverUsingConfiguration takes
            priority.</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolverUsingConfiguration">
            <summary>
            Definition for IssuerSigningKeyResolverUsingConfiguration.
            </summary>
            <param name="token">The <see cref="T:System.String"/> representation of the token that is being validated.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated. It may be null.</param>
            <param name="kid">A key identifier. It may be null.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <param name="configuration"><see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> required for validation.</param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use when validating a signature.</returns>
            <remarks> If both <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolverUsingConfiguration"/> and <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolver"/> are set, IssuerSigningKeyResolverUsingConfiguration takes
            priority.</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyValidator">
            <summary>
            Definition for IssuerSigningKeyValidator.
            </summary>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <remarks> If both <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolverUsingConfiguration"/> and <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolver"/> are set, IssuerSigningKeyResolverUsingConfiguration takes
            priority.</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyValidatorUsingConfiguration">
            <summary>
            Definition for IssuerSigningKeyValidatorUsingConfiguration.
            </summary>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <param name="configuration"><see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> required for validation.</param>
            <remarks> If both <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolverUsingConfiguration"/> and <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyResolver"/> are set, IssuerSigningKeyResolverUsingConfiguration takes
            priority.</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerValidator">
            <summary>
            Definition for IssuerValidator.
            </summary>
            <param name="issuer">The issuer to validate.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <remarks>The delegate should return a non null string that represents the 'issuer'. If null a default value will be used.
            If both <see cref="T:Microsoft.IdentityModel.Tokens.IssuerValidatorUsingConfiguration"/> and <see cref="T:Microsoft.IdentityModel.Tokens.IssuerValidator"/> are set, IssuerValidatorUsingConfiguration takes
            priority.</remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerValidatorUsingConfiguration">
            <summary>
            Definition for IssuerValidatorUsingConfiguration.
            </summary>
            <param name="issuer">The issuer to validate.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <param name="configuration"><see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <remarks>The delegate should return a non null string that represents the 'issuer'. If null a default value will be used.
            If both <see cref="T:Microsoft.IdentityModel.Tokens.IssuerValidatorUsingConfiguration"/> and <see cref="T:Microsoft.IdentityModel.Tokens.IssuerValidator"/> are set, IssuerValidatorUsingConfiguration takes
            priority.
            </remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.IssuerValidatorAsync">
            <summary>
            Definition for IssuerValidatorAsync. Left internal for now while we work out the details of async validation for all delegates.
            </summary>
            <param name="issuer">The issuer to validate.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <remarks>The delegate should return a non null string that represents the 'issuer'. If null a default value will be used.
            <see cref="T:Microsoft.IdentityModel.Tokens.IssuerValidatorAsync"/> if set, will be called before <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyValidatorUsingConfiguration"/> or <see cref="T:Microsoft.IdentityModel.Tokens.IssuerSigningKeyValidator"/>
            </remarks>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.LifetimeValidator">
            <summary>
            Definition for LifetimeValidator.
            </summary>
            <param name="notBefore">The 'notBefore' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="expires">The 'expiration' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenReplayValidator">
            <summary>
            Definition for TokenReplayValidator.
            </summary>
            <param name="expirationTime">The 'expiration' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SignatureValidator">
            <summary>
            Definition for SignatureValidator.
            </summary>
            <param name="token">A securityToken with a signature.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.SignatureValidatorUsingConfiguration">
            <summary>
            Definition for SignatureValidator.
            </summary>
            <param name="token">A securityToken with a signature.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <param name="configuration">The <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> that is required for validation.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenReader">
            <summary>
            Definition for TokenReader.
            </summary>
            <param name="token">A securityToken with a signature.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenDecryptionKeyResolver">
            <summary>
            Definition for TokenDecryptionKeyResolver.
            </summary>
            <param name="token">The <see cref="T:System.String"/> representation of the token to be decrypted.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> to be decrypted. The runtime by default passes null.</param>
            <param name="kid">A key identifier. It may be null.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>A <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> to use when decrypting the token.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TypeValidator">
            <summary>
            Definition for TypeValidator.
            </summary>
            <param name="type">The token type to validate.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The actual token type, that may be the same as <paramref name="type"/> or a different value if the token type was resolved from a different location.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TransformBeforeSignatureValidation">
            <summary>
            Definition for TransformBeforeSignatureValidation.
            </summary>
            <param name="token">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>A transformed <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters">
            <summary>
            Contains a set of parameters that are used by a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenHandler"/> when validating a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenValidationParameters.DefaultAuthenticationType">
            <summary>
            This is the default value of <see cref="P:System.Security.Claims.ClaimsIdentity.AuthenticationType"/> when creating a <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            The value is <c>"AuthenticationTypes.Federation"</c>.
            To change the value, set <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AuthenticationType"/> to a different value.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenValidationParameters.DefaultClockSkew">
            <summary>
            Default for the clock skew.
            </summary>
            <remarks>300 seconds (5 minutes).</remarks>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.TokenValidationParameters.DefaultMaximumTokenSizeInBytes">
            <summary>
            Default for the maximum token size.
            </summary>
            <remarks>250 KB (kilobytes).</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.#ctor(Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Copy constructor for <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ActorValidationParameters">
            <summary>
            Gets or sets <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AlgorithmValidator">
            <summary>
            Gets or sets a delegate used to validate the cryptographic algorithm used.
            </summary>
            <remarks>
            If set, this delegate will validate the cryptographic algorithm used and
            the algorithm will not be checked against <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAlgorithms"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AudienceValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the audience.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the 'audience', instead of default processing.
            This means that no default 'audience' validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience"/> is false, this delegate will still be called.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AuthenticationType">
            <summary>
            Gets or sets the AuthenticationType when creating a <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
            <exception cref="T:System.ArgumentNullException">If 'value' is null or whitespace.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ClockSkew">
            <summary>
            Gets or sets the clock skew to apply when validating a time.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">If 'value' is less than 0.</exception>
            The default is <c>300</c> seconds (5 minutes).
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.Clone">
            <summary>
            Returns a new instance of <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> with values copied from this object.
            </summary>
            <returns>A new <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> object copied from this object</returns>
            <remarks>This is a shallow Clone.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.CreateClaimsIdentity(Microsoft.IdentityModel.Tokens.SecurityToken,System.String)">
            <summary>
            Creates a <see cref="T:System.Security.Claims.ClaimsIdentity"/> using:
            <para><see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AuthenticationType"/></para>
            <para>'NameClaimType': If NameClaimTypeRetriever is set, call delegate, else call NameClaimType. If the result is a null or empty string, use <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultNameClaimType"/></para>.
            <para>'RoleClaimType': If RoleClaimTypeRetriever is set, call delegate, else call RoleClaimType. If the result is a null or empty string, use <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultRoleClaimType"/></para>.
            </summary>
            <returns>A <see cref="T:System.Security.Claims.ClaimsIdentity"/> with Authentication, NameClaimType and RoleClaimType set.</returns>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ConfigurationManager">
            <summary>
            If set, this property will be used to obtain the issuer and signing keys associated with the metadata endpoint of <see cref="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.Issuer"/>.
            The obtained issuer and signing keys will then be used along with those present on the TokenValidationParameters for validation of the incoming token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.CryptoProviderFactory">
            <summary>
            Users can override the default <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.CryptoProviderFactory"/> with this property. This factory will be used for creating signature providers.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.DebugId">
            <summary>
            Gets or sets a string that helps with setting breakpoints when debugging.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IgnoreTrailingSlashWhenValidatingAudience">
            <summary>
            Gets or sets a boolean that controls if a '/' is significant at the end of the audience.
            The default is <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IncludeTokenOnFailedValidation">
            <summary>
            Gets or sets the flag that indicates whether to include the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> when the validation fails.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidator">
            <summary>
            Gets or sets a delegate for validating the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the token.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the token, instead of default processing.
            This means that no default <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuerSigningKey"/> is false, this delegate will still be called.
            If both <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidatorUsingConfiguration"/> and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidator"/> are set, IssuerSigningKeyResolverUsingConfiguration takes
            priority.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidatorUsingConfiguration">
            <summary>
            Gets or sets a delegate for validating the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the token.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the token, instead of default processing.
            This means that no default <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuerSigningKey"/> is false, this delegate will still be called.
            This delegate should be used if properties from the configuration retrieved from the authority are necessary to validate the
            issuer signing key.
            If both <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidatorUsingConfiguration"/> and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidator"/> are set, IssuerSigningKeyValidatorUsingConfiguration takes
            priority.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.InstancePropertyBag">
            <summary>
            Gets a <see cref="T:System.Collections.Generic.IDictionary`2"/> that is unique to this instance.
            Calling <see cref="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.Clone"/> will result in a new instance of this IDictionary.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IsClone">
            <summary>
            Gets a value indicating if <see cref="M:Microsoft.IdentityModel.Tokens.TokenValidationParameters.Clone"/> was called to obtain this instance.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKey">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is to be used for signature validation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolver">
            <summary>
            Gets or sets a delegate that will be called to retrieve a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used for signature validation.
            </summary>
            <remarks>
            This <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> will be used to check the signature. This can be helpful when the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> does not contain a key identifier.
            If both <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolverUsingConfiguration"/> and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolver"/> are set, IssuerSigningKeyResolverUsingConfiguration takes
            priority.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolverUsingConfiguration">
            <summary>
            Gets or sets a delegate that will be called to retrieve a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used for signature validation using the
            <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> and <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/>.
            </summary>
            <remarks>
            This <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> will be used to check the signature. This can be helpful when the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> does not contain a key identifier.
            This delegate should be used if properties from the configuration retrieved from the authority are necessary to resolve the
            issuer signing key.
            If both <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolverUsingConfiguration"/> and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyResolver"/> are set, IssuerSigningKeyResolverUsingConfiguration takes
            priority.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeys">
            <summary>
            Gets or sets an <see cref="T:System.Collections.Generic.IEnumerable`1"/> used for signature validation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the issuer of the token.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the 'issuer' of the token, instead of default processing.
            This means that no default 'issuer' validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is false, this delegate will still be called.
            If both <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidatorUsingConfiguration"/> and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidator"/> are set, IssuerValidatorUsingConfiguration takes
            priority. 
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidatorAsync">
            <summary>
            Gets or sets a delegate that will be used to validate the issuer of the token.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the 'issuer' of the token, instead of default processing.
            This means that no default 'issuer' validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is false, this delegate will still be called.
            IssuerValidatorAsync takes precedence over <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidatorUsingConfiguration"/> and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidator"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidatorUsingConfiguration">
            <summary>
            Gets or sets a delegate that will be used to validate the issuer of the token.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the 'issuer' of the token, instead of default processing.
            This means that no default 'issuer' validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is false, this delegate will still be called.
            This delegate should be used if properties from the configuration retrieved from the authority are necessary to validate the issuer.
            If both <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidatorUsingConfiguration"/> and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidator"/> are set, IssuerValidatorUsingConfiguration takes
            priority.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TransformBeforeSignatureValidation">
            <summary>
            Gets or sets a delegate that will be called to transform a token to a supported format before validation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.LifetimeValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the lifetime of the token
            </summary>
            <remarks>
            If set, this delegate will be called to validate the lifetime of the token, instead of default processing.
            This means that no default lifetime validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateLifetime"/> is false, this delegate will still be called.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.LogTokenId">
            <summary>
            Gets or sets a <see cref="T:System.Boolean"/> that will decide if the token identifier claim needs to be logged.
            Default value is <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.LogValidationExceptions">
            <summary>
            Gets or sets a <see cref="T:System.Boolean"/> that will decide if validation failure needs to be logged as an error.
            Default value is <c>true</c> for backward compatibility of the behavior.
            If set to false, validation failures are logged as Information and then thrown.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.NameClaimType">
            <summary>
            Gets or sets a <see cref="T:System.String"/> that defines the <see cref="P:System.Security.Claims.ClaimsIdentity.NameClaimType"/>.
            </summary>
            <remarks>
            Controls the value <see cref="P:System.Security.Claims.ClaimsIdentity.Name"/> returns. It will return the first <see cref="P:System.Security.Claims.Claim.Value"/> where the <see cref="P:System.Security.Claims.Claim.Type"/> equals <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.NameClaimType"/>.
            The default is <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultNameClaimType"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.NameClaimTypeRetriever">
            <summary>
            Gets or sets a delegate that will be called to set the property <see cref="P:System.Security.Claims.ClaimsIdentity.NameClaimType"/> after validating a token.
            </summary>
            <remarks>
            The function will be passed:
            <para>The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</para>
            <para>The issuer associated with the token.</para>
            <para>Returns the value that will set the property <see cref="P:System.Security.Claims.ClaimsIdentity.NameClaimType"/>.</para>
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.PropertyBag">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IDictionary`2"/> that contains a collection of custom key/value pairs. This allows addition of parameters that could be used in custom token validation scenarios.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RefreshBeforeValidation">
            <summary>
            Gets or sets a boolean to control if configuration required to be refreshed before token validation.
            </summary>
            <remarks>
            The default is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RequireAudience">
            <summary>
            Gets or sets a value indicating whether SAML tokens must have at least one AudienceRestriction.
            The default is <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RequireExpirationTime">
            <summary>
            Gets or sets a value indicating whether tokens must have an 'expiration' value.
            The default is <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RequireSignedTokens">
            <summary>
            Gets or sets a value indicating whether a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> can be considered valid if not signed.
            The default is <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RoleClaimType">
            <summary>
            Gets or sets the <see cref="T:System.String"/> that defines the <see cref="P:System.Security.Claims.ClaimsIdentity.RoleClaimType"/>.
            </summary>
            <remarks>
            <para>Controls the results of <see cref="M:System.Security.Claims.ClaimsPrincipal.IsInRole(System.String)"/>.</para>
            <para>Each <see cref="T:System.Security.Claims.Claim"/> where <see cref="P:System.Security.Claims.Claim.Type"/> == <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RoleClaimType"/> will be checked for a match against the 'string' passed to <see cref="M:System.Security.Claims.ClaimsPrincipal.IsInRole(System.String)"/>.</para>
            The default is <see cref="F:System.Security.Claims.ClaimsIdentity.DefaultRoleClaimType"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RoleClaimTypeRetriever">
            <summary>
            Gets or sets a delegate that will be called to set the property <see cref="P:System.Security.Claims.ClaimsIdentity.RoleClaimType"/> after validating a token.
            </summary>
            <remarks>
            The function will be passed:
            <para>The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</para>
            <para>The issuer associated with the token.</para>
            <para>Returns the value that will set the property <see cref="P:System.Security.Claims.ClaimsIdentity.RoleClaimType"/>.</para>
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.SaveSigninToken">
            <summary>
            Gets or sets a boolean to control if the original token should be saved after the security token is validated.
            </summary>
            <remarks>The runtime will consult this value and save the original token that was validated.
            The default is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.SignatureValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the signature of the token.
            </summary>
            <remarks>
            If set, this delegate will be called to signature of the token, instead of default processing.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.SignatureValidatorUsingConfiguration">
            <summary>
            Gets or sets a delegate that will be used to validate the signature of the token using the <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> and
            the <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/>.
            </summary>
            <remarks>
            If set, this delegate will be called to signature of the token, instead of default processing.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenDecryptionKey">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that is to be used for decryption.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenDecryptionKeyResolver">
            <summary>
            Gets or sets a delegate that will be called to retreive a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> used for decryption.
            </summary>
            <remarks>
            This <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> will be used to decrypt the token. This can be helpful when the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> does not contain a key identifier.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenDecryptionKeys">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that is to be used for decrypting inbound tokens.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReader">
            <summary>
            Gets or sets a delegate that will be used to read the token.
            </summary>
            <remarks>
            If set, this delegate will be called to read the token instead of default processing.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache">
            <summary>
            Gets or set the <see cref="T:Microsoft.IdentityModel.Tokens.ITokenReplayCache"/> that store tokens that can be checked to help detect token replay.
            </summary>
            <remarks>If set, then tokens must have an expiration time or the runtime will fault.</remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the token replay of the token
            </summary>
            <remarks>
            If set, this delegate will be called to validate the token replay of the token, instead of default processing.
            This means no default token replay validation will occur.
            Even if <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateTokenReplay"/> is false, this delegate will still be called.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TryAllIssuerSigningKeys">
            <summary>
            Gets or sets a value indicating whether all <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeys"/> should be tried during signature validation when a key is not matched to token kid or if token kid is empty.
            The default is <c>true</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TypeValidator">
            <summary>
            Gets or sets a delegate that will be used to validate the type of the token.
            If the token type cannot be validated, an exception MUST be thrown by the delegate.
            Note: the 'type' parameter may be null if it couldn't be extracted from its usual location.
            Implementations that need to resolve it from a different location can use the 'token' parameter.
            </summary>
            <remarks>
            If set, this delegate will be called to validate the 'type' of the token, instead of default processing.
            This means that no default 'type' validation will occur.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateActor">
            <summary>
            Gets or sets a value indicating if an actor token is detected, whether it should be validated.
            The default is <c>false</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience">
            <summary>
            Gets or sets a boolean to control if the audience will be validated during token validation.
            </summary>
            <remarks>Validation of the audience, mitigates forwarding attacks. For example, a site that receives a token, could not replay it to another side.
            A forwarded token would contain the audience of the original site.
            This boolean only applies to default audience validation. If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.AudienceValidator"/> is set, it will be called regardless of whether this
            property is true or false.
            The default is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer">
            <summary>
            Gets or sets a boolean to control if the issuer will be validated during token validation.
            </summary>
            <remarks>
            Validation of the issuer mitigates forwarding attacks that can occur when an
            IdentityProvider represents multiple tenants and signs tokens with the same keys.
            It is possible that a token issued for the same audience could be from a different tenant. For example an application could accept users from
            contoso.onmicrosoft.com but not fabrikam.onmicrosoft.com, both valid tenants. An application that accepts tokens from fabrikam could forward them
            to the application that accepts tokens for contoso.
            This boolean only applies to default issuer validation. If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerValidator"/> is set, it will be called regardless of whether this
            property is true or false.
            The default is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateWithLKG">
            <summary>
            Gets or sets a boolean to control if the LKG configuration will be used for token validation.
            </summary>
            <remarks>
            The default is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuerSigningKey">
            <summary>
            Gets or sets a boolean that controls if validation of the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the securityToken is called.
            </summary>
            <remarks>It is possible for tokens to contain the public key needed to check the signature. For example, X509Data can be hydrated into an X509Certificate,
            which can be used to validate the signature. In these cases it is important to validate the SigningKey that was used to validate the signature. 
            This boolean only applies to default signing key validation. If <see cref= "P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.IssuerSigningKeyValidator" /> is set, it will be called regardless of whether this
            property is true or false.
            The default is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateLifetime">
            <summary>
            Gets or sets a boolean to control if the lifetime will be validated during token validation.
            </summary>
            <remarks>
            This boolean only applies to default lifetime validation. If <see cref= "P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.LifetimeValidator" /> is set, it will be called regardless of whether this
            property is true or false.
            The default is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateSignatureLast">
            <summary>
            Gets or sets a boolean that controls the validation order of the payload and signature during token validation.
            </summary>
            <remarks>If <see cref= "P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateSignatureLast" /> is set to true, it will validate payload ahead of signature.
            The default is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateTokenReplay">
            <summary>
            Gets or sets a boolean to control if the token replay will be validated during token validation.
            </summary> 
            <remarks>
            This boolean only applies to default token replay validation. If <see cref= "P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayValidator" /> is set, it will be called regardless of whether this
            property is true or false.
            The default is <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAlgorithms">
            <summary>
            Gets or sets the valid algorithms for cryptographic operations.
            </summary>
            <remarks>
            If set to a non-empty collection, only the algorithms listed will be considered valid.
            The default is <c>null</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudience">
            <summary>
            Gets or sets a string that represents a valid audience that will be used to check against the token's audience.
            The default is <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that contains valid audiences that will be used to check against the token's audience.
            The default is <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer">
            <summary>
            Gets or sets a <see cref="T:System.String"/> that represents a valid issuer that will be used to check against the token's issuer.
            The default is <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that contains valid issuers that will be used to check against the token's issuer.
            The default is <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidTypes">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IEnumerable`1"/> that contains valid types that will be used to check against the JWT header's 'typ' claim.
            If this property is not set, the 'typ' header claim will not be validated and all types will be accepted.
            In the case of a JWE, this property will ONLY apply to the inner token header.
            The default is <c>null</c>.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.TokenValidationResult">
            <summary>
            Contains artifacts obtained when a SecurityToken is validated.
            A SecurityTokenHandler returns an instance that captures the results of validating a token.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationResult.#ctor">
            <summary>
            Creates an instance of <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationResult"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.TokenValidationResult.#ctor(Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenHandler,Microsoft.IdentityModel.Tokens.TokenValidationParameters,System.String)">
            <summary>
            This ctor is used by the JsonWebTokenHandler as part of delaying creation of ClaimsIdentity.
            </summary>
            <param name="securityToken"></param>
            <param name="tokenHandler"></param>
            <param name="validationParameters"></param>
            <param name="issuer"></param>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.Claims">
            <summary>
            The <see cref="T:System.Collections.Generic.Dictionary`2"/> created from the validated security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.ClaimsIdentity">
            <summary>
            The <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.ClaimsIdentity"/> created from the validated security token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.ClaimsIdentityNoLocking">
            <summary>
            Gets or sets the <see cref="F:Microsoft.IdentityModel.Tokens.TokenValidationResult._claimsIdentity"/> without synchronization. All accesses must either
            be protected or used when the caller knows access is serialized.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.ClaimsIdentitySyncObj">
            <summary>Gets the object to use in <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.ClaimsIdentity"/> for double-checked locking.</summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.Exception">
            <summary>
            Gets or sets the <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.Exception"/> that occurred during validation.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.Issuer">
            <summary>
            Gets or sets the issuer that was found in the token.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.IsValid">
            <summary>
            True if the token was successfully validated, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.PropertyBag">
            <summary>
            Gets or sets the <see cref="T:System.Collections.Generic.IDictionary`2"/> that contains a collection of custom key/value pairs. This allows addition of data that could be used in custom scenarios. This uses <see cref="P:System.StringComparer.Ordinal"/> for case-sensitive comparison of keys.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.SecurityToken">
            <summary>
            Gets or sets the <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.SecurityToken"/> that was validated.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.TokenOnFailedValidation">
            <summary>
            The <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.SecurityToken"/> to be returned when validation fails.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.TokenContext">
            <summary>
            Gets or sets the <see cref="T:Microsoft.IdentityModel.Tokens.CallContext"/> that contains call information.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.TokenType">
            <summary>
            Gets or sets the token type of the <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationResult.SecurityToken"/> that was validated.
            When a <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TypeValidator"/> is registered,
            the type returned by the delegate is used to populate this property.
            Otherwise, the type is resolved from the token itself, if available
            (e.g for a JSON Web Token, from the "typ" header). 
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.UniqueId">
            <summary>
            Generates unique IDs.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.UniqueId.CreateUniqueId">
            <summary>
            Creates a unique ID suitable for use in an xml:id field. The value is 
            not hard to guess but is unique.
            </summary>
            <returns>The unique ID.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.UniqueId.CreateUniqueId(System.String)">
            <summary>
            Creates a unique ID similar to that created by CreateNonRandomId, 
            but instead of an underscore, the supplied prefix is used.
            </summary>
            <param name="prefix">The prefix to use.</param>
            <returns>The unique ID, with the given prefix.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.UniqueId.CreateRandomId">
            <summary>
            Creates a unique, random ID suitable for use in an xml:id field. The 
            value is hard to guess and unique.
            </summary>
            <returns>The unique ID.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.UniqueId.CreateRandomId(System.String)">
            <summary>
            Creates a unique, random ID similar to that created by CreateRandomId,
            but instead of an underscore, the supplied prefix is used.
            </summary>
            <param name="prefix">The prefix to use.</param>
            <returns>The random URI.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.UniqueId.CreateRandomUri">
            <summary>
            Creates a unique, random ID suitable for use as a URI. The value is 
            hard to guess and unique. The URI is in the urn:uuid: namespace.
            </summary>
            <returns>The random URI.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Utility">
            <summary>
            Contains some utility methods.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.Utility.Empty">
            <summary>
            A string with "empty" value.
            </summary>
        </member>
        <member name="F:Microsoft.IdentityModel.Tokens.Utility.Null">
            <summary>
            A string with "null" value.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.CloneByteArray(System.Byte[])">
            <summary>
            Creates a copy of the byte array.
            </summary>
            <param name="src">The resource array.</param>
            <returns>A copy of the byte array.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.SerializeAsSingleCommaDelimitedString(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Serializes the list of strings into string as follows:
            'str1','str2','str3' ...
            </summary>
            <param name="strings">
            The strings used to build a comma delimited string.
            </param>
            <returns>
            The single <see cref="T:System.String"/>.
            </returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.IsHttps(System.String)">
            <summary>
            Returns whether the input string is https.
            </summary>
            <param name="address">The input string.</param>
            <remarks>true if the input string is https; otherwise, false.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.IsHttps(System.Uri)">
            <summary>
            Returns whether the input uri is https.
            </summary>
            <param name="uri"><see cref="T:System.Uri"/>.</param>
            <returns>true if the input uri is https; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.AreEqual(System.Byte[],System.Byte[])">
            <summary>
            Compares two byte arrays for equality. Hash size is fixed normally it is 32 bytes.
            The attempt here is to take the same time if an attacker shortens the signature OR changes some of the signed contents.
            </summary>
            <param name="a">
            One set of bytes to compare.
            </param>
            <param name="b">
            The other set of bytes to compare with.
            </param>
            <returns>
            true if the bytes are equal, false otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Utility.AreEqual(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Int32)">
            <summary>
            Compares two byte spans for equality. Hash size is fixed normally it is 32 bytes.
            The attempt here is to take the same time if an attacker shortens the signature OR changes some of the signed contents.
            </summary>
            <param name="a">
            One set of bytes to compare.
            </param>
            <param name="b">
            The other set of bytes to compare with.
            </param>
            <param name="length">length of spans to check</param>
            <returns>
            true if the bytes are equal, false otherwise.
            </returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.Validators">
            <summary>
            AudienceValidator
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateAlgorithm(System.String,Microsoft.IdentityModel.Tokens.SecurityKey,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates if a given algorithm for a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> is valid.
            </summary>
            <param name="algorithm">The algorithm to be validated.</param>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateAudience(System.Collections.Generic.IEnumerable{System.String},Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if the audiences found in a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> are valid.
            </summary>
            <param name="audiences">The audiences found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException">If 'audiences' is null and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateAudience"/> is true.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudience"/> is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences"/> is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException">If none of the 'audiences' matched either <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudience"/> or one of <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidAudiences"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuer(System.String,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Determines if an issuer found in a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> is valid.
            </summary>
            <param name="issuer">The issuer to validate</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException">If 'issuer' is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is true.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/> is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If 'issuer' failed to matched either <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> or one of <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuer(System.String,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.BaseConfiguration)">
            <summary>
            Determines if an issuer found in a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> is valid.
            </summary>
            <param name="issuer">The issuer to validate</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <param name="configuration">The <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> required for issuer and signing key validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException">If 'issuer' is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is true.</exception>
            <exception cref="T:System.ArgumentNullException">If ' configuration' is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/> is null and <see cref="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.Issuer"/> is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If 'issuer' failed to matched either <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> or one of <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/> or <see cref="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.Issuer"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuerAsync(System.String,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.BaseConfiguration)">
            <summary>
            Determines if an issuer found in a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> is valid.
            </summary>
            <param name="issuer">The issuer to validate</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <param name="configuration">The <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> required for issuer and signing key validation.</param>
            <returns>The issuer to use when creating the "Claim"(s) in a "ClaimsIdentity".</returns>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null.</exception>
            <exception cref="T:System.ArgumentNullException">If 'issuer' is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidateIssuer"/> is true.</exception>
            <exception cref="T:System.ArgumentNullException">If ' configuration' is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/> is null and <see cref="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.Issuer"/> is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException">If 'issuer' failed to matched either <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuer"/> or one of <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidIssuers"/> or <see cref="P:Microsoft.IdentityModel.Tokens.BaseConfiguration.Issuer"/>.</exception>
            <remarks>An EXACT match is required.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuerSecurityKey(Microsoft.IdentityModel.Tokens.SecurityKey,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException"> if 'securityKey' is null and ValidateIssuerSigningKey is true.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'securityToken' is null and ValidateIssuerSigningKey is true.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'validationParameters' is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuerSecurityKey(Microsoft.IdentityModel.Tokens.SecurityKey,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters,Microsoft.IdentityModel.Tokens.BaseConfiguration)">
            <summary>
            Validates the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <param name="configuration">The <see cref="T:Microsoft.IdentityModel.Tokens.BaseConfiguration"/> required for issuer and signing key validation.</param>
            <exception cref="T:System.ArgumentNullException"> if 'securityKey' is null and ValidateIssuerSigningKey is true.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'securityToken' is null and ValidateIssuerSigningKey is true.</exception>
            <exception cref="T:System.ArgumentNullException"> if 'validationParameters' is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateIssuerSigningKeyLifeTime(Microsoft.IdentityModel.Tokens.SecurityKey,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Given a signing key, when it's derived from a certificate, validates that the certificate is already active and non-expired
            </summary>
            <param name="securityKey">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityKey"/> that signed the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="validationParameters">The <see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> that are used to validate the token.</param>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateLifetime(System.Nullable{System.DateTime},System.Nullable{System.DateTime},Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the lifetime of a <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.
            </summary>
            <param name="notBefore">The 'notBefore' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="expires">The 'expiration' time found in the <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/>.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException">If 'expires.HasValue' is false and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.RequireExpirationTime"/> is true.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException">If 'notBefore' is &gt; 'expires'.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException">If 'notBefore' is &gt; DateTime.UtcNow.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException">If 'expires' is &lt; DateTime.UtcNow.</exception>
            <remarks>All time comparisons apply <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ClockSkew"/>.</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateTokenReplay(System.Nullable{System.DateTime},System.String,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates if a token has been replayed.
            </summary>
            <param name="expirationTime">When does the security token expire.</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If 'securityToken' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null or whitespace.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/> is not null and expirationTime.HasValue is false. When a TokenReplayCache is set, tokens require an expiration time.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException">If the 'securityToken' is found in the cache.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException">If the 'securityToken' could not be added to the <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/>.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateTokenReplay(System.String,System.Nullable{System.DateTime},Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates if a token has been replayed.
            </summary>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> being validated.</param>
            <param name="expirationTime">When does the security token expire.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If 'securityToken' is null or whitespace.</exception>
            <exception cref="T:System.ArgumentNullException">If 'validationParameters' is null or whitespace.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException">If <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/> is not null and expirationTime.HasValue is false. When a TokenReplayCache is set, tokens require an expiration time.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayDetectedException">If the 'securityToken' is found in the cache.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenReplayAddFailedException">If the 'securityToken' could not be added to the <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.TokenReplayCache"/>.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.Validators.ValidateTokenType(System.String,Microsoft.IdentityModel.Tokens.SecurityToken,Microsoft.IdentityModel.Tokens.TokenValidationParameters)">
            <summary>
            Validates the type of the token.
            </summary>
            <param name="type">The token type or <c>null</c> if it couldn't be resolved (e.g from the 'typ' header for a JWT).</param>
            <param name="securityToken">The <see cref="T:Microsoft.IdentityModel.Tokens.SecurityToken"/> that is being validated.</param>
            <param name="validationParameters"><see cref="T:Microsoft.IdentityModel.Tokens.TokenValidationParameters"/> required for validation.</param>
            <exception cref="T:System.ArgumentNullException">If <paramref name="validationParameters"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">If <paramref name="securityToken"/> is null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException">If <paramref name="type"/> is null or whitespace and <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidTypes"/> is not null.</exception>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenInvalidTypeException">If <paramref name="type"/> failed to match <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidTypes"/>.</exception>
            <remarks>An EXACT match is required. <see cref="F:System.StringComparison.Ordinal"/> (case sensitive) is used for comparing <paramref name="type"/> against <see cref="P:Microsoft.IdentityModel.Tokens.TokenValidationParameters.ValidTypes"/>.</remarks>
            <returns>The actual token type, that may be the same as <paramref name="type"/> or a different value if the token type was resolved from a different location.</returns>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.X509EncryptingCredentials">
            <summary>
            An <see cref="T:Microsoft.IdentityModel.Tokens.X509EncryptingCredentials"/> designed to construct <see cref="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials"/> based on a x509 certificate.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509EncryptingCredentials.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Designed to construct <see cref="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials"/> based on a x509 certificate.
            </summary>
            <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/></param>
            <remarks>
            <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.DefaultAsymmetricKeyWrapAlgorithm"/> will be used as the key wrap algorithm
            <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.DefaultSymmetricEncryptionAlgorithm"/> will be used as the data encryption algorithm
            </remarks>
            <exception cref="T:System.ArgumentNullException">if 'certificate' is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509EncryptingCredentials.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.String,System.String)">
            <summary>
            Designed to construct <see cref="T:Microsoft.IdentityModel.Tokens.EncryptingCredentials"/> based on the x509 certificate, a key wrap algorithm, and data encryption algorithm.
            </summary>
            <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/></param>
            <param name="keyWrapAlgorithm">A key wrap algorithm</param>
            <param name="dataEncryptionAlgorithm">Data encryption algorithm</param>
            <exception cref="T:System.ArgumentNullException">if 'certificate' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'keyWrapAlgorithm' is null or empty.</exception>
            <exception cref="T:System.ArgumentNullException">if 'dataEncryptionAlgorithm' is null or empty.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509EncryptingCredentials.Certificate">
            <summary>
            Gets the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> used by this instance.
            </summary>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.X509SecurityKey">
            <summary>
            An <see cref="T:Microsoft.IdentityModel.Tokens.AsymmetricSecurityKey"/> that is backed by a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Instantiates a <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> using a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>
            </summary>
            <param name="certificate">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> to use.</param>
            <exception cref="T:System.ArgumentNullException">if <paramref name="certificate"/> is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.String)">
            <summary>
            Instantiates a <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> using a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>.
            </summary>
            <param name="certificate">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> to use.</param>
            <param name="keyId">The value to set for the KeyId</param>
            <exception cref="T:System.ArgumentNullException">if <paramref name="certificate"/> is null.</exception>
            <exception cref="T:System.ArgumentNullException">if <paramref name="keyId"/> is null or empty.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.KeySize">
            <summary>
            Gets the key size.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.X5t">
            <summary>
            Gets the X5t of this <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.PrivateKey">
            <summary>
            Returns the private key from the <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.PublicKey">
            <summary>
            Gets the public key from the <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>.
            </summary>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.HasPrivateKey">
            <summary>
            Gets a bool indicating if a private key exists.
            </summary>
            <return>true if it has a private key; otherwise, false.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.PrivateKeyStatus">
            <summary>
            Gets an enum indicating if a private key exists.
            </summary>
            <return>'Exists' if private key exists for sure; 'DoesNotExist' if private key doesn't exist for sure; 'Unknown' if we cannot determine.</return>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SecurityKey.Certificate">
            <summary>
            Gets the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.CanComputeJwkThumbprint">
            <summary>
            Determines whether the <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/> can compute a JWK thumbprint.
            </summary>
            <returns><c>true</c> if JWK thumbprint can be computed; otherwise, <c>false</c>.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.ComputeJwkThumbprint">
            <summary>
            Computes a sha256 hash over the <see cref="T:Microsoft.IdentityModel.Tokens.X509SecurityKey"/>.
            </summary>
            <returns>A JWK thumbprint.</returns>
            <remarks>https://datatracker.ietf.org/doc/html/rfc7638</remarks>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.Equals(System.Object)">
            <summary>
            Returns a bool indicating if this key is equivalent to another key.
            </summary>
            <return>true if the keys are equal; otherwise, false.</return>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SecurityKey.GetHashCode">
            <summary>
            Returns an int hash code.
            </summary>
            <return>An int hash code</return>
        </member>
        <member name="T:Microsoft.IdentityModel.Tokens.X509SigningCredentials">
            <summary>
            Defines the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/>, algorithm and digest for digital signatures.
            </summary>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SigningCredentials.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.X509SigningCredentials"/> class.
            </summary>
            <param name="certificate"><see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> that will be used for signing.</param>
            <remarks>Algorithm will be set to <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/>.
            the 'digest method' if needed may be implied from the algorithm. For example <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/> implies Sha256.</remarks>
            <exception cref="T:System.ArgumentNullException">if 'certificate' is null.</exception>
        </member>
        <member name="M:Microsoft.IdentityModel.Tokens.X509SigningCredentials.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.IdentityModel.Tokens.X509SigningCredentials"/> class.
            </summary>
            <param name="certificate">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> that will be used for signing.</param>
            <param name="algorithm">The signature algorithm to apply.</param>
            <remarks>the 'digest method' if needed may be implied from the algorithm. For example <see cref="F:Microsoft.IdentityModel.Tokens.SecurityAlgorithms.RsaSha256"/> implies Sha256.</remarks>
            <exception cref="T:System.ArgumentNullException">if 'certificate' is null.</exception>
            <exception cref="T:System.ArgumentNullException">if 'algorithm' is null or empty.</exception>
        </member>
        <member name="P:Microsoft.IdentityModel.Tokens.X509SigningCredentials.Certificate">
            <summary>
            Gets the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2"/> used by this instance.
            </summary>
        </member>
    </members>
</doc>
