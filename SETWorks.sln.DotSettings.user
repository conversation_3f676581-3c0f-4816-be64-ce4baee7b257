﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=C_003A_005CUsers_005Candre_005CDocuments_005CSW_005CSETWorks_005Cbin_005CStackExchange_002ERedis_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/Environment/Hierarchy/EntityFrameworkOptions/IsAlreadyNotifiedAboutEntityFramework/@EntryValue">True</s:Boolean>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=5c1b823b_002Dea32_002D4246_002D9640_002D1f2f103f0144/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="All tests from &amp;lt;tests&amp;gt;\&amp;lt;SetWorks.UnitTests&amp;gt;" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;Or&gt;&#xD;
    &lt;Project Location="C:\Users\<USER>\Documents\SW\SetWorks.UnitTests" Presentation="&amp;lt;tests&amp;gt;\&amp;lt;SetWorks.UnitTests&amp;gt;" /&gt;&#xD;
    &lt;Project Location="C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests" Presentation="&amp;lt;tests&amp;gt;\&amp;lt;SetWorks.IntegrationTests&amp;gt;" /&gt;&#xD;
  &lt;/Or&gt;&#xD;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>