<?xml version="1.0"?>
<doc xml:lang="en">
    <assembly>
        <name>Microsoft.AI.Web</name>
    </assembly>
    <members>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.HttpRequestExtensions">
            <summary>
            HttpRequest Extensions.
            </summary>
            <summary>
            HttpRequest Extensions.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants">
            <summary>
            Request tracking constants and keys.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants.RequestTelemetryItemName">
            <summary>
            Name of the HttpContext item containing RequestTelemetry object.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants.TransferHandlerType">
            <summary>
            Type name for the transfer handler. This handler is used to enable extension(less) URI 
            and it produces extra request, which should not be counted.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingConstants.WebAuthenticatedUserCookieName">
            <summary>
            The name of the cookie which holds authenticated user context information.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.RequestTrackingExtensions.CreateRequestNamePrivate(System.Web.HttpContext)">
            <summary>
            Creates request name on the base of HttpContext.
            </summary>
            <returns>Controller/Action for MVC or path for other cases.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.SyntheticUserAgentFilter">
            <summary>
            Allows configuration of patterns for synthetic traffic filters.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.Implementation.SyntheticUserAgentFilter.Pattern">
            <summary>
            Gets or sets the regular expression pattern applied to the user agent string to determine whether traffic is synthetic.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.Implementation.SyntheticUserAgentFilter.SourceName">
            <summary>
            Gets or sets the readable name for the synthetic traffic source. If not provided, defaults to the pattern match.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource">
            <summary>
            ETW EventSource tracing class.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Log">
            <summary>
            Instance of the PlatformEventSource class.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords">
            <summary>
            Keywords for the PlatformEventSource. Those keywords should match keywords in Core.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords.UserActionable">
            <summary>
            Key word for user actionable events.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords.Diagnostics">
            <summary>
            Diagnostics tracing keyword.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventSource.Keywords.VerboseFailure">
            <summary>
            Keyword for errors that trace at Verbose level.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebEventsPublisher">
            <summary>
            Class provides methods to post event about Web event like begin or end of the request.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.Implementation.WebEventsPublisher.Instance">
            <summary>
            WebEventsPublisher static instance.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.Implementation.WebEventsPublisher.Log">
            <summary>
            Gets the instance of WebEventsPublisher type.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebEventsPublisher.OnBegin">
            <summary>
            Method generates event about begin of the request.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebEventsPublisher.OnEnd">
            <summary>
            Method generates event about end of the request.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebEventsPublisher.OnError">
            <summary>
            Method generates event in case if request failed.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase">
            <summary>
            Base class for WebOperationTelemetryInitializers.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase.Initialize(Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Base implementation of the initialization method.
            </summary>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryInitializerBase.ResolvePlatformContext">
            <summary>
            Resolved web platform specific context.
            </summary>
            <returns>An instance of the context.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase">
            <summary>
            Base web telemetry module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.ModuleName">
            <summary>
            Gets the module name which is added to be used for internal tracing instead of GetType on each request to improve performance.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.OnBeginRequest(Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,System.Web.HttpContext)">
            <summary>
            Post initialization Web Telemetry Module callback.
            </summary>
            <param name="requestTelemetry">An instance of request telemetry context.</param>
            <param name="platformContext">Platform specific context.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.OnEndRequest(Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,System.Web.HttpContext)">
            <summary>
            Request telemetry finalization - sending callback Web Telemetry Module callback.
            </summary>
            <param name="requestTelemetry">An instance of request telemetry context.</param>
            <param name="platformContext">Platform specific context.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.Implementation.WebTelemetryModuleBase.OnError(Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,System.Web.HttpContext)">
            <summary>
            Http Error reporting Web Telemetry Module callback.
            </summary>
            <param name="requestTelemetry">An instance of request telemetry context.</param>
            <param name="platformContext">Platform specific context.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.AccountIdTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the User properties of Context corresponding to a RequestTelemetry object.
            User.AccountId is updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.User.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AccountIdTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule">
            <summary>
            Platform agnostic module for web application instrumentation.
            </summary>
        </member>
        <member name="F:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.isEnabled">
            <summary>
            Indicates if module initialized successfully.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.Init(System.Web.HttpApplication)">
            <summary>
            Initializes module for a given application.
            </summary>
            <param name="context">HttpApplication instance.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule.Dispose">
            <summary>
            Required IDisposable implementation.
            </summary>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.AuthenticatedUserIdTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the User properties of Context corresponding to a RequestTelemetry object.
            User.AuthenticatedUserId is updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.User.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.AuthenticatedUserIdTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the correlation context for all telemetry items in web application.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.ParentOperationIdHeaderName">
            <summary>
            Gets or sets the name of the header to get parent operation Id from.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.RootOperationIdHeaderName">
            <summary>
            Gets or sets the name of the header to get root operation Id from.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.OperationCorrelationTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer">
            <summary>
            Telemetry initializer populates client IP address for the current request.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.HeaderNames">
            <summary>
            Gets a list of request header names that is used to check client id.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.HeaderValueSeparators">
            <summary>
            Gets or sets a header values separator.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.UseFirstIp">
            <summary>
            Gets or sets a value indicating whether the first or the last IP should be used from the lists of IPs in the header.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ClientIpHeaderTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule">
            <summary>
            Telemetry module to collect unhandled exceptions caught by http module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule.OnError(System.Web.HttpContext)">
            <summary>
            Implements on error callback of http module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.ExceptionTrackingTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initializes the telemetry module.
            </summary>
            <param name="configuration">Telemetry configuration to use for initialization.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.OperationNameTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the NAME property of OperationContext corresponding to a TraceTelemetry object.
            If the telemetry object is of type RequestTelemetry, then the Name of the RequestTelemetry is updated. For all other cases,
            Operation.Name is updated with the name derived from the HttpContext.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.OperationNameTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="rootRequestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule">
            <summary>
            Telemetry module tracking requests using http module.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.Handlers">
            <summary>
            Gets the list of handler types for which requests telemetry will not be collected
            if request was successful.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.OnBeginRequest(System.Web.HttpContext)">
            <summary>
            Implements on begin callback of http module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.OnEndRequest(System.Web.HttpContext)">
            <summary>
            Implements on end callback of http module.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.Initialize(Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration)">
            <summary>
            Initializes the telemetry module.
            </summary>
            <param name="configuration">Telemetry configuration to use for initialization.</param>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.NeedProcessRequest(System.Web.HttpContext)">
            <summary>
            Verifies context to detect whether or not request needs to be processed.
            </summary>
            <param name="httpContext">Current http context.</param>
            <returns>True if request needs to be processed, otherwise - False.</returns>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.RequestTrackingTelemetryModule.IsHandlerToFilter(System.Web.IHttpHandler)">
            <summary>
            Checks whether or not handler is a transfer handler.
            </summary>
            <param name="handler">An instance of handler to validate.</param>
            <returns>True if handler is a transfer handler, otherwise - False.</returns>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.SessionTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the Session properties of Context corresponding to a RequestTelemetry object.
            Session is updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.Session.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.SessionTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.WebTestTelemetryInitializer">
            <summary>
            A telemetry initializer that will update the User, Session and Operation contexts if request originates from a web test.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.WebTestTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.SyntheticUserAgentTelemetryInitializer">
            <summary>
            A telemetry initializer that determines if the request came from a synthetic source based on the user agent string.
            </summary>
        </member>
        <member name="P:Microsoft.ApplicationInsights.Web.SyntheticUserAgentTelemetryInitializer.Filters">
            <summary>
            Gets or sets the configured patterns for matching synthetic traffic filters through user agent string.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.SyntheticUserAgentTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:Microsoft.ApplicationInsights.Web.UserTelemetryInitializer">
            <summary>
            A telemetry initializer that will set the User properties of Context corresponding to a RequestTelemetry object.
            User.Id are updated with properties derived from the RequestTelemetry.RequestTelemetry.Context.User.
            </summary>
        </member>
        <member name="M:Microsoft.ApplicationInsights.Web.UserTelemetryInitializer.OnInitializeTelemetry(System.Web.HttpContext,Microsoft.ApplicationInsights.DataContracts.RequestTelemetry,Microsoft.ApplicationInsights.Channel.ITelemetry)">
            <summary>
            Implements initialization logic.
            </summary>
            <param name="platformContext">Http context.</param>
            <param name="requestTelemetry">Request telemetry object associated with the current request.</param>
            <param name="telemetry">Telemetry item to initialize.</param>
        </member>
        <member name="T:System.Web.HttpContextExtension">
            <summary>
            HttpContextExtension class provides extensions methods for accessing Web Application Insights objects.
            </summary>
        </member>
        <member name="M:System.Web.HttpContextExtension.GetRequestTelemetry(System.Web.HttpContext)">
            <summary>
            Provide access to request generated by Web Application Insights SDK.
            </summary>
            <param name="context">HttpContext instance.</param>
            <returns>Request telemetry instance or null.</returns>
        </member>
    </members>
</doc>
