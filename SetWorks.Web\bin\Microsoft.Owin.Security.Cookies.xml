<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Security.Cookies</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieAuthenticationDefaults">
            <summary>
            Default values related to cookie-based authentication middleware
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieAuthenticationDefaults.AuthenticationType">
            <summary>
            The default value used for CookieAuthenticationOptions.AuthenticationType
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieAuthenticationDefaults.CookiePrefix">
            <summary>
            The prefix used to provide a default CookieAuthenticationOptions.CookieName
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieAuthenticationDefaults.ReturnUrlParameter">
            <summary>
            The default value of the CookieAuthenticationOptions.ReturnUrlParameter
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieAuthenticationDefaults.LoginPath">
            <summary>
            The default value used by UseApplicationSignInCookie for the
            CookieAuthenticationOptions.LoginPath
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieAuthenticationDefaults.LogoutPath">
            <summary>
            The default value used by UseApplicationSignInCookie for the
            CookieAuthenticationOptions.LogoutPath
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieSecureOption">
            <summary>
            Determines how the identity cookie's security property is set.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieSecureOption.SameAsRequest">
            <summary>
            If the URI that provides the cookie is HTTPS, then the cookie will only be returned to the server on 
            subsequent HTTPS requests. Otherwise if the URI that provides the cookie is HTTP, then the cookie will 
            be returned to the server on all HTTP and HTTPS requests. This is the default value because it ensures
            HTTPS for all authenticated requests on deployed servers, and also supports HTTP for localhost development 
            and for servers that do not have HTTPS support.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieSecureOption.Never">
            <summary>
            CookieOptions.Secure is never marked true. Use this value when your login page is HTTPS, but other pages
            on the site which are HTTP also require authentication information. This setting is not recommended because
            the authentication information provided with an HTTP request may be observed and used by other computers
            on your local network or wireless connection.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieSecureOption.Always">
            <summary>
            CookieOptions.Secure is always marked true. Use this value when your login page and all subsequent pages
            requiring the authenticated identity are HTTPS. Local development will also need to be done with HTTPS urls.
            </summary>
        </member>
        <member name="T:Owin.CookieAuthenticationExtensions">
            <summary>
            Extension methods provided by the cookies authentication middleware
            </summary>
        </member>
        <member name="M:Owin.CookieAuthenticationExtensions.UseCookieAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions)">
            <summary>
            Adds a cookie-based authentication middleware to your web application pipeline.
            </summary>
            <param name="app">The IAppBuilder passed to your configuration method</param>
            <param name="options">An options class that controls the middleware behavior</param>
            <returns>The original app parameter</returns>
        </member>
        <member name="M:Owin.CookieAuthenticationExtensions.UseCookieAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions,Owin.PipelineStage)">
            <summary>
            Adds a cookie-based authentication middleware to your web application pipeline.
            </summary>
            <param name="app">The IAppBuilder passed to your configuration method</param>
            <param name="options">An options class that controls the middleware behavior</param>
            <param name="stage"></param>
            <returns>The original app parameter</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieAuthenticationMiddleware">
            <summary>
            Cookie based authentication middleware
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Cookies.CookieAuthenticationMiddleware"/>
            </summary>
            <param name="next">The next middleware in the OWIN pipeline to invoke</param>
            <param name="app">The OWIN application</param>
            <param name="options">Configuration options for the middleware</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationMiddleware.CreateHandler">
            <summary>
            Provides the <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> object for processing authentication-related requests.
            </summary>
            <returns>An <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> configured with the <see cref="T:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions"/> supplied to the constructor.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions">
            <summary>
            Contains the options used by the CookiesAuthenticationMiddleware
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.#ctor">
            <summary>
            Create an instance of the options initialized with the default values
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.CookieName">
            <summary>
            Determines the cookie name used to persist the identity. The default value is ".AspNet.Cookies".
            This value should be changed if you change the name of the AuthenticationType, especially if your
            system uses the cookie authentication middleware multiple times.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.CookieDomain">
            <summary>
            Determines the domain used to create the cookie. Is not provided by default.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.CookiePath">
            <summary>
            Determines the path used to create the cookie. The default value is "/" for highest browser compatability.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.CookieHttpOnly">
            <summary>
            Determines if the browser should allow the cookie to be accessed by client-side javascript. The
            default is true, which means the cookie will only be passed to http requests and is not made available
            to script on the page.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.CookieSecure">
            <summary>
            Determines if the cookie should only be transmitted on HTTPS request. The default is to limit the cookie
            to HTTPS requests if the page which is doing the SignIn is also HTTPS. If you have an HTTPS sign in page
            and portions of your site are HTTP you may need to change this value.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.ExpireTimeSpan">
            <summary>
            Controls how much time the cookie will remain valid from the point it is created. The expiration
            information is in the protected cookie ticket. Because of that an expired cookie will be ignored 
            even if it is passed to the server after the browser should have purged it 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.SlidingExpiration">
            <summary>
            The SlidingExpiration is set to true to instruct the middleware to re-issue a new cookie with a new
            expiration time any time it processes a request which is more than halfway through the expiration window.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.LoginPath">
            <summary>
            The LoginPath property informs the middleware that it should change an outgoing 401 Unauthorized status
            code into a 302 redirection onto the given login path. The current url which generated the 401 is added
            to the LoginPath as a query string parameter named by the ReturnUrlParameter. Once a request to the
            LoginPath grants a new SignIn identity, the ReturnUrlParameter value is used to redirect the browser back  
            to the url which caused the original unauthorized status code.
            
            If the LoginPath is null or empty, the middleware will not look for 401 Unauthorized status codes, and it will
            not redirect automatically when a login occurs.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.LogoutPath">
            <summary>
            If the LogoutPath is provided the middleware then a request to that path will redirect based on the ReturnUrlParameter.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.ReturnUrlParameter">
            <summary>
            The ReturnUrlParameter determines the name of the query string parameter which is appended by the middleware
            when a 401 Unauthorized status code is changed to a 302 redirect onto the login path. This is also the query 
            string parameter looked for when a request arrives on the login path or logout path, in order to return to the 
            original url after the action is performed.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.Provider">
            <summary>
            The Provider may be assigned to an instance of an object created by the application at startup time. The middleware
            calls methods on the provider which give the application control at certain points where processing is occuring. 
            If it is not provided a default instance is supplied which does nothing when the methods are called.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.TicketDataFormat">
            <summary>
            The TicketDataFormat is used to protect and unprotect the identity and other properties which are stored in the
            cookie value. If it is not provided a default data handler is created using the data protection service contained
            in the IAppBuilder.Properties. The default data protection service is based on machine key when running on ASP.NET, 
            and on DPAPI when running in a different process.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.SystemClock">
            <summary>
            The SystemClock provides access to the system's current time coordinates. If it is not provided a default instance is
            used which calls DateTimeOffset.UtcNow. This is typically not replaced except for unit testing. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.CookieManager">
             <summary>
             The component used to get cookies from the request or set them on the response.
            
             ChunkingCookieManager will be used by default.
             </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions.SessionStore">
            <summary>
            An optional container in which to store the identity across requests. When used, only a session identifier is sent
            to the client. This can be used to mitigate potential problems with very large identities.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieApplyRedirectContext">
            <summary>
            Context passed when a Challenge, SignIn, or SignOut causes a redirect in the cookie middleware 
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieApplyRedirectContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions,System.String)">
            <summary>
            Creates a new context object.
            </summary>
            <param name="context">The OWIN request context</param>
            <param name="options">The cookie middleware options</param>
            <param name="redirectUri">The initial redirect URI</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieApplyRedirectContext.RedirectUri">
            <summary>
            Gets or Sets the URI used for the redirect operation.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider">
            <summary>
            This default implementation of the ICookieAuthenticationProvider may be used if the
            application only needs to override a few of the interface methods. This may be used as a base class
            or may be instantiated directly.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.ICookieAuthenticationProvider">
            <summary>
            Specifies callback methods which the <see cref="T:Microsoft.Owin.Security.Cookies.CookieAuthenticationMiddleware"></see> invokes to enable developer control over the authentication process. /&gt;
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.ICookieAuthenticationProvider.ValidateIdentity(Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext)">
            <summary>
            Called each time a request identity has been validated by the middleware. By implementing this method the
            application may alter or reject the identity which has arrived with the request.
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.ICookieAuthenticationProvider.ResponseSignIn(Microsoft.Owin.Security.Cookies.CookieResponseSignInContext)">
            <summary>
            Called when an endpoint has provided sign in information before it is converted into a cookie. By
            implementing this method the claims and extra information that go into the ticket may be altered.
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.ICookieAuthenticationProvider.ResponseSignedIn(Microsoft.Owin.Security.Cookies.CookieResponseSignedInContext)">
            <summary>
            Called when an endpoint has provided sign in information after it is converted into a cookie.
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.ICookieAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Cookies.CookieApplyRedirectContext)">
            <summary>
            Called when a Challenge, SignIn, or SignOut causes a redirect in the cookie middleware
            </summary>
            <param name="context">Contains information about the event</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.ICookieAuthenticationProvider.ResponseSignOut(Microsoft.Owin.Security.Cookies.CookieResponseSignOutContext)">
            <summary>
            Called during the sign-out flow to augment the cookie cleanup process.
            </summary>
            <param name="context">Contains information about the login session as well as information about the authentication cookie.</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.ICookieAuthenticationProvider.Exception(Microsoft.Owin.Security.Cookies.CookieExceptionContext)">
            <summary>
            Called when an exception occurs during request or response processing.
            </summary>
            <param name="context">Contains information about the exception that occurred</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.#ctor">
            <summary>
            Create a new instance of the default provider.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.ValidateIdentity(Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext)">
            <summary>
            Implements the interface method by invoking the related delegate method
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.ResponseSignIn(Microsoft.Owin.Security.Cookies.CookieResponseSignInContext)">
            <summary>
            Implements the interface method by invoking the related delegate method
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.ResponseSignedIn(Microsoft.Owin.Security.Cookies.CookieResponseSignedInContext)">
            <summary>
            Implements the interface method by invoking the related delegate method
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.ResponseSignOut(Microsoft.Owin.Security.Cookies.CookieResponseSignOutContext)">
            <summary>
            Implements the interface method by invoking the related delegate method
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Cookies.CookieApplyRedirectContext)">
            <summary>
            Implements the interface method by invoking the related delegate method
            </summary>
            <param name="context">Contains information about the event</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.Exception(Microsoft.Owin.Security.Cookies.CookieExceptionContext)">
            <summary>
            Implements the interface method by invoking the related delegate method
            </summary>
            <param name="context">Contains information about the event</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.OnValidateIdentity">
            <summary>
            A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.OnResponseSignIn">
            <summary>
            A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.OnResponseSignedIn">
            <summary>
            A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.OnResponseSignOut">
            <summary>
            A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.OnApplyRedirect">
            <summary>
            A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieAuthenticationProvider.OnException">
            <summary>
            A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieExceptionContext">
            <summary>
            Context object passed to the ICookieAuthenticationProvider method Exception.
            </summary>    
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieExceptionContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions,Microsoft.Owin.Security.Cookies.CookieExceptionContext.ExceptionLocation,System.Exception,Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            Creates a new instance of the context object.
            </summary>
            <param name="context">The OWIN request context</param>
            <param name="options">The middleware options</param>
            <param name="location">The location of the exception</param>
            <param name="exception">The exception thrown.</param>
            <param name="ticket">The current ticket, if any.</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieExceptionContext.Location">
            <summary>
            The code path the exception occurred in.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieExceptionContext.Exception">
            <summary>
            The exception thrown.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieExceptionContext.Rethrow">
            <summary>
            True if the exception should be re-thrown (default), false if it should be suppressed. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieExceptionContext.Ticket">
            <summary>
            The current authentication ticket, if any.
            In the AuthenticateAsync code path, if the given exception is not re-thrown then this ticket
            will be returned to the application. The ticket may be replaced if needed.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieExceptionContext.ExceptionLocation">
            <summary>
            The code paths where exceptions may be reported.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieExceptionContext.ExceptionLocation.AuthenticateAsync">
            <summary>
            The exception was reported in the AuthenticateAsync code path.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieExceptionContext.ExceptionLocation.ApplyResponseGrant">
            <summary>
            The exception was reported in the ApplyResponseGrant code path, during sign-in, sign-out, or refresh.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.Cookies.CookieExceptionContext.ExceptionLocation.ApplyResponseChallenge">
            <summary>
            The exception was reported in the ApplyResponseChallenge code path, during redirect generation.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieResponseSignedInContext">
            <summary>
            Context object passed to the ICookieAuthenticationProvider method ResponseSignedIn.
            </summary>    
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieResponseSignedInContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions,System.String,System.Security.Claims.ClaimsIdentity,Microsoft.Owin.Security.AuthenticationProperties)">
            <summary>
            Creates a new instance of the context object.
            </summary>
            <param name="context">The OWIN request context</param>
            <param name="options">The middleware options</param>
            <param name="authenticationType">Initializes AuthenticationType property</param>
            <param name="identity">Initializes Identity property</param>
            <param name="properties">Initializes Properties property</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignedInContext.AuthenticationType">
            <summary>
            The name of the AuthenticationType creating a cookie
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignedInContext.Identity">
            <summary>
            Contains the claims that were converted into the outgoing cookie.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignedInContext.Properties">
            <summary>
            Contains the extra data that was contained in the outgoing cookie.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieResponseSignInContext">
            <summary>
            Context object passed to the ICookieAuthenticationProvider method ResponseSignIn.
            </summary>    
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieResponseSignInContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions,System.String,System.Security.Claims.ClaimsIdentity,Microsoft.Owin.Security.AuthenticationProperties,Microsoft.Owin.CookieOptions)">
            <summary>
            Creates a new instance of the context object.
            </summary>
            <param name="context">The OWIN request context</param>
            <param name="options">The middleware options</param>
            <param name="authenticationType">Initializes AuthenticationType property</param>
            <param name="identity">Initializes Identity property</param>
            <param name="properties">Initializes Extra property</param>
            <param name="cookieOptions">Initializes options for the authentication cookie.</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignInContext.AuthenticationType">
            <summary>
            The name of the AuthenticationType creating a cookie
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignInContext.Identity">
            <summary>
            Contains the claims about to be converted into the outgoing cookie.
            May be replaced or altered during the ResponseSignIn call.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignInContext.Properties">
            <summary>
            Contains the extra data about to be contained in the outgoing cookie.
            May be replaced or altered during the ResponseSignIn call.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignInContext.CookieOptions">
            <summary>
            The options for creating the outgoing cookie.
            May be replace or altered during the ResponseSignIn call.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieResponseSignOutContext">
            <summary>
            Context object passed to the ICookieAuthenticationProvider method ResponseSignOut    
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieResponseSignOutContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions,Microsoft.Owin.CookieOptions)">
            <summary>
            
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="cookieOptions"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieResponseSignOutContext.CookieOptions">
            <summary>
            The options for creating the outgoing cookie.
            May be replace or altered during the ResponseSignOut call.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext">
            <summary>
            Context object passed to the ICookieAuthenticationProvider method ValidateIdentity.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.AuthenticationTicket,Microsoft.Owin.Security.Cookies.CookieAuthenticationOptions)">
            <summary>
            Creates a new instance of the context object.
            </summary>
            <param name="context"></param>
            <param name="ticket">Contains the initial values for identity and extra data</param>
            <param name="options"></param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext.ReplaceIdentity(System.Security.Principal.IIdentity)">
            <summary>
            Called to replace the claims identity. The supplied identity will replace the value of the 
            Identity property, which determines the identity of the authenticated request.
            </summary>
            <param name="identity">The identity used as the replacement</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext.RejectIdentity">
            <summary>
            Called to reject the incoming identity. This may be done if the application has determined the
            account is no longer active, and the request should be treated as if it was anonymous.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext.Identity">
            <summary>
            Contains the claims identity arriving with the request. May be altered to change the 
            details of the authenticated user.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Cookies.CookieValidateIdentityContext.Properties">
            <summary>
            Contains the extra meta-data arriving with the request ticket. May be altered.
            </summary>
        </member>
    </members>
</doc>
