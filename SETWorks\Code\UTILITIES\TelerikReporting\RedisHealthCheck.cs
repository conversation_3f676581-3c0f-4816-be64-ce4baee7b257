using System;
using System.Configuration;
using System.Linq;
using StackExchange.Redis;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Simple diagnostic tool to check Redis health and determine if separate database will help
    /// </summary>
    public static class RedisHealthCheck
    {
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(RedisHealthCheck));

        /// <summary>
        /// Check current Redis status and provide recommendations
        /// </summary>
        public static RedisHealthReport CheckRedisHealth()
        {
            var report = new RedisHealthReport();
            
            try
            {
                var connectionString = ConfigurationManager.AppSettings["SWRedisConnection"];
                if (string.IsNullOrEmpty(connectionString))
                {
                    report.Status = "ERROR";
                    report.Message = "No Redis connection string found";
                    return report;
                }

                using (var redis = ConnectionMultiplexer.Connect(connectionString))
                {
                    var server = redis.GetServer(redis.GetEndPoints()[0]);
                    var database = redis.GetDatabase(0); // Session database

                    // Get memory info
                    var memoryInfo = server.Info("memory");
                    foreach (var section in memoryInfo)
                    {
                        foreach (var item in section)
                        {
                            long tempBytes = 0;
                            long maxBytes = 0;
                            switch (item.Key.ToLower())
                            {
                                case "used_memory":
                                    if (long.TryParse(item.Value, out tempBytes))
                                    {
                                        report.UsedMemoryBytes = tempBytes;
                                    }
                                    else
                                    {
                                        report.UsedMemoryBytes = 0;
                                    }
                                    break;
                                case "used_memory_human":
                                    report.UsedMemoryHuman = item.Value;
                                    break;
                                case "maxmemory":
                                    if (long.TryParse(item.Value, out maxBytes))
                                    {
                                        report.MaxMemoryBytes = maxBytes;
                                    }
                                    else
                                    {
                                        report.MaxMemoryBytes = 0; 
                                    }
                                    break;
                                case "maxmemory_human":
                                    report.MaxMemoryHuman = item.Value;
                                    break;
                            }
                        }
                    }

                    // Calculate memory usage percentage
                    if (report.MaxMemoryBytes > 0)
                    {
                        report.MemoryUsagePercentage = (double)report.UsedMemoryBytes / report.MaxMemoryBytes * 100;
                    }

                    // Count keys in session database (database 0)
                    report.SessionKeyCount = server.Keys(database: 0, pattern: "*").Count();
                    
                    // Check if database 1 has any keys (report database)
                    report.ReportKeyCount = server.Keys(database: 1, pattern: "*").Count();

                    // Get largest keys in session database
                    var sessionKeys = server.Keys(database: 0, pattern: "*").Take(100).ToList();
                    var largeSessions = sessionKeys
                        .Select(key => new { Key = key.ToString(), Size = database.StringLength(key) })
                        .Where(x => x.Size > 1024 * 100) // Keys larger than 100KB
                        .OrderByDescending(x => x.Size)
                        .Take(10)
                        .ToList();

                    report.LargeSessionKeys = largeSessions.Select(x => $"{x.Key}: {x.Size / 1024}KB").ToArray();

                    // Determine status and recommendation
                    if (report.MemoryUsagePercentage > 90)
                    {
                        report.Status = "CRITICAL";
                        report.Message = "Redis memory usage is critical. Separate database won't help - need separate Redis instance.";
                        report.Recommendation = "SEPARATE_REDIS_INSTANCE";
                    }
                    else if (report.MemoryUsagePercentage > 70)
                    {
                        report.Status = "WARNING";
                        report.Message = "Redis memory usage is high. Separate database may provide limited benefit.";
                        report.Recommendation = "CONSIDER_SEPARATE_REDIS_INSTANCE";
                    }
                    else if (report.LargeSessionKeys.Length > 0)
                    {
                        report.Status = "OK";
                        report.Message = "Redis has capacity but large session keys detected. Separate database should help.";
                        report.Recommendation = "SEPARATE_DATABASE_OK";
                    }
                    else
                    {
                        report.Status = "GOOD";
                        report.Message = "Redis appears healthy. Separate database should work well.";
                        report.Recommendation = "SEPARATE_DATABASE_OK";
                    }
                }
            }
            catch (Exception ex)
            {
                report.Status = "ERROR";
                report.Message = $"Error checking Redis health: {ex.Message}";
                Log.Error("Error checking Redis health", ex);
            }

            return report;
        }

        /// <summary>
        /// Log the health report
        /// </summary>
        public static void LogHealthReport()
        {
            var report = CheckRedisHealth();
            
            Log.Info($"Redis Health Check Results:");
            Log.Info($"  Status: {report.Status}");
            Log.Info($"  Memory Usage: {report.UsedMemoryHuman} / {report.MaxMemoryHuman} ({report.MemoryUsagePercentage:F1}%)");
            Log.Info($"  Session Keys: {report.SessionKeyCount}");
            Log.Info($"  Report Keys: {report.ReportKeyCount}");
            Log.Info($"  Message: {report.Message}");
            Log.Info($"  Recommendation: {report.Recommendation}");
            
            if (report.LargeSessionKeys?.Length > 0)
            {
                Log.Info($"  Large Session Keys:");
                foreach (var key in report.LargeSessionKeys)
                {
                    Log.Info($"    {key}");
                }
            }
        }
    }

    public class RedisHealthReport
    {
        public string Status { get; set; } = "UNKNOWN";
        public string Message { get; set; } = "";
        public string Recommendation { get; set; } = "";
        
        public long UsedMemoryBytes { get; set; }
        public string UsedMemoryHuman { get; set; } = "Unknown";
        public long MaxMemoryBytes { get; set; }
        public string MaxMemoryHuman { get; set; } = "Unknown";
        public double MemoryUsagePercentage { get; set; }
        
        public int SessionKeyCount { get; set; }
        public int ReportKeyCount { get; set; }
        public string[] LargeSessionKeys { get; set; } = new string[0];
    }
}
