<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.ReportViewer.WebForms</name>
    </assembly>
    <members>
        <member name="T:Telerik.ReportViewer.WebForms.ReportPageOperation">
            <summary>
            Returns the html page corresponding to the page number. 
            If the report has not been rendered first request renders it.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ReportDataOperation">
            <summary>
            Summary description for ReportDataOperation.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.AjaxHelper.IsUpdatedByAjaxManager(System.Web.UI.Control)">
            <summary>
            Checks whether the control is being updated by the Telerik AjaxManager.
            </summary>        
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.AjaxHelper.IsInPartialRendering(System.Web.UI.Control)">
            <summary>
            Checks whether an update panel is being partially rendered.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.AjaxHelper.IsRenderedByUpdatePanel(System.Web.UI.Control)">
            <summary>
            Checks whether the control is being partially rendered in an UpdatePanel.
            </summary>        
        </member>
        <!-- Badly formed XML comment ignored for member "P:Telerik.ReportViewer.WebForms.AjaxHelper.IsAspNetAjaxRequest" -->
        <member name="T:Telerik.ReportViewer.WebForms.ParameterValueEditor">
            <summary>
            Base class for value editor controls.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ToolbarGroup">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ExportGroup">
            <summary>
            Summary description for ExportToolbarGroup.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ExportFormatButton">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ToolbarButton">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.HandlerOperationTypes">
            <summary>
            Summary description for HandlerOperationTypes.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.HistoryGroup">
            <summary>
            Summary description for RefreshGroup.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ImageStream">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.MultiCheckboxValueEditor">
            <summary>
            Drop down control with checkboxes.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.MultiValueAvailableValuesParamEditor">
            <summary>
            Parameter editor for parameters with multi values
            which have a set of predefined values to select from.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ReportParameterEditor">
            <summary>
            Base control for the report parameter editors. 
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.MultiValueOnlyParamEditor">
            <summary>
            Parameter editor for parameters which have multi values
            but do not have a set of predefined values to select from.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.PageNavigationGroup">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ParameterModel">
            <summary>
            Represents the report parameter model to associate the parameter editor UI with.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ObservableObject">
            <summary>
            Represents an observable object.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.Common.ObservableObject.OnPropertyChanged(System.String)">
            <summary>
            Raises the PropertyChanged event.
            </summary>
            <param name="propertyName"></param>
        </member>
        <member name="E:Telerik.ReportViewer.Common.ObservableObject.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.Common.ParameterModel.Validate(Telerik.Reporting.Processing.Parameter)">
            <summary>
            Returns localized error message if validation fails. Otherwise returns null
            </summary>
            <param name="parameter"></param>
            <param name="textResources"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.AvailableValues">
            <summary>
            Gets a list of the available <see cref="T:Telerik.ReportViewer.Common.ParameterValueModel"/>s.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.SafeValue">
            <summary>
            Prevent user input to be considered as expression
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.ID">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.ID"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.Name">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.Name"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.TypeName">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.Type"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.Type">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.Type"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.Text">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.Text"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.AllowNull">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.AllowNull"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.AllowBlank">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.AllowNull"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.IsNull">
            <summary>
            Specifies whether the 'null' value is selected.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.Multivalue">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.Multivalue"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.AutoRefresh">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.Parameter.AutoRefresh"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterModel.SelectedSingleValue">
            <summary>
            Gets or sets a single selected value.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ParameterValueModel">
            <summary>
            Represents the parameter value model.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.IParameterValueModel.IsSelected">
            <summary>
            Specifies whether the parameter value is selected.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.IParameterValueModel.Name">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.ParameterValue.Name"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.IParameterValueModel.Value">
            <summary>
            Gets <see cref="P:Telerik.Reporting.Processing.ParameterValue.Value"/>
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterValueModel.IsSelected">
            <summary>
            Specifies whether the parameter value is selected.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterValueModel.Name">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.ParameterValue.Name"/>.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterValueModel.Value">
            <summary>
            Gets <see cref="P:Telerik.Reporting.Processing.ParameterValue.Value"/>
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ParameterValueModel.Owner">
            <summary>
            Gets the owner parameter model.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ParameterValueModel.Name">
            <summary>
            Gets the <see cref="P:Telerik.Reporting.Processing.ParameterValue.Name"/>.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.ParametersPage.CreateParameterModels(System.Collections.Generic.IList{Telerik.Reporting.Processing.Parameter})">
            <summary>
            Gets or sets a list of report parameters.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ParametersPage.ParameterModels">
            <summary>
            Gets or sets a list of parameter models.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.PrintGroup">
            <summary>
            Summary description for PrintGroup.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.RefreshGroup">
            <summary>
            Summary description for RefreshGroup.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportBookControl.Reports">
            <summary>
            Gets the collection of reports that are parts of this ReportBook.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportInfo.Report">
            <summary>
            Specifies the assembly qualified name of the report.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.ReportViewer.RefreshReport">
            <summary>
            Causes the viewer to clear the cache
            and render the report again.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.ReportViewer.RefreshData">
            <summary>
            Refreshes the current report in the viewer. Last used parameter values are used.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.ReportViewer.ForceCreateSession">
            <summary>
            Creates the Session object to be available 
            in case when there is an ASP.NET AJAX request.
            Otherwise server side exception is raised and a 
            JavaScript alert appears.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.ReportViewer.CheckHttpHandlerRegistrations">
            <summary>
            Checks whether the ReportViewer's http handler is registered in the user application's web.config.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowHistoryButtons">
            <summary>
            Indicates whether the page navigation controls are visible.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowNavigationGroup">
            <summary>
            Indicates whether the page navigation controls are visible.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ToolbarVisible">
            <summary>
            Indicates whether the toolbar should be visible on the viewer.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ParametersAreaVisible">
            <summary>
            Indicates whether the ParametersArea should be visible.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.DocumentMapVisible">
            <summary>
            Indicates whether the DocumentMap should be visible.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowParametersButton">
            <summary>
            Indicates whether <strong>Parameters</strong> button should be visible. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowZoomSelect">
            <summary>
            Indicates whether the <strong>Zoom</strong> input box should be visible.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowDocumentMapButton">
            <summary>
            Indicates whether <strong>DocumentMap</strong> button should be visible. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowExportGroup">
            <summary>
            Indicates whether <strong>Export</strong> group should be visible. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowPrintPreviewButton">
            <summary>
            Indicates whether <strong>PrintPreview</strong> button should be visible. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowPrintButton">
            <summary>
            Indicates whether <strong>Print</strong> button should be visible. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ShowRefreshButton">
            <summary>
            Indicates whether the <strong>Refresh</strong> button should be visible.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ViewMode">
            <summary>
            Specifies whether the viewer is in interactive or print preview mode.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ZoomMode">
            <summary>
            Gets or sets the zoom mode of the viewer.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ZoomPercent">
            <summary>
            Gets or sets the percentage of zoom used for the report display.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.Skin">
            <summary>
            Gets or sets the name of the skin that is used by the viewer.
            </summary>
            <value>
            A string containing the name of the currently used skin. The default value is
            <strong>Default</strong>.
            </value>        
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.SkinsPath">
            <summary>
            Gets or sets the relative path of the folder containing the skins.
            </summary>
            <value>
            A string specifying the relative path to the folder which contains
            ReportViewer skins. The default value is "~/ReportViewer/Skins".
            </value>        
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.Resources">
            <summary>
            Use to customize the texts for labels, tooltips and messages.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.Report">
            <summary>
            Gets or sets the report displayed in the viewer.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.ReportViewer.ReportSource">
            <summary>
            Gets or sets the <see cref="T:Telerik.Reporting.ReportSource"/> for the report displayed in the viewer.
            </summary>
            <seealso cref="T:Telerik.Reporting.ReportSource"/>
            <seealso cref="T:Telerik.Reporting.UriReportSource"/>
            <seealso cref="T:Telerik.Reporting.TypeReportSource"/>
            <seealso cref="T:Telerik.Reporting.InstanceReportSource"/>
            <seealso cref="T:Telerik.Reporting.XmlReportSource"/>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ReportViewerFrame">
            <summary>
            Summary description for ReportViewerFrame.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ReportViewerHandlerNotRegisteredException">
            <summary>
            Summary description for ReportViewerHandlerNotRegisteredException.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ReportViewerHandlerNotRegisteredIIS7Exception">
            <summary>
            Summary description for ReportViewerHandlerNotRegisteredException for II7.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.Resources.ToScriptObject">
            <summary>
            Creates the definition for the JavaScript object.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.CurrentPageToolTip">
            <summary>
            Specifies the tooltip for the current page label.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ExportButtonText">
            <summary>
            Specifies the text for the Export button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ExportToolTip">
            <summary>
            Specifies the tooltip for the Export button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ExportSelectFormatText">
            <summary>
            Specifies the &lt;select export format&gt; text in the Export select input.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.FirstPageToolTip">
            <summary>
            Specifies the tooltip for the FirstPage button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.LabelOf">
            <summary>
            Specifies the text for the 'of' conjunction label.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.LastPageToolTip">
            <summary>
            Specifies the tooltip for the LastPage button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ProcessingReportMessage">
            <summary>
            Specifies the text for the wait message shown while the report is being processed.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.NoPageToDisplay">
            <summary>
            Specifies the text which appears if there are no pages to display.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.NextPageToolTip">
            <summary>
            Specifies the tooltip for the NextPage button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ParametersToolTip">
            <summary>
            Specifies the tooltips for the toggle parameters area button. 
            The two texts are separated by '|'.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.DocumentMapToolTip">
            <summary>
            Specifies the tooltips for the toggle parameters area button. 
            The two texts are separated by '|'.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.PreviousPageToolTip">
            <summary>
            Specifies the tooltip for the PreviousPage button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.TogglePageLayoutToolTip">
            <summary>
            Specifies the tooltip for the TogglePageLayout button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.SessionHasExpiredError">
            <summary>
            Specifies the error to be displayed when session has expired.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.SessionHasExpiredMessage">
            <summary>
            Specifies the message to be displayed when session has expired.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.PrintToolTip">
            <summary>
            Specifies the tooltip for the Print button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.RefreshToolTip">
            <summary>
            Specifies the tooltip for the Refresh button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.NavigateBackToolTip">
            <summary>
            Specifies the tooltip for the NavigateBack button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.NavigateForwardToolTip">
            <summary>
            Specifies the tooltip for the NavigateForward button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersSelectAllText">
            <summary>
            Specifies the &lt;select all&gt; text for a multivalue parameter editor.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersSelectAValueText">
            <summary>
            Specifies the &lt;select a value&gt; text for a dropdown parameter editor.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersInvalidValueText">
            <summary>
            Specifies the text for the InvalidValue error.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersNoValueText">
            <summary>
            Specifies the message when a value should be selected for a parameter.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersNullText">
            <summary>
            Specifies the text for the NULL checkbox.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersPreviewButtonText">
            <summary>
            Specifies the text for the Preview button.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersFalseValueLabel">
            <summary>
            Specifies the text for the False value label in a boolean parameter editor.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersInputDataError">
            <summary>
            Specifies the message which appears when not all the parameters are set.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ReportParametersTrueValueLabel">
            <summary>
            Specifies the text for the True value label in a boolean parameter editor.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.MissingReportSource">
            <summary>
            Specifies the text for the error message when report source is null.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ZoomToPageWidth">
            <summary>
            Specifies the name for the PageWidth zoom mode.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.WebForms.Resources.ZoomToWholePage">
            <summary>
            Specifies the name for the WholePage zoom mode.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.SingleValueAvailableValuesParamEditor">
            <summary>
            Parameter editor for parameters with a single value
            which have a set of predefined values to slect from.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.SingleValueOnlyParamEditor">
            <summary>
            Parameter editor for parameters with a single value
            which do not have a set of predefined values to select from.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.SR">
            <summary>
            A class for handling string resources of the control.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.SRCategoryAttribute">
            <summary>
            Summary description for SRCategoryAttribute.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.SRErrors">
            <summary>
            Summary description for SRErrors.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.StreamManager">
            <summary>
            Manages the streams of the HTML report - the main HTML stream, the image streams, other streams if available.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.WebForms.StreamManager.StoreBookmarkInfo(System.Collections.Generic.IList{Telerik.Reporting.Processing.BookmarkNode})">
            <summary>
            Stores bookmark info per page.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.TextButton">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ToolbarImageButton">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ToolbarTextBox">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.Utils">
            <summary>
            Summary description for Utils.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Telerik.ReportViewer.WebForms.UrlQueryBuilder" -->
        <member name="T:Telerik.ReportViewer.WebForms.ViewMode">
            <summary>
            Specifies the page view mode for the ReportViewer control.
            </summary>
        </member>
        <member name="F:Telerik.ReportViewer.WebForms.ViewMode.Interactive">
            <summary>
            Enables <strong>Interactive</strong> page view.
            </summary>
        </member>
        <member name="F:Telerik.ReportViewer.WebForms.ViewMode.PrintPreview">
            <summary>
            Enables <strong>PrintPreview</strong> page view.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.WaitControl">
            <summary>
            Summary description for WaitControl.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.WebForms.ZoomButton">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.Accessibility.ShortcutKeys">
            <summary>
            Keys for navigating on report viewer main areas. Used in conjunction with CTRL+ALT to avoid duplicating default shortcuts behavior.
            </summary>
        </member>
        <member name="F:Telerik.ReportViewer.Common.Accessibility.ShortcutKeys.CONFIRM_KEY">
            <summary>
            Key for confirming executing the action on the currently focused report item.
            </summary>
        </member>
        <member name="F:Telerik.ReportViewer.Common.Accessibility.ShortcutKeys.CONTENT_AREA_KEY">
            <summary>
            Key for navigating to the report contents area. Used with Ctrl+Alt.
            </summary>
        </member>
        <member name="F:Telerik.ReportViewer.Common.Accessibility.ShortcutKeys.DOCUMENT_MAP_AREA_KEY">
            <summary>
            Key for navigating to the document map area. Used with Ctrl+Alt.
            </summary>
        </member>
        <member name="F:Telerik.ReportViewer.Common.Accessibility.ShortcutKeys.MENU_AREA_KEY">
            <summary>
            Key for navigating to the menu (toolbar) area. Used with Ctrl+Alt.
            </summary>
        </member>
        <member name="F:Telerik.ReportViewer.Common.Accessibility.ShortcutKeys.PARAMETERS_AREA_KEY">
            <summary>
            Key for navigating to the parameters area. Used with Ctrl+Alt.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.AccessibilityChangedEventHandler">
            <summary>
            Represents the method that will handle the AccessibilityChanged event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.InteractiveActionCancelEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.AccessibilityChangedEventArgs">
            <summary>
            Provides data for the accessibility feature events.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.AccessibilityChangedEventArgs.Enabled">
            <summary>
            Determines the new state of the accessibility features
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ExportBeginEventHandler">
            <summary>
            Represents the method that will handle the ExportBegin event of both WinForms and WPF report viewers.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.ExportBeginEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ExportBeginEventArgs">
            <summary>
            Provides data for the ExportBegin event of both WinForms and WPF report viewers. 
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.RenderingBeginEventArgs">
            <summary>
            Provides data for the RenderingBegin event of both WinForms and WPF report viewers. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.RenderingBeginEventArgs.DeviceInfo">
            <summary>
            Gets the device info that will be used for the beginning export operation.
            </summary>
            <returns>The device info collection. 
            Add your device information settings to the collection.</returns>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ExportBeginEventArgs.Format">
            <summary>
            Gets the export format that will be used for the beginning export operation.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ExportEndEventHandler">
            <summary>
            Represents the method that will handle the ExportEnd event of both WinForms and WPF report viewers.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.ExportEndEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ExportEndEventArgs">
            <summary>
            Provides data for the ExportEnd event of both WinForms and WPF report viewers. 
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.RenderingEndEventArgs">
            <summary>
            Provides data for the RenderingEnd event of both WinForms and WPF report viewers. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.RenderingEndEventArgs.Exception">
            <summary>
            Gets an exception if such has occurred during the report processing.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ExportEndEventArgs.Handled">
            <summary>
            Gets or sets a value indicating whether the event was handled.
            If the event is handled the viewers' open SaveFileDialog operation will be suppressed.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ExportEndEventArgs.DocumentName">
            <summary>
            Gets or sets the rendered report document name.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ExportEndEventArgs.DocumentExtension">
            <summary>
            Gets the rendered report extension.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ExportEndEventArgs.DocumentBytes">
            <summary>
            Gets or sets a byte array that contains the rendered report.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.PrintBeginEventHandler">
            <summary>
            Represents the method that will handle the PrintBegin event of both WinForms and WPF report viewers.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.PrintBeginEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.PrintBeginEventArgs">
            <summary>
            Provides data for the PrintBegin event of both WinForms and WPF report viewers. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.PrintBeginEventArgs.PrinterSettings">
            <summary>
            Gets or sets PrinterSettings.
            If a valid printer is specified the print dialog will not be shown.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.PrintBeginEventArgs.PrintController">
            <summary>
            Gets or sets PrinterController. 
            The specified print controller will be used in the PrintDocument.
            The PrintController controls how a document is printed.
            If PrintController is not specified 
            the default print controller is PrintControllerWithStatusDialog.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.PrintEndEventHandler">
            <summary>
            Represents the method that will handle the PrintEnd event of both WinForms and WPF report viewers.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.PrintEndEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.PrintEndEventArgs">
            <summary>
            Provides data for the PrintEnd event of both WinForms and WPF report viewers. 
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.PrintEndEventArgs.Exception">
            <summary>
            Gets an exception that has occurred during the report processing.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.RenderingBeginEventHandler">
            <summary>
            Represents the method that will handle the RenderingBegin event of both WinForms and WPF report viewers.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.RenderingBeginEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.RenderingEndEventHandler">
            <summary>
            Represents the method that will handle the RenderingEnd event of both WinForms and WPF report viewers.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.RenderingEndEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="M:Telerik.ReportViewer.Common.IRenderingController.GetParameters(Telerik.Reporting.ParameterCollection)">
            <summary>
            
            </summary>
            <param name="values">The values collected from the parameters controls.
            Null must be passed for the initial parameter resolution.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.ReportViewer.Common.RenderingControllerBase.StartTask(Telerik.ReportViewer.Common.RenderingThreadArgs)">
            <summary>
            Cancels the current rendering task and starts a new one.
            </summary>
            <param name="args"></param>
        </member>
        <member name="P:Telerik.ReportViewer.Common.RenderingControllerBase.RenderingThreadArgs">
            <summary>
            Internal for test purposes
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.Common.ReportEngineRenderingController.GetReportEngine(Telerik.ReportViewer.Common.ReportViewerEngineServerContext)">
            <summary>
            Internal for test purposes
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.Common.Search.SearchDialogControllerBase.ViewerStateChanged(System.Object,System.EventArgs)">
            <summary>
            When the viewer state is changed (refreshed or ViewMode/ReportSource changed), the dialog must be closed so when opened, it will be reinitialized with the new search data
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.ReportViewer.Common.Search.SearchViewModel.StartSearchOverride">
            <summary>
            This method is called from a timer thread and will not be executed in UI thread!
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.InteractiveActionExecutingEventHandler">
            <summary>
            Represents the method that will handle the InteractiveActionExecuting event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.InteractiveActionCancelEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.InteractiveActionEnterEventHandler">
            <summary>
            Represents the method that will handle the InteractiveActionEnter event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.InteractiveActionEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.InteractiveActionLeaveEventHandler">
            <summary>
            Represents the method that will handle the InteractiveActionLeave event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.InteractiveActionEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.InteractiveActionEventArgs">
            <summary>
            Provides data for interactive actions related events on the <see cref="!:Telerik.ReportViewer.WinForms.ReportViewer"/>
            and on the <see cref="!:Telerik.ReportViewer.Wpf.ReportViewer"/> controls.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.InteractiveActionEventArgs.Action">
            <summary>
            Gets the action instance
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.InteractiveActionEventArgs.CursorPos">
            <summary>
            Gets the relative coordinates in pixels of the mouse cursor according to the report item that triggered the action.
            Returns an empty Point instance when used in InteractiveActionLeave event handler.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.InteractiveActionEventArgs.Bounds">
            <summary>
            Gets the client bounds in pixels of the element that triggered the action
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.InteractiveActionCancelEventArgs">
            <summary>
            /// Provides data for interactive actions related event on the <see cref="!:Telerik.ReportViewer.WinForms.ReportViewer"/>
            and on the <see cref="!:Telerik.ReportViewer.Wpf.ReportViewer"/> controls.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.InteractiveActionCancelEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the event should be canceled.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.FileOutputProcessor">
            <summary>
            Used when exporting/rendering to a non-interactive format
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.MissingReportSourceException">
            <summary>
            
            </summary>	
        </member>
        <member name="T:Telerik.ReportViewer.Common.ParameterModelCollection`1">
            <summary>
            Represents a collection of parameter models.
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.RenderingExtension">
            <summary>
            Represents a rendering extension
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.RenderingExtension.Name">
            <summary>
            Gets the name of the <b>RenderingExtension</b>
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.RenderingExtension.Description">
            <summary>
            Gets the description of the <b>RenderingExtension</b>
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.RenderingExtension.Visible">
            <summary>
            Gets the visibility of the <b>RenderingExtension</b>
            </summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ReportEngineConnectionInfoBase">
            <summary>
            Base class for providing report engine connection string information
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ReportEngineConnectionInfoBase.ConnectionString">
            <summary>
            The connection string of the current ReportingEngineConnection instance.
            </summary>        
        </member>
        <member name="T:Telerik.ReportViewer.Common.EmbeddedConnectionInfo">
            <summary>
            Class for creating an embedded report engine connection string
            </summary>
            <example>
            <code lang="C#" source="CodeSnippets\CS\API\Telerik\ReportViewer\WinForms\Form1.cs" region="WinFormsEmbeddedReportEngineConnectionSnippet" />
            <code lang="VB" source="CodeSnippets\VB\API\Telerik\ReportViewer\WinForms\Form1.vb" region="WinFormsEmbeddedReportEngineConnectionSnippet" />
            </example>
        </member>
        <member name="T:Telerik.ReportViewer.Common.RestServiceConnectionInfo">
            <summary>
            Class for creating a connection string that will be used with a REST service instance
            </summary>
            <example>
            <code lang="C#" source="CodeSnippets\CS\API\Telerik\ReportViewer\WinForms\Form1.cs" region="WinFormsRestServiceReportEngineConnectionSnippet" />
            <code lang="VB" source="CodeSnippets\VB\API\Telerik\ReportViewer\WinForms\Form1.vb" region="WinFormsRestServiceReportEngineConnectionSnippet" />
            </example>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ReportServerConnectionInfo">
            <summary>
            Class for creating a connection string that will be used with a Report Server instance
            </summary>
            <example>
            <code lang="C#" source="CodeSnippets\CS\API\Telerik\ReportViewer\WinForms\Form1.cs" region="WinFormsReportServerReportEngineConnectionSnippet" />
            <code lang="VB" source="CodeSnippets\VB\API\Telerik\ReportViewer\WinForms\Form1.vb" region="WinFormsReportServerReportEngineConnectionSnippet" />
            </example>    
        </member>
        <member name="P:Telerik.ReportViewer.Common.ReportingEngineConnectionStringBuilder.Engine">
            <summary>
            Mandatory Engine token. Will be included in the connection string even when empty.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ReportingEngineConnectionStringBuilder.Uri">
            <summary>
            A Uri token. Will be included in the connection string even when empty.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ReportingEngineConnectionStringBuilder.Token">
            <summary>
            A security token. If empty, won't be included in the connection string.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ReportingEngineConnectionStringBuilder.UseDefaultCredentials">
            <summary>
            Determines if the default credentials will be send with the request
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ReportingEngineConnectionStringBuilder.Username">
            <summary>
            A username token. If empty, won't be included in the connection string.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ReportingEngineConnectionStringBuilder.Password">
            <summary>
            A password token. If empty, won't be included in the connection string.
            </summary>
        </member>
        <member name="M:Telerik.ReportViewer.Common.ReportViewerUtils.ClearReportSourceFromHistory(Telerik.Reporting.ReportSource,System.String,Telerik.ReportViewer.Common.ServerReportViewerHistory)">
            <summary>
            Removes from history all the remote report sources that have the same connection string as the current(expired) one 
            </summary>
            <param name="expiredReportSource"></param>
            <param name="currentConnString"></param>
        </member>
        <member name="M:Telerik.ReportViewer.Common.ReportViewerUtils.AreConnectionsEqual(System.String,System.String)">
            <summary>
            If the new and the old connection have different connection strings, clear the session id.
            </summary>
            <param name="newConnectionString"></param>
        </member>
        <member name="F:Telerik.ReportViewer.Common.StaTaskScheduler.tasks">
            <summary>Stores the queued tasks to be executed by our pool of STA threads.</summary>
        </member>
        <member name="F:Telerik.ReportViewer.Common.StaTaskScheduler.threads">
            <summary>The STA threads used by the scheduler.</summary>
        </member>
        <member name="M:Telerik.ReportViewer.Common.StaTaskScheduler.#ctor(System.Int32)">
            <summary>Initializes a new instance of the StaTaskScheduler class with the specified concurrency level.</summary>
            <param name="numberOfThreads">The number of threads that should be created and used by this scheduler.</param>
        </member>
        <member name="M:Telerik.ReportViewer.Common.StaTaskScheduler.QueueTask(System.Threading.Tasks.Task)">
            <summary>Queues a Task to be executed by this scheduler.</summary>
            <param name="task">The task to be executed.</param>
        </member>
        <member name="M:Telerik.ReportViewer.Common.StaTaskScheduler.GetScheduledTasks">
            <summary>Provides a list of the scheduled tasks for the debugger to consume.</summary>
            <returns>An enumerable of all tasks currently scheduled.</returns>
        </member>
        <member name="M:Telerik.ReportViewer.Common.StaTaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
            <summary>Determines whether a Task may be inlined.</summary>
            <param name="task">The task to be executed.</param>
            <param name="taskWasPreviouslyQueued">Whether the task was previously queued.</param>
            <returns>true if the task was successfully inlined; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.ReportViewer.Common.StaTaskScheduler.Dispose">
            <summary>
            Cleans up the scheduler by indicating that no more tasks will be queued.
            This method blocks until all threads successfully shutdown.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.StaTaskScheduler.MaximumConcurrencyLevel">
            <summary>Gets the maximum concurrency level supported by this scheduler.</summary>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ToolTipOpeningEventHandler">
            <summary>
            Represents the method that will handle the ToolTipOpening event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="args">An <see cref="T:Telerik.ReportViewer.Common.ToolTipOpeningEventArgs"/> object that contains the event data.</param>
        </member>
        <member name="T:Telerik.ReportViewer.Common.ToolTipOpeningEventArgs">
            <summary>
            Provides data for tooltip related event raised from desktop viewers.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ToolTipOpeningEventArgs.ToolTip">
            <summary>
            Gets the toolTip instance
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ToolTipOpeningEventArgs.CursorPos">
            <summary>
            Gets the relative coordinates in pixels of the mouse cursor according to the report item that shows the tooltip.
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ToolTipOpeningEventArgs.Bounds">
            <summary>
            Gets the client bounds in pixels of the element that shows the tooltip
            </summary>
        </member>
        <member name="P:Telerik.ReportViewer.Common.ToolTipOpeningEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the event should be canceled.
            </summary>
        </member>
    </members>
</doc>
