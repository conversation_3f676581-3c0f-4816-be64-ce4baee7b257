<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GleamTech.DocumentUltimate</name>
    </assembly>
    <members>
        <member name="T:GleamTech.DocumentUltimate.AspNet.DocumentCache">
            <summary>
            Provides methods for handling caching of documents.
            </summary>
            <example>
            <para>Pre-caching in the same web application which uses DocumentViewer:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentCacheExamples.cs" region="PreCache1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentCacheExamples.vb" region="PreCache1" language="vb" />
            <para />
            <para>Pre-caching in other applications:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentCacheExamples.cs" region="PreCache2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentCacheExamples.vb" region="PreCache2" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.#ctor(GleamTech.FileSystems.Location,System.Boolean,System.Boolean,GleamTech.DocumentUltimate.DocumentUltimateConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentCache"/> class.
            </summary>
            <param name="location">
            The location to store cached documents. 
            The location can be a plain physical/virtual path string or
            a <see cref="T:GleamTech.FileSystems.Location"/> instance for one of the supported file systems like Amazon S3 and Azure Blob.
            </param>
            <param name="encryptionEnabled">Whether encryption of documents should be enabled. The default is true.</param>
            <param name="keepVariations">Whether to keep variations of document conversion results. The default is false.</param>
            <param name="configuration">The configuration to use instead of the global configuration.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentCache.EncryptionEnabled">
            <summary>
            Gets a value that specifies whether encryption of documents should be enabled.
            The default is true.
            Document viewer downloads a special document format (XPZ) on the client-side but for additional 
            security (DRM), the document is also encypted by default.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentCache.KeepVariations">
            <summary>
            Gets a value that specifies whether to keep variations of document conversion results.
            The default is false.
            For instance when you change watermark options, both Pdf and Xpz outputs are regenerated.
            By default the Pdf and Xpz outputs are replaced in cache when watermarks are changed.
            If you set this property to true, the Pdf and Xpz outputs will not be replaced but all variations will
            be kept in cache instead. This can be useful especially when you want different outputs for different users.
            So this way, for instance you can have different watermarked variations for each different user.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.GetCacheInfo(GleamTech.DocumentUltimate.AspNet.DocumentOptions)">
            <summary>
            Gets the cache info for a document. This method can be used if you need to know the cache keys 
            beforehand without calling <see cref="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.PreCacheDocument(GleamTech.DocumentUltimate.AspNet.DocumentOptions)"/> method.
            </summary>
            <param name="documentOptions">The document options of <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> for pre-caching.</param>
            <returns>A new <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo"/> instance.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.SaveCacheInfo(GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo)">
            <summary>Saves the document cache info.</summary>
            <param name="cacheInfo">The document cache info.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.LoadCacheInfo(GleamTech.Caching.FileCacheKey)">
            <summary>Loads the document cache info that was saved before.</summary>
            <param name="cacheInfoKey">The cache key for the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo"/> object.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo"/> instance.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.PreCacheDocument(GleamTech.DocumentUltimate.AspNet.DocumentOptions)">
            <summary>
            Pre-caches a document. Normally the source document is converted to a special web-friendly format (XPZ) and cached
            after the document viewer is displayed in the page and when the document is viewed for the first time. 
            With this method, you can do the conversion and caching beforehand so this way, when a user opens the 
            document for the first time, there will be no waiting and the document will be loaded immediately from 
            the cache. If the source document is already converted and cached, then this method will do nothing and
            return the existing cached item.
            </summary>
            <param name="documentOptions">The document options of <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> for pre-caching.</param>
            <returns>A <see cref="T:GleamTech.Caching.FileCacheItem"/> instance for the cached XPZ file, this object contains information like cache key.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.GetOrAddPdf(GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo)">
            <summary>
            Gets or adds the PDF cache file. 
            This method handles the first step of the conversion: Source -> PDF.
            Normally you would not need to call this method directly because
            calling only <see cref="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.PreCacheDocument(GleamTech.DocumentUltimate.AspNet.DocumentOptions)"/> method handles the conversion chain Source -> PDF -> XPZ.
            </summary>
            <param name="cacheInfo">The document cache info.</param>
            <returns>A <see cref="T:GleamTech.Caching.FileCacheItem"/> instance for the cached PDF file, this object contains information like cache key.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.GetOrAddXpz(GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo)">
            <summary>
            Gets or adds the XPZ cache file.
            This method handles the second step of the conversion: PDF -> XPZ.
            Normally you would not need to call this method directly because
            calling only <see cref="M:GleamTech.DocumentUltimate.AspNet.DocumentCache.PreCacheDocument(GleamTech.DocumentUltimate.AspNet.DocumentOptions)"/> method handles the conversion chain Source -> PDF -> XPZ.
            </summary>
            <param name="cacheInfo">The document cache info.</param>
            <returns>A <see cref="T:GleamTech.Caching.FileCacheItem"/> instance for the cached XPZ file, this object contains information like cache key.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo">
            <summary>Represents the cache information for a document.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo.CacheSourceKey">
            <summary>
            Gets the cache key for the source. 
            This is the parent key and is unique for a document.
            This key is used in subfolder names in the cache folder to group outputs for the same input.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo.CachePdfKey">
            <summary>
            Gets the cache key for PDF. 
            This key corresponds to the cache item which is the first step of the conversion: Source -> PDF.
            This format is used both as an input to XPZ and as the file for "Download as PDF" button.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo.CacheXpzKey">
            <summary>
            Gets the cache key for XPZ. 
            This key corresponds to the cache item which is the second step of the conversion: PDF -> XPZ.
            This format is the web-friendly format used by the DocumentViewer.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo.CacheInfoKey">
            <summary>
            Gets the cache key for this <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo"/> object. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentCacheInfo.CacheCopyKey">
            <summary>
            Gets the cache key for the copy of the source. 
            This key is used only when the source needs to be serialized. The source stream is copied
            to the cache folder for serialization. This is currently done when you use <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentSource"/>
            so that your source is available out of the context of your page that hosts <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/>.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters">
            <summary>
            Represents a name-value collection of parameters to be used with <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/>.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters"/>.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters.Set(System.String,System.Object)">
            <summary>
            Set the parameter value with the specified parameter name.
            If parameter name already exists, the value is updated.
            </summary>
            <param name="parameterName">Name of the parameter.</param>
            <param name="parameterValue">Value of the parameter.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters.Get``1(System.String)">
            <summary>
            Gets the parameter value for the specified parameter name.
            </summary>
            <param name="parameterName">Name of the parameter.</param>
            <returns>parameter value of type <typeparamref name="T"/> if found, otherwise default value of <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters.Remove(System.String)">
            <summary>
            Removes the parameter value with the specified parameter name.
            </summary>
            <param name="parameterName">Name of the parameter.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters.Clear">
            <summary>
            Clears all the parameters.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.DocumentInfo">
            <summary>Represents the result of <see cref="M:GleamTech.DocumentUltimate.AspNet.IDocumentHandler.GetInfo(System.String,GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters)"/> method.</summary>
            <example>
            <para>Initializing a DocumentInfo instance:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentInfo1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentInfo1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentInfo.#ctor(System.String,System.String,System.Nullable{GleamTech.DocumentUltimate.DocumentFormat})">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentInfo"/> class.</summary>
            <param name="uniqueId">
            The unique identifier that will be used for generating the cache key for this document.
            For instance, it can be an ID from your database table or a simple file name; 
            you just need to make sure this ID varies for each different document so that they are cached correctly.
            For example for files on disk,
            we internally use a string combination of file extension, file size and file date for uniquely
            identifying them, this way cache collisions do not occur and we can resuse the cached file
            even if the file name before extension is changed (because it's still the same document).
            </param>
            <param name="fileName">
            The file name which will be used for display purposes such as when downloading the document
            within <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> or for the subfolder name prefix in cache folder. 
            It will also be used to determine the document format from extension if <paramref name="format"/> 
            parameter is not specified. If not specified or empty, <paramref name="uniqueId"/> will be used 
            as the file name.
            </param>
            <param name="format">The format of the document. If not specified, 
            the format will be automatically determined from the file extension in <paramref name="fileName"/>
            or <paramref name="uniqueId"/>. If your file name does not have an extension, you should explictly
            specify the correct format so that <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> can correctly load the document.
            </param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentInfo.UniqueId">
            <summary>
            Gets the unique identifier of the document.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentInfo.FileName">
            <summary>
            Gets the file name of the document.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentInfo.Format">
            <summary>
            Gets the document format.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.DocumentOptions">
            <summary>
            Represents document options of <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> for pre-caching.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentOptions"/> class.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Watermarks">
            <summary>
            Gets the collection of the text or image watermarks to embed to the displayed document.
            Downloading as PDF will also include the same watermarks.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.InputOptions">
            <summary>
            Gets the collection of input options used when generating the displayed document.
            For example, when you add an instance of <see cref="T:GleamTech.DocumentUltimate.SpreadsheetInputOptions"/> to this collection,
            it will be used only for spreadsheet input files.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Document">
            <summary>
            Gets or sets the document to load and display. 
            This is usually the path to the document to load. 
            Both physical (eg. c:\document.docx) and virtual (eg. /document.docx or ~/document.docx) paths are supported.
            If a custom document handler is provided via <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentHandlerType"/> property, then
            this value will be passed to that handler which should open and return a readable stream according 
            to this file identifier. 
            So it can be any string value that your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> implementation understands.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentFormat">
            <summary>
            Gets or sets the document format to use for the file specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Document"/> property.
            By default, the format is determined from the file extension but you can use this property
            when you don't have an extension or when you need to override the format determined from the extension
            (e.g. you want file.xml to be treated as <see cref="F:GleamTech.DocumentUltimate.DocumentFormat.Txt"/>).
            This property is not effective when you use <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentSource"/> or <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentHandlerType"/>
            properties because those have their own way to specify/override the document format.
            </summary>
            <example>
            <para>Specify/override the document format:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentFormat" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentFormat" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentLocation">
            <summary>
            Gets or sets the file system location for the file specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Document"/> property. 
            By default, the file is considered on disk but you can use this property
            to load files from any of the supported file systems like UNC paths with password, Amazon S3 and Azure Blob.
            When specified <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Document"/> property should be set to a relative path for this location.
            (e.g. "Document.docx" or "SomeFolder\Document.docx")
            </summary>
            <example>
            <para>Using physical/virtual path for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentPhysicalLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentPhysicalLocation" language="vb" />
            <para />
            <para>Using Amazon S3 for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentAmazonS3Location" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentAmazonS3Location" language="vb" />
            <para />
            <para>Using Azure for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentAzureBlobLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentAzureBlobLocation" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentHandlerType">
            <summary>
            Gets or sets the document handler type which provides a custom way of loading the input files.
            This class should implement <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> interface and should have a parameterless
            constructor so that it can be instantiated internally when necessary.
            Value of <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Document"/> property will be passed to this handler which should open 
            and return a readable stream according to that file identifier.
            So it can be any string value that your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> implementation understands.
            <example>
            <para>How to implement IDocumentHandler interface:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for loading documents from a database:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for passing multiple parameters at once:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler1" language="vb" />
            </example>
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentHandlerParameters">
            <summary>
            Gets or sets a name-value collection of parameters which will be passed to your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/>
            implementation specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentHandlerType"/>.
            <example>
            <para>Sample IDocumentHandler implementation for passing multiple parameters at once:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler1" language="vb" />
            </example>
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentSource">
            <summary>
            Gets or sets the document source to load and display.
            This is an alternative to <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Document"/> property.
            When you don't have a file on disk and implementing <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> interface is not
            convenient, you can use this property to load documents from a stream or a byte array.
            Note that your stream or byte array will be copied to the cache folder if not already exists 
            when you load via <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.DocumentSource"/> because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            </summary>
            <example>
            <para>Load document from a stream:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource1" language="vb" />
            <para />
            <para>Initializing a DocumentInfo instance:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentInfo1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentInfo1" language="vb" />
            <para />
            <para>Load document from a byte array:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource2" language="vb" />
            <para />
            <para>Load document from a stream callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_2" language="vb" />
            <para />
            <para>Load document from a byte array callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_2" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.Password">
            <summary>Gets or sets the password which is used for loading a protected document.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentOptions.HighQualityEnabled">
            <summary>
            Gets or sets a value that specifies whether to render high quality within DocumentViewer.
            The default is false.
            For providing some guarantees regarding how quickly a page can be rendered on different systems 
            such as mobile devices, by default some parts of a page that are very complex and would take really 
            long to render in the browser, are rasterized to an image (flattened). However as a side effect, 
            parts of some pages may look blurry when you zoom in. You can set this property to true to disable 
            this possible flattening (if you observe it's happening with your specific document).
            This way you can ensure vector quality rendering for complex documents such as CAD drawings.
            If you have errors due to insufficient memory, keep it at false.
            This setting does not effect the first step of the conversion: Source -> PDF,
            it only effects the second step of the conversion: PDF -> XPZ.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.DocumentSource">
            <summary>
            Defines a document that can be loaded either from a stream or a byte array.
            This class is a convenient alternative to <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> interface which
            requires the developer to maintain the state. However with this class you can load a 
            stream or a byte array that is only available in the context of your host page.
            <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> is more robust but this class is easier to use.
            Note that your stream or byte array will be copied to the cache folder if not already exists 
            when you load via <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentSource"/> because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            </summary>
            <example>
            <para>Load document from a stream:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource1" language="vb" />
            <para />
            <para>Initializing a DocumentInfo instance:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentInfo1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentInfo1" language="vb" />
            <para />
            <para>Load document from a byte array:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource2" language="vb" />
            <para />
            <para>Load document from a stream callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_2" language="vb" />
            <para />
            <para>Load document from a byte array callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_2" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentSource.#ctor(GleamTech.DocumentUltimate.AspNet.DocumentInfo,GleamTech.DocumentUltimate.StreamResult)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentSource"/> class from the specified document stream.
            </summary>
            <param name="documentInfo">The object which contains necessary info to identify the document.</param>
            <param name="documentStream">
            The stream containing the document. 
            The stream is not required to be seekable because it will be copied to the cache folder if not already exists. 
            This is because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            </param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentSource.#ctor(GleamTech.DocumentUltimate.AspNet.DocumentInfo,System.Func{GleamTech.DocumentUltimate.StreamResult})">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentSource"/> class from the specified document stream callback.
            </summary>
            <param name="documentInfo">The object which contains necessary info to identify the document.</param>
            <param name="documentStreamCallback">
            The callback method to return an opened stream containing the document. 
            The returned stream is not required to be seekable because it will be copied to the cache folder if not already exists. 
            This is because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            This callback will only be called when necessary, i.e. if the stream is already cached before 
            then the callback will not be called. 
            You can prefer callback over a direct <see cref="T:System.IO.Stream"/> instance when you need extra performance;
            consider opening the stream is costly in your implementation, for example you are retrieving it from network
            or a database or even from a zip file so opening the stream only when it's really necessary would be more
            performant when your document is viewed several times by several users.
            </param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentSource.#ctor(GleamTech.DocumentUltimate.AspNet.DocumentInfo,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentSource"/> class from the specified document byte array.
            </summary>
            <param name="documentInfo">The object which contains necessary info to identify the document.</param>
            <param name="documentBytes">
            The byte array containing the document. 
            This byte array will be copied to the cache folder if not already exists.
            This is because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            </param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentSource.#ctor(GleamTech.DocumentUltimate.AspNet.DocumentInfo,System.Func{System.Byte[]})">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentSource"/> class from the specified document byte array callback.
            </summary>
            <param name="documentInfo">The object which contains necessary info to identify the document.</param>
            <param name="documentBytesCallback">
            The callback method to return a byte array containing the document. 
            The returned byte array will be copied to the cache folder if not already exists. 
            This is because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            This callback will only be called when necessary, i.e. if the byte array is already cached before 
            then the callback will not be called. 
            You can prefer callback over a direct <see cref="T:byte[]"/> instance when you need extra performance;
            consider retrieving the byte array is costly in your implementation, for example you are retrieving it from network
            or a database or even from a zip file so retrieving the byte array only when it's really necessary would be more
            performant when your document is viewed several times by several users.
            </param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentSource.Info">
            <summary>Gets the document info.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentSource.CopyToStream">
            <summary>
            Gets the function for copying document data to a target stream.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration">
            <summary>
            Provides properties and methods for changing this library's configuration for web use.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration"/> class.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.#ctor(GleamTech.DocumentUltimate.DocumentUltimateConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration"/> class with custom configuration.
            </summary>
            <param name="configuration">The configuration to use instead of the global configuration.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.CacheLocation">
            <summary>
            Gets or sets the location to store cached documents. 
            The default value is "~/App_Data/DocumentCache".
            The location can be a plain physical/virtual path string or
            a <see cref="T:GleamTech.FileSystems.Location"/> instance for one of the supported file systems like Amazon S3 and Azure Blob.
            </summary>
            <example>
            <para>Using physical/virtual path for cache location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentUltimateWebConfigurationExamples.cs" region="CachePhysicalLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentUltimateWebConfigurationExamples.vb" region="CachePhysicalLocation" language="vb" />
            <code source="AspNetWebFormsCS\DocumentUltimate\AppSettings.Web.config" region="CachePhysicalLocation" language="xml" />
            <para />
            <para>Using Amazon S3 for cache location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentUltimateWebConfigurationExamples.cs" region="CacheAmazonS3Location" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentUltimateWebConfigurationExamples.vb" region="CacheAmazonS3Location" language="vb" />
            <code source="AspNetWebFormsCS\DocumentUltimate\AppSettings.Web.config" region="CacheAmazonS3Location" language="xml" />
            <para />
            <para>Using Azure for cache location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentUltimateWebConfigurationExamples.cs" region="CacheAzureBlobLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentUltimateWebConfigurationExamples.vb" region="CacheAzureBlobLocation" language="vb" />
            <code source="AspNetWebFormsCS\DocumentUltimate\AppSettings.Web.config" region="CacheAzureBlobLocation" language="xml" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.CacheMaxAge">
            <summary>
            Gets or sets a value that specifies the maximum amount of time to consider a cached item fresh and keep it stored in the cache.
            Cached items older than this age will be removed when the cache trimming (clean up) runs (when auto trim is run or
            when Trim method is manually called).
            The default value is 90 days.
            In string representation, value in format "d" is considered days (e.g. "30" is 30 days) and value in format "hh:mm" is considered hours and minutes
            (e.g. "1:30" is 1 hour and 30 minutes). Note that "24:00" would mean 24 days as hour range is 0 to 23 and minute range is 0 to 59.
            See <see cref="M:System.TimeSpan.Parse(System.String)"/> documentation for details on possible string representations.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.CacheWaitTimeout">
            <summary>
            Gets or sets a value that specifies the maximum amount of time to wait for a cached item to be available (wait for a file lock from other threads or
            processes to become available, i.e. wait for an ongoing caching of the same file to complete) before giving up on the cache request.
            The default value is 5 minutes.
            In string representation, value in format "d" is considered days (e.g. "30" is 30 days) and value in format "hh:mm" is considered hours and minutes
            (e.g. "1:30" is 1 hour and 30 minutes). Note that "24:00" would mean 24 days as hour range is 0 to 23 and minute range is 0 to 59.
            See <see cref="M:System.TimeSpan.Parse(System.String)"/> documentation for details on possible string representations.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.CacheAutoTrimInterval">
            <summary>
            Gets or sets a value that specifies the interval to run automatic cache trimming (clean up).
            Cached items older than <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.CacheMaxAge"/> will be removed when the cache is trimmed.
            If the value is 0 or negative, the auto trim is disabled.
            The default value is 20 minutes.
            In string representation, value in format "d" is considered days (e.g. "30" is 30 days) and value in format "hh:mm" is considered hours and minutes
            (e.g. "1:30" is 1 hour and 30 minutes). Note that "24:00" would mean 24 days as hour range is 0 to 23 and minute range is 0 to 59.
            See <see cref="M:System.TimeSpan.Parse(System.String)"/> documentation for details on possible string representations.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.CacheEncryptionEnabled">
            <summary>
            Gets a value that specifies whether encryption of documents should be enabled.
            The default is true.
            Document viewer downloads a special document format (XPZ) on the client-side but for additional 
            security (DRM), the document is also encypted by default.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.CacheKeepVariations">
            <summary>
            Gets or sets a value that specifies whether to keep variations of document conversion results.
            The default is false.
            For instance when you change watermark options, both Pdf and Xpz outputs are regenerated.
            By default the Pdf and Xpz outputs are replaced in cache when watermarks are changed.
            If you set this property to true, the Pdf and Xpz outputs will not be replaced but all variations will
            be kept in cache instead. This can be useful especially when you want different outputs for different users.
            So this way, for instance you can have different watermarked variations for each different user.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.GetCache">
            <summary>Gets the document cache used by this web configuration instance.</summary>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentCache"/> instance.</returns>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.DocumentViewerHandlerPath">
            <summary>
            Gets or sets a value that specifies the path for the document viewer handler. 
            The default value is "documentviewer.ashx".
            Normally you would not need to change the default value.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.InitCurrent">
            <summary>Initializes only the static Current instance.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration.Current">
            <summary>Gets current global configuration instance.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler">
            <summary>
            Represents an object for handling of the input files in <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/>.
            Implement this interface to provide a custom way of loading the input files.
            </summary>
            <example>
            <para>How to implement IDocumentHandler interface:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for loading documents from a database:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for passing multiple parameters at once:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.IDocumentHandler.GetInfo(System.String,GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters)">
            <summary>
            Gets the required information which corresponds to the current input file.
            This method will be called every time DocumentViewer requests a document.
            The cache key and document format will be determined according to the info you return here.
            The returned <see cref="P:GleamTech.DocumentUltimate.AspNet.DocumentInfo.UniqueId"/>determines the cache key, 
            so when uniqueId is changed it's considered a different file.
            </summary>
            <param name="inputFile">
            The input file that was requested to be loaded in <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/>, i.e. the value 
            that was set in <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document"/> property.
            </param>
            <param name="handlerParameters">
            The custom parameters to be used in your handler implementation i.e. the parameters 
            that were set in <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentHandlerParameters"/> property.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentInfo"/> instance initialized with required information.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.IDocumentHandler.OpenRead(System.String,GleamTech.DocumentUltimate.InputOptions,GleamTech.DocumentUltimate.AspNet.DocumentHandlerParameters)">
            <summary>
            Opens a readable stream which corresponds to the current input file.
            This method will be called only when original input document is required to be read by the DocumentViewer, 
            for example if DocumentViewer already did the required conversions and cached the results, 
            it will not be called.
            </summary>
            <param name="inputFile">The input file for the current document conversion.</param>
            <param name="inputOptions">The input options that was specified for the current document conversion.</param>
            <param name="handlerParameters">
            The custom parameters to be used in your handler implementation i.e. the parameters 
            that were set in <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentHandlerParameters"/> property.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> instance initialized with a readable <see cref="T:System.IO.Stream" /> object.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.NamespaceDoc">
            <summary>
            This is a sub-namespace of this library for web use, it extends the core namespace to support ASP.NET and handles caching for DocumentViewer.
            <para />
            <para>The main classes in this namespace are:</para>
            <list type="bullet">
                <item><see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentCache"/> is the class for handling caching of documents.</item>
                <item><see cref="T:GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration"/> is the class for changing this library's configuration for web use (e.g. setting cache location for DocumentViewer).</item>
            </list>
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer">
            <summary>
            The component for viewing several formats of documents.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> class.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.#ctor(GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> class with custom web configuration.
            </summary>
            <param name="webConfiguration">The web configuration to use instead of the global web configuration.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.AllowedPermissions">
            <summary>
            Gets or sets a value that specifies the allowed permissions for the document viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.All"/>.
            Denied permissions take precedence over allowed permissions. 
            For instance, when <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.AllowedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.All"/>
            and <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DeniedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Download"/> and <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Print"/>
            all permissions except Download and Print will be allowed.
            When combining permissions, they should be separated by comma in string and by bitwise 'or' operator
            in code (| in C# and OR in VB).
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DeniedPermissions">
            <summary>
            Gets or sets a value that specifies the denied permissions for the document viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.None"/>.
            Denied permissions take precedence over allowed permissions. 
            For instance, when <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.AllowedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.All"/>
            and <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DeniedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Download"/> and <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Print"/>
            all permissions except Download and Print will be allowed.
            When combining permissions, they should be separated by comma in string and by bitwise 'or' operator
            in code (| in C# and OR in VB).
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.FitMode">
            <summary>
            Gets or sets a value that specifies the initial mode for fitting pages to the viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.FitMode.FitWidth"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.LayoutMode">
            <summary>
            Gets or sets a value that specifies the initial mode for placing pages within the viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.Continuous"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.MobileMode">
            <summary>
            Gets or sets a value that specifies which mobile device type to use mobile document viewer on.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.MobileMode.OnAny"/>.
            Note that using full document viewer on mobile devices would cause performance issues (slow rendering), 
            so you should prefer mobile document viewer which is optimized for mobile devices.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.ZoomLevel">
            <summary>
            Gets or sets a value in percentage that specifies the initial zoom level of the document.
            The default is 100.
            For this property to be effective <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.FitMode"/> property should be set 
            to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.FitMode.Zoom"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DownloadUrl">
            <summary>
            Gets or sets a value that specifies the custom url that should be used for downloading. 
            By default, when you click the download button, the download is handled internally by the document viewer.
            You can override the default behaviour by specifying your own url which handles the download. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DownloadFileName">
            <summary>
            Gets or sets a value that specifies the custom file name used for downloading.
            This property will override the original file name specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document"/>. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.ToolbarVisible">
            <summary>
            Gets or sets a value that specifies whether the top toolbar is visible.
            The default is true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.SidePaneVisible">
            <summary>
            Gets or sets a value that specifies whether the left side pane is visible.
            The default is true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Language">
            <summary>Gets or sets the display language of the document viewer.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.LoadingMessage">
            <summary>
            Gets or sets a value that specifies the message displayed while the document is being prepared 
            for the first-time viewing. For example, you can override the default message for localization purpose. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.SearchOptions">
            <summary>
            Gets or sets the text search options of the viewer. 
            If you specify the term, an automatic search will be done when the document is displayed, i.e.
            the specified term will be searched and results with clickable positions will be listed 
            on the left pane and the term will be highlighted in the pages.
            For example, if you launch the document viewer from a search results page, you can pass the same 
            search term to the viewer.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.PrintOptions">
            <summary>
            Gets or sets the print options of the viewer. 
            The options will be reflected as default values on the print dialog of the viewer.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.SinglePageRotationEnabled">
            <summary>
            Gets or sets a value that specifies whether rotate buttons should only rotate the currently viewed 
            page rather than all pages. The default is false.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Watermarks">
            <summary>
            Gets the collection of the text or image watermarks to embed to the displayed document.
            Downloading as PDF will also include the same watermarks.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.InputOptions">
            <summary>
            Gets the collection of input options used when generating the displayed document.
            For example, when you add an instance of <see cref="T:GleamTech.DocumentUltimate.SpreadsheetInputOptions"/> to this collection,
            it will be used only for spreadsheet input files.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document">
            <summary>
            Gets or sets the document to load and display. 
            This is usually the path to the document to load. 
            Both physical (eg. c:\document.docx) and virtual (eg. /document.docx or ~/document.docx) paths are supported.
            If a custom document handler is provided via <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentHandlerType"/> property, then
            this value will be passed to that handler which should open and return a readable stream according 
            to this file identifier. 
            So it can be any string value that your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> implementation understands.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentFormat">
            <summary>
            Gets or sets the document format to use for the file specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document"/> property.
            By default, the format is determined from the file extension but you can use this property
            when you don't have an extension or when you need to override the format determined from the extension
            (e.g. you want file.xml to be treated as <see cref="F:GleamTech.DocumentUltimate.DocumentFormat.Txt"/>).
            This property is not effective when you use <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentSource"/> or <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentHandlerType"/>
            properties because those have their own way to specify/override the document format.
            </summary>
            <example>
            <para>Specify/override the document format:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentFormat" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentFormat" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentLocation">
            <summary>
            Gets or sets the file system location for the file specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document"/> property. 
            By default, the file is considered on disk but you can use this property
            to load files from any of the supported file systems like UNC paths with password, Amazon S3 and Azure Blob.
            When specified <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document"/> property should be set to a relative path for this location.
            (e.g. "Document.docx" or "SomeFolder\Document.docx")
            </summary>
            <example>
            <para>Using physical/virtual path for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentPhysicalLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentPhysicalLocation" language="vb" />
            <para />
            <para>Using Amazon S3 for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentAmazonS3Location" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentAmazonS3Location" language="vb" />
            <para />
            <para>Using Azure for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentAzureBlobLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentAzureBlobLocation" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentHandlerType">
            <summary>
            Gets or sets the document handler type which provides a custom way of loading the input files.
            This class should implement <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> interface and should have a parameterless
            constructor so that it can be instantiated internally when necessary.
            Value of <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document"/> property will be passed to this handler which should open 
            and return a readable stream according to that file identifier.
            So it can be any string value that your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> implementation understands.
            </summary>
            <example>
            <para>How to implement IDocumentHandler interface:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for loading documents from a database:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for passing multiple parameters at once:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler1" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentHandlerParameters">
            <summary>
            Gets or sets a name-value collection of parameters which will be passed to your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/>
            implementation specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentHandlerType"/>.
            </summary>
            <example>
            <para>Sample IDocumentHandler implementation for passing multiple parameters at once:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler1" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentSource">
            <summary>
            Gets or sets the document source to load and display.
            This is an alternative to <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Document"/> property.
            When you don't have a file on disk and implementing <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> interface is not
            convenient, you can use this property to load documents from a stream or a byte array.
            Note that your stream or byte array will be copied to the cache folder if not already exists 
            when you load via <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.DocumentSource"/> because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            </summary>
            <example>
            <para>Load document from a stream:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource1" language="vb" />
            <para />
            <para>Initializing a DocumentInfo instance:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentInfo1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentInfo1" language="vb" />
            <para />
            <para>Load document from a byte array:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource2" language="vb" />
            <para />
            <para>Load document from a stream callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_2" language="vb" />
            <para />
            <para>Load document from a byte array callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_2" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.Password">
            <summary>Gets or sets the password which is used for loading a protected document.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.HighQualityEnabled">
            <summary>
            Gets or sets a value that specifies whether to render high quality within DocumentViewer.
            The default is false.
            For providing some guarantees regarding how quickly a page can be rendered on different systems 
            such as mobile devices, by default some parts of a page that are very complex and would take really 
            long to render in the browser, are rasterized to an image (flattened). However as a side effect, 
            parts of some pages may look blurry when you zoom in. You can set this property to true to disable 
            this possible flattening (if you observe it's happening with your specific document).
            This way you can ensure vector quality rendering for complex documents such as CAD drawings.
            If you have errors due to insufficient memory, keep it at false.
            This setting does not effect the first step of the conversion: Source -> PDF,
            it only effects the second step of the conversion: PDF -> XPZ.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.ClientEvents">
            <summary>Gets or sets the client-side event handlers.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.GetBodyTags">
            <summary>Gets body tags of the component.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.WriteProperties(GleamTech.AspNet.UI.PropertyWriter)">
            <summary>Writes client properties of the component.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.GetMetaTags">
            <summary>Gets meta tags of the component.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.GetCssFileTags">
            <summary>Gets css file tags of the component.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer.GetJsFileTags">
            <summary>Gets js file tags of the component.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents">
            <summary>
            Defines the client-side events handlers of DocumentViewer.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.Loaded">
            <summary>
            Gets or sets the client-side event raised when the document viewer has been loaded. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="Loaded" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.Failed">
            <summary>
            Gets or sets the client-side event raised when an error occurs. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="Failed" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.DocumentLoaded">
            <summary>
            Gets or sets the client-side event raised when a document has been loaded. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="DocumentLoaded" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.PageChanged">
            <summary>
            Gets or sets the client-side event raised when viewed page is changed. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="PageChanged" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.PageRendered">
            <summary>
            Gets or sets the client-side event raised when a page had finished rendering.
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="PageRendered" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.RotationChanged">
            <summary>
            Gets or sets the client-side event raised when page rotation is changed. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="RotationChanged" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.Downloading">
            <summary>
            Gets or sets the client-side event raised before user downloads the original document or PDF version. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="Downloading" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.Printing">
            <summary>
            Gets or sets the client-side event raised before user prints the document. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="Printing" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.PrintProgress">
            <summary>
            Gets or sets the client-side event raised when print progress is changed. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="PrintProgress" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.Printed">
            <summary>
            Gets or sets the client-side event raised when printing is completed. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="Printed" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.TextSelected">
            <summary>
            Gets or sets the client-side event raised when user selects text in the document. 
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="TextSelected" language="js" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerClientEvents.TextCopied">
            <summary>
            Gets or sets the client-side event raised when user copies the selected text in the document.
            Copying to clipboard with popup menu or keyboard shortcut (e.g. CTRL + C) will both be detected.
            The value should be a valid JavaScript function name which is accessible on the host page. 
            Function names should be specified without parentheses like "FunctionName" or "Namespace.FunctionName". 
            </summary>
            <example>
            <para>Example function:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerClientEvents.js" region="TextCopied" language="js" />
            </example>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions">
            <summary>Defines the the document viewer permissions.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.None">
            <summary>
            None of the permissions (unset).
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Download">
            <summary>
            Ability to download the original document.
            Downloading can be disabled for security (DRM) purpose.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.DownloadAsPdf">
            <summary>
            Ability to download a PDF version of the original document .
            Downloading as PDF can be disabled for additional security (DRM) purpose.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Print">
            <summary>
            Ability to print the displayed document 
            Printing can be disabled for additional security (DRM) purpose.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.SelectText">
            <summary>
            Ability to select and copy text in the displayed document 
            Text selection can be disabled for additional security (DRM) purpose.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.NavigatePages">
            <summary>
            Ability to navigate pages, e.g. next page, previous page and jump to page.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.NavigateHistory">
            <summary>
            Ability to navigate UI history, e.g. go back and go forward.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Zoom">
            <summary>
            Ability to zoom in or out of the document.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Pan">
            <summary>
            Ability to pan the document.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.ChangeFitMode">
            <summary>
            Ability to change fit mode of the document, e.g. fit width and fit page.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.ChangeLayoutMode">
            <summary>
            Ability to change layout mode of the document, e.g. continuous, facing etc.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Rotate">
            <summary>
            Ability to rotate the page or document, e.g. rotate clockwise and rotate counterclockwise.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Find">
            <summary>
            Ability to find text (simple search) within the document.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.GoFullScreen">
            <summary>
            Ability to switch the document viewer to full screen.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.ViewThumbnails">
            <summary>
            Ability to view thumbnails for the document pages.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.ViewBookmarks">
            <summary>
            Ability to view bookmarks/outlines of the document.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Search">
            <summary>
            Ability to search text (advanced search) within the document.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.ViewAnnotations">
            <summary>
            Ability to view annotations of the document.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.FillForms">
            <summary>
            Ability to fill values into form fields of the document.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.All">
            <summary>
            All the permissions.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerState">
            <tocexclude />
            <summary>Represents DocumentViewer state.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.FitMode">
            <summary>
            Defines posible modes for fitting pages to the viewer. 
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.FitMode.FitWidth">
            <summary>
            The document's zoom level will be adjusted so that the width 
            of the current page will exactly fit into the available space.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.FitMode.FitPage">
            <summary>
            The document's zoom level will be adjusted so that the width or height (whichever is smaller)
            of the current page will exactly fit into the available space.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.FitMode.Zoom">
            <summary>
            The document's zoom level will freely adjusted and not constrained with the width and height 
            of the current page.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode">
            <summary>
            Defines possible modes for placing pages within the viewer.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.Continuous">
            <summary>
             All pages are visible in one column.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.Single">
            <summary>
            Only the current page will be visible.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.FacingContinuous">
            <summary>
            All pages visible in two columns.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.Facing">
            <summary>
            Up to two pages will be visible.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.FacingCoverContinuous">
             <summary>
            All pages visible, with an even numbered page rendered first. 
            The first page of the document is rendered by itself on the right side of the viewer 
            to simulate a book cover.
             </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.FacingCover">
            <summary>
            All pages visible in two columns, with an even numbered page rendered first. 
            The first page of the document is rendered by itself on the right side of the viewer 
            to simulate a book cover.)
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.MatchOptions">
            <summary>
            Defines posible match options for text search in the viewer. 
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MatchOptions.None">
            <summary>No specific match option.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MatchOptions.MatchAnyWord">
            <summary>
            The search should match the term as a whole or match any word in the term separately 
            (e.g. "bicycle bells" or "bicycle" or "bells").
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MatchOptions.MatchCase">
            <summary>
            The search should be case sensitive, i.e the found text must match the case of the search term.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MatchOptions.MatchWholeWord">
            <summary>
            The search should match the whole word only, 
            i.e the found text must be a whole word (surrounded by a non-word character).
            (e.g. "doc" matches " doc " or ".doc" but not "document").
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MatchOptions.UseWildcard">
            <summary>
            The search should treat <value>*</value> (asterisk) character in the search term as a wildcard.
            Wildcard <value>*</value> matches zero or more characters.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.MobileMode">
            <summary>
            Defines modes which specify mobile device types to use mobile document viewer on.
            </summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MobileMode.OnAny">
            <summary>Use mobile document viewer on any mobile device.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MobileMode.OnPhone">
            <summary>Use mobile document viewer only on phones.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MobileMode.OnTablet">
            <summary>Use mobile document viewer only on tablets.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.AspNet.UI.MobileMode.Never">
            <summary>Never use mobile document viewer, i.e. always use full document viewer.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.NamespaceDoc">
            <summary>
            This is a sub-namespace of this library which contains the DocumentViewer component and related classes.
            <para />
            <para>The main classes in this namespace are:</para>
            <list type="bullet">
                <item><see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> is the ASP.NET component for viewing several formats of documents.</item>
            </list>
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions">
            <summary>
            Represents print options of the viewer.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions"/> class.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.HighQuality">
            <summary>
            Gets or sets a value that specifies whether to print in high quality.
            The default is true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.HighQualityLevel">
            <summary>
            Gets or sets a value in percentage that specifies the level used when printing in high quality.
            The default is 100.
            Level 100 ensures excellent quality but may cause slow printing for some documents, you can decrease 
            the level to increase the printing speed while maintaining an acceptable quality for your case.
            For this property to be effective <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.HighQuality"/> property should be set 
            to true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.Grayscale">
            <summary>
            Gets or sets a value that specifies whether to print in grayscale (black and white).
            The default is false.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.IncludeComments">
            <summary>
            Gets or sets a value that specifies whether to include annotation comments in the print output.
            The default is false.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.SkipDialog">
            <summary>
            Gets or sets a value that specifies whether to start printing immediately without showing DocumentViewer's print dialog.
            The default is false.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.PageRange">
            <summary>
            Gets or sets a value that specifies the page numbers (eg. "1-5, 8, 11-13") to print.
            This property can be useful especially when <see cref="P:GleamTech.DocumentUltimate.AspNet.UI.PrintOptions.SkipDialog"/> is set to true and you need to restrict user to print
            only specific pages.
            The default is "" which means all pages.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.UI.SearchOptions">
            <summary>
            Represents text search options of the viewer.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.UI.SearchOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.SearchOptions"/> class.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.SearchOptions.Term">
            <summary>Gets or sets a value that specifies the search term used.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.SearchOptions.MatchOptions">
            <summary>
            Gets or sets a value that specifies the match options for text search.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.SearchOptions.HighlightColor">
            <summary>Gets or sets the color to use for highlighting search results. Default is yellow.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.UI.SearchOptions.ActiveHighlightColor">
            <summary>Gets or sets the color to use for highlighting search results. Default is orange.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.WebActivation">
            <exclude />
            <summary>
            Handles ASP.NET application activation.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.WebActivation.PreApplicationStart">
            <summary>Runs automatically before the ASP.NET application is started.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl">
            <summary>
            The control for viewing several formats of documents.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.#ctor(GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl"/> class with an existing
            <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> component instance.
            </summary>
            <param name="documentViewer">The existing <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> component instance to use.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl"/> class.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.#ctor(GleamTech.DocumentUltimate.AspNet.DocumentUltimateWebConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl"/> class with custom web configuration.
            </summary>
            <param name="webConfiguration">The web configuration to use instead of the global web configuration.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.AllowedPermissions">
            <summary>
            Gets or sets a value that specifies the allowed permissions for the document viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.All"/>.
            Denied permissions take precedence over allowed permissions. 
            For instance, when <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.AllowedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.All"/>
            and <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DeniedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Download"/> and <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Print"/>
            all permissions except Download and Print will be allowed.
            When combining permissions, they should be separated by comma in string and by bitwise 'or' operator
            in code (| in C# and OR in VB).
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DeniedPermissions">
            <summary>
            Gets or sets a value that specifies the denied permissions for the document viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.None"/>.
            Denied permissions take precedence over allowed permissions. 
            For instance, when <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.AllowedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.All"/>
            and <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DeniedPermissions"/> is set to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Download"/> and <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewerPermissions.Print"/>
            all permissions except Download and Print will be allowed.
            When combining permissions, they should be separated by comma in string and by bitwise 'or' operator
            in code (| in C# and OR in VB).
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.FitMode">
            <summary>
            Gets or sets a value that specifies the initial mode for fitting pages to the viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.FitMode.FitWidth"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.LayoutMode">
            <summary>
            Gets or sets a value that specifies the initial mode for placing pages within the viewer.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.LayoutMode.Continuous"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.ZoomLevel">
            <summary>
            Gets or sets a value in percentage that specifies the initial zoom level of the document.
            The default is 100.
            For this property to be effective <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.FitMode"/> property should be set 
            to <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.FitMode.Zoom"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.MobileMode">
            <summary>
            Gets or sets a value that specifies which mobile device type to use mobile document viewer on.
            The default is <see cref="F:GleamTech.DocumentUltimate.AspNet.UI.MobileMode.OnAny"/>.
            Note that using full document viewer on mobile devices would cause performance issues (slow rendering), 
            so you should prefer mobile document viewer which is optimized for mobile devices.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DownloadUrl">
            <summary>
            Gets or sets a value that specifies the custom url that should be used for downloading. 
            By default, when you click the download button, the download is handled internally by the document viewer.
            You can override the default behaviour by specifying your own url which handles the download. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DownloadFileName">
            <summary>
            Gets or sets a value that specifies the custom file name used for downloading.
            This property will override the original file name specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Document"/>. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.ToolbarVisible">
            <summary>
            Gets or sets a value that specifies whether the top toolbar is visible.
            The default is true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.SidePaneVisible">
            <summary>
            Gets or sets a value that specifies whether the left side pane is visible.
            The default is true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Language">
            <summary>Gets or sets the display language of the document viewer.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.LoadingMessage">
            <summary>
            Gets or sets a value that specifies the message displayed while the document is being prepared 
            for the first-time viewing. For example, you can override the default message for localization purpose. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.SearchOptions">
            <summary>
            Gets or sets the text search options of the viewer. 
            If you specify the term, an automatic search will be done when the document is displayed, i.e.
            the specified term will be searched and results with clickable positions will be listed 
            on the left pane and the term will be highlighted in the pages.
            For example, if you launch the document viewer from a search results page, you can pass the same 
            search term to the viewer.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.PrintOptions">
            <summary>
            Gets or sets the print options of the viewer. 
            The options will be reflected as default values on the print dialog of the viewer.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.SinglePageRotationEnabled">
            <summary>
            Gets or sets a value that specifies whether rotate buttons should only rotate the currently viewed 
            page rather than all pages. The default is false.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Watermarks">
            <summary>
            Gets the collection of the text or image watermarks to embed to the displayed document.
            Downloading as PDF will also include the same watermarks.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.InputOptions">
            <summary>
            Gets the collection of input options used when generating the displayed document.
            For example, when you add an instance of <see cref="T:GleamTech.DocumentUltimate.SpreadsheetInputOptions"/> to this collection,
            it will be used only for spreadsheet input files.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Document">
            <summary>
            Gets or sets the document to load and display. 
            This is usually the path to the document to load. 
            Both physical (eg. c:\document.docx) and virtual (eg. /document.docx or ~/document.docx) paths are supported.
            If a custom document handler is provided via <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentHandlerType"/> property, then
            this value will be passed to that handler which should open and return a readable stream according 
            to this file identifier. 
            So it can be any string value that your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> implementation understands.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentFormat">
            <summary>
            Gets or sets the document format to use for the file specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Document"/> property.
            By default, the format is determined from the file extension but you can use this property
            when you don't have an extension or when you need to override the format determined from the extension
            (e.g. you want file.xml to be treated as <see cref="F:GleamTech.DocumentUltimate.DocumentFormat.Txt"/>).
            This property is not effective when you use <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentSource"/> or <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentHandlerType"/>
            properties because those have their own way to specify/override the document format.
            </summary>
            <example>
            <para>Specify/override the document format:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentFormat" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentFormat" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentLocation">
            <summary>
            Gets or sets the file system location for the file specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Document"/> property. 
            By default, the file is considered on disk but you can use this property
            to load files from any of the supported file systems like UNC paths with password, Amazon S3 and Azure Blob.
            When specified <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Document"/> property should be set to a relative path for this location.
            (e.g. "Document.docx" or "SomeFolder\Document.docx")
            </summary>
            <example>
            <para>Using physical/virtual path for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentPhysicalLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentPhysicalLocation" language="vb" />
            <para />
            <para>Using Amazon S3 for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentAmazonS3Location" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentAmazonS3Location" language="vb" />
            <para />
            <para>Using Azure for document location:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentAzureBlobLocation" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentAzureBlobLocation" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentHandlerType">
            <summary>
            Gets or sets the document handler type which provides a custom way of loading the input files.
            This class should implement <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> interface and should have a parameterless
            constructor so that it can be instantiated internally when necessary.
            Value of <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Document"/> property will be passed to this handler which should open 
            and return a readable stream according to that file identifier.
            So it can be any string value that your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> implementation understands.
            </summary>
            <example>
            <para>How to implement IDocumentHandler interface:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="CustomDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="CustomDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for loading documents from a database:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DbDocumentHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DbDocumentHandler1_1" language="vb" />
            <para />
            <para>Sample IDocumentHandler implementation for passing multiple parameters at once:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler1" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentHandlerParameters">
            <summary>
            Gets or sets a name-value collection of parameters which will be passed to your <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/>
            implementation specified in <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentHandlerType"/>.
            </summary>
            <example>
            <para>Sample IDocumentHandler implementation for passing multiple parameters at once:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler2" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="MultiParameterDocumentHandler1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="MultiParameterDocumentHandler1" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentSource">
            <summary>
            Gets or sets the document source to load and display.
            This is an alternative to <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Document"/> property.
            When you don't have a file on disk and implementing <see cref="T:GleamTech.DocumentUltimate.AspNet.IDocumentHandler"/> interface is not
            convenient, you can use this property to load documents from a stream or a byte array.
            Note that your stream or byte array will be copied to the cache folder if not already exists 
            when you load via <see cref="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.DocumentSource"/> because DocumentViewer needs to access your document out
            of the context of the host page (i.e. serialization is needed).
            </summary>
            <example>
            <para>Load document from a stream:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource1" language="vb" />
            <para />
            <para>Initializing a DocumentInfo instance:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentInfo1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentInfo1" language="vb" />
            <para />
            <para>Load document from a byte array:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource2" language="vb" />
            <para />
            <para>Load document from a stream callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource3_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource3_2" language="vb" />
            <para />
            <para>Load document from a byte array callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentViewerExamples.cs" region="DocumentSource4_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentViewerExamples.vb" region="DocumentSource4_2" language="vb" />
            </example>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.Password">
            <summary>Gets or sets the password which is used for loading a protected document.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.HighQualityEnabled">
            <summary>
            Gets or sets a value that specifies whether to render high quality within DocumentViewer.
            The default is false.
            For providing some guarantees regarding how quickly a page can be rendered on different systems 
            such as mobile devices, by default some parts of a page that are very complex and would take really 
            long to render in the browser, are rasterized to an image (flattened). However as a side effect, 
            parts of some pages may look blurry when you zoom in. You can set this property to true to disable 
            this possible flattening (if you observe it's happening with your specific document).
            This way you can ensure vector quality rendering for complex documents such as CAD drawings.
            If you have errors due to insufficient memory, keep it at false.
            This setting does not effect the first step of the conversion: Source -> PDF,
            it only effects the second step of the conversion: PDF -> XPZ.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl.ClientEvents">
            <summary>Gets or sets the client-side event handlers.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.AspNet.WebForms.NamespaceDoc">
            <summary>
            This is a sub-namespace of this library for ASP.NET Web Forms support of DocumentViewer.
            <para />
            <para>The main classes in this namespace are:</para>
            <list type="bullet">
                <item><see cref="T:GleamTech.DocumentUltimate.AspNet.WebForms.DocumentViewerControl"/> is the ASP.NET Web Forms Control for viewing several formats of documents.</item>
            </list>
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.CadInputOptions">
            <summary>Represents a set of Cad input options used for document conversion.</summary>
            <example>
            <para>Render as black background but keep foreground colors:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CadInputOptions1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CadInputOptions1" language="vb" />
            <para />
            <para>Render as black and white:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CadInputOptions2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CadInputOptions2" language="vb" />
            <para />
            <para>Render as blueprint:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CadInputOptions3" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CadInputOptions3" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.CadInputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.CadInputOptions"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.CadInputOptions.#ctor(GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.CadInputOptions"/> class with a document format.</summary>
            <param name="format">The format to convert from.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.CadInputOptions.RenderBackgroundColor">
            <summary>
            Gets or sets the background color of the drawing when it's rendered.
            The default is white.
            Default foreground color in the drawing is corrected automatically when required so that 
            it's always visible on the set background color.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.CadInputOptions.RenderForegroundColor">
            <summary>
            Gets or sets the foreground color of the drawing when it's rendered.
            The default is null, which means foreground colors will be kept same as in the drawing.
            When set, all foreground colors will be fixed to this same color.
            For example, you can have a black and white or a blueprint output.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.ConversionContext">
             <summary>
             Encapsulates all specific information about an individual document conversion.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.ConversionContext.InputContext">
            <summary>Gets the input context for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.ConversionContext.OutputContext">
            <summary>Gets the output context for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.ConversionContext.CurrentPageNumber">
            <summary>
            Gets the current page number being converted. 
            Value is valid only for multi-page output conversions, otherwise it's 0.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.ConversionContext.FinalPageNumber">
            <summary>
            Gets the final page number of the current document conversion. 
            Value is valid only for multi-page output conversions, otherwise it's 0.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentConverter">
            <summary>
            Provides methods for document conversion.
            </summary>
            <example>
            <para>You can use static Convert method to easily convert an input document to another format:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="Convert1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="Convert1" language="vb" />
            <para />
            <para>You can use static CanConvert method to check beforehand if an input document can be directly converted another format:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CanConvert1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CanConvert1" language="vb" />
            <para />
            <para>You can also create an instance of DocumentConverter class with an input document and call instance methods ConvertTo and CanConvertTo. This is useful especially when you want to convert the same input document to several output formats:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertTo1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertTo1" language="vb" />
            <para />
            <para>You can enumerate possible output document formats for a given input document file with EnumeratePossibleOutputFormats method and vice versa with EnumeratePossibleInputFormats method:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="EnumerateFormats1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="EnumerateFormats1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.#ctor(System.String,GleamTech.DocumentUltimate.InputOptions,GleamTech.DocumentUltimate.IInputHandler,GleamTech.DocumentUltimate.DocumentUltimateConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/> class from a document file.
            </summary>
            <param name="inputFile">
            The path to the document file to convert.
            If a custom input handler is provided via <paramref name="inputHandler"/> parameter, then
            this value will be passed to that handler which should open and return a readable stream according 
            to this file identifier.
            </param>
            <param name="inputOptions">
            The input options which contains format and other options for the file to be converted.If not specified, 
            the format will be automatically determined from the file extension in <paramref name="inputFile"/>
            </param>
            <param name="inputHandler">The input handler which provides a custom way of loading the input files.</param>
            <param name="configuration">The configuration to use instead of the global configuration.</param>
            <example>
            <para>Sample IInputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomInputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomInputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomInputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomInputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.#ctor(System.String,GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.IInputHandler,GleamTech.DocumentUltimate.DocumentUltimateConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/> class from a document file.
            </summary>
            <param name="inputFile">
            The path to the document file to convert.
            If a custom input handler is provided via <paramref name="inputHandler"/> parameter, then
            this value will be passed to that handler which should open and return a readable stream according 
            to this file identifier.
            </param>
            <param name="inputFormat">The format of the file to be converted.</param>
            <param name="inputHandler">The input handler which provides a custom way of loading the input files.</param>
            <param name="configuration">The configuration to use instead of the global configuration.</param>
            <example>
            <para>Sample IInputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomInputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomInputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomInputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomInputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.#ctor(System.IO.Stream,GleamTech.DocumentUltimate.InputOptions,GleamTech.DocumentUltimate.DocumentUltimateConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/> class from a document stream.
            </summary>
            <param name="inputStream">The stream containing the document to convert.</param>
            <param name="inputOptions">
            The input options which contains format and other options for the file to be converted.
            </param>
            <param name="configuration">The configuration to use instead of the global configuration.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.#ctor(System.IO.Stream,GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.DocumentUltimateConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/> class from a document stream.
            </summary>
            <param name="inputStream">The stream containing the document to convert.</param>
            <param name="inputFormat">The format of the file to be converted.</param>
            <param name="configuration">The configuration to use instead of the global configuration.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.CanConvertTo(GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Gets a value that indicates whether a direct conversion is possible to the specified format.</summary>
            <param name="outputFormat">The format to convert to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>true if a direct conversion is possible; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.CanConvertTo(System.String,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Gets a value that indicates whether a direct conversion is possible to the specified file.</summary>
            <param name="outputFile">The file name or path or extension (with or without leading dot) of the converted document file.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>true if a direct conversion is possible; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.EnumeratePossibleOutputFormats(System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Enumerates possible output document formats for the current input document file.</summary>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>An enumerable collection of document formats.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.IOutputHandler,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputFormat"/>.
            </summary>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="outputHandler">The output handler which provides a custom way of saving the output files.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Sample IOutputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(GleamTech.DocumentUltimate.OutputOptions,GleamTech.DocumentUltimate.IOutputHandler,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputOptions"/>.
            </summary>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file.
            </param>
            <param name="outputHandler">The output handler which provides a custom way of saving the output files.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Sample IOutputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.String,GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.IOutputHandler,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output path and format.
            </summary>
            <param name="outputFile">
            The path to the converted document file.
            If a custom output handler is provided via <paramref name="outputHandler"/> parameter, then
            this value will be passed to that handler which should open and return a writable stream according 
            to this file identifier.
            </param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="outputHandler">The output handler which provides a custom way of saving the output files.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Sample IOutputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.String,GleamTech.DocumentUltimate.OutputOptions,GleamTech.DocumentUltimate.IOutputHandler,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output path and options.
            </summary>
            <param name="outputFile">
            The path to the converted document file.
            If a custom output handler is provided via <paramref name="outputHandler"/> parameter, then
            this value will be passed to that handler which should open and return a writable stream according 
            to this file identifier.
            </param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file. If not specified, 
            the format will be automatically determined from the file extension in <paramref name="outputFile"/>.
            </param>
            <param name="outputHandler">The output handler which provides a custom way of saving the output files.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Sample IOutputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.IO.Stream,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output stream and format.
            For multi-page output conversions, only the first page will be saved to the stream as it would be
            useless to save all pages to the same stream. 
            Use <see cref="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.Func{System.String,GleamTech.DocumentUltimate.StreamResult},GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})"/> overload
            with output stream callback for full control over multi-page output conversions.
            </summary>
            <param name="outputStream">
            The stream to write the converted document file.
            </param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Converting to an output stream:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToStream1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToStream1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.IO.Stream,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output stream and options.
            For multi-page output conversions, only the first page will be saved to the stream as it would be
            useless to save all pages to the same stream. 
            Use <see cref="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.Func{System.String,GleamTech.DocumentUltimate.StreamResult},GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})"/> overload 
            with output stream callback for full control over multi-page output conversions.
            </summary>
            <param name="outputStream">The stream to write the converted document file.</param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Converting to an output stream:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToStream1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToStream1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.Func{System.String,GleamTech.DocumentUltimate.StreamResult},GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output stream callback and format.
            For multi-page output conversions, the callback will be called for each output file.
            </summary>
            <param name="outputStreamCallback">
            The callback method to return a writable stream for each output file. 
            </param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Converting via output stream callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToStreamCallback1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToStreamCallback1_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToStreamCallback1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToStreamCallback1_2" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.Func{System.String,GleamTech.DocumentUltimate.StreamResult},GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output stream callback and options.
            For multi-page output conversions, the callback will be called for each output file.
            </summary>
            <param name="outputStreamCallback">
            The callback method to return a writable stream for each output file. 
            </param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Converting via output stream callback:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToStreamCallback1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToStreamCallback1_1" language="vb" />
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToStreamCallback1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToStreamCallback1_2" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.Collections.Generic.Dictionary{System.String,System.IO.MemoryStream}@,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output stream callback and format.
            For multi-page output conversions, the callback will be called for each output file.
            </summary>
            <param name="outputMemoryStreams">
            A dictionary of MemoryStreams that was generated, dictonary keys are the output file names for the corresponding streams. 
            </param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Converting to memory streams:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToMemoryStreams1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToMemoryStreams1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.ConvertTo(System.Collections.Generic.Dictionary{System.String,System.IO.MemoryStream}@,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts the document file to another format with the specified output stream callback and options.
            For multi-page output conversions, the callback will be called for each output file.
            </summary>
            <param name="outputMemoryStreams">
            A dictionary of MemoryStreams that was generated, dictonary keys are the output file names for the corresponding streams. 
            </param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
            <example>
            <para>Converting to memory streams with output options:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ConvertToMemoryStreams2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ConvertToMemoryStreams2" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.CanConvert(System.String,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Gets a value that indicates whether a direct conversion is possible from one document file to another format.</summary>
            <param name="inputFile">The file name or path or extension (with or without leading dot) of the document file to convert.</param>
            <param name="outputFormat">The format to convert to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>true if a direct conversion is possible; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.CanConvert(System.String,System.String,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Gets a value that indicates whether a direct conversion is possible from one document file to another file.</summary>
            <param name="inputFile">The file name or path or extension (with or without leading dot) of the document file to convert.</param>
            <param name="outputFile">The file name or path or extension (with or without leading dot) of the converted document file.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>true if a direct conversion is possible; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.CanConvert(GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Gets a value that indicates whether a direct conversion is possible from one document format to another format.</summary>
            <param name="inputFormat">The format to convert from</param>
            <param name="outputFormat">The format to convert to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>true if a direct conversion is possible; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputFormat"/>.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputOptions"/>.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,System.String,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format with the specified output path and format.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="outputFile">The path to the converted document file.</param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,System.String,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format with the specified output path and options.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="outputFile">The path to the converted document file.</param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file. If not specified, 
            the format will be automatically determined from the file extension in <paramref name="outputFile"/>.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputFormat"/>.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputFormat">The format of the file to be converted.</param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.InputOptions,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputFormat"/>.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputOptions">
            The input options which contains format and other options for the file to be converted.
            </param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputOptions"/>.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputFormat">The format of the file to be converted.</param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.InputOptions,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format. The converted file be generated in the same folder
            as the input file and the extension will be automatically determined 
            from <paramref name="outputOptions"/>.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputOptions">
            The input options which contains format and other options for the file to be converted.
            </param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.DocumentFormat,System.String,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format with the specified output path and format.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputFormat">The format of the file to be converted.</param>
            <param name="outputFile">The path to the converted document file.</param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.InputOptions,System.String,GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format with the specified output path and format.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputOptions">
            The input options which contains format and other options for the file to be converted.
            </param>
            <param name="outputFile">The path to the converted document file.</param>
            <param name="outputFormat">The target format to convert the document file to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.DocumentFormat,System.String,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format with the specified output path and format.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputFormat">The format of the file to be converted.</param>
            <param name="outputFile">The path to the converted document file.</param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file. If not specified, 
            the format will be automatically determined from the file extension in <paramref name="outputFile"/>.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.Convert(System.String,GleamTech.DocumentUltimate.InputOptions,System.String,GleamTech.DocumentUltimate.OutputOptions,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>
            Converts a given document file to another format with the specified output path and options.
            </summary>
            <param name="inputFile">The path to the document file to convert.</param>
            <param name="inputOptions">
            The input options which contains format and other options for the file to be converted.
            </param>
            <param name="outputFile">The path to the converted document file.</param>
            <param name="outputOptions">
            The output options which contains format and other options for the converted file. If not specified, 
            the format will be automatically determined from the file extension in <paramref name="outputFile"/>.
            </param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentConverterResult" /> object that contains the result of the document conversion such as output file paths.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.EnumeratePossibleOutputFormats(System.String,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Enumerates possible output document formats for a given input document file.</summary>
            <param name="inputFile">The file name or path or extension (with or without leading dot) of the document file to convert.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>An enumerable collection of output formats.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.EnumeratePossibleOutputFormats(GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Enumerates possible output document formats for a given input document format.</summary>
            <param name="inputFormat">The format to convert from</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>An enumerable collection of output formats.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.EnumeratePossibleInputFormats(System.String,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Enumerates possible input document formats for a given output document file.</summary>
            <param name="outputFile">The file name or path or extension (with or without leading dot) of the converted document file.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>An enumerable collection of input formats.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.EnumeratePossibleInputFormats(GleamTech.DocumentUltimate.DocumentFormat,System.Nullable{GleamTech.DocumentUltimate.DocumentEngine})">
            <summary>Enumerates possible input document formats for a given output document format.</summary>
            <param name="outputFormat">The format to convert to.</param>
            <param name="engine">
            The document engine to force. If not specified, the best document engine 
            will be chosen automatically according to the input and output formats.
            </param>
            <returns>An enumerable collection of input formats.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentConverter.GetEngine(GleamTech.DocumentUltimate.DocumentFormat,GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Gets the engine that would be chosen for a conversion between an input an an output format.</summary>
            <param name="inputFormat">The format to convert from</param>
            <param name="outputFormat">The format to convert to.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.DocumentEngine"/> value if an engine is found; otherwise, null.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentConverterResult">
            <summary>Represents the result of a document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentConverterResult.OutputFiles">
            <summary>
            Gets the paths of the converted document files. 
            When there is only one output file, the value will be a single item array.
            If the output is a directory (e.g. for <see cref="F:GleamTech.DocumentUltimate.DocumentFormat.Web"/> format), the string will have
            a trailing backslash (\).
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentConverterResult.ElapsedTime">
            <summary>Gets the total elapsed time for the document conversion.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentEngine">
            <summary>Defines the document engines.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.Portable">
            <summary>Portable document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.WordProcessing">
            <summary>Word-processing document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.Spreadsheet">
            <summary>Spreadsheet document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.Presentation">
            <summary>Presentation document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.Diagram">
            <summary>Diagram document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.ProjectManagement">
            <summary>Project-management document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.Email">
            <summary>Email document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.Imaging">
            <summary>Imaging document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngine.Cad">
            <summary>Cad document engine.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentEngineFilter">
            <summary>Defines the document engine filters.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngineFilter.None">
            <summary>The document format should be not supported by any document engine.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngineFilter.All">
            <summary>The document format should be supported by all document engines.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentEngineFilter.Any">
            <summary>The document format should be supported by any document engine.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentFormat">
            <summary>Defines the supported document file formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Pdf">
            <summary>Adobe Portable Document Format (PDF).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xps">
            <summary>Microsoft XML Paper Specification (XPS).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Oxps">
            <summary>Microsoft Open XML Paper Specification (OpenXPS).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xpz">
            <summary>DocumentUltimate Web Viewer Format (XPZ).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Docx">
            <summary>Microsoft Word Document.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Docm">
            <summary>Microsoft Word Macro-Enabled Document.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dotx">
            <summary>Microsoft Word Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dotm">
            <summary>Microsoft Word Macro-Enabled Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Doc">
            <summary>Microsoft Word 97-2003 Document.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dot">
            <summary>Microsoft Word 97-2003 Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Rtf">
            <summary>Rich Text Document.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Odt">
            <summary>OpenDocument Text.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Ott">
            <summary>OpenDocument Text Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xlsx">
            <summary>Microsoft Excel Worksheet.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xlsm">
            <summary>Microsoft Excel Macro-Enabled Worksheet.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xltx">
            <summary>Microsoft Excel Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xltm">
            <summary>Microsoft Excel Macro-Enabled Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xlam">
            <summary>Microsoft Excel Macro-Enabled Add-In.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xlsb">
            <summary>Microsoft Excel Binary Worksheet.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xls">
            <summary>Microsoft Excel 97-2003 Worksheet.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xlt">
            <summary>Microsoft Excel 97-2003 Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Csv">
            <summary>Comma Separated Values File.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Tsv">
            <summary>Tab Separated Values File.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dif">
            <summary>Data Interchange Format.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Ods">
            <summary>OpenDocument Spreadsheet.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Ots">
            <summary>OpenDocument Spreadsheet Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Pptx">
            <summary>Microsoft PowerPoint Presentation.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Pptm">
            <summary>Microsoft PowerPoint Macro-Enabled Presentation.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Potx">
            <summary>Microsoft PowerPoint Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Potm">
            <summary>Microsoft PowerPoint Macro-Enabled Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Ppsx">
            <summary>Microsoft PowerPoint Slide Show.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Ppsm">
            <summary>Microsoft PowerPoint Macro-Enabled Slide Show.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Ppt">
            <summary>Microsoft PowerPoint 97-2003 Presentation.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Pps">
            <summary>Microsoft PowerPoint 97-2003 Slide Show.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Odp">
            <summary>OpenDocument Presentation.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Otp">
            <summary>OpenDocument Presentation Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vsdx">
            <summary>Microsoft Visio Drawing.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vsdm">
            <summary>Microsoft Visio Macro-Enabled Drawing.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vstx">
            <summary>Microsoft Visio Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vstm">
            <summary>Microsoft Visio Macro-Enabled Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vssx">
            <summary>Microsoft Visio Stencil.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vssm">
            <summary>Microsoft Visio Macro-Enabled Stencil.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vdx">
            <summary>Microsoft Visio 2003-2010 Drawing.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vsx">
            <summary>Microsoft Visio 2003-2010 Stencil.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vtx">
            <summary>Microsoft Visio 2003-2010 Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vsd">
            <summary>Microsoft Visio 2003-2010 Drawing.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vss">
            <summary>Microsoft Visio 2003-2010 Stencil.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vst">
            <summary>Microsoft Visio 2003-2010 Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Vdw">
            <summary>Microsoft Visio 2010 Web Drawing.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Mpp">
            <summary>Microsoft Project Document.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Mpt">
            <summary>Microsoft Project Template.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Mpx">
            <summary>Microsoft Project Exchange File.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Msg">
            <summary>Microsoft Outlook E-mail Message.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Eml">
            <summary>E-mail Message.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Emlx">
            <summary>Apple Mail E-mail File.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Epub">
            <summary>Electronic Publication (EPUB).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Html">
            <summary>HyperText Markup Language (HTML).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Mht">
            <summary>Mime HTML (MHTML).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Web">
            <summary>Web Directory (One HTML file per page).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Txt">
            <summary>Plain Text Document.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dwg">
            <summary>AutoCAD Drawing (R13 to 2018).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dxf">
            <summary>AutoCAD Drawing Interchange (R12 to 2018).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Stl">
            <summary>STL File Format (3D Printing).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Tif">
            <summary>Tagged Image File Format (TIFF).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.DjVu">
            <summary>Deja Vu (DjVu) Image.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dcm">
            <summary>Digital Imaging and Communications in Medicine (DICOM).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Ps">
            <summary>PostScript (PS).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Svg">
            <summary>Scalable Vector Graphics (SVG).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Emf">
            <summary>Windows Enhanced Metafile (EMF).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Xaml">
            <summary>Extensible Application Markup Language (XAML).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Psd">
            <summary>Adobe Photoshop Document (PSD).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Jpg">
            <summary>Joint Photographic Experts Group (JPEG).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Jp2">
            <summary>JPEG 2000 (JP2).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Jxr">
            <summary>JPEG XR (HD Photo).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Png">
            <summary>Portable Network Graphics (PNG).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Gif">
            <summary>Graphics Interchange Format (GIF).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.WebP">
            <summary>WebP Image.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Bmp">
            <summary>Bitmap Picture (BMP).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Wmf">
            <summary>Windows Metafile (WMF).</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormat.Dib">
            <summary>Device Independent Bitmap (DIB).</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentFormatGroup">
            <summary>Defines the groups for supported document file formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Portable">
            <summary>Portable Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.WordProcessing">
            <summary>Word Processing Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Spreadsheet">
            <summary>Spreadsheet Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Presentation">
            <summary>Presentation Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Diagram">
            <summary>Diagram Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.ProjectManagement">
            <summary>Project Management Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Email">
            <summary>Email Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Ebook">
            <summary>Ebook Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Markup">
            <summary>Markup Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Text">
            <summary>Text Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.Cad">
            <summary>Cad Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.ScannerImage">
            <summary>Scanner Image Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.VectorImage">
            <summary>Vector Image Formats.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatGroup.RasterImage">
            <summary>Raster Image Formats.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentFormatGroupInfo">
            <summary>Provides information about a document format group.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.Value">
            <summary>Gets the corresponding <see cref="T:GleamTech.DocumentUltimate.DocumentFormatGroup"/> enumeration value.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.Formats">
            <summary>Gets all file formats for this document format group.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.Description">
            <summary>Gets the user-friendly description of this document format group.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.EnumerateFormats(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngine)">
            <summary>Enumerates supported document formats of this group for loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engine">The document engine that should support the format.</param>
            <returns>An enumerable collection of document format infos.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.EnumerateFormats(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngineFilter)">
            <summary>Enumerates supported document formats of this group for loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engineFilter">The document engines that should support the format.</param>
            <returns>An enumerable collection of document format infos.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.ToString">
            <summary>Returns the same value as <see cref="P:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.Description"/> property.</summary>
            <returns>The user-friendly description of this document format group.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.Enumerate(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngine)">
            <summary>Enumerates supported file format groups for document loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engine">The document engine that should support the format.</param>
            <returns>An enumerable collection of document format group infos.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.Enumerate(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngineFilter)">
            <summary>Enumerates supported file format groups for document loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engineFilter">The document engines that should support the format.</param>
            <returns>An enumerable collection of document format group infos.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatGroupInfo.Get(GleamTech.DocumentUltimate.DocumentFormatGroup)">
            <summary>Gets the document format group info corresponding to the specified <see cref="T:GleamTech.DocumentUltimate.DocumentFormatGroup"/> enumeration value.</summary>
            <param name="formatGroup">The document format group.</param>
            <returns>a <see cref="T:GleamTech.DocumentUltimate.DocumentFormatGroupInfo"/> object if there is a match; otherwise, null.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentFormatInfo">
            <summary>Provides information about a document format.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatInfo.Value">
            <summary>Gets the corresponding <see cref="T:GleamTech.DocumentUltimate.DocumentFormat"/> enumeration value.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatInfo.Group">
            <summary>Gets the group of this document format.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatInfo.DefaultExtension">
            <summary>Gets the default file extension for this document format.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatInfo.Extensions">
            <summary>Gets all file extensions for this document format.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentFormatInfo.Description">
            <summary>Gets the user-friendly description of this document format.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.SupportedFor(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngine)">
            <summary>Gets a value that indicates whether this format is supported for loading or saving with the specified document engine.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engine">The document engine that should support the format.</param>
            <returns>true if supported; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.SupportedFor(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngineFilter)">
            <summary>Gets a value that indicates whether this format is supported for loading or saving with the specified document engine.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engineFilter">The document engines that should support the format.</param>
            <returns>true if supported; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.CanLoadWith(System.String)">
            <summary>Gets a value that indicates whether this format can be loaded with the specified document engine.</summary>
            <param name="engineName">The document engine name that should support the format.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.CanLoadWith(GleamTech.DocumentUltimate.DocumentEngineFilter)">
            <summary>Gets a value that indicates whether this format can be loaded with the specified document engine.</summary>
            <param name="engineFilter">The document engines that should support the format.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.CanSaveWith(System.String)">
            <summary>Gets a value that indicates whether this format can be saved with the specified document engine.</summary>
            <param name="engineName">The document engine name that should support the format.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.CanSaveWith(GleamTech.DocumentUltimate.DocumentEngineFilter)">
            <summary>Gets a value that indicates whether this format can be saved with the specified document engine.</summary>
            <param name="engineFilter">The document engines that should support the format.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.ToString">
            <summary>Returns the same value as <see cref="P:GleamTech.DocumentUltimate.DocumentFormatInfo.Description"/> property.</summary>
            <returns>The user-friendly description of this document format.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.Enumerate(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngine)">
            <summary>Enumerates supported document format infos for loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engine">The document engine that should support the format.</param>
            <returns>An enumerable collection of document format infos.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.Enumerate(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngineFilter)">
            <summary>Enumerates supported document format infos for loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engineFilter">The document engines that should support the format.</param>
            <returns>An enumerable collection of document format infos.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.EnumerateExtensions(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngine)">
            <summary> Enumerates supported document file extensions for loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engine">The document engine that should support the format.</param>
            <returns>An enumerable collection of document file extensions.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.EnumerateExtensions(GleamTech.DocumentUltimate.DocumentFormatSupport,GleamTech.DocumentUltimate.DocumentEngineFilter)">
            <summary> Enumerates supported document file extensions for loading or saving.</summary>
            <param name="formatSupport">The load or save support for the format.</param>
            <param name="engineFilter">The document engines that should support the format.</param>
            <returns>An enumerable collection of document file extensions.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.Get(System.String)">
            <summary>Gets the document format info by file name or path or extension.</summary>
            <param name="file">The file name or path or extension (with or without leading dot) of the document.</param>
            <returns>a <see cref="T:GleamTech.DocumentUltimate.DocumentFormatInfo"/> object if there is a match; otherwise, null.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentFormatInfo.Get(GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Gets the document format info corresponding to the specified <see cref="T:GleamTech.DocumentUltimate.DocumentFormat"/> enumeration value.</summary>
            <param name="format">The document format.</param>
            <returns>a <see cref="T:GleamTech.DocumentUltimate.DocumentFormatInfo"/> object if there is a match; otherwise, null.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentFormatSupport">
            <summary>Defines the document format support for loading or saving.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatSupport.None">
            <summary>The document format should be not supported for any.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatSupport.Load">
            <summary>The document format should be supported for loading.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatSupport.Save">
            <summary>The document format should be supported for saving.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatSupport.Both">
            <summary>The document format should be supported for both loading and saving.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.DocumentFormatSupport.Either">
            <summary>The document format should be supported for either loading and saving.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.DocumentUltimateConfiguration">
            <summary>
            Provides properties and methods for changing this library's configuration.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentUltimateConfiguration.CreateLicense(System.String)">
            <summary>Create a new license.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.DocumentUltimateConfiguration.InitCurrent">
            <summary>Initializes only the static Current instance.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentUltimateConfiguration.Current">
            <summary>Gets current global configuration instance.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.DocumentUltimateConfiguration.AssemblyInfo">
            <summary>Gets information on this library's assembly.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.HorizontalAlignment">
            <summary>Specifies how a watermark is horizontally aligned relative to a document page.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.HorizontalAlignment.Left">
            <summary>Default. The watermark is aligned on the left of the document page.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.HorizontalAlignment.Right">
            <summary>The watermark is aligned on the right of the document page.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.HorizontalAlignment.Center">
            <summary>The watermark is aligned in the horizontal center of the document page.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.HtmlOutputOptions">
            <summary>Represents a set of html output options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.HtmlOutputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.HtmlOutputOptions"/> class.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.HtmlOutputOptions.ExternalImages">
            <summary>
            Gets or sets a value indicating whether the images in html page should be exported 
            as separate files (externally linked) or embedded as data-URIs (as base64 encoded string).
            The default is false.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.HtmlOutputOptions.ExternalCss">
            <summary>
            Gets or sets a value indicating whether the CSS in html page should be exported 
            as separate files (externally linked) or embedded as &lt;style&gt; tags.
            The default is false.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.IInputHandler">
            <summary>
            Represents an object for handling of the input files.
            Implement this interface to provide a custom way of loading the input files.
            </summary>
            <example>
            <para>Sample IInputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomInputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomInputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomInputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomInputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.IInputHandler.OpenRead(System.String,GleamTech.DocumentUltimate.InputOptions)">
            <summary>Opens a readable stream for the current input file.</summary>
            <param name="inputFile">The input file for the current document conversion.</param>
            <param name="inputOptions">The input options that was specified for the current document conversion.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> instance initialized with a readable <see cref="T:System.IO.Stream" /> object.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.ImageOutputOptions">
            <summary>Represents a set of image output options used for document conversion.</summary>
            <example>
            <para>Increasing the resolution of the output images (default is 96 DPI):</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="ImageOutputOptions1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="ImageOutputOptions1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.ImageOutputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.ImageOutputOptions"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.ImageOutputOptions.#ctor(GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.ImageOutputOptions"/> class with a document format.</summary>
            <param name="format">The format to convert to.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.ImageOutputOptions.Resolution">
            <summary>
            Gets or sets desired resolution for the generated images, in dots per inch (DPI).
            The default value is 96 DPI.
            For example, when DPI is 96 and input document page is 8 inches wide, 
            the output image width will be 96*8 = 768 pixels.
            So the output dimensions (width and height) depends both input document dimensions and DPI.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.ImageWatermark">
            <summary>Represents a set of image watermark options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.ImageWatermark.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.ImageWatermark"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.ImageWatermark.#ctor(System.IO.Stream,System.String)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.ImageWatermark"/> class from a image stream.</summary>
            <param name="imageStream">The image stream to use as the watermark. The stream will be disposed automatically after it's used.</param>
            <param name="version">
            The version of the image stream, only useful when the watermark is used with DocumentViewer. 
            As the image source is not on disk, information like file date and size will not be available 
            for unique cache key calculation so you can make DocumentViewer know that you changed the source image
            by manually incrementing this version value. This way DocumentViewer can recognize the change, invalidate 
            the cached document and force watermarking with new image. Instead of incrementing, you can also pass 
            some kind of hash or DateTime.Ticks, of course the value should
            only change when the image is actually changed.
            </param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.ImageWatermark.#ctor(System.Drawing.Bitmap,System.String)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.ImageWatermark"/> class from a <see cref="T:System.Drawing.Bitmap"/> instance.</summary>
            <param name="bitmap">The bitmap to use as the watermark. The bitmap will be disposed automatically after it's used.</param>
            <param name="version">
            The version of the image bitmap, only useful when the watermark is used with DocumentViewer. 
            As the image source is not on disk, information like file date and size will not be available 
            for unique cache key calculation so you can make DocumentViewer know that you changed the source image
            by manually incrementing this version value. This way DocumentViewer can recognize the change, invalidate 
            the cached document and force watermarking with new image. Instead of incrementing, you can also pass 
            some kind of hash or DateTime.Ticks, of course the value should
            only change when the image is actually changed.
            </param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.ImageWatermark.ImageFile">
            <summary>Gets or sets the image file to use as the watermark.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.ImageWatermark.Version">
            <summary>
            Gets the version of image file to use as the watermark.
            This is calculated automatically for physical files.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.InputContext">
             <summary>
             Encapsulates all specific information about the input of a document conversion.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.InputContext.File">
            <summary>Gets the input file that was specified for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.InputContext.Options">
            <summary>Gets the input options that was specified for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.InputContext.Handler">
            <summary>Gets the input handler that was specified for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.InputContext.Format">
            <summary>Gets the input format that was specified or auto-detected for the current document conversion.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.InputOptions">
            <summary>Represents a set of common input options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.InputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.InputOptions"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.InputOptions.#ctor(GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.InputOptions"/> class with a document format.</summary>
            <param name="format">The format to convert from.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.InputOptions.Format">
            <summary>Gets or set the format to convert from.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.InputOptions.Password">
            <summary>Gets or sets the password which is used for loading a protected document.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.IOutputHandler">
            <summary>
            Represents an object for handling of the output files.
            Implement this interface to provide a custom way of saving the output files.
            </summary>
            <example>
            <para>Sample IOutputHandler implementation for loading documents:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_2" language="vb"/>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="CustomOutputHandler1_1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="CustomOutputHandler1_1" language="vb"/>
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.IOutputHandler.OpenWrite(System.String,GleamTech.DocumentUltimate.ConversionContext)">
            <summary>Opens a writable stream for the current output file.</summary>
            <param name="currentOutputFile">The current output file. For multi-page output conversions, this wil be pre-formatted with current page number (e.g. Document-01.jpg).</param>
            <param name="conversionContext">The context which contains useful information for current document conversion.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> instance initialized with a writable <see cref="T:System.IO.Stream" /> object.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.ISingleOutputHandler">
            <summary>
            Represents an object for handling of a single output file. 
            Implement this interface to provide a custom way of saving an output file when multi-page output 
            conversions can not be supported. Only the first page will be saved when you use this interface.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.MemoryInputOutputHandler">
            <summary>
            Creates an input and output handler whose backing store is memory.
            This class can be used for in-memory conversions, e.g. you can convert to a document format in memory and 
            then convert from that document format to another via using this handler.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.MemoryInputOutputHandler.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.MemoryInputOutputHandler"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.MemoryInputOutputHandler.OpenRead(System.String,GleamTech.DocumentUltimate.InputOptions)">
            <summary>Opens a readable stream for the current input file.</summary>
            <param name="inputFile">The input file for the current document conversion.</param>
            <param name="inputOptions">The input options that was specified for the current document conversion.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> instance initialized with a readable <see cref="T:System.IO.Stream" /> object.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.MemoryInputOutputHandler.OpenWrite(System.String,GleamTech.DocumentUltimate.ConversionContext)">
            <summary>Opens a writable stream for the current output file.</summary>
            <param name="currentOutputFile">The current output file. For multi-page output conversions, this wil be pre-formatted with current page number (e.g. Document-01.jpg).</param>
            <param name="conversionContext">The context which contains useful information for current document conversion.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> instance initialized with a writable <see cref="T:System.IO.Stream" /> object.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.MemoryInputOutputHandler.Dispose">
            <summary>Releases all resources used by this instance.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.NamespaceDoc">
            <summary>
            This is the core namespace of this library, it contains classes used to convert document files
            to another format.
            <para />
            <para>The main classes in this namespace are:</para>
            <list type="bullet">
                <item><see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/> is the class for converting between document file formats.</item>
                <item><see cref="T:GleamTech.DocumentUltimate.DocumentFormat"/> defines the supported file formats for document conversion.</item>
                <item><see cref="T:GleamTech.DocumentUltimate.InputOptions"/> is the base class for input options used for document conversion.</item>
                <item><see cref="T:GleamTech.DocumentUltimate.OutputOptions"/> is the base class for output options used for document conversion.</item>
                <item><see cref="T:GleamTech.DocumentUltimate.DocumentUltimateConfiguration"/> is the class for changing this library's configuration (e.g. setting license key).</item>
            </list>
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.NumberRange">
            <summary>Represents a range of numbers.</summary>
            <remarks>
            <para>The range is specified as a string in the following forms or combination thereof:</para>
            <list type="table">
            <item><term>5</term><description>single value</description></item>
            <item><term>1,2,3,4,5</term><description>sequence of values</description></item>
            <item><term>1-5</term><description>closed range</description></item>
            <item><term>-5</term><description>open range (converted to a sequence from 1 to 5)</description></item>
            <item><term>1-</term><description>open range (converted to a sequence from 1 to final number)</description></item>
            <item><term>all</term><description>keyword representing all numbers.</description></item>
            <item><term>odd</term><description>keyword representing odd numbers.</description></item>
            <item><term>even</term><description>keyword representing even numbers.</description></item>
            </list>
            <para>The value delimiter can be either ',' or ';' and the range separator can be
            either '-' or ':'. Whitespace is permitted at any point in the input.</para>
            <para>Any elements of the sequence that contain non-digit, non-whitespace, or non-separator
            characters or that are empty are ignored.</para>
            </remarks>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.NumberRange"/> class from the specified string.
            </summary>
            <param name="rangeString">The string containing the range of numbers.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.Add(System.Int32)">
            <summary>Adds a single number to the sequence.</summary>
            <param name="singleNumber">The single number.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.Add(System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>Adds a range of numbers to the sequence. Reverse ordering is accepted.</summary>
            <param name="startNumber">The start number of the range. If value is null, range is considered an open range from 1 to <paramref name="endNumber"/>.</param>
            <param name="endNumber">The end number of the range. If value is null, range is considered an open range from <paramref name="startNumber"/> to final number.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.Add(System.String)">
            <summary>Adds a range of numbers to the sequence from a string representation (e.g. 1-5, 8, 11-13).</summary>
            <param name="rangeString">The string containing the range of numbers.</param>
            <remarks>
            <para>The range is specified as a string in the following forms or combination thereof:</para>
            <list type="table">
            <item><term>5</term><description>single value</description></item>
            <item><term>1,2,3,4,5</term><description>sequence of values</description></item>
            <item><term>1-5</term><description>closed range</description></item>
            <item><term>-5</term><description>open range (converted to a sequence from 1 to 5)</description></item>
            <item><term>1-</term><description>open range (converted to a sequence from 1 to final number)</description></item>
            <item><term>all</term><description>keyword representing all numbers.</description></item>
            <item><term>odd</term><description>keyword representing odd numbers.</description></item>
            <item><term>even</term><description>keyword representing even numbers.</description></item>
            </list>
            <para>The value delimiter can be either ',' or ';' and the range separator can be
            either '-' or ':'. Whitespace is permitted at any point in the input.</para>
            <para>Any elements of the sequence that contain non-digit, non-whitespace, or non-separator
            characters or that are empty are ignored.</para>
            </remarks>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.Contains(System.String)">
            <summary>
            Determines whether this instance contains the specified range part (number range or keyword).
            </summary>
            <param name="rangePart">The range part (number range or keyword) to check.</param>
            <returns>true if the instance contains the specified range part; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.Enumerate(System.Int32)">
            <summary>Enumerates all the numbers that this range instance describes. The sequence will be distinct and in ascending order.</summary>
            <param name="finalNumber">The final number which is used as the maximum value for open range specifier.</param>
            <returns>An enumerable sequence of integers. The sequence will be distinct and in ascending order</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.MatchKeyword(System.String)">
            <summary>
            Matches a supported keyword. It can be overriden in derived classed to support additional keywords.
            </summary>
            <param name="keyword">The lower case keyword string to match.</param>
            <returns>true if keyword is supported; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.ExpandKeyword(System.String,System.Int32)">
            <summary>
            Expands a supported keyword to an enumerable sequence of numbers. It can be overriden in derived classed to support additional keywords.
            </summary>
            <param name="keyword">The lower case keyword string to expand.</param>
            <param name="finalNumber">The final number which is used as the maximum value for open range specifier.</param>
            <returns>An enumerable sequence of integers.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.NumberRange.ToString">
            <summary>Converts this instance to a string containing the range of numbers.</summary>
            <returns>A string representation of the range of numbers.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.OutputContext">
             <summary>
             Encapsulates all specific information about the output of a document conversion.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.OutputContext.File">
            <summary>Gets the output file that was specified for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.OutputContext.Options">
            <summary>Gets the output options that was specified for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.OutputContext.Handler">
            <summary>Gets the output handler that was specified for the current document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.OutputContext.Format">
            <summary>Gets the output format that was specified or auto-detected for the current document conversion.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.OutputOptions">
            <summary>Represents a set of common output options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.OutputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.OutputOptions"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.OutputOptions.#ctor(GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.OutputOptions"/> class with a document format.</summary>
            <param name="format">The format to convert to.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.OutputOptions.Format">
            <summary>Gets or sets the format to convert to.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.PageRange">
            <summary>Represents a range of page numbers.</summary>
            <remarks>
            <para>The range is specified as a string in the following forms or combination thereof:</para>
            <list type="table">
            <item><term>5</term><description>single value</description></item>
            <item><term>1,2,3,4,5</term><description>sequence of values</description></item>
            <item><term>1-5</term><description>closed range</description></item>
            <item><term>-5</term><description>open range (converted to a sequence from 1 to 5)</description></item>
            <item><term>1-</term><description>open range (converted to a sequence from 1 to final page)</description></item>
            <item><term>all</term><description>keyword representing all page numbers.</description></item>
            <item><term>odd</term><description>keyword representing odd page numbers.</description></item>
            <item><term>even</term><description>keyword representing even page numbers.</description></item>
            </list>
            <para>The value delimiter can be either ',' or ';' and the range separator can be
            either '-' or ':'. Whitespace is permitted at any point in the input.</para>
            <para>Any elements of the sequence that contain non-digit, non-whitespace, or non-separator
            characters or that are empty are ignored.</para>
            </remarks>
            <example>
            <para>Creating a PageRange instance:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="PageRange1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="PageRange1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.PageRange"/> class from the specified string.
            </summary>
            <param name="pageRangeString">The string containing the range of page numbers.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.Add(System.Int32)">
            <summary>Adds a single page number to the sequence.</summary>
            <param name="singlePage">The page number.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.Add(System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>Adds a range of page numbers to the sequence. Reverse ordering is accepted.</summary>
            <param name="startPage">The start page number of the range. If value is null, range is considered an open range from 1 to <paramref name="endPage"/>.</param>
            <param name="endPage">The end page number of the range. If value is null, range is considered an open range from <paramref name="startPage"/> to final page.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.Add(System.String)">
            <summary>Adds a range of page numbers to the sequence from a string representation (e.g. 1-5, 8, 11-13).</summary>
            <param name="pageRangeString">The string containing the range of page numbers.</param>
            <remarks>
            <para>The range is specified as a string in the following forms or combination thereof:</para>
            <list type="table">
            <item><term>5</term><description>single value</description></item>
            <item><term>1,2,3,4,5</term><description>sequence of values</description></item>
            <item><term>1-5</term><description>closed range</description></item>
            <item><term>-5</term><description>open range (converted to a sequence from 1 to 5)</description></item>
            <item><term>1-</term><description>open range (converted to a sequence from 1 to final page)</description></item>
            <item><term>all</term><description>keyword representing all page numbers.</description></item>
            <item><term>odd</term><description>keyword representing odd page numbers.</description></item>
            <item><term>even</term><description>keyword representing even page numbers.</description></item>
            </list>
            <para>The value delimiter can be either ',' or ';' and the range separator can be
            either '-' or ':'. Whitespace is permitted at any point in the input.</para>
            <para>Any elements of the sequence that contain non-digit, non-whitespace, or non-separator
            characters or that are empty are ignored.</para>
            </remarks>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.Enumerate(System.Int32)">
            <summary>Enumerates all the page numbers that this range instance describes. The sequence will be distinct and in ascending order.</summary>
            <param name="finalPageNumber">The final page number which is used as the maximum value for open range specifier.</param>
            <returns>An enumerable sequence of integers. The sequence will be distinct and in ascending order</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.ToString">
            <summary>Converts this instance to a string containing the range of page numbers.</summary>
            <returns>A string representation of the range of page numbers.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.op_Implicit(System.String)~GleamTech.DocumentUltimate.PageRange">
            <summary>
            Converts a string containing the range of page numbers to a <see cref="T:GleamTech.DocumentUltimate.PageRange"/>.
            </summary>
            <param name="value">A <see cref="T:System.String"/> that contains the range of page numbers.</param>
            <returns>A new <see cref="T:GleamTech.DocumentUltimate.PageRange"/> instance.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.op_Implicit(GleamTech.DocumentUltimate.PageRange)~System.String">
            <summary>
            Converts a <see cref="T:GleamTech.DocumentUltimate.PageRange"/> to a string containing the range of page numbers.
            </summary>
            <param name="value">A <see cref="T:GleamTech.DocumentUltimate.PageRange"/> instance.</param>
            <returns>A string representation of the range of page numbers.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PageRange.Parse(System.String)">
            <summary>
            Converts a string containing the range of page numbers to a <see cref="T:GleamTech.DocumentUltimate.PageRange"/>.
            </summary>
            <param name="value">A <see cref="T:System.String"/> that contains the range of page numbers.</param>
            <returns>A new <see cref="T:GleamTech.DocumentUltimate.PageRange"/> instance.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.PdfOutputOptions">
            <summary>Represents a set of PDF output options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.PdfOutputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.PdfOutputOptions"/> class.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.PdfOutputOptions.Watermarks">
            <summary>Gets or sets the watermarks to apply for the document pages.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.PdfOutputOptions.FastWebViewEnabled">
            <summary>
            Gets or sets a value that specifies whether output should be linearized for fast viewing over the web.
            The default is false. 
            Linearized PDF output (fast web view) has a big advantage when PDFs will be created and placed on 
            a web server as a file. The first page of the PDF displays more quickly because the entire PDF does 
            not need to be downloaded before it can be displayed. Pages can also be requested individually as 
            they are needed while navigating through the document. Linearized PDFs are structured for optimized
            viewing, meaning the reader will have to do less work to display their content.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.PdfOutputOptions.Password">
            <summary>Gets or sets the password which is used for saving a protected document.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.Resources.AssemblyResourceLocator">
            <summary>Dummy type for locating the root namespace of the assembly resources.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.SheetRange">
            <summary>Represents a range of sheet numbers.</summary>
            <remarks>
            <para>The range is specified as a string in the following forms or combination thereof:</para>
            <list type="table">
            <item><term>5</term><description>single value</description></item>
            <item><term>1,2,3,4,5</term><description>sequence of values</description></item>
            <item><term>1-5</term><description>closed range</description></item>
            <item><term>-5</term><description>open range (converted to a sequence from 1 to 5)</description></item>
            <item><term>1-</term><description>open range (converted to a sequence from 1 to final sheet)</description></item>
            <item><term>all</term><description>keyword representing all sheet numbers.</description></item>
            <item><term>odd</term><description>keyword representing odd sheet numbers.</description></item>
            <item><term>even</term><description>keyword representing even sheet numbers.</description></item>
            <item><term>active</term><description>keyword representing the active sheet number.</description></item>
            <item><term>visible</term><description>keyword representing visible sheet numbers.</description></item>
            </list>
            <para>The value delimiter can be either ',' or ';' and the range separator can be
            either '-' or ':'. Whitespace is permitted at any point in the input.</para>
            <para>Any elements of the sequence that contain non-digit, non-whitespace, or non-separator
            characters or that are empty are ignored.</para>
            </remarks>
            <example>
            <para>Creating a SheetRange instance:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="SheetRange1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="SheetRange1" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.SheetRange"/> class from the specified string.
            </summary>
            <param name="sheetRangeString">The string containing the range of sheet numbers.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.Add(System.Int32)">
            <summary>Adds a single sheet number to the sequence.</summary>
            <param name="singleSheet">The sheet number.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.Add(System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>Adds a range of sheet numbers to the sequence. Reverse ordering is accepted.</summary>
            <param name="startSheet">The start sheet number of the range. If value is null, range is considered an open range from 1 to <paramref name="endSheet"/>.</param>
            <param name="endSheet">The end sheet number of the range. If value is null, range is considered an open range from <paramref name="startSheet"/> to final sheet.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.Add(System.String)">
            <summary>Adds a range of sheet numbers to the sequence from a string representation (e.g. 1-5, 8, 11-13).</summary>
            <param name="sheetRangeString">The string containing the range of sheet numbers.</param>
            <remarks>
            <para>The range is specified as a string in the following forms or combination thereof:</para>
            <list type="table">
            <item><term>5</term><description>single value</description></item>
            <item><term>1,2,3,4,5</term><description>sequence of values</description></item>
            <item><term>1-5</term><description>closed range</description></item>
            <item><term>-5</term><description>open range (converted to a sequence from 1 to 5)</description></item>
            <item><term>1-</term><description>open range (converted to a sequence from 1 to final sheet)</description></item>
            <item><term>all</term><description>keyword representing all sheet numbers.</description></item>
            <item><term>odd</term><description>keyword representing odd sheet numbers.</description></item>
            <item><term>even</term><description>keyword representing even sheet numbers.</description></item>
            <item><term>active</term><description>keyword representing the active sheet number.</description></item>
            <item><term>visible</term><description>keyword representing visible sheet numbers.</description></item>
            </list>
            <para>The value delimiter can be either ',' or ';' and the range separator can be
            either '-' or ':'. Whitespace is permitted at any point in the input.</para>
            <para>Any elements of the sequence that contain non-digit, non-whitespace, or non-separator
            characters or that are empty are ignored.</para>
            </remarks>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.Enumerate(System.Int32)">
            <summary>Enumerates all the sheet numbers that this range instance describes. The sequence will be distinct and in ascending order.</summary>
            <param name="finalSheetNumber">The final sheet number which is used as the maximum value for open range specifier.</param>
            <returns>An enumerable sequence of integers. The sequence will be distinct and in ascending order</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.MatchKeyword(System.String)">
            <summary>
            Matches a supported keyword. It can be overriden in derived classed to support additional keywords.
            </summary>
            <param name="keyword">The lower case keyword string to match.</param>
            <returns>true if keyword is supported; otherwise, false.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.ToString">
            <summary>Converts this instance to a string containing the range of sheet numbers.</summary>
            <returns>A string representation of the range of sheet numbers.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.op_Implicit(System.String)~GleamTech.DocumentUltimate.SheetRange">
            <summary>
            Converts a string containing the range of sheet numbers to a <see cref="T:GleamTech.DocumentUltimate.SheetRange"/>.
            </summary>
            <param name="value">A <see cref="T:System.String"/> that contains the range of sheet numbers.</param>
            <returns>A new <see cref="T:GleamTech.DocumentUltimate.SheetRange"/> instance.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.op_Implicit(GleamTech.DocumentUltimate.SheetRange)~System.String">
            <summary>
            Converts a <see cref="T:GleamTech.DocumentUltimate.SheetRange"/> to a string containing the range of sheet numbers.
            </summary>
            <param name="value">A <see cref="T:GleamTech.DocumentUltimate.SheetRange"/> instance.</param>
            <returns>A string representation of the range of sheet numbers.</returns>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SheetRange.Parse(System.String)">
            <summary>
            Converts a string containing the range of sheet numbers to a <see cref="T:GleamTech.DocumentUltimate.SheetRange"/>.
            </summary>
            <param name="value">A <see cref="T:System.String"/> that contains the range of sheet numbers.</param>
            <returns>A new <see cref="T:GleamTech.DocumentUltimate.SheetRange"/> instance.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.SpreadsheetInputOptions">
            <summary>Represents a set of Spreadsheet input options used for document conversion.</summary>
            <example>
            <para>Choosing only the visible sheets for output and preventing breaking of columns:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="SpreadsheetInputOptions1" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="SpreadsheetInputOptions1" language="vb" />
            <para />
            <para>Choosing only the active sheet for output and fitting all rows in a predefined page count:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="SpreadsheetInputOptions2" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="SpreadsheetInputOptions2" language="vb" />
            <para />
            <para>Choosing specific sheets for output:</para>
            <code source="LibraryCS\DocumentUltimate\DocumentConverterExamples.cs" region="SpreadsheetInputOptions3" language="cs" />
            <code source="LibraryVB\DocumentUltimate\DocumentConverterExamples.vb" region="SpreadsheetInputOptions3" language="vb" />
            </example>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SpreadsheetInputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.SpreadsheetInputOptions"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.SpreadsheetInputOptions.#ctor(GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.SpreadsheetInputOptions"/> class with a document format.</summary>
            <param name="format">The format to convert from.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.OutputSheetRange">
            <summary>
            Gets or sets the range of sheet numbers to output.
            Default is "all".
            A string representation of number ranges (e.g. 1-5, 8, 11-13) or keywords (all, even, odd, visible, active)
            can be directly assigned to this property.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderColumnsFitToPages">
            <summary>
            Gets or sets the number of pages the columns should fit when it's rendered.
            The default is 0 which means columns could be broken into multiple pages if required.
            For example if you set the value 1, all columns would be rendered on a single page.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderRowsFitToPages">
            <summary>
            Gets or sets the number of pages the rows should fit when it's rendered.
            The default is 0 which means rows could be broken into multiple pages if required.
            For example if you set the value 1, all rows would be rendered on a single page.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderHeadings">
            <summary>
            Gets or sets a value that specifies whether column (e.g. A, B) and row (e.g. 1, 2) headings are rendered on pages.
            The default is false. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderGridlines">
            <summary>
            Gets or sets a value that specifies whether cell gridlines are rendered on pages.
            The default is false. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderTitleColumns">
            <summary>
            Gets or sets the columns that contain the cells to be repeated on the left side of each page.
            For example "$A:$A" specifies first column only and "$A:$C" specifies first, second and third columns.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderTitleRows">
            <summary>
            Gets or sets the rows that contain the cells to be repeated at the top of each page.
            For example "$1:$1" specifies first row only and "$1:$3" specifies first, second and third rows.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderArea">
            <summary>
            Gets or sets the range to be rendered.
            For example "A1:K15" specifies a range of A to K columns and 1 to 15 rows.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderBlackAndWhite">
            <summary>
            Gets or sets a value that specifies whether the document will be rendered in black and white.
            The default is false. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderCenterHorizontally">
            <summary>
            Gets or sets a value that specifies whether the sheet is centered horizontally when rendered.
            The default is false. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderCenterVertically">
            <summary>
            Gets or sets a value that specifies whether the sheet is centered vertically when rendered.
            The default is false. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderLeftHeader">
            <summary>
            Gets or sets the left header to be rendered. See remarks in docs for possible script and format codes.
            </summary>
            <remarks>
            <para>The following special script and format codes can be included as a part of the header and footer properties:</para>
            <list type="table">
               <listheader>
                   <term>Script code</term>
                   <description>Description</description>
               </listheader>
               <item>
                   <term>&amp;D</term>
                   <description>Prints the current date.</description>
               </item>
               <item>
                   <term>&amp;T</term>
                   <description>Prints the current time.</description>
               </item>
               <item>
                   <term>&amp;F</term>
                   <description>Prints the name of the document.</description>
               </item>
               <item>
                   <term>&amp;A</term>
                   <description>Prints the name of the workbook tab (worksheet name).</description>
               </item>
               <item>
                   <term>&amp;P</term>
                   <description>Prints the page number.</description>
               </item>
               <item>
                   <term>&amp;P+number</term>
                   <description>Prints the page number plus the specified number. E.g. &amp;P+1</description>
               </item>
               <item>
                   <term>&amp;P-number</term>
                   <description>Prints the page number minus the specified number.E.g. &amp;P-1</description>
               </item>
               <item>
                   <term>&amp;&amp;</term>
                   <description>Prints a single ampersand.</description>
               </item>
               <item>
                   <term>&amp;N</term>
                   <description>Prints the total number of pages in the document.</description>
               </item>
               <item>
                   <term>&amp;Z</term>
                   <description>Prints the file path.</description>
               </item>
            </list>
            <list type="table">
               <listheader>
                   <term>Format code</term>
                   <description>Description</description>
               </listheader>
               <item>
                   <term>&amp;E</term>
                   <description>Turns double-underline printing on or off.</description>
               </item>
               <item>
                   <term>&amp;X</term>
                   <description>Turns superscript printing on or off.</description>
               </item>
               <item>
                   <term>&amp;Y</term>
                   <description>Turns subscript printing on or off.</description>
               </item>
               <item>
                   <term>&amp;B</term>
                   <description>Turns bold printing on or off.</description>
               </item>
               <item>
                   <term>&amp;I</term>
                   <description>Turns italic printing on or off.</description>
               </item>
               <item>
                   <term>&amp;U</term>
                   <description>Turns underline printing on or off.</description>
               </item>
               <item>
                   <term>&amp;S</term>
                   <description>Turns strike-through printing on or off.</description>
               </item>
               <item>
                   <term>&amp;"font-name"</term>
                   <description>Prints the characters that follow in the specified font. Be sure to include the double quotation marks. E.g. &amp;"Arial", &amp;"Arial,Bold"</description>
               </item>
               <item>
                   <term>&amp;nn</term>
                   <description>Prints the characters that follow in the specified font size. Use a two-digit number to specify a size in points. E.g. &amp;14</description>
               </item>
               <item>
                   <term>&amp;color</term>
                   <description>Prints the characters in the specified color. User supplies a hexadecimal color value. E.g. &amp;FF0000 (red)</description>
               </item>
            </list>
            </remarks>
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderCenterHeader">
            <summary>
            Gets or sets the center header to be rendered. See remarks in docs for possible script and format codes.
            </summary>
            <inheritdoc cref="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderLeftHeader" select="remarks" />
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderRightHeader">
            <summary>
            Gets or sets the right header to be rendered. See remarks in docs for possible script and format codes.
            </summary>
            <inheritdoc cref="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderLeftHeader" select="remarks" />
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderLeftFooter">
            <summary>
            Gets or sets the left footer to be rendered. See remarks in docs for possible script and format codes.
            </summary>
            <inheritdoc cref="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderLeftHeader" select="remarks" />
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderCenterFooter">
            <summary>
            Gets or sets the center footer to be rendered. See remarks in docs for possible script and format codes.
            </summary>
            <inheritdoc cref="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderLeftHeader" select="remarks" />
        </member>
        <member name="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderRightFooter">
            <summary>
            Gets or sets the right footer to be rendered. See remarks in docs for possible script and format codes.
            </summary>
            <inheritdoc cref="P:GleamTech.DocumentUltimate.SpreadsheetInputOptions.RenderLeftHeader" select="remarks" />
        </member>
        <member name="T:GleamTech.DocumentUltimate.StreamInputHandler">
            <summary>
            Creates an input handler whose backing store is a specific stream.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.StreamInputHandler.#ctor(System.IO.Stream)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.StreamInputHandler"/> class.</summary>
            <param name="stream">The input stream.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.StreamInputHandler.OpenRead(System.String,GleamTech.DocumentUltimate.InputOptions)">
            <summary>Opens a readable stream for the current input file.</summary>
            <param name="inputFile">The input file for the current document conversion.</param>
            <param name="inputOptions">The input options that was specified for the current document conversion.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> instance initialized with a readable <see cref="T:System.IO.Stream" /> object.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.StreamResult">
            <summary>Represents the result of a stream opening method either for reading or writing.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.StreamResult.#ctor(System.IO.Stream,System.Boolean,System.Boolean)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> class with a <see cref="T:System.IO.Stream"/> instance.</summary>
            <param name="stream">The stream to read from or write to.</param>
            <param name="leaveOpen">Whether to leave the stream open after reading or writing is complete. The default is false.</param>
            <param name="rewindAfterUse">
            Whether to seek stream to beginning after being used. 
            This is usually useful for streams where you write to and then read from, such as a MemoryStream.
            Effective only when <paramref name="leaveOpen"/> is also true.
            </param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.StreamResult.Stream">
            <summary>
            Gets the stream to read from or write to.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.StreamResult.LeaveOpen">
            <summary>
            Gets a value that specifies whether to leave the stream open after reading or writing is complete.
            The default is false. 
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.StreamSingleOutputHandler">
            <summary>
            Creates an output handler whose backing store is a specific stream.
            </summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.StreamSingleOutputHandler.#ctor(System.IO.Stream)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.StreamSingleOutputHandler"/> class.</summary>
            <param name="stream">The output stream.</param>
        </member>
        <member name="M:GleamTech.DocumentUltimate.StreamSingleOutputHandler.OpenWrite(System.String,GleamTech.DocumentUltimate.ConversionContext)">
            <summary>Opens a writable stream for the current output file.</summary>
            <param name="currentOutputFile">The current output file. For multi-page output conversions, this wil be pre-formatted with current page number (e.g. Document-01.jpg).</param>
            <param name="conversionContext">The context which contains useful information for current document conversion.</param>
            <returns>A <see cref="T:GleamTech.DocumentUltimate.StreamResult"/> instance initialized with a writable <see cref="T:System.IO.Stream" /> object.</returns>
        </member>
        <member name="T:GleamTech.DocumentUltimate.TextAlignment">
            <summary>Specifies how the text is horizontally aligned relative to the watermark rectangle.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.TextAlignment.Left">
            <summary>Default. The text is aligned to the left of the watermark rectangle.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.TextAlignment.Right">
            <summary>The text is aligned to the right of the watermark rectangle.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.TextAlignment.Center">
            <summary>The text is aligned in the horizontal center of the watermark rectangle.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.TextWatermark">
            <summary>Represents a set of text watermark options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.TextWatermark.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.TextWatermark"/> class.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.TextWatermark.Text">
            <summary>Gets or sets the text to use as the watermark.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.TextWatermark.TextAlignment">
            <summary>Gets or sets a value indicating how the text is horizontally aligned relative to the watermark rectangle.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.TextWatermark.Font">
            <summary>Gets or sets the font to use for the text watermark.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.TextWatermark.FontColor">
            <summary>Gets or sets the font color to use for the text watermark.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.TextWatermark.UseFontSize">
            <summary>
            Gets or sets a value indicating whether to use the font size from <see cref="P:GleamTech.DocumentUltimate.TextWatermark.Font"/> property
            instead of <see cref="P:GleamTech.DocumentUltimate.Watermark.Width"/> and <see cref="P:GleamTech.DocumentUltimate.Watermark.Height"/> values.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.VerticalAlignment">
            <summary>Specifies how a watermark is vertically aligned relative to a document page.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.VerticalAlignment.Top">
            <summary>Default. The watermark is aligned on the top of the document page.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.VerticalAlignment.Bottom">
            <summary>The watermark is aligned on the bottom of the document page.</summary>
        </member>
        <member name="F:GleamTech.DocumentUltimate.VerticalAlignment.Center">
            <summary>The watermark is aligned in the vertical center of the document page.</summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.Watermark">
            <summary>Represents a set of watermark options used for document conversion.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.HorizontalAlignment">
            <summary>Gets or sets a value indicating how the watermark is horizontally aligned relative to a document page.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.VerticalAlignment">
            <summary>Gets or sets a value indicating how the watermark is vertically aligned relative to a document page.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.HorizontalDistance">
            <summary>
            Gets or sets the horizontal distance from left, right or center of document page; in points 
            or in percentage (e.g. 50 is 50% of the page width) if <see cref="P:GleamTech.DocumentUltimate.Watermark.DistanceIsPercentage"/> 
            is set to true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.VerticalDistance">
            <summary>
            Gets or sets the vertical distance from top, bottom or center of document page; in points 
            or in percentage (e.g. 50 is 50% of the page height) if <see cref="P:GleamTech.DocumentUltimate.Watermark.DistanceIsPercentage"/> 
            is set to true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.DistanceIsPercentage">
            <summary>
            Gets or sets a value indicating how to handle <see cref="P:GleamTech.DocumentUltimate.Watermark.HorizontalDistance"/> and <see cref="P:GleamTech.DocumentUltimate.Watermark.VerticalDistance"/> values.
            If true, <see cref="P:GleamTech.DocumentUltimate.Watermark.HorizontalDistance"/> is a percentage of the page width 
            (e.g. 50 is 50% of the page width) 
            and <see cref="P:GleamTech.DocumentUltimate.Watermark.VerticalDistance"/> is a percentage of the page height 
            (e.g. 50 is 50% of the page height). 
            If false, <see cref="P:GleamTech.DocumentUltimate.Watermark.HorizontalDistance"/> and <see cref="P:GleamTech.DocumentUltimate.Watermark.VerticalDistance"/> are measured in points. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.Width">
            <summary>
            Gets or sets the width of the watermark; in points 
            or in percentage (e.g. 50 is 50% of the page width) if <see cref="P:GleamTech.DocumentUltimate.Watermark.SizeIsPercentage"/> 
            is set to true. If also <see cref="P:GleamTech.DocumentUltimate.Watermark.Height"/> is specified, the resulting width may be smaller than 
            the given one to maintain the aspect ratio.
            For text watermaks, only percentage values are accepted after setting <see cref="P:GleamTech.DocumentUltimate.Watermark.SizeIsPercentage"/> to true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.Height">
            <summary>
            Gets or sets the height of the watermark; in points 
            or in percentage (e.g. 50 is 50% of the page height) if <see cref="P:GleamTech.DocumentUltimate.Watermark.SizeIsPercentage"/> 
            is set to true. If also <see cref="P:GleamTech.DocumentUltimate.Watermark.Width"/> is specified, the resulting height may be smaller than 
            the given one to maintain the aspect ratio.
            For text watermaks, only percentage values are accepted after setting <see cref="P:GleamTech.DocumentUltimate.Watermark.SizeIsPercentage"/> to true.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.SizeIsPercentage">
            <summary>
            Gets or sets a value indicating how to handle <see cref="P:GleamTech.DocumentUltimate.Watermark.Width"/> and <see cref="P:GleamTech.DocumentUltimate.Watermark.Height"/> values.
            If true, <see cref="P:GleamTech.DocumentUltimate.Watermark.Width"/> is a percentage of the page width 
            (e.g. 50 is 50% of the page width) 
            and <see cref="P:GleamTech.DocumentUltimate.Watermark.Height"/> is a percentage of the page height 
            (e.g. 50 is 50% of the page height). 
            If false, <see cref="P:GleamTech.DocumentUltimate.Watermark.Width"/> and <see cref="P:GleamTech.DocumentUltimate.Watermark.Height"/> are measured in points. 
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.Rotation">
            <summary>Gets or sets the rotation angle in degrees of the watermark.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.Opacity">
            <summary>
            Gets or sets the opacity of the watermark. 
            Default is 100.
            Values are between 0 and 100. Value 0 is invisible, value 100 is fully visible.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.InBackground">
            <summary>
            Gets or sets a value indicating if the watermark should be added as a background layer to a document page.
            Default is false.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.AnnotationMode">
            <summary>
            Gets or sets a value indicating if the watermark should be added as an annotation to a document page.
            Default is false.
            <see cref="P:GleamTech.DocumentUltimate.Watermark.InBackground"/> will not work with annotation mode.
            Note that enabling this property is currently not supported for <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> but 
            it can be enabled when generating PDF documents directly via <see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.PrintVisible">
            <summary>
            Gets or sets a value indicating if the watermark should be displayed when printed.
            Default is true.
            Note that disabling this property is currently not supported for <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> but 
            it can be disabled when generating PDF documents directly via <see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.ScreenVisible">
            <summary>
            Gets or sets a value indicating if the watermark should be displayed on screen.
            Default is true.
            Note that disabling this property is currently not supported for <see cref="T:GleamTech.DocumentUltimate.AspNet.UI.DocumentViewer"/> but 
            it can be disabled when generating PDF documents directly via <see cref="T:GleamTech.DocumentUltimate.DocumentConverter"/>.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.Watermark.PageRange">
            <summary>
            Gets or sets the range of page numbers to add the watermark to.
            Default is "all".
            A string representation of number ranges (e.g. 1-5, 8, 11-13) or keywords (all, even, odd)
            can be directly assigned to this property.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.WordProcessingInputOptions">
            <summary>Represents a set of Word Processing input options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.WordProcessingInputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.WordProcessingInputOptions"/> class.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.WordProcessingInputOptions.#ctor(GleamTech.DocumentUltimate.DocumentFormat)">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.WordProcessingInputOptions"/> class with a document format.</summary>
            <param name="format">The format to convert from.</param>
        </member>
        <member name="P:GleamTech.DocumentUltimate.WordProcessingInputOptions.FieldFileName">
            <summary>
            Gets or sets the value to be used for the FileName field in a Word document.
            When the input is a file on disk, this field is automatically updated to the original file name when saved.
            However when the input is a stream, the file name will not be known so you can set this
            property to make sure FileName field contains the correct file name.
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.WordProcessingInputOptions.UpdateFields">
            <summary>
            Gets or sets a value indicating if fields should be updated before the document is rendered/converted.
            The default is true.
            When set to true, it will mimic default "Save as PDF" or "Print" behaviour in Microsoft Word.
            For example, if you see "Error! Reference Source Not Found" messages instead of clickable links
            in the rendered document, you can set this to false (until you actually fix reference errors in your document).
            </summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.WordProcessingInputOptions.Encoding">
            <summary>
            Gets or sets the encoding that will be used to load an <see cref="F:GleamTech.DocumentUltimate.DocumentFormat.Html"/> or <see cref="F:GleamTech.DocumentUltimate.DocumentFormat.Txt"/>
            document if the encoding is not specified in the file. If encoding is not specified in HTML/TXT and this property is null,
            then the converter will try to automatically detect the encoding.
            The default is null.
            </summary>
        </member>
        <member name="T:GleamTech.DocumentUltimate.XpzOutputOptions">
            <summary>Represents a set of XPZ output options used for document conversion.</summary>
        </member>
        <member name="M:GleamTech.DocumentUltimate.XpzOutputOptions.#ctor">
            <summary>Initializes a new instance of the <see cref="T:GleamTech.DocumentUltimate.XpzOutputOptions"/> class.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.XpzOutputOptions.Password">
            <summary>Gets or sets the password which is used for saving a protected document.</summary>
        </member>
        <member name="P:GleamTech.DocumentUltimate.XpzOutputOptions.HighQualityEnabled">
            <summary>
            Gets or sets a value that specifies whether to produce high quality XPZ.
            The default is false.
            For providing some guarantees regarding how quickly a page can be rendered on different systems 
            such as mobile devices, by default some parts of a page that are very complex and would take really 
            long to render in the browser, are rasterized to an image (flattened). However as a side effect, 
            parts of some pages may look blurry when you zoom in. You can set this property to true to disable 
            this possible flattening (if you observe it's happening with your specific document).
            This way you can ensure vector quality rendering for complex documents such as CAD drawings.
            If you have errors due to insufficient memory, keep it at false.
            </summary>
        </member>
    </members>
</doc>
