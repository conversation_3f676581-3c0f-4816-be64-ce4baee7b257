<?xml version="1.0" encoding="utf-8" ?>
<connectionStrings>
  <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=ASPSTATE;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=NOTIFICATION;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_DSWPROD" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_FORMS" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <!--
  <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=ASPSTATE;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=NOTIFICATION;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_DSWPROD" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_FORMS" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  -->
  <!--
 <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=ASPSTATE;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=NOTIFICATION;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_DSWPROD" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_FORMS" connectionString="Data Source=127.0.0.1,1069;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
   -->

  <!--
    <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=ASPSTATE;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=NOTIFICATION;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_DSWPROD" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=DSWPROD;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  <add name="READONLY_FORMS" connectionString="Data Source=127.0.0.1,1030;Initial Catalog=FORMS;User ID=admin;Password=******$3984;" providerName="S*stem.Data.SqlClient" />
  -->


  <!--
  <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=ASPSTATE;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
  <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=DSWPROD;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
  <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=FORMS;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
  <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=DSWPROD;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
  <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=NOTIFICATION;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
  <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=DSWPROD;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
  -->


  <!--
  <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=ASPSTATE;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
  <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
  <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=FORMS;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
  <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
  <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=NOTIFICATION;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
  <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
  -->


    <!--
    <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=ASPSTATE;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=DSWPROD;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=FORMS;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=DSWPROD;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=NOTIFICATION;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=DSWPROD;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    -->
 








    <!--
    <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=ASPSTATE;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
    <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
    <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=FORMS;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
    <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
    <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=NOTIFICATION;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
    <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
-->





    <!--
    <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=ASPSTATE;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
    <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=DSWPROD;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
    <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=FORMS;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
    <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=DSWPROD;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
    <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=NOTIFICATION;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />
    <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1081;Initial Catalog=DSWPROD;User ID=andrew;Password=********************" providerName="S*stem.Data.SqlClient" />


    
    <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=ASPSTATE;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=DSWPROD;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=FORMS;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=DSWPROD;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=NOTIFICATION;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1050;Initial Catalog=DSWPROD;User ID=sa;Password=*'CLJGp@MJIqsnwI;" providerName="S*stem.Data.SqlClient" />
    -->
    



</connectionStrings>

        
        <!--




<?xml version="1.0" encoding="utf-8" ?>
<connectionStrings>


   <add name="ASPSTATEConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=ASPSTATE;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
   <add name="SETPROFESSIONALConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
   <add name="FORMSConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=FORMS;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
   <add name="DSWPROD" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
   <add name="NOTIFICATIONConnectionString" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=NOTIFICATION;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />
   <add name="SETWorksREPORTS.Properties.Settings.DSWPRODDB" connectionString="Data Source=127.0.0.1,1060;Initial Catalog=DSWPROD;User ID=sa;Password=********************************;" providerName="S*stem.Data.SqlClient" />

</connectionStrings>   
-->


