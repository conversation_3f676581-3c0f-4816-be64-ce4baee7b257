<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Memory.Data</name>
    </assembly>
    <members>
        <member name="T:System.BinaryData">
            <summary>
            A lightweight abstraction for a payload of bytes that supports converting between string, stream, JSON, and bytes.
            </summary>
        </member>
        <member name="F:System.BinaryData._bytes">
            <summary>
            The backing store for the <see cref="T:System.BinaryData"/> instance.
            </summary>
        </member>
        <member name="P:System.BinaryData.Empty">
            <summary>
            Returns an empty BinaryData.
            </summary>
        </member>
        <member name="M:System.BinaryData.#ctor(System.Byte[])">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance by wrapping the
            provided byte array.
            </summary>
            <param name="data">The array to wrap.</param>
        </member>
        <member name="M:System.BinaryData.#ctor(System.Object,System.Text.Json.JsonSerializerOptions,System.Type)">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance by serializing the provided object to JSON
            using <see cref="T:System.Text.Json.JsonSerializer"/>.
            </summary>
            <param name="jsonSerializable">The object that will be serialized to JSON using
            <see cref="T:System.Text.Json.JsonSerializer"/>.</param>
            <param name="options">The options to use when serializing to JSON.</param>
            <param name="type">The type to use when serializing the data. If not specified, <see cref="M:System.Object.GetType"/> will
            be used to determine the type.</param>
        </member>
        <member name="M:System.BinaryData.#ctor(System.ReadOnlyMemory{System.Byte})">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance by wrapping the
            provided bytes.
            </summary>
            <param name="data">Byte data to wrap.</param>
        </member>
        <member name="M:System.BinaryData.#ctor(System.String)">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance from a string by converting
            the string to bytes using the UTF-8 encoding.
            </summary>
            <param name="data">The string data.</param>
        </member>
        <member name="M:System.BinaryData.FromBytes(System.ReadOnlyMemory{System.Byte})">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance by wrapping the provided
            <see cref="T:System.ReadOnlyMemory`1"/>.
            </summary>
            <param name="data">Byte data to wrap.</param>
            <returns>A wrapper over <paramref name="data"/>.</returns>
        </member>
        <member name="M:System.BinaryData.FromBytes(System.Byte[])">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance by wrapping the provided
            byte array.
            </summary>
            <param name="data">The array to wrap.</param>
            <returns>A wrapper over <paramref name="data"/>.</returns>
        </member>
        <member name="M:System.BinaryData.FromString(System.String)">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance from a string by converting
            the string to bytes using the UTF-8 encoding.
            </summary>
            <param name="data">The string data.</param>
            <returns>A value representing the UTF-8 encoding of <paramref name="data"/>.</returns>
        </member>
        <member name="M:System.BinaryData.FromStream(System.IO.Stream)">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance from the specified stream.
            The stream is not disposed by this method.
            </summary>
            <param name="stream">Stream containing the data.</param>
            <returns>A value representing all of the data remaining in <paramref name="stream"/>.</returns>
        </member>
        <member name="M:System.BinaryData.FromStreamAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance from the specified stream.
            The stream is not disposed by this method.
            </summary>
            <param name="stream">Stream containing the data.</param>
            <param name="cancellationToken">A token that may be used to cancel the operation.</param>
            <returns>A value representing all of the data remaining in <paramref name="stream"/>.</returns>
        </member>
        <member name="M:System.BinaryData.FromObjectAsJson``1(``0,System.Text.Json.JsonSerializerOptions)">
            <summary>
            Creates a <see cref="T:System.BinaryData"/> instance by serializing the provided object using
            the <see cref="T:System.Text.Json.JsonSerializer"/>.
            </summary>
            <typeparam name="T">The type to use when serializing the data.</typeparam>
            <param name="jsonSerializable">The data to use.</param>
            <param name="options">The options to use when serializing to JSON.</param>
            <returns>A value representing the UTF-8 encoding of the JSON representation of <paramref name="jsonSerializable" />.</returns>
        </member>
        <member name="M:System.BinaryData.ToString">
            <summary>
            Converts the value of this instance to a string using UTF-8.
            </summary>
            <remarks>
            No special treatment is given to the contents of the data, it is merely decoded as a UTF-8 string.
            For a JPEG or other binary file format the string will largely be nonsense with many embedded NUL characters,
            and UTF-8 JSON values will look like their file/network representation,
            including starting and stopping quotes on a string.
            </remarks>
            <returns>
            A string from the value of this instance, using UTF-8 to decode the bytes.
            </returns>
            <seealso cref="M:System.BinaryData.ToObjectFromJson``1(System.Text.Json.JsonSerializerOptions)" />
        </member>
        <member name="M:System.BinaryData.ToStream">
            <summary>
            Converts the <see cref="T:System.BinaryData"/> to a read-only stream.
            </summary>
            <returns>A stream representing the data.</returns>
        </member>
        <member name="M:System.BinaryData.ToMemory">
            <summary>
            Gets the value of this instance as bytes without any further interpretation.
            </summary>
            <returns>The value of this instance as bytes without any further interpretation.</returns>
        </member>
        <member name="M:System.BinaryData.ToArray">
            <summary>
            Converts the <see cref="T:System.BinaryData"/> to a byte array.
            </summary>
            <returns>A byte array representing the data.</returns>
        </member>
        <member name="M:System.BinaryData.ToObjectFromJson``1(System.Text.Json.JsonSerializerOptions)">
            <summary>
            Converts the <see cref="T:System.BinaryData"/> to the specified type using
            <see cref="T:System.Text.Json.JsonSerializer"/>.
            </summary>
            <typeparam name="T">The type that the data should be
            converted to.</typeparam>
            <param name="options">The <see cref="T:System.Text.Json.JsonSerializerOptions"/> to use when serializing to JSON.</param>
            <returns>The data converted to the specified type.</returns>
        </member>
        <member name="M:System.BinaryData.op_Implicit(System.BinaryData)~System.ReadOnlyMemory{System.Byte}">
            <summary>
            Defines an implicit conversion from a <see cref="T:System.BinaryData" /> to a <see cref="T:System.ReadOnlyMemory`1"/>.
            </summary>
            <param name="data">The value to be converted.</param>
        </member>
        <member name="M:System.BinaryData.op_Implicit(System.BinaryData)~System.ReadOnlySpan{System.Byte}">
            <summary>
            Defines an implicit conversion from a <see cref="T:System.BinaryData" /> to a <see cref="T:System.ReadOnlySpan`1"/>.
            </summary>
            <param name="data">The value to be converted.</param>
        </member>
        <member name="M:System.BinaryData.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>
            <see langword="true" /> if the specified object is equal to the current object; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="M:System.BinaryData.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:System.IO.ReadOnlyMemoryStream">
            <summary>Provides a <see cref="T:System.IO.Stream"/> for the contents of a <see cref="T:System.ReadOnlyMemory`1"/>.</summary>
        </member>
        <member name="T:System.Threading.Tasks.TaskToApm">
            <summary>
            Provides support for efficiently using Tasks to implement the APM (Begin/End) pattern.
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.Begin(System.Threading.Tasks.Task,System.AsyncCallback,System.Object)">
            <summary>
            Marshals the Task as an IAsyncResult, using the supplied callback and state
            to implement the APM pattern.
            </summary>
            <param name="task">The Task to be marshaled.</param>
            <param name="callback">The callback to be invoked upon completion.</param>
            <param name="state">The state to be stored in the IAsyncResult.</param>
            <returns>An IAsyncResult to represent the task's asynchronous operation.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End``1(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.GetTask(System.IAsyncResult)">
            <summary>Gets the task represented by the IAsyncResult.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.ThrowArgumentException(System.IAsyncResult)">
            <summary>Throws an argument exception for the invalid <paramref name="asyncResult"/>.</summary>
        </member>
        <member name="T:System.Threading.Tasks.TaskToApm.TaskAsyncResult">
            <summary>Provides a simple IAsyncResult that wraps a Task.</summary>
            <remarks>
            We could use the Task as the IAsyncResult if the Task's AsyncState is the same as the object state,
            but that's very rare, in particular in a situation where someone cares about allocation, and always
            using TaskAsyncResult simplifies things and enables additional optimizations.
            </remarks>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._task">
            <summary>The wrapped Task.</summary>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._callback">
            <summary>Callback to invoke when the wrapped task completes.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.TaskAsyncResult.#ctor(System.Threading.Tasks.Task,System.Object,System.AsyncCallback)">
            <summary>Initializes the IAsyncResult with the Task to wrap and the associated object state.</summary>
            <param name="task">The Task to wrap.</param>
            <param name="state">The new AsyncState value.</param>
            <param name="callback">Callback to invoke when the wrapped task completes.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.TaskAsyncResult.InvokeCallback">
            <summary>Invokes the callback.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.AsyncState">
            <summary>Gets a user-defined object that qualifies or contains information about an asynchronous operation.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.CompletedSynchronously">
            <summary>Gets a value that indicates whether the asynchronous operation completed synchronously.</summary>
            <remarks>This is set lazily based on whether the <see cref="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._task"/> has completed by the time this object is created.</remarks>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.IsCompleted">
            <summary>Gets a value that indicates whether the asynchronous operation has completed.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.AsyncWaitHandle">
            <summary>Gets a <see cref="T:System.Threading.WaitHandle"/> that is used to wait for an asynchronous operation to complete.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute">
            <summary>
            Indicates that the specified method requires dynamic access to code that is not referenced
            statically, for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when removing unreferenced
            code from an application.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of unreferenced code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of unreferenced code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requries unreferenced code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedNonNegNum">
            <summary>Non-negative number required.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_StreamLengthMustBeNonNegativeInt32">
            <summary>Stream length must be non-negative and less than or equal to Int32.MaxValue.</summary>
        </member>
        <member name="P:System.SR.Argument_InvalidOffLen">
            <summary>Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.</summary>
        </member>
        <member name="P:System.SR.IO_SeekBeforeBegin">
            <summary>An attempt was made to move the position before the beginning of the stream.</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed_StreamClosed">
            <summary>Cannot access a closed Stream.</summary>
        </member>
    </members>
</doc>
