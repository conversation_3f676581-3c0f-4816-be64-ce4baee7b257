﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6B989DAF-265A-4FB6-92DE-924D173C4A0B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SETWorksREPORTS</RootNamespace>
    <AssemblyName>SETWorksREPORTS</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.Barcode, Version=4.11.0.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Barcode.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.DataExport, Version=4.1.9.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.DataExport.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.DataExport.ResourceMgr, Version=2.1.0.0, Culture=neutral, PublicKeyToken=4bc1500157862925">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.DataExport.ResourceMgr.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.Doc, Version=8.11.16.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Doc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.DocViewer.Forms, Version=5.1.1.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.DocViewer.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.Email, Version=3.9.1.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Email.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.License, Version=1.3.8.40, Culture=neutral, PublicKeyToken=b1144360237c8b3f">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.License.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.OfficeViewer.Forms, Version=5.12.0.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.OfficeViewer.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.Pdf, Version=6.11.12.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Pdf.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.PdfViewer.Asp, Version=5.11.2.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.PdfViewer.Asp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.PdfViewer.Forms, Version=5.11.2.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.PdfViewer.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.Presentation, Version=5.11.4.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Presentation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.Spreadsheet, Version=4.10.1.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Spreadsheet.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.XLS, Version=10.11.7.0, Culture=neutral, PublicKeyToken=663f351905198cb3">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.XLS.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="StackExchange.Redis, Version=2.6.122.0, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.2.6.122\lib\net461\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Reporting, Version=12.1.18.620, Culture=neutral, PublicKeyToken=a9d7983dfcc261be, processorArchitecture=MSIL">
      <HintPath>..\packages\Telerik.Reporting.12.1.18.620\lib\net40\Telerik.Reporting.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Reporting.OpenXmlRendering.2.7.2, Version=12.1.18.620, Culture=neutral, PublicKeyToken=a9d7983dfcc261be, processorArchitecture=MSIL">
      <HintPath>..\packages\Telerik.Reporting.12.1.18.620\lib\net40\Telerik.Reporting.OpenXmlRendering.2.7.2.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Reporting.XpsRendering, Version=12.1.18.620, Culture=neutral, PublicKeyToken=a9d7983dfcc261be, processorArchitecture=MSIL">
      <HintPath>..\packages\Telerik.Reporting.12.1.18.620\lib\net40\Telerik.Reporting.XpsRendering.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Web.UI, Version=2016.2.607.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4, processorArchitecture=MSIL">
      <HintPath>..\packages\Telerik.UI.for.AspNet.Ajax.Net45.2016.2.607\lib\net45\Telerik.Web.UI.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Web.UI.Skins, Version=2016.2.607.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4, processorArchitecture=MSIL">
      <HintPath>..\packages\Telerik.UI.for.AspNet.Ajax.Net45.2016.2.607\lib\net45\Telerik.Web.UI.Skins.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.ReportViewer.WebForms, Version=12.1.18.620, Culture=neutral, PublicKeyToken=a9d7983dfcc261be">
      <Private>True</Private>
      <HintPath>C:\Program Files (x86)\Progress\Telerik Reporting R2 2018\bin\Telerik.ReportViewer.WebForms.dll</HintPath>
      <SpecificVersion>True</SpecificVersion>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Cache\TelerikReportRedisStorage.cs" />
    <Compile Include="DAO\FORMS\FormsReportBO2.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\AccidentReport\AccidentReportBO.cs" />
    <Compile Include="REPORTS\AccidentReport\AccidentReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordAppointmentConversionReportStaff\ActivityRecordAppointmentConversionReportStaff.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordAppointmentConversionReportStaff\ActivityRecordAppointmentConversionReportStaffBO.cs" />
    <Compile Include="REPORTS\ActivityRecordAppointmentConversionReport\ActivityRecordAppointmentConversionReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordAppointmentConversionReport\ActivityRecordAppointmentConversionReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordAuditReport\ActivityRecordAuditReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordAuditReport\ActivityRecordAuditReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordNonBillableWithConsumerReport\ActivityRecordNonBillableWithConsumerReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordNonBillableWithConsumerReport\ActivityRecordNonBillableWithConsumerReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordNonBillableWithConsumerReport\ActivityRecordsNonBillableWithConsumerReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingReportDayRateBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingReportDayRateExceptionStatus.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingReportDayRateNoMaxExtraColumnsUnrounded.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingReportDayRateExtraColumnsUnrounded.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingDepartmentReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingMonthlyInvoiceReportWithFundingSourceContact.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingInvoiceReportWithFundingSourceContact.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReportExtendedExcel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReportExtendedExcelNEBAFIXED.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsGroupReport\ActivityRecordsGroupReportMultiMonth.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterAccessReport\GompersQuarterlyReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTrackingAddCommentsWithPictures\ActivityRecordsInterventionAndAssessmentAdditionalCommentsWithPicturesBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTrackingAddCommentsWithPictures\ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsWithPicturesStandAlone.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAssessmentKeyDateReport\ActivityRecordsInterventionAssessmentKeyDateReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAssessmentKeyDateReport\ActivityRecordsInterventionAssessmentKeyDateReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationOverviewSimpleReport\AuthorizationOverviewSimpleReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\BenefitPlanningBillingReport\BenefitPlanningBillingReportParameters.cs" />
    <Compile Include="REPORTS\BenefitPlanningBillingReport\BenefitPlanningBillingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\BenefitPlanningBillingReport\BenefitPlanningBillingReportBO.cs" />
    <Compile Include="REPORTS\BenefitPlanningBillingReport\BenefitPlanningData.cs" />
    <Compile Include="REPORTS\BrokerageServicesReport\BrokerageAggregateData.cs" />
    <Compile Include="REPORTS\BrokerageServicesReport\BrokerageGrouping.cs" />
    <Compile Include="REPORTS\BrokerageServicesReport\BrokerageServiceData.cs" />
    <Compile Include="REPORTS\BrokerageServicesReport\BrokerageServicesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\BrokerageServicesReport\BrokerageServicesReportBO.cs" />
    <Compile Include="REPORTS\BrokerageServicesReport\BrokerageServicesReportParameters.cs" />
    <Compile Include="REPORTS\CAGroupInvoiceReport\CAGroupInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CAGroupInvoiceReport\CAGroupInvoiceReportBO.cs" />
    <Compile Include="REPORTS\CAIPInvoiceReport\CAAdultInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByTaskWithRules.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerProductionReport\ConsumerProductionEarningsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionEarningsReportGroupedByConsumer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerProductionReport\ConsumerProductionPayStubsReportWithRounding.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerProductionReport\ConsumerProductionPayStubsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByConsumerWithRules.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByDepartment.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportWithRules.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByConsumer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceReport\EmployeeTimesheetBillableWithConsumerServiceExcelUngroupedReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportWithGoalsAndCommunityIntegrationLocations.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportWithGoals.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MileageReport\MileageReportAdditionalBO.cs" />
    <Compile Include="REPORTS\MileageReport\MileageReportExcel3.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V4\MOSEMonthlyJobSupportsReport_V4.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V4\MOSEMonthlyJobSupportsReport_V4BO.cs" />
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V5\MOSEMonthlyJobSupportsReport_V5.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V5\MOSEMonthlyJobSupportsReport_V5BO.cs" />
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V4\MOSEMonthlyJobSupportsSummaryReport_V4.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V4\MOSEMonthlyJobSupportsSummaryReport_V4BO.cs" />
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V5\MOSEMonthlyJobSupportsSummaryReport_V5.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V5\MOSEMonthlyJobSupportsSummaryReport_V5BO.cs" />
    <Compile Include="REPORTS\NetworkContactAveryMailingLabels\NetworkContactAveryMailingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\NetworkContactAveryMailingLabels\NetworkContactAveryMailingReportBO.cs" />
    <Compile Include="REPORTS\OutcomesBasedPaymentsReportNDI\OutcomesBasedPaymentsReportNDI.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomesBasedPaymentsReportNDI\OutcomesBasedPaymentsReportNDIBO.cs" />
    <Compile Include="REPORTS\PointInTimeRAReport\PointInTimeRAReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RequestDetailsReport\RequestDetailsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RequestDetailsReport\RequestDetailsReportBO.cs" />
    <Compile Include="REPORTS\DistAndDemoEquipmentReport\DistAndDemoEquipmentReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DistAndDemoEquipmentReport\DistAndDemoEquipmentReportBO.cs" />
    <Compile Include="REPORTS\ConsumerCertificationsReport\ConsumerCertificationsReportBO.cs" />
    <Compile Include="REPORTS\ConsumerCertificationsReport\ConsumerCertificationsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerDepartmentFinanceReport\ConsumerFinanceReportWithReceiptImagesBO.cs" />
    <Compile Include="REPORTS\ConsumerDepartmentFinanceReport\ConsumerFinanceReportWithReceiptImages.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DepartmentFinanceReport\DepartmentFinanceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DepartmentFinanceReport\DepartmentFinanceReportBO.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceReport\EmployeeTimesheetBillableWithConsumerServiceExcelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerContactReport\NDIContactReportBO.cs" />
    <Compile Include="REPORTS\ConsumerContactReport\NDIContactExcelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DemographicsReport\NDIDemographicsReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DemographicsReport\NDIDemographicsReportBO.cs" />
    <Compile Include="REPORTS\EVVReconciliationReport\EVVReconciliationReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EVVReconciliationReport\EVVReconciliationReportBO.cs" />
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsGenericMedicaidWaiverSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsRequiredReport\FormsRequiredReportBO.cs" />
    <Compile Include="REPORTS\FormsRequiredReport\FormsRequiredReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReport2WithStaffFocusAndService\ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesNoStaffCIMOR.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AppointmentDepartmentCalendarReport\AppointmentDepartmentCalendarReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AppointmentDepartmentCalendarReport\AppointmentDepartmentCalendarReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationOverviewWithDatesAndFrequenciesReport\AuthorizationOverviewWithDatesAndFrequenciesAndUnitsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DDaPFile\DDaPFile.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DDaPFile\DDaPFileBO.cs" />
    <Compile Include="REPORTS\DDaPFile\ProcessDDaPFile.cs" />
    <Compile Include="REPORTS\DDSEmergencyFactSheetReport\DDSEmergencyFactSheetReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DDSEmergencyFactSheetReport\DDSEmergencyFactSheetReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterAccessReport\ServiceAlternativesMonthlyProgressReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportWithSignature.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportBlankSignature.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\HealthCarePaymentAgeingReport\SageIntacctAccRecInvoicesLimited.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\HealthCarePaymentKeystoneMSDynamicsInterface\HealthCarePaymentKeystoneMSDynamicsInterfaceReportBO.cs" />
    <Compile Include="REPORTS\HealthCarePaymentKeystoneMSDynamicsInterface\HealthCarePaymentKeystoneMSDynamicsInterfaceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MedicationSignatureReport\MedicationSignatureReportBO.cs" />
    <Compile Include="REPORTS\MedicationSignatureReport\MedicationSignatureReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MonthlyActivityRecordBillingReport\MonthlyActivityRecordBillingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MonthlyActivityRecordBillingReport\MonthlyActivityRecordBillingReportBO.cs" />
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V2\MOSEMonthlyJobSupportsReport_V2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V2\MOSEMonthlyJobSupportsReportBO_V2.cs" />
    <Compile Include="REPORTS\GenericStringReport\GenericStringReportBO.cs" />
    <Compile Include="REPORTS\GenericStringReport\GenericStringReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V3\MOSEMonthlyJobSupportsReport_V3.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport_V3\MOSEMonthlyJobSupportsReport_V3BO.cs" />
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V3\MOSEMonthlyJobSupportsSummaryReport_V3.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V3\MOSEMonthlyJobSupportsSummaryReport_V3BO.cs" />
    <Compile Include="REPORTS\OregonEXPRS\OregonEXPRSEVV.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedReport\NDIOutcomeBasedReportBO.cs" />
    <Compile Include="REPORTS\OutcomeBasedReport\NDIOutcomeBasedReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomesBasedPaymentsReport\OutcomesBasedPaymentsReportBO.cs" />
    <Compile Include="REPORTS\OutcomesBasedPaymentsReport\OutcomesBasedPaymentsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CAIPInvoiceReport\CAIPInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CAIPInvoiceReport\CAIPInvoiceReportBO.cs" />
    <Compile Include="REPORTS\ProductionReport\ProductionReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksAccountingInvoiceExportIIFReport\QuickBooksAccountingInvoiceExportIIFReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksAccountingInvoiceExportIIFReport\QuickBooksAccountingInvoiceExportIIFReportBO.cs" />
    <Compile Include="REPORTS\QuickBooksTimesheetNonbillableAndCostCenterReport\QuickBooksTimesheetNonbillableAndCostCenterReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksTimesheetNonbillableAndCostCenterReport\QuickBooksTimesheetNonbillableAndCostCenterReportBO.cs" />
    <Compile Include="REPORTS\StaffProductionReport\StaffProductionReportNew.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffProductionReport\StaffProductionReportNewBO.cs" />
    <Compile Include="REPORTS\TaskStaffPerformanceWithTaskList\TaskStaffPerformanceWithTaskListBO.cs" />
    <Compile Include="REPORTS\TaskStaffPerformanceWithTaskList\TaskStaffPerformanceWithTaskListTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\Tier3ScreeningReport\Tier3ScreeningReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\Tier3ScreeningReport\Tier3ScreeningReportBO.cs" />
    <Compile Include="REPORTS\Tier3ServiceReport\Tier3AggregateData.cs" />
    <Compile Include="REPORTS\Tier3ServiceReport\Tier3Data.cs" />
    <Compile Include="REPORTS\Tier3ServiceReport\Tier3Grouping.cs" />
    <Compile Include="REPORTS\Tier3ServiceReport\Tier3ServiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\Tier3ServiceReport\Tier3ServiceReportBO.cs" />
    <Compile Include="REPORTS\Tier3ServiceReport\Tier3ServiceReportParameters.cs" />
    <Compile Include="REPORTS\UserExpenseReportSubtotals\UserExpenseReportSubtotals.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\WarrantyReport\WarrantyReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\WarrantyReport\WarrantyReportBO.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetAllRecordsWithoutOverlapWithSums\EmployeeTimesheetAllRecordsWithoutOverlapWithSumsWithPayrollCode.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterAccessReport\GompersQuarterlyReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterAccessReport\ActivityRecordsInterAssessReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsReportUnitSummary\ActivityRecordsReportUnitSummaryWithClock.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsBillingWithGoalReport\ActivityRecordsBillingWithGoalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsPlaceOfServiceReport\ActivityRecordsPlaceOfServiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsPlaceOfServiceReport\ActivityRecordsPlaceOfServiceReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsBillingWithGoalReport\ActivityRecordsBillingWithGoalReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsHistoryReport\ActivityRecordsHistoryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsHistoryReport\ActivityRecordsHistoryReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTracking\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTracking.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTracking\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTrackingBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthly\ActivityRecordsInterventionAndAssessmentMonthly.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthly\ActivityRecordsInterventionAndAssessmentMonthlyAddComments.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthly\ActivityRecordsInterventionAndAssessmentMonthlyBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterventionAssessmentKeyPercentageReport\ActivityRecordsInterventionAssessmentKeyPercentageReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAssessmentKeyPercentageReport\ActivityRecordsInterventionAssessmentKeyPercentageReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterventionAssessmentKeyPercentageReport\ActivityRecordsInterventionAssessmentKeyPercentageReportHeaderRepeat.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAssessmentKeyReport\ActivityRecordsInterventionAssessmentKeyReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAssessmentKeyReport\ActivityRecordsInterventionAssessmentKeyReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsMultiGoalReport\ActivityRecordsMultiGoalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsMultiGoalReport\ActivityRecordsMultiGoalReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsMultiGoalReport\ActivityRecordsMultiGoalWithoutStrategiesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsNonBillableReport\ActivityRecordsNonBillableReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsNonBillableReport\ActivityRecordsNonBillableReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsNonBillableReport\ActivityRecordsNonBillableReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsReport2\ActivityRecordsReportBusinessObject2.cs" />
    <Compile Include="REPORTS\ActivityRecordsReport2\ActivityRecordsReportTelerik2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsReportReconciliation\ActivityRecordsReportReconciliationTelerikExcel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsReportReconciliation\ActivityRecordsReportBusinessObjectReconciliation.cs" />
    <Compile Include="REPORTS\ActivityRecordsReportReconciliation\ActivityRecordsReportReconciliationTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsReportSpreadsheetWithCAPStaffAndService\ActivityRecordsReportSpreadsheetWithCAPStaffAndService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsReportSpreadsheetWithCAPStaffAndService\ActivityRecordsReportSpreadsheetWithCAPStaffAndServiceBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsReportUnitSummary\ActivityRecordsReportUnitSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsReportUnitSummary\ActivityRecordsReportUnitSummaryBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsReportWithoutHoursAndMRVR\ActivityRecordsReportWithoutHoursAndMRVRBusinessObject.cs" />
    <Compile Include="REPORTS\ActivityRecordsReportWithoutHoursAndMRVR\ActivityRecordsReportWithoutHoursAndMRVRTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsReport\ActivityRecordsReportBusinessObject.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsReport\ActivityRecordsReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsSumsReport\ActivityRecordsSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsSumsReport\ActivityRecordsSumsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsSumsReport\ActivityRecordsSumsReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsTCMStaffHoursReport\ActivityRecordsTCMStaffHoursReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTCMStaffHoursReport\ActivityRecordsTCMStaffHoursReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReport2WithStaffAndService\ActivityRecordsUnitReport2WithStaffAndService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReport2WithStaffAndService\ActivityRecordsUnitReport2WithStaffAndServiceBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReport2WithStaffFocusAndService\ActivityRecordsUnitReport2WithStaffFocusAndService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReport2WithStaffFocusAndService\ActivityRecordsUnitReport2WithStaffFocusAndServiceBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReport2WithStaffFocusAndService\ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReport2\ActivityRecordsUnitReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReport2\ActivityRecordsUnitReport2BO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReport2\ActivityRecordsUnitContractReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesHours.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesAndInAndOutTimesGroupedByConsumer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesNoStaffHours.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReport3Extended.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationRequestSummaryReport\AuthorizationRequestSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationRequestSummaryReport\AuthorizationRequestSummaryReport.Designer.cs">
      <DependentUpon>AuthorizationRequestSummaryReport.cs</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\AuthorizationRequestSummaryReport\AuthorizationRequestSummaryReportBO.cs" />
    <Compile Include="REPORTS\ClientStatsReport\ClientStatusReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ClientStatsReport\ClientStatusReportBO.cs" />
    <Compile Include="REPORTS\ClinicalEventsReport\ClinicalEventsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ClinicalEventsReport\ClinicalEventsReportBO.cs" />
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerWeightAnalysisReport\ConsumerWeightAnalysisReportBO.cs" />
    <Compile Include="REPORTS\ConsumerWeightAnalysisReport\ConsumerWeightAnalysisReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DailyJobSupportsTimeLog\DailyJobSupportsTimeLog.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DailyJobSupportsTimeLog\DailyJobSupportsTimeLogReportBO.cs" />
    <Compile Include="REPORTS\HealthCarePaymentAgeingReport\HealthCarePaymentAgeingTotalsOnly.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\HealthCarePaymentAgeingReport\SageIntacctAccRecInvoices.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\HealthCarePaymentAgeingReport\HealthCarePaymentAgeingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\HealthCarePaymentAgeingReport\HealthCarePaymentAgeingReportBO.cs" />
    <Compile Include="REPORTS\JobEarningsReport\JobEarningsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobEarningsReport\JobEarningsReportBO.cs" />
    <Compile Include="REPORTS\MileageReport\MileageReportExcelByStaff.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\LogInternshipDevelopmentSupports\LogInternshipDevelopmentSupports.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\LogInternshipDevelopmentSupports\LogInternshipDevelopmentSupports.Designer.cs">
      <DependentUpon>LogInternshipDevelopmentSupports.cs</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\LogInternshipDevelopmentSupports\LogInternshipDevelopmentSupportsBO.cs" />
    <Compile Include="REPORTS\NWCGreatPlainsInterface\NWCGreatPlainsInterface.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OregonEXPRS\OregonEXPRS.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBillingReport\OutcomeBillingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksPayrollExportIIFReport\QuickBooksPayrollExportIIFReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksPayrollExportIIFReport\QuickBooksPayrollExportIIFReportBO.cs" />
    <Compile Include="REPORTS\StaffServiceWithNonBillableTotalReport\StaffServiceWithNonBillableTotalFunderGroupingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\TaskListReport\TaskListReportBO.cs" />
    <Compile Include="REPORTS\TaskListReport\TaskListReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FunderIndividualPlanBudgetComparisonReport\FunderIndividualPlanBudgetComparisonReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FunderIndividualPlanBudgetComparisonReport\FunderIndividualPlanBudgetComparisonReport.Designer.cs">
      <DependentUpon>FunderIndividualPlanBudgetComparisonReport.cs</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\FunderIndividualPlanBudgetComparisonReport\FunderIndividualPlanBudgetComparisonReportBO.cs" />
    <Compile Include="REPORTS\MileageReport\MileageReportBO.cs" />
    <Compile Include="REPORTS\NJ_DVRS_Activities\NJ_DVRS_Activities.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\NJ_DVRS_Activities\NJ_DVRS_Activities.Designer.cs">
      <DependentUpon>NJ_DVRS_Activities.cs</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\NJ_DVRS_Activities\NJ_DVRS_ActivitiesBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndPlaceOfService\ActivityRecordsUnitReportWithAuthAndPlaceOfService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndPlaceOfService\ActivityRecordsUnitReportWithAuthAndPlaceOfServiceBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndEmployer\ActivityRecordsUnitReportWithAuthAndEmployerCHS.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndEmployer\ActivityRecordsUnitReportWithAuthAndEmployerCHSBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuth\ActivityRecordsUnitReportWithAuthCustomIDAndTotals.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuth\ActivityRecordsUnitReportWithAuth.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuth\ActivityRecordsUnitReportWithAuthBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithPlacement.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRules.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesAndInAndOutTimes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesNoStaff.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AgeGenderEthnicityGroupStatistics\AgeGenderEthnicityGroupStatisticsBusinessObject.cs" />
    <Compile Include="REPORTS\AnnualReviewDueReport\AnnualReviewDueBO.cs" />
    <Compile Include="REPORTS\AnnualReviewDueReport\AnnualReviewDueReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AppointmentAuthAndConsumerWorkCalendarReport\AppointmentAuthAndConsumerWorkCalendarReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AppointmentAuthAndConsumerWorkCalendarReport\AppointmentAuthAndConsumerWorkCalendarReportBO.cs" />
    <Compile Include="REPORTS\AppointmentCalendarReport\AppointmentCalendarReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AppointmentCalendarReport\AppointmentCalendarReportBO.cs" />
    <Compile Include="REPORTS\AppointmentConsumerCalendarReport\AppointmentConsumerCalendarReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AppointmentConsumerCalendarReport\AppointmentConsumerCalendarReportBO.cs" />
    <Compile Include="REPORTS\AppointmentScheduleReport\AppointmentScheduleReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AppointmentScheduleReport\AppointmentScheduleReportBO.cs" />
    <Compile Include="REPORTS\AttendanceFTEReport\AttendanceFTEReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AttendanceFTEReport\AttendanceFTEReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationAggregateReport\AuthorizationAggregateReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationAggregateReport\AuthorizationAggregateReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationExceededReport\AuthorizationExceededReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationExceededReport\AuthorizationExceededReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationGroupOverviewReport\AuthorizationGroupOverviewReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationGroupOverviewReport\AuthorizationGroupOverviewReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationLimitHoursUsedReport\AuthorizationLimitHoursUsedReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationLimitHoursUsedReport\AuthorizationLimitHoursUsedReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReport3.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitExtendedReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitExtendedReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationOverviewReport\AuthorizationOverviewReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationOverviewReport\AuthorizationOverviewReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationOverviewWithDatesAndFrequenciesReport\AuthorizationOverviewWithDatesAndFrequenciesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationOverviewWithDatesAndFrequenciesReport\AuthorizationOverviewWithDatesAndFrequenciesReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationPercentageLeftReport\AuthorizationPercentageLeftReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationPercentageLeftReport\AuthorizationPercentageLeftReportBO.cs" />
    <Compile Include="REPORTS\AuthorizationReportBillable\AuthorizationReportBusinessObjectJoinedBillable.cs" />
    <Compile Include="REPORTS\AuthorizationReportBillable\AuthorizationReportBusinessObjectJoinedBillable2.cs" />
    <Compile Include="REPORTS\AuthorizationReportBillable\AuthorizationReportTelerikBillable.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationReportBillable\AuthorizationReportTelerikBillable2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationReportGoals\AuthorizationReportBusinessObjectJoinedGoal.cs" />
    <Compile Include="REPORTS\AuthorizationReportGoals\AuthorizationReportTelerikGoal.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\AuthorizationReportTemporary\AuthorizationReportTemporaryBO.cs" />
    <Compile Include="REPORTS\AuthorizationReportTemporary\AuthorizationReportTemporaryTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\BillingReport837\837Main.cs" />
    <Compile Include="REPORTS\BillingReport837\ISA.cs" />
    <Compile Include="REPORTS\CAPBimonthlyTimesheetReport\CAPBimonthlyTimesheetDataSetBO.cs" />
    <Compile Include="REPORTS\CAPBimonthlyTimesheetReport\CAPBiMonthlyTimesheetReport3.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CAPBimonthlyTimesheetReport\CAPBiMonthlyTimesheetReport4.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CAPBimonthlyTimesheetReport\CAPBimonthlyTimesheetReportBusinessObject.cs" />
    <Compile Include="REPORTS\CAPBimonthlyTimesheetReport\CAPBimonthlyTimesheetReportBusinessObject2.cs" />
    <Compile Include="REPORTS\CAPGoalsReport\CAPGoalsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CAPGoalsReport\CAPGoalsReportBusinessObject.cs" />
    <Compile Include="REPORTS\CareerDiscoveryReport\CareerDiscoveryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CareerDiscoveryReport\CareerDiscoveryReportBO.cs" />
    <Compile Include="REPORTS\CareerDiscoveryReport\Conditions\ConditionsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CareerDiscoveryReport\Contributions\ContributionsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CareerDiscoveryReport\Preferences\PreferencesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CareerDiscoveryReport\SupportNeeds\SupportNeedsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CaseNoteAuditReport\CaseNoteAuditReportBO.cs" />
    <Compile Include="REPORTS\CaseNoteAuditReport\CaseNoteAuditReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CaseNoteCoverageReport\CaseNoteCoverageReportBO.cs" />
    <Compile Include="REPORTS\CaseNoteCoverageReport\CaseNoteCoverageReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CaseNoteReport\CaseNoteReportBO.cs" />
    <Compile Include="REPORTS\CaseNoteReport\CaseNoteReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\CEOEmployeeIDReport\CEOEmployeeIDReportBO.cs" />
    <Compile Include="REPORTS\CEOEmployeeIDReport\CEOEmployeeIDReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionAggregateReport\ConsumerAgencyProductionAggregateReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByTask.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAgeReport\ConsumerAgeReportBO.cs" />
    <Compile Include="REPORTS\ConsumerAgeReport\ConsumerAgeReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAtWorkCalendarReport\ConsumerAtWorkCalendarReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAtWorkCalendarReport\ConsumerAtWorkCalendarReportBO.cs" />
    <Compile Include="REPORTS\ConsumerAveryMailingLabels\ConsumerAveryMailingReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerAveryMailingLabels\ConsumerAveryMailingReportBO.cs" />
    <Compile Include="REPORTS\ConsumerContactExtendedReport\ConsumerContactExtendedReportBO.cs" />
    <Compile Include="REPORTS\ConsumerContactExtendedReport\ConsumerContactExtendedReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerContactReport\ConsumerContactExcelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerContactReport\ConsumerContactReportBO.cs" />
    <Compile Include="REPORTS\ConsumerContactReport\ConsumerContactReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerDepartmentFinanceReport\ConsumerDepartmentFinanceReportBO.cs" />
    <Compile Include="REPORTS\ConsumerDetailedContactReport\ConsumerDetailedContactBO.cs" />
    <Compile Include="REPORTS\ConsumerDetailedContactReport\ConsumerDetailedContactReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerDisabilityReport\ConsumerDisabilityReportBO.cs" />
    <Compile Include="REPORTS\ConsumerDisabilityReport\ConsumerDisabilityReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerEquipmentOrderReport\ConsumerEquipmentOrderExcelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerEquipmentOrderStatisticsReport\ConsumerEquipmentOrderStatisticsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerEquipmentOrderStatisticsReport\ConsumerEquipmentOrderStatisticsReportBO.cs" />
    <Compile Include="REPORTS\ConsumerEarningsSpreadsheetReport\ConsumerEarningsSpreadsheetReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerEarningsSpreadsheetReport\ConsumerEarningsSpreadsheetReportBO.cs" />
    <Compile Include="REPORTS\ConsumerExpirationFieldReport\ConsumerExpirationFieldReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerExpirationFieldReport\ConsumerExpirationFieldReportBO.cs" />
    <Compile Include="REPORTS\ConsumerNetworkCommunicationReport\ConsumerNetworkCommunicationHistoryBO.cs" />
    <Compile Include="REPORTS\ConsumerNetworkContactHistoryReport\ConsumerNetworkContactHistoryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerNetworkContactHistoryReport\ConsumerNetworkContactHistoryReportBO.cs" />
    <Compile Include="REPORTS\ConsumerNetworkContactReport\ConsumerNetworkContactReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerNetworkContactReport\ConsumerNetworkContactReportBO.cs" />
    <Compile Include="REPORTS\ConsumerOutcomeGoalStrategyReport\ConsumerOutcomeGoalStrategyExcelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerOutcomeGoalStrategyReport\ConsumerOutcomeGoalStrategyReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerOutcomeGoalStrategyReport\ConsumerOutcomeGoalStrategyReportBO.cs" />
    <Compile Include="REPORTS\ConsumerOwnGuardianReport\ConsumerOwnGuardianReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerOwnGuardianReport\ConsumerOwnGuardianReportBO.cs" />
    <Compile Include="REPORTS\ConsumerPrimaryAndSecondaryContactReport\ConsumerPrimaryAndSecondaryContactReportBO.cs" />
    <Compile Include="REPORTS\ConsumerPrimaryAndSecondaryContactReport\ConsumerPrimaryAndSecondaryContactReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerScaleAssessmentReport\ConsumerScaleAssessmentReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerScaleAssessmentReport\ConsumerScaleAssessmentReportBO.cs" />
    <Compile Include="REPORTS\ConsumerScaleAssessmentWithGraphReport\ConsumerScaleAssessmentWithGraphReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerScaleAssessmentWithGraphReport\ConsumerScaleAssessmentWithGraphReportBO.cs" />
    <Compile Include="REPORTS\ConsumerServiceTotalReport\ConsumerServiceTotalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerServiceTotalReport\ConsumerServiceTotalReportBO.cs" />
    <Compile Include="REPORTS\ConsumerServiceWithNonbillableTotalReport\ConsumerServiceWithNonbillableTotalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerServiceWithNonbillableTotalReport\ConsumerServiceWithNonbillableTotalReportBO.cs" />
    <Compile Include="REPORTS\ConsumerServiceWithOutcomesTotalReport\ConsumerServiceWithOutcomesTotalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerServiceWithOutcomesTotalReport\ConsumerServiceWithOutcomesTotalReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTrackingAddComments\ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsStandAlone.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTrackingAddComments\ActivityRecordsInterventionAndAssessmentAdditionalCommentsBO.cs" />
    <Compile Include="REPORTS\ConsumerPlacementBenefitReport\ConsumerPlacementBenefitReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerPlacementBenefitReport\ConsumerPlacementBenefitReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsTransportationServiceCountReport\ActivityRecordsTransportationServiceCountReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTransportationServiceCountReport\ActivityRecordsTransportationServiceCountReportBO.cs" />
    <Compile Include="REPORTS\ConsumerMedsAdministrationCoverageAndTrainingReport\ConsumerMedsAdministrationCoverageAndTrainingReportBO.cs" />
    <Compile Include="REPORTS\ConsumerMedsAdministrationCoverageAndTrainingReport\ConsumerMedsAdministrationCoverageAndTrainingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsGroupReport\ActivityRecordsGroupReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsGroupReport\ActivityRecordsGroupReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsGroupOverallAndConsumerCommentReport\ActivityRecordsGroupOverallAndConsumerCommentReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsGroupOverallAndConsumerCommentReport\ActivityRecordsGroupOverallAndConsumerCommentReportStandAlone.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsGroupDetailsReport\ActivityRecordsGroupDetailsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsGroupDetailsReport\ActivityRecordsGroupDetailsReportBO.cs" />
    <Compile Include="DAO\FORMS\FormsReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndPhases\ActivityRecordsUnitReportWithAuthAndPhases.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndPhases\ActivityRecordsUnitReportWithAuthAndPhasesBO.cs" />
    <Compile Include="REPORTS\ConsumerOutcomeGoalStrategyComprehensiveReport\ConsumerOutcomeGoalStrategyComprehensiveReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerOutcomeGoalStrategyComprehensiveReport\ConsumerOutcomeGoalStrategyComprehensiveReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithConsumerTimeInStaffAndService\ActivityRecordsUnitReportWithConsumerTimeInStaffAndService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithConsumerTimeInStaffAndService\ActivityRecordsUnitReportWithConsumerTimeInStaffAndServiceBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndEmployer\ActivityRecordsUnitReportWithAuthAndEmployer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsUnitReportWithAuthAndEmployer\ActivityRecordsUnitReportWithAuthAndEmployerBO.cs" />
    <Compile Include="REPORTS\ConsumerAggregateDataReport\ConsumerAggregateDataReportBO.cs" />
    <Compile Include="REPORTS\ConsumerAggregateDataReport\ConsumerAggregateDataReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerEquipmentOrderReport\ConsumerEquipmentOrderReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerEquipmentOrderReport\ConsumerEquipmentOrderReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReportByConsumer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReportByConsumerAndDepartment.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReportExtendedExcel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingSummaryReportAndDepartment.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ActivityRecordsTCMRawDataReport\ActivityRecordsTCMRawDataReportBO.cs" />
    <Compile Include="REPORTS\ActivityRecordsTCMRawDataReport\ActivityRecordsTCMRawDataReportExcel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerDepartmentFinanceReport\ConsumerDepartmentFinanceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployeeTimesheetAllRecordsWithoutOverlapWithSums\EmployeeTimesheetAllRecordsWithoutOverlapNoDepartmentsWithSums.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMonthlySummaryReportUpdated\FormsMonthlySummaryReportUpdated.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMonthlySummaryReportUpdated\FormsMonthlySummaryReportUpdatedBO.cs" />
    <Compile Include="REPORTS\FormsSAServiceDeliveryPlanReport\FormSAServiceDeliveryPlanReportPage2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FunderIndividualPlanBudgetReport\FunderIndividualPlanBudgetReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FunderIndividualPlanBudgetReport\FunderIndividualPlanBudgetReport.Designer.cs">
      <DependentUpon>FunderIndividualPlanBudgetReport.cs</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\FunderIndividualPlanBudgetReport\FunderIndividualPlanBudgetReportBO.cs" />
    <Compile Include="REPORTS\HealthCarePaymentBatchReport\HealthCarePaymentBatchExcelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\HealthCarePaymentBatchReport\HealthCarePaymentBatchProcessedReportBO.cs" />
    <Compile Include="REPORTS\MarylandJobDevLog\MarylandJobDevLog.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MarylandJobDevLog\MarylandJobDevLog.Designer.cs">
      <DependentUpon>MarylandJobDevLog.cs</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\MarylandJobDevLog\MarylandJobDevLogBO.cs" />
    <Compile Include="REPORTS\MileageReport\MileageReportTelerikGroupedByCostCenter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\NotificationReport\NotificationReportInternalUtilitiesBO.cs" />
    <Compile Include="REPORTS\StaffEquipmentOrderReport\StaffEquipmentOrderExcelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffEquipmentOrderReport\StaffEquipmentOrderReportBO.cs" />
    <Compile Include="REPORTS\FormsADVPQuarterlyReport\FormsADVPQuarterlyReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsADVPQuarterlyReport\FormsADVPQuarterlyReportBO.cs" />
    <Compile Include="REPORTS\PMLogsReport\PMLogsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PMLogsReport\PMLogsReportBO.cs" />
    <Compile Include="REPORTS\StaffMonthlyHoursProductionWithoutTargetsReport\StaffMonthlyHoursProductionWithoutTargetsReportBO.cs" />
    <Compile Include="REPORTS\StaffMonthlyHoursProductionWithoutTargetsReport\StaffMonthlyHoursProductionWithoutTargetsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffMonthlyHoursProductionReport\StaffMonthlyHoursProductionReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffMonthlyHoursProductionReport\StaffMonthlyHoursProductionReportBO.cs" />
    <Compile Include="REPORTS\StaffMonthlyHoursSummaryReport\StaffMonthlyHoursSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffMonthlyHoursSummaryReport\StaffMonthlyHoursSummaryReportBO.cs" />
    <Compile Include="REPORTS\UserExpenseReport\UserExpenseReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\TaskStaffPerformanceReport\TaskStaffPerformanceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\TaskStaffPerformanceReport\TaskStaffPerformanceReport.Designer.cs">
      <DependentUpon>TaskStaffPerformanceReport.cs</DependentUpon>
    </Compile>
    <Compile Include="REPORTS\TaskStaffPerformanceReport\TaskStaffPerformanceReportBO.cs" />
    <Compile Include="REPORTS\UserExpenseReport\UserExpenseReportBO.cs" />
    <Compile Include="REPORTS\UserExpenseReport\UserExpenseUngroupedReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VAOutcomesAndActivities\VAPartVPlanOfSupportsBO.cs" />
    <Compile Include="REPORTS\VAOutcomesAndActivities\VAOutcomesAndActivities.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VAOutcomesAndActivities\VAPartVPlanOfSupports.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VAOutcomesAndActivities\VAOutcomesAndActivitiesBO.cs" />
    <Compile Include="REPORTS\VAPersonCenteredReview\VAPersonCenteredReview.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VAPersonCenteredReview\VAPersonCenteredReviewBO.cs" />
    <Compile Include="REPORTS\HealthCarePaymentBatchReport\HealthCarePaymentBatchReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\HealthCarePaymentBatchReport\HealthCarePaymentBatchReportBO.cs" />
    <Compile Include="REPORTS\DoctorOrderReport\DoctorOrderReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DoctorOrderReport\DoctorOrderReportBO.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetAllRecordsWithoutOverlapWithSums\EmployeeTimesheetAllRecordsWithoutOverlapWithSums.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployeeTimesheetAllRecordsWithoutOverlapWithSums\EmployeeTimesheetAllRecordsWithoutOverlapWithSumsBO.cs" />
    <Compile Include="REPORTS\TCFSS_PayrollReport\TCFSS_PayrollReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ConsumerNetworkCommunicationReport\ConsumerNetworkCommunicationReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\TCFSS_AccountingReport\TCFSS_AccountingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\GenericReport\GenericReportBO.cs" />
    <Compile Include="REPORTS\FormsSAServiceDeliveryPlanReport\FormSAServiceDeliveryPlanReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsSAServiceDeliveryPlanReport\FormsSAServiceDeliveryPlanReportBO.cs" />
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReportOld\FormsMedicaidWaiverSummaryReportOld.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReportOld\FormsMedicaidWaiverSummaryReportBOOld.cs" />
    <Compile Include="REPORTS\GenericReport\GenericReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MedicationReport\MedicationReportBO.cs" />
    <Compile Include="REPORTS\MedicationReport\MedicationReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsCHSROIReport\FormCHSROIReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsCHSROIReport\FormCHSROIReportBO.cs" />
    <Compile Include="REPORTS\StaffPrimaryContactWeightReport\StaffPrimaryContactWeightReportBO.cs" />
    <Compile Include="REPORTS\StaffPrimaryContactWeightReport\StaffPrimaryContactWeightReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffBirthdayReport\StaffBirthdayReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffBirthdayReport\StaffBirthdayReportBO.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceWGroupReport\EmployeeTimesheetBillableWithConsumerServiceWGroupReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceWGroupReport\EmployeeTimesheetBillableWithConsumerServiceWGroupReportBO.cs" />
    <Compile Include="REPORTS\PlacementProgramCountReport\PlacementProgramCountReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PlacementProgramCountReport\PlacementProgramCountReportBO.cs" />
    <Compile Include="REPORTS\TCMEmployeeTimesheetBillableWithConsumerServiceReport\TCMEmployeeTimesheetBillableWithConsumerServiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\TCMEmployeeTimesheetBillableWithConsumerServiceReport\TCMEmployeeTimesheetBillableWithConsumerServiceReportBO.cs" />
    <Compile Include="REPORTS\StaffServiceWithCodeGroupedTotalReport\StaffServiceWithCodeGroupedTotalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffServiceWithCodeGroupedTotalReport\StaffServiceWithCodeGroupedTotalReportBO.cs" />
    <Compile Include="REPORTS\StaffDobHireTenureReport\StaffDobHireTenureReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffDobHireTenureReport\StaffDobHireTenureReportBO.cs" />
    <Compile Include="REPORTS\MileageCompanyCarReport\MileageCompanyCarReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MileageCompanyCarReport\MileageCompanyCarReportBO.cs" />
    <Compile Include="REPORTS\MileageMonthlyReport\MileageMonthlyReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MileageMonthlyReport\MileageMonthlyReportBO.cs" />
    <Compile Include="REPORTS\FormsSemiAnnualAssessmentReport\FormsSemiAnnualAssessmentReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsSemiAnnualAssessmentReport\FormsSemiAnnualAssessmentReportBO.cs" />
    <Compile Include="REPORTS\JobAnalysisReport\JobAnalysisReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobAnalysisReport\JobAnalysisReportBO.cs" />
    <Compile Include="REPORTS\NotificationReport\NotificationReportBO.cs" />
    <Compile Include="REPORTS\NotificationReport\NotificationReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeMeasurementReport\OutcomeMeasurementDrilldownReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffExpirationFieldReport\StaffExpirationFieldReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffExpirationFieldReport\StaffExpirationFieldReportBO.cs" />
    <Compile Include="REPORTS\GoalCompletionReport\GoalCompletionReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\GoalCompletionReport\GoalCompletionReportBO.cs" />
    <Compile Include="REPORTS\OutcomeMeasurementReport\OutcomeMeasurementReportBO.cs" />
    <Compile Include="REPORTS\OutcomeMeasurementReport\OutcomeMeasurementReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DemographicsReport\DemographicsReportBO.cs" />
    <Compile Include="REPORTS\DemographicsReport\DemographicsReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DistributionListReport\DistributionListReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\DistributionListReport\DistributionListReportBO.cs" />
    <Compile Include="REPORTS\EarlyClosingReport\EarlyClosingReportBO.cs" />
    <Compile Include="REPORTS\EarlyClosingReport\EarlyClosingReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportAllergy\EmergencyReportAllergy.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportBO.cs" />
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportContacts\EmergencyReportContacts.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportDoctor\EmergencyReportDoctor.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportHospital\EmergencyReportHospital.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportMedication\EmergencyReportMedication.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportSeizure\EmergencyReportSeizure.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportComplete\EmergencyReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportFactSheet\EmergencyFactSheetBackside.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportFactSheet\EmergencyFactSheetReportReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmergencyReportFactSheet\EmergencyFactSheetReportReportBO.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetBillableReport\EmloyeeTimesheetBillableReportBusinessObject.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetBillableReport\EmployeeTimesheetBillableReportTelerik2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceReport\EmployeeTimesheetBillableWithConsumerServiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceReport\EmployeeTimesheetBillableWithConsumerServiceReportBO.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetReport\EmloyeeTimesheetReportBusinessObject.cs" />
    <Compile Include="REPORTS\EmployeeTimesheetReport\EmployeeTimesheetReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerAveryMailingLabels\EmployerAveryMailingReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerAveryMailingLabels\EmployerAveryMailingReportBO.cs" />
    <Compile Include="REPORTS\EmployerCreatedReport\EmployerCreatedReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerCreatedReport\EmployerCreatedReportBO.cs" />
    <Compile Include="REPORTS\EmployerDetailedContactReport\EmployerDetailedContactBO.cs" />
    <Compile Include="REPORTS\EmployerDetailedContactReport\EmployerDetailedContactReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerEmailAndPhoneReport\EmployerEmailAndPhoneReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerEmailAndPhoneReport\EmployerEmailAndPhoneReportBO.cs" />
    <Compile Include="REPORTS\EmployerExportReport\EmployerExportReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerExportReport\EmployerExportReportBO.cs" />
    <Compile Include="REPORTS\EmployerJobAnalysisReport\EmployerJobAnalysisReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerJobAnalysisReport\EmployerJobAnalysisReportBO.cs" />
    <Compile Include="REPORTS\EmployerJobContactSourceReport\EmployerJobContactSourceReportBO.cs" />
    <Compile Include="REPORTS\EmployerJobContactSourceReport\EmployerJobContactSourceReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerNotesReport\EmployerNotesReportBO.cs" />
    <Compile Include="REPORTS\EmployerNotesReport\EmployerNotesReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\EmployerWorkplaceRelationshipHistoryReport\EmployerWorkplaceRelationshipHistoryReportBO.cs" />
    <Compile Include="REPORTS\EmployerWorkplaceRelationshipHistoryReport\EmployerWorkplaceRelationshipHistoryReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ErrorReport\MostErrorsReportBO.cs" />
    <Compile Include="REPORTS\ErrorReport\MostErrorsReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ExpirationFieldReport\ExpirationFieldReportBO.cs" />
    <Compile Include="REPORTS\ExpirationFieldReport\ExpirationFieldReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsIWPQuestionnaireResult\IWPQuestionnaireResult2BO.cs" />
    <Compile Include="REPORTS\FormsIWPQuestionnaireResult\IWPQuestionnaireResultBO.cs" />
    <Compile Include="REPORTS\FormsIWPQuestionnaireResult\IWPQuestionnaireResultTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportBO.cs" />
    <Compile Include="REPORTS\FormsMonthlyProgressReview\FormsMonthlyProgressReview.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMonthlyProgressReview\FormsMonthlyProgressReviewBO.cs" />
    <Compile Include="REPORTS\FormsMonthlySummaryReport\FormsMonthlySummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsMonthlySummaryReport\FormsMonthlySummaryReportBO.cs" />
    <Compile Include="REPORTS\FormsRCDDSMonthlyProviderSummaryReport\FormsRCDDSMonthlyProviderSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsRCDDSMonthlyProviderSummaryReport\FormsRCDDSMonthlyProviderSummaryReportBO.cs" />
    <Compile Include="REPORTS\FormsReport\DynamicEmployerFormsReport\DynamicEmployerFormsReportBusinessObject.cs" />
    <Compile Include="REPORTS\FormsReport\DynamicEmployerFormsReport\DynamicEmployerFormsReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormsReport\DynamicFormsReport\DynamicFormsReportBusinessObject.cs" />
    <Compile Include="REPORTS\FormsReport\DynamicFormsReport\DynamicFormsReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormStatusReport\FormStatusReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FormStatusReport\FormStatusReportBO.cs" />
    <Compile Include="REPORTS\FundingSourceContactConsumerReport\FundingSourceContactConsumerReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FundingSourceContactConsumerReport\FundingSourceContactConsumerReportBO.cs" />
    <Compile Include="REPORTS\FundingSourceContactHistoryReport\FundingSourceContactHistoryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\FundingSourceContactHistoryReport\FundingSourceContactHistoryReportBO.cs" />
    <Compile Include="REPORTS\InactiveConsumersReport\InactiveConsumersReportBO.cs" />
    <Compile Include="REPORTS\InactiveConsumersReport\InactiveConsumersReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\IncidentReport\IncidentReportBO.cs" />
    <Compile Include="REPORTS\IncidentReport\IncidentReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\InterventionTimesheetReportWithOffSite\InterventionReportWithOffSiteTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\InterventionTimesheetReportWithOffSite\InterventionTimesheetWithOffSiteBusinessObject.cs" />
    <Compile Include="REPORTS\InterventionTimesheetReport\InterventionReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\InterventionTimesheetReport\InterventionReportTelerik2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\InterventionTimesheetReport\InterventionTimesheetBusinessObject.cs" />
    <Compile Include="REPORTS\InterventionTimesheetReport\InterventionTimesheetBusinessObject2.cs" />
    <Compile Include="REPORTS\JobContactsRecallReport\JobContactRecallReportBusinessObject.cs" />
    <Compile Include="REPORTS\JobContactsRecallReport\JobContactsRecallReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobContactsReport\JobContactReportBusinessObject.cs" />
    <Compile Include="REPORTS\JobContactsReport\JobContactsReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobContactsReport\JobContactsReportTelerikExcel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\JobDevelopmentOnlineContacts\JobDevelopmentContacts.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\JobDevProgressReportSQLDirect.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\JobDevProgressReportTHISSUCKSAGAIN.cs" />
    <Compile Include="REPORTS\JobDevelopmentProgressReport\TEMP\EmployerCaseNotes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevDesign.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevelopmentProgressReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevelopmentProgressReportBO.cs" />
    <Compile Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevelopmentProgressReportSimple.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevelopmentProgressReportTelerikSample.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevTest.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\JobPlacementReport\JobPlacementReportBusinessObject.cs" />
    <Compile Include="REPORTS\JobPlacementReport\JobPlacementReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ManagementAggregateReport\ManagementAggregateReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ManagementAggregateReport\ManagementAggregateReportBO.cs" />
    <Compile Include="REPORTS\ManagementDepartmentProductivityReport\ManagementDepartmentProductivityReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ManagementDepartmentProductivityReport\ManagementDepartmentProductivityReportBO.cs" />
    <Compile Include="REPORTS\ManagementReport\ManagementReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ManagementReport\ManagementReportBO.cs" />
    <Compile Include="REPORTS\MedicaidReport\MedicaidReportBO.cs" />
    <Compile Include="REPORTS\MedicaidReport\MedicationReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MedicalRosterReport\MedicalRosterBO.cs" />
    <Compile Include="REPORTS\MedicalRosterReport\MedicalRosterReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MileageReportSummarized\MileageReportSummarizedBO.cs" />
    <Compile Include="REPORTS\MileageReportSummarized\MileageReportSummarizedClockNumberTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MileageReportSummarized\MileageReportSummarizedTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MileageReport\MileageReport_CostCenterFromAuthContractBO.cs" />
    <Compile Include="REPORTS\MileageReport\MileageReportExcel2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MileageReport\MileageReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport\MOSEMonthlyJobSupportsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsReport\MOSEMonthlyJobSupportsReportBO.cs" />
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport\MOSEMonthlyJobSupportsSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport\MOSEMonthlyJobSupportsSummaryReportBO.cs" />
    <Compile Include="REPORTS\NCSEJobDevelopmentMilestonePaymentRequestForm\NCSEJobDevelopmentMilestonePaymentRequestForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\NCSEJobDevelopmentMilestonePaymentRequestForm\NCSEJobDevelopmentMilestonePaymentRequestFormBO.cs" />
    <Compile Include="REPORTS\NCWAJobDevelopmentMilestonePaymentRequestForm\NCWAJobDevelopmentMilestonePaymentRequestForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\NCWAJobDevelopmentMilestonePaymentRequestForm\NCWAJobDevelopmentMilestonePaymentRequestFormBO.cs" />
    <Compile Include="REPORTS\NonAspirinReport\NonAspirinReportBO.cs" />
    <Compile Include="REPORTS\NonAspirinReport\NonAspirinReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\NoResultsReport\NoResultsReportBO.cs" />
    <Compile Include="REPORTS\NoResultsReport\NoResultsTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedBillingReport\OutcomeBasedBillingReportBusinessObject.cs" />
    <Compile Include="REPORTS\OutcomeBasedBillingReport\OutcomeBasedBillingReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedBillingReport\OutcomeBasedBillingSubReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedReport\OutcomeBasedExportReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedReport\OutcomeBasedReportBO.cs" />
    <Compile Include="REPORTS\OutcomeBasedReport\OutcomeBasedReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedServiceReport\OutcomeBasedServiceReportBO.cs" />
    <Compile Include="REPORTS\OutcomeBasedServiceReport\OutcomeBasedServiceReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedTotalsReport\OutcomeBasedTotalsExportReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\OutcomeBasedTotalsReport\OutcomeBasedTotalsReportBO.cs" />
    <Compile Include="REPORTS\OutcomeMeasurementReportLOQW\OutcomeMeasurementReportLOQWBO.cs" />
    <Compile Include="REPORTS\OutcomeMeasurementReportLOQW\OutcomeMeasurementReportLOQWTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PayrollReport\PayrollReportBusinessObject.cs" />
    <Compile Include="REPORTS\PayrollReport\PayrollReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PhotoConsentReport\PhotoConsentReportBO.cs" />
    <Compile Include="REPORTS\PhotoConsentReport\PhotoConsentReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PlacementFollowUpFormReport\PlacementFollowUpFormReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PlacementFollowUpFormReport\PlacementFollowUpFormReportBO.cs" />
    <Compile Include="REPORTS\PlacementFollowUpFullFormReport\PlacementFollowUpFullFormReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PlacementFollowUpFullFormReport\PlacementFollowUpFullFormReportBO.cs" />
    <Compile Include="REPORTS\PlacementWagePartTimeReport\PlacementWagePartTimeReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\PlacementWagePartTimeReport\PlacementWagePartTimeReportBO.cs" />
    <Compile Include="REPORTS\QuickBooksInvoiceReport\QuickBooksInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksInvoiceReport\QuickBooksInvoiceReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksInvoiceReport\QuickBooksInvoiceReportBO.cs" />
    <Compile Include="REPORTS\QuickBooksTimesheetOvertimeReport\QuickBooksTimesheetOvertimeReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksTimesheetOvertimeReport\QuickBooksTimesheetOvertimeReportBO.cs" />
    <Compile Include="REPORTS\QuickBooksTimesheetReport\QuickBooksTimesheetReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\QuickBooksTimesheetReport\QuickBooksTimesheetReportBO.cs" />
    <Compile Include="REPORTS\ResidentialRosterReport\ResidentialRosterReportBO.cs" />
    <Compile Include="REPORTS\ResidentialRosterReport\ResidentialRosterReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportBO.cs" />
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportDiabetic.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportDiabeticBO.cs" />
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportDietaryNeeds.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportDietaryNeedsBO.cs" />
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportEatingOversight.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportEatingOversightBO.cs" />
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportNonAspirin.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportNonAspirinBO.cs" />
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportOnlyBO.cs" />
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\RestrictionReport\RestrictionReportTelerikRestrictionOnly.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\SeizureReport\SeizureReportBO.cs" />
    <Compile Include="REPORTS\SeizureReport\SeizureReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ServiceCountReport\ServiceCountReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\ServiceCountReport\ServiceCountReportBO.cs" />
    <Compile Include="REPORTS\StaffAuditReport\StaffAuditReportBO.cs" />
    <Compile Include="REPORTS\StaffAuditReport\StaffAuditReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffAveryMailingLabels\StaffAveryMailingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffAveryMailingLabels\StaffAveryMailingReportBO.cs" />
    <Compile Include="REPORTS\StaffCoverageReport\StaffCoverageReportBO.cs" />
    <Compile Include="REPORTS\StaffCoverageReport\StaffCoverageReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffEducationReport\StaffEducationReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffEducationReport\StaffEducationReportBO.cs" />
    <Compile Include="REPORTS\StaffExpirationReport\StaffExpirationReportBO.cs" />
    <Compile Include="REPORTS\StaffExpirationReport\StaffExpirationReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffHoursOverviewReport\StaffHoursOverviewPercentageReportBO.cs" />
    <Compile Include="REPORTS\StaffHoursOverviewReport\StaffHoursOverviewReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffHoursOverviewReport\StaffHoursOverviewReportBO.cs" />
    <Compile Include="REPORTS\StaffNoteReport\StaffNoteReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffNoteReport\StaffNoteReportBO.cs" />
    <Compile Include="REPORTS\StaffPaymentReportCustomServiceRate\StaffPaymentReportCustomServiceRate.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffPaymentReportCustomServiceRate\StaffPaymentReportCustomServiceRateBO.cs" />
    <Compile Include="REPORTS\StaffProductionReport\StaffProductionReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffProductionReport\StaffProductionReportBO.cs" />
    <Compile Include="REPORTS\StaffRoleDepartmentReport\StaffRoleDepartmentReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffRoleDepartmentReport\StaffRoleDepartmentReportBO.cs" />
    <Compile Include="REPORTS\StaffServiceTotalReport\StaffServiceTotalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffServiceTotalReport\StaffServiceTotalReportBO.cs" />
    <Compile Include="REPORTS\StaffServiceTotalWithOutcomesReport\StaffServiceTotalWithOutcomesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffServiceTotalWithOutcomesReport\StaffServiceTotalWithOutcomesReportBO.cs" />
    <Compile Include="REPORTS\StaffServiceWithNonBillableTotalReport\StaffServiceWithNonBillableTotalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffServiceWithNonBillableTotalReport\StaffServiceWithNonBillableTotalReportBO.cs" />
    <Compile Include="REPORTS\StaffTimesheetExportReport\StaffTimesheetExportReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\StaffTimesheetExportReport\StaffTimesheetExportReportBO.cs" />
    <Compile Include="REPORTS\TextFieldReport\TextFieldReportBO.cs" />
    <Compile Include="REPORTS\TextFieldReport\TextFieldReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VisualVaultPreBillingReport\VisualVaultPreBillingReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VisualVaultPreBillingReport\VisualVaultPreBillingReportBO.cs" />
    <Compile Include="REPORTS\VRCBAInterventionTimesheet\VRCBAInterventionTimesheet.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VRCBAInterventionTimesheet\VRCBAInterventionTimesheetBO.cs" />
    <Compile Include="REPORTS\VRCBAInterventionTimesheet\VRCBAInterventionTimesheetHeaderBO.cs" />
    <Compile Include="REPORTS\VRSEInterventionTimesheet\VRSEInterventionTimesheet.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VRSEInterventionTimesheet\VRSEInterventionTimesheetBO.cs" />
    <Compile Include="REPORTS\VRSEInterventionTimesheet\VRSEInterventionTimesheetHeaderBO.cs" />
    <Compile Include="REPORTS\VRWAInterventionTimesheet\VRWAInterventionTimesheet.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORTS\VRWAInterventionTimesheet\VRWAInterventionTimesheetBO.cs" />
    <Compile Include="REPORTS\VRWAInterventionTimesheet\VRWAInterventionTimesheetHeaderBO.cs" />
    <Compile Include="REPORTS\WorkHistoryReport\WorkHistoryReportBO.cs" />
    <Compile Include="REPORTS\WorkHistoryReport\WorkHistoryReportTelerik.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\AuthLimitExceptionHours.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\HtmlTextBoxHelper.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ExpirationFieldReports.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\GeneralReportFunctions.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ProviderImagesWrapper.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ReportDateComparisons.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ReportDateTimeManipulation.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ReportMath.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ReportAgeingCalculations.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ReportServiceDayRate.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ReportSiteLabel.cs" />
    <Compile Include="REPORT_CUSTOM_FUNCTIONS\ReportTime.cs" />
    <Compile Include="REPORT_INTERFACES\UCP_Abila_BillingInterface\UCP_Abila_BillingInterface.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="REPORT_INTERFACES\UCP_Abila_BillingInterface\UCP_Abila_BillingInterfaceBO.cs" />
    <Compile Include="Settings.cs" />
    <Compile Include="Utilities\FileHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="REPORTS\AccidentReport\AccidentReportTelerik.resx">
      <DependentUpon>AccidentReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordAppointmentConversionReportStaff\ActivityRecordAppointmentConversionReportStaff.resx">
      <DependentUpon>ActivityRecordAppointmentConversionReportStaff.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordAppointmentConversionReport\ActivityRecordAppointmentConversionReport.resx">
      <DependentUpon>ActivityRecordAppointmentConversionReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordAuditReport\ActivityRecordAuditReportTelerik.resx">
      <DependentUpon>ActivityRecordAuditReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordNonBillableWithConsumerReport\ActivityRecordNonBillableWithConsumerReportTelerik.resx">
      <DependentUpon>ActivityRecordNonBillableWithConsumerReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordNonBillableWithConsumerReport\ActivityRecordsNonBillableWithConsumerReport.resx">
      <DependentUpon>ActivityRecordsNonBillableWithConsumerReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingReportDayRateExceptionStatus.resx">
      <DependentUpon>ActivityRecordsBillingReportDayRateExceptionStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingReportDayRateNoMaxExtraColumnsUnrounded.resx">
      <DependentUpon>ActivityRecordsBillingReportDayRateNoMaxExtraColumnsUnrounded.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingReportDayRateExtraColumnsUnrounded.resx">
      <DependentUpon>ActivityRecordsBillingReportDayRateExtraColumnsUnrounded.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReportDayRate\ActivityRecordsBillingMonthlyInvoiceReportWithFundingSourceContact.resx">
      <DependentUpon>ActivityRecordsBillingMonthlyInvoiceReportWithFundingSourceContact.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingInvoiceReportWithFundingSourceContact.resx">
      <DependentUpon>ActivityRecordsBillingInvoiceReportWithFundingSourceContact.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingInvoiceReport.resx">
      <DependentUpon>ActivityRecordsBillingInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReport.resx">
      <DependentUpon>ActivityRecordsBillingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReport2.resx">
      <DependentUpon>ActivityRecordsBillingReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReportExtendedExcel.resx">
      <DependentUpon>ActivityRecordsBillingReportExtendedExcel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingReport\ActivityRecordsBillingReportExtendedExcelNEBAFIXED.resx">
      <DependentUpon>ActivityRecordsBillingReportExtendedExcelNEBAFIXED.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsGroupReport\ActivityRecordsGroupReportMultiMonth.resx">
      <DependentUpon>ActivityRecordsGroupReportMultiMonth.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTrackingAddCommentsWithPictures\ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsWithPicturesStandAlone.resx">
      <DependentUpon>ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsWithPicturesStandAlone.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAssessmentKeyDateReport\ActivityRecordsInterventionAssessmentKeyDateReport.resx">
      <DependentUpon>ActivityRecordsInterventionAssessmentKeyDateReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationOverviewSimpleReport\AuthorizationOverviewSimpleReport.resx">
      <DependentUpon>AuthorizationOverviewSimpleReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\BenefitPlanningBillingReport\BenefitPlanningBillingReport.resx">
      <DependentUpon>BenefitPlanningBillingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\BrokerageServicesReport\BrokerageServicesReport.resx">
      <DependentUpon>BrokerageServicesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CAGroupInvoiceReport\CAGroupInvoiceReport.resx">
      <DependentUpon>CAGroupInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CAIPInvoiceReport\CAAdultInvoiceReport.resx">
      <DependentUpon>CAAdultInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByTaskWithRules.resx">
      <DependentUpon>ConsumerAgencyProductionReportGroupedByTaskWithRules.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerProductionReport\ConsumerProductionEarningsReport.resx">
      <DependentUpon>ConsumerProductionEarningsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionEarningsReportGroupedByConsumer.resx" />
    <EmbeddedResource Include="REPORTS\ConsumerProductionReport\ConsumerProductionPayStubsReportWithRounding.resx">
      <DependentUpon>ConsumerProductionPayStubsReportWithRounding.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerProductionReport\ConsumerProductionPayStubsReport.resx">
      <DependentUpon>ConsumerProductionPayStubsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByConsumerWithRules.resx">
      <DependentUpon>ConsumerAgencyProductionReportGroupedByConsumerWithRules.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByDepartment.resx" />
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportWithRules.resx">
      <DependentUpon>ConsumerAgencyProductionReportWithRules.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByConsumer.resx">
      <DependentUpon>ConsumerAgencyProductionReportGroupedByConsumer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceReport\EmployeeTimesheetBillableWithConsumerServiceExcelUngroupedReport.resx">
      <DependentUpon>EmployeeTimesheetBillableWithConsumerServiceExcelUngroupedReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportWithGoalsAndCommunityIntegrationLocations.resx">
      <DependentUpon>FormsMedicaidWaiverSummaryReportWithGoalsAndCommunityIntegrationLocations.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportWithGoals.resx">
      <DependentUpon>FormsMedicaidWaiverSummaryReportWithGoals.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageReport\MileageReportExcel3.resx">
      <DependentUpon>MileageReportExcel3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsReport_V4\MOSEMonthlyJobSupportsReport_V4.resx">
      <DependentUpon>MOSEMonthlyJobSupportsReport_V4.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsReport_V5\MOSEMonthlyJobSupportsReport_V5.resx">
      <DependentUpon>MOSEMonthlyJobSupportsReport_V5.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V4\MOSEMonthlyJobSupportsSummaryReport_V4.resx">
      <DependentUpon>MOSEMonthlyJobSupportsSummaryReport_V4.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V5\MOSEMonthlyJobSupportsSummaryReport_V5.resx">
      <DependentUpon>MOSEMonthlyJobSupportsSummaryReport_V5.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NetworkContactAveryMailingLabels\NetworkContactAveryMailingReport.resx">
      <DependentUpon>NetworkContactAveryMailingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomesBasedPaymentsReportNDI\OutcomesBasedPaymentsReportNDI.resx">
      <DependentUpon>OutcomesBasedPaymentsReportNDI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PointInTimeRAReport\PointInTimeRAReport.resx">
      <DependentUpon>PointInTimeRAReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\RequestDetailsReport\RequestDetailsReport.resx">
      <DependentUpon>RequestDetailsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DistAndDemoEquipmentReport\DistAndDemoEquipmentReport.resx">
      <DependentUpon>DistAndDemoEquipmentReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerCertificationsReport\ConsumerCertificationsReport.resx">
      <DependentUpon>ConsumerCertificationsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerDepartmentFinanceReport\ConsumerFinanceReportWithReceiptImages.resx">
      <DependentUpon>ConsumerFinanceReportWithReceiptImages.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DepartmentFinanceReport\DepartmentFinanceReport.resx">
      <DependentUpon>DepartmentFinanceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceReport\EmployeeTimesheetBillableWithConsumerServiceExcelReport.resx">
      <DependentUpon>EmployeeTimesheetBillableWithConsumerServiceExcelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerContactReport\NDIContactExcelReport.resx">
      <DependentUpon>NDIContactExcelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DemographicsReport\NDIDemographicsReportTelerik.resx">
      <DependentUpon>NDIDemographicsReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EVVReconciliationReport\EVVReconciliationReport.resx">
      <DependentUpon>EVVReconciliationReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsGenericMedicaidWaiverSummaryReport.resx">
      <DependentUpon>FormsGenericMedicaidWaiverSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsRequiredReport\FormsRequiredReport.resx">
      <DependentUpon>FormsRequiredReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReport2WithStaffFocusAndService\ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended2.resx">
      <DependentUpon>ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesNoStaffCIMOR.resx">
      <DependentUpon>ActivityRecordsUnitReportWithRulesNoStaffCIMOR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AppointmentDepartmentCalendarReport\AppointmentDepartmentCalendarReport.resx">
      <DependentUpon>AppointmentDepartmentCalendarReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationOverviewWithDatesAndFrequenciesReport\AuthorizationOverviewWithDatesAndFrequenciesAndUnitsReport.resx">
      <DependentUpon>AuthorizationOverviewWithDatesAndFrequenciesAndUnitsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DDaPFile\DDaPFile.resx">
      <DependentUpon>DDaPFile.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DDSEmergencyFactSheetReport\DDSEmergencyFactSheetReport.resx">
      <DependentUpon>DDSEmergencyFactSheetReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterAccessReport\ServiceAlternativesMonthlyProgressReport.resx">
      <DependentUpon>ServiceAlternativesMonthlyProgressReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportWithSignature.resx">
      <DependentUpon>FormsMedicaidWaiverSummaryReportWithSignature.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReportBlankSignature.resx">
      <DependentUpon>FormsMedicaidWaiverSummaryReportBlankSignature.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\HealthCarePaymentAgeingReport\SageIntacctAccRecInvoicesLimited.resx">
      <DependentUpon>SageIntacctAccRecInvoicesLimited.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\HealthCarePaymentKeystoneMSDynamicsInterface\HealthCarePaymentKeystoneMSDynamicsInterfaceReport.resx">
      <DependentUpon>HealthCarePaymentKeystoneMSDynamicsInterfaceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MonthlyActivityRecordBillingReport\MonthlyActivityRecordBillingReport.resx">
      <DependentUpon>MonthlyActivityRecordBillingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsReport_V2\MOSEMonthlyJobSupportsReport_V2.resx">
      <DependentUpon>MOSEMonthlyJobSupportsReport_V2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\GenericStringReport\GenericStringReport.resx">
      <DependentUpon>GenericStringReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CAIPInvoiceReport\CAIPInvoiceReport.resx">
      <DependentUpon>CAIPInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsReport_V3\MOSEMonthlyJobSupportsReport_V3.resx">
      <DependentUpon>MOSEMonthlyJobSupportsReport_V3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport_V3\MOSEMonthlyJobSupportsSummaryReport_V3.resx">
      <DependentUpon>MOSEMonthlyJobSupportsSummaryReport_V3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OregonEXPRS\OregonEXPRSEVV.resx">
      <DependentUpon>OregonEXPRSEVV.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ProductionReport\ProductionReport.resx">
      <DependentUpon>ProductionReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\QuickBooksAccountingInvoiceExportIIFReport\QuickBooksAccountingInvoiceExportIIFReport.resx">
      <DependentUpon>QuickBooksAccountingInvoiceExportIIFReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\QuickBooksTimesheetNonbillableAndCostCenterReport\QuickBooksTimesheetNonbillableAndCostCenterReport.resx">
      <DependentUpon>QuickBooksTimesheetNonbillableAndCostCenterReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBasedReport\NDIOutcomeBasedReportTelerik.resx">
      <DependentUpon>NDIOutcomeBasedReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\TaskStaffPerformanceWithTaskList\TaskStaffPerformanceWithTaskListTelerik.resx">
      <DependentUpon>TaskStaffPerformanceWithTaskListTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\Tier3ScreeningReport\Tier3ScreeningReport.resx">
      <DependentUpon>Tier3ScreeningReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\Tier3ServiceReport\Tier3ServiceReport.resx">
      <DependentUpon>Tier3ServiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\UserExpenseReportSubtotals\UserExpenseReportSubtotals.resx">
      <DependentUpon>UserExpenseReportSubtotals.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\WarrantyReport\WarrantyReport.resx">
      <DependentUpon>WarrantyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetAllRecordsWithoutOverlapWithSums\EmployeeTimesheetAllRecordsWithoutOverlapWithSumsWithPayrollCode.resx">
      <DependentUpon>EmployeeTimesheetAllRecordsWithoutOverlapWithSumsWithPayrollCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterAccessReport\GompersQuarterlyReport.resx">
      <DependentUpon>GompersQuarterlyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReportUnitSummary\ActivityRecordsReportUnitSummaryWithClock.resx">
      <DependentUpon>ActivityRecordsReportUnitSummaryWithClock.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsBillingWithGoalReport\ActivityRecordsBillingWithGoalReport.resx">
      <DependentUpon>ActivityRecordsBillingWithGoalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsHistoryReport\ActivityRecordsHistoryReport.resx">
      <DependentUpon>ActivityRecordsHistoryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTracking\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTracking.resx">
      <DependentUpon>ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTracking.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthly\ActivityRecordsInterventionAndAssessmentMonthly.resx">
      <DependentUpon>ActivityRecordsInterventionAndAssessmentMonthly.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthly\ActivityRecordsInterventionAndAssessmentMonthlyAddComments.resx">
      <DependentUpon>ActivityRecordsInterventionAndAssessmentMonthlyAddComments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAssessmentKeyPercentageReport\ActivityRecordsInterventionAssessmentKeyPercentageReport.resx">
      <DependentUpon>ActivityRecordsInterventionAssessmentKeyPercentageReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAssessmentKeyPercentageReport\ActivityRecordsInterventionAssessmentKeyPercentageReport2.resx" />
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAssessmentKeyPercentageReport\ActivityRecordsInterventionAssessmentKeyPercentageReportHeaderRepeat.resx">
      <DependentUpon>ActivityRecordsInterventionAssessmentKeyPercentageReportHeaderRepeat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAssessmentKeyReport\ActivityRecordsInterventionAssessmentKeyReport.resx">
      <DependentUpon>ActivityRecordsInterventionAssessmentKeyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsMultiGoalReport\ActivityRecordsMultiGoalReport.resx">
      <DependentUpon>ActivityRecordsMultiGoalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsMultiGoalReport\ActivityRecordsMultiGoalWithoutStrategiesReport.resx">
      <DependentUpon>ActivityRecordsMultiGoalWithoutStrategiesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsNonBillableReport\ActivityRecordsNonBillableReport.resx">
      <DependentUpon>ActivityRecordsNonBillableReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsNonBillableReport\ActivityRecordsNonBillableReport2.resx">
      <DependentUpon>ActivityRecordsNonBillableReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReport2\ActivityRecordsReportTelerik2.resx">
      <DependentUpon>ActivityRecordsReportTelerik2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReportReconciliation\ActivityRecordsReportReconciliationTelerikExcel.resx">
      <DependentUpon>ActivityRecordsReportReconciliationTelerikExcel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReportReconciliation\ActivityRecordsReportReconciliationTelerik.resx">
      <DependentUpon>ActivityRecordsReportReconciliationTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReportSpreadsheetWithCAPStaffAndService\ActivityRecordsReportSpreadsheetWithCAPStaffAndService.resx">
      <DependentUpon>ActivityRecordsReportSpreadsheetWithCAPStaffAndService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReportUnitSummary\ActivityRecordsReportUnitSummary.resx">
      <DependentUpon>ActivityRecordsReportUnitSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReportWithoutHoursAndMRVR\ActivityRecordsReportWithoutHoursAndMRVRTelerik.resx">
      <DependentUpon>ActivityRecordsReportWithoutHoursAndMRVRTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsReport\ActivityRecordsReportTelerik.resx">
      <DependentUpon>ActivityRecordsReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsSumsReport\ActivityRecordsSummaryReport.resx">
      <DependentUpon>ActivityRecordsSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsSumsReport\ActivityRecordsSumsReport.resx">
      <DependentUpon>ActivityRecordsSumsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMStaffHoursReport\ActivityRecordsTCMStaffHoursReport.resx">
      <DependentUpon>ActivityRecordsTCMStaffHoursReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReport2WithStaffAndService\ActivityRecordsUnitReport2WithStaffAndService.resx">
      <DependentUpon>ActivityRecordsUnitReport2WithStaffAndService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReport2WithStaffFocusAndService\ActivityRecordsUnitReport2WithStaffFocusAndService.resx">
      <DependentUpon>ActivityRecordsUnitReport2WithStaffFocusAndService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReport2WithStaffFocusAndService\ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended.resx">
      <DependentUpon>ActivityRecordsUnitReport2WithStaffFocusAndServiceExtended.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReport2\ActivityRecordsUnitReport2.resx">
      <DependentUpon>ActivityRecordsUnitReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReport2\ActivityRecordsUnitContractReport.resx">
      <DependentUpon>ActivityRecordsUnitContractReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithAuthAndPlaceOfService\ActivityRecordsUnitReportWithAuthAndPlaceOfService.resx">
      <DependentUpon>ActivityRecordsUnitReportWithAuthAndPlaceOfService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithAuthAndEmployer\ActivityRecordsUnitReportWithAuthAndEmployerCHS.resx">
      <DependentUpon>ActivityRecordsUnitReportWithAuthAndEmployerCHS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithAuth\ActivityRecordsUnitReportWithAuthCustomIDAndTotals.resx">
      <DependentUpon>ActivityRecordsUnitReportWithAuthCustomIDAndTotals.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithAuth\ActivityRecordsUnitReportWithAuth.resx">
      <DependentUpon>ActivityRecordsUnitReportWithAuth.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithPlacement.resx">
      <DependentUpon>ActivityRecordsUnitReportWithPlacement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesHours.resx">
      <DependentUpon>ActivityRecordsUnitReportWithRulesHours.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRules.resx">
      <DependentUpon>ActivityRecordsUnitReportWithRules.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesAndInAndOutTimesGroupedByConsumer.resx">
      <DependentUpon>ActivityRecordsUnitReportWithRulesAndInAndOutTimesGroupedByConsumer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesAndInAndOutTimes.resx">
      <DependentUpon>ActivityRecordsUnitReportWithRulesAndInAndOutTimes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesNoStaff.resx">
      <DependentUpon>ActivityRecordsUnitReportWithRulesNoStaff.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithRules\ActivityRecordsUnitReportWithRulesNoStaffHours.resx">
      <DependentUpon>ActivityRecordsUnitReportWithRulesNoStaffHours.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AnnualReviewDueReport\AnnualReviewDueReport.resx">
      <DependentUpon>AnnualReviewDueReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AppointmentAuthAndConsumerWorkCalendarReport\AppointmentAuthAndConsumerWorkCalendarReport.resx">
      <DependentUpon>AppointmentAuthAndConsumerWorkCalendarReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AppointmentCalendarReport\AppointmentCalendarReport.resx">
      <DependentUpon>AppointmentCalendarReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AppointmentConsumerCalendarReport\AppointmentConsumerCalendarReport.resx">
      <DependentUpon>AppointmentConsumerCalendarReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AppointmentScheduleReport\AppointmentScheduleReport.resx">
      <DependentUpon>AppointmentScheduleReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AttendanceFTEReport\AttendanceFTEReport.resx">
      <DependentUpon>AttendanceFTEReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationAggregateReport\AuthorizationAggregateReport.resx">
      <DependentUpon>AuthorizationAggregateReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationExceededReport\AuthorizationExceededReport.resx">
      <DependentUpon>AuthorizationExceededReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationGroupOverviewReport\AuthorizationGroupOverviewReport.resx">
      <DependentUpon>AuthorizationGroupOverviewReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationLimitHoursUsedReport\AuthorizationLimitHoursUsedReport.resx" />
    <EmbeddedResource Include="REPORTS\AuthorizationLimitHoursUsedReport\AuthorizationLimitHoursUsedReport2.resx">
      <DependentUpon>AuthorizationLimitHoursUsedReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReport.resx" />
    <EmbeddedResource Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReport2.resx">
      <DependentUpon>AuthorizationLimitReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitExtendedReport.resx">
      <DependentUpon>AuthorizationLimitExtendedReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReport3Extended.resx">
      <DependentUpon>AuthorizationLimitReport3Extended.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationLimitReport\AuthorizationLimitReport3.resx">
      <DependentUpon>AuthorizationLimitReport3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationOverviewReport\AuthorizationOverviewReport.resx">
      <DependentUpon>AuthorizationOverviewReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationOverviewWithDatesAndFrequenciesReport\AuthorizationOverviewWithDatesAndFrequenciesReport.resx">
      <DependentUpon>AuthorizationOverviewWithDatesAndFrequenciesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationPercentageLeftReport\AuthorizationPercentageLeftReport.resx">
      <DependentUpon>AuthorizationPercentageLeftReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationReportBillable\AuthorizationReportTelerikBillable.resx">
      <DependentUpon>AuthorizationReportTelerikBillable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationReportBillable\AuthorizationReportTelerikBillable2.resx">
      <DependentUpon>AuthorizationReportTelerikBillable2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationReportGoals\AuthorizationReportTelerikGoal.resx">
      <DependentUpon>AuthorizationReportTelerikGoal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationReportGoals\CareerDiscoveryReport.resx" />
    <EmbeddedResource Include="REPORTS\AuthorizationReportTemporary\AuthorizationReportTemporaryTelerik.resx">
      <DependentUpon>AuthorizationReportTemporaryTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\AuthorizationRequestSummaryReport\AuthorizationRequestSummaryReport.resx">
      <DependentUpon>AuthorizationRequestSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CAPBimonthlyTimesheetReport\CAPBiMonthlyTimesheetReport3.resx">
      <DependentUpon>CAPBiMonthlyTimesheetReport3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CAPBimonthlyTimesheetReport\CAPBiMonthlyTimesheetReport4.resx">
      <DependentUpon>CAPBiMonthlyTimesheetReport4.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CareerDiscoveryReport\CareerDiscoveryReport.resx">
      <DependentUpon>CareerDiscoveryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CareerDiscoveryReport\Conditions\ConditionsReport.resx">
      <DependentUpon>ConditionsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CareerDiscoveryReport\Contributions\ContributionsReport.resx">
      <DependentUpon>ContributionsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CareerDiscoveryReport\Preferences\PreferencesReport.resx">
      <DependentUpon>PreferencesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CareerDiscoveryReport\SupportNeeds\SupportNeedsReport.resx">
      <DependentUpon>SupportNeedsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CaseNoteAuditReport\CaseNoteAuditReportTelerik.resx">
      <DependentUpon>CaseNoteAuditReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CaseNoteCoverageReport\CaseNoteCoverageReportTelerik.resx">
      <DependentUpon>CaseNoteCoverageReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CaseNoteReport\CaseNoteReportTelerik.resx">
      <DependentUpon>CaseNoteReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\CEOEmployeeIDReport\CEOEmployeeIDReportTelerik.resx">
      <DependentUpon>CEOEmployeeIDReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ClientStatsReport\ClientStatusReport.resx">
      <DependentUpon>ClientStatusReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ClinicalEventsReport\ClinicalEventsReport.resx">
      <DependentUpon>ClinicalEventsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionAggregateReport\ConsumerAgencyProductionAggregateReport.resx">
      <DependentUpon>ConsumerAgencyProductionAggregateReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReport.resx">
      <DependentUpon>ConsumerAgencyProductionReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgencyProductionReport\ConsumerAgencyProductionReportGroupedByTask.resx">
      <DependentUpon>ConsumerAgencyProductionReportGroupedByTask.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAgeReport\ConsumerAgeReportTelerik.resx">
      <DependentUpon>ConsumerAgeReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAtWorkCalendarReport\ConsumerAtWorkCalendarReport.resx">
      <DependentUpon>ConsumerAtWorkCalendarReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAveryMailingLabels\ConsumerAveryMailingReport2.resx">
      <DependentUpon>ConsumerAveryMailingReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerContactExtendedReport\ConsumerContactExtendedReportTelerik.resx">
      <DependentUpon>ConsumerContactExtendedReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerContactReport\ConsumerContactExcelReport.resx">
      <DependentUpon>ConsumerContactExcelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerContactReport\ConsumerContactReportTelerik.resx">
      <DependentUpon>ConsumerContactReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerDetailedContactReport\ConsumerDetailedContactReport.resx">
      <DependentUpon>ConsumerDetailedContactReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerDisabilityReport\ConsumerDisabilityReportTelerik.resx">
      <DependentUpon>ConsumerDisabilityReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerEquipmentOrderReport\ConsumerEquipmentOrderExcelReport.resx">
      <DependentUpon>ConsumerEquipmentOrderExcelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerEquipmentOrderStatisticsReport\ConsumerEquipmentOrderStatisticsReport.resx">
      <DependentUpon>ConsumerEquipmentOrderStatisticsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerEarningsSpreadsheetReport\ConsumerEarningsSpreadsheetReport.resx">
      <DependentUpon>ConsumerEarningsSpreadsheetReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerExpirationFieldReport\ConsumerExpirationFieldReport.resx">
      <DependentUpon>ConsumerExpirationFieldReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerNetworkContactHistoryReport\ConsumerNetworkContactHistoryReport.resx">
      <DependentUpon>ConsumerNetworkContactHistoryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerNetworkContactReport\ConsumerNetworkContactReport.resx">
      <DependentUpon>ConsumerNetworkContactReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerOutcomeGoalStrategyReport\ConsumerOutcomeGoalStrategyExcelReport.resx">
      <DependentUpon>ConsumerOutcomeGoalStrategyExcelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerOutcomeGoalStrategyReport\ConsumerOutcomeGoalStrategyReport.resx">
      <DependentUpon>ConsumerOutcomeGoalStrategyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerOwnGuardianReport\ConsumerOwnGuardianReport.resx">
      <DependentUpon>ConsumerOwnGuardianReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerPrimaryAndSecondaryContactReport\ConsumerPrimaryAndSecondaryContactReportTelerik.resx">
      <DependentUpon>ConsumerPrimaryAndSecondaryContactReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerScaleAssessmentReport\ConsumerScaleAssessmentReport.resx">
      <DependentUpon>ConsumerScaleAssessmentReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerScaleAssessmentWithGraphReport\ConsumerScaleAssessmentWithGraphReport.resx">
      <DependentUpon>ConsumerScaleAssessmentWithGraphReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerServiceTotalReport\ConsumerServiceTotalReport.resx">
      <DependentUpon>ConsumerServiceTotalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerServiceWithNonbillableTotalReport\ConsumerServiceWithNonbillableTotalReport.resx">
      <DependentUpon>ConsumerServiceWithNonbillableTotalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerServiceWithOutcomesTotalReport\ConsumerServiceWithOutcomesTotalReport.resx">
      <DependentUpon>ConsumerServiceWithOutcomesTotalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsInterventionAndAssessmentMonthlyObjectiveTrackingAddComments\ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsStandAlone.resx">
      <DependentUpon>ActivityRecordsInterventionAndAssessmentMonthlyAddCommentsStandAlone.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerPlacementBenefitReport\ConsumerPlacementBenefitReport.resx">
      <DependentUpon>ConsumerPlacementBenefitReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTransportationServiceCountReport\ActivityRecordsTransportationServiceCountReport.resx">
      <DependentUpon>ActivityRecordsTransportationServiceCountReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerMedsAdministrationCoverageAndTrainingReport\ConsumerMedsAdministrationCoverageAndTrainingReport.resx">
      <DependentUpon>ConsumerMedsAdministrationCoverageAndTrainingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsGroupReport\ActivityRecordsGroupReport.resx">
      <DependentUpon>ActivityRecordsGroupReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsGroupOverallAndConsumerCommentReport\ActivityRecordsGroupOverallAndConsumerCommentReportStandAlone.resx">
      <DependentUpon>ActivityRecordsGroupOverallAndConsumerCommentReportStandAlone.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsGroupDetailsReport\ActivityRecordsGroupDetailsReport.resx">
      <DependentUpon>ActivityRecordsGroupDetailsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithAuthAndPhases\ActivityRecordsUnitReportWithAuthAndPhases.resx">
      <DependentUpon>ActivityRecordsUnitReportWithAuthAndPhases.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerOutcomeGoalStrategyComprehensiveReport\ConsumerOutcomeGoalStrategyComprehensiveReport.resx">
      <DependentUpon>ConsumerOutcomeGoalStrategyComprehensiveReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithConsumerTimeInStaffAndService\ActivityRecordsUnitReportWithConsumerTimeInStaffAndService.resx">
      <DependentUpon>ActivityRecordsUnitReportWithConsumerTimeInStaffAndService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsUnitReportWithAuthAndEmployer\ActivityRecordsUnitReportWithAuthAndEmployer.resx">
      <DependentUpon>ActivityRecordsUnitReportWithAuthAndEmployer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerAggregateDataReport\ConsumerAggregateDataReportTelerik.resx">
      <DependentUpon>ConsumerAggregateDataReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerEquipmentOrderReport\ConsumerEquipmentOrderReport.resx">
      <DependentUpon>ConsumerEquipmentOrderReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReport.resx">
      <DependentUpon>ActivityRecordsTCMBillingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReportByConsumer.resx">
      <DependentUpon>ActivityRecordsTCMBillingReportByConsumer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReportByConsumerAndDepartment.resx">
      <DependentUpon>ActivityRecordsTCMBillingReportByConsumerAndDepartment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingReportExtendedExcel.resx">
      <DependentUpon>ActivityRecordsTCMBillingReportExtendedExcel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingSummaryReport.resx">
      <DependentUpon>ActivityRecordsTCMBillingSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMBillingReport2\ActivityRecordsTCMBillingSummaryReportAndDepartment.resx">
      <DependentUpon>ActivityRecordsTCMBillingSummaryReportAndDepartment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ActivityRecordsTCMRawDataReport\ActivityRecordsTCMRawDataReportExcel.resx">
      <DependentUpon>ActivityRecordsTCMRawDataReportExcel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerDepartmentFinanceReport\ConsumerDepartmentFinanceReport.resx">
      <DependentUpon>ConsumerDepartmentFinanceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerWeightAnalysisReport\ConsumerWeightAnalysisReport.resx">
      <DependentUpon>ConsumerWeightAnalysisReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DailyJobSupportsTimeLog\DailyJobSupportsTimeLog.resx">
      <DependentUpon>DailyJobSupportsTimeLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetAllRecordsWithoutOverlapWithSums\EmployeeTimesheetAllRecordsWithoutOverlapNoDepartmentsWithSums.resx">
      <DependentUpon>EmployeeTimesheetAllRecordsWithoutOverlapNoDepartmentsWithSums.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\HealthCarePaymentAgeingReport\HealthCarePaymentAgeingTotalsOnly.resx">
      <DependentUpon>HealthCarePaymentAgeingTotalsOnly.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\HealthCarePaymentAgeingReport\SageIntacctAccRecInvoices.resx">
      <DependentUpon>SageIntacctAccRecInvoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\HealthCarePaymentAgeingReport\HealthCarePaymentAgeingReport.resx">
      <DependentUpon>HealthCarePaymentAgeingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobEarningsReport\JobEarningsReport.resx">
      <DependentUpon>JobEarningsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageReport\MileageReportExcelByStaff.resx">
      <DependentUpon>MileageReportExcelByStaff.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\LogInternshipDevelopmentSupports\LogInternshipDevelopmentSupports.resx">
      <DependentUpon>LogInternshipDevelopmentSupports.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NWCGreatPlainsInterface\NWCGreatPlainsInterface.resx">
      <DependentUpon>NWCGreatPlainsInterface.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OregonEXPRS\OregonEXPRS.resx">
      <DependentUpon>OregonEXPRS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBillingReport\OutcomeBillingReport.resx">
      <DependentUpon>OutcomeBillingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\QuickBooksPayrollExportIIFReport\QuickBooksPayrollExportIIFReport.resx">
      <DependentUpon>QuickBooksPayrollExportIIFReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffServiceWithNonBillableTotalReport\StaffServiceWithNonBillableTotalFunderGroupingReport.resx">
      <DependentUpon>StaffServiceWithNonBillableTotalFunderGroupingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\TaskListReport\TaskListReportTelerik.resx">
      <DependentUpon>TaskListReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMonthlySummaryReportUpdated\FormsMonthlySummaryReportUpdated.resx">
      <DependentUpon>FormsMonthlySummaryReportUpdated.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsSAServiceDeliveryPlanReport\FormSAServiceDeliveryPlanReportPage2.resx">
      <DependentUpon>FormSAServiceDeliveryPlanReportPage2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FunderIndividualPlanBudgetComparisonReport\FunderIndividualPlanBudgetComparisonReport.resx">
      <DependentUpon>FunderIndividualPlanBudgetComparisonReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FunderIndividualPlanBudgetReport\FunderIndividualPlanBudgetReport.resx">
      <DependentUpon>FunderIndividualPlanBudgetReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\HealthCarePaymentBatchReport\HealthCarePaymentBatchExcelReport.resx">
      <DependentUpon>HealthCarePaymentBatchExcelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MarylandJobDevLog\MarylandJobDevLog.resx">
      <DependentUpon>MarylandJobDevLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageReport\MileageReportTelerikGroupedByCostCenter.resx">
      <DependentUpon>MileageReportTelerikGroupedByCostCenter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NJ_DVRS_Activities\NJ_DVRS_Activities.resx">
      <DependentUpon>NJ_DVRS_Activities.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffEquipmentOrderReport\StaffEquipmentOrderExcelReport.resx">
      <DependentUpon>StaffEquipmentOrderExcelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsADVPQuarterlyReport\FormsADVPQuarterlyReport.resx">
      <DependentUpon>FormsADVPQuarterlyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PMLogsReport\PMLogsReport.resx">
      <DependentUpon>PMLogsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffMonthlyHoursProductionWithoutTargetsReport\StaffMonthlyHoursProductionWithoutTargetsReport.resx">
      <DependentUpon>StaffMonthlyHoursProductionWithoutTargetsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffMonthlyHoursProductionReport\StaffMonthlyHoursProductionReport.resx">
      <DependentUpon>StaffMonthlyHoursProductionReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffMonthlyHoursSummaryReport\StaffMonthlyHoursSummaryReport.resx">
      <DependentUpon>StaffMonthlyHoursSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\TaskStaffPerformanceReport\TaskStaffPerformanceReport.resx">
      <DependentUpon>TaskStaffPerformanceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\UserExpenseReport\UserExpenseReport.resx">
      <DependentUpon>UserExpenseReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\UserExpenseReport\UserExpenseUngroupedReport.resx">
      <DependentUpon>UserExpenseUngroupedReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\VAOutcomesAndActivities\VAOutcomesAndActivities.resx">
      <DependentUpon>VAOutcomesAndActivities.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\VAOutcomesAndActivities\VAPartVPlanOfSupports.resx">
      <DependentUpon>VAPartVPlanOfSupports.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\VAPersonCenteredReview\VAPersonCenteredReview.resx">
      <DependentUpon>VAPersonCenteredReview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\HealthCarePaymentBatchReport\HealthCarePaymentBatchReport.resx">
      <DependentUpon>HealthCarePaymentBatchReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DoctorOrderReport\DoctorOrderReport.resx">
      <DependentUpon>DoctorOrderReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetAllRecordsWithoutOverlapWithSums\EmployeeTimesheetAllRecordsWithoutOverlapWithSums.resx">
      <DependentUpon>EmployeeTimesheetAllRecordsWithoutOverlapWithSums.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\TCFSS_PayrollReport\TCFSS_PayrollReport.resx">
      <DependentUpon>TCFSS_PayrollReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ConsumerNetworkCommunicationReport\ConsumerNetworkCommunicationReport.resx">
      <DependentUpon>ConsumerNetworkCommunicationReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\TCFSS_AccountingReport\TCFSS_AccountingReport.resx">
      <DependentUpon>TCFSS_AccountingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMedicaidWaiverSummaryReportOld\FormsMedicaidWaiverSummaryReportOld.resx">
      <DependentUpon>FormsMedicaidWaiverSummaryReportOld.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsSAServiceDeliveryPlanReport\FormSAServiceDeliveryPlanReport.resx">
      <DependentUpon>FormSAServiceDeliveryPlanReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\GenericReport\GenericReport.resx">
      <DependentUpon>GenericReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MedicationReport\MedicationReport.resx">
      <DependentUpon>MedicationReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsCHSROIReport\FormCHSROIReport.resx">
      <DependentUpon>FormCHSROIReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffPrimaryContactWeightReport\StaffPrimaryContactWeightReport.resx">
      <DependentUpon>StaffPrimaryContactWeightReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffBirthdayReport\StaffBirthdayReport.resx">
      <DependentUpon>StaffBirthdayReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceWGroupReport\EmployeeTimesheetBillableWithConsumerServiceWGroupReport.resx">
      <DependentUpon>EmployeeTimesheetBillableWithConsumerServiceWGroupReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PlacementProgramCountReport\PlacementProgramCountReport.resx">
      <DependentUpon>PlacementProgramCountReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\TCMEmployeeTimesheetBillableWithConsumerServiceReport\TCMEmployeeTimesheetBillableWithConsumerServiceReport.resx">
      <DependentUpon>TCMEmployeeTimesheetBillableWithConsumerServiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffServiceWithCodeGroupedTotalReport\StaffServiceWithCodeGroupedTotalReport.resx">
      <DependentUpon>StaffServiceWithCodeGroupedTotalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffDobHireTenureReport\StaffDobHireTenureReport.resx">
      <DependentUpon>StaffDobHireTenureReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageCompanyCarReport\MileageCompanyCarReport.resx">
      <DependentUpon>MileageCompanyCarReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageMonthlyReport\MileageMonthlyReport.resx">
      <DependentUpon>MileageMonthlyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsSemiAnnualAssessmentReport\FormsSemiAnnualAssessmentReport.resx">
      <DependentUpon>FormsSemiAnnualAssessmentReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobAnalysisReport\JobAnalysisReport.resx">
      <DependentUpon>JobAnalysisReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NotificationReport\NotificationReport.resx">
      <DependentUpon>NotificationReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeMeasurementReport\OutcomeMeasurementDrilldownReportTelerik.resx">
      <DependentUpon>OutcomeMeasurementDrilldownReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffExpirationFieldReport\StaffExpirationFieldReport.resx">
      <DependentUpon>StaffExpirationFieldReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\GoalCompletionReport\GoalCompletionReport.resx">
      <DependentUpon>GoalCompletionReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeMeasurementReport\OutcomeMeasurementReportTelerik.resx">
      <DependentUpon>OutcomeMeasurementReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DemographicsReport\DemographicsReportTelerik.resx">
      <DependentUpon>DemographicsReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\DistributionListReport\DistributionListReport.resx">
      <DependentUpon>DistributionListReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EarlyClosingReport\EarlyClosingReportTelerik.resx">
      <DependentUpon>EarlyClosingReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportComplete\EmergencyReportAllergy\EmergencyReportAllergy.resx">
      <DependentUpon>EmergencyReportAllergy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportComplete\EmergencyReportContacts\EmergencyReportContacts.resx">
      <DependentUpon>EmergencyReportContacts.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportComplete\EmergencyReportDoctor\EmergencyReportDoctor.resx">
      <DependentUpon>EmergencyReportDoctor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportComplete\EmergencyReportHospital\EmergencyReportHospital.resx">
      <DependentUpon>EmergencyReportHospital.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportComplete\EmergencyReportMedication\EmergencyReportMedication.resx">
      <DependentUpon>EmergencyReportMedication.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportComplete\EmergencyReportSeizure\EmergencyReportSeizure.resx">
      <DependentUpon>EmergencyReportSeizure.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportComplete\EmergencyReportTelerik.resx">
      <DependentUpon>EmergencyReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportFactSheet\EmergencyFactSheetBackside.resx">
      <DependentUpon>EmergencyFactSheetBackside.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmergencyReportFactSheet\EmergencyFactSheetReportReport.resx">
      <DependentUpon>EmergencyFactSheetReportReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetBillableReport\EmployeeTimesheetBillableReportTelerik2.resx">
      <DependentUpon>EmployeeTimesheetBillableReportTelerik2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetBillableWithConsumerServiceReport\EmployeeTimesheetBillableWithConsumerServiceReport.resx">
      <DependentUpon>EmployeeTimesheetBillableWithConsumerServiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployeeTimesheetReport\EmployeeTimesheetReportTelerik.resx">
      <DependentUpon>EmployeeTimesheetReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerAveryMailingLabels\EmployerAveryMailingReport2.resx">
      <DependentUpon>EmployerAveryMailingReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerCreatedReport\EmployerCreatedReport.resx">
      <DependentUpon>EmployerCreatedReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerDetailedContactReport\EmployerDetailedContactReport.resx">
      <DependentUpon>EmployerDetailedContactReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerEmailAndPhoneReport\EmployerEmailAndPhoneReport.resx">
      <DependentUpon>EmployerEmailAndPhoneReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerExportReport\EmployerExportReport.resx">
      <DependentUpon>EmployerExportReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerJobAnalysisReport\EmployerJobAnalysisReport.resx">
      <DependentUpon>EmployerJobAnalysisReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerJobContactSourceReport\EmployerJobContactSourceReportTelerik.resx">
      <DependentUpon>EmployerJobContactSourceReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerNotesReport\EmployerNotesReportTelerik.resx">
      <DependentUpon>EmployerNotesReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\EmployerWorkplaceRelationshipHistoryReport\EmployerWorkplaceRelationshipHistoryReportTelerik.resx">
      <DependentUpon>EmployerWorkplaceRelationshipHistoryReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ErrorReport\MostErrorsReportTelerik.resx">
      <DependentUpon>MostErrorsReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ExpirationFieldReport\ExpirationFieldReportTelerik.resx">
      <DependentUpon>ExpirationFieldReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsIWPQuestionnaireResult\IWPQuestionnaireResultTelerik.resx">
      <DependentUpon>IWPQuestionnaireResultTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMedicaidWaiverSummaryReport\FormsMedicaidWaiverSummaryReport.resx">
      <DependentUpon>FormsMedicaidWaiverSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMonthlyProgressReview\FormsMonthlyProgressReview.resx">
      <DependentUpon>FormsMonthlyProgressReview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsMonthlySummaryReport\FormsMonthlySummaryReport.resx">
      <DependentUpon>FormsMonthlySummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsRCDDSMonthlyProviderSummaryReport\FormsRCDDSMonthlyProviderSummaryReport.resx">
      <DependentUpon>FormsRCDDSMonthlyProviderSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsReport\DynamicEmployerFormsReport\DynamicEmployerFormsReportTelerik.resx">
      <DependentUpon>DynamicEmployerFormsReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormsReport\DynamicFormsReport\DynamicFormsReportTelerik.resx">
      <DependentUpon>DynamicFormsReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FormStatusReport\FormStatusReport.resx">
      <DependentUpon>FormStatusReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FundingSourceContactConsumerReport\FundingSourceContactConsumerReport.resx">
      <DependentUpon>FundingSourceContactConsumerReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\FundingSourceContactHistoryReport\FundingSourceContactHistoryReport.resx">
      <DependentUpon>FundingSourceContactHistoryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\InactiveConsumersReport\InactiveConsumersReportTelerik.resx">
      <DependentUpon>InactiveConsumersReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\IncidentReport\IncidentReportTelerik.resx">
      <DependentUpon>IncidentReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\InterventionTimesheetReportWithOffSite\InterventionReportWithOffSiteTelerik.resx">
      <DependentUpon>InterventionReportWithOffSiteTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\InterventionTimesheetReport\InterventionReportTelerik.resx">
      <DependentUpon>InterventionReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\InterventionTimesheetReport\InterventionReportTelerik2.resx">
      <DependentUpon>InterventionReportTelerik2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobContactsReport\JobContactsReportTelerik.resx">
      <DependentUpon>JobContactsReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobContactsReport\JobContactsReportTelerikExcel.resx">
      <DependentUpon>JobContactsReportTelerikExcel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobDevelopmentProgressReport\JobDevelopmentOnlineContacts\JobDevelopmentContacts.resx">
      <DependentUpon>JobDevelopmentContacts.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobDevelopmentProgressReport\JobDevProgressReportSQLDirect.resx">
      <DependentUpon>JobDevProgressReportSQLDirect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobDevelopmentProgressReport\TEMP\EmployerCaseNotes.resx">
      <DependentUpon>EmployerCaseNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevDesign.resx">
      <DependentUpon>JobDevDesign.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevelopmentProgressReport.resx">
      <DependentUpon>JobDevelopmentProgressReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevelopmentProgressReportSimple.resx">
      <DependentUpon>JobDevelopmentProgressReportSimple.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobDevelopmentProgressReport\TEMP\JobDevTest.resx">
      <DependentUpon>JobDevTest.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\JobPlacementReport\JobPlacementReportTelerik.resx">
      <DependentUpon>JobPlacementReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ManagementAggregateReport\ManagementAggregateReport.resx">
      <DependentUpon>ManagementAggregateReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ManagementDepartmentProductivityReport\ManagementDepartmentProductivityReport.resx">
      <DependentUpon>ManagementDepartmentProductivityReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ManagementReport\ManagementReport.resx">
      <DependentUpon>ManagementReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MedicaidReport\MedicationReportTelerik.resx">
      <DependentUpon>MedicationReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MedicalRosterReport\MedicalRosterReport.resx">
      <DependentUpon>MedicalRosterReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageReportSummarized\MileageReportSummarizedClockNumberTelerik.resx">
      <DependentUpon>MileageReportSummarizedClockNumberTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageReportSummarized\MileageReportSummarizedTelerik.resx">
      <DependentUpon>MileageReportSummarizedTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageReport\MileageReportExcel2.resx">
      <DependentUpon>MileageReportExcel2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MileageReport\MileageReportTelerik.resx">
      <DependentUpon>MileageReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsReport\MOSEMonthlyJobSupportsReport.resx">
      <DependentUpon>MOSEMonthlyJobSupportsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\MOSEMonthlyJobSupportsSummaryReport\MOSEMonthlyJobSupportsSummaryReport.resx">
      <DependentUpon>MOSEMonthlyJobSupportsSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NCSEJobDevelopmentMilestonePaymentRequestForm\NCSEJobDevelopmentMilestonePaymentRequestForm.resx">
      <DependentUpon>NCSEJobDevelopmentMilestonePaymentRequestForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NCWAJobDevelopmentMilestonePaymentRequestForm\NCWAJobDevelopmentMilestonePaymentRequestForm.resx">
      <DependentUpon>NCWAJobDevelopmentMilestonePaymentRequestForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NonAspirinReport\NonAspirinReportTelerik.resx">
      <DependentUpon>NonAspirinReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\NoResultsReport\NoResultsTelerik.resx">
      <DependentUpon>NoResultsTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBasedBillingReport\OutcomeBasedBillingReportTelerik.resx">
      <DependentUpon>OutcomeBasedBillingReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBasedBillingReport\OutcomeBasedBillingSubReport.resx">
      <DependentUpon>OutcomeBasedBillingSubReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBasedReport\OutcomeBasedExportReport.resx">
      <DependentUpon>OutcomeBasedExportReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBasedReport\OutcomeBasedReportTelerik.resx">
      <DependentUpon>OutcomeBasedReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBasedServiceReport\OutcomeBasedServiceReportTelerik.resx">
      <DependentUpon>OutcomeBasedServiceReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeBasedTotalsReport\OutcomeBasedTotalsExportReport.resx">
      <DependentUpon>OutcomeBasedTotalsExportReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\OutcomeMeasurementReportLOQW\OutcomeMeasurementReportLOQWTelerik.resx">
      <DependentUpon>OutcomeMeasurementReportLOQWTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PayrollReport\PayrollReportTelerik.resx">
      <DependentUpon>PayrollReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PhotoConsentReport\PhotoConsentReportTelerik.resx">
      <DependentUpon>PhotoConsentReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PhotoConsentReport\PhotoConsentTelerik.resx" />
    <EmbeddedResource Include="REPORTS\PlacementFollowUpFormReport\PlacementFollowUpFormReport.resx">
      <DependentUpon>PlacementFollowUpFormReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PlacementFollowUpFullFormReport\PlacementFollowUpFullFormReport.resx">
      <DependentUpon>PlacementFollowUpFullFormReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\PlacementWagePartTimeReport\PlacementWagePartTimeReport.resx">
      <DependentUpon>PlacementWagePartTimeReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\QuickBooksInvoiceReport\QuickBooksInvoiceReport.resx">
      <DependentUpon>QuickBooksInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\QuickBooksInvoiceReport\QuickBooksInvoiceReport2.resx">
      <DependentUpon>QuickBooksInvoiceReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\QuickBooksTimesheetOvertimeReport\QuickBooksTimesheetOvertimeReport.resx">
      <DependentUpon>QuickBooksTimesheetOvertimeReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\QuickBooksTimesheetReport\QuickBooksTimesheetReport.resx">
      <DependentUpon>QuickBooksTimesheetReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ResidentialRosterReport\ResidentialRosterReportTelerik.resx">
      <DependentUpon>ResidentialRosterReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\RestrictionReport\RestrictionReportDiabetic.resx">
      <DependentUpon>RestrictionReportDiabetic.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\RestrictionReport\RestrictionReportDietaryNeeds.resx">
      <DependentUpon>RestrictionReportDietaryNeeds.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\RestrictionReport\RestrictionReportEatingOversight.resx">
      <DependentUpon>RestrictionReportEatingOversight.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\RestrictionReport\RestrictionReportNonAspirin.resx">
      <DependentUpon>RestrictionReportNonAspirin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\RestrictionReport\RestrictionReportTelerik.resx">
      <DependentUpon>RestrictionReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\RestrictionReport\RestrictionReportTelerikRestrictionOnly.resx">
      <DependentUpon>RestrictionReportTelerikRestrictionOnly.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\SeizureReport\SeizureReportTelerik.resx">
      <DependentUpon>SeizureReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\ServiceCountReport\ServiceCountReport.resx">
      <DependentUpon>ServiceCountReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffAuditReport\StaffAuditReportTelerik.resx">
      <DependentUpon>StaffAuditReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffAveryMailingLabels\StaffAveryMailingReport.resx">
      <DependentUpon>StaffAveryMailingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffCoverageReport\StaffCoverageReportTelerik.resx">
      <DependentUpon>StaffCoverageReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffEducationReport\StaffEducationReport.resx">
      <DependentUpon>StaffEducationReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffExpirationReport\StaffExpirationReportTelerik.resx">
      <DependentUpon>StaffExpirationReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffHoursOverviewReport\StaffHoursOverviewReport.resx">
      <DependentUpon>StaffHoursOverviewReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffNoteReport\StaffNoteReport.resx">
      <DependentUpon>StaffNoteReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffPaymentReportCustomServiceRate\StaffPaymentReportCustomServiceRate.resx">
      <DependentUpon>StaffPaymentReportCustomServiceRate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffProductionReport\StaffProductionReport.resx">
      <DependentUpon>StaffProductionReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffRoleDepartmentReport\StaffRoleDepartmentReport.resx">
      <DependentUpon>StaffRoleDepartmentReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffServiceTotalReport\StaffServiceTotalReport.resx">
      <DependentUpon>StaffServiceTotalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffServiceTotalWithOutcomesReport\StaffServiceTotalWithOutcomesReport.resx">
      <DependentUpon>StaffServiceTotalWithOutcomesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffServiceWithNonBillableTotalReport\StaffServiceWithNonBillableTotalReport.resx">
      <DependentUpon>StaffServiceWithNonBillableTotalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\StaffTimesheetExportReport\StaffTimesheetExportReport.resx">
      <DependentUpon>StaffTimesheetExportReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\TextFieldReport\TextFieldReportTelerik.resx">
      <DependentUpon>TextFieldReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\VisualVaultPreBillingReport\VisualVaultPreBillingReport.resx">
      <DependentUpon>VisualVaultPreBillingReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\VRCBAInterventionTimesheet\VRCBAInterventionTimesheet.resx">
      <DependentUpon>VRCBAInterventionTimesheet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\VRSEInterventionTimesheet\VRSEInterventionTimesheet.resx">
      <DependentUpon>VRSEInterventionTimesheet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\VRWAInterventionTimesheet\VRWAInterventionTimesheet.resx">
      <DependentUpon>VRWAInterventionTimesheet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORTS\WorkHistoryReport\WorkHistoryReportTelerik.resx">
      <DependentUpon>WorkHistoryReportTelerik.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="REPORT_INTERFACES\UCP_Abila_BillingInterface\UCP_Abila_BillingInterface.resx">
      <DependentUpon>UCP_Abila_BillingInterface.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SETWorksDAO\SETWorksDAO.csproj">
      <Project>{33a4efad-9990-47ea-b6b7-5b75d544b0ab}</Project>
      <Name>SETWorksDAO</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="REPORTS\FixedWidthReport\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <TelerikWebUIAssembliesSourceDir>..\packages\Telerik.UI.for.AspNet.Ajax.Net45.2016.2.607\lib\net45</TelerikWebUIAssembliesSourceDir>
    <BuildDependsOn>$(BuildDependsOn);CopyTelerikWebUIUtilityAssemblies</BuildDependsOn>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <Import Project="..\packages\EmptyLicensesLicx.1.1.0\build\EmptyLicensesLicx.targets" Condition="Exists('..\packages\EmptyLicensesLicx.1.1.0\build\EmptyLicensesLicx.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EmptyLicensesLicx.1.1.0\build\EmptyLicensesLicx.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EmptyLicensesLicx.1.1.0\build\EmptyLicensesLicx.targets'))" />
  </Target>
  <Target Name="CopyTelerikWebUIUtilityAssemblies">
    <PropertyGroup>
      <TelerikWebUIAssembliesSourceDirFullPath>$([System.IO.Path]::GetFullPath('$(TelerikWebUIAssembliesSourceDir)'))</TelerikWebUIAssembliesSourceDirFullPath>
    </PropertyGroup>
    <ItemGroup>
      <TelerikWebUIAssemblies Include="$(TelerikWebUIAssembliesSourceDirFullPath)\Telerik.Web.Design.dll" />
    </ItemGroup>
    <Copy SourceFiles="@(TelerikWebUIAssemblies)" DestinationFolder="$(TargetDir)" SkipUnchangedFiles="true" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>