<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Datadog.Trace</name>
    </assembly>
    <members>
        <member name="T:Datadog.Trace.AgentHttpHeaderNames">
            <summary>
            Names of HTTP headers that can be used when sending traces to the Trace Agent.
            </summary>
        </member>
        <member name="F:Datadog.Trace.AgentHttpHeaderNames.Language">
            <summary>
            The language-specific tracer that generated this span.
            Always ".NET" for the .NET Tracer.
            </summary>
        </member>
        <member name="F:Datadog.Trace.AgentHttpHeaderNames.LanguageInterpreter">
            <summary>
            The interpreter for the given language, e.g. ".NET Framework" or ".NET Core".
            </summary>
        </member>
        <member name="F:Datadog.Trace.AgentHttpHeaderNames.LanguageVersion">
            <summary>
            The interpreter version for the given language, e.g. "4.7.2" for .NET Framework or "2.1" for .NET Core.
            </summary>
        </member>
        <member name="F:Datadog.Trace.AgentHttpHeaderNames.TracerVersion">
            <summary>
            The version of the tracer that generated this span.
            </summary>
        </member>
        <member name="F:Datadog.Trace.AgentHttpHeaderNames.TraceCount">
            <summary>
            The number of unique traces per request.
            </summary>
        </member>
        <member name="F:Datadog.Trace.AgentHttpHeaderNames.ContainerId">
            <summary>
            The id of the container where the traced application is running.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Configuration.CompositeConfigurationSource">
            <summary>
            Represents one or more configuration sources.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.Add(Datadog.Trace.Configuration.IConfigurationSource)">
            <summary>
            Adds a new configuration source to this instance.
            </summary>
            <param name="source">The configuration source to add.</param>
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.Insert(System.Int32,Datadog.Trace.Configuration.IConfigurationSource)">
            <summary>
            Inserts an element into the <see cref="T:Datadog.Trace.Configuration.CompositeConfigurationSource"/> at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item"/> should be inserted.</param>
            <param name="item">The configuration source to insert.</param>
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.GetString(System.String)">
            <summary>
            Gets the <see cref="T:System.String"/> value of the first setting found with
            the specified key from the current list of configuration sources.
            Sources are queried in the order in which they were added.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.GetInt32(System.String)">
            <summary>
            Gets the <see cref="T:System.Int32"/> value of the first setting found with
            the specified key from the current list of configuration sources.
            Sources are queried in the order in which they were added.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.GetDouble(System.String)">
            <summary>
            Gets the <see cref="T:System.Double"/> value of the first setting found with
            the specified key from the current list of configuration sources.
            Sources are queried in the order in which they were added.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.GetBool(System.String)">
            <summary>
            Gets the <see cref="T:System.Boolean"/> value of the first setting found with
            the specified key from the current list of configuration sources.
            Sources are queried in the order in which they were added.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.System#Collections#Generic#IEnumerable{Datadog#Trace#Configuration#IConfigurationSource}#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Configuration.CompositeConfigurationSource.GetDictionary(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Datadog.Trace.Configuration.ConfigurationKeys">
            <summary>
            String constants for standard Datadog configuration keys.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.ConfigurationFileName">
            <summary>
            Configuration key for the path to the configuration file.
            Can only be set with an environment variable
            or in the <c>app.config</c>/<c>web.config</c> file.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.Environment">
            <summary>
            Configuration key for the application's environment. Sets the "env" tag on every <see cref="T:Datadog.Trace.Span"/>.
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.Environment"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.ServiceName">
            <summary>
            Configuration key for the application's default service name.
            Used as the service name for top-level spans,
            and used to determine service name of some child spans.
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.ServiceName"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.TraceEnabled">
            <summary>
            Configuration key for enabling or disabling the Tracer.
            Default is value is true (enabled).
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.TraceEnabled"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.DebugEnabled">
            <summary>
            Configuration key for enabling or disabling the Tracer's debug mode.
            Default is value is false (disabled).
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.DebugEnabled"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.DisabledIntegrations">
            <summary>
            Configuration key for a list of integrations to disable. All other integrations remain enabled.
            Default is empty (all integrations are enabled).
            Supports multiple values separated with semi-colons.
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.DisabledIntegrationNames"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentHost">
            <summary>
            Configuration key for the Agent host where the Tracer can send traces.
            Overriden by <see cref="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentUri"/> if present.
            Default value is "localhost".
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.AgentUri"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentPort">
            <summary>
            Configuration key for the Agent port where the Tracer can send traces.
            Default value is 8126.
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.AgentUri"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentUri">
            <summary>
            Configuration key for the Agent URL where the Tracer can send traces.
            Overrides values in <see cref="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentHost"/> and <see cref="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentPort"/> if present.
            Default value is "http://localhost:8126".
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.AgentUri"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.GlobalAnalyticsEnabled">
            <summary>
            Configuration key for enabling or disabling default Analytics.
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.AnalyticsEnabled"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.GlobalTags">
            <summary>
            Configuration key for a list of tags to be applied globally to spans.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.LogsInjectionEnabled">
            <summary>
            Configuration key for enabling or disabling the automatic injection
            of correlation identifiers into the logging context.
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.LogsInjectionEnabled"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.MaxTracesSubmittedPerSecond">
            <summary>
            Configuration key for setting the number of traces allowed
            to be submitted per second.
            </summary>
            <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.MaxTracesSubmittedPerSecond"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.CustomSamplingRules">
             <summary>
             Configuration key for setting custom sampling rules based on regular expressions.
             Semi-colon separated list of sampling rules.
             The rule is matched in order of specification. The first match in a list is used.
            
             Per entry:
               The item 'rate' is required in decimal format.
               The item 'service' is optional in regular expression format, to match on service name.
               The item 'operation' is optional in regular expression format, to match on operation name.
            
             To give a rate of 50% to any traces in a service starting with the text "cart":
               'rate=0.5, service=cart.*'
            
             To give a rate of 20% to any traces which have an operation name of "http.request":
               'rate=0.2, operation=http.request'
            
             To give a rate of 100% to any traces within a service named "background" and with an operation name of "sql.query":
               'rate=1.0, service=background, operation=sql.query
            
             To give a rate of 10% to all traces
               'rate=0.1'
            
             To configure multiple rules, separate by semi-colon and order from most specific to least specific:
               'rate=0.5, service=cart.*; rate=0.2, operation=http.request; rate=1.0, service=background, operation=sql.query; rate=0.1'
            
             If no rules are specified, or none match, default internal sampling logic will be used.
             </summary>
             <seealso cref="P:Datadog.Trace.Configuration.TracerSettings.CustomSamplingRules"/>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.DogStatsdPort">
            <summary>
            Configuration key for the DogStatsd port where the Tracer can send metrics.
            Default value is 8125/
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.TracerMetricsEnabled">
            <summary>
            Configuration key for enabling or disabling internal metrics sent to DogStatsD.
            Default value is <c>false</c> (disabled).
            </summary>
        </member>
        <member name="T:Datadog.Trace.Configuration.ConfigurationKeys.Integrations">
            <summary>
            String format patterns used to match integration-specific configuration keys.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.Integrations.Enabled">
            <summary>
            Configuration key pattern for enabling or disabling an integration.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.Integrations.AnalyticsEnabled">
            <summary>
            Configuration key pattern for enabling or disabling Analytics in an integration.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.Integrations.AnalyticsSampleRate">
            <summary>
            Configuration key pattern for setting Analytics sampling rate in an integration.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Configuration.ConfigurationKeys.Debug">
            <summary>
            String constants for debug configuration keys.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.Debug.ForceMdTokenLookup">
            <summary>
            Configuration key for forcing the automatic instrumentation to only use the mdToken method lookup mechanism.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.ConfigurationKeys.Debug.ForceFallbackLookup">
            <summary>
            Configuration key for forcing the automatic instrumentation to only use the fallback method lookup mechanism.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Configuration.EnvironmentConfigurationSource">
            <summary>
            Represents a configuration source that
            retrieves values from environment variables.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.EnvironmentConfigurationSource.GetString(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Datadog.Trace.Configuration.IConfigurationSource">
            <summary>
            A source of configuration settings, identifiable by a string key.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.IConfigurationSource.GetString(System.String)">
            <summary>
            Gets the <see cref="T:System.String"/> value of
            the setting with the specified key.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.IConfigurationSource.GetInt32(System.String)">
            <summary>
            Gets the <see cref="T:System.Int32"/> value of
            the setting with the specified key.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.IConfigurationSource.GetDouble(System.String)">
            <summary>
            Gets the <see cref="T:System.Double"/> value of
            the setting with the specified key.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.IConfigurationSource.GetBool(System.String)">
            <summary>
            Gets the <see cref="T:System.Boolean"/> value of
            the setting with the specified key.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.IConfigurationSource.GetDictionary(System.String)">
            <summary>
            Gets the <see cref="T:System.Collections.Generic.IDictionary`2"/> value of
            the setting with the specified key.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or <c>null</c> if not found.</returns>
        </member>
        <member name="T:Datadog.Trace.Configuration.IntegrationSettings">
            <summary>
            Contains integration-specific settings.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.IntegrationSettings.#ctor(System.String,Datadog.Trace.Configuration.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Configuration.IntegrationSettings"/> class.
            </summary>
            <param name="integrationName">The integration name.</param>
            <param name="source">The <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/> to use when retrieving configuration values.</param>
        </member>
        <member name="P:Datadog.Trace.Configuration.IntegrationSettings.IntegrationName">
            <summary>
            Gets the name of the integration. Used to retrieve integration-specific settings.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Configuration.IntegrationSettings.Enabled">
            <summary>
            Gets or sets a value indicating whether
            this integration is enabled.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Configuration.IntegrationSettings.AnalyticsEnabled">
            <summary>
            Gets or sets a value indicating whether
            Analytics are enabled for this integration.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Configuration.IntegrationSettings.AnalyticsSampleRate">
            <summary>
            Gets or sets a value between 0 and 1 (inclusive)
            that determines the sampling rate for this integration.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Configuration.IntegrationSettingsCollection">
            <summary>
            A collection of <see cref="T:Datadog.Trace.Configuration.IntegrationSettings"/> instances, referenced by name.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.IntegrationSettingsCollection.#ctor(Datadog.Trace.Configuration.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Configuration.IntegrationSettingsCollection"/> class.
            </summary>
            <param name="source">The <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/> to use when retrieving configuration values.</param>
        </member>
        <member name="P:Datadog.Trace.Configuration.IntegrationSettingsCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Datadog.Trace.Configuration.IntegrationSettings"/> for the specified integration.
            </summary>
            <param name="integrationName">The name of the integration.</param>
            <returns>The integration-specific settings for the specified integration.</returns>
        </member>
        <member name="T:Datadog.Trace.Configuration.JsonConfigurationSource">
            <summary>
            Represents a configuration source that retrieves
            values from the provided JSON string.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Configuration.JsonConfigurationSource"/>
            class with the specified JSON string.
            </summary>
            <param name="json">A JSON string that contains configuration values.</param>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.FromFile(System.String)">
            <summary>
            Creates a new <see cref="T:Datadog.Trace.Configuration.JsonConfigurationSource"/> instance
            by loading the JSON string from the specified file.
            </summary>
            <param name="filename">A JSON file that contains configuration values.</param>
            <returns>The newly created configuration source.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.Datadog#Trace#Configuration#IConfigurationSource#GetString(System.String)">
            <summary>
            Gets the <see cref="T:System.String"/> value of
            the setting with the specified key.
            Supports JPath.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or null if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.Datadog#Trace#Configuration#IConfigurationSource#GetInt32(System.String)">
            <summary>
            Gets the <see cref="T:System.Int32"/> value of
            the setting with the specified key.
            Supports JPath.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or null if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.Datadog#Trace#Configuration#IConfigurationSource#GetDouble(System.String)">
            <summary>
            Gets the <see cref="T:System.Double"/> value of
            the setting with the specified key.
            Supports JPath.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or null if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.Datadog#Trace#Configuration#IConfigurationSource#GetBool(System.String)">
            <summary>
            Gets the <see cref="T:System.Boolean"/> value of
            the setting with the specified key.
            Supports JPath.
            </summary>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or null if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.GetValue``1(System.String)">
            <summary>
            Gets the value of the setting with the specified key and converts it into type <typeparamref name="T"/>.
            Supports JPath.
            </summary>
            <typeparam name="T">The type to convert the setting value into.</typeparam>
            <param name="key">The key that identifies the setting.</param>
            <returns>The value of the setting, or the default value of T if not found.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.JsonConfigurationSource.GetDictionary(System.String)">
            <summary>
            Gets a <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2"/> containing all of the values.
            </summary>
            <remarks>
            Example JSON where `globalTags` is the configuration key.
            {
             "globalTags": {
                "name1": "value1",
                "name2": "value2"
                }
            }
            </remarks>
            <param name="key">The key that identifies the setting.</param>
            <returns><see cref="T:System.Collections.Generic.IDictionary`2"/> containing all of the key-value pairs.</returns>
            <exception cref="T:Newtonsoft.Json.JsonReaderException">Thrown if the configuration value is not a valid JSON string.</exception>"
        </member>
        <member name="T:Datadog.Trace.Configuration.NameValueConfigurationSource">
            <summary>
            Represents a configuration source that retrieves
            values from the provided <see cref="T:System.Collections.Specialized.NameValueCollection"/>.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.NameValueConfigurationSource.#ctor(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Configuration.NameValueConfigurationSource"/> class
            that wraps the specified <see cref="T:System.Collections.Specialized.NameValueCollection"/>.
            </summary>
            <param name="nameValueCollection">The collection that will be wrapped by this configuration source.</param>
        </member>
        <member name="M:Datadog.Trace.Configuration.NameValueConfigurationSource.GetString(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Datadog.Trace.Configuration.StringConfigurationSource">
            <summary>
            A base <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/> implementation
            for string-only configuration sources.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.StringConfigurationSource.ParseCustomKeyValues(System.String)">
            <summary>
            Returns a <see cref="T:System.Collections.Generic.IDictionary`2"/> from parsing
            <paramref name="data"/>.
            </summary>
            <param name="data">A string containing key-value pairs which are comma-separated, and for which the key and value are colon-separated.</param>
            <returns><see cref="T:System.Collections.Generic.IDictionary`2"/> of key value pairs.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.StringConfigurationSource.GetString(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Configuration.StringConfigurationSource.GetInt32(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Configuration.StringConfigurationSource.GetDouble(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Configuration.StringConfigurationSource.GetBool(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Configuration.StringConfigurationSource.GetDictionary(System.String)">
            <summary>
            Gets a <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2"/> from parsing
            </summary>
            <param name="key">The key</param>
            <returns><see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2"/> containing all of the key-value pairs.</returns>
        </member>
        <member name="T:Datadog.Trace.Configuration.TracerSettings">
            <summary>
            Contains Tracer settings.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.TracerSettings.DefaultAgentHost">
            <summary>
            The default host value for <see cref="P:Datadog.Trace.Configuration.TracerSettings.AgentUri"/>.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Configuration.TracerSettings.DefaultAgentPort">
            <summary>
            The default port value for <see cref="P:Datadog.Trace.Configuration.TracerSettings.AgentUri"/>.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.TracerSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Configuration.TracerSettings"/> class with default values.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.TracerSettings.#ctor(Datadog.Trace.Configuration.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Configuration.TracerSettings"/> class
            using the specified <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/> to initialize values.
            </summary>
            <param name="source">The <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/> to use when retrieving configuration values.</param>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.Environment">
            <summary>
            Gets or sets the default environment name applied to all spans.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.Environment"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.ServiceName">
            <summary>
            Gets or sets the service name applied to top-level spans and used to build derived service names.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.ServiceName"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.TraceEnabled">
            <summary>
            Gets or sets a value indicating whether tracing is enabled.
            Default is <c>true</c>.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.TraceEnabled"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.DebugEnabled">
            <summary>
            Gets or sets a value indicating whether debug mode is enabled.
            Default is <c>false</c>.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.DebugEnabled"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.DisabledIntegrationNames">
            <summary>
            Gets or sets the names of disabled integrations.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.DisabledIntegrations"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.AgentUri">
            <summary>
            Gets or sets the Uri where the Tracer can connect to the Agent.
            Default is <c>"http://localhost:8126"</c>.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentUri"/>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentHost"/>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.AgentPort"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.AnalyticsEnabled">
            <summary>
            Gets or sets a value indicating whether default Analytics are enabled.
            Settings this value is a shortcut for setting
            <see cref="P:Datadog.Trace.Configuration.IntegrationSettings.AnalyticsEnabled"/> on some predetermined integrations.
            See the documentation for more details.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.GlobalAnalyticsEnabled"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.LogsInjectionEnabled">
            <summary>
            Gets or sets a value indicating whether correlation identifiers are
            automatically injected into the logging context.
            Default is <c>false</c>.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.LogsInjectionEnabled"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.MaxTracesSubmittedPerSecond">
            <summary>
            Gets or sets a value indicating the maximum number of traces set to AutoKeep (p1) per second.
            Default is <c>100</c>.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.MaxTracesSubmittedPerSecond"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.CustomSamplingRules">
            <summary>
            Gets or sets a value indicating custom sampling rules.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.CustomSamplingRules"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.Integrations">
            <summary>
            Gets a collection of <see cref="P:Datadog.Trace.Configuration.TracerSettings.Integrations"/> keyed by integration name.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.GlobalTags">
            <summary>
            Gets or sets the global tags, which are applied to all <see cref="T:Datadog.Trace.Span"/>s.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.DogStatsdPort">
            <summary>
            Gets or sets the port where the DogStatsd server is listening for connections.
            Default is <c>8125</c>.
            </summary>
            <seealso cref="F:Datadog.Trace.Configuration.ConfigurationKeys.DogStatsdPort"/>
        </member>
        <member name="P:Datadog.Trace.Configuration.TracerSettings.TracerMetricsEnabled">
            <summary>
            Gets or sets a value indicating whether internal metrics
            are enabled and send to DogStatsd.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Configuration.TracerSettings.FromDefaultSources">
            <summary>
            Create a <see cref="T:Datadog.Trace.Configuration.TracerSettings"/> populated from the default sources
            returned by <see cref="M:Datadog.Trace.Configuration.TracerSettings.CreateDefaultConfigurationSource"/>.
            </summary>
            <returns>A <see cref="T:Datadog.Trace.Configuration.TracerSettings"/> populated from the default sources.</returns>
        </member>
        <member name="M:Datadog.Trace.Configuration.TracerSettings.CreateDefaultConfigurationSource">
            <summary>
            Creates a <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/> by combining environment variables,
            AppSettings where available, and a local datadog.json file, if present.
            </summary>
            <returns>A new <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/> instance.</returns>
        </member>
        <member name="T:Datadog.Trace.Containers.ContainerInfo">
            <summary>
            Utility class with methods to interact with container hosts.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Containers.ContainerInfo.GetContainerId">
            <summary>
            Gets the id of the container executing the code.
            Return <c>null</c> if code is not executing inside a supported container.
            </summary>
            <returns>The container id or <c>null</c>.</returns>
        </member>
        <member name="M:Datadog.Trace.Containers.ContainerInfo.ParseCgroupLines(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Uses regular expression to try to extract a container id from the specified string.
            </summary>
            <param name="lines">Lines of text from a cgroup file.</param>
            <returns>The container id if found; otherwise, <c>null</c>.</returns>
        </member>
        <member name="M:Datadog.Trace.Containers.ContainerInfo.ParseCgroupLine(System.String)">
            <summary>
            Uses regular expression to try to extract a container id from the specified string.
            </summary>
            <param name="line">A single line from a cgroup file.</param>
            <returns>The container id if found; otherwise, <c>null</c>.</returns>
        </member>
        <member name="T:Datadog.Trace.CorrelationIdentifier">
            <summary>
            An API to access the active trace and span ids.
            </summary>
        </member>
        <member name="P:Datadog.Trace.CorrelationIdentifier.TraceId">
            <summary>
            Gets the trace id
            </summary>
        </member>
        <member name="P:Datadog.Trace.CorrelationIdentifier.SpanId">
            <summary>
            Gets the span id
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Api.Requests">
            <summary>
            Count: Total number of API requests made
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Api.Responses">
            <summary>
            Count: Count of API responses.
            This metric has an additional tag of "status: {code}" to group the responses by the HTTP response code.
            This is different from <seealso cref="F:Datadog.Trace.DogStatsd.TracerMetricNames.Api.Errors"/> in that this is all HTTP responses
            regardless of status code, and <seealso cref="F:Datadog.Trace.DogStatsd.TracerMetricNames.Api.Errors"/> is exceptions raised from making an API call.
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Api.Errors">
            <summary>
            Count: Total number of exceptions raised by API calls.
            This is different from receiving a 4xx or 5xx response.
            It is a "timeout error" or something from making the API call.
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.EnqueuedTraces">
            <summary>
            Count: Total number of traces pushed into the queue (does not include traces dropped due to a full queue)
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.EnqueuedSpans">
            <summary>
            Count: Total number of spans pushed into the queue (does not include traces dropped due to a full queue)
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.EnqueuedBytes">
            <summary>
            Count: Total size in bytes of traces pushed into the queue (does not include traces dropped due to a full queue)
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.DroppedTraces">
            <summary>
            Count: Total number of traces dropped due to a full queue
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.DroppedSpans">
            <summary>
            Count: Total number of spans dropped due to a full queue
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.DequeuedTraces">
            <summary>
            Count: Number of traces pulled from the queue for flushing
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.DequeuedSpans">
            <summary>
            Count: Total number of spans pulled from the queue for flushing
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.DequeuedBytes">
            <summary>
            Count: Size in bytes of traces pulled from the queue for flushing
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Queue.MaxTraces">
            <summary>
            Gauge: The maximum number of traces buffered by the background writer (this is static at 1k for now)
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Health.Heartbeat">
            <summary>
            Gauge: Set to 1 by each Tracer instance.
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Health.Exceptions">
            <summary>
            Count: The number of exceptions thrown by the Tracer.
            </summary>
        </member>
        <member name="F:Datadog.Trace.DogStatsd.TracerMetricNames.Health.Warnings">
            <summary>
            Count: The number of warnings generated by the Tracer.
            </summary>
        </member>
        <member name="T:Datadog.Trace.ExtensionMethods.HttpHeadersExtensions">
            <summary>
            Extension methods for <see cref="T:System.Net.Http.Headers.HttpHeaders"/> objects.
            </summary>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.HttpHeadersExtensions.Wrap(System.Net.Http.Headers.HttpHeaders)">
            <summary>
            Provides an <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/> implementation that wraps the specified <see cref="T:System.Net.Http.Headers.HttpHeaders"/>.
            </summary>
            <param name="headers">The HTTP headers to wrap.</param>
            <returns>An object that implements <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/>.</returns>
        </member>
        <member name="T:Datadog.Trace.ExtensionMethods.NameValueCollectionExtensions">
            <summary>
            Extension methods for <see cref="T:System.Collections.Specialized.NameValueCollection"/> objects.
            </summary>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.NameValueCollectionExtensions.Wrap(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Provides an <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/> implementation that wraps the specified <see cref="T:System.Collections.Specialized.NameValueCollection"/>.
            </summary>
            <param name="collection">The name/value collection to wrap.</param>
            <returns>An object that implements <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/>.</returns>
        </member>
        <member name="T:Datadog.Trace.ExtensionMethods.SpanExtensions">
            <summary>
            Extension methods for the <see cref="T:Datadog.Trace.Span"/> class.
            </summary>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.SpanExtensions.SetTraceSamplingPriority(Datadog.Trace.Span,Datadog.Trace.SamplingPriority)">
            <summary>
            Sets the sampling priority for the trace that contains the specified <see cref="T:Datadog.Trace.Span"/>.
            </summary>
            <param name="span">A span that belongs to the trace.</param>
            <param name="samplingPriority">The new sampling priority for the trace.</param>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.SpanExtensions.AddTagsFromDbCommand(Datadog.Trace.Span,System.Data.IDbCommand)">
            <summary>
            Adds standard tags to a span with values taken from the specified <see cref="T:System.Data.Common.DbCommand"/>.
            </summary>
            <param name="span">The span to add the tags to.</param>
            <param name="command">The db command to get tags values from.</param>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.StringExtensions.TrimEnd(System.String,System.String,System.StringComparison)">
            <summary>
            Removes the trailing occurrence of a substring from the current string.
            </summary>
            <param name="value">The original string.</param>
            <param name="suffix">The string to remove from the end of <paramref name="value"/>.</param>
            <param name="comparisonType">One of the enumeration values that determines how this string and <paramref name="suffix"/> are compared.</param>
            <returns>A new string with <paramref name="suffix"/> removed from the end, if found. Otherwise, <paramref name="value"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.StringExtensions.ToBoolean(System.String)">
            <summary>
            Converts a <see cref="T:System.String"/> into a <see cref="T:System.Boolean"/> by comparing it to commonly used values
            such as "True", "yes", or "1". Case-insensitive. Defaults to <c>false</c> if string is not recognized.
            </summary>
            <param name="value">The string to convert.</param>
            <returns><c>true</c> if <paramref name="value"/> is one of the accepted values for <c>true</c>; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.TimeExtensions.ToUnixTimeNanoseconds(System.DateTimeOffset)">
            <summary>
            Returns the number of nanoseconds that have elapsed since 1970-01-01T00:00:00.000Z.
            </summary>
            <param name="dateTimeOffset">The value to get the number of elapsed nanoseconds for.</param>
            <returns>The number of nanoseconds that have elapsed since 1970-01-01T00:00:00.000Z.</returns>
        </member>
        <member name="T:Datadog.Trace.ExtensionMethods.WebHeadersExtensions">
            <summary>
            Extension methods for <see cref="T:System.Net.WebHeaderCollection"/> objects.
            </summary>
        </member>
        <member name="M:Datadog.Trace.ExtensionMethods.WebHeadersExtensions.Wrap(System.Net.WebHeaderCollection)">
            <summary>
            Provides an <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/> implementation that wraps the specified <see cref="T:System.Net.WebHeaderCollection"/>.
            </summary>
            <param name="headers">The Web headers to wrap.</param>
            <returns>An object that implements <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/>.</returns>
        </member>
        <member name="T:Datadog.Trace.Headers.IHeadersCollection">
            <summary>
            Specified a common interface that can be used to manipulate collections of headers.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Headers.IHeadersCollection.GetValues(System.String)">
            <summary>
            Returns all header values for a specified header stored in the collection.
            </summary>
            <param name="name">The specified header to return values for.</param>
            <returns>Zero or more header strings.</returns>
        </member>
        <member name="M:Datadog.Trace.Headers.IHeadersCollection.Set(System.String,System.String)">
            <summary>
            Sets the value of an entry in the collection, replacing any previous values.
            </summary>
            <param name="name">The header to add to the collection.</param>
            <param name="value">The content of the header.</param>
        </member>
        <member name="M:Datadog.Trace.Headers.IHeadersCollection.Add(System.String,System.String)">
            <summary>
            Adds the specified header and its value into the collection.
            </summary>
            <param name="name">The header to add to the collection.</param>
            <param name="value">The content of the header.</param>
        </member>
        <member name="M:Datadog.Trace.Headers.IHeadersCollection.Remove(System.String)">
            <summary>
            Removes the specified header from the collection.
            </summary>
            <param name="name">The name of the header to remove from the collection.</param>
        </member>
        <member name="T:Datadog.Trace.HttpHeaderNames">
            <summary>
            Names of HTTP headers that can be used tracing inbound or outbound HTTP requests.
            </summary>
        </member>
        <member name="F:Datadog.Trace.HttpHeaderNames.TraceId">
            <summary>
            ID of a distributed trace.
            </summary>
        </member>
        <member name="F:Datadog.Trace.HttpHeaderNames.ParentId">
            <summary>
            ID of the parent span in a distributed trace.
            </summary>
        </member>
        <member name="F:Datadog.Trace.HttpHeaderNames.SamplingPriority">
            <summary>
            Setting used to determine whether a trace should be sampled or not.
            </summary>
        </member>
        <member name="F:Datadog.Trace.HttpHeaderNames.TracingEnabled">
            <summary>
            If header is set to "false", tracing is disabled for that http request.
            Tracing is enabled by default.
            </summary>
        </member>
        <member name="T:Datadog.Trace.ISpanContext">
            <summary>
            Span context interface.
            </summary>
        </member>
        <member name="P:Datadog.Trace.ISpanContext.TraceId">
            <summary>
            Gets the trace identifier.
            </summary>
        </member>
        <member name="P:Datadog.Trace.ISpanContext.SpanId">
            <summary>
            Gets the span identifier.
            </summary>
        </member>
        <member name="P:Datadog.Trace.ISpanContext.ServiceName">
            <summary>
            Gets the service name to propagate to child spans.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Logging.LibLogScopeEventSubscriber">
            <summary>
            Subscriber to ScopeManager events that sets/unsets correlation identifier
            properties in the application's logging context.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Logging.ILog">
            <summary>
                Simple interface that represent a logger.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.ILog.Log(Datadog.Trace.Logging.LogLevel,System.Func{System.String},System.Exception,System.Object[])">
            <summary>
                Log a message the specified log level.
            </summary>
            <param name="logLevel">The log level.</param>
            <param name="messageFunc">The message function.</param>
            <param name="exception">An optional exception.</param>
            <param name="formatParameters">Optional format parameters for the message generated by the messagefunc. </param>
            <returns>true if the message was logged. Otherwise false.</returns>
            <remarks>
                Note to implementers: the message func should not be called if the loglevel is not enabled
                so as not to incur performance penalties.
                To check IsEnabled call Log with only LogLevel and check the return value, no event will be written.
            </remarks>
        </member>
        <member name="T:Datadog.Trace.Logging.ILogProvider">
            <summary>
            Represents a way to get a <see cref="T:Datadog.Trace.Logging.ILog"/>
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.ILogProvider.GetLogger(System.String)">
            <summary>
            Gets the specified named logger.
            </summary>
            <param name="name">Name of the logger.</param>
            <returns>The logger reference.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.ILogProvider.OpenNestedContext(System.String)">
            <summary>
            Opens a nested diagnostics context. Not supported in EntLib logging.
            </summary>
            <param name="message">The message to add to the diagnostics context.</param>
            <returns>A disposable that when disposed removes the message from the context.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.ILogProvider.OpenMappedContext(System.String,System.Object,System.Boolean)">
            <summary>
            Opens a mapped diagnostics context. Not supported in EntLib logging.
            </summary>
            <param name="key">A key.</param>
            <param name="value">A value.</param>
            <param name="destructure">Determines whether to call the destructor or not.</param>
            <returns>A disposable that when disposed removes the map from the context.</returns>
        </member>
        <member name="T:Datadog.Trace.Logging.LogExtensions">
            <summary>
                Extension methods for the <see cref="T:Datadog.Trace.Logging.ILog"/> interface.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.IsDebugEnabled(Datadog.Trace.Logging.ILog)">
            <summary>
                Check if the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level is enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to check with.</param>
            <returns>True if the log level is enabled; false otherwise.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.IsErrorEnabled(Datadog.Trace.Logging.ILog)">
            <summary>
                Check if the <see cref="F:Datadog.Trace.Logging.LogLevel.Error"/> log level is enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to check with.</param>
            <returns>True if the log level is enabled; false otherwise.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.IsFatalEnabled(Datadog.Trace.Logging.ILog)">
            <summary>
                Check if the <see cref="F:Datadog.Trace.Logging.LogLevel.Fatal"/> log level is enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to check with.</param>
            <returns>True if the log level is enabled; false otherwise.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.IsInfoEnabled(Datadog.Trace.Logging.ILog)">
            <summary>
                Check if the <see cref="F:Datadog.Trace.Logging.LogLevel.Info"/> log level is enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to check with.</param>
            <returns>True if the log level is enabled; false otherwise.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.IsTraceEnabled(Datadog.Trace.Logging.ILog)">
            <summary>
                Check if the <see cref="F:Datadog.Trace.Logging.LogLevel.Trace"/> log level is enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to check with.</param>
            <returns>True if the log level is enabled; false otherwise.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.IsWarnEnabled(Datadog.Trace.Logging.ILog)">
            <summary>
                Check if the <see cref="F:Datadog.Trace.Logging.LogLevel.Warn"/> log level is enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to check with.</param>
            <returns>True if the log level is enabled; false otherwise.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Debug(Datadog.Trace.Logging.ILog,System.Func{System.String})">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="messageFunc">The message function.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Debug(Datadog.Trace.Logging.ILog,System.String)">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Debug(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Debug(Datadog.Trace.Logging.ILog,System.Exception,System.String,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.DebugFormat(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.DebugException(Datadog.Trace.Logging.ILog,System.String,System.Exception)">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.DebugException(Datadog.Trace.Logging.ILog,System.String,System.Exception,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Debug"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Error(Datadog.Trace.Logging.ILog,System.Func{System.String})">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Error"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="messageFunc">The message function.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Error(Datadog.Trace.Logging.ILog,System.String)">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Error"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Error(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Error"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Error(Datadog.Trace.Logging.ILog,System.Exception,System.String,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Error"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.ErrorFormat(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Error"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.ErrorException(Datadog.Trace.Logging.ILog,System.String,System.Exception,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Error"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Fatal(Datadog.Trace.Logging.ILog,System.Func{System.String})">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Fatal"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="messageFunc">The message function.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Fatal(Datadog.Trace.Logging.ILog,System.String)">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Fatal"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Fatal(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Fatal"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Fatal(Datadog.Trace.Logging.ILog,System.Exception,System.String,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Fatal"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.FatalFormat(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Fatal"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.FatalException(Datadog.Trace.Logging.ILog,System.String,System.Exception,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Fatal"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Info(Datadog.Trace.Logging.ILog,System.Func{System.String})">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Info"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="messageFunc">The message function.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Info(Datadog.Trace.Logging.ILog,System.String)">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Info"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Info(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Info"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Info(Datadog.Trace.Logging.ILog,System.Exception,System.String,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Info"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.InfoFormat(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Info"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.InfoException(Datadog.Trace.Logging.ILog,System.String,System.Exception,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Info"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Trace(Datadog.Trace.Logging.ILog,System.Func{System.String})">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Trace"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="messageFunc">The message function.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Trace(Datadog.Trace.Logging.ILog,System.String)">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Trace"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Trace(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Trace"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Trace(Datadog.Trace.Logging.ILog,System.Exception,System.String,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Trace"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.TraceFormat(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Trace"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.TraceException(Datadog.Trace.Logging.ILog,System.String,System.Exception,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Trace"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Warn(Datadog.Trace.Logging.ILog,System.Func{System.String})">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Warn"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="messageFunc">The message function.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Warn(Datadog.Trace.Logging.ILog,System.String)">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Warn"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Warn(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Warn"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.Warn(Datadog.Trace.Logging.ILog,System.Exception,System.String,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Warn"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.WarnFormat(Datadog.Trace.Logging.ILog,System.String,System.Object[])">
            <summary>
                Logs a message at the <see cref="F:Datadog.Trace.Logging.LogLevel.Warn"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogExtensions.WarnException(Datadog.Trace.Logging.ILog,System.String,System.Exception,System.Object[])">
            <summary>
                Logs an exception at the <see cref="F:Datadog.Trace.Logging.LogLevel.Warn"/> log level, if enabled.
            </summary>
            <param name="logger">The <see cref="T:Datadog.Trace.Logging.ILog"/> to use.</param>
            <param name="message">The message.</param>
            <param name="exception">The exception.</param>
            <param name="args">Optional format parameters for the message.</param>
        </member>
        <member name="T:Datadog.Trace.Logging.LogLevel">
            <summary>
                The log level.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Logging.LogLevel.Trace">
            <summary>
            Trace
            </summary>
        </member>
        <member name="F:Datadog.Trace.Logging.LogLevel.Debug">
            <summary>
            Debug
            </summary>
        </member>
        <member name="F:Datadog.Trace.Logging.LogLevel.Info">
            <summary>
            Info
            </summary>
        </member>
        <member name="F:Datadog.Trace.Logging.LogLevel.Warn">
            <summary>
            Warn
            </summary>
        </member>
        <member name="F:Datadog.Trace.Logging.LogLevel.Error">
            <summary>
            Error
            </summary>
        </member>
        <member name="F:Datadog.Trace.Logging.LogLevel.Fatal">
            <summary>
            Fatal
            </summary>
        </member>
        <member name="T:Datadog.Trace.Logging.LogProvider">
            <summary>
            Provides a mechanism to create instances of <see cref="T:Datadog.Trace.Logging.ILog" /> objects.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProvider.SetCurrentLogProvider(Datadog.Trace.Logging.ILogProvider)">
            <summary>
            Sets the current log provider.
            </summary>
            <param name="logProvider">The log provider.</param>
        </member>
        <member name="P:Datadog.Trace.Logging.LogProvider.IsDisabled">
            <summary>
            Gets or sets a value indicating whether this is logging is disabled.
            </summary>
            <value>
            <c>true</c> if logging is disabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Datadog.Trace.Logging.LogProvider.OnCurrentLogProviderSet">
            <summary>
            Sets an action that is invoked when a consumer of your library has called SetCurrentLogProvider. It is 
            important that hook into this if you are using child libraries (especially ilmerged ones) that are using
            LibLog (or other logging abstraction) so you adapt and delegate to them.
            <see cref="M:Datadog.Trace.Logging.LogProvider.SetCurrentLogProvider(Datadog.Trace.Logging.ILogProvider)"/> 
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProvider.For``1">
            <summary>
            Gets a logger for the specified type.
            </summary>
            <typeparam name="T">The type whose name will be used for the logger.</typeparam>
            <returns>An instance of <see cref="T:Datadog.Trace.Logging.ILog"/></returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProvider.GetCurrentClassLogger">
            <summary>
            Gets a logger for the current class.
            </summary>
            <returns>An instance of <see cref="T:Datadog.Trace.Logging.ILog"/></returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProvider.GetLogger(System.Type,System.String)">
            <summary>
            Gets a logger for the specified type.
            </summary>
            <param name="type">The type whose name will be used for the logger.</param>
            <param name="fallbackTypeName">If the type is null then this name will be used as the log name instead</param>
            <returns>An instance of <see cref="T:Datadog.Trace.Logging.ILog"/></returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProvider.GetLogger(System.String)">
            <summary>
            Gets a logger with the specified name.
            </summary>
            <param name="name">The name.</param>
            <returns>An instance of <see cref="T:Datadog.Trace.Logging.ILog"/></returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProvider.OpenNestedContext(System.String)">
            <summary>
            Opens a nested diagnostics context.
            </summary>
            <param name="message">A message.</param>
            <returns>An <see cref="T:System.IDisposable"/> that closes context when disposed.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProvider.OpenMappedContext(System.String,System.Object,System.Boolean)">
            <summary>
            Opens a mapped diagnostics context.
            </summary>
            <param name="key">A key.</param>
            <param name="value">A value.</param>
            <param name="destructure">A optional paramater to indicate message should be destructured.</param>
            <returns>An <see cref="T:System.IDisposable"/> that closes context when disposed.</returns>
        </member>
        <member name="T:Datadog.Trace.Logging.LogProviders.LibLogException">
            <summary>
            Exception thrown by LibLog.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LibLogException.#ctor(System.String)">
            <summary>
            Initializes a new LibLogException with the specified message.
            </summary>
            <param name="message">The message</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LibLogException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new LibLogException with the specified message and inner exception.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The inner exception.</param>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LogMessageFormatter.SimulateStructuredLogging(System.Func{System.String},System.Object[])">
            <summary>
                Some logging frameworks support structured logging, such as serilog. This will allow you to add names to structured
                data in a format string:
                For example: Log("Log message to {user}", user). This only works with serilog, but as the user of LibLog, you don't
                know if serilog is actually
                used. So, this class simulates that. it will replace any text in {curly braces} with an index number.
                "Log {message} to {user}" would turn into => "Log {0} to {1}". Then the format parameters are handled using regular
                .net string.Format.
            </summary>
            <param name="messageBuilder">The message builder.</param>
            <param name="formatParameters">The format parameters.</param>
            <returns></returns>
        </member>
        <member name="T:Datadog.Trace.Logging.LogProviders.LogProviderBase">
            <summary>
                Base class for specific log providers.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Logging.LogProviders.LogProviderBase.ErrorInitializingProvider">
            <summary>
                Error message should initializing the log provider fail.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LogProviderBase.#ctor">
            <summary>
                Initialize an instance of the <see cref="T:Datadog.Trace.Logging.LogProviders.LogProviderBase"/> class by initializing the references
                to the nested and mapped diagnostics context-obtaining functions.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LogProviderBase.GetLogger(System.String)">
            <summary>
            Gets the specified named logger.
            </summary>
            <param name="name">Name of the logger.</param>
            <returns>The logger reference.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LogProviderBase.OpenNestedContext(System.String)">
            <summary>
            Opens a nested diagnostics context. Not supported in EntLib logging.
            </summary>
            <param name="message">The message to add to the diagnostics context.</param>
            <returns>A disposable that when disposed removes the message from the context.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LogProviderBase.OpenMappedContext(System.String,System.Object,System.Boolean)">
            <summary>
            Opens a mapped diagnostics context. Not supported in EntLib logging.
            </summary>
            <param name="key">A key.</param>
            <param name="value">A value.</param>
            <param name="destructure">Determines whether to call the destructor or not.</param>
            <returns>A disposable that when disposed removes the map from the context.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LogProviderBase.GetOpenNdcMethod">
            <summary>
                Returns the provider-specific method to open a nested diagnostics context.
            </summary>
            <returns>A provider-specific method to open a nested diagnostics context.</returns>
        </member>
        <member name="M:Datadog.Trace.Logging.LogProviders.LogProviderBase.GetOpenMdcMethod">
            <summary>
                Returns the provider-specific method to open a mapped diagnostics context.
            </summary>
            <returns>A provider-specific method to open a mapped diagnostics context.</returns>
        </member>
        <member name="T:Datadog.Trace.Logging.LogProviders.LogProviderBase.OpenNdc">
            <summary>
                Delegate defining the signature of the method opening a nested diagnostics context.
            </summary>
            <param name="message">The message to add to the diagnostics context.</param>
            <returns>A disposable that when disposed removes the message from the context.</returns>
        </member>
        <member name="T:Datadog.Trace.Logging.LogProviders.LogProviderBase.OpenMdc">
            <summary>
                Delegate defining the signature of the method opening a mapped diagnostics context.
            </summary>
            <param name="key">A key.</param>
            <param name="value">A value.</param>
            <param name="destructure">Determines whether to call the destructor or not.</param>
            <returns>A disposable that when disposed removes the map from the context.</returns>
        </member>
        <member name="T:Datadog.Trace.Logging.LogProviders.LoupeLogProvider.WriteDelegate">
            <summary>
                The form of the Loupe Log.Write method we're using
            </summary>
        </member>
        <member name="P:Datadog.Trace.Logging.LogProviders.LoupeLogProvider.ProviderIsAvailableOverride">
            <summary>
                Gets or sets a value indicating whether [provider is available override]. Used in tests.
            </summary>
            <value>
                <c>true</c> if [provider is available override]; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Datadog.Trace.Logging.Logger">
            <summary>
            Logger delegate.
            </summary>
            <param name="logLevel">The log level</param>
            <param name="messageFunc">The message function</param>
            <param name="exception">The exception</param>
            <param name="formatParameters">The format parameters</param>
            <returns>A boolean.</returns>
        </member>
        <member name="F:Datadog.Trace.Metrics.SamplingAgentDecision">
            <summary>
            To be set when the agent determines the sampling rate for a trace
            Read: Agent Priority Sample Rate
            </summary>
        </member>
        <member name="F:Datadog.Trace.Metrics.SamplingRuleDecision">
            <summary>
            To be set when a sampling rule is applied to a trace
            Read: Sampling Rule Priority Sample Rate
            </summary>
        </member>
        <member name="F:Datadog.Trace.Metrics.SamplingLimitDecision">
            <summary>
            To be set when a rate limiter is applied to a trace.
            Read: Rate Limiter Priority Sample Rate
            </summary>
        </member>
        <member name="T:Datadog.Trace.SamplingPriority">
            <summary>
            A traces sampling priority determines whether is should be kept and stored.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SamplingPriority.UserReject">
            <summary>
            Explicitly ask the backend to not store a trace.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SamplingPriority.AutoReject">
            <summary>
            Used by the built-in sampler to inform the backend that a trace should be rejected and not stored.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SamplingPriority.AutoKeep">
            <summary>
            Used by the built-in sampler to inform the backend that a trace should be kept and stored.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SamplingPriority.UserKeep">
            <summary>
            Explicitly ask the backend to keep a trace.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Sampling.RegexSamplingRule.Priority">
            <summary>
            Gets the Priority of the rule.
            Configuration rules will default to 1 as a priority and rely on order of specification.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Sampling.RuleBasedSampler.RegisterRule(Datadog.Trace.Sampling.ISamplingRule)">
            <summary>
            Will insert a rule according to how high the Priority field is set.
            If the priority is equal to other rules, the new rule will be the last in that priority group.
            </summary>
            <param name="rule">The new rule being registered.</param>
        </member>
        <member name="T:Datadog.Trace.Scope">
            <summary>
            A scope is a handle used to manage the concept of an active span.
            Meaning that at a given time at most one span is considered active and
            all newly created spans that are not created with the ignoreActiveSpan
            parameter will be automatically children of the active span.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Scope.Span">
            <summary>
            Gets the active span wrapped in this scope
            </summary>
        </member>
        <member name="P:Datadog.Trace.Scope.Datadog#Trace#Interfaces#IScope#Span">
            <summary>
            Gets the active span wrapped in this scope
            Proxy to Span without concrete return value
            </summary>
        </member>
        <member name="M:Datadog.Trace.Scope.Close">
            <summary>
            Closes the current scope and makes its parent scope active
            </summary>
        </member>
        <member name="M:Datadog.Trace.Scope.Dispose">
            <summary>
            Closes the current scope and makes its parent scope active
            </summary>
        </member>
        <member name="T:Datadog.Trace.Span">
            <summary>
            A Span represents a logical unit of work in the system. It may be
            related to other spans by parent/children relationships. The span
            tracks the duration of an operation as well as associated metadata in
            the form of a resource name, a service name, and user defined tags.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Span.OperationName">
            <summary>
            Gets or sets operation name
            </summary>
        </member>
        <member name="P:Datadog.Trace.Span.ResourceName">
            <summary>
            Gets or sets the resource name
            </summary>
        </member>
        <member name="P:Datadog.Trace.Span.Type">
            <summary>
            Gets or sets the type of request this span represents (ex: web, db).
            Not to be confused with span kind.
            </summary>
            <seealso cref="T:Datadog.Trace.SpanTypes"/>
        </member>
        <member name="P:Datadog.Trace.Span.Error">
            <summary>
            Gets or sets a value indicating whether this span represents an error
            </summary>
        </member>
        <member name="P:Datadog.Trace.Span.ServiceName">
            <summary>
            Gets or sets the service name.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Span.TraceId">
            <summary>
            Gets the trace's unique identifier.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Span.SpanId">
            <summary>
            Gets the span's unique identifier.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Span.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="M:Datadog.Trace.Span.SetTag(System.String,System.String)">
            <summary>
            Add a the specified tag to this span.
            </summary>
            <param name="key">The tag's key.</param>
            <param name="value">The tag's value.</param>
            <returns>This span to allow method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Span.Datadog#Trace#Interfaces#ISpan#SetTag(System.String,System.String)">
            <summary>
            Add a the specified tag to this span.
            </summary>
            <param name="key">The tag's key.</param>
            <param name="value">The tag's value.</param>
            <returns>This span to allow method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Span.Finish">
            <summary>
            Record the end time of the span and flushes it to the backend.
            After the span has been finished all modifications will be ignored.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Span.Finish(System.DateTimeOffset)">
            <summary>
            Explicitly set the end time of the span and flushes it to the backend.
            After the span has been finished all modifications will be ignored.
            </summary>
            <param name="finishTimestamp">Explicit value for the end time of the Span</param>
        </member>
        <member name="M:Datadog.Trace.Span.Dispose">
            <summary>
            Record the end time of the span and flushes it to the backend.
            After the span has been finished all modifications will be ignored.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Span.SetException(System.Exception)">
            <summary>
            Add the StackTrace and other exception metadata to the span
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:Datadog.Trace.Span.Datadog#Trace#Interfaces#ISpan#SetException(System.Exception)">
            <summary>
            Proxy to SetException without return value
            See <see cref="M:Datadog.Trace.Span.SetException(System.Exception)"/> for more information
            </summary>
            <param name="exception">The exception.</param>
        </member>
        <member name="M:Datadog.Trace.Span.GetTag(System.String)">
            <summary>
            Gets the value (or default/null if the key is not a valid tag) of a tag with the key value passed
            </summary>
            <param name="key">The tag's key</param>
            <returns> The value for the tag with the key specified, or null if the tag does not exist</returns>
        </member>
        <member name="T:Datadog.Trace.SpanContext">
            <summary>
            The SpanContext contains all the information needed to express relationships between spans inside or outside the process boundaries.
            </summary>
        </member>
        <member name="M:Datadog.Trace.SpanContext.#ctor(System.Nullable{System.UInt64},System.UInt64,System.Nullable{Datadog.Trace.SamplingPriority},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.SpanContext"/> class
            from a propagated context. <see cref="P:Datadog.Trace.SpanContext.Parent"/> will be null
            since this is a root context locally.
            </summary>
            <param name="traceId">The propagated trace id.</param>
            <param name="spanId">The propagated span id.</param>
            <param name="samplingPriority">The propagated sampling priority.</param>
            <param name="serviceName">The service name to propagate to child spans.</param>
        </member>
        <member name="M:Datadog.Trace.SpanContext.#ctor(Datadog.Trace.ISpanContext,Datadog.Trace.ITraceContext,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.SpanContext"/> class
            that is the child of the specified parent context.
            </summary>
            <param name="parent">The parent context.</param>
            <param name="traceContext">The trace context.</param>
            <param name="serviceName">The service name to propagate to child spans.</param>
        </member>
        <member name="P:Datadog.Trace.SpanContext.Parent">
            <summary>
            Gets the parent context.
            </summary>
        </member>
        <member name="P:Datadog.Trace.SpanContext.TraceId">
            <summary>
            Gets the trace id
            </summary>
        </member>
        <member name="P:Datadog.Trace.SpanContext.ParentId">
            <summary>
            Gets the span id of the parent span
            </summary>
        </member>
        <member name="P:Datadog.Trace.SpanContext.SpanId">
            <summary>
            Gets the span id
            </summary>
        </member>
        <member name="P:Datadog.Trace.SpanContext.ServiceName">
            <summary>
            Gets or sets the service name to propagate to child spans.
            </summary>
        </member>
        <member name="P:Datadog.Trace.SpanContext.TraceContext">
            <summary>
            Gets the trace context.
            Returns null for contexts created from incoming propagated context.
            </summary>
        </member>
        <member name="P:Datadog.Trace.SpanContext.SamplingPriority">
            <summary>
            Gets the sampling priority for contexts created from incoming propagated context.
            Returns null for local contexts.
            </summary>
        </member>
        <member name="M:Datadog.Trace.SpanContextPropagator.Inject(Datadog.Trace.SpanContext,Datadog.Trace.Headers.IHeadersCollection)">
            <summary>
            Propagates the specified context by adding new headers to a <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/>.
            This locks the sampling priority for <paramref name="context"/>.
            </summary>
            <param name="context">A <see cref="T:Datadog.Trace.SpanContext"/> value that will be propagated into <paramref name="headers"/>.</param>
            <param name="headers">A <see cref="T:Datadog.Trace.Headers.IHeadersCollection"/> to add new headers to.</param>
        </member>
        <member name="M:Datadog.Trace.SpanContextPropagator.Extract(Datadog.Trace.Headers.IHeadersCollection)">
            <summary>
            Extracts a <see cref="T:Datadog.Trace.SpanContext"/> from the values found in the specified headers.
            </summary>
            <param name="headers">The headers that contain the values to be extracted.</param>
            <returns>A new <see cref="T:Datadog.Trace.SpanContext"/> that contains the values obtained from <paramref name="headers"/>.</returns>
        </member>
        <member name="T:Datadog.Trace.SpanKinds">
            <summary>
            A set of standard span kinds that can be used by integrations.
            Not to be confused with span types.
            </summary>
            <seealso cref="T:Datadog.Trace.SpanTypes"/>
        </member>
        <member name="F:Datadog.Trace.SpanKinds.Client">
            <summary>
            A span generated by the client in a client/server architecture.
            </summary>
            <seealso cref="F:Datadog.Trace.Tags.SpanKind"/>
        </member>
        <member name="F:Datadog.Trace.SpanKinds.Server">
            <summary>
            A span generated by the server in a client/server architecture.
            </summary>
            <seealso cref="F:Datadog.Trace.Tags.SpanKind"/>
        </member>
        <member name="F:Datadog.Trace.SpanKinds.Producer">
            <summary>
            A span generated by the producer in a producer/consumer architecture.
            </summary>
            <seealso cref="F:Datadog.Trace.Tags.SpanKind"/>
        </member>
        <member name="F:Datadog.Trace.SpanKinds.Consumer">
            <summary>
            A span generated by the consumer in a producer/consumer architecture.
            </summary>
            <seealso cref="F:Datadog.Trace.Tags.SpanKind"/>
        </member>
        <member name="T:Datadog.Trace.SpanTypes">
            <summary>
            A set of standard span types that can be used by integrations.
            Not to be confused with span kinds.
            </summary>
            <seealso cref="T:Datadog.Trace.SpanKinds"/>
        </member>
        <member name="F:Datadog.Trace.SpanTypes.Redis">
            <summary>
            The span type for a Redis client integration.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SpanTypes.Sql">
            <summary>
            The span type for a SQL client integration.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SpanTypes.Web">
            <summary>
            The span type for a web framework integration (incoming HTTP requests).
            </summary>
        </member>
        <member name="F:Datadog.Trace.SpanTypes.MongoDb">
            <summary>
            The span type for a MongoDB integration.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SpanTypes.Http">
            <summary>
            The span type for an outgoing HTTP integration.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SpanTypes.GraphQL">
            <summary>
            The span type for a GraphQL integration.
            </summary>
        </member>
        <member name="F:Datadog.Trace.SpanTypes.Custom">
            <summary>
            The span type for a custom integration.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Tags">
            <summary>
            Standard span tags used by integrations.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.Env">
            <summary>
            The environment of the profiled service.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.InstrumentationName">
            <summary>
            The name of the integration that generated the span.
            Use OpenTracing tag "component"
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.InstrumentedMethod">
            <summary>
            The name of the method that was instrumented to generate the span.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.SpanKind">
            <summary>
            The kind of span (e.g. client, server). Not to be confused with <see cref="P:Datadog.Trace.Span.Type"/>.
            </summary>
            <seealso cref="T:Datadog.Trace.SpanKinds"/>
        </member>
        <member name="F:Datadog.Trace.Tags.HttpUrl">
            <summary>
            The URL of an HTTP request
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.HttpMethod">
            <summary>
            The method of an HTTP request
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.HttpRequestHeadersHost">
            <summary>
            The host of an HTTP request
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.HttpStatusCode">
            <summary>
            The status code of an HTTP response
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.ErrorMsg">
            <summary>
            The error message of an exception
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.ErrorType">
            <summary>
            The type of an exception
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.ErrorStack">
            <summary>
            The stack trace of an exception
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.DbType">
            <summary>
            The type of database (e.g. mssql, mysql)
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.DbUser">
            <summary>
            The user used to sign into a database
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.DbName">
            <summary>
            The name of the database.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.SqlQuery">
            <summary>
            The query text
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.SqlRows">
            <summary>
            The number of rows returned by a query
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.AspNetRoute">
            <summary>
            The ASP.NET routing template.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.AspNetController">
            <summary>
            The MVC or Web API controller name.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.AspNetAction">
            <summary>
            The MVC or Web API action name.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.OutHost">
            <summary>
            The hostname of a outgoing server connection.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.OutPort">
            <summary>
            The port of a outgoing server connection.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.RedisRawCommand">
            <summary>
            The raw command sent to Redis.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.MongoDbQuery">
            <summary>
            A MongoDB query.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.MongoDbCollection">
            <summary>
            A MongoDB collection name.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.GraphQLOperationName">
            <summary>
            The operation name of the GraphQL request.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.GraphQLOperationType">
            <summary>
            The operation type of the GraphQL request.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.GraphQLSource">
            <summary>
            The source defining the GraphQL request.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.SamplingPriority">
            <summary>
            The sampling priority for the entire trace.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.ForceKeep">
            <summary>
            Obsolete. Use <see cref="F:Datadog.Trace.Tags.ManualKeep"/>.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.ForceDrop">
            <summary>
            Obsolete. Use <see cref="F:Datadog.Trace.Tags.ManualDrop"/>.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.ManualKeep">
            <summary>
            A user-friendly tag that sets the sampling priority to <see cref="F:Datadog.Trace.SamplingPriority.UserKeep"/>.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.ManualDrop">
            <summary>
            A user-friendly tag that sets the sampling priority to <see cref="F:Datadog.Trace.SamplingPriority.UserReject"/>.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.Analytics">
            <summary>
            Configures Trace Analytics.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tags.Language">
            <summary>
            Language tag, applied to root spans that are .NET runtime (e.g., ASP.NET)
            </summary>
        </member>
        <member name="P:Datadog.Trace.TraceContext.SamplingPriority">
            <summary>
            Gets or sets sampling priority.
            Once the sampling priority is locked with <see cref="M:Datadog.Trace.TraceContext.LockSamplingPriority"/>,
            further attempts to set this are ignored.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Tracer">
            <summary>
            The tracer is responsible for creating spans and flushing them to the Datadog agent
            </summary>
        </member>
        <member name="F:Datadog.Trace.Tracer._liveTracerCount">
            <summary>
            The number of Tracer instances that have been created and not yet destroyed.
            This is used in the heartbeat metrics to estimate the number of
            "live" Tracers that could potentially be sending traces to the Agent.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Tracer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Tracer"/> class with default settings.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Tracer.#ctor(Datadog.Trace.Configuration.TracerSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Tracer"/>
            class using the specified <see cref="T:Datadog.Trace.Configuration.IConfigurationSource"/>.
            </summary>
            <param name="settings">
            A <see cref="T:Datadog.Trace.Configuration.TracerSettings"/> instance with the desired settings,
            or null to use the default configuration sources.
            </param>
        </member>
        <member name="M:Datadog.Trace.Tracer.Finalize">
            <summary>
            Finalizes an instance of the <see cref="T:Datadog.Trace.Tracer"/> class.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Tracer.Instance">
            <summary>
            Gets or sets the global tracer object
            </summary>
        </member>
        <member name="P:Datadog.Trace.Tracer.ActiveScope">
            <summary>
            Gets the active scope
            </summary>
        </member>
        <member name="P:Datadog.Trace.Tracer.Datadog#Trace#IDatadogTracer#IsDebugEnabled">
            <summary>
            Gets a value indicating whether debugging mode is enabled.
            </summary>
            <value><c>true</c> is debugging is enabled, otherwise <c>false</c>.</value>
        </member>
        <member name="P:Datadog.Trace.Tracer.DefaultServiceName">
            <summary>
            Gets the default service name for traces where a service name is not specified.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Tracer.Settings">
            <summary>
            Gets this tracer's settings.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Tracer.Datadog#Trace#IDatadogTracer#ScopeManager">
            <summary>
            Gets the tracer's scope manager, which determines which span is currently active, if any.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Tracer.Datadog#Trace#IDatadogTracer#Sampler">
            <summary>
            Gets the <see cref="T:Datadog.Trace.Sampling.ISampler"/> instance used by this <see cref="T:Datadog.Trace.IDatadogTracer"/> instance.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Tracer.Create(System.Uri,System.String,System.Boolean)">
            <summary>
            Create a new Tracer with the given parameters
            </summary>
            <param name="agentEndpoint">The agent endpoint where the traces will be sent (default is http://localhost:8126).</param>
            <param name="defaultServiceName">Default name of the service (default is the name of the executing assembly).</param>
            <param name="isDebugEnabled">Turns on all debug logging (this may have an impact on application performance).</param>
            <returns>The newly created tracer</returns>
        </member>
        <member name="M:Datadog.Trace.Tracer.ActivateSpan(Datadog.Trace.Span,System.Boolean)">
            <summary>
            Make a span active and return a scope that can be disposed to close the span
            </summary>
            <param name="span">The span to activate</param>
            <param name="finishOnClose">If set to false, closing the returned scope will not close the enclosed span </param>
            <returns>A Scope object wrapping this span</returns>
        </member>
        <member name="M:Datadog.Trace.Tracer.StartActive(System.String,Datadog.Trace.ISpanContext,System.String,System.Nullable{System.DateTimeOffset},System.Boolean,System.Boolean)">
            <summary>
            This is a shortcut for <see cref="M:Datadog.Trace.Tracer.StartSpan(System.String,Datadog.Trace.ISpanContext,System.String,System.Nullable{System.DateTimeOffset},System.Boolean)"/> and <see cref="M:Datadog.Trace.Tracer.ActivateSpan(Datadog.Trace.Span,System.Boolean)"/>, it creates a new span with the given parameters and makes it active.
            </summary>
            <param name="operationName">The span's operation name</param>
            <param name="parent">The span's parent</param>
            <param name="serviceName">The span's service name</param>
            <param name="startTime">An explicit start time for that span</param>
            <param name="ignoreActiveScope">If set the span will not be a child of the currently active span</param>
            <param name="finishOnClose">If set to false, closing the returned scope will not close the enclosed span </param>
            <returns>A scope wrapping the newly created span</returns>
        </member>
        <member name="M:Datadog.Trace.Tracer.StartSpan(System.String,Datadog.Trace.ISpanContext,System.String,System.Nullable{System.DateTimeOffset},System.Boolean)">
            <summary>
            Creates a new <see cref="T:Datadog.Trace.Span"/> with the specified parameters.
            </summary>
            <param name="operationName">The span's operation name</param>
            <param name="parent">The span's parent</param>
            <param name="serviceName">The span's service name</param>
            <param name="startTime">An explicit start time for that span</param>
            <param name="ignoreActiveScope">If set the span will not be a child of the currently active span</param>
            <returns>The newly created span</returns>
        </member>
        <member name="M:Datadog.Trace.Tracer.Datadog#Trace#IDatadogTracer#Write(System.Collections.Generic.List{Datadog.Trace.Span})">
            <summary>
            Writes the specified <see cref="T:Datadog.Trace.Span"/> collection to the agent writer.
            </summary>
            <param name="trace">The <see cref="T:Datadog.Trace.Span"/> collection to write.</param>
        </member>
        <member name="M:Datadog.Trace.Tracer.GetAgentUri(Datadog.Trace.Configuration.TracerSettings)">
            <summary>
            Create an Uri to the Agent using host and port from
            the specified <paramref name="settings"/>.
            </summary>
            <param name="settings">A <see cref="T:Datadog.Trace.Configuration.TracerSettings"/> object </param>
            <returns>An Uri that can be used to send traces to the Agent.</returns>
        </member>
        <member name="M:Datadog.Trace.Tracer.GetApplicationName">
            <summary>
            Gets an "application name" for the executing application by looking at
            the hosted app name (.NET Framework on IIS only), assembly name, and process name.
            </summary>
            <returns>The default service name.</returns>
        </member>
        <member name="F:Datadog.Trace.TracerConstants.MaxTraceId">
            <summary>
            2^63-1
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions">
            <summary>Extends <see cref="T:Datadog.Trace.Vendors.Serilog.LoggerConfiguration"/> with methods to add file sinks.</summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,System.Nullable{System.Int64},Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan})">
            <summary>
            Write log events to the specified file.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="path">Path to the file.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
            <param name="outputTemplate">A message template describing the format used to write to the sink.
            the default is "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}".</param>
            <param name="fileSizeLimitBytes">The approximate maximum size, in bytes, to which a log file will be allowed to grow.
            For unrestricted growth, pass null. The default is 1 GB. To avoid writing partial events, the last event within the limit
            will be written in full even if it exceeds the limit.</param>
            <param name="buffered">Indicates if flushing to the output file can be buffered or not. The default
            is false.</param>
            <param name="shared">Allow the log file to be shared by multiple processes. The default is false.</param>
            <param name="flushToDiskInterval">If provided, a full disk flush will be performed periodically at the specified interval.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Nullable{System.Int64},Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan})">
            <summary>
            Write log events to the specified file.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="formatter">A formatter, such as <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter"/>, to convert the log events into
            text for the file. If control of regular text formatting is required, use the other
            overload of <see cref="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,System.Nullable{System.Int64},Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan})"/>
            and specify the outputTemplate parameter instead.
            </param>
            <param name="path">Path to the file.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="fileSizeLimitBytes">The approximate maximum size, in bytes, to which a log file will be allowed to grow.
            For unrestricted growth, pass null. The default is 1 GB. To avoid writing partial events, the last event within the limit
            will be written in full even if it exceeds the limit.</param>
            <param name="buffered">Indicates if flushing to the output file can be buffered or not. The default
            is false.</param>
            <param name="shared">Allow the log file to be shared by multiple processes. The default is false.</param>
            <param name="flushToDiskInterval">If provided, a full disk flush will be performed periodically at the specified interval.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,System.Nullable{System.Int64},Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan},Datadog.Trace.Vendors.Serilog.RollingInterval,System.Boolean,System.Nullable{System.Int32},System.Text.Encoding)">
            <summary>
            Write log events to the specified file.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="path">Path to the file.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
            <param name="outputTemplate">A message template describing the format used to write to the sink.
            the default is "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}".</param>
            <param name="fileSizeLimitBytes">The approximate maximum size, in bytes, to which a log file will be allowed to grow.
            For unrestricted growth, pass null. The default is 1 GB. To avoid writing partial events, the last event within the limit
            will be written in full even if it exceeds the limit.</param>
            <param name="buffered">Indicates if flushing to the output file can be buffered or not. The default
            is false.</param>
            <param name="shared">Allow the log file to be shared by multiple processes. The default is false.</param>
            <param name="flushToDiskInterval">If provided, a full disk flush will be performed periodically at the specified interval.</param>
            <param name="rollingInterval">The interval at which logging will roll over to a new file.</param>
            <param name="rollOnFileSizeLimit">If <code>true</code>, a new file will be created when the file size limit is reached. Filenames 
            will have a number appended in the format <code>_NNN</code>, with the first filename given no number.</param>
            <param name="retainedFileCountLimit">The maximum number of log files that will be retained,
            including the current log file. For unlimited retention, pass null. The default is 31.</param>
            <param name="encoding">Character encoding used to write the text file. The default is UTF-8 without BOM.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Nullable{System.Int64},Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan},Datadog.Trace.Vendors.Serilog.RollingInterval,System.Boolean,System.Nullable{System.Int32},System.Text.Encoding)">
            <summary>
            Write log events to the specified file.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="formatter">A formatter, such as <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter"/>, to convert the log events into
            text for the file. If control of regular text formatting is required, use the other
            overload of <see cref="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,System.Nullable{System.Int64},Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan},Datadog.Trace.Vendors.Serilog.RollingInterval,System.Boolean,System.Nullable{System.Int32},System.Text.Encoding)"/>
            and specify the outputTemplate parameter instead.
            </param>
            <param name="path">Path to the file.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="fileSizeLimitBytes">The approximate maximum size, in bytes, to which a log file will be allowed to grow.
            For unrestricted growth, pass null. The default is 1 GB. To avoid writing partial events, the last event within the limit
            will be written in full even if it exceeds the limit.</param>
            <param name="buffered">Indicates if flushing to the output file can be buffered or not. The default
            is false.</param>
            <param name="shared">Allow the log file to be shared by multiple processes. The default is false.</param>
            <param name="flushToDiskInterval">If provided, a full disk flush will be performed periodically at the specified interval.</param>
            <param name="rollingInterval">The interval at which logging will roll over to a new file.</param>
            <param name="rollOnFileSizeLimit">If <code>true</code>, a new file will be created when the file size limit is reached. Filenames 
            will have a number appended in the format <code>_NNN</code>, with the first filename given no number.</param>
            <param name="retainedFileCountLimit">The maximum number of log files that will be retained,
            including the current log file. For unlimited retention, pass null. The default is 31.</param>
            <param name="encoding">Character encoding used to write the text file. The default is UTF-8 without BOM.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Write log events to the specified file.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="path">Path to the file.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
            <param name="outputTemplate">A message template describing the format used to write to the sink.
            the default is "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}".</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration,Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Write log events to the specified file.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="formatter">A formatter, such as <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter"/>, to convert the log events into
            text for the file. If control of regular text formatting is required, use the other
            overload of <see cref="M:Datadog.Trace.Vendors.Serilog.FileLoggerConfigurationExtensions.File(Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration,System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)"/>
            and specify the outputTemplate parameter instead.
            </param>
            <param name="path">Path to the file.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.RollingInterval">
            <summary>
            Specifies the frequency at which the log file should roll.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.RollingInterval.Infinite">
            <summary>
            The log file will never roll; no time period information will be appended to the log file name.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.RollingInterval.Year">
            <summary>
            Roll every year. Filenames will have a four-digit year appended in the pattern <code>yyyy</code>.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.RollingInterval.Month">
            <summary>
            Roll every calendar month. Filenames will have <code>yyyyMM</code> appended.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.RollingInterval.Day">
            <summary>
            Roll every day. Filenames will have <code>yyyyMMdd</code> appended.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.RollingInterval.Hour">
            <summary>
            Roll every hour. Filenames will have <code>yyyyMMddHH</code> appended.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.RollingInterval.Minute">
            <summary>
            Roll every minute. Filenames will have <code>yyyyMMddHHmm</code> appended.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink">
            <summary>
            Write log events to a disk file.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink.#ctor(System.String,Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter,System.Nullable{System.Int64},System.Text.Encoding,System.Boolean)">
            <summary>Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink"/>.</summary>
            <param name="path">Path to the file.</param>
            <param name="textFormatter">Formatter used to convert log events to text.</param>
            <param name="fileSizeLimitBytes">The approximate maximum size, in bytes, to which a log file will be allowed to grow.
            For unrestricted growth, pass null. The default is 1 GB. To avoid writing partial events, the last event within the limit
            will be written in full even if it exceeds the limit.</param>
            <param name="encoding">Character encoding used to write the text file. The default is UTF-8 without BOM.</param>
            <param name="buffered">Indicates if flushing to the output file can be buffered or not. The default
            is false.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
            <exception cref="T:System.IO.IOException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink.Emit(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Emit the provided log event to the sink.
            </summary>
            <param name="logEvent">The log event to write.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink.FlushToDisk">
            <inheritdoc />
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Sinks.File.IFileSink">
            <summary>
            Exists only for the convenience of <see cref="T:Datadog.Trace.Vendors.Serilog.Sinks.File.RollingFileSink"/>, which
            switches implementations based on sharing. Would refactor, but preserving
            backwards compatibility.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Sinks.File.IFlushableFileSink">
            <summary>
            Supported by (file-based) sinks that can be explicitly flushed.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.IFlushableFileSink.FlushToDisk">
            <summary>
            Flush buffered contents to disk.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Sinks.File.NullSink">
            <summary>
            An instance of this sink may be substituted when an instance of the
            <see cref="T:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink"/> is unable to be constructed.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Sinks.File.PeriodicFlushToDiskSink">
            <summary>
            A sink wrapper that periodically flushes the wrapped sink to disk.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.PeriodicFlushToDiskSink.#ctor(Datadog.Trace.Vendors.Serilog.Core.ILogEventSink,System.TimeSpan)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Sinks.File.PeriodicFlushToDiskSink"/> that wraps
            <paramref name="sink"/> and flushes it at the specified <paramref name="flushInterval"/>.
            </summary>
            <param name="sink">The sink to wrap.</param>
            <param name="flushInterval">The interval at which to flush the underlying sink.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.PeriodicFlushToDiskSink.Emit(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.PeriodicFlushToDiskSink.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Sinks.File.SharedFileSink">
            <summary>
            Write log events to a disk file.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.SharedFileSink.#ctor(System.String,Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter,System.Nullable{System.Int64},System.Text.Encoding)">
            <summary>Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Sinks.File.FileSink"/>.</summary>
            <param name="path">Path to the file.</param>
            <param name="textFormatter">Formatter used to convert log events to text.</param>
            <param name="fileSizeLimitBytes">The approximate maximum size, in bytes, to which a log file will be allowed to grow.
            For unrestricted growth, pass null. The default is 1 GB. To avoid writing partial events, the last event within the limit
            will be written in full even if it exceeds the limit.</param>
            <param name="encoding">Character encoding used to write the text file. The default is UTF-8 without BOM.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>The file will be written using the UTF-8 character set.</remarks>
            <exception cref="T:System.IO.IOException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.SharedFileSink.Emit(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Emit the provided log event to the sink.
            </summary>
            <param name="logEvent">The log event to write.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.SharedFileSink.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Sinks.File.SharedFileSink.FlushToDisk">
            <inheritdoc />
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Capturing.PropertyBinder.ConstructProperties(Datadog.Trace.Vendors.Serilog.Events.MessageTemplate,System.Object[])">
            <summary>
            Create properties based on an ordered list of provided values.
            </summary>
            <param name="messageTemplate">The template that the parameters apply to.</param>
            <param name="messageTemplateParameters">Objects corresponding to the properties
            represented in the message template.</param>
            <returns>A list of properties; if the template is malformed then
            this will be empty.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.ILoggerSettings">
            <summary>
            Implemented on types that apply settings to a logger configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.ILoggerSettings.Configure(Datadog.Trace.Vendors.Serilog.LoggerConfiguration)">
            <summary>
            Apply the settings to the logger configuration.
            </summary>
            <param name="loggerConfiguration">The logger configuration to apply settings to.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration">
            <summary>
            Controls audit sink configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration.Sink(Datadog.Trace.Vendors.Serilog.Core.ILogEventSink,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Audit log events to the specified <see cref="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink"/>.
            </summary>
            <param name="logEventSink">The sink.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration.Sink``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Audit log events to the specified <see cref="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink"/>.
            </summary>
            <typeparam name="TSink">The sink.</typeparam>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration.Logger(System.Action{Datadog.Trace.Vendors.Serilog.LoggerConfiguration},Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Audit log events to a sub-logger, where further processing may occur. Events through
            the sub-logger will be constrained by filters and enriched by enrichers that are
            active in the parent. A sub-logger cannot be used to log at a more verbose level, but
            a less verbose level is possible.
            </summary>
            <param name="configureLogger">An action that configures the sub-logger.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration.Logger(Datadog.Trace.Vendors.Serilog.ILogger,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Audit log events to a sub-logger, where further processing may occur. Events through
            the sub-logger will be constrained by filters and enriched by enrichers that are
            active in the parent. A sub-logger cannot be used to log at a more verbose level, but
            a less verbose level is possible.
            </summary>
            <param name="logger">The sub-logger. This will <em>not</em> be shut down automatically when the
            parent logger is disposed.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration">
            <summary>
            Controls template parameter destructuring configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.AsScalar(System.Type)">
            <summary>
            Treat objects of the specified type as scalar values, i.e., don't break
            them down into properties event when destructuring complex types.
            </summary>
            <param name="scalarType">Type to treat as scalar.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.AsScalar``1">
            <summary>
            Treat objects of the specified type as scalar values, i.e., don't break
            them down into properties event when destructuring complex types.
            </summary>
            <typeparam name="TScalar">Type to treat as scalar.</typeparam>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.With(Datadog.Trace.Vendors.Serilog.Core.IDestructuringPolicy[])">
            <summary>
            When destructuring objects, transform instances with the provided policies.
            </summary>
            <param name="destructuringPolicies">Policies to apply when destructuring.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.With``1">
            <summary>
            When destructuring objects, transform instances with the provided policy.
            </summary>
            <typeparam name="TDestructuringPolicy">Policy to apply when destructuring.</typeparam>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.ByTransforming``1(System.Func{``0,System.Object})">
            <summary>
            When destructuring objects, transform instances of the specified type with
            the provided function.
            </summary>
            <param name="transformation">Function mapping instances of <typeparamref name="TValue"/>
            to an alternative representation.</param>
            <typeparam name="TValue">Type of values to transform.</typeparam>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.ByTransformingWhere``1(System.Func{System.Type,System.Boolean},System.Func{``0,System.Object})">
            <summary>
            When destructuring objects, transform instances of the specified type with
            the provided function, if the predicate returns true. Be careful to avoid any
            intensive work in the predicate, as it can slow down the pipeline significantly.
            </summary>
            <param name="predicate">A predicate used to determine if the transform applies to
            a specific type of value</param>
            <param name="transformation">Function mapping instances of <typeparamref name="TValue"/>
            to an alternative representation.</param>
            <typeparam name="TValue">Type of values to transform.</typeparam>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.ToMaximumDepth(System.Int32)">
            <summary>
            When destructuring objects, depth will be limited to 5 property traversals deep to
            guard against ballooning space when recursive/cyclic structures are accidentally passed. To
            increase this limit pass a higher value.
            </summary>
            <param name="maximumDestructuringDepth">The maximum depth to use.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.ToMaximumStringLength(System.Int32)">
            <summary>
            When destructuring objects, string values can be restricted to specified length
            thus avoiding bloating payload. Limit is applied to each value separately, 
            sum of length of strings can exceed limit.
            </summary>
            <param name="maximumStringLength">The maximum string length.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">When passed length is less than 2</exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerDestructuringConfiguration.ToMaximumCollectionCount(System.Int32)">
            <summary>
            When destructuring objects, collections be restricted to specified count
            thus avoiding bloating payload. Limit is applied to each collection separately, 
            sum of length of collection can exceed limit.
            Applies limit to all <see cref="T:System.Collections.IEnumerable"/> including dictionaries.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">When passed length is less than 1</exception>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerEnrichmentConfiguration">
            <summary>
            Controls enrichment configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerEnrichmentConfiguration.With(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher[])">
            <summary>
            Specifies one or more enrichers that may add properties dynamically to
            log events.
            </summary>
            <param name="enrichers">Enrichers to apply to all events passing through
            the logger.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerEnrichmentConfiguration.With``1">
            <summary>
            Specifies an enricher that may add properties dynamically to
            log events.
            </summary>
            <typeparam name="TEnricher">Enricher type to apply to all events passing through
            the logger.</typeparam>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerEnrichmentConfiguration.WithProperty(System.String,System.Object,System.Boolean)">
            <summary>
            Include the specified property value in all events logged to the logger.
            </summary>
            <param name="name">The name of the property to add.</param>
            <param name="value">The property value to add.</param>
            <param name="destructureObjects">If true, objects of unknown type will be logged as structures; otherwise they will be converted using <see cref="M:System.Object.ToString"/>.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerEnrichmentConfiguration.FromLogContext">
            <summary>
            Enrich log events with properties from <see cref="T:Datadog.Trace.Vendors.Serilog.Context.LogContext"/>.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerFilterConfiguration">
            <summary>
            Controls filter configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerFilterConfiguration.With(Datadog.Trace.Vendors.Serilog.Core.ILogEventFilter[])">
            <summary>
            Filter out log events from the stream based on the provided filter.
            </summary>
            <param name="filters">The filters to apply.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerFilterConfiguration.With``1">
            <summary>
            Filter out log events from the stream based on the provided filter.
            </summary>
            <typeparam name="TFilter">The filters to apply.</typeparam>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerFilterConfiguration.ByExcluding(System.Func{Datadog.Trace.Vendors.Serilog.Events.LogEvent,System.Boolean})">
            <summary>
            Filter out log events that match a predicate.
            </summary>
            <param name="exclusionPredicate">Function that returns true when an event
            should be excluded (silenced).</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerFilterConfiguration.ByIncludingOnly(System.Func{Datadog.Trace.Vendors.Serilog.Events.LogEvent,System.Boolean})">
            <summary>
            Filter log events to include only those that match a predicate.
            </summary>
            <param name="inclusionPredicate">Function that returns true when an event
            should be included (emitted).</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration">
            <summary>
            Controls sink configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Is(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Sets the minimum level at which events will be passed to sinks.
            </summary>
            <param name="minimumLevel">The minimum level to set.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.ControlledBy(Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Sets the minimum level to be dynamically controlled by the provided switch.
            </summary>
            <param name="levelSwitch">The switch.</param>
            <returns>Configuration object allowing method chaining.</returns>        
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Verbose">
            <summary>
            Anything and everything you might want to know about
            a running block of code.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Debug">
            <summary>
            Internal system events that aren't necessarily
            observable from the outside.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Information">
            <summary>
            The lifeblood of operational intelligence - things
            happen.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Warning">
            <summary>
            Service is degraded or endangered.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Error">
            <summary>
            Functionality is unavailable, invariants are broken
            or data is lost.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Fatal">
            <summary>
            If you have a pager, it goes off when one of these
            occurs.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Override(System.String,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Override the minimum level for events from a specific namespace or type name.
            </summary>
            <param name="source">The (partial) namespace or type name to set the override for.</param>
            <param name="levelSwitch">The switch controlling loggers for matching sources.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerMinimumLevelConfiguration.Override(System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Override the minimum level for events from a specific namespace or type name.
            </summary>
            <param name="source">The (partial) namespace or type name to set the override for.</param>
            <param name="minimumLevel">The minimum level applied to loggers for matching sources.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSettingsConfiguration">
            <summary>
            Allows additional setting sources to drive the logger configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSettingsConfiguration.Settings(Datadog.Trace.Vendors.Serilog.Configuration.ILoggerSettings)">
            <summary>
            Apply external settings to the logger configuration.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSettingsConfiguration.KeyValuePairs(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Apply settings specified in the Serilog key-value setting format to the logger configuration.
            </summary>
            <param name="settings">A list of key-value pairs describing logger settings.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>In case of duplicate keys, the last value for the key is kept and the previous ones are ignored.</remarks>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration">
            <summary>
            Controls sink configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration.Sink(Datadog.Trace.Vendors.Serilog.Core.ILogEventSink,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Write log events to the specified <see cref="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink"/>.
            </summary>
            <param name="logEventSink">The sink.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <remarks>Provided for binary compatibility for earlier versions,
            should be removed in 3.0. Not marked obsolete because warnings
            would be syntactically annoying to avoid.</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration.Sink(Datadog.Trace.Vendors.Serilog.Core.ILogEventSink,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Write log events to the specified <see cref="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink"/>.
            </summary>
            <param name="logEventSink">The sink.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration.Sink``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Write log events to the specified <see cref="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink"/>.
            </summary>
            <typeparam name="TSink">The sink.</typeparam>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration.Logger(System.Action{Datadog.Trace.Vendors.Serilog.LoggerConfiguration},Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Write log events to a sub-logger, where further processing may occur. Events through
            the sub-logger will be constrained by filters and enriched by enrichers that are
            active in the parent. A sub-logger cannot be used to log at a more verbose level, but
            a less verbose level is possible.
            </summary>
            <param name="configureLogger">An action that configures the sub-logger.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration.Logger(Datadog.Trace.Vendors.Serilog.ILogger,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Write log events to a sub-logger, where further processing may occur. Events through
            the sub-logger will be constrained by filters and enriched by enrichers that are
            active in the parent. A sub-logger cannot be used to log at a more verbose level, but
            a less verbose level is possible.
            </summary>
            <param name="logger">The sub-logger. This will <em>not</em> be shut down automatically when the
            parent logger is disposed.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration.Wrap(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,System.Func{Datadog.Trace.Vendors.Serilog.Core.ILogEventSink,Datadog.Trace.Vendors.Serilog.Core.ILogEventSink},System.Action{Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration})">
            <summary>
            Helper method for wrapping sinks.
            </summary>
            <param name="loggerSinkConfiguration">The parent sink configuration.</param>
            <param name="wrapSink">A function that allows for wrapping <see cref="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink"/>s
            added in <paramref name="configureWrappedSink"/>.</param>
            <param name="configureWrappedSink">An action that configures sinks to be wrapped in <paramref name="wrapSink"/>.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration.Wrap(Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration,System.Func{Datadog.Trace.Vendors.Serilog.Core.ILogEventSink,Datadog.Trace.Vendors.Serilog.Core.ILogEventSink},System.Action{Datadog.Trace.Vendors.Serilog.Configuration.LoggerSinkConfiguration},Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)">
            <summary>
            Helper method for wrapping sinks.
            </summary>
            <param name="loggerSinkConfiguration">The parent sink configuration.</param>
            <param name="wrapSink">A function that allows for wrapping <see cref="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink"/>s
            added in <paramref name="configureWrappedSink"/>.</param>
            <param name="configureWrappedSink">An action that configures sinks to be wrapped in <paramref name="wrapSink"/>.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Context.LogContext">
            <summary>
            Holds ambient properties that can be attached to log events. To
            configure, use the <see cref="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerEnrichmentConfiguration.FromLogContext"/> method.
            </summary>
            <example>
            Configuration:
            <code lang="C#">
            var log = new LoggerConfiguration()
                .Enrich.FromLogContext()
                ...
            </code>
            Usage:
            <code lang="C#">
            using (LogContext.PushProperty("MessageId", message.Id))
            {
                Log.Information("The MessageId property will be attached to this event");
            }
            </code>
            </example>
            <remarks>The scope of the context is the current logical thread, using AsyncLocal
            (and so is preserved across async/await calls).</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.PushProperty(System.String,System.Object,System.Boolean)">
            <summary>
            Push a property onto the context, returning an <see cref="T:System.IDisposable"/>
            that must later be used to remove the property, along with any others that
            may have been pushed on top of it and not yet popped. The property must
            be popped from the same thread/logical call context.
            </summary>
            <param name="name">The name of the property.</param>
            <param name="value">The value of the property.</param>
            <returns>A handle to later remove the property from the context.</returns>
            <param name="destructureObjects">If true, and the value is a non-primitive, non-array type,
            then the value will be converted to a structure; otherwise, unknown types will
            be converted to scalars, which are generally stored as strings.</param>
            <returns>A token that must be disposed, in order, to pop properties back off the stack.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.Push(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher)">
            <summary>
            Push an enricher onto the context, returning an <see cref="T:System.IDisposable"/>
            that must later be used to remove the property, along with any others that
            may have been pushed on top of it and not yet popped. The property must
            be popped from the same thread/logical call context.
            </summary>
            <param name="enricher">An enricher to push onto the log context</param>
            <returns>A token that must be disposed, in order, to pop properties back off the stack.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.Push(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher[])">
            <summary>
            Push multiple enrichers onto the context, returning an <see cref="T:System.IDisposable"/>
            that must later be used to remove the property, along with any others that
            may have been pushed on top of it and not yet popped. The property must
            be popped from the same thread/logical call context.
            </summary>
            <seealso cref="T:Datadog.Trace.Vendors.Serilog.Core.Enrichers.PropertyEnricher"/>.
            <param name="enrichers">Enrichers to push onto the log context</param>
            <returns>A token that must be disposed, in order, to pop properties back off the stack.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.PushProperties(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher[])">
            <summary>
            Push enrichers onto the log context. This method is obsolete, please
            use <see cref="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.Push(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher[])"/> instead.
            </summary>
            <param name="properties">Enrichers to push onto the log context</param>
            <returns>A token that must be disposed, in order, to pop properties back off the stack.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.Clone">
            <summary>
            Obtain an enricher that represents the current contents of the <see cref="T:Datadog.Trace.Vendors.Serilog.Context.LogContext"/>. This
            can be pushed back onto the context in a different location/thread when required.
            </summary>
            <returns>An enricher that represents the current contents of the <see cref="T:Datadog.Trace.Vendors.Serilog.Context.LogContext"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.Suspend">
            <summary>
            Remove all enrichers from the <see cref="T:Datadog.Trace.Vendors.Serilog.Context.LogContext"/>, returning an <see cref="T:System.IDisposable"/>
            that must later be used to restore enrichers that were on the stack before <see cref="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.Suspend"/> was called.
            </summary>
            <returns>A token that must be disposed, in order, to restore properties back to the stack.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Context.LogContext.Reset">
            <summary>
            Remove all enrichers from <see cref="T:Datadog.Trace.Vendors.Serilog.Context.LogContext"/> for the current async scope. 
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.Constants">
            <summary>
            Constants used in the core logging pipeline and associated types.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Core.Constants.SourceContextPropertyName">
            <summary>
            The name of the property included in the emitted log events
            when <code>ForContext&lt;T&gt;()</code> and overloads are
            applied.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.Enrichers.PropertyEnricher">
            <summary>
            Adds a new property encricher to the log event.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Enrichers.PropertyEnricher.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Create a new property enricher.
            </summary>
            <param name="name">The name of the property.</param>
            <param name="value">The value of the property.</param>
            <returns>A handle to later remove the property from the context.</returns>
            <param name="destructureObjects">If true, and the value is a non-primitive, non-array type,
            then the value will be converted to a structure; otherwise, unknown types will
            be converted to scalars, which are generally stored as strings.</param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Enrichers.PropertyEnricher.Enrich(Datadog.Trace.Vendors.Serilog.Events.LogEvent,Datadog.Trace.Vendors.Serilog.Core.ILogEventPropertyFactory)">
            <summary>
            Enrich the log event.
            </summary>
            <param name="logEvent">The log event to enrich.</param>
            <param name="propertyFactory">Factory for creating new properties to add to the event.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.IDestructuringPolicy">
            <summary>
            Determine how, when destructuring, a supplied value is represented
            as a complex log event property.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.IDestructuringPolicy.TryDestructure(System.Object,Datadog.Trace.Vendors.Serilog.Core.ILogEventPropertyValueFactory,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue@)">
            <summary>
            If supported, destructure the provided value.
            </summary>
            <param name="value">The value to destructure.</param>
            <param name="propertyValueFactory">Recursively apply policies to destructure additional values.</param>
            <param name="result">The destructured value, or null.</param>
            <returns>True if the value could be destructured under this policy.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher">
            <summary>
            Applied during logging to add additional information to log events.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher.Enrich(Datadog.Trace.Vendors.Serilog.Events.LogEvent,Datadog.Trace.Vendors.Serilog.Core.ILogEventPropertyFactory)">
            <summary>
            Enrich the log event.
            </summary>
            <param name="logEvent">The log event to enrich.</param>
            <param name="propertyFactory">Factory for creating new properties to add to the event.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventFilter">
            <summary>
            Provides filtering of the log event stream.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.ILogEventFilter.IsEnabled(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Returns true if the provided event is enabled. Otherwise, false.
            </summary>
            <param name="logEvent">The event to test.</param>
            <returns>True if the event is enabled by this filter. If false
            is returned, the event will not be emitted.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventPropertyFactory">
            <summary>
            Creates log event properties from regular .NET objects, applying policies as
            required.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.ILogEventPropertyFactory.CreateProperty(System.String,System.Object,System.Boolean)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty"/> with the specified name and value.
            </summary>
            <param name="name">The name of the property.</param>
            <param name="value">The value of the property.</param>
            <param name="destructureObjects">If true, and the value is a non-primitive, non-array type,
            then the value will be converted to a structure; otherwise, unknown types will
            be converted to scalars, which are generally stored as strings.</param>
            <returns></returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventPropertyValueFactory">
            <summary>
            Supports the policy-driven construction of <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue"/>s given
            regular .NET objects.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.ILogEventPropertyValueFactory.CreatePropertyValue(System.Object,System.Boolean)">
            <summary>
            Create a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue"/> given a .NET object and destructuring
            strategy.
            </summary>
            <param name="value">The value of the property.</param>
            <param name="destructureObjects">If true, and the value is a non-primitive, non-array type,
            then the value will be converted to a structure; otherwise, unknown types will
            be converted to scalars, which are generally stored as strings.</param>
            <returns>The value.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink">
            <summary>
            A destination for log events.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.ILogEventSink.Emit(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Emit the provided log event to the sink.
            </summary>
            <param name="logEvent">The log event to write.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.IScalarConversionPolicy">
            <summary>
            Determine how a simple value is carried through the logging
            pipeline as an immutable <see cref="T:Datadog.Trace.Vendors.Serilog.Events.ScalarValue"/>.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.IScalarConversionPolicy.TryConvertToScalar(System.Object,Datadog.Trace.Vendors.Serilog.Events.ScalarValue@)">
            <summary>
            If supported, convert the provided value into an immutable scalar.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="result">The converted value, or null.</param>
            <returns>True if the value could be converted under this policy.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.Logger">
            <summary>
            The core Serilog logging pipeline. A <see cref="T:Datadog.Trace.Vendors.Serilog.Core.Logger"/> must
            be disposed to flush any events buffered within it. Most application
            code should depend on <see cref="T:Datadog.Trace.Vendors.Serilog.ILogger"/>, not this class.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.ForContext(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher)">
            <summary>
            Create a logger that enriches log events via the provided enrichers.
            </summary>
            <param name="enricher">Enricher that applies in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.ForContext(System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher})">
            <summary>
            Create a logger that enriches log events via the provided enrichers.
            </summary>
            <param name="enrichers">Enrichers that apply in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.ForContext(System.String,System.Object,System.Boolean)">
            <summary>
            Create a logger that enriches log events with the specified property.
            </summary>
            <param name="propertyName">The name of the property. Must be non-empty.</param>
            <param name="value">The property value.</param>
            <param name="destructureObjects">If true, the value will be serialized as a structured
            object if possible; if false, the object will be recorded as a scalar or simple array.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.ForContext(System.Type)">
            <summary>
            Create a logger that marks log events as being from the specified
            source type.
            </summary>
            <param name="source">Type generating log messages in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.ForContext``1">
            <summary>
            Create a logger that marks log events as being from the specified
            source type.
            </summary>
            <typeparam name="TSource">Type generating log messages in the context.</typeparam>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write``2(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0,``1)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write``3(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.Object[])">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate"></param>
            <param name="propertyValues"></param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.IsEnabled(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Determine if events at the specified level will be passed through
            to the log sinks.
            </summary>
            <param name="level">Level to check.</param>
            <returns>True if the level is enabled; otherwise, false.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write``2(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write``3(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Write an event to the log.
            </summary>
            <param name="logEvent">The event to write.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Verbose(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Information(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Warning(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Fatal(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.BindMessageTemplate(System.String,System.Object[],Datadog.Trace.Vendors.Serilog.Events.MessageTemplate@,System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Events.LogEventProperty}@)">
            <summary>
            Uses configured scalar conversion and destructuring rules to bind a set of properties to a
            message template. Returns false if the template or values are invalid (<summary>ILogger</summary>
            methods never throw exceptions).
            </summary>
            <param name="messageTemplate">Message template describing an event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <param name="parsedTemplate">The internal representation of the template, which may be used to
            render the <paramref name="boundProperties"/> as text.</param>
            <param name="boundProperties">Captured properties from the template and <paramref name="propertyValues"/>.</param>
            <example>
            MessageTemplate template;
            IEnumerable&lt;LogEventProperty&gt; properties>;
            if (Log.BindMessageTemplate("Hello, {Name}!", new[] { "World" }, out template, out properties)
            {
                var propsByName = properties.ToDictionary(p => p.Name, p => p.Value);
                Console.WriteLine(template.Render(propsByName, null));
                // -> "Hello, World!"
            }
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.BindProperty(System.String,System.Object,System.Boolean,Datadog.Trace.Vendors.Serilog.Events.LogEventProperty@)">
            <summary>
            Uses configured scalar conversion and destructuring rules to bind a property value to its captured
            representation.
            </summary>
            <returns>True if the property could be bound, otherwise false (<summary>ILogger</summary>
            <param name="propertyName">The name of the property. Must be non-empty.</param>
            <param name="value">The property value.</param>
            <param name="destructureObjects">If true, the value will be serialized as a structured
            object if possible; if false, the object will be recorded as a scalar or simple array.</param>
            <param name="property">The resulting property.</param>
            methods never throw exceptions).</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.Logger.Dispose">
            <summary>
            Close and flush the logging pipeline.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Core.Logger.None">
            <summary>
            An <see cref="T:Datadog.Trace.Vendors.Serilog.ILogger"/> instance that efficiently ignores all method calls.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch">
            <summary>
            Dynamically controls logging level.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch.#ctor(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Create a <see cref="T:Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch"/> at the initial
            minimum level.
            </summary>
            <param name="initialMinimumLevel">The initial level to which the switch is set.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch.MinimumLevel">
            <summary>
            The current minimum level, below which no events
            should be generated.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.MessageTemplateFormatMethodAttribute">
             <summary>
             Indicates that the marked method logs data using a message template and (optional) arguments.
             The name of the parameter which contains the message template should be given in the constructor.
             </summary>
             <example>
             <code>
             [LoggerMethod("messageTemplate")]
             public void Information(string messageTemplate, params object[] propertyValues)
             {
                 // Do something
             }
            
             public void Foo()
             {
                 Information("Hello, {Name}!") // Warning: Non-existing argument in message template.
             }
             </code>
             </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Core.MessageTemplateFormatMethodAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Datadog.Trace.Vendors.Serilog.Core.MessageTemplateFormatMethodAttribute"/> class.
            </summary>
            <param name="messageTemplateParameterName">Name of the message template parameter.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Core.MessageTemplateFormatMethodAttribute.MessageTemplateParameterName">
            <summary>
            Gets the name of the message template parameter.
            </summary>
            <value>The name of the message template parameter.</value>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Core.Sinks.SecondaryLoggerSink">
            <summary>
            Forwards log events to another logging pipeline. Copies the events so
            that mutations performed on the copies do not affect the originals.
            </summary>
            <remarks>The properties dictionary is copied, however the values within
            the dictionary (of type <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty"/> are expected to
            be immutable.</remarks>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueRewriter`1">
            <summary>
            A base class for visitors that rewrite the value with modifications. For example, implementations
            might remove all structure properties with a certain name, apply size/length limits, or convert scalar properties of
            one type into scalar properties of another.
            </summary>
            <typeparam name="TState"></typeparam>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueRewriter`1.VisitScalarValue(`0,Datadog.Trace.Vendors.Serilog.Events.ScalarValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.ScalarValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="scalar">The value to visit.</param>
            <returns>The result of visiting <paramref name="scalar"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueRewriter`1.VisitSequenceValue(`0,Datadog.Trace.Vendors.Serilog.Events.SequenceValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.SequenceValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="sequence">The value to visit.</param>
            <returns>The result of visiting <paramref name="sequence"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueRewriter`1.VisitStructureValue(`0,Datadog.Trace.Vendors.Serilog.Events.StructureValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.StructureValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="structure">The value to visit.</param>
            <returns>The result of visiting <paramref name="structure"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueRewriter`1.VisitDictionaryValue(`0,Datadog.Trace.Vendors.Serilog.Events.DictionaryValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="dictionary">The value to visit.</param>
            <returns>The result of visiting <paramref name="dictionary"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueRewriter`1.VisitUnsupportedValue(`0,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue)">
            <summary>
            Visit a value of an unsupported type. Returns the value unchanged.
            </summary>
            <param name="state">Operation state.</param>
            <param name="value">The value to visit.</param>
            <returns>The result of visiting <paramref name="value"/>.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueVisitor`2">
            <summary>
            An abstract base class for visitors that walk data in the
            <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue"/> format. Subclasses, by
            overriding appropriate methods, may search for, transform,
            or print the value structures being visited.
            </summary>
            <remarks>
            Stateless, designed to accommodate allocation-free visiting of multiple
            values by the same visitor instance.
            </remarks>
            <typeparam name="TState">The type of a state object passed through
            the visiting process.</typeparam>
            <typeparam name="TResult">The type of the result generated by visiting
            a node.</typeparam>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueVisitor`2.Visit(`0,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue)">
            <summary>
            Visit the root node type. This method delegates to
            a concrete Visit*Value() method appropriate for the value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="value">The value to visit.</param>
            <returns>The result of visiting <paramref name="value"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueVisitor`2.VisitScalarValue(`0,Datadog.Trace.Vendors.Serilog.Events.ScalarValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.ScalarValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="scalar">The value to visit.</param>
            <returns>The result of visiting <paramref name="scalar"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueVisitor`2.VisitSequenceValue(`0,Datadog.Trace.Vendors.Serilog.Events.SequenceValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.SequenceValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="sequence">The value to visit.</param>
            <returns>The result of visiting <paramref name="sequence"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueVisitor`2.VisitStructureValue(`0,Datadog.Trace.Vendors.Serilog.Events.StructureValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.StructureValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="structure">The value to visit.</param>
            <returns>The result of visiting <paramref name="structure"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueVisitor`2.VisitDictionaryValue(`0,Datadog.Trace.Vendors.Serilog.Events.DictionaryValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="dictionary">The value to visit.</param>
            <returns>The result of visiting <paramref name="dictionary"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Data.LogEventPropertyValueVisitor`2.VisitUnsupportedValue(`0,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue)">
            <summary>
            Visit a value of an unsupported type.
            </summary>
            <param name="state">Operation state.</param>
            <param name="value">The value to visit.</param>
            <returns>The result of visiting <paramref name="value"/>.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Debugging.LoggingFailedException">
            <summary>
            May be thrown by log event sinks when a failure occurs. Should not be used in cases
            where the exception would propagate out to callers.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Debugging.LoggingFailedException.#ctor(System.String)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Debugging.LoggingFailedException"/> to communicate a logging failure.
            </summary>
            <param name="message">A message describing the logging failure.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Debugging.SelfLog">
            <summary>
            A simple source of information generated by Serilog itself,
            for example when exceptions are thrown and caught internally.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Debugging.SelfLog.Out">
            <summary>
            The output mechanism for self-log messages.
            </summary>
            <example>
            SelfLog.Out = Console.Error;
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Debugging.SelfLog.Enable(System.IO.TextWriter)">
            <summary>
            Set the output mechanism for self-log messages.
            </summary>
            <param name="output">A synchronized <see cref="T:System.IO.TextWriter"/> to which
            self-log messages will be written.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Debugging.SelfLog.Enable(System.Action{System.String})">
            <summary>
            Set the output mechanism for self-log messages.
            </summary>
            <param name="output">An action to invoke with self-log messages.</param>
            // ReSharper disable once MemberCanBePrivate.Global
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Debugging.SelfLog.Disable">
            <summary>
            Clear the output mechanism and disable self-log events.
            </summary>
            // ReSharper disable once MemberCanBePrivate.Global
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Debugging.SelfLog.WriteLine(System.String,System.Object,System.Object,System.Object)">
            <summary>
            Write a message to the self-log.
            </summary>
            <param name="format">Standard .NET format string containing the message.</param>
            <param name="arg0">First argument, if supplied.</param>
            <param name="arg1">Second argument, if supplied.</param>
            <param name="arg2">Third argument, if supplied.</param>
            <remarks>
            The name is historical; because this is used from third-party sink packages, removing the "Line"
            suffix as would seem sensible isn't worth the breakage.
            </remarks>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue">
            <summary>
            A value represented as a mapping from keys to values.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{Datadog.Trace.Vendors.Serilog.Events.ScalarValue,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue}})">
            <summary>
            Create a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue"/> with the provided <paramref name="elements"/>.
            </summary>
            <param name="elements">The key-value mappings represented in the dictionary.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue.Elements">
            <summary>
            The dictionary mapping.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue.Render(System.IO.TextWriter,System.String,System.IFormatProvider)">
            <summary>
            Render the value to the output.
            </summary>
            <param name="output">The output.</param>
            <param name="format">A format string applied to the value, or null.</param>
            <param name="formatProvider">A format provider to apply to the value, or null to use the default.</param>
            <seealso cref="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.ToString(System.String,System.IFormatProvider)"/>.
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.LevelAlias">
            <summary>
            Descriptive aliases for <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel"/>.
            </summary>
            <remarks>These do not appear as members of the enumeration
            as duplicated underlying values result in issues when presenting
            enum values with <see cref="M:System.Object.ToString"/>.</remarks>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LevelAlias.Minimum">
            <summary>
            The least significant level of event.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LevelAlias.Maximum">
            <summary>
            The most significant level of event.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.LogEvent">
            <summary>
            A log event.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEvent.#ctor(System.DateTimeOffset,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,Datadog.Trace.Vendors.Serilog.Events.MessageTemplate,System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Events.LogEventProperty})">
            <summary>
            Construct a new <seealso cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEvent"/>.
            </summary>
            <param name="timestamp">The time at which the event occurred.</param>
            <param name="level">The level of the event.</param>
            <param name="exception">An exception associated with the event, or null.</param>
            <param name="messageTemplate">The message template describing the event.</param>
            <param name="properties">Properties associated with the event, including those presented in <paramref name="messageTemplate"/>.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.LogEvent.Timestamp">
            <summary>
            The time at which the event occurred.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.LogEvent.Level">
            <summary>
            The level of the event.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.LogEvent.MessageTemplate">
            <summary>
            The message template describing the event.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEvent.RenderMessage(System.IO.TextWriter,System.IFormatProvider)">
            <summary>
            Render the message template to the specified output, given the properties associated
            with the event.
            </summary>
            <param name="output">The output.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEvent.RenderMessage(System.IFormatProvider)">
            <summary>
            Render the message template given the properties associated
            with the event, and return the result.
            </summary>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.LogEvent.Properties">
            <summary>
            Properties associated with the event, including those presented in <see cref="P:Datadog.Trace.Vendors.Serilog.Events.LogEvent.MessageTemplate"/>.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.LogEvent.Exception">
            <summary>
            An exception associated with the event, or null.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEvent.AddOrUpdateProperty(Datadog.Trace.Vendors.Serilog.Events.LogEventProperty)">
            <summary>
            Add a property to the event if not already present, otherwise, update its value.
            </summary>
            <param name="property">The property to add or update.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEvent.AddPropertyIfAbsent(Datadog.Trace.Vendors.Serilog.Events.LogEventProperty)">
            <summary>
            Add a property to the event if not already present.
            </summary>
            <param name="property">The property to add.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEvent.RemovePropertyIfPresent(System.String)">
            <summary>
            Remove a property from the event, if present. Otherwise no action
            is performed.
            </summary>
            <param name="propertyName">The name of the property to remove.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel">
            <summary>
            Specifies the meaning and relative importance of a log event.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose">
            <summary>
            Anything and everything you might want to know about
            a running block of code.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug">
            <summary>
            Internal system events that aren't necessarily
            observable from the outside.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information">
            <summary>
            The lifeblood of operational intelligence - things
            happen.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning">
            <summary>
            Service is degraded or endangered.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error">
            <summary>
            Functionality is unavailable, invariants are broken
            or data is lost.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal">
            <summary>
            If you have a pager, it goes off when one of these
            occurs.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty">
            <summary>
            A property associated with a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEvent"/>.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty.#ctor(System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty"/> with the specified name and value.
            </summary>
            <param name="name">The name of the property.</param>
            <param name="value">The value of the property.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty.Name">
            <summary>
            The name of the property.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty.Value">
            <summary>
            The value of the property.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty.IsValidName(System.String)">
            <summary>
            Test <paramref name="name" /> to determine if it is a valid property name.
            </summary>
            <param name="name">The name to check.</param>
            <returns>True if the name is valid; otherwise, false.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue">
            <summary>
            The value associated with a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEventProperty"/>. Divided into scalar,
            sequence and structure values to direct serialization into various formats.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.Render(System.IO.TextWriter,System.String,System.IFormatProvider)">
            <summary>
            Render the value to the output.
            </summary>
            <param name="output">The output.</param>
            <param name="format">A format string applied to the value, or null.</param>
            <param name="formatProvider">A format provider to apply to the value, or null to use the default.</param>
            <seealso cref="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.ToString(System.String,System.IFormatProvider)"/>.
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns>
            A string that represents the current object.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.ToString(System.String,System.IFormatProvider)">
            <summary>
            Formats the value of the current instance using the specified format.
            </summary>
            <returns>
            The value of the current instance in the specified format.
            </returns>
            <param name="format">The format to use.-or- A null reference (Nothing in Visual Basic) to use
            the default format defined for the type of the <see cref="T:System.IFormattable"/> implementation. </param>
            <param name="formatProvider">The provider to use to format the value.-or- A null reference
            (Nothing in Visual Basic) to obtain the numeric format information from the current locale
            setting of the operating system. </param><filterpriority>2</filterpriority>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate">
            <summary>
            Represents a message template passed to a log method. The template
            can subsequently render the template in textual form given the list
            of properties.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.Empty">
            <summary>
            Represents the empty message template.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.#ctor(System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken})">
            <summary>
            Construct a message template using manually-defined text and property tokens.
            </summary>
            <param name="tokens">The text and property tokens defining the template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.#ctor(System.String,System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken})">
            <summary>
            Construct a message template using manually-defined text and property tokens.
            </summary>
            <param name="text">The full text of the template; used by Serilog internally to avoid unneeded
            string concatenation.</param>
            <param name="tokens">The text and property tokens defining the template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.GetElementsOfTypeToArray``1(Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken[])">
            <summary>
            Similar to <see cref="M:System.Linq.Enumerable.OfType``1(System.Collections.IEnumerable)"/>, but faster.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.Text">
            <summary>
            The raw text describing the template.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.ToString">
            <summary>
            Render the template as a string.
            </summary>
            <returns>The string representation of the template.</returns>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.Tokens">
            <summary>
            The tokens parsed from the template.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.Render(System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IFormatProvider)">
            <summary>
            Convert the message template into a textual message, given the
            properties matching the tokens in the message template.
            </summary>
            <param name="properties">Properties matching template tokens.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
            <returns>The message created from the template and properties. If the
            properties are mismatched with the template, the template will be
            returned with incomplete substitution.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.MessageTemplate.Render(System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter,System.IFormatProvider)">
            <summary>
            Convert the message template into a textual message, given the
            properties matching the tokens in the message template.
            </summary>
            <param name="properties">Properties matching template tokens.</param>
            <param name="output">The message created from the template and properties. If the
            properties are mismatched with the template, the template will be
            returned with incomplete substitution.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.ScalarValue">
            <summary>
            A property value corresponding to a simple, scalar type.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.ScalarValue.#ctor(System.Object)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.ScalarValue"/> with the specified
            value.
            </summary>
            <param name="value">The value, which may be <code>null</code>.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.ScalarValue.Value">
            <summary>
            The value, which may be <code>null</code>.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.ScalarValue.Render(System.IO.TextWriter,System.String,System.IFormatProvider)">
            <summary>
            Render the value to the output.
            </summary>
            <param name="output">The output.</param>
            <param name="format">A format string applied to the value, or null.</param>
            <param name="formatProvider">A format provider to apply to the value, or null to use the default.</param>
            <seealso cref="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.ToString(System.String,System.IFormatProvider)"/>.
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.ScalarValue.Equals(System.Object)">
            <summary>
            Determine if this instance is equal to <paramref name="obj"/>.
            </summary>
            <param name="obj">The instance to compare with.</param>
            <returns>True if the instances are equal; otherwise, false.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.ScalarValue.GetHashCode">
            <summary>
            Get a hash code representing the value.
            </summary>
            <returns>The instance's hash code.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.SequenceValue">
            <summary>
            A value represented as an ordered sequence of values.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.SequenceValue.#ctor(System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue})">
            <summary>
            Create a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.SequenceValue"/> with the provided <paramref name="elements"/>.
            </summary>
            <param name="elements">The elements of the sequence.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.SequenceValue.Elements">
            <summary>
            The elements of the sequence.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.SequenceValue.Render(System.IO.TextWriter,System.String,System.IFormatProvider)">
            <summary>
            Render the value to the output.
            </summary>
            <param name="output">The output.</param>
            <param name="format">A format string applied to the value, or null.</param>
            <param name="formatProvider">A format provider to apply to the value, or null to use the default.</param>
            <seealso cref="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.ToString(System.String,System.IFormatProvider)"/>.
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Events.StructureValue">
            <summary>
            A value represented as a collection of name-value properties.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.StructureValue.#ctor(System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Events.LogEventProperty},System.String)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.StructureValue"/> with the provided properties.
            </summary>
            <param name="typeTag">Optionally, a piece of metadata describing the "type" of the
            structure.</param>
            <param name="properties">The properties of the structure.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.StructureValue.TypeTag">
            <summary>
            A piece of metadata describing the "type" of the
            structure, or null.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Events.StructureValue.Properties">
            <summary>
            The properties of the structure.
            </summary>
            <remarks>Not presented as a dictionary because dictionary construction is
            relatively expensive; it is cheaper to build a dictionary over properties only
            when the structure is of interest.</remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Events.StructureValue.Render(System.IO.TextWriter,System.String,System.IFormatProvider)">
            <summary>
            Render the value to the output.
            </summary>
            <param name="output">The output.</param>
            <param name="format">A format string applied to the value, or null.</param>
            <param name="formatProvider">A format provider to apply to the value, or null to use the default.</param>
            <seealso cref="M:Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue.ToString(System.String,System.IFormatProvider)"/>.
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Filters.Matching">
            <summary>
            Predicates applied to log events that can be used
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Filters.Matching.FromSource``1">
            <summary>
            Matches events from the specified source type.
            </summary>
            <typeparam name="TSource">The source type.</typeparam>
            <returns>A predicate for matching events.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Filters.Matching.FromSource(System.String)">
            <summary>
            Matches events from the specified source type or namespace and
            nested types or namespaces.
            </summary>
            <param name="source">A dotted source type or namespace identifier.</param>
            <returns>A function that matches log events emitted by the source.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Filters.Matching.WithProperty(System.String)">
            <summary>
            Matches events with the specified property attached,
            regardless of its value.
            </summary>
            <param name="propertyName">The name of the property to match.</param>
            <returns>A predicate for matching events.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Filters.Matching.WithProperty(System.String,System.Object)">
            <summary>
            Matches events with the specified property value.
            </summary>
            <param name="propertyName">The name of the property to match.</param>
            <param name="scalarValue">The property value to match; must be a scalar type.
            Null is allowed.</param>
            <returns>A predicate for matching events.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Filters.Matching.WithProperty``1(System.String,System.Func{``0,System.Boolean})">
            <summary>
            Matches events with the specified property value.
            </summary>
            <param name="propertyName">The name of the property to match.</param>
            <param name="predicate">A predicate for testing </param>
            <typeparam name="TScalar">The type of scalar values to match.</typeparam>
            <returns>A predicate for matching events.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Formatting.Display.LevelOutputFormat">
            <summary>
            Implements the {Level} element.
            can now have a fixed width applied to it, as well as casing rules.
            Width is set through formats like "u3" (uppercase three chars),
            "w1" (one lowercase char), or "t4" (title case four chars).
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Formatting.Display.MessageTemplateTextFormatter">
            <summary>
            A <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter"/> that supports the Serilog
            message template format. Formatting log events for display
            has a different set of requirements and expectations from
            rendering the data within them. To meet this, the formatter
            overrides some behavior: First, strings are always output
            as literals (not quoted) unless some other format is applied
            to them. Second, tokens without matching properties are skipped
            rather than being written as raw text.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Display.MessageTemplateTextFormatter.#ctor(System.String,System.IFormatProvider)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.Display.MessageTemplateTextFormatter"/>.
            </summary>
            <param name="outputTemplate">A message template describing the
            output messages.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Display.MessageTemplateTextFormatter.Format(Datadog.Trace.Vendors.Serilog.Events.LogEvent,System.IO.TextWriter)">
            <summary>
            Format the log event into the output.
            </summary>
            <param name="logEvent">The event to format.</param>
            <param name="output">The output.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Display.Obsolete.LogEventLevelValue.Render(System.IO.TextWriter,System.String,System.IFormatProvider)">
            <summary>
            This method will apply only upper or lower case formatting, not fixed width
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties">
            <summary>
            Describes the properties available in standard message template-based
            output format strings.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.MessagePropertyName">
            <summary>
            The message rendered from the log event.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.TimestampPropertyName">
            <summary>
            The timestamp of the log event.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.LevelPropertyName">
            <summary>
            The level of the log event.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.NewLinePropertyName">
            <summary>
            A new line.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.ExceptionPropertyName">
            <summary>
            The exception associated with the log event.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.PropertiesPropertyName">
            <summary>
            The properties of the log event.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.GetOutputProperties(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Create properties from the provided log event.
            </summary>
            <param name="logEvent">The log event.</param>
            <returns>A dictionary with properties representing the log event.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Display.OutputProperties.GetOutputProperties(Datadog.Trace.Vendors.Serilog.Events.LogEvent,Datadog.Trace.Vendors.Serilog.Events.MessageTemplate)">
            <summary>
            Create properties from the provided log event.
            </summary>
            <param name="logEvent">The log event.</param>
            <param name="outputTemplate">The output template.</param>
            <returns>A dictionary with properties representing the log event.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter">
            <summary>
            Formats log events in a textual representation.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.ITextFormatter.Format(Datadog.Trace.Vendors.Serilog.Events.LogEvent,System.IO.TextWriter)">
            <summary>
            Format the log event into the output.
            </summary>
            <param name="logEvent">The event to format.</param>
            <param name="output">The output.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter">
            <summary>
            Formats log events in a simple JSON structure. Instances of this class
            are safe for concurrent access by multiple threads.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.#ctor(System.String,System.Boolean,System.IFormatProvider)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter"/>.
            </summary>
            <param name="closingDelimiter">A string that will be written after each log event is formatted.
            If null, <see cref="P:System.Environment.NewLine"/> will be used.</param>
            <param name="renderMessage">If true, the message will be rendered and written to the output as a
            property named RenderedMessage.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.#ctor(System.Boolean,System.String,System.Boolean,System.IFormatProvider)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter"/>.
            </summary>
            <param name="omitEnclosingObject">If true, the properties of the event will be written to
            the output without enclosing braces. Otherwise, if false, each event will be written as a well-formed
            JSON object.</param>
            <param name="closingDelimiter">A string that will be written after each log event is formatted.
            If null, <see cref="P:System.Environment.NewLine"/> will be used. Ignored if <paramref name="omitEnclosingObject"/>
            is true.</param>
            <param name="renderMessage">If true, the message will be rendered and written to the output as a
            property named RenderedMessage.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.Format(Datadog.Trace.Vendors.Serilog.Events.LogEvent,System.IO.TextWriter)">
            <summary>
            Format the log event into the output.
            </summary>
            <param name="logEvent">The event to format.</param>
            <param name="output">The output.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.AddLiteralWriter(System.Type,System.Action{System.Object,System.IO.TextWriter})">
            <summary>
            Adds a writer function for a given type.
            </summary>
            <param name="type">The type of values, which <paramref name="writer" /> handles.</param>
            <param name="writer">The function, which writes the values.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteRenderings(System.Linq.IGrouping{System.String,Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken}[],System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter)">
            <summary>
            Writes out individual renderings of attached properties
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteRenderingsValues(System.Linq.IGrouping{System.String,Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken}[],System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter)">
            <summary>
            Writes out the values of individual renderings of attached properties
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteProperties(System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter)">
            <summary>
            Writes out the attached properties
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WritePropertiesValues(System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter)">
            <summary>
            Writes out the attached properties values
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteException(System.Exception,System.String@,System.IO.TextWriter)">
            <summary>
            Writes out the attached exception
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteRenderedMessage(System.String,System.String@,System.IO.TextWriter)">
            <summary>
            (Optionally) writes out the rendered message
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteMessageTemplate(System.String,System.String@,System.IO.TextWriter)">
            <summary>
            Writes out the message template for the logevent.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteLevel(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String@,System.IO.TextWriter)">
            <summary>
            Writes out the log level
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteTimestamp(System.DateTimeOffset,System.String@,System.IO.TextWriter)">
            <summary>
            Writes out the log timestamp
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteStructure(System.String,System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Events.LogEventProperty},System.IO.TextWriter)">
            <summary>
            Writes out a structure property
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteSequence(System.Collections.IEnumerable,System.IO.TextWriter)">
            <summary>
            Writes out a sequence property
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteDictionary(System.Collections.Generic.IReadOnlyDictionary{Datadog.Trace.Vendors.Serilog.Events.ScalarValue,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter)">
            <summary>
            Writes out a dictionary
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteJsonProperty(System.String,System.Object,System.String@,System.IO.TextWriter)">
            <summary>
            Writes out a json property with the specified value on output writer
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.WriteLiteralValue(System.Object,System.IO.TextWriter)">
            <summary>
            Allows a subclass to write out objects that have no configured literal writer.
            </summary>
            <param name="value">The value to be written as a json construct</param>
            <param name="output">The writer to write on</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter.Escape(System.String)">
            <summary>
            Perform simple JSON string escaping on <paramref name="s"/>.
            </summary>
            <param name="s">A raw string.</param>
            <returns>A JSON-escaped version of <paramref name="s"/>.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter">
            <summary>
            Converts Serilog's structured property value format into JSON.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.#ctor(System.String)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonFormatter"/>.
            </summary>
            <param name="typeTagName">When serializing structured (object) values,
            the property name to use for the Serilog <see cref="P:Datadog.Trace.Vendors.Serilog.Events.StructureValue.TypeTag"/> field
            in the resulting JSON. If null, no type tag field will be written. The default is
            "_typeTag".</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.Format(Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue,System.IO.TextWriter)">
            <summary>
            Format <paramref name="value"/> as JSON to <paramref name="output"/>.
            </summary>
            <param name="value">The value to format</param>
            <param name="output">The output</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.VisitScalarValue(System.IO.TextWriter,Datadog.Trace.Vendors.Serilog.Events.ScalarValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.ScalarValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="scalar">The value to visit.</param>
            <returns>The result of visiting <paramref name="scalar"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.VisitSequenceValue(System.IO.TextWriter,Datadog.Trace.Vendors.Serilog.Events.SequenceValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.SequenceValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="sequence">The value to visit.</param>
            <returns>The result of visiting <paramref name="sequence"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.VisitStructureValue(System.IO.TextWriter,Datadog.Trace.Vendors.Serilog.Events.StructureValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.StructureValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="structure">The value to visit.</param>
            <returns>The result of visiting <paramref name="structure"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.VisitDictionaryValue(System.IO.TextWriter,Datadog.Trace.Vendors.Serilog.Events.DictionaryValue)">
            <summary>
            Visit a <see cref="T:Datadog.Trace.Vendors.Serilog.Events.DictionaryValue"/> value.
            </summary>
            <param name="state">Operation state.</param>
            <param name="dictionary">The value to visit.</param>
            <returns>The result of visiting <paramref name="dictionary"/>.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.FormatLiteralValue(System.Object,System.IO.TextWriter)">
            <summary>
            Write a literal as a single JSON value, e.g. as a number or string. Override to
            support more value types. Don't write arrays/structures through this method - the
            active destructuring policies have already indicated the value should be scalar at
            this point.
            </summary>
            <param name="value">The value to write.</param>
            <param name="output">The output</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Json.JsonValueFormatter.WriteQuotedJsonString(System.String,System.IO.TextWriter)">
            <summary>
            Write a valid JSON string literal, escaping as necessary.
            </summary>
            <param name="str">The string value to write.</param>
            <param name="output">The output.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Formatting.Raw.RawFormatter">
            <summary>
            Formats log events as a raw dump of the message template and properties.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Formatting.Raw.RawFormatter.Format(Datadog.Trace.Vendors.Serilog.Events.LogEvent,System.IO.TextWriter)">
            <summary>
            Format the log event into the output.
            </summary>
            <param name="logEvent">The event to format.</param>
            <param name="output">The output.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.ILogger">
             <summary>
             The core Serilog logging API, used for writing log events.
             </summary>
             <example>
             var log = new LoggerConfiguration()
                 .WriteTo.Console()
                 .CreateLogger();
            
             var thing = "World";
             log.Information("Hello, {Thing}!", thing);
             </example>
             <remarks>
             The methods on <see cref="T:Datadog.Trace.Vendors.Serilog.ILogger"/> (and its static sibling <see cref="T:Datadog.Trace.Vendors.Serilog.Log"/>) are guaranteed
             never to throw exceptions. Methods on all other types may.
             </remarks>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.ForContext(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher)">
            <summary>
            Create a logger that enriches log events via the provided enrichers.
            </summary>
            <param name="enricher">Enricher that applies in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.ForContext(System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher})">
            <summary>
            Create a logger that enriches log events via the provided enrichers.
            </summary>
            <param name="enrichers">Enrichers that apply in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.ForContext(System.String,System.Object,System.Boolean)">
            <summary>
            Create a logger that enriches log events with the specified property.
            </summary>
            <param name="propertyName">The name of the property. Must be non-empty.</param>
            <param name="value">The property value.</param>
            <param name="destructureObjects">If true, the value will be serialized as a structured
            object if possible; if false, the object will be recorded as a scalar or simple array.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.ForContext``1">
            <summary>
            Create a logger that marks log events as being from the specified
            source type.
            </summary>
            <typeparam name="TSource">Type generating log messages in the context.</typeparam>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.ForContext(System.Type)">
            <summary>
            Create a logger that marks log events as being from the specified
            source type.
            </summary>
            <param name="source">Type generating log messages in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Write an event to the log.
            </summary>
            <param name="logEvent">The event to write.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write``2(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0,``1)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write``3(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.Object[])">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate"></param>
            <param name="propertyValues"></param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write``2(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write``3(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.IsEnabled(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Determine if events at the specified level will be passed through
            to the log sinks.
            </summary>
            <param name="level">Level to check.</param>
            <returns>True if the level is enabled; otherwise, false.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Verbose(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Information(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Warning(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.Fatal(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.BindMessageTemplate(System.String,System.Object[],Datadog.Trace.Vendors.Serilog.Events.MessageTemplate@,System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Events.LogEventProperty}@)">
            <summary>
            Uses configured scalar conversion and destructuring rules to bind a set of properties to a
            message template. Returns false if the template or values are invalid (<summary>ILogger</summary>
            methods never throw exceptions).
            </summary>
            <param name="messageTemplate">Message template describing an event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <param name="parsedTemplate">The internal representation of the template, which may be used to
            render the <paramref name="boundProperties"/> as text.</param>
            <param name="boundProperties">Captured properties from the template and <paramref name="propertyValues"/>.</param>
            <example>
            MessageTemplate template;
            IEnumerable&lt;LogEventProperty&gt; properties>;
            if (Log.BindMessageTemplate("Hello, {Name}!", new[] { "World" }, out template, out properties)
            {
                var propsByName = properties.ToDictionary(p => p.Name, p => p.Value);
                Console.WriteLine(template.Render(propsByName, null));
                // -> "Hello, World!"
            }
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.ILogger.BindProperty(System.String,System.Object,System.Boolean,Datadog.Trace.Vendors.Serilog.Events.LogEventProperty@)">
            <summary>
            Uses configured scalar conversion and destructuring rules to bind a property value to its captured
            representation.
            </summary>
            <returns>True if the property could be bound, otherwise false (<summary>ILogger</summary>
            <param name="propertyName">The name of the property. Must be non-empty.</param>
            <param name="value">The property value.</param>
            <param name="destructureObjects">If true, the value will be serialized as a structured
            object if possible; if false, the object will be recorded as a scalar or simple array.</param>
            <param name="property">The resulting property.</param>
            methods never throw exceptions).</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Log">
             <summary>
             An optional static entry point for logging that can be easily referenced
             by different parts of an application. To configure the <see cref="T:Datadog.Trace.Vendors.Serilog.Log"/>
             set the Logger static property to a logger instance.
             </summary>
             <example>
             Log.Logger = new LoggerConfiguration()
                 .WithConsoleSink()
                 .CreateLogger();
            
             var thing = "World";
             Log.Logger.Information("Hello, {Thing}!", thing);
             </example>
             <remarks>
             The methods on <see cref="T:Datadog.Trace.Vendors.Serilog.Log"/> (and its dynamic sibling <see cref="T:Datadog.Trace.Vendors.Serilog.ILogger"/>) are guaranteed
             never to throw exceptions. Methods on all other types may.
             </remarks>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Log.Logger">
            <summary>
            The globally-shared logger.
            </summary>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.CloseAndFlush">
            <summary>
            Resets <see cref="P:Datadog.Trace.Vendors.Serilog.Log.Logger"/> to the default and disposes the original if possible
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.ForContext(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher)">
            <summary>
            Create a logger that enriches log events via the provided enrichers.
            </summary>
            <param name="enricher">Enricher that applies in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.ForContext(Datadog.Trace.Vendors.Serilog.Core.ILogEventEnricher[])">
            <summary>
            Create a logger that enriches log events via the provided enrichers.
            </summary>
            <param name="enrichers">Enrichers that apply in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.ForContext(System.String,System.Object,System.Boolean)">
            <summary>
            Create a logger that enriches log events with the specified property.
            </summary>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.ForContext``1">
            <summary>
            Create a logger that marks log events as being from the specified
            source type.
            </summary>
            <typeparam name="TSource">Type generating log messages in the context.</typeparam>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.ForContext(System.Type)">
            <summary>
            Create a logger that marks log events as being from the specified
            source type.
            </summary>
            <param name="source">Type generating log messages in the context.</param>
            <returns>A logger that will enrich log events as specified.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write(Datadog.Trace.Vendors.Serilog.Events.LogEvent)">
            <summary>
            Write an event to the log.
            </summary>
            <param name="logEvent">The event to write.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write``2(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0,``1)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write``3(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,System.Object[])">
            <summary>
            Write a log event with the specified level.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write``1(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write``2(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write``3(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Write(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the specified level and associated exception.
            </summary>
            <param name="level">The level of the event.</param>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.IsEnabled(Datadog.Trace.Vendors.Serilog.Events.LogEventLevel)">
            <summary>
            Determine if events at the specified level will be passed through
            to the log sinks.
            </summary>
            <param name="level">Level to check.</param>
            <returns>True if the level is enabled; otherwise, false.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Verbose("Staring into space, wondering if we're alone.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Verbose(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Verbose"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Verbose(ex, "Staring into space, wondering where this comet came from.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Debug("Starting up at {StartedAt}.", DateTime.Now);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Debug"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Debug(ex, "Swallowing a mundane exception.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Information("Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Information(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Information(ex, "Processed {RecordCount} records in {TimeMS}.", records.Length, sw.ElapsedMilliseconds);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Warning("Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Warning(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Warning"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Warning(ex, "Skipped {SkipCount} records.", skippedRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Error("Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Error"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Error(ex, "Failed {ErrorCount} records.", brokenRecords.Length);
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal(System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal``1(System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal``2(System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal``3(System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal(System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level.
            </summary>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Fatal("Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal(System.Exception,System.String)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal``1(System.Exception,System.String,``0)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal``2(System.Exception,System.String,``0,``1)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValue0">Object positionally formatted into the message template.</param>
            <param name="propertyValue1">Object positionally formatted into the message template.</param>
            <param name="propertyValue2">Object positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.Fatal(System.Exception,System.String,System.Object[])">
            <summary>
            Write a log event with the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Fatal"/> level and associated exception.
            </summary>
            <param name="exception">Exception related to the event.</param>
            <param name="messageTemplate">Message template describing the event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <example>
            Log.Fatal(ex, "Process terminating.");
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.BindMessageTemplate(System.String,System.Object[],Datadog.Trace.Vendors.Serilog.Events.MessageTemplate@,System.Collections.Generic.IEnumerable{Datadog.Trace.Vendors.Serilog.Events.LogEventProperty}@)">
            <summary>
            Uses configured scalar conversion and destructuring rules to bind a set of properties to a
            message template. Returns false if the template or values are invalid (<summary>ILogger</summary>
            methods never throw exceptions).
            </summary>
            <param name="messageTemplate">Message template describing an event.</param>
            <param name="propertyValues">Objects positionally formatted into the message template.</param>
            <param name="parsedTemplate">The internal representation of the template, which may be used to
            render the <paramref name="boundProperties"/> as text.</param>
            <param name="boundProperties">Captured properties from the template and <paramref name="propertyValues"/>.</param>
            <example>
            MessageTemplate template;
            IEnumerable&lt;LogEventProperty&gt; properties>;
            if (Log.BindMessageTemplate("Hello, {Name}!", new[] { "World" }, out template, out properties)
            {
                var propsByName = properties.ToDictionary(p => p.Name, p => p.Value);
                Console.WriteLine(template.Render(propsByName, null));
                // -> "Hello, World!"
            }
            </example>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Log.BindProperty(System.String,System.Object,System.Boolean,Datadog.Trace.Vendors.Serilog.Events.LogEventProperty@)">
            <summary>
            Uses configured scalar conversion and destructuring rules to bind a property value to its captured
            representation.
            </summary>
            <returns>True if the property could be bound, otherwise false (<summary>ILogger</summary>
            <param name="propertyName">The name of the property. Must be non-empty.</param>
            <param name="value">The property value.</param>
            <param name="destructureObjects">If true, the value will be serialized as a structured
            object if possible; if false, the object will be recorded as a scalar or simple array.</param>
            <param name="property">The resulting property.</param>
            methods never throw exceptions).</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.LoggerConfiguration">
            <summary>
            Configuration object for creating <see cref="T:Datadog.Trace.Vendors.Serilog.ILogger"/> instances.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.#ctor">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.LoggerConfiguration"/>.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.WriteTo">
            <summary>
            Configures the sinks that log events will be emitted to.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.AuditTo">
            <summary>
            Configures sinks for auditing, instead of regular (safe) logging. When auditing is used,
            exceptions from sinks and any intermediate filters propagate back to the caller. Most callers
            should use <see cref="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.WriteTo"/> instead.
            </summary>
            <remarks>
            Not all sinks are compatible with transactional auditing requirements (many will use asynchronous
            batching to improve write throughput and latency). Sinks need to opt-in to auditing support by
            extending <see cref="T:Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration"/>, though the generic <see cref="M:Datadog.Trace.Vendors.Serilog.Configuration.LoggerAuditSinkConfiguration.Sink(Datadog.Trace.Vendors.Serilog.Core.ILogEventSink,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,Datadog.Trace.Vendors.Serilog.Core.LoggingLevelSwitch)"/>
            method allows any sink class to be adapted for auditing. 
            </remarks>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.MinimumLevel">
            <summary>
            Configures the minimum level at which events will be passed to sinks. If
            not specified, only events at the <see cref="F:Datadog.Trace.Vendors.Serilog.Events.LogEventLevel.Information"/>
            level and above will be passed through.
            </summary>
            <returns>Configuration object allowing method chaining.</returns>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.Enrich">
            <summary>
            Configures enrichment of <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEvent"/>s. Enrichers can add, remove and
            modify the properties associated with events.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.Filter">
            <summary>
            Configures global filtering of <see cref="T:Datadog.Trace.Vendors.Serilog.Events.LogEvent"/>s.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.Destructure">
            <summary>
            Configures destructuring of message template parameters.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.ReadFrom">
            <summary>
            Apply external settings to the logger configuration.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.LoggerConfiguration.CreateLogger">
            <summary>
            Create a logger using the configured sinks, enrichers and minimum level.
            </summary>
            <returns>The logger.</returns>
            <remarks>To free resources held by sinks ahead of program shutdown,
            the returned logger may be cast to <see cref="T:System.IDisposable"/> and
            disposed.</remarks>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.LoggerExtensions">
            <summary>
            Extends <see cref="T:Datadog.Trace.Vendors.Serilog.ILogger" /> with additional methods.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.LoggerExtensions.ForContext``1(Datadog.Trace.Vendors.Serilog.ILogger,Datadog.Trace.Vendors.Serilog.Events.LogEventLevel,System.String,``0,System.Boolean)">
            <summary>
            Create a logger that enriches log events when the specified level is enabled.
            </summary>
            <typeparam name="TValue"> The type of the property value. </typeparam>
            <param name="logger">The logger.</param>
            <param name="level">The log event level used to determine if log is enriched with property.</param>
            <param name="propertyName">The name of the property. Must be non-empty.</param>
            <param name="value">The property value.</param>
            <param name="destructureObjects">If true, the value will be serialized as a structured
            object if possible; if false, the object will be recorded as a scalar or simple array.</param>
            <returns>A logger that will enrich log events as specified.</returns>
            <returns></returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Parsing.Alignment">
            <summary>
            A structure representing the alignment settings to apply when rendering a property.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.Alignment.#ctor(Datadog.Trace.Vendors.Serilog.Parsing.AlignmentDirection,System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Datadog.Trace.Vendors.Serilog.Parsing.Alignment"/>.
            </summary>
            <param name="direction">The text alignment direction.</param>
            <param name="width">The width of the text, in characters.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.Alignment.Direction">
            <summary>
            The text alignment direction.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.Alignment.Width">
            <summary>
            The width of the text.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Parsing.AlignmentDirection">
            <summary>
            Defines the direction of the alignment.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Parsing.AlignmentDirection.Left">
            <summary>
            Text will be left-aligned.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Parsing.AlignmentDirection.Right">
            <summary>
            Text will be right-aligned.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Parsing.Destructuring">
            <summary>
            Instructs the logger on how to store information about provided
            parameters.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Parsing.Destructuring.Default">
            <summary>
            Convert known types and objects to scalars, arrays to sequences.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Parsing.Destructuring.Stringify">
            <summary>
            Convert all types to scalar strings. Prefix name with '$'.
            </summary>
        </member>
        <member name="F:Datadog.Trace.Vendors.Serilog.Parsing.Destructuring.Destructure">
            <summary>
            Convert known types to scalars, destructure objects and collections
            into sequences and structures. Prefix name with '@'.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateParser">
            <summary>
            Parses message template strings into sequences of text or property
            tokens.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateParser.Parse(System.String)">
            <summary>
            Parse the supplied message template.
            </summary>
            <param name="messageTemplate">The message template to parse.</param>
            <returns>A sequence of text or property tokens. Where the template
            is not syntactically valid, text tokens will be returned. The parser
            will make a best effort to extract valid property tokens even in the
            presence of parsing issues.</returns>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken">
            <summary>
            An element parsed from a message template string.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken.#ctor(System.Int32)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken"/>.
            </summary>
            <param name="startIndex">The token's start index in the template.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken.StartIndex">
            <summary>
            The token's start index in the template.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken.Length">
            <summary>
            The token's length.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.MessageTemplateToken.Render(System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter,System.IFormatProvider)">
            <summary>
            Render the token to the output.
            </summary>
            <param name="properties">Properties that may be represented by the token.</param>
            <param name="output">Output for the rendered string.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken">
            <summary>
            A message template token representing a log event property.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.#ctor(System.String,System.String,System.String,Datadog.Trace.Vendors.Serilog.Parsing.Destructuring)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken"/>.
            </summary>
            <param name="propertyName">The name of the property.</param>
            <param name="rawText">The token as it appears in the message template.</param>
            <param name="formatObsolete">The format applied to the property, if any.</param>
            <param name="destructuringObsolete">The destructuring strategy applied to the property, if any.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.#ctor(System.String,System.String,System.String,System.Nullable{Datadog.Trace.Vendors.Serilog.Parsing.Alignment},Datadog.Trace.Vendors.Serilog.Parsing.Destructuring,System.Int32)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken"/>.
            </summary>
            <param name="propertyName">The name of the property.</param>
            <param name="rawText">The token as it appears in the message template.</param>
            <param name="format">The format applied to the property, if any.</param>
            <param name="alignment">The alignment applied to the property, if any.</param>
            <param name="destructuring">The destructuring strategy applied to the property, if any.</param>
            <param name="startIndex">The token's start index in the template.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.Length">
            <summary>
            The token's length.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.Render(System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter,System.IFormatProvider)">
            <summary>
            Render the token to the output.
            </summary>
            <param name="properties">Properties that may be represented by the token.</param>
            <param name="output">Output for the rendered string.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.PropertyName">
            <summary>
            The property name.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.Destructuring">
            <summary>
            Destructuring strategy applied to the property.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.Format">
            <summary>
            Format applied to the property.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.Alignment">
            <summary>
            Alignment applied to the property.
            </summary>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.IsPositional">
            <summary>
            True if the property name is a positional index; otherwise, false.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.TryGetPositionalValue(System.Int32@)">
            <summary>
            Try to get the integer value represented by the property name.
            </summary>
            <param name="position">The integer value, if present.</param>
            <returns>True if the property is positional, otherwise false.</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            true if the specified object  is equal to the current object; otherwise, false.
            </returns>
            <param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.PropertyToken.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns>
            A string that represents the current object.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Parsing.TextToken">
            <summary>
            A message template token representing literal text.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.TextToken.#ctor(System.String,System.Int32)">
            <summary>
            Construct a <see cref="T:Datadog.Trace.Vendors.Serilog.Parsing.TextToken"/>.
            </summary>
            <param name="text">The text of the token.</param>
            <param name="startIndex">The token's start index in the template.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.TextToken.Length">
            <summary>
            The token's length.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.TextToken.Render(System.Collections.Generic.IReadOnlyDictionary{System.String,Datadog.Trace.Vendors.Serilog.Events.LogEventPropertyValue},System.IO.TextWriter,System.IFormatProvider)">
            <summary>
            Render the token to the output.
            </summary>
            <param name="properties">Properties that may be represented by the token.</param>
            <param name="output">Output for the rendered string.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.TextToken.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            true if the specified object  is equal to the current object; otherwise, false.
            </returns>
            <param name="obj">The object to compare with the current object. </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.TextToken.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Parsing.TextToken.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns>
            A string that represents the current object.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Datadog.Trace.Vendors.Serilog.Parsing.TextToken.Text">
            <summary>
            The text of the token.
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Rendering.Casing.Format(System.String,System.String)">
            <summary>
            Apply upper or lower casing to <paramref name="value"/> when <paramref name="format"/> is provided.
            Returns <paramref name="value"/> when no or invalid format provided
            </summary>
            <returns>The provided <paramref name="value"/> with formatting applied</returns>
        </member>
        <member name="M:Datadog.Trace.Vendors.Serilog.Rendering.Padding.Apply(System.IO.TextWriter,System.String,System.Nullable{Datadog.Trace.Vendors.Serilog.Parsing.Alignment})">
            <summary>
            Writes the provided value to the output, applying direction-based padding when <paramref name="alignment"/> is provided.
            </summary>
        </member>
        <member name="T:Datadog.Trace.Vendors.Serilog.Settings.KeyValuePairs.SurrogateConfigurationMethods">
             <summary>
             Contains "fake extension" methods for the Serilog configuration API.
             By default the settings knows how to find extension methods, but some configuration
             are actually "regular" method calls and would not be found otherwise.
            
             This static class contains internal methods that can be used instead.
            
             See also <seealso cref="T:Datadog.Trace.Vendors.Serilog.Settings.KeyValuePairs.CallableConfigurationMethodFinder"/>
             </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.StatsdClient.IStatsd.Add(System.String,System.Int32,System.Nullable{System.Int32},System.String,System.String[],System.String,System.Boolean)">
            <summary>
            Add service check
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.StatsdClient.IStatsd.Send(System.String,System.Int32,System.Nullable{System.Int32},System.String,System.String[],System.String,System.Boolean)">
            <summary>
            Send service check
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.StatsdClient.Statsd.Add(System.String,System.Int32,System.Nullable{System.Int32},System.String,System.String[],System.String,System.Boolean)">
            <summary>
            Add a Service check
            </summary>
        </member>
        <member name="M:Datadog.Trace.Vendors.StatsdClient.Statsd.Send(System.String,System.Int32,System.Nullable{System.Int32},System.String,System.String[],System.String,System.Boolean)">
            <summary>
            Send a service check
            </summary>
        </member>
    </members>
</doc>
