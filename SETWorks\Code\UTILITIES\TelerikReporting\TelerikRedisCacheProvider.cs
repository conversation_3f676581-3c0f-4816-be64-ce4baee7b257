using System;
using System.Collections;
using Telerik.Reporting.Cache.Interfaces;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Custom Redis cache provider for Telerik Reporting following official documentation
    /// https://docs.telerik.com/reporting/embedding-reports/cache-management/configuring-custom-cache-provider
    /// </summary>
    [Serializable]
    public class TelerikRedisCacheProvider : ICacheProvider
    {
        /// <summary>
        /// Parameterless constructor required by Telerik Reporting engine
        /// </summary>
        public TelerikRedisCacheProvider()
        {
        }

        /// <summary>
        /// Creates a new cache instance with the specified parameters
        /// </summary>
        /// <param name="parameters">Parameters from the Telerik.Reporting/Cache configuration section</param>
        /// <returns>A new ICache instance</returns>
        public ICache CreateCache(IDictionary parameters)
        {
            return new TelerikRedisCache(parameters);
        }
    }
}
