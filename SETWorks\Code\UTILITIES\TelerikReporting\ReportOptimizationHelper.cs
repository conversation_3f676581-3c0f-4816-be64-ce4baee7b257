using System;
using System.Configuration;
using System.Data;
using Telerik.Reporting;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Helper class for optimizing existing reports to use pagination and reduce session storage
    /// </summary>
    public static class ReportOptimizationHelper
    {
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(ReportOptimizationHelper));

        /// <summary>
        /// Optimize a report by implementing pagination for large datasets
        /// </summary>
        public static void OptimizeReportForLargeData(Report report, string dataSourceName, 
            Func<int, int, DataTable> dataProvider, int totalRecords, int pageSize = 1000)
        {
            try
            {
                // Create paginated data source
                var paginatedSource = new PaginatedReportDataSource(dataProvider, totalRecords, pageSize);
                
                // Replace the original data source with paginated version
                var dataSource = report.DataSource as ObjectDataSource;
                if (dataSource != null)
                {
                    // Store original data source info for reference
                    report.ReportParameters.Add(new ReportParameter
                    {
                        Name = "OriginalDataSourceType",
                        Value = dataSource.GetType().Name,
                        Visible = false
                    });

                    // Create new paginated object data source
                    var paginatedDataSource = new ObjectDataSource
                    {
                        DataSource = paginatedSource,
                        DataMember = "GetAllData" // This will still get all data but in a memory-efficient way
                    };

                    report.DataSource = paginatedDataSource;
                    
                    Log.Info($"Optimized report '{report.Name}' with pagination. Total records: {totalRecords}, Page size: {pageSize}");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Failed to optimize report '{report.Name}' for large data", ex);
                throw;
            }
        }

        /// <summary>
        /// Create an optimized version of a report that uses SQL-based pagination
        /// </summary>
        public static Report CreateOptimizedSqlReport(string reportName, string connectionString, 
            string baseQuery, string countQuery = null, int pageSize = 1000)
        {
            try
            {
                var report = new Report
                {
                    Name = reportName + "_Optimized"
                };

                // Create paginated data source
                var paginatedSource = PaginatedReportDataSourceFactory.CreateFromSql(
                    connectionString, baseQuery, countQuery, pageSize);

                // Set up the report data source
                var dataSource = new ObjectDataSource
                {
                    DataSource = paginatedSource,
                    DataMember = "GetAllData"
                };

                report.DataSource = dataSource;

                // Add metadata parameters
                report.ReportParameters.Add(new ReportParameter
                {
                    Name = "TotalRecords",
                    Value = paginatedSource.TotalRecords.ToString(),
                    Visible = false
                });

                report.ReportParameters.Add(new ReportParameter
                {
                    Name = "PageSize",
                    Value = paginatedSource.PageSize.ToString(),
                    Visible = false
                });

                report.ReportParameters.Add(new ReportParameter
                {
                    Name = "TotalPages",
                    Value = paginatedSource.TotalPages.ToString(),
                    Visible = false
                });

                Log.Info($"Created optimized SQL report '{reportName}' with {paginatedSource.TotalRecords} records");
                
                return report;
            }
            catch (Exception ex)
            {
                Log.Error($"Failed to create optimized SQL report '{reportName}'", ex);
                throw;
            }
        }

        /// <summary>
        /// Check if a report should be optimized based on estimated data size
        /// </summary>
        public static bool ShouldOptimizeReport(int estimatedRecordCount, int averageRecordSizeBytes = 1024)
        {
            var estimatedSizeBytes = estimatedRecordCount * averageRecordSizeBytes;
            var thresholdBytes = GetOptimizationThreshold();
            
            return estimatedSizeBytes > thresholdBytes;
        }

        /// <summary>
        /// Get the threshold for when reports should be optimized (from config or default)
        /// </summary>
        public static long GetOptimizationThreshold()
        {
            var configValue = ConfigurationManager.AppSettings["TelerikReportOptimizationThresholdMB"];
            if (!string.IsNullOrEmpty(configValue) && int.TryParse(configValue, out var thresholdMB))
            {
                return thresholdMB * 1024 * 1024; // Convert MB to bytes
            }
            
            return 50 * 1024 * 1024; // Default 50MB threshold
        }

        /// <summary>
        /// Analyze a DataTable to estimate if it should use pagination
        /// </summary>
        public static ReportSizeAnalysis AnalyzeDataTableSize(DataTable dataTable)
        {
            if (dataTable == null)
            {
                return new ReportSizeAnalysis { ShouldOptimize = false, Reason = "DataTable is null" };
            }

            var recordCount = dataTable.Rows.Count;
            var columnCount = dataTable.Columns.Count;
            
            // Estimate average row size
            var estimatedRowSize = EstimateRowSize(dataTable);
            var estimatedTotalSize = recordCount * estimatedRowSize;
            
            var threshold = GetOptimizationThreshold();
            var shouldOptimize = estimatedTotalSize > threshold;
            
            return new ReportSizeAnalysis
            {
                RecordCount = recordCount,
                ColumnCount = columnCount,
                EstimatedRowSizeBytes = estimatedRowSize,
                EstimatedTotalSizeBytes = estimatedTotalSize,
                ShouldOptimize = shouldOptimize,
                Reason = shouldOptimize 
                    ? $"Estimated size ({estimatedTotalSize / 1024 / 1024}MB) exceeds threshold ({threshold / 1024 / 1024}MB)"
                    : "Size is within acceptable limits"
            };
        }

        private static int EstimateRowSize(DataTable dataTable)
        {
            if (dataTable.Rows.Count == 0)
                return 0;

            var sampleSize = Math.Min(100, dataTable.Rows.Count);
            long totalSize = 0;

            for (int i = 0; i < sampleSize; i++)
            {
                var row = dataTable.Rows[i];
                foreach (var item in row.ItemArray)
                {
                    if (item != null && item != DBNull.Value)
                    {
                        totalSize += EstimateObjectSize(item);
                    }
                }
            }

            return (int)(totalSize / sampleSize);
        }

        private static int EstimateObjectSize(object obj)
        {
            switch (obj)
            {
                case string str:
                    return str.Length * 2; // Unicode characters
                case int _:
                    return 4;
                case long _:
                    return 8;
                case double _:
                    return 8;
                case decimal _:
                    return 16;
                case DateTime _:
                    return 8;
                case bool _:
                    return 1;
                case byte[] bytes:
                    return bytes.Length;
                default:
                    return 50; // Default estimate for unknown types
            }
        }
    }

    /// <summary>
    /// Analysis result for report data size
    /// </summary>
    public class ReportSizeAnalysis
    {
        public int RecordCount { get; set; }
        public int ColumnCount { get; set; }
        public int EstimatedRowSizeBytes { get; set; }
        public long EstimatedTotalSizeBytes { get; set; }
        public bool ShouldOptimize { get; set; }
        public string Reason { get; set; }
        
        public string EstimatedTotalSizeMB => $"{EstimatedTotalSizeBytes / 1024.0 / 1024.0:F2} MB";
    }
}
