﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3C76702A-D1E2-4B11-A888-9ED75E000138}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SetWorks.Services</RootNamespace>
    <AssemblyName>SetWorks.Services</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=1.1.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.1.1.2\lib\net451\Microsoft.IdentityModel.Logging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=5.1.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.5.1.2\lib\net451\Microsoft.IdentityModel.Tokens.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Ninject, Version=3.2.0.0, Culture=neutral, PublicKeyToken=c7192dc5380945e7">
      <HintPath>..\packages\Ninject.3.2.0.0\lib\net45-full\Ninject.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Renci.SshNet, Version=2020.0.1.0, Culture=neutral, PublicKeyToken=1cee9f8bde3db106">
      <HintPath>..\packages\SSH.NET.2020.0.1\lib\net40\Renci.SshNet.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RestSharp, Version=106.6.10.0, Culture=neutral, PublicKeyToken=598062e77f915f75">
      <HintPath>..\packages\RestSharp.106.6.10\lib\net452\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Spire.Barcode, Version=4.11.0.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Barcode.dll</HintPath>
    </Reference>
    <Reference Include="Spire.DataExport, Version=4.1.9.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.DataExport.dll</HintPath>
    </Reference>
    <Reference Include="Spire.DataExport.ResourceMgr, Version=2.1.0.0, Culture=neutral, PublicKeyToken=4bc1500157862925, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.DataExport.ResourceMgr.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Doc, Version=8.11.16.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Doc.dll</HintPath>
    </Reference>
    <Reference Include="Spire.DocViewer.Forms, Version=5.1.1.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.DocViewer.Forms.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Email, Version=3.9.1.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Email.dll</HintPath>
    </Reference>
    <Reference Include="Spire.License, Version=1.3.8.40, Culture=neutral, PublicKeyToken=b1144360237c8b3f, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.License.dll</HintPath>
    </Reference>
    <Reference Include="Spire.OfficeViewer.Forms, Version=5.12.0.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.OfficeViewer.Forms.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Pdf, Version=6.11.12.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="Spire.PdfViewer.Asp, Version=5.11.2.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.PdfViewer.Asp.dll</HintPath>
    </Reference>
    <Reference Include="Spire.PdfViewer.Forms, Version=5.11.2.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.PdfViewer.Forms.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Presentation, Version=5.11.4.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Presentation.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Spreadsheet, Version=4.10.1.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.Spreadsheet.dll</HintPath>
    </Reference>
    <Reference Include="Spire.XLS, Version=10.11.7.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\Spire.Office.5.12.0\lib\net40\Spire.XLS.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=5.1.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.5.1.2\lib\net451\System.IdentityModel.Tokens.Jwt.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="Telerik.Web.UI, Version=2016.2.607.45, Culture=neutral, PublicKeyToken=121fae78165ba3d4" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AbilityOneService.cs" />
    <Compile Include="ActivityRecordConflictDetectionService.cs" />
    <Compile Include="ActivityRecordImportService.cs" />
    <Compile Include="AppointmentService.cs" />
    <Compile Include="AssignedStaffTypeService.cs" />
    <Compile Include="AssignedStaffTypeServiceFactory.cs" />
    <Compile Include="Authorizations\FrequencyAndExceptions.cs" />
    <Compile Include="Authorizations\FrequencyLimitResult.cs" />
    <Compile Include="Authorizations\FrequencyValidationResult.cs" />
    <Compile Include="Authorizations\IFrequencyAndExceptions.cs" />
    <Compile Include="Authorizations\IFrequencyLimitResult.cs" />
    <Compile Include="Authorizations\IValidationResult.cs" />
    <Compile Include="CodeSetServiceFactory.cs" />
    <Compile Include="ConsumerAccessService.cs" />
    <Compile Include="ConsumerAccessServiceFactory.cs" />
    <Compile Include="ConsumerAssignedStaffService.cs" />
    <Compile Include="ConsumerAssignedStaffServiceFactory.cs" />
    <Compile Include="ConsumerBenefitInformationService.cs" />
    <Compile Include="ConsumerBenefitInformationServiceFactory.cs" />
    <Compile Include="ConsumerDepartmentsService.cs" />
    <Compile Include="ConsumerDepartmentsServiceFactory.cs" />
    <Compile Include="ConsumerDetailsService.cs" />
    <Compile Include="ConsumerDetailsServiceFactory.cs" />
    <Compile Include="ConsumerDisabilitiesService.cs" />
    <Compile Include="ConsumerDisabilitiesServiceFactory.cs" />
    <Compile Include="ConsumerFinanceService.cs" />
    <Compile Include="ConsumerFinanceServiceFactory.cs" />
    <Compile Include="ConsumerEventsService.cs" />
    <Compile Include="ConsumerEventsServiceFactory.cs" />
    <Compile Include="ConsumerJobPlacementsService.cs" />
    <Compile Include="ConsumerJobPlacementsServiceFactory.cs" />
    <Compile Include="ConsumerNetworkContactsService.cs" />
    <Compile Include="ConsumerNetworkContactsServiceFactory.cs" />
    <Compile Include="ConsumerFormsServiceFactory.cs" />
    <Compile Include="ConsumerMedicationsService.cs" />
    <Compile Include="ConsumerMedicationsServiceFactory.cs" />
    <Compile Include="CustomTextFieldsService.cs" />
    <Compile Include="CustomTextFieldsServiceFactory.cs" />
    <Compile Include="DepartmentDetailsService.cs" />
    <Compile Include="DepartmentsServiceFactory.cs" />
    <Compile Include="DisabilitiesService.cs" />
    <Compile Include="DisabilitiesServiceFactory.cs" />
    <Compile Include="DoctorsService.cs" />
    <Compile Include="DoctorsServiceFactory.cs" />
    <Compile Include="DODDRateImportService.cs" />
    <Compile Include="DODDRateReaderService.cs" />
    <Compile Include="Extractor\BillingService.cs" />
    <Compile Include="Extractor\ClientMergeReportService.cs" />
    <Compile Include="Extractor\DataExtractor3ModelRelationValidationService.cs" />
    <Compile Include="Extractor\DataExtractor3ModelRelationValidatorService.cs" />
    <Compile Include="Extractor\DataExtractorModelRelationService.cs" />
    <Compile Include="Extractor\DataExtractorRunner.cs" />
    <Compile Include="Extractor\InvoiceBillingService.cs" />
    <Compile Include="Extractor\WeightedAverageRateService.cs" />
    <Compile Include="FileRequestService.cs" />
    <Compile Include="FilesService.cs" />
    <Compile Include="FilesServiceFactory.cs" />
    <Compile Include="Forms\ConsumerFormsService.cs" />
    <Compile Include="GroupServiceLocationActivity.cs" />
    <Compile Include="HCSISAuthImportService.cs" />
    <Compile Include="HealthBenefitsService.cs" />
    <Compile Include="HealthBenefitsServiceFactory.cs" />
    <Compile Include="IdentifiersService.cs" />
    <Compile Include="IdentifiersServiceFactory.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AddressInfoUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AdhocClientVitalInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AdhocClientVitals.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AdhocVital.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AdhocVitalErrors.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AdhocVitalResponse.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AllergyInfoRequest.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ClientInfoUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ClientPhotoRequest.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ClientRequestUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\DiagnosisUpdateRequest.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\LocationInfoAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\LocationUpdateRequest.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\PersonInfoUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\RoleResponse.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ShortRole.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ShouldSerializeContractResolver.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\SyncResponse.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\UserInfoUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\UserRequestUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\UserSignature.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ValidationResponse.cs" />
    <Compile Include="Integrations\Zoho\Handler\ZohoDeskApiService.cs" />
    <Compile Include="Interfaces\IGroupServiceLocationActivityService.cs" />
    <Compile Include="Interfaces\ISecureLinkService.cs" />
    <Compile Include="InvoicePaymentsService.cs" />
    <Compile Include="IPaymentImportService.cs" />
    <Compile Include="MergeReportService.cs" />
    <Compile Include="Models\V2\Files\ConsumerFinanceTransactionFileDTO.cs" />
    <Compile Include="Models\V2\Files\ConsumerFinanceTransactionFileDTOConverter.cs" />
    <Compile Include="Models\V2\Finances\ConsumerFinanceAccountDTO.cs" />
    <Compile Include="Models\V2\Finances\ConsumerFinanceTransactionDTO.cs" />
    <Compile Include="Models\V2\Finances\DepartmentFinanceAccountDTO.cs" />
    <Compile Include="Models\V2\Finances\DepartmentFinanceTransactionDTO.cs" />
    <Compile Include="Models\V2\Finances\FinanceAccountDTO.cs" />
    <Compile Include="Models\V2\Finances\FinanceAccountInstitutionDTO.cs" />
    <Compile Include="Models\V2\Finances\FinanceAccountTypeDTO.cs" />
    <Compile Include="Models\V2\Finances\FinanceTransactionCategoryDTO.cs" />
    <Compile Include="Models\V2\Finances\FinanceTransactionDTO.cs" />
    <Compile Include="Models\V2\Finances\FinanceTransactionTypeDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerEventDTO.cs" />
    <Compile Include="Models\V2\Users\StaffGroupCoverageDTO.cs" />
    <Compile Include="PaymentImportService.cs" />
    <Compile Include="SWAdminService.cs" />
    <Compile Include="MedicationsService.cs" />
    <Compile Include="MedicationsServiceFactory.cs" />
    <Compile Include="Models\ConsumerFormResultDTO.cs" />
    <Compile Include="Models\FormAnswerQuestionDisplayDTO.cs" />
    <Compile Include="Models\FormResultContentsDTO.cs" />
    <Compile Include="Models\UserResultDTO.cs" />
    <Compile Include="Models\V2\AssignedStaffTypeDTO.cs" />
    <Compile Include="Models\V2\CodeValueDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerInactiveReasonDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerTextFieldDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerDisabilitiesDTO.cs" />
    <Compile Include="Models\V2\Consumers\JobPlacementBenefitsDTO.cs" />
    <Compile Include="Models\V2\Consumers\JobPlacementEarningsDTO.cs" />
    <Compile Include="Models\V2\Consumers\JobPlacementsDTO.cs" />
    <Compile Include="Models\V2\Consumers\SharedGuardianDTO.cs" />
    <Compile Include="Models\V2\CustomTextFieldDTO.cs" />
    <Compile Include="Models\V2\DisabilityDTO.cs" />
    <Compile Include="Models\V2\DoctorTypeDTO.cs" />
    <Compile Include="Models\V2\Files\ConsumerFileDTO.cs" />
    <Compile Include="Models\V2\Files\FileAcknowledgedDTO.cs" />
    <Compile Include="Models\V2\Files\FileDTO.cs" />
    <Compile Include="Models\V2\Files\ConsumerFilesPerConsumerDTO.cs" />
    <Compile Include="Models\V2\HealthBenefitsDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerAssignedStaffDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerBenefitInformationDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerBlindWorkExpenseDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerContributionExpenseDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerDetailsDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerDoctorsDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerFoodAndHousingDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerGuardianDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerHealthBenefitsDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerIdentifiersDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerImpairmentRelatedWorkExpenseDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerMedicaidEligibilityDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerMedicaidServiceDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerMedicareDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerMedicationsAdministeredDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerMedicationsDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerOtherIncomeDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerOverpaymentDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerPASSDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerPhoneDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerPhotoDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerPrivateHealthDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerResourceDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumersPerDepartmentsDTO.cs" />
    <Compile Include="Models\V2\Consumers\ConsumerStudentEarnedIncomeExclusionDTO.cs" />
    <Compile Include="Models\V2\Consumers\DepartmentsPerConsumersDTO.cs" />
    <Compile Include="Models\V2\Consumers\GuardianPhoneDTO.cs" />
    <Compile Include="Models\V2\Consumers\GuardianRelationshipDTO.cs" />
    <Compile Include="Models\V2\Consumers\GuardianTypeDTO.cs" />
    <Compile Include="Models\V2\CountyDTO.cs" />
    <Compile Include="Models\V2\DepartmentDetailsDTO.cs" />
    <Compile Include="Models\V2\DoctorsDTO.cs" />
    <Compile Include="Models\V2\Forms\ConsumerFormResultWithSignaturesDTO.cs" />
    <Compile Include="Models\V2\Forms\FormSignatureDTO.cs" />
    <Compile Include="Models\V2\IdentifierTypesDTO.cs" />
    <Compile Include="Models\V2\LanguageDTO.cs" />
    <Compile Include="Models\V2\MedicationPurposeOptionsDTO.cs" />
    <Compile Include="Models\V2\MedicationsDTO.cs" />
    <Compile Include="Models\V2\MedicationSideEffectOptionsDTO.cs" />
    <Compile Include="Models\V2\PhoneTypeDTO.cs" />
    <Compile Include="Models\V2\PositionDTO.cs" />
    <Compile Include="Models\V2\RoleDTO.cs" />
    <Compile Include="Models\V2\StateDTO.cs" />
    <Compile Include="Models\V2\Users\UserDefaultDepartmentDTO.cs" />
    <Compile Include="Models\V2\Users\UserDepartmentsDTO.cs" />
    <Compile Include="Models\V2\Users\UserPositionsDTO.cs" />
    <Compile Include="Models\V2\Users\UserProfileDTO.cs" />
    <Compile Include="Models\V2\Users\UserProfileMembershipDTO.cs" />
    <Compile Include="Models\V2\Users\UserRolesDTO.cs" />
    <Compile Include="Models\V2\Users\UserTypesDTO.cs" />
    <Compile Include="Models\V2\Wise\WiseConsumerDetailsDTO.cs" />
    <Compile Include="Models\V2\Wise\WiseConsumerDetailsDTOConverter.cs" />
    <Compile Include="NCAuthImportService.cs" />
    <Compile Include="NCAuthValidationService.cs" />
    <Compile Include="OAuthClientService.cs" />
    <Compile Include="DODDAuthImportService.cs" />
    <Compile Include="DODDAuthValidationService.cs" />
    <Compile Include="OODPaymentImportService.cs" />
    <Compile Include="PhotoService.cs" />
    <Compile Include="PhotoServiceFactory.cs" />
    <Compile Include="PositionsService.cs" />
    <Compile Include="PositionsServiceFactory.cs" />
    <Compile Include="RateImportServiceFactory.cs" />
    <Compile Include="RolesService.cs" />
    <Compile Include="RolesServiceFactory.cs" />
    <Compile Include="SecureLinkService.cs" />
    <Compile Include="Authorizations\AuthorizationException.cs" />
    <Compile Include="Authorizations\AuthorizationsService.cs" />
    <Compile Include="Authorizations\AuthorizationValidationResult.cs" />
    <Compile Include="Authorizations\AvailableAuthorizationsDTO.cs" />
    <Compile Include="Authorizations\FrequencyType.cs" />
    <Compile Include="Authorizations\IAuthorizationsService.cs" />
    <Compile Include="Forms\QuestionCodeService.cs" />
    <Compile Include="FTPUploadService.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AddressInfoAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AllergyInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AuthenticationRequest.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\AuthenticationResponse.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\CalendarInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ClientInfoAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ClientRequestAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ClientResponse.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\DiagnosisInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\LocationRequestAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\LocationInfoUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\LocationRequestUpdate.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\LocationResponse.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\MedicationInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\PersonInfoAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\PhotoInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\PRNInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ReturnMessage.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ScheduleInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ScheduleInfoAntibiotic.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ScheduleInfoDaily.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ScheduleInfoMonthly.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ScheduleInfoPeriodic.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ScheduleInfoWeekly.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ScheduleJtubeInterval.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ShortLocation.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\ShortLocationPlus.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\TaskInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\TimeSpan.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\UserInfoAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\UserRequestAdd.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\UserResponse.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\VitalInfo.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportDataSchemas\VitalValue.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportSyncService.cs" />
    <Compile Include="Integrations\CaraSolva\MedSupportSsoService.cs" />
    <Compile Include="Interfaces\IMedSupportSyncService.cs" />
    <Compile Include="ServicesService.cs" />
    <Compile Include="ClientService.cs" />
    <Compile Include="ActivityRecordsGroupLocationService.cs" />
    <Compile Include="ContiguousActivityRecordsService.cs" />
    <Compile Include="ContiguousAppointmentService.cs" />
    <Compile Include="DefaultDatePickerService.cs" />
    <Compile Include="Extractor\MergeReportDataFactoryService.cs" />
    <Compile Include="Extractor\DataExtractorFormService.cs" />
    <Compile Include="Extractor\DataExtractorService.cs" />
    <Compile Include="ForeignSystemConfigurationService.cs" />
    <Compile Include="ForeignSystemService.cs" />
    <Compile Include="InclusaAuthValidationService.cs" />
    <Compile Include="Integrations\Zoho\SSO\SSOZohoDesk.cs" />
    <Compile Include="Integrations\Zoho\ClientApp\UserProperty.cs" />
    <Compile Include="Integrations\Zoho\ClientApp\ZohoOAuthFilePersistence.cs" />
    <Compile Include="Integrations\Zoho\Client\ZohoOAuth.cs" />
    <Compile Include="Integrations\Zoho\Client\ZohoOAuthClient.cs" />
    <Compile Include="Integrations\Zoho\Client\ZohoPersistenceHandler.cs" />
    <Compile Include="Integrations\Zoho\Common\ZohoHTTPConnector.cs" />
    <Compile Include="Integrations\Zoho\Common\ZohoOAuthConstants.cs" />
    <Compile Include="Integrations\Zoho\Common\ZohoOAuthException.cs" />
    <Compile Include="Integrations\Zoho\Contract\ZohoOAuthTokens.cs" />
    <Compile Include="Integrations\Zoho\SSO\SSOZohoRedirect.cs" />
    <Compile Include="LegislativeDistrictService.cs" />
    <Compile Include="NotificationService.cs" />
    <Compile Include="InclusaAuthImportService.cs" />
    <Compile Include="AuthImportServiceFactory.cs" />
    <Compile Include="OhioAuthImportService.cs" />
    <Compile Include="OhioAuthReaderService.cs" />
    <Compile Include="OhioAuthValidationService.cs" />
    <Compile Include="OhioAuthWriterService.cs" />
    <Compile Include="SSO\SSODomainService.cs" />
    <Compile Include="SSO\SSOUrlResolverService.cs" />
    <Compile Include="SSO\SSOUrlResolverServiceFactory.cs" />
    <Compile Include="SWEncryption.cs" />
    <Compile Include="TaskService.cs" />
    <Compile Include="UsernameChangeService.cs" />
    <Compile Include="DeletedDataService.cs" />
    <Compile Include="ActivityRecordService.cs" />
    <Compile Include="ActivityRecordSignatureService.cs" />
    <Compile Include="CodeSetService.cs" />
    <Compile Include="CountryService.cs" />
    <Compile Include="CountyService.cs" />
    <Compile Include="DistributionListService.cs" />
    <Compile Include="EmployerService.cs" />
    <Compile Include="EvvCheckInOutService.cs" />
    <Compile Include="Exceptions\ValidationException.cs" />
    <Compile Include="FormRequirementService.cs" />
    <Compile Include="FormRequirementToServiceService.cs" />
    <Compile Include="HttpContextService.cs" />
    <Compile Include="Integrations\ChartMedsObserver.cs" />
    <Compile Include="Integrations\ChartMedsService.cs" />
    <Compile Include="Integrations\HRSTObserver.cs" />
    <Compile Include="Integrations\HRSTService.cs" />
    <Compile Include="Integrations\SisenseDataSecurityService.cs" />
    <Compile Include="Interfaces\IChartMedsService.cs" />
    <Compile Include="Interfaces\IClientService.cs" />
    <Compile Include="Interfaces\IDeletedDataService.cs" />
    <Compile Include="Interfaces\IActivityRecordSignatureService.cs" />
    <Compile Include="Interfaces\ICodeSetService.cs" />
    <Compile Include="Interfaces\ICountryService.cs" />
    <Compile Include="Interfaces\ICountyService.cs" />
    <Compile Include="Interfaces\IDataExtractorService.cs" />
    <Compile Include="Interfaces\IEmployerService.cs" />
    <Compile Include="Interfaces\INonBillableService.cs" />
    <Compile Include="Interfaces\IStateService.cs" />
    <Compile Include="Interfaces\IUserLocationService.cs" />
    <Compile Include="Models\PossibleFormEntitiesDTO.cs" />
    <Compile Include="Models\SendFormToDistributionListDTO.cs" />
    <Compile Include="Models\SmartyStreets\SmartyStreetsAddress.cs" />
    <Compile Include="Models\SmartyStreets\SmartyStreetsAddressAnalysis.cs" />
    <Compile Include="Models\SmartyStreets\SmartyStreetsAddressComponents.cs" />
    <Compile Include="Models\SmartyStreets\SmartyStreetsAddressMetadata.cs" />
    <Compile Include="Models\ClientDTO.cs" />
    <Compile Include="SmartyStreetsService.cs" />
    <Compile Include="Models\DeletedDataDTO.cs" />
    <Compile Include="Models\FormToClientDTO.cs" />
    <Compile Include="Models\ActivityRecordsGroupLocationDTO.cs" />
    <Compile Include="Models\CodeSetDTO.cs" />
    <Compile Include="Models\CodeValueDTO.cs" />
    <Compile Include="Models\CountryDTO.cs" />
    <Compile Include="Models\CountyDTO.cs" />
    <Compile Include="Models\EmployerDTO.cs" />
    <Compile Include="Models\QuestionOptionDTO.cs" />
    <Compile Include="Models\QuestionTypeDTO.cs" />
    <Compile Include="Models\Sisense\SisenseExceptions.cs" />
    <Compile Include="Models\Sisense\SisenseRESTObjects.cs" />
    <Compile Include="Models\StateDTO.cs" />
    <Compile Include="NonBillableService.cs" />
    <Compile Include="RedirectionService.cs" />
    <Compile Include="StateService.cs" />
    <Compile Include="TwoFactorAuthService.cs" />
    <Compile Include="UserLocationService.cs" />
    <Compile Include="ConsumerServiceService.cs" />
    <Compile Include="DepartmentService.cs" />
    <Compile Include="ConsumerService.cs" />
    <Compile Include="Forms\QuestionService.cs" />
    <Compile Include="Forms\QuestionControlService.cs" />
    <Compile Include="Forms\FormGroupService.cs" />
    <Compile Include="Forms\QuestionQueryAnswerService.cs" />
    <Compile Include="Forms\ConditionalQuestionService.cs" />
    <Compile Include="Forms\QuestionTypeService.cs" />
    <Compile Include="Forms\QuestionGroupService.cs" />
    <Compile Include="FunderBillingService.cs" />
    <Compile Include="GenericFormReportService.cs" />
    <Compile Include="Interfaces\Forms\IQuestionService.cs" />
    <Compile Include="Interfaces\Forms\IQuestionTypeService.cs" />
    <Compile Include="Interfaces\Forms\IQuestionControlService.cs" />
    <Compile Include="Interfaces\Forms\IFormGroupService.cs" />
    <Compile Include="Interfaces\Forms\IQuestionQueryAnswerService.cs" />
    <Compile Include="Interfaces\Forms\IConditionalQuestionService.cs" />
    <Compile Include="Interfaces\Forms\IQuestionGroupService.cs" />
    <Compile Include="Interfaces\IActivityRecordService.cs" />
    <Compile Include="Interfaces\IActivityRecordsReportService.cs" />
    <Compile Include="Interfaces\IConsumerServiceService.cs" />
    <Compile Include="Interfaces\IConsumerGoalProgressService.cs" />
    <Compile Include="ConsumerGoalProgressService.cs" />
    <Compile Include="Interfaces\IDepartmentService.cs" />
    <Compile Include="Interfaces\IConsumerService.cs" />
    <Compile Include="Interfaces\IFunderBillingService.cs" />
    <Compile Include="Interfaces\IPlaceOfServiceService.cs" />
    <Compile Include="Interfaces\IRoleService.cs" />
    <Compile Include="Interfaces\ISecureMessagingService.cs" />
    <Compile Include="Interfaces\ISignatureRequestFormTemplateService.cs" />
    <Compile Include="Interfaces\ISiteLabelService.cs" />
    <Compile Include="Interfaces\IUserService.cs" />
    <Compile Include="Models\GenericFormReportDTO.cs" />
    <Compile Include="Models\SignatureRequestFormTemplateDTO.cs" />
    <Compile Include="Models\ServiceDTO.cs" />
    <Compile Include="Models\FormSignatureConfigurationDTO.cs" />
    <Compile Include="Models\UserItemDTO.cs" />
    <Compile Include="Models\FormResultDTO.cs" />
    <Compile Include="Models\MessageTemplateDTO.cs" />
    <Compile Include="Forms\FormsService.cs" />
    <Compile Include="Interfaces\Forms\IFormsService.cs" />
    <Compile Include="MedicationAdministrationService.cs" />
    <Compile Include="AuthorizationRequestService.cs" />
    <Compile Include="Interfaces\IMedicationAdministrationService.cs" />
    <Compile Include="Interfaces\IAuthorizationRequestService.cs" />
    <Compile Include="Interfaces\ISignatureRequestService.cs" />
    <Compile Include="Models\AuthorizationRequestDTO.cs" />
    <Compile Include="Models\AuthorizationRequestUnitDTO.cs" />
    <Compile Include="Models\DepartmentDTO.cs" />
    <Compile Include="Models\ConsumerDTO.cs" />
    <Compile Include="Models\CreateAuthorizationRequestDTO.cs" />
    <Compile Include="Models\CreateSignatureRequestForAuthorizationRequestDTO.cs" />
    <Compile Include="Models\FormDTO.cs" />
    <Compile Include="Models\FormAnswerDTO.cs" />
    <Compile Include="Models\FormControlDTO.cs" />
    <Compile Include="Models\MedicationAdministrationRequestDTO.cs" />
    <Compile Include="Models\RoleDTO.cs" />
    <Compile Include="Models\SignatureRequestDTO.cs" />
    <Compile Include="Models\SignatureRequestType.cs" />
    <Compile Include="Models\SignResponseDTO.cs" />
    <Compile Include="Models\UserDTO.cs" />
    <Compile Include="PlaceOfServiceService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ActivityRecordsReportService.cs" />
    <Compile Include="SignatureRequestFormTemplateService.cs" />
    <Compile Include="RoleService.cs" />
    <Compile Include="SecureMessagingService.cs" />
    <Compile Include="SignatureRequestService.cs" />
    <Compile Include="SiteLabelService.cs" />
    <Compile Include="UserPrivService.cs" />
    <Compile Include="UserProfileService.cs" />
    <Compile Include="UserProfileServiceFactory.cs" />
    <Compile Include="UserService.cs" />
    <Compile Include="WIDVRAuthImportService.cs" />
    <Compile Include="WIDVRAuthValidationService.cs" />
    <Compile Include="WiseConsumerBenefitInformationService.cs" />
    <Compile Include="WiseConsumerBenefitInformationServiceFactory.cs" />
    <Compile Include="WiseConsumerJobPlacementsService.cs" />
    <Compile Include="WiseConsumerJobPlacementsServiceFactory.cs" />
    <Compile Include="WorkDayActivityRecordImportService.cs" />
    <Compile Include="WPSAuthImportService.cs" />
    <Compile Include="WPSAuthValidationService.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SetWorks.Common\SetWorks.Common.csproj">
      <Project>{8e1ea2eb-d6e8-4d33-9e8f-0c07859485ef}</Project>
      <Name>SetWorks.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\SetWorks.Core\SetWorks.Core.csproj">
      <Project>{ee36956f-0420-42f5-8454-41cf576bf82f}</Project>
      <Name>SetWorks.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\SetWorks.Infrastructure\SetWorks.Infrastructure.csproj">
      <Project>{69a9dc26-5ccd-40f9-a82c-090f2d6df739}</Project>
      <Name>SetWorks.Infrastructure</Name>
    </ProjectReference>
    <ProjectReference Include="..\SetWorks.Reporting\SetWorks.Reporting.csproj">
      <Project>{eba2ff59-a8ae-49aa-8c37-df576a9ff169}</Project>
      <Name>SetWorks.Reporting</Name>
    </ProjectReference>
    <ProjectReference Include="..\SETWorksDAO\SETWorksDAO.csproj">
      <Project>{33a4efad-9990-47ea-b6b7-5b75d544b0ab}</Project>
      <Name>SETWorksDAO</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include=".gitignore" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Interfaces\TwoFactorAuth\" />
    <Folder Include="Legacy\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Models\SmartyStreets\ExampleModel.json" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>