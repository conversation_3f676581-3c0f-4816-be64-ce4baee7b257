<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNet.SessionState.Resources.SR">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Cant_connect_sql_session_database">
            <summary>
              Looks up a localized string similar to Unable to connect to SQL Server session database..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Connection_name_not_specified">
            <summary>
              Looks up a localized string similar to The attribute &apos;connectionStringName&apos; is missing or empty..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Connection_string_not_found">
            <summary>
              Looks up a localized string similar to The connection name &apos;{0}&apos; was not found in the applications configuration or the connection string is empty..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Invalid_session_state">
            <summary>
              Looks up a localized string similar to The session state information is invalid and might be corrupted..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Login_failed_sql_session_database">
            <summary>
              Looks up a localized string similar to Failed to login to session state SQL server for user &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Provider_unrecognized_attribute">
            <summary>
              Looks up a localized string similar to Attribute not recognized &apos;{0}&apos;.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Session_id_too_long">
            <summary>
              Looks up a localized string similar to SessionId is too long..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.SessionState.Resources.SR.Session_not_found">
            <summary>
              Looks up a localized string similar to Session not found..
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync">
            <summary>
            Async version of SqlSessionState provider based on EF
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.Initialize(System.String,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Initialize the provider through the configuration
            </summary>
            <param name="name">Sessionstate provider name</param>
            <param name="config">Configuration values</param>
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.CreateNewStoreData(System.Web.HttpContextBase,System.Int32)">
            <summary>
            Create a new SessionStateStoreData
            </summary>
            <param name="context">Httpcontext</param>
            <param name="timeout">Session timeout</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.CreateUninitializedItemAsync(System.Web.HttpContextBase,System.String,System.Int32,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.EndRequestAsync(System.Web.HttpContextBase)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.GetItemAsync(System.Web.HttpContextBase,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.GetItemExclusiveAsync(System.Web.HttpContextBase,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.InitializeRequest(System.Web.HttpContextBase)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.ReleaseItemExclusiveAsync(System.Web.HttpContextBase,System.String,System.Object,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.RemoveItemAsync(System.Web.HttpContextBase,System.String,System.Object,System.Web.SessionState.SessionStateStoreData,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.ResetItemTimeoutAsync(System.Web.HttpContextBase,System.String,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.SetAndReleaseItemExclusiveAsync(System.Web.HttpContextBase,System.String,System.Web.SessionState.SessionStateStoreData,System.Object,System.Boolean,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.SetItemExpireCallback(System.Web.SessionState.SessionStateItemExpireCallback)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNet.SessionState.SqlSessionStateRepository">
            <summary>
            SQL server version must be >= 8.0
            </summary>
        </member>
    </members>
</doc>
