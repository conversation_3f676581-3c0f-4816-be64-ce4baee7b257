<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.CodeAnalysis.CSharp.Scripting</name>
    </assembly>
    <members>
        <member name="T:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript">
            <summary>
            A factory for creating and running C# scripts.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.Create``1(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Type,Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader)">
            <summary>
            Create a new C# script.
            </summary>
            <param name="code">The source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globalsType">Type of global object.</param>
            <param name="assemblyLoader">Custom  assembly loader.</param>
            <typeparam name="T">The return type of the script</typeparam>
            <exception cref="T:System.ArgumentNullException">Code is null.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.Create``1(System.IO.Stream,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Type,Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader)">
            <summary>
            Create a new C# script.
            </summary>
            <param name="code">The <see cref="T:System.IO.Stream"/> representing the source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globalsType">Type of global object.</param>
            <param name="assemblyLoader">Custom  assembly loader.</param>
            <typeparam name="T">The return type of the script</typeparam>
            <exception cref="T:System.ArgumentNullException">Stream is null.</exception>
            <exception cref="T:System.ArgumentException">Stream is not readable or seekable.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.Create(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Type,Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader)">
            <summary>
            Create a new C# script.
            </summary>
            <param name="code">The source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globalsType">Type of global object.</param>
            <param name="assemblyLoader">Custom  assembly loader.</param>
            <exception cref="T:System.ArgumentNullException">Code is null.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.Create(System.IO.Stream,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Type,Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader)">
            <summary>
            Create a new C# script.
            </summary>
            <param name="code">The <see cref="T:System.IO.Stream"/> representing the source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globalsType">Type of global object.</param>
            <param name="assemblyLoader">Custom  assembly loader.</param>
            <exception cref="T:System.ArgumentNullException">Stream is null.</exception>
            <exception cref="T:System.ArgumentException">Stream is not readable or seekable.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.RunAsync``1(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Run a C# script.
            </summary>
            <param name="code">The source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globals">An object instance whose members can be accessed by the script as global variables.</param>
            <param name="globalsType">Type of global object, <paramref name="globals"/>.GetType() is used if not specified.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <typeparam name="T">The return type of the submission</typeparam>
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Specified code has errors.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.RunAsync(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Run a C# script.
            </summary>
            <param name="code">The source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globals">An object instance whose members can be accessed by the script as global variables.</param>
            <param name="globalsType">Type of global object, <paramref name="globals"/>.GetType() is used if not specified.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Specified code has errors.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.EvaluateAsync``1(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Run a C# script and return its resulting value.
            </summary>
            <param name="code">The source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globals">An object instance whose members can be accessed by the script as global variables.</param>
            <param name="globalsType">Type of global object, <paramref name="globals"/>.GetType() is used if not specified.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <typeparam name="T">The return type of the submission</typeparam>
            <return>Returns the value returned by running the script.</return>
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Specified code has errors.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScript.EvaluateAsync(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Object,System.Type,System.Threading.CancellationToken)">
            <summary>
            Run a C# script and return its resulting value.
            </summary>
            <param name="code">The source code of the script.</param>
            <param name="options">The script options.</param>
            <param name="globals">An object instance whose members can be accessed by the script as global variables.</param>
            <param name="globalsType">Type of global object, <paramref name="globals"/>.GetType() is used if not specified.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <return>Returns the value returned by running the script.</return>
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Specified code has errors.</exception>
        </member>
        <member name="T:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScriptingResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScriptingResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScriptingResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScriptingResources.InteractiveHelp">
             <summary>
               Looks up a localized string similar to Usage: csi [option] ... [script-file.csx] [script-argument] ...
            
            Executes script-file.csx if specified, otherwise launches an interactive REPL (Read Eval Print Loop).
            
            Options:
              /help                          Display this usage message (alternative form: /?)
              /version                       Display the version and exit
              /i                             Drop to REPL after executing the specified script.
              /r:&lt;file&gt;                      Reference metadata from the specified assembly file (alternative  [rest of string was truncated]&quot;;.
             </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScriptingResources.LogoLine1">
            <summary>
              Looks up a localized string similar to Microsoft (R) Visual C# Interactive Compiler version {0}.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.CSharp.Scripting.CSharpScriptingResources.LogoLine2">
            <summary>
              Looks up a localized string similar to Copyright (C) Microsoft Corporation. All rights reserved..
            </summary>
        </member>
    </members>
</doc>
