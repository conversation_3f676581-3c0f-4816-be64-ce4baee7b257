using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI.Calendar;
using Telerik.Web.UI;
using System.Data;
using System.Collections;
using System.IO;
using System.Web.UI.HtmlControls;
using BOs;
using Forms;
using SW.UI;
using HtmlAgilityPack;
using Home.SWControls;
using SetWorks.Services.Models;
using Ninject;
using SetWorks.Common.Tools.SETWorksAI;
using SetWorks.DAO.Authorization;
using SetWorks.Services;
using SetWorks.Services.Authorizations;
using SetWorks.Services.Interfaces;
using SETWorksDAO.DAO.AI_ASSISTANT_ACTIVITY_RECORD_CONFIGURATION;

public partial class Home_ActivityRecords_ActivityRecordTypes_BillableActivityRecordWithAssessmentAndIntervention : SWPage
{
    [Inject]
    public ISignatureRequestService SigService { get; set; }

    [Inject]
    public IActivityRecordService ActivityRecordService { get; set; }

    [Inject]
    public IFormRequirementService FormRequirementService { get; set; }
    
    [Inject]
    public IAuthorizationsService AuthorizationsService { get; set; }
    [Inject]
    public IAIConfiguration _aAiConfiguration { get; set; }
    [Inject]
    public IAISystemConfiguration _aiSystemConfiguration { get; set; }
    [Inject]
    public IAIClient _aiClient { get; set; }
    
    private String USER_COMMENT_ID = "UserCommentID";
    private String GOAL_ID = "GoalID";
    private String PARENT_CONTAINER = "PARENT_CONTAINER_ID";
    private String COMMENT_TYPE = "COMMENT_TYPE";
    private bool USER_HAS_AI_ASSISTANT_ACCESS = false;
    private DSAIAssistantActivityRecordConfiguration activityRecordsAIAssistantConfig;
    
    public string ActivityRecordID
    {
        get
        {
            return string.IsNullOrEmpty(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value) || HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value == "0"
                ? HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value
                : HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
        }
    }

    protected void Page_Init(object sender, EventArgs e)
    {
        HtmlMeta viewport = new HtmlMeta();
        viewport.Name = "viewport";
        viewport.Content = "width=device-width, initial-scale=1";
        Page.Header.Controls.Add(viewport);
        int clientID = getClientID();
        string userID = getUserID();
        USER_HAS_AI_ASSISTANT_ACCESS = BOAIAccessConfiguration.hasAIEnhancedAccess(clientID, userID);
        activityRecordsAIAssistantConfig = BOAIAssistantActivityRecordConfiguration.getAIAssistantActivityRecordConfiguration(getClientID());
    }
    
    protected void Page_Load(object sender, EventArgs e)
    {
        HiddenCURRENTWINDOWCLIENTID.Value = getClientID().ToString();
        HiddenCURRENTWINDOWUSERID.Value = getUserID();

        CheckInitializationPrivs();
        
        long[] iAccess = getUserPrivs();

        if (!Page.IsPostBack)
        {

            FilesGrid1.hideLabelsColumn = true;

            ActivityRecordsMisc.setupScheduler(RadSchedulerDayWAI, HiddenCURRENTWINDOWUSERID.Value);

            if (Request["CreateActivityRecordAtDateTime"] != null && Request["CreateActivityRecordAtDateTime"].ToString().CompareTo("USESESSION") != 0)
            {
                // create a new activity record - then set the date time accordingly
                DateTime clickedDateTime = DateTime.Parse(Request["CreateActivityRecordAtDateTime"].ToString());
                Session[SessionDefinitions.AR_START_DATE_TIME] = clickedDateTime;
            }
            if (Request["ActivityRecordID"] != null)
            {
                HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value = Request["ActivityRecordID"];
                FilesGrid1.TempElementID = Request["ActivityRecordID"];
            }

            HiddenACTION_TYPEWAI.Value = Constants.CREATE;
            if (Request["Action"] != null)
            {
                String temp = Request["Action"];
                if (temp.CompareTo(Constants.CREATE) == 0 || temp.CompareTo(Constants.MODIFY) == 0)
                {
                    HiddenACTION_TYPEWAI.Value = Request["Action"];
                }
                else if (temp.CompareTo(Constants.CONVERT_FROM_APPT) == 0)
                {
                    HiddenCONVERTEDFROMAPPT.Value = "True";
                    HiddenACTION_TYPEWAI.Value = Constants.MODIFY;
                }
            }
            if (Request["ConsumerID"] != null)
            {
                HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value = Request["ConsumerID"];
                HiddenBILLABLECAPAR_CONSUMER_ID.Value = Request["ConsumerID"];
            }

            setSiteLabels();

            bool arWithinDateRange = true;
            bool isAppointment = false;

            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
            {
                HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value = BORandom.getRandomIDTableIncrementer(getUsername()).ToString();
                FilesGrid1.FileTypeCode = "BILLABLE_CAP_FILE";
                FilesGrid1.ElementID = HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
                ActivityRecordsMisc.setARDescriptionByTypeAndAction(DSActivityRecordType.ACTIVITYRECORD_BILLABLE_INTER_ASSESS_KEY, I18NHelper.I18NString("create") + " ", HiddenCURRENTWINDOWCLIENTID.Value, ActivityRecordTitle);
                LinkButtonUpdate.Visible = false;
                UpdateListItem.Visible = false;
                LinkButtonStaffUpdateAndSign.Visible = false;
                UpdateAndSignListItem.Visible = false;
                LinkButtonManagerUpdateAndSign.Visible = false;
                UpdateAndManagerSignListItem.Visible = false;
                LinkButtonUpdateAndDuplicate.Visible = false;
                UpdateAndDuplicateListItem.Visible = false;
                LinkButtonDuplicate.Visible = false;
                DuplicateListItem.Visible = false;
                arAddendumIcon.Visible = false;
                initializePage();
                ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);
                LinkButtonComposeMessage.Visible = false;
                ComposeMessageListItem.Visible = false;
                composeSpacer.Visible = false;
                ComposeMessageSpacer.Visible = false;

                if (Privilege.isPriv3True(iAccess, Privileges3.AR_SHOW_OFF_SITE_OPTION_FOR_NARRATIVE_AR))
                {
                    chckBoxOffSite.Visible = true;
                    LabelCheckBoxOffSite.Visible = true;
                }

                if (Privilege.isPriv6True(iAccess, Privileges6.ACTIVITY_RECORD_SHOW_CREATE_CONSUMER_AT_WORK_OPTION))
                {
                    lblCheckBoxCreateConsumerWork.Visible = true;
                    chkBoxCreateConsumerAtWorkRecord.Visible = true;
                    PanelConsumerAtWork.Visible = false;
                   
                    DSActivityRecordType[] activityRecordTypes = BOActivityRecord.getActivityRecordTypesByClientIDAndUserDept(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), HiddenCURRENTWINDOWUSERID.Value);
                    foreach (var arType in activityRecordTypes.Where(t => t.getActive))
                    {
                        if (arType.getCode == "CONSUMER_WORK")
                        {
                            lblCheckBoxCreateConsumerWork.Text = "Create " + arType.getDescription + " Record:";
                        }
                    }
                }
                else
                {
                    lblCheckBoxCreateConsumerWork.Visible = false;
                    chkBoxCreateConsumerAtWorkRecord.Visible = false;
                    PanelConsumerAtWork.Visible = false;
                }

                LINightAttendance.Visible = Privilege.isPriv6True(iAccess, Privileges6.ALLOW_MANUALLY_SETTING_NIGHT_ATTENDANCE_ON_ARS);
                arHistoryIcon.Visible = false;
                ARGLIcon.Visible = false;
                LinkButtonManagerSaveAndSign.Visible = false;
                SaveAndSignManagerListItem.Visible = false;
                if (!Privilege.isPrivTrue(iAccess, Privileges5.STAFF_SIGNATURE_REQUIRED_FOR_MGR_TO_SIGN) && Privilege.isPrivTrue(iAccess, Privileges.SIGN_ARS_AS_MANAGER))
                {
                    LinkButtonManagerSaveAndSign.Visible = true;
                    SaveAndSignManagerListItem.Visible = true;
                }
                if (!Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_SIGN_AND_UPDATE_AND_SIGN_ON_BILLABLE_CAP))
                {
                    LinkButtonStaffSaveAndSign.Visible = false;
                    LinkButtonManagerSaveAndSign.Visible = false;
                    SaveAndSignListItem.Visible = false;
                    SaveAndSignManagerListItem.Visible = false;
                }
                if (!Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_UPDATE_ON_BILLABLE_CAP))
                {
                    LinkButtonSave.Visible = false;
                    SaveListItem.Visible = false;
                }
                LinkButtonSaveAndDuplicate.Visible = false;
                SaveAndDuplicateListItem.Visible = false;
                if (Privilege.isPrivTrue(iAccess,Privileges10.SHOW_DUPLICATE_BUTTON_ON_BILLABLE_CAP))
                {
                    LinkButtonSaveAndDuplicate.Visible = true;
                    SaveAndDuplicateListItem.Visible = true;
                }
                if (!Privilege.isPrivTrue(iAccess, Privileges17.SHOW_GOALS_AND_OUTCOMES_SECTION_BILLABLE_INDIVIDUALS))
                {
                    Fieldsetgoals.Attributes.Add("style", "display:none;");
                }
            }
            else if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
            {
                Label3.Text = "Activity Details #" + HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
                LinkButtonSave.Visible = false;
               SaveListItem.Visible = false;
                LinkButtonStaffSaveAndSign.Visible = false;
                SaveAndSignListItem.Visible = false;
                LinkButtonManagerSaveAndSign.Visible = false;
                SaveAndSignManagerListItem.Visible = false;
                LinkButtonSaveAndDuplicate.Visible = false;
                SaveAndDuplicateListItem.Visible = false;
                ActivityRecordsMisc.setARDescriptionByTypeAndAction(DSActivityRecordType.ACTIVITYRECORD_BILLABLE_INTER_ASSESS_KEY, I18NHelper.I18NString("modify") + " ", HiddenCURRENTWINDOWCLIENTID.Value, ActivityRecordTitle);
                initializePage();
                openActivityRecordWAI(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
                ActivityRecordsMisc.setNumericTimePickerBasedOnCurrentDateTimeSelection(RadTimePickerFromWAI, RadTimePickerToWAI, RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM);
                LinkButtonComposeMessage.Visible = true;
                ComposeMessageListItem.Visible = true;
                composeSpacer.Visible = true;
                ComposeMessageSpacer.Visible = true;

                if (Privilege.isPrivTrue(iAccess, Privileges13.CAN_CONVERT_BILLABLE_TO_NONBILLABLE_INDIVIDUAL_AR) && HiddenCONVERTEDFROMAPPT.Value.Equals("False"))
                {
                    ConvertToSubMenu.Visible = true;
                    SetConvertableARTypes();
                }

                lblCheckBoxCreateConsumerWork.Visible = false;
                chkBoxCreateConsumerAtWorkRecord.Visible = false;
                PanelConsumerAtWork.Visible = false;

                if (!String.IsNullOrEmpty(RadComboBoxConsumerWAI.SelectedValue))
                {
                    HiddenBILLABLECAPAR_CONSUMER_ID.Value = RadComboBoxConsumerWAI.SelectedValue;
                    HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value = RadComboBoxConsumerWAI.SelectedValue;
                }

                // hide the consumer at work panel
                PanelConsumerAtWork.Visible = false;
                if (HiddenTRANSFORMATION.Value == "TRUE" && Privilege.isPriv6True(iAccess, Privileges6.ACTIVITY_RECORD_SHOW_CREATE_CONSUMER_AT_WORK_OPTION))
                {
                    lblCheckBoxCreateConsumerWork.Visible = true;
                    chkBoxCreateConsumerAtWorkRecord.Visible = true;
                    PanelConsumerAtWork.Visible = false;

                    DSActivityRecordType[] activityRecordTypes = BOActivityRecord.getActivityRecordTypesByClientIDAndUserDept(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), HiddenCURRENTWINDOWUSERID.Value);
                    foreach (var arType in activityRecordTypes.Where(t => t.getActive))
                    {
                        if (arType.getCode == "CONSUMER_WORK")
                        {
                            lblCheckBoxCreateConsumerWork.Text = "Create " + arType.getDescription + " Record:";
                        }
                    }
                }

                if (Privilege.isPriv3True(iAccess, Privileges3.AR_SHOW_OFF_SITE_OPTION_FOR_NARRATIVE_AR))
                {
                    chckBoxOffSite.Visible = true;
                    LabelCheckBoxOffSite.Visible = true;
                }
                if(Privilege.isPrivTrue(iAccess, Privileges11.SHOW_ARGE_BUTTON_CAP))
                {
                    ARGLIcon.Visible = true;
                }
                if (!Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_UPDATE_ON_BILLABLE_CAP))
                {
                    LinkButtonUpdate.Visible = false;
                    UpdateListItem.Visible = false;
                }
                LinkButtonDuplicate.Visible = false;
                DuplicateListItem.Visible = false;
                LinkButtonUpdateAndDuplicate.Visible = false;
                UpdateAndDuplicateListItem.Visible = false;
                if(Privilege.isPrivTrue(iAccess, Privileges10.SHOW_DUPLICATE_BUTTON_ON_BILLABLE_CAP))
                {
                    LinkButtonUpdateAndDuplicate.Visible = true;
                    UpdateAndDuplicateListItem.Visible = true;
                    LinkButtonDuplicate.Visible = true;
                    DuplicateListItem.Visible = true;
                }
                if (!Privilege.isPrivTrue(iAccess, Privileges9.CAN_ADD_ADDENDUMS_TO_BILLABLE_INDIVIDUAL))
                {
                    arAddendumIcon.Visible = false;
                }
                else
                {
                    arAddendumIcon.Visible = true;
                    List<DSAddendum> addendums = BOAddendum.getAddendumsByActivityRecordID(getClientID(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

                    if (addendums != null && addendums.Count > 0)
                    {
                        HtmlImage arAddendumImage = (HtmlImage)LIServicePhase.FindControl("arAddendumImage");
                        if (arAddendumImage != null) arAddendumImage.Src = "~/Images/ExistingAddendum.png";
                    }
                }
                if (!Privilege.isPrivTrue(iAccess, Privileges11.BILLABLE_INDIVIDUAL_IS_ABLE_TO_EDIT_CONSUMER_DROPDOWN_ON_EDIT_AND_CONVERT))
                {
                    RadComboBoxConsumerWAI.Enabled = false;
                    RadComboBoxDepartmentWAI.Enabled = false;
                    RadComboBoxAuthorizationWAI.Enabled = false;
                }

                HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value = BORandom.getRandomIDTableIncrementer(getUsername()).ToString();
                FilesGrid1.FileTypeCode = "BILLABLE_CAP_FILE";
                FilesGrid1.ElementID = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;

                DSActivityRecord ar = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getClientID());
                if (ar.getActivityRecordType.getCode.CompareTo("APPOINTMENT") == 0 || ar.getActivityRecordType.getCode.CompareTo("APPOINTMENT_AUTH") == 0)
                {
                    isAppointment = true;
                }

                arWithinDateRange = ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblInformation, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value);

                if (!arWithinDateRange)
                {
                    if (!isAppointment)
                    {
                        disableCapRecord(true);
                        RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                        InformationDIV.Style.Add("display", "block");
                        HiddenRECORDLOCK.Value = "True";
                    }
                }
            }

            if (!Privilege.isPrivTrue(iAccess, Privileges9.CONSUMER_SIGNATURE_ON_BILLABLE_CAP))
            {
                SignatureList.Visible = false;
            }
            else
            {
                LoadSignatures();
            }

            DateTime timePickerFromDateTime = RadTimePickerFromWAI.SelectedDate.Value;
            DateTime timePickerToDateTime = RadTimePickerToWAI.SelectedDate.Value;
            DateTime schedulerFromDateTime = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToShortDateString());
            DateTime startTime = schedulerFromDateTime.AddHours(timePickerFromDateTime.Hour).AddMinutes(timePickerFromDateTime.Minute);
            DateTime endTime = schedulerFromDateTime.AddHours(timePickerToDateTime.Hour).AddMinutes(timePickerToDateTime.Minute);

            DateTime featureSuspensionStartTime = ActivityRecordsMisc.getActivityRecordLockDateTimePastBack(iAccess, getUserID(), getClientID().ToString(), "BILLABLE_INDIVIDUAL");
            DateTime featureSuspensionEndTime = ActivityRecordsMisc.getActivityRecordLockDateTimeFutureToFuture(iAccess, getUserID(), getClientID().ToString(), "BILLABLE_INDIVIDUAL");

            DSRecordLock recordLock = BORecordLock.GetRecordLockByClientID(getClientID());
            List<string> stringsToAjaxify = new List<string> {
                    chkBoxCreateConsumerAtWorkRecord.Visible ? "chkBoxCreateConsumerAtWorkRecord" : "",
                    RadNumericTextFromHours.Visible ? "RadNumericTextFromHours" : "",
                    RadioButtonListFromAMPM.Visible ? "RadioButtonListFromAMPM" : "",
                    RadNumericTextToHours.Visible ? "RadNumericTextToHours" : "",
                    RadNumericTextToMinutes.Visible ? "RadNumericTextToMinutes" : "",
                    RadioButtonListToAMPM.Visible ? "RadioButtonListToAMPM" : "",
                    CheckboxNightAttendance.Visible ? "CheckboxNightAttendance" : "",
                    chckBoxOffSite.Visible ? "chckBoxOffSite" : "",
                    chkBoxFaceToFace.Visible ? "chkBoxFaceToFace" : "",
                    FaceToFaceTimeHours.Visible ? "FaceToFaceTimeHours" : "",
                    FaceToFaceTimeMinutes.Visible ? "FaceToFaceTimeMinutes" : "",
                    RecordKeepingTimeHours.Visible ? "RecordKeepingTimeHours" : "",
                    RecordKeepingTimeMinutes.Visible ? "RecordKeepingTimeMinutes" : "",
                    TravelTimeHours.Visible ? "TravelTimeHours" : "",
                    TravelTimeMinutes.Visible ? "TravelTimeMinutes": "",
                    Miles.Visible ? "Miles" : "",
                    CollaborationTimeHours.Visible ? "CollaborationTimeHours" : "",
                    CollaborationTimeMinutes.Visible ? "CollaborationTimeMinutes" : "",
                    TotalBillableTimeHours.Visible ? "TotalBillableTimeHours" : "",
                    TotalBillableTimeMinutes.Visible ? "TotalBillableTimeMinutes" : "",
                    RadTextBoxAdditionalComments.Visible ? "RadTextBoxAdditionalComments" : "",
                    RadComboBoxAdditionalSupports.Visible ? "RadComboBoxAdditionalSupports" : "",
                    RadComboBoxPlaceOfService.Visible ? "RadComboBoxPlaceOfService" : "",
                    RadComboBoxIndividualServicePhase.Visible ? "RadComboBoxIndividualServicePhase" : "",
                    lateRecordDiv.Visible ? "lateRecordDiv" : "",
                    InformationDIV.Visible ? "InformationDIV": "",
                    RadComboBoxAdditionalSupports.Visible ? "RadComboBoxAdditionalSupports" : "",
                    RadTimePickerFromWAI.Visible ? "RadTimePickerFromWAI" : "",
                    RadTimePickerToWAI.Visible ? "RadTimePickerToWAI" : "",
                    RadTextBoxAdditionalComments.Visible ? "RadTextBoxAdditionalComments" : "",
                    LinkButtonUpdate.Visible ? "LinkButtonUpdate" : "",
                    LinkButtonDuplicate.Visible ? "LinkButtonDuplicate" : "",
                    LinkButtonUpdateAndDuplicate.Visible ? "LinkButtonUpdateAndDuplicate" : "",
                    LinkButtonManagerUpdateAndSign.Visible ? "LinkButtonManagerUpdateAndSign" : "",
                    LinkButtonStaffUpdateAndSign.Visible ? "LinkButtonStaffUpdateAndSign" : "",
                    LinkButtonSave.Visible ? "LinkButtonSave" : "",
                    LinkButtonSaveAndDuplicate.Visible ? "LinkButtonSaveAndDuplicate" : "",
                    LinkButtonStaffSaveAndSign.Visible ? "LinkButtonStaffSaveAndSign" : "",
                    LinkButtonManagerSaveAndSign.Visible ? "LinkButtonManagerSaveAndSign" : "",
            };

            if (recordLock != null)
            {
                ajaxify("RadSchedulerDayWAI", stringsToAjaxify.ToArray());

                if ((recordLock.LockDateTime > startTime && recordLock.LockDateTime >= endTime) && recordLock.LockARDates)
                {
                    if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0 && !isAppointment)
                    {
                        RadTimePickerFromWAI.Enabled = false;
                        RadTimePickerToWAI.Enabled = false;
                        RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                        HiddenRECORDLOCK.Value = "True";
                    }

                    lblInformation.Text = "The system has been locked to prevent changing the dates of activity records prior to " + recordLock.LockDateTime.ToShortDateString() + " at " + recordLock.LockDateTime.ToShortTimeString();
                    InformationDIV.Style.Add("display", "block");
                }
                else if (recordLock.LockDateTime > startTime || recordLock.LockDateTime >= endTime)
                {

                    if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0 && !isAppointment)
                    {
                        disableCapRecordNoTimePicker(true);
                        RadTimePickerFromWAI.Enabled = false;
                        RadTimePickerToWAI.Enabled = false;
                        RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                        HiddenRECORDLOCK.Value = "True";
                    }

                    lblInformation.Text = "The system has been locked to prevent the modification or creation of activity records prior to " + recordLock.LockDateTime.ToShortDateString() + " at " + recordLock.LockDateTime.ToShortTimeString();
                    InformationDIV.Style.Add("display", "block");
                }
                else if (lblInformation.Text.Length > 0 || lblStatusUpdateWAI.Text.Length > 0)
                {
                    if (arWithinDateRange)
                    {
                        InformationDIV.Style.Add("display", "none");
                    }
                }
            }

            DSUserFeatureSuspension[] featureSuspension = BOUserFeatureSuspension.getUserFeatureSuspensionByFeatureCodeAndUserID("AR_PRIV_DAYS_FORWARD_AND_BACK_SUSPEND", getUserID(), getClientID());
            foreach (DSUserFeatureSuspension fs in featureSuspension)
            {
                ajaxify("RadSchedulerDayWAI", stringsToAjaxify.ToArray());
                if (fs.getSuspendedFrom == DateTime.MinValue && fs.getSuspendedTo == DateTime.MinValue)
                {
                    if ((featureSuspensionEndTime != DateTime.MinValue && startTime > featureSuspensionEndTime) || (featureSuspensionStartTime != DateTime.MinValue && endTime < featureSuspensionStartTime))
                    {
                        if (!fs.getChangeAllOtherDetailsOfAR)
                        {
                            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0 && !isAppointment)
                            {
                                if (fs.getChangeDateAndTimeOfAR)
                                {
                                    disableCapRecordNoTimePicker(true, false);
                                }
                                else
                                {
                                    disableCapRecordNoTimePicker(true);
                                }
                            }

                            lblInformation.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            InformationDIV.Style.Add("display", "block");
                        }
                        if (!fs.getChangeDateAndTimeOfAR)
                        {
                            if (fs.getChangeAllOtherDetailsOfAR)
                            {
                                disableCapRecordNoTimePicker(false, false);
                            }
                            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0 && !isAppointment)
                            {
                                RadTimePickerFromWAI.Enabled = false;
                                RadTimePickerToWAI.Enabled = false;
                                RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                            }
                            lblInformation.Text = "Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            InformationDIV.Style.Add("display", "block");
                        }
                        else
                        {
                            if (recordLock != null && (recordLock.LockDateTime >= startTime || recordLock.LockDateTime >= endTime)) // && !recordLock.LockARDates)
                            {
                                RadTimePickerFromWAI.Enabled = true;
                                RadTimePickerToWAI.Enabled = true;
                                RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                                HiddenRECORDLOCK.Value = "False";
                            }

                            if (fs.getChangeAllOtherDetailsOfAR)
                            {
                                lblInformation.Text = "";
                                InformationDIV.Style.Add("display", "none");
                            }
                        }
                        if (!fs.getChangeDateAndTimeOfAR && !fs.getChangeAllOtherDetailsOfAR)
                        {
                            lblInformation.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString() + "<br/>Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            InformationDIV.Style.Add("display", "block");
                        }

                        if (fs.getChangeAllOtherDetailsOfAR && fs.getChangeDateAndTimeOfAR)
                        {
                            if (recordLock != null && (recordLock.LockDateTime >= startTime || recordLock.LockDateTime >= endTime) && !recordLock.LockARDates)
                            {
                                RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                                disableCapRecordNoTimePicker(false);
                                lblInformation.Text = "";
                                InformationDIV.Style.Add("display", "none");
                                HiddenRECORDLOCK.Value = "False";
                            }
                        }
                    }
                    else
                    {
                        RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                        disableCapRecordNoTimePicker(false);
                        lblInformation.Text = "";
                        InformationDIV.Style.Add("display", "none");
                        HiddenRECORDLOCK.Value = "False";
                    }
                }
                else if (fs.getSuspendedFrom <= startTime && fs.getSuspendedTo >= endTime)
                {
                    if ((featureSuspensionEndTime != DateTime.MinValue && startTime > featureSuspensionEndTime) || (featureSuspensionStartTime != DateTime.MinValue && endTime < featureSuspensionStartTime))
                    {
                        if (!fs.getChangeAllOtherDetailsOfAR)
                        {
                            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0 && !isAppointment)
                            {
                                if (fs.getChangeDateAndTimeOfAR)
                                {
                                    disableCapRecordNoTimePicker(true, false);
                                }
                                else
                                {
                                    disableCapRecordNoTimePicker(true);
                                }
                            }

                            lblInformation.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            InformationDIV.Style.Add("display", "block");
                        }
                        if (!fs.getChangeDateAndTimeOfAR)
                        {

                            if (fs.getChangeAllOtherDetailsOfAR)
                            {
                                disableCapRecordNoTimePicker(false, false);
                            }
                            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                            {
                                RadTimePickerFromWAI.Enabled = false;
                                RadTimePickerToWAI.Enabled = false;
                                RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                            }
                            lblInformation.Text = "Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            InformationDIV.Style.Add("display", "block");
                        }
                        else
                        {
                            if (recordLock != null && (recordLock.LockDateTime >= startTime || recordLock.LockDateTime >= endTime) && !recordLock.LockARDates)
                            {
                                RadTimePickerFromWAI.Enabled = true;
                                RadTimePickerToWAI.Enabled = true;
                                HiddenRECORDLOCK.Value = "False";
                                RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                            }

                            if (fs.getChangeAllOtherDetailsOfAR)
                            {
                                lblInformation.Text = "";
                                InformationDIV.Style.Add("display", "none");
                            }
                        }
                        if (!fs.getChangeDateAndTimeOfAR && !fs.getChangeAllOtherDetailsOfAR)
                        {
                            lblInformation.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString() + "<br/>Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            InformationDIV.Style.Add("display", "block");
                        }

                        if (fs.getChangeAllOtherDetailsOfAR && fs.getChangeDateAndTimeOfAR)
                        {
                            if (recordLock != null && (recordLock.LockDateTime >= startTime || recordLock.LockDateTime >= endTime) && !recordLock.LockARDates)
                            {
                                RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                                disableCapRecordNoTimePicker(false);
                                lblInformation.Text = "";
                                HiddenRECORDLOCK.Value = "False";
                                InformationDIV.Style.Add("display", "none");
                            }
                            if (recordLock == null)
                            {
                                RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                                disableCapRecordNoTimePicker(false);
                                lblInformation.Text = "";
                                HiddenRECORDLOCK.Value = "False";
                                InformationDIV.Style.Add("display", "none");
                            }
                        }
                    }
                    else
                    {
                        RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                        disableCapRecordNoTimePicker(false);
                        lblInformation.Text = "";
                        HiddenRECORDLOCK.Value = "False";
                        InformationDIV.Style.Add("display", "none");
                    }
                }
                else if (lblInformation.Text.Length > 0)
                {
                    if (recordLock == null)
                    {
                        disableCapRecordNoTimePicker(false);
                        lblInformation.Text = "";
                        InformationDIV.Style.Add("display", "none");
                    }
                }
            }

            HiddenARTIMESPANSTART.Value = DateTime.UtcNow.ToString();
            FieldsetAuthorizationInformation.Visible = false;
            validateFileAcknowledgement();
        }

        string consumerDescriptionSingular =
            BOSiteLabel.getSiteLabelSingularDescriptionByClientIDAndCode(getClientID().ToString(), "CONSUMER_DESCRIPTION");
        lblConsumerColonWAI.Text = consumerDescriptionSingular + ":";
        HiddenCONSUMER_TERMINOLOGY.Value = consumerDescriptionSingular;

        DSUserPreferenceString preferenceObject = BOUser.getUserPreferenceStringByUserIDAndPreferenceCODE(getUserID(), DSUserPreferenceString.DEFAULT_TIME_FOR_NEW_ARS);
        String minutes = preferenceObject.getPreferenceValue;

        LabelARSlotTimePreferenceWAI.Text = minutes;

        if (Privilege.isPriv2True(iAccess, Privileges2.ACTIVITY_RECORD_TIME_VIEW_RESTRICTED_TO_15_MIN_INCREMENT))
        {
            RadTimePickerFromWAI.EnableTyping = false;
            RadTimePickerFromWAI.TimeView.StartTime = new TimeSpan();
            RadTimePickerFromWAI.TimeView.EndTime = new TimeSpan(23, 59, 0);

            RadTimePickerFromWAI.TimeView.Interval = new TimeSpan(0, 15, 0);
            RadTimePickerFromWAI.TimeView.Columns = 4;
            RadTimePickerFromWAI.PopupDirection = DatePickerPopupDirection.BottomRight;

            RadTimePickerToWAI.EnableTyping = false;
            RadTimePickerToWAI.TimeView.StartTime = new TimeSpan();
            RadTimePickerToWAI.TimeView.EndTime = new TimeSpan(23, 59, 0);

            RadTimePickerToWAI.TimeView.Interval = new TimeSpan(0, 15, 0);
            RadTimePickerToWAI.TimeView.Columns = 4;
            RadTimePickerToWAI.PopupDirection = DatePickerPopupDirection.BottomLeft;
        }

        if (Privilege.isPriv6True(iAccess, Privileges6.CAP_RECORDS_CAN_TYPE_IN_TIME_BOXES_ALWAYS))
        {
            RadTimePickerFromWAI.EnableTyping = true;
            RadTimePickerToWAI.EnableTyping = true;
        }

        LIServicePhase.Visible = false;
        if (Privilege.isPriv6True(iAccess, Privileges6.ENABLE_SERVICE_PHASES))
        {
            LIServicePhase.Visible = true;

            ajaxify(RadComboBoxDepartmentWAI.ID, RadComboBoxIndividualServicePhase.ID);
            ajaxify(RadComboBoxConsumerWAI.ID, RadComboBoxIndividualServicePhase.ID);
            ajaxify(RadComboBoxAuthorizationWAI.ID, RadComboBoxIndividualServicePhase.ID);
			ajaxify(LinkButtonSaveAndDuplicate.ID, RadComboBoxIndividualServicePhase.ID);
			ajaxify(LinkButtonUpdateAndDuplicate.ID, RadComboBoxIndividualServicePhase.ID);
			ajaxify(LinkButtonDuplicate.ID, RadComboBoxIndividualServicePhase.ID);
            ajaxify(RadSchedulerDayWAI.ID, RadComboBoxIndividualServicePhase.ID);
        }

        // Disable single comment if to have multiple
        if (Privilege.isPriv4True(iAccess, Privileges4.ACTIVITY_RECORD_CAP_HAVE_GOAL_SPECIFIC_COMMENT))
        {
            RadTextBoxComment.Visible = false;
            lblComments.Visible = false;
        }
        else
        {
            if (!Privilege.isPrivTrue(iAccess, Privileges13.CAP_RECORDS_SHOW_COMMENT_BOX_IN_LEFT_PANEL_WHEN_SINGLE_GOAL_COMMENT))
            {
                RadTextBoxComment.Visible = false;
                lblComments.Visible = false;
            }
        }
        
		AutoReviewAdditionalCommentsPanel.Controls.Clear();
        rebuildDynamicControlsWithCurrentSelection(false);

        FieldsetAdditionalSupports.Style.Add("display", "none");
        if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_LOGGING_ON_ADDITIONAL_SUPPORTS))
        {
            AjaxSetting setting = new AjaxSetting(RadComboBoxConsumerWAI.ID);
            setting.UpdatedControls.Add(new AjaxUpdatedControl(RadComboBoxAdditionalSupports.ID, RadAjaxLoadingPanel1WAI.ID));
            RadAjaxManagerBillableWAI.AjaxSettings.Add(setting);

            FieldsetAdditionalSupports.Style.Add("display", "block");
        }
        
        FieldsetClientInformation.Style.Add("display", "none");
        if (Privilege.isPrivTrue(iAccess, Privileges10.WITHIN_AR_CAP_SHOW_CLIENT_INFORMATION))
        {
            LabelClientInformation.Text = HiddenCONSUMER_TERMINOLOGY.Value + " Information";
            
            FieldsetClientInformation.Style.Add("display", "block");
        }

        if (!Privilege.isPrivTrue(iAccess, Privileges8.ALLOW_ADDING_PICTURES_BILLABLE_CAP))
        {
            RadTabStripBillableCAP.Tabs.FindTabByValue("RadTabConsumerFiles").Visible = false;
        }

        chkBoxFaceToFace.Visible = false;
        LabelCheckBoxFaceToFace.Visible = false;
        if (Privilege.isPrivTrue(iAccess, Privileges10.FACE_TO_FACE_ON_BILLABLE_CAP))
        {
            chkBoxFaceToFace.Visible = true;
            LabelCheckBoxFaceToFace.Visible = true;
        }
        TravelTimeHours.Visible = false;
        TravelTimeMinutes.Visible = false;
        LabelTravelTime.Visible = false;
        LabelTravelTimeHours.Visible = false;
        LabelTravelTimeMinutes.Visible = false;
        if (Privilege.isPrivTrue(iAccess, Privileges10.TRAVEL_TIME_ON_BILLABLE_CAP))
        {
            TravelTimeMinutes.Visible = true;
            TravelTimeHours.Visible = true;
            LabelTravelTime.Visible = true;
            LabelTravelTimeHours.Visible = true;
            LabelTravelTimeMinutes.Visible = true;
        }
        
        if(Privilege.isPrivTrue(iAccess, Privileges11.SHOW_ARGE_BUTTON_CAP))
        {
            ARGLIcon.Visible = true;
        }

        if (Privilege.isPrivTrue(iAccess, Privileges12.CAN_SEE_IN_CONTEXT_PRIVS))
        {
            InContextPrivContainer.Visible = true;
        }
        
        if (!Privilege.isPrivTrue(iAccess, Privileges14.ABLE_TO_EDIT_DATE_AND_TIME_ON_EDIT_AND_CONVERT_BILLABLE_INDIVIDUAL_AR))
        {
            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CONVERT_FROM_APPT) == 0 || HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
            {
                RadTimePickerFromWAI.Enabled = false;
                RadTimePickerToWAI.Enabled = false;
                DIVDatePicker.Style.Add("display", "block");
                RadTextBoxARDate.Text = RadSchedulerDayWAI.SelectedDate.ToString("D");
                RadSchedulerDayWAI.Visible = false;
                DIVUserNameWAI.Visible = false;
                DIVExpandCalendar.Visible = false;
            }
        }
        else
        {
            if(LinkButtonSaveAndDuplicate.Visible) ajaxify(LinkButtonSaveAndDuplicate.ID, RadSchedulerDayWAI.ID);
            if(LinkButtonUpdateAndDuplicate.Visible) ajaxify(LinkButtonUpdateAndDuplicate.ID, RadSchedulerDayWAI.ID);
            if(LinkButtonDuplicate.Visible) ajaxify(LinkButtonDuplicate.ID, RadSchedulerDayWAI.ID);
        }


        LabelFaceToFaceTime.Visible = false;
        LabelFaceToFaceTimeHours.Visible = false;
        FaceToFaceTimeHours.Visible = false;
        LabelFaceToFaceTimeMinutes.Visible = false;
        FaceToFaceTimeMinutes.Visible = false;
        if(Privilege.isPrivTrue(iAccess, Privileges14.DISPLAY_FACE_TO_FACE_TIME_ON_BILLABLE_CAP))
        {
            LabelFaceToFaceTime.Visible = true;
            LabelFaceToFaceTimeHours.Visible = true;
            FaceToFaceTimeHours.Visible = true;
            LabelFaceToFaceTimeMinutes.Visible = true;
            FaceToFaceTimeMinutes.Visible = true;
        }

        LabelRecordKeepingTime.Visible = false;
        LabelRecordKeepingTimeHours.Visible = false;
        RecordKeepingTimeHours.Visible = false;
        LabelRecordKeepingTimeMinutes.Visible = false;
        RecordKeepingTimeMinutes.Visible = false;
        if(Privilege.isPrivTrue(iAccess, Privileges14.DISPLAY_RECORD_KEEPING_TIME_ON_BILLABLE_CAP))
        {
            LabelRecordKeepingTime.Visible = true;
            LabelRecordKeepingTimeHours.Visible = true;
            RecordKeepingTimeHours.Visible = true;
            LabelRecordKeepingTimeMinutes.Visible = true;
            RecordKeepingTimeMinutes.Visible = true;
        }

        LabelMiles.Visible = false;
        Miles.Visible = false;
        if(Privilege.isPrivTrue(iAccess, Privileges14.DISPLAY_MILES_ON_BILLABLE_CAP))
        {
            LabelMiles.Visible = true;
            Miles.Visible = true;
        }

        LabelCollaborationTime.Visible = false;
        LabelCollaborationTimeHours.Visible = false;
        CollaborationTimeHours.Visible = false;
        LabelCollaborationTimeMinutes.Visible = false;
        CollaborationTimeMinutes.Visible = false;
        if(Privilege.isPrivTrue(iAccess, Privileges14.DISPLAY_COLLABORATION_TIME_ON_BILLABLE_CAP))
        {
            LabelCollaborationTime.Visible = true;
            LabelCollaborationTimeHours.Visible = true;
            CollaborationTimeHours.Visible = true;
            LabelCollaborationTimeMinutes.Visible = true;
            CollaborationTimeMinutes.Visible = true;
        }

        LabelTotalBillableTime.Visible = false;
        LabelTotalBillableTimeHours.Visible = false;
        TotalBillableTimeHours.Visible = false;
        LabelTotalBillableTimeMinutes.Visible = false;
        TotalBillableTimeMinutes.Visible = false;
        if (Privilege.isPrivTrue(iAccess, Privileges14.DISPLAY_TOTAL_BILLABLE_TIME_ON_BILLABLE_CAP))
        {
            LabelTotalBillableTime.Visible = true;
            LabelTotalBillableTimeHours.Visible = true;
            TotalBillableTimeHours.Visible = true;
            LabelTotalBillableTimeMinutes.Visible = true;
            TotalBillableTimeMinutes.Visible = true;
        }
        
        I18NHelper.I18NPage(this.Page);
        FieldStateManager.setFieldStates(getClientID(), "BILLABLE_ACTIVITY_RECORD_WITH_AI_KEYS", this.Page.Controls);
    }
    
    private bool validateFileAcknowledgement()
    {
        bool validated = false;
        List<string> consumer = new List<string>();
        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
        {
            consumer.Add(HiddenBILLABLECAPAR_CONSUMER_ID.Value);
        }
        else if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
        {
            consumer.Add(RadComboBoxConsumerWAI.SelectedValue);
        }
        if(consumer.Count == 0)
        {
            return false;
        }
        List<DSFile> dsFiles = new List<DSFile>(BOFileAcknowledgement.GetConsumerFilesThatNeedAcknowledgementByClientIDConsumerIDs(getClientID(), consumer));
        List<DSFile> filesNeedingAcknowledgement = new List<DSFile>();

        foreach (DSFile file in dsFiles)
        {
            DSFileAcknowledgement acknowledgement = BOFileAcknowledgement.GetFileAcknowledgementByFileIDUserID(getClientID(), getUserID(), file.fileID);
            
            if(acknowledgement == null)
            {
                filesNeedingAcknowledgement.Add(file);
            }
        }

        if (filesNeedingAcknowledgement.Count > 0)
        {
            string infoMessage = "The following files need to be acknowledged before you can edit this record: ";
            foreach (DSFile file in filesNeedingAcknowledgement)
            {
                infoMessage += "<br /> <br />" + BOConsumer.getConsumerNameById(getClientID(), Int32.Parse(file.getElementID)) + " - ";
                Button filePreviewBtn = new Button();
                filePreviewBtn.Text = string.IsNullOrEmpty(file.getFileDescription) ? file.getFileName : file.getFileDescription;
                filePreviewBtn.ID = "btnFilePreview" + file.fileID;
                filePreviewBtn.Attributes["runat"] = "server";
                filePreviewBtn.Style.Add("background-color", "transparent");
                filePreviewBtn.Style.Add("border", "none");
                filePreviewBtn.Style.Add("color", "#1e7ace");
                filePreviewBtn.Style.Add("text-decoration", "underline");
                filePreviewBtn.Style.Add("cursor", "pointer");
                filePreviewBtn.Attributes["onclick"] = "LoadFilePreviewAck(event, this, " + file.getElementID + "," + file.fileID.ToString() + "); return false;";
                
                StringWriter stringWriter = new StringWriter();
                HtmlTextWriter htmlWriter = new HtmlTextWriter(stringWriter);
                filePreviewBtn.RenderControl(htmlWriter);
                infoMessage +=  stringWriter;
            }

            Button refreshBtn = new Button();
            refreshBtn.Text = "Refresh";
            refreshBtn.ID = "btnRefresh";
            refreshBtn.Attributes["runat"] = "server";
            refreshBtn.Style.Add("background-color", "transparent");
            refreshBtn.Style.Add("border", "none");
            refreshBtn.Style.Add("color", "#1e7ace");
            refreshBtn.Style.Add("text-decoration", "underline");
            refreshBtn.Style.Add("cursor", "pointer");
            refreshBtn.OnClientClick = "refresh_OnClick()";
            refreshBtn.Attributes["AutoPostBack"] = "true";

            StringWriter refreshStringWriter = new StringWriter();
            HtmlTextWriter refreshHtmlWriter = new HtmlTextWriter(refreshStringWriter);
            refreshBtn.RenderControl(refreshHtmlWriter);
            infoMessage += "<br /> <br />" + refreshStringWriter;
            
            ajaxify(RadComboBoxConsumerWAI.ID, InformationDIV.ID);
            ajaxify(RadAjaxManagerBillableWAI.ID, InformationDIV.ID);
            setInformation(infoMessage);
        }
        else
        {
            validated = true;
            if(lblInformation.Text.Contains("The following files need to be acknowledged before you can edit this record:"))
            {
                ajaxify(RadAjaxManagerBillableWAI.ID, InformationDIV.ID);
                ajaxify(RadComboBoxConsumerWAI.ID, InformationDIV.ID);
                setInformation("");
                
            }
        }
        return validated;
    }

    private void CheckInitializationPrivs()
    {
        long[] iAccess = getUserPrivs();
        
        LIPlaceOfService.Visible = false;
        if (Privilege.isPriv6True(iAccess, Privileges6.PLACE_OF_SERVICE_IS_APPLICABLE))
        {
            LIPlaceOfService.Visible = true;
            ajaxify(LinkButtonUpdate.ID, RadComboBoxPlaceOfService.ID);
            ajaxify(LinkButtonSave.ID, RadComboBoxPlaceOfService.ID);
        }

        if (!Privilege.isPrivTrue(iAccess, Privileges10.SHOW_FILES_TAB_IN_ACTIVITY_RECORD))
        {
            RadTabFiles.Visible = false;
        }

        if (!Privilege.isPrivTrue(iAccess, Privileges10.SHOW_FORMS_TAB_IN_ACTIVITY_RECORD))
        {
            RadTabForms.Visible = false;
        }

        if (!Privilege.isPrivTrue(iAccess, Privileges10.SHOW_COMMUNICATIONS_IN_ACTIVITY_RECORD))
        {
            RadTabCommunications.Visible = false;
        }

        HiddenUSEUPDATEDMILEAGEWINDOW.Value = Privilege.isPrivTrue(iAccess, Privileges14.USE_THE_UPDATED_MILEAGE_WINDOW).ToString();
        
        if (Privilege.isPrivTrue(iAccess, Privileges14.USE_THE_UPDATED_MILEAGE_WINDOW) && 
            !Privilege.isPrivTrue(iAccess, Privileges16.SHOW_STAFF_MILEAGE) && 
            !Privilege.isPrivTrue(iAccess, Privileges16.SHOW_CONSUMER_MILEAGE))
        {
            ImageExpense.Visible = false;
        }
    }

    private void setSiteLabels()
    {
        DSSiteLabel[] dsSiteLabels = BOSiteLabel.getSiteLabelsByClientID(getClientID());
        string outcomesHeader = "";
        string goalsHeader = "";

        foreach (DSSiteLabel siteLabel in dsSiteLabels)
        {
            if (!siteLabel.getActive) continue;

            else if (siteLabel.getSiteLabelCode == "OUTCOMES_HEADER")
            {
                outcomesHeader = siteLabel.getSiteLabelDescription;
            }
            else if (siteLabel.getSiteLabelCode == "Outcome.Description")
            {
                HiddenOutcomeDescription.Value = siteLabel.getSiteLabelDescription;
            }
            else if (siteLabel.getSiteLabelCode == "Outcome.OutcomeDetailDescription")
            {
                HiddenOutcomeOutcomeDetailDescription.Value = siteLabel.getSiteLabelDescription;
            }
            else if (siteLabel.getSiteLabelCode == "Outcome.MethodsTechniquesStrategies")
            {
                HiddenOutcomeMethodsTechniquesStrategies.Value = siteLabel.getSiteLabelDescription;
            }
            else if (siteLabel.getSiteLabelCode == "Goal.ShortDescription")
            {
                HiddenGoalShortDescription.Value = siteLabel.getSiteLabelDescription;
            }
            else if (siteLabel.getSiteLabelCode == "Goal.LongDescription")
            {
                HiddenGoalLongDescription.Value = siteLabel.getSiteLabelDescription;
            }
            else if (siteLabel.getSiteLabelCode == "GOALS_HEADER")
            {
                goalsHeader = siteLabel.getSiteLabelDescription;
            }
            else if (siteLabel.getSiteLabelCode == "ARCAP_ADD_COMMENTS")
            {
                LabelAdditionalComments.Text = siteLabel.getSiteLabelDescription;
                lblAdditionalComments.Text = siteLabel.getSiteLabelDescription + ":";
            }
            else if (siteLabel.getSiteLabelCode == "ACTIVITYRECORDSGROUP_EMPLOYER")
            {
                HiddenACTIVITYRECORDSGROUP_EMPLOYERDescription.Value = siteLabel.getSiteLabelDescription;
            }
        }

        long[] iAccess = getUserPrivs();
        String NACheckControl = "";

        if (HiddenShowNAOption.Value.Equals("True"))
            NACheckControl = "<span id='SpanNACheck' style='display:none;font-size:14px;margin-left:10px;margin-right:6px;'><a href='#' onclick='javascript:checkAllNA();'>(Mark all " + outcomesHeader + " as N/A)</a><span id='LabelNASetInfo' style='color:#af0006;'></span></span>";

        if (Privilege.isPriv5True(iAccess, Privileges5.CAP_TYPE_USES_OUTCOME_DETAILS_AND_METHODS_FROM_OUTCOME))
        {
            if (outcomesHeader.Length > 0)
            {
                LabelOutcomesGoals.Text = outcomesHeader + NACheckControl;
            }
        }
        else
        {
            if (outcomesHeader.Length > 0)
            {
                LabelOutcomesGoals.Text = goalsHeader + NACheckControl;
            }
        }

    }

    public void initializePage()
    {
        long[] iAccess = getUserPrivs();
        if (Session[SessionDefinitions.AR_START_DATE_TIME] != null && Session[SessionDefinitions.AR_START_DATE_TIME].ToString().Length > 0)
        {
            updateRadSchedulerDayByDateWAI((DateTime)Session[SessionDefinitions.AR_START_DATE_TIME], HiddenCURRENTWINDOWUSERID.Value);
        }

        if (RadTimePickerFromWAI.SelectedDate == null || RadTimePickerToWAI.SelectedDate == null)
        {
            if (Session[SessionDefinitions.AR_START_DATE_TIME] != null && Session[SessionDefinitions.AR_START_DATE_TIME].ToString().Length > 0)
            {
                ActivityRecordsMisc.setActivityRecordHourSlotsToPreciseDateTime((DateTime)Session[SessionDefinitions.AR_START_DATE_TIME], RadNumericTextFromHours, RadNumericTextFromMinutes,
                    RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI,
                    iAccess, HiddenACTION_TYPEWAI.Value, getUserID(), LabelARSlotTimePreferenceWAI);
            }
            else
            {
                ActivityRecordsMisc.changeTimeToOnTheHourInTimePickers(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes,
                    RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, getClientID().ToString(), getUserID(), LabelARSlotTimePreferenceWAI, iAccess);
            }
        }

        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
        {
            if (HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value != null && HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value.Length > 0)
            {
                if (Session[SessionDefinitions.AR_START_DATE_TIME] != null && Session[SessionDefinitions.AR_START_DATE_TIME].ToString().Length > 0)
                    setupActivityRecordWAI((DateTime)Session[SessionDefinitions.AR_START_DATE_TIME], HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value);
                else
                    setupActivityRecordWAI(DateTime.UtcNow, HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value);
            }
            else
            {
                if (Session[SessionDefinitions.AR_START_DATE_TIME] != null && Session[SessionDefinitions.AR_START_DATE_TIME].ToString().Length > 0)
                    setupActivityRecordWAI((DateTime)Session[SessionDefinitions.AR_START_DATE_TIME], null);
                else
                    setupActivityRecordWAI(DateTime.UtcNow, null);
            }

            if (Session[SessionDefinitions.AR_START_DATE_TIME] != null && Session[SessionDefinitions.AR_START_DATE_TIME].ToString().Length > 0)
                updateRadSchedulerDayByDateWAI((DateTime)Session[SessionDefinitions.AR_START_DATE_TIME], HiddenCURRENTWINDOWUSERID.Value);
            else
                updateRadSchedulerDayByDateWAI(DateTime.UtcNow, HiddenCURRENTWINDOWUSERID.Value);
        }

        


        TimeWithScrolling.Visible = false;
        if (Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_SHOW_NUMERIC_TIME_SELECTION))
        {
            TimeWithScrolling.Visible = true;
        }
        else
        {
            TimeWithTimePicker.Visible = true;
        }

        RadTimePickerFromConsumerAtWork.EnableTyping = false;
        RadTimePickerFromConsumerAtWork.TimeView.StartTime = new TimeSpan();
        RadTimePickerFromConsumerAtWork.TimeView.EndTime = new TimeSpan(23, 59, 0);

        RadTimePickerFromConsumerAtWork.TimeView.Interval = new TimeSpan(0, 15, 0);
        RadTimePickerFromConsumerAtWork.TimeView.Columns = 4;
        RadTimePickerFromConsumerAtWork.PopupDirection = DatePickerPopupDirection.BottomRight;

        RadTimePickerToConsumerAtWork.EnableTyping = false;
        RadTimePickerToConsumerAtWork.TimeView.StartTime = new TimeSpan();
        RadTimePickerToConsumerAtWork.TimeView.EndTime = new TimeSpan(23, 59, 0);

        RadTimePickerToConsumerAtWork.TimeView.Interval = new TimeSpan(0, 15, 0);
        RadTimePickerToConsumerAtWork.TimeView.Columns = 4;
        RadTimePickerToConsumerAtWork.PopupDirection = DatePickerPopupDirection.BottomLeft;

        if (Privilege.isPriv6True(iAccess, Privileges6.CAP_RECORDS_CAN_TYPE_IN_TIME_BOXES_ALWAYS))
        {
            RadTimePickerFromConsumerAtWork.EnableTyping = true;
            RadTimePickerToConsumerAtWork.EnableTyping = true;
        }

        DSActivityRecordAdditional dsARAdditional = new DSActivityRecordAdditional();
        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
        {
            dsARAdditional = BOActivityRecordAdditional.getActivityRecordAdditional(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getClientID().ToString());
        }
        setLate(dsARAdditional);
    }

    #region UI-Managers
    protected void RadComboBoxDepartmentWAI_OnSelectedIndexChanged(object o, RadComboBoxSelectedIndexChangedEventArgs e)
    {
        if (RadComboBoxDepartmentWAI.Text.Length == 0)
            return;

        if (RadComboBoxDepartmentWAI.SelectedValue == null || RadComboBoxDepartmentWAI.SelectedValue.Length == 0)
        {
            return;
        }

        lblPlacementWAI.Visible = false;
        RadComboBoxPlacementWAI.Visible = false;

        RadComboBoxConsumerWAI.Items.Clear();
        RadComboBoxAuthorizationWAI.Items.Clear();

        // if ALL departments are selected the ID is 0 and we get all consumers for that clientID and department = 0

        RadComboBoxAuthorizationWAI.Text = "";

        long[] iAccessCoverage = getUserPrivs();
        if (Privilege.isPriv1True(iAccessCoverage, Privileges.FORCE_COVERAGE_FOR_CONSUMER_ACCESS))
        {
            BOConsumer.GetConsumersDetailedByDepartmentIDAndClientIDWithCoverageByUserIDActiveOnly(RadComboBoxDepartmentWAI.SelectedValue, HiddenCURRENTWINDOWCLIENTID.Value, HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
        }
        else
        {
            BOConsumer.GetConsumersByDepartmentIDAndClientIDAndStaffIDWithBlankActiveOnly(Int32.Parse(RadComboBoxDepartmentWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
        }
        RadComboBoxConsumerWAI.Text = "";

        HiddenCURRENTWINDOWSELECTEDDEPARTMENTID.Value = RadComboBoxDepartmentWAI.SelectedValue;

        setError("");
    }

    protected void RadComboBoxConsumerWAI_OnSelectedIndexChanged(object o, RadComboBoxSelectedIndexChangedEventArgs e)
    {
		long[] iAccess = getUserPrivs();

        HiddenBILLABLECAPAR_CONSUMER_ID.Value = RadComboBoxConsumerWAI.SelectedValue;
        RadComboBoxPlacementWAI.Visible = false;
        lblPlacementWAI.Visible = false;
        
        validateFileAcknowledgement();

        if (RadComboBoxConsumerWAI.Text.Length == 0)
        {
            RadComboBoxAuthorizationWAI.Items.Clear();
            return;
        }
        RadComboBoxAuthorizationWAI.Text = "";

        if (!string.IsNullOrEmpty(RadComboBoxConsumerWAI.SelectedValue))
        {
            bool disableAuthPriv = Privilege.isPriv1True(iAccess, Privileges.DISABLE_AUTHORIZATION_WHEN_HOURS_EXCEEDED);

            if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_LOGGING_ON_ADDITIONAL_SUPPORTS))
            {
                RadComboBoxAdditionalSupports.DataBind();
            }

            if (Privilege.isPrivTrue(iAccess, Privileges10.WITHIN_AR_CAP_SHOW_CLIENT_INFORMATION))
            {
                DSConsumer dsConsumer = BOConsumer.GetConsumerDetail(Int32.Parse(RadComboBoxConsumerWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                showClientInformation(iAccess, RadComboBoxConsumerWAI.SelectedValue, dsConsumer);
            }

            var authorizations = new List<AvailableAuthorizationsDTO>();

            if (int.Parse(RadComboBoxDepartmentWAI.SelectedValue) == 0)
            {
                List<int> departmentIDList = new List<int>();
                for (int i = 0; i < RadComboBoxDepartmentWAI.Items.Count; i++)
                {
                    departmentIDList.Add(Int32.Parse(RadComboBoxDepartmentWAI.Items[i].Value));
                }
                
                authorizations = AuthorizationsService.GetAvailableAuthorizationsForConsumer(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), 
                    int.Parse(RadComboBoxConsumerWAI.SelectedValue), int.Parse(RadComboBoxDepartmentWAI.SelectedValue), HiddenCURRENTWINDOWUSERID.Value, RadSchedulerDayWAI.SelectedDate, departmentIDList);
            }
            else
            { 
                authorizations = AuthorizationsService.GetAvailableAuthorizationsForConsumer(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), 
                    int.Parse(RadComboBoxConsumerWAI.SelectedValue), int.Parse(RadComboBoxDepartmentWAI.SelectedValue), HiddenCURRENTWINDOWUSERID.Value, RadSchedulerDayWAI.SelectedDate, null);

            }

            RadComboBoxAuthorizationWAI.DataSource = authorizations;
            RadComboBoxAuthorizationWAI.DataValueField = "AuthID";
            RadComboBoxAuthorizationWAI.DataTextField = "Code";
            
            RadComboBoxAuthorizationWAI.DataBind();
            
            if (Privilege.isPriv6True(iAccess, Privileges6.ENABLE_SERVICE_PHASES))
            {
                RadComboBoxIndividualServicePhase.Text = "";
                RadComboBoxIndividualServicePhase.ClearSelection();
                RadComboBoxIndividualServicePhase.DataBind();
            }
            
            if(RadComboBoxAuthorizationWAI.Items.Count == 1)
            {
                RadComboBoxAuthorizationWAI.SelectedIndex = 0;
                DSAuthorization auth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                HiddenShowNAOption.Value = auth.getAllowSelectNAForAllGoalsButton.ToString();
                if (HiddenShowNAOption.Value.Equals("True"))
                {
                    String NACheckControl = "";
                    NACheckControl = "<span id='SpanNACheck' style='display:none;font-size:14px;margin-left:10px;margin-right:6px;'><a href='#' onclick='javascript:checkAllNA();'>(Mark all as N/A)</a><span id='LabelNASetInfo' style='color:#af0006;'></span></span>";
                    LabelOutcomesGoals.Text += NACheckControl;

                }
                int serviceID = auth.getServiceID;
                DSService service = BOService.getServiceByServiceIDAndClientID(serviceID, getClientID());
                if (!String.IsNullOrEmpty(service.getDefaultContent))
                {
                    RadTextBoxAdditionalComments.Text = service.getDefaultContent;
                }

                if (!String.IsNullOrEmpty(service.GetEmptyContent))
                {
                    RadTextBoxAdditionalComments.EmptyMessage = service.GetEmptyContent;
                }
                else
                {
                    RadTextBoxAdditionalComments.EmptyMessage = "Daily notes should include a complete accounting of the appointment including activities, goals worked on, places traveled, and any additional comments or concerns.";
                }
            }
        }

		ActivityRecordsMisc.processAuthorizationToDetermineNeedForPlacementSelectionOnlyActive(lblPlacementWAI, RadComboBoxPlacementWAI, RadTimePickerFromWAI, RadSchedulerDayWAI, RadComboBoxConsumerWAI);
        
        deleteActivityRecordsGroup_Locations();
        SurveyGoalsWithEvaluations.Controls.Clear();
        AutoReviewAdditionalCommentsPanel.Controls.Clear();
        rebuildDynamicControlsWithCurrentSelection(false);
        setError("");

        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
        {
            // keep the calendar on the opened AR person's window
            updateRadSchedulerDayByDateWAI(RadSchedulerDayWAI.SelectedDate, HiddenBILLABLECAPAR_CREATOR_USER_ID.Value);
        }
        else
        {
            updateRadSchedulerDayByDateWAI(RadSchedulerDayWAI.SelectedDate, HiddenCURRENTWINDOWUSERID.Value);
        }
        ActivityRecordsMisc.updateLabelUserName(LabelUserNameWAI, RadComboBoxConsumerWAI, HiddenACTION_TYPEWAI, HiddenCURRENTWINDOWUSERID, HiddenBILLABLECAPAR_CREATOR_USER_ID);

        FieldsetAuthorizationInformation.Visible = false;
    }

    protected void RadComboBoxAuthorizationWAI_OnSelectedIndexChanged(object o, RadComboBoxSelectedIndexChangedEventArgs e)
    {
        setError("");
        SurveyGoalsWithEvaluations.Controls.Clear();
        AutoReviewAdditionalCommentsPanel.Controls.Clear();
        
        DSAuthorization dsAuth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), getClientID());
        HiddenShowNAOption.Value = dsAuth.getAllowSelectNAForAllGoalsButton.ToString();

        setSiteLabels();
        rebuildDynamicControlsWithCurrentSelection(false);

        long[] iAccess = getUserPrivs();
        if (useSingleRecordPerDayPrivOrForClient_CO(iAccess, RadSchedulerDayWAI.SelectedDate))
        {
            // determine if there already is a min primary AR For this instance
            DateTime fromDateRange = new DateTime(RadSchedulerDayWAI.SelectedDate.Year, RadSchedulerDayWAI.SelectedDate.Month, RadSchedulerDayWAI.SelectedDate.Day, 0, 0, 0);
            openActivityRecordPopulateKeysAndGoalComments(getMinARIDPrimary(RadComboBoxConsumerWAI.SelectedValue, RadComboBoxAuthorizationWAI.SelectedValue, HiddenCURRENTWINDOWUSERID.Value, fromDateRange).ToString());
        }

		ActivityRecordsMisc.processAuthorizationToDetermineNeedForPlacementSelectionOnlyActive(lblPlacementWAI, RadComboBoxPlacementWAI, RadTimePickerFromWAI, RadSchedulerDayWAI, RadComboBoxConsumerWAI);

		if (Privilege.isPriv6True(iAccess, Privileges6.ENABLE_SERVICE_PHASES))
        {
            RadComboBoxIndividualServicePhase.Text = "";
            RadComboBoxIndividualServicePhase.ClearSelection();
            RadComboBoxIndividualServicePhase.DataBind();
        }

        int serviceID = dsAuth.getServiceID;
        DSService service = BOService.getServiceByServiceIDAndClientID(serviceID, getClientID());
        if (!String.IsNullOrEmpty(service.getDefaultContent))
        {
            RadTextBoxAdditionalComments.Text = service.getDefaultContent;
        }

        if (!String.IsNullOrEmpty(service.GetEmptyContent))
        {
            RadTextBoxAdditionalComments.EmptyMessage = service.GetEmptyContent;
        }
        else
        {
            RadTextBoxAdditionalComments.EmptyMessage = "Daily notes should include a complete accounting of the appointment including activities, goals worked on, places traveled, and any additional comments or concerns.";
        }

        processNightAttendanceCheckbox(iAccess, dsAuth.getDepartmentID);

        if (Privilege.isPrivTrue(iAccess, Privileges8.SHOW_AUTH_FREQUENCY_DESCRIPTION_IN_AR))
        {
            if (RadComboBoxAuthorizationWAI.SelectedValue != "")
            {
                FieldsetAuthorizationInformation.Visible = true;
                LabelFrequencyDescription.Text = dsAuth.getFrequencyDescription;
            }
        }
    }

    protected void RadSchedulerDayWAIOnNavigationComplete(object sender, EventArgs e)
    {
        long[] iAccess = getUserPrivs();

        String authSelectedValue = RadComboBoxAuthorizationWAI.SelectedValue;
        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
        {
            // keep the calendar on the opened AR person's window
            updateRadSchedulerDayByDateWAI(RadSchedulerDayWAI.SelectedDate, HiddenBILLABLECAPAR_CREATOR_USER_ID.Value);
            // get the latest goals, and auth stuff

            RadComboBoxAuthorizationWAI.Items.Clear();
            RadComboBoxAuthorizationWAI.Text = "";
            if (RadComboBoxConsumerWAI.Text.Length > 0)
            {
                // For Auths
                if (RadComboBoxConsumerWAI.SelectedValue.Length != 0)
                {
                    bool disableAuthPriv = false;
                    if (Privilege.isPriv1True(iAccess, Privileges.DISABLE_AUTHORIZATION_WHEN_HOURS_EXCEEDED))
                    {
                        disableAuthPriv = true;
                    }

                    BOAuthorization.getElligibleAuthorizationsByConsumerIDWithHoursWithoutTCM(System.Convert.ToInt32(RadComboBoxConsumerWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), RadComboBoxAuthorizationWAI, RadSchedulerDayWAI.SelectedDate, "", disableAuthPriv, RadComboBoxDepartmentWAI.SelectedValue, HiddenCURRENTWINDOWUSERID.Value, iAccess);
                }
				
				ActivityRecordsMisc.processAuthorizationToDetermineNeedForPlacementSelectionOnlyActive(lblPlacementWAI, RadComboBoxPlacementWAI, RadTimePickerFromWAI, RadSchedulerDayWAI, RadComboBoxConsumerWAI);
			}
        }
        else
        {
            updateRadSchedulerDayByDateWAI(RadSchedulerDayWAI.SelectedDate, HiddenCURRENTWINDOWUSERID.Value);
            setupActivityRecordWAI(RadSchedulerDayWAI.SelectedDate, RadComboBoxConsumerWAI.SelectedValue);
        }
        
        if (RadComboBoxAuthorizationWAI.Items.FindItemByValue(authSelectedValue) != null)
        {
            RadComboBoxAuthorizationWAI.SelectedValue = authSelectedValue;
            // check here to see if the goals are different, if the same goals, don't clear and don't rebuild, otherwise do

            if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
            {

                // remove the current AR goals, add in all the new selections
                Hashtable uniqueGoalIDs = new Hashtable();

                for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
                {
                    String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
                    if (foundControlID == null)
                        continue;

                    if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
                    {
                        //                    System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[i];
                        Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[i];
                        String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                        if (!uniqueGoalIDs.ContainsKey(goalID))
                        {
                            uniqueGoalIDs.Add(goalID, goalID);
                        }
                    }
                }
                for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
                {
                    String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
                    if (foundControlID == null)
                        continue;

                    if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("System.Web.UI.WebControls.CheckBox") == 0)
                    {
                        System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[i];
                        //                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[i];

                        String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                        if (!uniqueGoalIDs.ContainsKey(goalID))
                        {
                            uniqueGoalIDs.Add(goalID, goalID);
                        }
                    }
                }

                bool goalsMatch = true;
                DSGoal[] goals = BOGoal.getElligibleGoalsByConsumerIDAndAuthID(getClientID(), Int32.Parse(RadComboBoxConsumerWAI.SelectedValue), Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), RadSchedulerDayWAI.SelectedDate, "1");
                for (int i = 0; i < goals.Length; i++)
                {
                    String goalID = goals[i].getGoalID.ToString();
                    if (uniqueGoalIDs.Contains(goalID))
                    {
                        continue;
                    }
                    else
                    {
                        goalsMatch = false;
                        break;
                    }
                }

                foreach (object userGoalKey in uniqueGoalIDs)
                {
                    bool goalFound = false;
                    for (int i = 0; i < goals.Length; i++)
                    {
                        String goalID = goals[i].getGoalID.ToString();
                        if (goalID.CompareTo(((DictionaryEntry)userGoalKey).Key) == 0)
                        {
                            goalFound = true;
                        }
                    }

                    if (!goalFound)
                    {
                        goalsMatch = false;
                        break;
                    }
                }

                if (!goalsMatch)
                {
                    SurveyGoalsWithEvaluations.Controls.Clear();
                    AutoReviewAdditionalCommentsPanel.Controls.Clear();
                    rebuildDynamicControlsWithCurrentSelection(false);
                }
            }
            else
            {
                RadComboBoxAuthorizationWAI.SelectedValue = authSelectedValue;
                SurveyGoalsWithEvaluations.Controls.Clear();
                AutoReviewAdditionalCommentsPanel.Controls.Clear();
                rebuildDynamicControlsWithCurrentSelection(false);
            }

            if (useSingleRecordPerDayPrivOrForClient_CO(iAccess, RadSchedulerDayWAI.SelectedDate))
            {
                // determine if there already is a min primary AR For this instance
                DateTime fromDateRange = new DateTime(RadSchedulerDayWAI.SelectedDate.Year, RadSchedulerDayWAI.SelectedDate.Month, RadSchedulerDayWAI.SelectedDate.Day, 0, 0, 0);
                openActivityRecordPopulateKeysAndGoalComments(getMinARIDPrimary(RadComboBoxConsumerWAI.SelectedValue, RadComboBoxAuthorizationWAI.SelectedValue, HiddenCURRENTWINDOWUSERID.Value, fromDateRange).ToString());
            }


        }
        else
        {
            SurveyGoalsWithEvaluations.Controls.Clear();
            AutoReviewAdditionalCommentsPanel.Controls.Clear();
            rebuildDynamicControlsWithCurrentSelection(false);
        }

        setLate(null);

    }

    protected void RadSchedulerDayWAIOnAppointmentCreated(object sender, AppointmentCreatedEventArgs e)
    {
        ActivityRecordsScheduler.RadScheduler_OnAppointmentCreated(sender, e, getUserID(), getClientID(), getUserPrivs(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
    }

    protected void RadioButtonListFromAMPM_OnSelectedIndexChanged(object sender, EventArgs e)
    {
        ActivityRecordsMisc.RadioButtonistFromAMPMSelectedIndexChanged(RadioButtonListFromAMPM, RadNumericTextFromHours);
    }

    protected void RadioButtonListToAMPM_OnSelectedIndexChanged(object sender, EventArgs e)
    {
        ActivityRecordsMisc.RadioButtonistFromAMPMSelectedIndexChanged(RadioButtonListToAMPM, RadNumericTextToHours);
    }

    protected void RadTimePickerToWAI_SelectedDateChanged(object sender, SelectedDateChangedEventArgs e)
    {
        setLate(null);
    }

    private void updateRadSchedulerDayByDateWAI(DateTime _date, String _userID)
    {
        HiddenACTIVITYRECORDQUERYFROMDATE.Value = _date.ToShortDateString();
        HiddenACTIVITYRECORDQUERYTODATE.Value = _date.AddDays(1).ToShortDateString();
        HiddenARUSERID.Value = _userID;
        RadSchedulerDayWAI.SelectedDate = _date;
        RadSchedulerDayWAI.DataBind();
    }

    protected void LinkButtonSave_OnClick(object sender, EventArgs e)
    {
        if (!isValidatorsValid() && !validateFileAcknowledgement())
            return;

        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);

        expandOrCollapseDocumentedOutcomes();
        LinkButtonSave.Enabled = false;
        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonSave.Enabled = true;
                return;
            }
        }

        bool saveCheck = checkForSave();

        if (saveCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value))
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
        RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value)
        && isAbleToCreateConsumerWorkActivityRecord() 
        && ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadTimePickerFromWAI.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID())
        && ValidateFormRequirement())
        {
            if (!createConsumerWorkActivityRecord())
            {
                return;
            }

            setError("Successfully created new CAP Activity Record at " + RadTimePickerFromWAI.SelectedDate.Value.ToShortTimeString());
            if (createActivityRecordWAI())
            {
                if (!HiddenAUTOSAVEID.Value.Equals("False"))
                    BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
                RadAjaxManagerBillableWAI.ResponseScripts.Add("window.CloseOrRedirect();");
            }
            else
            {
                // remove consumer at work record
                if (HiddenCONSUMERATWORKACTIVITYRECORDID != null && HiddenCONSUMERATWORKACTIVITYRECORDID.Value != null && HiddenCONSUMERATWORKACTIVITYRECORDID.Value != "0")
                {
                    BOActivityRecord.fncDeleteActivityRecordByActivityRecordIDAndClientID(HiddenCONSUMERATWORKACTIVITYRECORDID.Value, getClientID().ToString(), getUserID(), getUsername());
                }
            }
            
        }
        else
        {
            setInformationLabelVisibility(false);
            setError(true);
        }

        LinkButtonSave.Enabled = true;

    }

    protected void LinkButtonUpdate_OnClick(object sender, EventArgs e)
    {
        expandOrCollapseDocumentedOutcomes();
        if (!isValidatorsValid() || !validateFileAcknowledgement())
            return;


        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);
        LinkButtonUpdate.Enabled = false;

        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonUpdate.Enabled = true;
                return;
            }
        }

        if (!isAbleToCreateConsumerWorkActivityRecord())
        {
            return;
        }

        bool arWithinDateRange = ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value);

        bool saveCheck = checkForSave();

        if (saveCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!arWithinDateRange)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
                RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value) &&
            ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadSchedulerDayWAI.SelectedDate, ref lblStatusUpdateWAI, getClientID())
            && ValidateFormRequirement())
        {
            if (!createConsumerWorkActivityRecord())
            {
                return;
            }

            if (saveActivityRecordWAI())
            {
                if (!HiddenAUTOSAVEID.Value.Equals("False"))
                    BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
                
                RadAjaxManagerBillableWAI.ResponseScripts.Add("window.CloseOrRedirect();");

            }
        }
        else
        {
            setInformationLabelVisibility(false);
            setError(true);
        }
        LinkButtonUpdate.Enabled = true;

    }

    protected void LinkButtonClose_Click(object sender, EventArgs e)
    {
        if (!HiddenAUTOSAVEID.Value.Equals("False"))
        {
            BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
        }

        if (string.IsNullOrEmpty(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value))
        {
            BOForms.deleteFormResultAndAllAssociatedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUsername(), Guid.Parse(getUserID()));

            deleteTempSignatures();
            deleteActivityRecordsGroup_Locations();
            
        }
        RadAjaxManagerBillableWAI.ResponseScripts.Add("window.CloseOrRedirect();");
    }

    protected void LinkButtonStaffSaveAndSign_Click(object sender, EventArgs e)
    {
        expandOrCollapseDocumentedOutcomes();
        if (!isValidatorsValid() || !validateFileAcknowledgement())
            return;


        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);
        LinkButtonUpdate.Enabled = false;

        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonUpdate.Enabled = true;
                return;
            }
        }


        bool saveCheck = checkForSave();

        if (saveCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value))
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
        RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value) 
        && isAbleToCreateConsumerWorkActivityRecord()
        && ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadTimePickerFromWAI.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID())
        && ValidateFormRequirement())
        {
            HiddenSIGNROLE.Value = "STAFF";
            ValidatePinControl.Show();
        }
        else
        {
            setInformationLabelVisibility(false);
            setError(true);
        }
        LinkButtonUpdate.Enabled = true;
    }

    protected void LinkButtonManagerSaveAndSign_Click(object sender, EventArgs e)
    {
        bool acknowledgeCheck = validateFileAcknowledgement();
        expandOrCollapseDocumentedOutcomes();
        if (!isValidatorsValid() || !acknowledgeCheck)
            return;


        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);
        LinkButtonUpdate.Enabled = false;

        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonUpdate.Enabled = true;
                return;
            }
        }
        
        bool saveCheck = checkForSave();

        if (saveCheck && acknowledgeCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value))
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
        RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value)
        && isAbleToCreateConsumerWorkActivityRecord()
        && ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadTimePickerFromWAI.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID())
        && ValidateFormRequirement())
        {
            HiddenSIGNROLE.Value = "MANAGER";
            ValidatePinControl.Show();
        }
        else
        {
            setInformationLabelVisibility(false);
            setError(true);
        }
        LinkButtonUpdate.Enabled = true;
    }

    protected void LinkButtonSaveAndDuplicate_Click(object sender, EventArgs e)
    {
        if (!isValidatorsValid() || !validateFileAcknowledgement())
        {
            return;
        }
        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);

        expandOrCollapseDocumentedOutcomes();
        LinkButtonSave.Enabled = false;
        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonSave.Enabled = true;
                return;
            }
        }

        bool saveCheck = checkForSave();

        if (saveCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value))
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
        RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value)
        && isAbleToCreateConsumerWorkActivityRecord()
        && ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadTimePickerFromWAI.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID())
        && ValidateFormRequirement())
        {
            //If we fail to create a consumer work record, return
            if (!createConsumerWorkActivityRecord())
            {
                return;
            }

            setError("Successfully created new CAP Activity Record at " + RadTimePickerFromWAI.SelectedDate.Value.ToShortTimeString());
            //If we successfully create an activity record
            if (createActivityRecordWAI())
            {
                //if the activity record has a autosave, delete the autosave
                if (!HiddenAUTOSAVEID.Value.Equals("False"))
                {
                    BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
                }
                //duplicate record by clearing UI and reloading the auths
                else
                {
                    duplicateActivityRecordWAI();
                    setInformation("The record has been saved and duplicated. Note that comments and some other fields are not duplicated/copied over.");
                }
            }
            else
            {
                // remove consumer at work record if we don't successfully create an activity record
                if (HiddenCONSUMERATWORKACTIVITYRECORDID != null && HiddenCONSUMERATWORKACTIVITYRECORDID.Value != null && HiddenCONSUMERATWORKACTIVITYRECORDID.Value != "0")
                {
                    BOActivityRecord.fncDeleteActivityRecordByActivityRecordIDAndClientID(HiddenCONSUMERATWORKACTIVITYRECORDID.Value, getClientID().ToString(), getUserID(), getUsername());
                }
            }

        }
        else
        {
            setInformationLabelVisibility(false);
            setError(true);
        }

        LinkButtonSave.Enabled = true;

    }

    protected void LinkButtonUpdateAndDuplicate_Click(object sender, EventArgs e)
    {
        expandOrCollapseDocumentedOutcomes();
        if (!isValidatorsValid() || !validateFileAcknowledgement())
            return;


        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);
        LinkButtonUpdate.Enabled = false;

        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonUpdate.Enabled = true;
                return;
            }
        }

        if (!isAbleToCreateConsumerWorkActivityRecord())
        {
            return;
        }

        
        
        bool saveCheck = checkForSave();

        if (saveCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value))
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
                     RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value) &&
                 ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadTimePickerFromWAI.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID())
                 && ValidateFormRequirement())
        { 
            
            if (!createConsumerWorkActivityRecord())
            {
                  return;
            }
            
            if (saveActivityRecordWAI())
            {
                if (!HiddenAUTOSAVEID.Value.Equals("False"))
                    BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
                
                    HiddenACTION_TYPEWAI.Value = Constants.CREATE;
                    duplicateActivityRecordWAI();
                    LinkButtonUpdate.Visible = false;
                    UpdateListItem.Visible = false;
                    LinkButtonStaffUpdateAndSign.Visible = false;
                    UpdateAndSignListItem.Visible = false;
                    LinkButtonManagerUpdateAndSign.Visible = false;
                    UpdateAndManagerSignListItem.Visible = false;
                    LinkButtonUpdateAndDuplicate.Visible = false;
                    UpdateAndDuplicateListItem.Visible = false;
                    LinkButtonDuplicate.Visible = false;
                    DuplicateListItem.Visible = false;
                    LinkButtonSaveAndDuplicate.Visible = true;
                    SaveAndDuplicateListItem.Visible = true;
                    chkBoxCreateConsumerAtWorkRecord.Visible = true;
                    lblCheckBoxCreateConsumerWork.Visible = true;

                    if (Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_SIGN_AND_UPDATE_AND_SIGN_ON_BILLABLE_CAP))
                    {
                        LinkButtonStaffSaveAndSign.Visible = true;
                        SaveAndSignListItem.Visible = true;
                        LinkButtonManagerSaveAndSign.Visible = true;
                        SaveAndSignManagerListItem.Visible = true;
                    }
                    if (Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_UPDATE_ON_BILLABLE_CAP))
                    {
                        LinkButtonSave.Visible = true;
                        SaveListItem.Visible = true;
                    }
                    setInformation("The record has been updated and duplicated. Note that comments and some other fields are not duplicated/copied over.");
                }

            
        }
        else
        {
            setInformationLabelVisibility(false);
            setError(true);
        }
        LinkButtonUpdate.Enabled = true;
    }

    protected void LinkButtonDuplicate_Click(object sender, EventArgs e)
    {
        long[] iAccess = getUserPrivs();
        HiddenACTION_TYPEWAI.Value = Constants.CREATE;
        duplicateActivityRecordWAI();
        
        LinkButtonUpdate.Visible = false;
        UpdateListItem.Visible = false;
        
        LinkButtonStaffUpdateAndSign.Visible = false;
        UpdateAndSignListItem.Visible = false;
        
        LinkButtonManagerUpdateAndSign.Visible = false;
        UpdateAndManagerSignListItem.Visible = false;
        
        LinkButtonUpdateAndDuplicate.Visible = false;
        UpdateAndDuplicateListItem.Visible = false;
        
        LinkButtonDuplicate.Visible = false;
        DuplicateListItem.Visible = false;
        
        LinkButtonSaveAndDuplicate.Visible = true;
        SaveAndDuplicateListItem.Visible = true;
        
        chkBoxCreateConsumerAtWorkRecord.Visible = true;
        lblCheckBoxCreateConsumerWork.Visible = true;

        if (Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_SIGN_AND_UPDATE_AND_SIGN_ON_BILLABLE_CAP))
        {
            LinkButtonStaffSaveAndSign.Visible = true;
            SaveAndSignListItem.Visible = true;
        }
        if (Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_UPDATE_ON_BILLABLE_CAP))
        {
            LinkButtonSave.Visible = true;
            SaveListItem.Visible = true;
        }
        setInformation("The record has been duplicated. Note that comments and some other fields are not duplicated/copied over.");
        
    }

    protected void LinkButtonStaffUpdateAndSign_Click(object sender, EventArgs e)
    {
        expandOrCollapseDocumentedOutcomes();
        if (!isValidatorsValid() || !validateFileAcknowledgement())
            return;


        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours, RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);
        LinkButtonUpdate.Enabled = false;

        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonUpdate.Enabled = true;
                return;
            }
        }

        if (!isAbleToCreateConsumerWorkActivityRecord())
        {
            return;
        }
        
        if (!createConsumerWorkActivityRecord())
        {
            return;
        }

        bool saveCheck = checkForSave();

        if (saveCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value))
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
    RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value)&&
            ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadTimePickerFromWAI.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID())
            && ValidateFormRequirement())
        {
            HiddenSIGNROLE.Value = "STAFF";
            ValidatePinControl.Show();
        }
        else
        {
            setInformationLabelVisibility(false);

            setError(true);
        }
        LinkButtonUpdate.Enabled = true;
    }

    protected void LinkButtonManagerUpdateAndSign_Click(object sender, EventArgs e)
    {
        expandOrCollapseDocumentedOutcomes();
        if (!isValidatorsValid() || !validateFileAcknowledgement())
            return;


        long[] iAccess = getUserPrivs();
        ActivityRecordsMisc.updateDateTimePickerBasedOnNumericSelection(RadNumericTextFromHours, RadNumericTextFromMinutes, RadioButtonListFromAMPM, RadNumericTextToHours,
            RadNumericTextToMinutes, RadioButtonListToAMPM, RadSchedulerDayWAI, RadTimePickerFromWAI, RadTimePickerToWAI, iAccess, HiddenACTION_TYPEWAI.Value);
        LinkButtonUpdate.Enabled = false;

        if (!Privilege.isPriv3True(iAccess, Privileges3.ACTIVITY_RECORDS_IGNORE_OVERLAP_AND_AVAILABILITY))
        {
            if (!isActivityRecordConsumerAvailableAndOverlapCheckWAI())
            {
                LinkButtonUpdate.Enabled = true;
                return;
            }
        }

        if (!isAbleToCreateConsumerWorkActivityRecord())
        {
            return;
        }

        if (!createConsumerWorkActivityRecord())
        {
            return;
        }

        bool saveCheck = checkForSave();

        if (saveCheck)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (!ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(RadTimePickerFromWAI, RadSchedulerDayWAI, RadTimePickerToWAI, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), true, "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value))
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else if (ActivityRecordsAuth.isAuthLimitExceededByHoursAttempted(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value, HiddenACTION_TYPEWAI.Value, iAccess, RadTimePickerFromWAI,
    RadTimePickerToWAI, RadSchedulerDayWAI, RadComboBoxAuthorizationWAI, lblStatusUpdateWAI, HiddenTRANSFORMATION.Value) &&
            ActivityRecordsMisc.isElligibleForCreationGivenRequiredBillingExpirationFields(HiddenCURRENTWINDOWUSERID.Value, RadTimePickerFromWAI.SelectedDate.Value, ref lblStatusUpdateWAI, getClientID())
            && ValidateFormRequirement())
        {
            HiddenSIGNROLE.Value = "MANAGER";
            ValidatePinControl.Show();
        }
        else
        {
            setInformationLabelVisibility(false);
            setError(true);
        }
        LinkButtonUpdate.Enabled = true;
    }

    protected void CreateConsumerWorkRecord_OnCheckChange(object sender, EventArgs e)
    {
        CheckBox chk = (CheckBox)sender;
        bool active = chk.Checked;
        PanelConsumerAtWork.Visible = active;
        
    }

    protected void RadAjaxManager1_AjaxRequest(object sender, AjaxRequestEventArgs e)
    {
        if (e.Argument == "Autosave")
        {
            autosaveData();
        }

        if (e.Argument == "Refresh")
        {
            validateFileAcknowledgement();
        }
    }

    private void expandOrCollapseDocumentedOutcomes()
    {
        bool isDirty = false;
        String outcomeID = "";
        String outcomeIDExpanded = "";
        ArrayList dirtyOutcomeIDs = new ArrayList();

        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            string controlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (controlID != null && controlID.Length > 7)
            {
                if (controlID.Substring(0, 7).CompareTo("OUTCOME") == 0)
                    outcomeID = controlID.Substring(7, controlID.Length - 7);
            }

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
            {
                RadNumericTextBox box = (RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[i];
                if (box.Text.Length > 0 && box.Text.CompareTo("0") != 0)
                    isDirty = true;
            }

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("System.Web.UI.WebControls.CheckBox") == 0)
            {
                CheckBox box = (CheckBox)SurveyGoalsWithEvaluations.Controls[i];
                if (box.Checked)
                    isDirty = true;
            }

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadTextBox") == 0)
            {
                RadTextBox box = (RadTextBox)SurveyGoalsWithEvaluations.Controls[i];
                if (box.Text.Length > 0)
                    isDirty = true;
            }

            if (isDirty && outcomeIDExpanded.CompareTo(outcomeID) != 0)
            {
                outcomeIDExpanded = outcomeID;
                dirtyOutcomeIDs.Add(outcomeID);
            }

            isDirty = false;
        }

        expandOutcome(dirtyOutcomeIDs);
    }

    private void expandOutcome(ArrayList dirtyOutcomeIDs)
    {
        string div = "";

        foreach (object outcomeID in dirtyOutcomeIDs)
        {
            div += "var divVar = document.getElementById(\"DIVShort" + outcomeID.ToString() + "\");divVar.style.display=\"none\";";
            div += "var divVar2 = document.getElementById(\"DIVLong" + outcomeID.ToString() + "\");divVar2.style.display=\"inline\";";
        }


        ScriptManager.RegisterStartupScript(Page, this.GetType(), "DatePickdderScript", div, true);
    }

    protected void ValidatePinControl_PinValidated(object sender, PinValidatedEventArgs e)
    {
        int clientID = Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value);

        //Update list whenever new SAVE button is added.
        if (LinkButtonStaffSaveAndSign.Visible == true || LinkButtonSave.Visible == true || LinkButtonManagerSaveAndSign.Visible == true || LinkButtonSaveAndDuplicate.Visible == true)
        {
            HiddenACTION_TYPEWAI.Value = Constants.CREATE;
        }
        if (HiddenACTION_TYPEWAI.Value == "MODIFY")
        {
            if (saveActivityRecordWAI())
            {
                if (!HiddenAUTOSAVEID.Value.Equals("False"))
                    BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
                long[] iAccess = getUserPrivs();

                DSActivityRecord activityRecord2 = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));

                // Check for completeness if priv is on
                if (Privilege.isPrivTrue(iAccess, Privileges9.AR_PREVENT_SIGNING_IF_RECORD_IS_INCOMPLETE))
                {
                    Hashtable activityRecordDetailhash = BOActivityRecord.getActivityRecordDetailExtraHashByActivityRecordID(activityRecord2.getActivityRecordID, getUserID(), clientID, true, "", "", false);

                    String activityRecordsGoalsDocumented = activityRecordDetailhash["ActivityRecordsGoalsDocumented"].ToString();

                    if (!isGoalsDocumentedComplete(activityRecordsGoalsDocumented))
                    {
                        RadAjaxManagerBillableWAI.ResponseScripts.Add("showRecordIsIncompleteMessage('UPDATE', '" + activityRecord2.getActivityRecordID.ToString() + "', 'The record was saved but was not signed because the record is not complete.  Ensure all documentation has been entered.');");
                        return;
                    }
                }

                if (validateSignStatus_IsRecordInFuture(getClientID(), getUserPrivs(), getEndTime()))
                {
                    RadAjaxManagerBillableWAI.ResponseScripts.Add("showRecordIsIncompleteMessage('UPDATE', '" + activityRecord2.getActivityRecordID.ToString() + "', 'The record was saved but was not signed because the record end time has not yet been met.');");
                    return;
                }

                bool userIsCreator = (activityRecord2.getCreatorUserID.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0 || HiddenTRANSFORMATION.Value == "TRUE");
                bool userIsManager = (Privilege.isPriv1True(iAccess, Privileges.SIGN_ARS_AS_MANAGER));

                if(HiddenSIGNROLE.Value == "MANAGER")
                {
                    // sign both staff and manager if staff sig not present
                    bool staffSignaturePresent = (activityRecord2.getStaffSign_ID != null && activityRecord2.getStaffSign_ID.Length != 0);
                    if (!staffSignaturePresent)
                        ActivityRecordsSignatures.signRecord(clientID, getUserPrivs(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUserID(), ActivityRecordsSignatures.STAFF_GUID, getUsername(), getRequestHostAddress());

                    ActivityRecordsSignatures.signRecord(clientID, getUserPrivs(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUserID(), ActivityRecordsSignatures.MANAGER_GUID, getUsername(), getRequestHostAddress());
                }
                else if(HiddenSIGNROLE.Value == "STAFF")
                {
                    ActivityRecordsSignatures.signRecord(clientID, getUserPrivs(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUserID(), ActivityRecordsSignatures.STAFF_GUID, getUsername(), getRequestHostAddress());
                }
                
                RadAjaxManagerBillableWAI.ResponseScripts.Add("window.CloseOrRedirect();");
            }
        }
        else if (HiddenACTION_TYPEWAI.Value == "CREATE")
        {
            if (!createConsumerWorkActivityRecord())
            {
                return;
            }
            if (createActivityRecordWAI())
            {
                if (!HiddenAUTOSAVEID.Value.Equals("False"))
                    BOAutosavedData.DeleteAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenAUTOSAVEID.Value));
                long[] iAccess = getUserPrivs();

                // Check for completeness if priv is on
                if (Privilege.isPrivTrue(iAccess, Privileges9.AR_PREVENT_SIGNING_IF_RECORD_IS_INCOMPLETE))
                {
                    Hashtable activityRecordDetailhash = BOActivityRecord.getActivityRecordDetailExtraHashByActivityRecordID(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUserID(), clientID, true, "", "", false);

                    String activityRecordsGoalsDocumented = activityRecordDetailhash["ActivityRecordsGoalsDocumented"].ToString();

                    if (!isGoalsDocumentedComplete(activityRecordsGoalsDocumented))
                    {
                        RadAjaxManagerBillableWAI.ResponseScripts.Add("showRecordIsIncompleteMessage('CREATE', '" + HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value + "', 'The record was saved but was not signed because the record is not complete.  Ensure all documentation has been entered.');");
                        return;
                    }
                }

                if (validateSignStatus_IsRecordInFuture(getClientID(), getUserPrivs(), getEndTime()))
                {
                    RadAjaxManagerBillableWAI.ResponseScripts.Add("showRecordIsIncompleteMessage('UPDATE', '" + HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value + "', 'The record was saved but was not signed because the record end time has not yet been met.');");
                    return;
                }

                if (HiddenSIGNROLE.Value == "MANAGER")
                {
                    ActivityRecordsSignatures.signRecord(clientID, getUserPrivs(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUserID(), ActivityRecordsSignatures.STAFF_GUID, getUsername(), getRequestHostAddress());
                    ActivityRecordsSignatures.signRecord(clientID, getUserPrivs(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUserID(), ActivityRecordsSignatures.MANAGER_GUID, getUsername(), getRequestHostAddress());
                }
                else if (HiddenSIGNROLE.Value == "STAFF")
                {
                    ActivityRecordsSignatures.signRecord(clientID, getUserPrivs(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getUserID(), ActivityRecordsSignatures.STAFF_GUID, getUsername(), getRequestHostAddress());
                }

                RadAjaxManagerBillableWAI.ResponseScripts.Add("window.CloseOrRedirect();");            }
        }
    }

    private void ajaxify(String _sourceControlID, params String[] _toAjaxifyControlIDs)
    {
        AjaxSetting setting = new AjaxSetting(_sourceControlID);

        foreach (String toAjaxifyControlID in _toAjaxifyControlIDs)
        {
            AjaxUpdatedControl ajaxifyControl = new AjaxUpdatedControl(toAjaxifyControlID, "RadAjaxLoadingPanel1WAI");
            ajaxifyControl.UpdatePanelRenderMode = UpdatePanelRenderMode.Block;
            setting.UpdatedControls.Add(ajaxifyControl);
        }

        RadAjaxManagerBillableWAI.AjaxSettings.Add(setting);
    }

    protected void RadSchedulerDayWAI_NavigationCommand(object sender, SchedulerNavigationCommandEventArgs e)
    {
        long[] iAccess = getUserPrivs();

        DateTime timePickerFromDateTime = RadTimePickerFromWAI.SelectedDate.Value;
        DateTime timePickerToDateTime = RadTimePickerToWAI.SelectedDate.Value;
        DateTime schedulerFromDateTime = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToString());
        DateTime startTime = schedulerFromDateTime.AddHours(timePickerFromDateTime.Hour).AddMinutes(timePickerFromDateTime.Minute);
        DateTime endTime = schedulerFromDateTime.AddHours(timePickerToDateTime.Hour).AddMinutes(timePickerToDateTime.Minute);

        DateTime newSchedulerFromDateTime = DateTime.Parse(e.SelectedDate.ToString());
        DateTime newStartTime = newSchedulerFromDateTime.AddHours(timePickerFromDateTime.Hour).AddMinutes(timePickerFromDateTime.Minute);
        DateTime newEndTime = newSchedulerFromDateTime.AddHours(timePickerToDateTime.Hour).AddMinutes(timePickerToDateTime.Minute);

        DateTime featureSuspensionStartTime = ActivityRecordsMisc.getActivityRecordLockDateTimePastBack(iAccess, getUserID(), getClientID().ToString(), "BILLABLE_INDIVIDUAL");
        DateTime featureSuspensionEndTime = ActivityRecordsMisc.getActivityRecordLockDateTimeFutureToFuture(iAccess, getUserID(), getClientID().ToString(), "BILLABLE_INDIVIDUAL");

        bool arWithinDateRange = ActivityRecordsMisc.isActivityRecordWithinDayLimitRange2(newStartTime, newEndTime, iAccess, getUserID(), lblStatusUpdateWAI, getClientID().ToString(), "BILLABLE_INDIVIDUAL", HiddenACTION_TYPEWAI.Value);

        if (!arWithinDateRange)
        {
            ErrorDIV.Style.Add("display", "block");
            InformationDIV.Style.Add("display", "none");
        }
        else
        {
            ErrorDIV.Style.Add("display", "none");
        }

        DSRecordLock recordLock = BORecordLock.GetRecordLockByClientID(getClientID());
        DSUserFeatureSuspension[] featureSuspension = BOUserFeatureSuspension.getUserFeatureSuspensionByFeatureCodeAndUserID("AR_PRIV_DAYS_FORWARD_AND_BACK_SUSPEND", getUserID(), getClientID());
   
        if (recordLock != null && featureSuspension.Length == 0)
        {
            List<string> stringsToAjaxify = new List<string> {
                    chkBoxCreateConsumerAtWorkRecord.Visible ? "chkBoxCreateConsumerAtWorkRecord" : "",
                    RadNumericTextFromHours.Visible ? "RadNumericTextFromHours" : "",
                    RadioButtonListFromAMPM.Visible ? "RadioButtonListFromAMPM" : "",
                    RadNumericTextToHours.Visible ? "RadNumericTextToHours" : "",
                    RadNumericTextToMinutes.Visible ? "RadNumericTextToMinutes" : "",
                    RadioButtonListToAMPM.Visible ? "RadioButtonListToAMPM" : "",
                    CheckboxNightAttendance.Visible ? "CheckboxNightAttendance" : "",
                    chckBoxOffSite.Visible ? "chckBoxOffSite" : "",
                    chkBoxFaceToFace.Visible ? "chkBoxFaceToFace" : "",
                    TravelTimeHours.Visible ? "TravelTimeHours" : "",
                    TravelTimeMinutes.Visible ? "TravelTimeMinutes": "",
                    RadTextBoxAdditionalComments.Visible ? "RadTextBoxAdditionalComments" : "",
                    RadComboBoxAdditionalSupports.Visible ? "RadComboBoxAdditionalSupports" : "",
                    RadComboBoxPlaceOfService.Visible ? "RadComboBoxPlaceOfService" : "",
                    RadComboBoxIndividualServicePhase.Visible ? "RadComboBoxIndividualServicePhase" : "",
                    lateRecordDiv.Visible ? "lateRecordDiv" : "",
                    InformationDIV.Visible ? "InformationDIV": "",
                    RadComboBoxAdditionalSupports.Visible ? "RadComboBoxAdditionalSupports" : "",
                    RadTimePickerFromWAI.Visible ? "RadTimePickerFromWAI" : "",
                    RadTimePickerToWAI.Visible ? "RadTimePickerToWAI" : "",
                    RadTextBoxAdditionalComments.Visible ? "RadTextBoxAdditionalComments" : "",
                    LinkButtonUpdate.Visible ? "LinkButtonUpdate" : "",
                    LinkButtonDuplicate.Visible ? "LinkButtonDuplicate" : "",
                    LinkButtonUpdateAndDuplicate.Visible ? "LinkButtonUpdateAndDuplicate" : "",
                    LinkButtonManagerUpdateAndSign.Visible ? "LinkButtonManagerUpdateAndSign" : "",
                    LinkButtonStaffUpdateAndSign.Visible ? "LinkButtonStaffUpdateAndSign" : "",
                    LinkButtonSave.Visible ? "LinkButtonSave" : "",
                    LinkButtonSaveAndDuplicate.Visible ? "LinkButtonSaveAndDuplicate" : "",
                    LinkButtonStaffSaveAndSign.Visible ? "LinkButtonStaffSaveAndSign" : "",
                    LinkButtonManagerSaveAndSign.Visible ? "LinkButtonManagerSaveAndSign" : "",
    };

            ajaxify("RadSchedulerDayWAI", stringsToAjaxify.ToArray());
            if ((recordLock.LockDateTime > newStartTime || recordLock.LockDateTime >= newEndTime) && recordLock.LockARDates)
            {
                if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                {
                    RadTimePickerFromWAI.Enabled = false;
                    RadTimePickerToWAI.Enabled = false;
                    RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                    HiddenRECORDLOCK.Value = "True";
                }

                lblStatusUpdateWAI.Text = "The system has been locked to prevent changing the dates of activity records prior to " + recordLock.LockDateTime.ToShortDateString() + " at " + recordLock.LockDateTime.ToShortTimeString();
                ErrorDIV.Style.Add("display", "block");
                InformationDIV.Style.Add("display", "none");
            }
            else if (recordLock.LockDateTime > newStartTime || recordLock.LockDateTime >= newEndTime)
            {
                if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                {
                    disableCapRecordNoTimePicker(true);
                    RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                    HiddenRECORDLOCK.Value = "True";
                }

                lblStatusUpdateWAI.Text = "The system has been locked to prevent the modification or creation of activity records prior to " + recordLock.LockDateTime.ToShortDateString() + " at " + recordLock.LockDateTime.ToShortTimeString();
                ErrorDIV.Style.Add("display", "block");
                InformationDIV.Style.Add("display", "none");
            }
            else if (lblStatusUpdateWAI.Text.Length > 0 || lblInformation.Text.Length > 0)
            {
                disableCapRecordNoTimePicker(false);
                RadTimePickerFromWAI.Enabled = true;
                RadTimePickerToWAI.Enabled = true;


                ErrorDIV.Style.Add("display", "none");
                InformationDIV.Style.Add("display", "none");
            }
        }
        
        foreach (DSUserFeatureSuspension fs in featureSuspension)
        {
            List<string> stringsToAjaxify = new List<string> {
                    chkBoxCreateConsumerAtWorkRecord.Visible ? "chkBoxCreateConsumerAtWorkRecord" : "",
                    RadNumericTextFromHours.Visible ? "RadNumericTextFromHours" : "",
                    RadioButtonListFromAMPM.Visible ? "RadioButtonListFromAMPM" : "",
                    RadNumericTextToHours.Visible ? "RadNumericTextToHours" : "",
                    RadNumericTextToMinutes.Visible ? "RadNumericTextToMinutes" : "",
                    RadioButtonListToAMPM.Visible ? "RadioButtonListToAMPM" : "",
                    CheckboxNightAttendance.Visible ? "CheckboxNightAttendance" : "",
                    chckBoxOffSite.Visible ? "chckBoxOffSite" : "",
                    chkBoxFaceToFace.Visible ? "chkBoxFaceToFace" : "",
                    FaceToFaceTimeHours.Visible ? "FaceToFaceTimeHours" : "",
                    FaceToFaceTimeMinutes.Visible ? "FaceToFaceTimeMinutes" : "",
                    RecordKeepingTimeHours.Visible ? "RecordKeepingTimeHours" : "",
                    RecordKeepingTimeMinutes.Visible ? "RecordKeepingTimeMinutes" : "",
                    TravelTimeHours.Visible ? "TravelTimeHours" : "",
                    TravelTimeMinutes.Visible ? "TravelTimeMinutes": "",
                    Miles.Visible ? "Miles" : "",
                    CollaborationTimeHours.Visible ? "CollaborationTimeHours" : "",
                    CollaborationTimeMinutes.Visible ? "CollaborationTimeMinutes" : "",
                    TotalBillableTimeHours.Visible ? "TotalBillableTimeHours" : "",
                    TotalBillableTimeMinutes.Visible ? "TotalBillableTimeMinutes" : "",
                    RadTextBoxAdditionalComments.Visible ? "RadTextBoxAdditionalComments" : "",
                    RadComboBoxAdditionalSupports.Visible ? "RadComboBoxAdditionalSupports" : "",
                    RadComboBoxPlaceOfService.Visible ? "RadComboBoxPlaceOfService" : "",
                    RadComboBoxIndividualServicePhase.Visible ? "RadComboBoxIndividualServicePhase" : "",
                    lateRecordDiv.Visible ? "lateRecordDiv" : "",
                    InformationDIV.Visible ? "InformationDIV": "",
                    RadComboBoxAdditionalSupports.Visible ? "RadComboBoxAdditionalSupports" : "",
                    RadTimePickerFromWAI.Visible ? "RadTimePickerFromWAI" : "",
                    RadTimePickerToWAI.Visible ? "RadTimePickerToWAI" : "",
                    RadTextBoxAdditionalComments.Visible ? "RadTextBoxAdditionalComments" : "",
                    LinkButtonUpdate.Visible ? "LinkButtonUpdate" : "",
                    LinkButtonDuplicate.Visible ? "LinkButtonDuplicate" : "",
                    LinkButtonUpdateAndDuplicate.Visible ? "LinkButtonUpdateAndDuplicate" : "",
                    LinkButtonManagerUpdateAndSign.Visible ? "LinkButtonManagerUpdateAndSign" : "",
                    LinkButtonStaffUpdateAndSign.Visible ? "LinkButtonStaffUpdateAndSign" : "",
                    LinkButtonSave.Visible ? "LinkButtonSave" : "",
                    LinkButtonSaveAndDuplicate.Visible ? "LinkButtonSaveAndDuplicate" : "",
                    LinkButtonStaffSaveAndSign.Visible ? "LinkButtonStaffSaveAndSign" : "",
                    LinkButtonManagerSaveAndSign.Visible ? "LinkButtonManagerSaveAndSign" : "",
    };

            ajaxify("RadSchedulerDayWAI", stringsToAjaxify.ToArray());
            if (fs.getSuspendedFrom == DateTime.MinValue && fs.getSuspendedTo == DateTime.MinValue)
            {
                if ((featureSuspensionEndTime != DateTime.MinValue && newStartTime > featureSuspensionEndTime) || (featureSuspensionStartTime != DateTime.MinValue && newEndTime < featureSuspensionStartTime))
                {
                    if (!fs.getChangeAllOtherDetailsOfAR)
                    {
                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            disableCapRecordNoTimePicker(true);
                        }

                        lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                        InformationDIV.Style.Add("display", "none");
                    }
                    if (!fs.getChangeDateAndTimeOfAR)
                    {
                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                            HiddenRECORDLOCK.Value = "True";
                        }
                        lblStatusUpdateWAI.Text = "Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                        InformationDIV.Style.Add("display", "none");
                    }
                    else
                    {
                        if (fs.getChangeAllOtherDetailsOfAR)
                        {
                            lblStatusUpdateWAI.Text = "";
                            ErrorDIV.Style.Add("display", "none");
                        }
                    }
                    if (!fs.getChangeDateAndTimeOfAR && !fs.getChangeAllOtherDetailsOfAR)
                    {
                        lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString() + "<br/>Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                        InformationDIV.Style.Add("display", "none");
                    }
                }
                else
                {
                    lblStatusUpdateWAI.Text = "";
                    ErrorDIV.Style.Add("display", "none");
                }
            }
            else if (fs.getSuspendedFrom <= startTime && fs.getSuspendedTo >= endTime)
            {
                if ((featureSuspensionEndTime != DateTime.MinValue && newStartTime > featureSuspensionEndTime) || (featureSuspensionStartTime != DateTime.MinValue && newEndTime < featureSuspensionStartTime))
                {
                    if (!fs.getChangeAllOtherDetailsOfAR)
                    {

                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            disableCapRecordNoTimePicker(true);
                        }
                        lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                        InformationDIV.Style.Add("display", "none");
                    }
                    if (!fs.getChangeDateAndTimeOfAR)
                    {
                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            RadAjaxManagerBillableWAI.ResponseScripts.Add("hideSchedulerButtons()");
                            HiddenRECORDLOCK.Value = "True";
                        }
                        lblStatusUpdateWAI.Text = "Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                        InformationDIV.Style.Add("display", "none");
                    }
                    else
                    {
                        RadTimePickerFromWAI.Enabled = true;
                        RadTimePickerToWAI.Enabled = true;

                        if (fs.getChangeAllOtherDetailsOfAR)
                        {
                            lblStatusUpdateWAI.Text = "";
                            ErrorDIV.Style.Add("display", "none");
                        }
                    }
                    if (!fs.getChangeDateAndTimeOfAR && !fs.getChangeAllOtherDetailsOfAR)
                    {
                        lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString() + "<br/>Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                        InformationDIV.Style.Add("display", "none");
                    }
                }
                else
                {
                    lblStatusUpdateWAI.Text = "";
                    ErrorDIV.Style.Add("display", "none");
                }
            }
            else if (lblStatusUpdateWAI.Text.Length > 0)
            {
                RadAjaxManagerBillableWAI.ResponseScripts.Add("reverseSchedulerButtons()");
                disableCapRecordNoTimePicker(false);
                RadTimePickerFromWAI.Enabled = true;
                RadTimePickerToWAI.Enabled = true;
                lblStatusUpdateWAI.Text = "";
                ErrorDIV.Style.Add("display", "none");
            }
        }
    }
     
    #endregion
    
    #region Setup
    private void setupActivityRecordWAI(DateTime _startTime, String _consumerID)
    {
        // makes appointments unclickable
        RadSchedulerDayWAI.OnClientAppointmentClick = "";
        ActivityRecordsMisc.updateLabelUserName(LabelUserNameWAI, RadComboBoxConsumerWAI, HiddenACTION_TYPEWAI, HiddenCURRENTWINDOWUSERID, HiddenBILLABLECAPAR_CREATOR_USER_ID);

        BODepartment.getDepartmentsByUserID(getUserID(), RadComboBoxDepartmentWAI, 1);
        long[] iAccess = getUserPrivs();
        
        
        // Setup Department Dropdown

        if(Privilege.isPriv2True(iAccess, Privileges2.SHOW_ALL_DEPARTMENT_OPTION))
        {
            RadComboBoxDepartmentWAI.Items.Insert(0, new RadComboBoxItem("All", "0"));
        }
        // If a Consumer is passed..
        if(!String.IsNullOrWhiteSpace(HiddenBILLABLECAPAR_CONSUMER_ID.Value))
        {
            //If a Consumer is passed and ALL is disabled, select the first shared consumer/user department
            if(!Privilege.isPriv2True(iAccess, Privileges2.SHOW_ALL_DEPARTMENT_OPTION))
            {
                DSDepartment[] departments = getUserDepartments();
                DSDepartment[] consumerDepartments = BODepartment.getDepartmentsArrayByConsumerID(getClientID(), HiddenBILLABLECAPAR_CONSUMER_ID.Value);
                DSDepartment combined = departments.FirstOrDefault(ud => consumerDepartments.Any(cd => cd.Department_ID == ud.Department_ID));
                if (combined != null)
                {
                    ControlHelper.assignComboBoxValue(RadComboBoxDepartmentWAI, combined.Department_ID);
                }
                else
                {
                    ControlHelper.assignComboBoxValue(RadComboBoxDepartmentWAI, getDefaultDepartment().ToString());
                }
            }
            else //If a Consumer is passed and ALL is not disabled, select ALL
            {
                RadComboBoxDepartmentWAI.SelectedIndex = 0;
            }
        }
        else // If a Consumer is not passed in, auto-select the user's default department
        {

            ControlHelper.assignComboBoxValue(RadComboBoxDepartmentWAI, getDefaultDepartment().ToString());
        }
        

        //Check for Coverage
        if (Privilege.isPriv1True(iAccess, Privileges.FORCE_COVERAGE_FOR_CONSUMER_ACCESS))
        {
            BOConsumer.GetConsumersDetailedByDepartmentIDAndClientIDWithCoverageByUserIDActiveOnly(RadComboBoxDepartmentWAI.SelectedValue, HiddenCURRENTWINDOWCLIENTID.Value, HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
            RadComboBoxConsumerWAI.Items.Insert(0, new RadComboBoxItem(""));
        }
        else
        {
            BOConsumer.GetConsumersByDepartmentIDAndClientIDAndStaffIDWithBlankActiveOnly(Int32.Parse(RadComboBoxDepartmentWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
        }
        RadComboBoxConsumerWAI.Text = "";

        /**
         * If a consumer is passed in
         * - Set Consumer as Selected Consumer
         * - If Consumer only has 1 Authorization, Auto select that Authorization
         * - If Authorization is selected, check to see if the Placement Combobox needs to be shown
         **/
        RadTextBoxComment.Text = "";
        RadTextBoxAdditionalComments.Text = "";
        if (!string.IsNullOrEmpty(_consumerID))
        {

            if (Privilege.isPrivTrue(iAccess, Privileges10.WITHIN_AR_CAP_SHOW_CLIENT_INFORMATION))
            {
                DSConsumer consumer = BOConsumer.GetConsumerDetail(Int32.Parse(_consumerID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                showClientInformation(iAccess, _consumerID, consumer);
            }

            if (RadComboBoxConsumerWAI.Items.FindItemByValue(_consumerID) != null)
            {
                RadComboBoxConsumerWAI.SelectedValue = _consumerID;

                if (!string.IsNullOrEmpty(RadComboBoxConsumerWAI.SelectedValue))
                {
                    RadComboBoxConsumerWAI_OnSelectedIndexChanged(null, null);
                }

                RadComboBoxAuthorizationWAI.Items.Clear();
                RadComboBoxAuthorizationWAI.Text = "";

                bool disableAuthPriv = Privilege.isPriv1True(iAccess, Privileges.DISABLE_AUTHORIZATION_WHEN_HOURS_EXCEEDED);

                BOAuthorization.getElligibleAuthorizationsByConsumerIDWithHoursWithoutTCM(System.Convert.ToInt32(RadComboBoxConsumerWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), RadComboBoxAuthorizationWAI, RadSchedulerDayWAI.SelectedDate, "", disableAuthPriv, RadComboBoxDepartmentWAI.SelectedValue, HiddenCURRENTWINDOWUSERID.Value, iAccess);
                if (RadComboBoxAuthorizationWAI.Items.Count == 1)
                {
                    RadComboBoxAuthorizationWAI.SelectedIndex = 0;
                    DSAuthorization auth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                    HiddenShowNAOption.Value = auth.getAllowSelectNAForAllGoalsButton.ToString();
                    if (HiddenShowNAOption.Value.Equals("True"))
                    {
                        String NACheckControl = "";
                        NACheckControl = "<span id='SpanNACheck' style='display:none;font-size:14px;margin-left:10px;margin-right:6px;'><a href='#' onclick='javascript:checkAllNA();'>(Mark all as N/A)</a><span id='LabelNASetInfo' style='color:#af0006;'></span></span>";
                        LabelOutcomesGoals.Text += NACheckControl;

                    }
                    ActivityRecordsMisc.processAuthorizationToDetermineNeedForPlacementSelectionOnlyActive(lblPlacementWAI, RadComboBoxPlacementWAI, RadTimePickerFromWAI, RadSchedulerDayWAI, RadComboBoxConsumerWAI);
                }
                else
                {
                    lblPlacementWAI.Visible = false;
                    RadComboBoxPlacementWAI.Visible = false;
                }
            }
        }

    }

    private void showClientInformation(long[] iAccess, string _consumerID, DSConsumer _dsConsumer)
    {
        string clientInfo = "<b>Name:</b> " + _dsConsumer.getLastName + ", " + _dsConsumer.getFirstName + "<br />";

        DSAllergy[] dsAllergies = BOAllergy.getConsumerAllergiesByConsumerID(_consumerID);

            
        String allergies = "";

        foreach (DSAllergy dsAllergy in dsAllergies)
        {
            if (allergies.Length > 0)
                allergies += ", ";

            DSAllergyUpdated allergy = BOAllergy.getAllergyByAllergyID(dsAllergy.getAllergyID, Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));

            allergies = allergies + allergy.Description;
        }

        clientInfo += "<b>Allergies:</b> " + (allergies.Length == 0 ? "NKA" : allergies);
        clientInfo += "<br />";

        string precautions = BOConsumerTextField.getConsumerTextField(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(_consumerID), "PRECAUTIONS");

        clientInfo += "<b>Precautions:</b> " + precautions;

        LabelClientInformationDetail.Text = clientInfo;
    }

    private void rebuildDynamicControlsWithCurrentSelection(bool _disableAllControls)
    {
        long[] iAccess = getUserPrivs();

        if (USER_HAS_AI_ASSISTANT_ACCESS && 
            Privilege.isPrivTrue(iAccess, Privileges17.ENABLE_AI_ASSISTANT_AUTO_REVIEW_INDIVIDUAL_ACTIVITY_RECORD) && 
            (activityRecordsAIAssistantConfig.provideGeneralSuggestions || 
             activityRecordsAIAssistantConfig.reviewGrammar || 
             activityRecordsAIAssistantConfig.reviewTone || 
             activityRecordsAIAssistantConfig.reviewSubjectiveObjectiveLanguage || 
             activityRecordsAIAssistantConfig.useCustomInstructions))
        {
            var goalID = 0;
            CreateAutoReviewControls("CONSUMER", goalID, AutoReviewAdditionalCommentsPanel);
        }
        
        if (SurveyGoalsWithEvaluations.Controls.Count > 1)
        {
            return;
        }
        if (RadComboBoxAuthorizationWAI.SelectedValue.Length == 0)
        {
            return;
        }
        if (RadComboBoxConsumerWAI.SelectedValue.Length == 0)
        {
            RadComboBoxAuthorizationWAI.Text = "";
            RadComboBoxAuthorizationWAI.Items.Clear();
            RadComboBoxAuthorizationWAI.ClearSelection();
            return;
        }
        if (!Privilege.isPrivTrue(iAccess, Privileges17.SHOW_GOALS_AND_OUTCOMES_SECTION_BILLABLE_INDIVIDUALS))
        {
            Fieldsetgoals.Attributes.Add("style", "display:none;");
            return;
        }

        DSAuthorization authorization = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
        DSService service = BOService.getServiceByServiceIDAndClientID(authorization.getServiceID, Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));

        DSAssessmentKey[] assessmentKeys = BOAssessmentKey.getAssessmentKeyByClientIDAndServiceID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), authorization.getServiceID.ToString(), ActivityRecordID);
        DSInterventionKey[] interventionKeys = BOInterventionKey.getInterventionKeyByClientIDAndServiceID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), authorization.getServiceID.ToString(), ActivityRecordID);

        DSGoal[] goals = BOGoal.getElligibleGoalsByConsumerIDAndAuthID(getClientID(), Int32.Parse(RadComboBoxConsumerWAI.SelectedValue), Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), RadSchedulerDayWAI.SelectedDate, "1");
        
        bool goalWithNoOutcome = false;

        if (Privilege.isPriv5True(iAccess, Privileges5.CAP_TYPE_USES_OUTCOME_DETAILS_AND_METHODS_FROM_OUTCOME))
        {
            DSOutcome[] dsOutcomes = BOOutcome.getOutcomesByConsumerID(Int32.Parse(RadComboBoxConsumerWAI.SelectedValue), getClientID());
            
            //foreach goals if outcome id is null then set outcome id to 0 on goal and insert an empty outcome into dsOutcomes
            for (int i = 0; i < goals.Length; i++)
            {
                if (goals[i].getOutcomeID == null || goals[i].getOutcomeID == 0)
                {
                    DSGoal goal = goals[i];
                    goal.getOutcomeID = 0;
                    //if outcomes does not contain an outcome with id 0 then add one
                    if (!dsOutcomes.Any(o => o.getOutcomeID == 0))
                    {
                        DSOutcome outcome = new DSOutcome();
                        outcome.getOutcomeID = 0;
                        outcome.getDescription = "No Outcome";
                        outcome.getOutcomeDetailDescription = "No Outcome Detail";
                        outcome.getMethodsTechniquesStrategies = "";
                        Array.Resize(ref dsOutcomes, dsOutcomes.Length + 1);
                        dsOutcomes[dsOutcomes.Length -1] = outcome;
                        goalWithNoOutcome = true;
                    }
                }
            }

            // Sort by outcome description and then outcomedetaildescription
            Array.Sort(dsOutcomes, (x, y) => String.Compare(x.getDescription + " " + x.getOutcomeDetailDescription, y.getDescription + " " + y.getOutcomeDetailDescription));

            int outcomeIndex = 0;

            List<DSGoal> goalsReordered = new List<DSGoal>();

            foreach (DSOutcome dsOutcome in dsOutcomes)
            {
                for (int i = 0; i < goals.Length; i++)
                {
                    if (goals[i].getOutcomeID == dsOutcome.getOutcomeID)
                    {
                        goalsReordered.Add(goals[i]);

                    }
                }
            }

            goals = new DSGoal[goals.Length];
            goals = goalsReordered.ToArray();
        }

        int outcomeID = 0;
        int outcomeIDforDiv = 0;
        string collapseDIV = "";
        string expandAll = "";
        string collapseAll = "";
        bool hideNAOption = true;

        // at this point have the goal array ordered by outcome

        if (Privilege.isPriv5True(iAccess, Privileges5.CAP_TYPE_USES_OUTCOME_DETAILS_AND_METHODS_FROM_OUTCOME))
        {
            if (goals.Length > 0)
            {
                for (int i = 0; i < goals.Length; i++)
                {
                    expandAll += "var divVar = document.getElementById(\"DIVShort" + goals[i].getOutcomeID + "\");divVar.style.display=\"none\";";
                    expandAll += "var divVar2 = document.getElementById(\"DIVLong" + goals[i].getOutcomeID + "\");divVar2.style.display=\"inline\";";
                    collapseAll += "var divVar = document.getElementById(\"DIVShort" + goals[i].getOutcomeID + "\");divVar.style.display=\"inline\";";
                    collapseAll += "var divVar2 = document.getElementById(\"DIVLong" + goals[i].getOutcomeID + "\");divVar2.style.display=\"none\";";
                }

                expandAll += "return false;";
                collapseAll += "return false;";

                SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div style='margin-top:-5px;float:right;margin-right:10px;'><a style='' href='#' onclick='javascript:" + expandAll + "'>Expand All</a>&nbsp;&nbsp;<a style='color:' href='#' onclick='javascript:" + collapseAll + "'>Collapse All</a></div><br />"));
            }
        }
        
        goals = goals.ToList().OrderBy(g => g.getOutcomeID == 0).ToArray();
        
        bool emptyOutcome = false;
        for (int i = 0; i < goals.Length; i++)
        {
            if (i > 0)
                SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));

            if (Privilege.isPriv5True(iAccess, Privileges5.CAP_TYPE_USES_OUTCOME_DETAILS_AND_METHODS_FROM_OUTCOME))
            {
                outcomeIDforDiv = goals[i].getOutcomeID;
                DSOutcome dsOutcome = new DSOutcome();

                // So the divID from previous outcome can be assigned to the collapseDIV for the previous
                if (i == 0)
                {
                    // Create control to divide the control array in the Panel to know when a new outcome comes.  Used when detecting which outcomes have been documented on.
                    LiteralControl con = new LiteralControl();
                    con.ID = "OUTCOME" + outcomeIDforDiv.ToString();
                    SurveyGoalsWithEvaluations.Controls.Add(con);
                }

                if (outcomeIDforDiv == 0 && goalWithNoOutcome && !emptyOutcome)
                {
                    dsOutcome.getOutcomeID = 0;
                    dsOutcome.getDescription = "No Outcome";
                    dsOutcome.getOutcomeDetailDescription = "No Outcome Detail";
                    dsOutcome.getMethodsTechniquesStrategies = "";
                    emptyOutcome = true;

                }
                else if (outcomeIDforDiv == 0 && goalWithNoOutcome && emptyOutcome)
                {
                    continue;
                }
                else
                {
                    dsOutcome = BOOutcome.getOutcomeByOutcomeID(outcomeIDforDiv);
                }
                string outcome = dsOutcome.getDescription.Replace("\r\n", "<br />");
                string shortOutcome = outcome.Substring(0, outcome.Length <= 90 ? outcome.Length : 90);
                shortOutcome = outcome.Length <= 90 ? shortOutcome : shortOutcome + "...";
                string longOutcome = outcome;
                string outcomeDetail = "";
                string outcomeStrategy = "";
                string outcomeStrategyLabel = "Strategy";

                String outcomeDescriptionLabel = HiddenOutcomeDescription.Value.Length > 0 ? HiddenOutcomeDescription.Value : "Outcome";

                if (Privilege.isPriv5True(iAccess, Privileges5.CAP_TYPE_USES_OUTCOME_DETAILS_AND_METHODS_FROM_OUTCOME))
                {
                    String longOutcomeLabel = HiddenOutcomeOutcomeDetailDescription.Value.Replace("\r\n", "<br />").Length > 0 ? "<b>" + HiddenOutcomeOutcomeDetailDescription.Value + "</b>: " : "";
                    longOutcome = longOutcome + "<br />" + longOutcomeLabel + dsOutcome.getOutcomeDetailDescription.Replace("\r\n", "<br />");
                    outcomeStrategy = dsOutcome.getMethodsTechniquesStrategies.Replace("\r\n", "<br />");
                    outcomeStrategyLabel = HiddenOutcomeMethodsTechniquesStrategies.Value.Length > 0 ? "<b>" + HiddenOutcomeMethodsTechniquesStrategies.Value + "</b>" : "Method/Techniques/Strategy";
                }
                else
                {
                    outcomeStrategy = goals[i].getLongDescription.Replace("\r\n", "<br />");
                    outcomeStrategyLabel = HiddenOutcomeOutcomeDetailDescription.Value;
                }

                collapseDIV = "var divVar = document.getElementById(\"DIVShort" + outcomeIDforDiv + "\");divVar.style.display=\"inline\";";
                collapseDIV += "var divVar2 = document.getElementById(\"DIVLong" + outcomeIDforDiv + "\");divVar2.style.display=\"none\";return false;";

                string expandDIV = "var divVar = document.getElementById(\"DIVShort" + outcomeIDforDiv + "\");divVar.style.display=\"none\";";
                expandDIV += "var divVar2 = document.getElementById(\"DIVLong" + outcomeIDforDiv + "\");divVar2.style.display=\"inline\";return false;";

                if (i + 1 == goals.Length)
                {
                    collapseDIV = "var divVar = document.getElementById(\"DIVShort" + outcomeIDforDiv + "\");divVar.style.display=\"inline\";";
                    collapseDIV += "var divVar2 = document.getElementById(\"DIVLong" + outcomeIDforDiv + "\");divVar2.style.display=\"none\";return false;";
                }

                if (outcomeID == 0)
                {
                    outcomeID = goals[i].getOutcomeID;

                    if (Request["Action"].ToString().CompareTo(Constants.CREATE) == 0)
                    {
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div id='DIVShort" + outcomeID + "' style='display:none;'><div style='left-padding:-10px;vertical-align:center;'><a class='CAPOutcomeLink' href='#' onclick='javascript:" + expandDIV + "' class='autosaveoutcome'><img src=\"../../../images/icon-arrow-right-24.png\" align='top'></img><b>" + outcomeDescriptionLabel + ": </b>" + shortOutcome + "</a></div></div>"));
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div id='DIVLong" + outcomeID + "' style='display:inline;'><a style='' href='#' onclick='javascript:" + collapseDIV + "'><img src=\"../../../images/icon-arrow-down-24.png\" align='top'></img><div style='width:100%;height:100%;padding-left:24px;display:inline-block;margin-top:-24px;text-decoration:none;color:#000000;' class='autosaveoutcome'><b>" + outcomeDescriptionLabel + ":</b> " + longOutcome + "</div></a><br />"));
                    }
                    else
                    {
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div id='DIVShort" + outcomeID + "' style='display:inline;'><div style='left-padding:-10px;vertical-align:center;' class='autosaveoutcome'><a class='CAPOutcomeLink' href='#' onclick='javascript:" + expandDIV + "'><img src=\"../../../images/icon-arrow-right-24.png\" align='top'></img><b>" + outcomeDescriptionLabel + ": </b>" + shortOutcome + "</a></div></div>"));
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div id='DIVLong" + outcomeID + "' style='display:none;'><a style='' href='#' onclick='javascript:" + collapseDIV + "'><img src=\"../../../images/icon-arrow-down-24.png\" align='top'></img><div style='width:100%;height:100%;padding-left:24px;display:inline-block;margin-top:-24px;text-decoration:none;color:#000000;' class='autosaveoutcome'><b>" + outcomeDescriptionLabel + ":</b> " + longOutcome + "</div></a><br />"));
                    }

                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div style='padding-left:24px;' class='autosaveoutcome'><b>" + outcomeStrategyLabel + ": </b>" + outcomeStrategy));
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));
                }

                String goalLabel = HiddenGoalShortDescription.Value.Length > 0 ? "<b>" + HiddenGoalShortDescription.Value + "</b>: " : "<b>Goal</b>: ";

                if (outcomeID == goals[i].getOutcomeID)
                {
                    // don't do anything in terms of adding the strategty - already added above
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div class='autosavegoal'>" + goalLabel + goals[i].getShortDescription.Replace("\n", "<br />") + "</div>"));
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));
                }
                else
                {
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("</div></div><br /><hr /><br />"));
                    outcomeID = goals[i].getOutcomeID;

                    // Create control to divide the control array in the Panel to know when a new outcome comes.  Used when detecting which outcomes have been documented on.
                    LiteralControl con = new LiteralControl();
                    con.ID = "OUTCOME" + outcomeID.ToString();
                    SurveyGoalsWithEvaluations.Controls.Add(con);

                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div id='DIVShort" + outcomeID + "' style='display:inline;'><div style='left-padding:-10px;vertical-align:center;'><a class='CAPOutcomeLink' href='#' onclick='javascript:" + expandDIV + "'><img src=\"../../../images/icon-arrow-right-24.png\" align='top'></img><b>" + outcomeDescriptionLabel + ": </b>" + shortOutcome + "</a></div></div>"));
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div id='DIVLong" + outcomeID + "' style='display:none;'><a style='' href='#' onclick='javascript:" + collapseDIV + "'><img src=\"../../../images/icon-arrow-down-24.png\" align='top'></img><div style='width:100%;height:100%;padding-left:24px;display:inline-block;margin-top:-24px;text-decoration:none;color:#000000;' class='autosaveoutcome'><b>" + outcomeDescriptionLabel + ":</b> " + longOutcome + "</div></a><br />"));

                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div style='padding-left:24px;' class='autosavegoal'><b>" + outcomeStrategyLabel + ": </b>" + outcomeStrategy));
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));

                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div class='autosavegoal'>" + goalLabel + goals[i].getShortDescription.Replace("\n", "<br />") + "</div>"));
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));
                }
                // the code here will check and see if the outcome changed - if it did, then show it + the strategy, if not don't draw anything and go to next goal
            }
            else
            {
                if (Privilege.isPriv3True(iAccess, Privileges3.OUTCOME_IS_VISIBLE_FOR_GOAL))
                {
                    outcomeIDforDiv = goals[i].getOutcomeID;
                    if (outcomeIDforDiv == 0) continue;
                    string outcome = BOOutcome.getOutcomeByOutcomeID(outcomeIDforDiv).getDescription.Replace("\r\n", "<br />");
                    String outcomeLabel = HiddenOutcomeDescription.Value.Length > 0 ? HiddenOutcomeDescription.Value : "Outcome";
                    SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div class='autosavegoal'><b>" + outcomeLabel + ": </b>" + outcome));
                }
                string goalLabel = HiddenGoalShortDescription.Value.Length > 0 ? "<b>" + HiddenGoalShortDescription.Value + "</b>" : "<b>Goal</b>";
                string goal = goals[i].getShortDescription.Replace("\n", "<br />");
                string goalLongDescriptionLabel = HiddenGoalShortDescription.Value.Length > 0 ? "<b>" + HiddenGoalLongDescription.Value + "</b>" : "<b>Methods / Techniques / Strategies</b>";
                string goalLongDescription = goals[i].getLongDescription.Replace("\n", "<br />");
                SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div class='autosavegoal'>" + goalLabel + ": </b>" + goal + "</div>"));
                SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div class='autosavegoal'>" + goalLongDescriptionLabel + ": </b>" + goalLongDescription + "</div>"));
                SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br />"));
            }

            for (int j = 0; j < interventionKeys.Length; j++)
            {
                SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("&nbsp;"));

                if (Int32.Parse(interventionKeys[j].getMaxCount) > 1)
                {
                    RadNumericTextBox radNumericTextBox = new RadNumericTextBox();
                    radNumericTextBox.Label = interventionKeys[j].getCode;
                    radNumericTextBox.Font.Size = 9;
                    radNumericTextBox.ToolTip = interventionKeys[j].getDescription;
                    radNumericTextBox.AllowOutOfRangeAutoCorrect = false;
                    radNumericTextBox.MaxValue = Int32.Parse(interventionKeys[j].getMaxCount);
                    radNumericTextBox.MaxLength = Int32.Parse(interventionKeys[j].getMaxCount) > 99 ? 3 : 2;
                    radNumericTextBox.MinValue = 0;
                    radNumericTextBox.NumberFormat.DecimalDigits = 0;
                    radNumericTextBox.EmptyMessage = "0";
                    radNumericTextBox.ShowSpinButtons = true;
                    radNumericTextBox.ButtonsPosition = InputButtonsPosition.Right;
                    radNumericTextBox.Type = NumericType.Number;
                    radNumericTextBox.Width = Int32.Parse(interventionKeys[j].getMaxCount) > 99 ? 70 : 60;
                    radNumericTextBox.BorderStyle = BorderStyle.None;
                    radNumericTextBox.EnableViewState = false;
                    radNumericTextBox.Text = "0";
                    radNumericTextBox.Style.Add("margin-right", "10px");
                    radNumericTextBox.ID = goals[i].getGoalID + "_INTERVENTION_" + interventionKeys[j].getInterventionKeyID;

                    radNumericTextBox.Enabled = !_disableAllControls;
                    SurveyGoalsWithEvaluations.Controls.Add(radNumericTextBox);
                    if (j < interventionKeys.Length - 1)
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("|"));
                }
                else if (Int32.Parse(interventionKeys[j].getMaxCount) == 1)
                {
                    CheckBox checkboxInterventions = new CheckBox();
                    checkboxInterventions.Font.Size = 9;
                    checkboxInterventions.Text = "&nbsp;" + interventionKeys[j].getCode;
                    checkboxInterventions.ToolTip = interventionKeys[j].getDescription;
                    checkboxInterventions.ID = goals[i].getGoalID + "_INTERVENTION_" + interventionKeys[j].getInterventionKeyID;

                    if (interventionKeys[j].getDescription.Equals("Hold_NH"))
                        checkboxInterventions.Attributes.Add("onClick", "HoldPrompt();");

                    if (interventionKeys[j].getCode.Equals("N/A") || interventionKeys[j].getCode.Equals("NA"))
                    {
                        hideNAOption = false;
                        checkboxInterventions.CssClass = "NotAppCheckbox";
                    }
                    checkboxInterventions.EnableViewState = false;
                    checkboxInterventions.Enabled = !_disableAllControls;

                    SurveyGoalsWithEvaluations.Controls.Add(checkboxInterventions);
                    if (j < interventionKeys.Length - 1)
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("|"));
                }
            }

            SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<div style='margin-top:5px;margin-bottom:5px;border-top:1px solid;border-color:#AAAAAA;'></div>"));

            for (int j = 0; j < assessmentKeys.Length; j++)
            {
                SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("&nbsp;"));

                if (Int32.Parse(assessmentKeys[j].getMaxCount) > 1)
                {
                    RadNumericTextBox radNumericTextBox = new RadNumericTextBox();
                    radNumericTextBox.Font.Size = 9;
                    radNumericTextBox.Label = assessmentKeys[j].getCode;
                    radNumericTextBox.ToolTip = assessmentKeys[j].getDescription;
                    radNumericTextBox.AllowOutOfRangeAutoCorrect = false;
                    radNumericTextBox.MaxValue = Int32.Parse(assessmentKeys[j].getMaxCount);
                    radNumericTextBox.MaxLength = Int32.Parse(assessmentKeys[j].getMaxCount) > 99 ? 3 : 2;
                    radNumericTextBox.MinValue = 0;
                    radNumericTextBox.NumberFormat.DecimalDigits = 0;
                    radNumericTextBox.EmptyMessage = "0";
                    radNumericTextBox.ShowSpinButtons = true;
                    radNumericTextBox.ButtonsPosition = InputButtonsPosition.Right;
                    radNumericTextBox.Type = NumericType.Number;
                    radNumericTextBox.Width = Int32.Parse(assessmentKeys[j].getMaxCount) > 99 ? 70 : 60;
                    radNumericTextBox.BorderStyle = BorderStyle.None;
                    radNumericTextBox.EnableViewState = false;
                    radNumericTextBox.Text = "0";
                    radNumericTextBox.Style.Add("margin-right", "10px");
                    radNumericTextBox.ID = goals[i].getGoalID + "_ASSESSMENT_" + assessmentKeys[j].getAssessmentKeyID;

                    radNumericTextBox.Enabled = !_disableAllControls;

                    SurveyGoalsWithEvaluations.Controls.Add(radNumericTextBox);
                    if (j < assessmentKeys.Length - 1)
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("|"));
                }
                else if (Int32.Parse(assessmentKeys[j].getMaxCount) == 1)
                {
                    CheckBox checkboxAssessments = new CheckBox();
                    checkboxAssessments.Text = "&nbsp;" + assessmentKeys[j].getCode;
                    checkboxAssessments.Font.Size = 9;

                    if (assessmentKeys[j].getCode.Equals("N/A") || assessmentKeys[j].getCode.Equals("NA"))
                    {
                        hideNAOption = false;
                        checkboxAssessments.CssClass = "NotAppCheckbox";
                    }

                    checkboxAssessments.ToolTip = assessmentKeys[j].getDescription;
                    checkboxAssessments.ID = goals[i].getGoalID + "_ASSESSMENT_" + assessmentKeys[j].getAssessmentKeyID;
                    checkboxAssessments.EnableViewState = false;
                    checkboxAssessments.Enabled = !_disableAllControls;

                    SurveyGoalsWithEvaluations.Controls.Add(checkboxAssessments);
                    if (j < assessmentKeys.Length - 1)
                        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("|"));
                }
            }
            
            if (USER_HAS_AI_ASSISTANT_ACCESS && 
                Privilege.isPrivTrue(iAccess, Privileges17.ENABLE_AI_ASSISTANT_AUTO_REVIEW_INDIVIDUAL_ACTIVITY_RECORD) &&
                (activityRecordsAIAssistantConfig.reviewOutcomeGoalComments || 
                 activityRecordsAIAssistantConfig.useCustomInstructions))
            {
                CreateAutoReviewControls("GOAL", goals[i].getGoalID, SurveyGoalsWithEvaluations);
            }

            if (Privilege.isPriv4True(iAccess, Privileges4.ACTIVITY_RECORD_CAP_HAVE_GOAL_SPECIFIC_COMMENT))
            {
                // add goal text below
                RadTextBox radTextGoalComment = new RadTextBox();
                radTextGoalComment.ID = "GOALID_" + goals[i].getGoalID;
                radTextGoalComment.Width = Unit.Percentage(100);
                radTextGoalComment.Rows = 5;
                radTextGoalComment.Style.Add("margin-top", "5px");
                radTextGoalComment.TextMode = InputMode.MultiLine;
                radTextGoalComment.EnableViewState = false;

                string goalLabel = HiddenGoalShortDescription.Value.Length > 0 ? HiddenGoalShortDescription.Value : "<b>Goal</b>: ";
                string goalLabelContent = HiddenGoalShortDescription.Value.Length > 0 ? HiddenGoalShortDescription.Value : "Goal";
                radTextGoalComment.EmptyMessage = "For above " + goalLabelContent.ToLower() + " please add specific comments here. Please include what happened (Activity), what the staff did (Support) and what the individual did.";
                radTextGoalComment.Text = HttpUtility.HtmlDecode(service.GetDefaultGoalComment);
                if (_disableAllControls)
                {
                    radTextGoalComment.Enabled = false;
                }
                else
                {
                    radTextGoalComment.Enabled = true;
                    radTextGoalComment.ClientEvents.OnLoad = "ResetGoalTextValue";
                }
                SurveyGoalsWithEvaluations.Controls.Add(radTextGoalComment);
            }
            SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("<br>"));
        }

        // Add final collapse to last outcome
        SurveyGoalsWithEvaluations.Controls.Add(new LiteralControl("</div>"));

        DSAuthorization auth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
        HiddenShowNAOption.Value = auth.getAllowSelectNAForAllGoalsButton.ToString();
        if (!hideNAOption || HiddenShowNAOption.Value.Equals("True"))
        {
            RadAjaxManagerBillableWAI.ResponseScripts.Add("showNAOption();");
        }
        else if (interventionKeys.Length == 0 && assessmentKeys.Length == 0)
        {
            if (HiddenShowNAOption.Value.Equals("True"))
            {
                RadAjaxManagerBillableWAI.ResponseScripts.Add("showNAOption();");
            }
        }
        else
        {
            RadAjaxManagerBillableWAI.ResponseScripts.Add("hideNAOption();");
        }
    }

    private void openActivityRecordWAI(String activityRecordID)
    {
        long[] iAccess = getUserPrivs();
        bool privAdministratorOverrideEnabled = Privilege.isPriv1True(iAccess, Privileges.ACTIVITY_RECORD_ADMIN_OVERRIDE);
		bool disableAuthPriv = Privilege.isPriv1True(iAccess, Privileges.DISABLE_AUTHORIZATION_WHEN_HOURS_EXCEEDED);
        DSActivityRecord activityRecord2 = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
        if (activityRecord2?.getActivityRecordType == null)
        {
            //activity record was deleted
            DSDeletedData deletedData = BODeletedData.getDeletedDataByEntityTypeAndEntityID(getClientID(), "ACTIVITY_RECORDS_BILLABLE_INDIVIDUAL", activityRecordID);
            string user = BOUser.getUserDescriptionByUserID(deletedData.Deleted_Data_User_ID.ToString());
            Response.Redirect(PathHelper.getAbsoluteUrl("~/ErrorPages/RecordDeleted.aspx") + "?RecordDeletedBy=" + user + "&DateDeleted=" + TimeZoneProcessor.convertDateTimeToClientLocalDateTime(getClientID().ToString(), deletedData.Deleted_Data_DateTime).ToString("MM/dd/yyyy h:mm tt"));
        }

        Session.Add(SessionDefinitions.ACTIVITYRECORD_ID_DEBUG_ONLY, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);

        String consumerID = activityRecord2.getConsumerID.ToString();
        String staffID = activityRecord2.getCreatorUserID;
        String authID = activityRecord2.getAuth_ID.ToString();
        String departmentID = "0";

        if (activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_BILLABLE_INTER_ASSESS_KEY") == 0)
        {
            HiddenTRANSFORMATION.Value = "FALSE";
        }
        else
        {
            HiddenTRANSFORMATION.Value = "TRUE";
            // get the actual type, get any relevant data as well, this will be used to prepolate things when opening.

            if (activityRecord2.getActivityRecordType.getCode.CompareTo("APPOINTMENT") == 0 || activityRecord2.getActivityRecordType.getCode.CompareTo("APPOINTMENT_AUTH") == 0)
            {
                // on save any appointments nad appointment relations must be cleared out

                DSAppointment appointment = BOAppointment.getAppointmentByAppointmentIDAndClientID(Int32.Parse(activityRecord2.getAppointmentID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                DSAppointmentRelation[] appointmentRelations = BOAppointmentRelation.getAppointmentRelationsByAppointmentIDAndClientID(Int32.Parse(activityRecord2.getAppointmentID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                String employerID = "";
                for (int i = 0; i < appointmentRelations.Length; i++)
                {
                    if (appointmentRelations[i].getEntityCode.CompareTo("CONSUMER_ID") == 0)
                    {
                        consumerID = appointmentRelations[i].getEntityID;
                        HiddenBILLABLECAPAR_CONSUMER_ID_PASSED.Value = consumerID;
                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("EMPLOYER_ID") == 0)
                    {
                        // ignore for now
                        employerID = appointmentRelations[i].getEntityID;
                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("STAFF_USER_ID") == 0)
                    {
                        // ignore for now
                        staffID = appointmentRelations[i].getEntityID;
                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("AUTHORIZATION_ID") == 0)
                    {
                        // ignore for now
                        authID = appointmentRelations[i].getEntityID;
                    }
                    if (appointmentRelations[i].getEntityCode.CompareTo("DEPARTMENT_ID") == 0)
                    {
                        departmentID = appointmentRelations[i].getEntityID;
                    }
                }
            }
            else if (activityRecord2.getActivityRecordType.getCode.CompareTo("ACTIVITYRECORD_NONBILLABLE") == 0)
            {
                // nothing to prepopulate with!
            }
        }
        LabelARIDWAI.Text = activityRecord2.getActivityRecordID.ToString();
        RadTextBoxComment.Text = HttpUtility.HtmlDecode(activityRecord2.getComment);
        RadTextBoxAdditionalComments.Text = HttpUtility.HtmlDecode(activityRecord2.getEffect);
        chckBoxOffSite.Checked = activityRecord2.getOffSite;
        chkBoxFaceToFace.Checked = activityRecord2.getConsumerPresent;
        if (consumerID != null && consumerID.Length > 0 && consumerID != "0")
        {
            DSConsumer consumer = BOConsumer.GetConsumerDetail(Int32.Parse(consumerID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
            if (authID != null && authID.Length != 0 && authID != "0")
            {
                DSAuthorization auth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(authID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                departmentID = auth.getDepartmentID.ToString();
                HiddenShowNAOption.Value = auth.getAllowSelectNAForAllGoalsButton.ToString();
                String NACheckControl = "";
                if (HiddenShowNAOption.Value.Equals("True"))
                    NACheckControl = "<span id='SpanNACheck' style='display:none;font-size:14px;margin-left:10px;margin-right:6px;'><a href='#' onclick='javascript:checkAllNA();'>(Mark all as N/A)</a><span id='LabelNASetInfo' style='color:#af0006;'></span></span>";

				LabelOutcomesGoals.Text += NACheckControl;

				DSActivityRecord ar = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getClientID());
                if (ar.getActivityRecordType.getCode.CompareTo("APPOINTMENT") == 0 || ar.getActivityRecordType.getCode.CompareTo("APPOINTMENT_AUTH") == 0)
                {
                    DSService service = BOService.getServiceByServiceIDAndClientID(auth.getServiceID, getClientID());
                    if (!String.IsNullOrEmpty(service.getDefaultContent))
                        RadTextBoxAdditionalComments.Text = service.getDefaultContent + "\n";
                }
            }
            
            RadComboBoxDepartmentWAI.Items.Clear();
            BODepartment.getDepartmentsByUserID(getUserID(), RadComboBoxDepartmentWAI, 1);
            if (Privilege.isPriv2True(iAccess, Privileges2.SHOW_ALL_DEPARTMENT_OPTION))
            {
                RadComboBoxDepartmentWAI.Items.Insert(0, new RadComboBoxItem("All", "0"));
            }

            ControlHelper.assignComboBoxValue(RadComboBoxDepartmentWAI, departmentID, departmentId => BODepartment.getDepartmentByDepartmentID(departmentId).Department_Description);

            if (Privilege.isPriv1True(iAccess, Privileges.FORCE_COVERAGE_FOR_CONSUMER_ACCESS))
            {
                BOConsumer.GetConsumersDetailedByDepartmentIDAndClientIDWithCoverageByUserIDActiveOnly(departmentID, HiddenCURRENTWINDOWCLIENTID.Value, HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
                RadComboBoxConsumerWAI.Items.Insert(0, new RadComboBoxItem(""));
            }
            else
            {
                BOConsumer.GetConsumersByDepartmentIDAndClientIDAndStaffIDWithBlankActiveOnly(Int32.Parse(departmentID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
            }

            if (RadComboBoxConsumerWAI.Items.FindItemByValue(consumer.getID.ToString()) == null)
            {
                // explicitly add the one you don't have access to for viewing only
                RadComboBoxConsumerWAI.Items.Add(new RadComboBoxItem(consumer.getLastName + ", " + consumer.getFirstName, consumer.getID.ToString()));
            }

            RadComboBoxConsumerWAI.SelectedValue = consumer.getID.ToString();

            BOAuthorization.getElligibleAuthorizationsByConsumerIDWithHoursWithoutTCM(consumer.getID, Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), RadComboBoxAuthorizationWAI, activityRecord2.getStartDate, authID, disableAuthPriv, RadComboBoxDepartmentWAI.SelectedValue, HiddenCURRENTWINDOWUSERID.Value, iAccess);
            if (RadComboBoxAuthorizationWAI.FindItemByValue(authID) != null)
            {
                RadComboBoxAuthorizationWAI.SelectedValue = authID;
            }

			ActivityRecordsMisc.processAuthorizationToDetermineNeedForPlacementSelectionOnlyActive(lblPlacementWAI, RadComboBoxPlacementWAI, RadTimePickerFromWAI, RadSchedulerDayWAI, RadComboBoxConsumerWAI);

			LINightAttendance.Visible = false;
            if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_MANUALLY_SETTING_NIGHT_ATTENDANCE_ON_ARS))
            {
                LINightAttendance.Visible = true;
                DSStaffShift[] staffShifts = BOStaffShift.getNightShiftsForDepartment(getClientID(), Int32.Parse(departmentID));

                saveNightShiftToHiddenField(staffShifts);

                DSActivityRecordAdditional additional = BOActivityRecordAdditional.getActivityRecordAdditional(activityRecord2.getActivityRecordID.ToString(), getClientID().ToString());

                if (additional != null)
                    CheckboxNightAttendance.Checked = additional.IsNightAttendance;
            }

            if (activityRecord2.getPlacementID != null && activityRecord2.getPlacementID.Length != 0)
            {
                RadComboBoxPlacementWAI.SelectedValue = activityRecord2.getPlacementID;

                bool exists = false;
                foreach (RadComboBoxItem item in RadComboBoxPlacementWAI.Items)
                {
                    if (item.Value.CompareTo(activityRecord2.getPlacementID) == 0)
                    {
                        exists = true;
                    }
                }

                if (!exists)
                {
                    RadComboBoxPlacementWAI.Items.Add(new RadComboBoxItem(BOPlacement.getInactivePlacementDescriptionByPlacementID(Int32.Parse(activityRecord2.getPlacementID)), activityRecord2.getPlacementID));
                    RadComboBoxPlacementWAI.SelectedValue = activityRecord2.getPlacementID;
                    RadComboBoxPlacementWAI.Items.Insert(0, "");
                    lblPlacementWAI.Visible = true;
                    RadComboBoxPlacementWAI.Visible = true;
                }
            }

            if (Privilege.isPrivTrue(iAccess, Privileges10.WITHIN_AR_CAP_SHOW_CLIENT_INFORMATION))
            {
                showClientInformation(iAccess, consumer.getID.ToString(), consumer);
            }

            if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_LOGGING_ON_ADDITIONAL_SUPPORTS))
            {
                DSAdditionalSupport[] additionalSupports = BOAdditionalSupports.getActivityRecordsAdditionalSupportsByClientIDAndActivityRecordsID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

                RadComboBoxAdditionalSupports.DataBind();

                foreach (DSAdditionalSupport support in additionalSupports)
                {
                    bool found = false;

                    foreach (RadComboBoxItem item in RadComboBoxAdditionalSupports.Items)
                    {
                        if (support.AdditionalSupport_ID == Int32.Parse(item.Value))
                        {
                            item.Checked = true;
                            found = true;
                        }
                    }

                    if (!found)
                    {
                        RadComboBoxItem newItem = new RadComboBoxItem(support.Description, support.AdditionalSupport_ID.ToString());
                        newItem.Checked = true;
                        RadComboBoxAdditionalSupports.Items.Add(newItem);
                    }
                }
            }
        }
        else
        {
            RadComboBoxDepartmentWAI.Items.Clear();
            BODepartment.getDepartmentsByUserID(getUserID(), RadComboBoxDepartmentWAI, 1);
            if (Privilege.isPriv2True(iAccess, Privileges2.SHOW_ALL_DEPARTMENT_OPTION))
            {
                RadComboBoxDepartmentWAI.Items.Insert(0, new RadComboBoxItem("All", "0"));
            }

            if (RadComboBoxDepartmentWAI.SelectedValue.CompareTo("") != 0)
            {
                departmentID = RadComboBoxDepartmentWAI.SelectedValue;
            }
            else
            {
                departmentID = RadComboBoxDepartmentWAI.Items[0].Value;
            }

            long[] iAccessCoverage = getUserPrivs();
            if (Privilege.isPriv1True(iAccessCoverage, Privileges.FORCE_COVERAGE_FOR_CONSUMER_ACCESS))
            {
                BOConsumer.GetConsumersDetailedByDepartmentIDAndClientIDWithCoverageByUserIDActiveOnly(departmentID, HiddenCURRENTWINDOWCLIENTID.Value, HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
                RadComboBoxConsumerWAI.Items.Insert(0, new RadComboBoxItem(""));
            }
            else
            {
                BOConsumer.GetConsumersByDepartmentIDAndClientIDAndStaffIDWithBlankActiveOnly(Int32.Parse(departmentID), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), HiddenCURRENTWINDOWUSERID.Value, RadComboBoxConsumerWAI);
            }
        }

        if (staffID != null && staffID.Length > 0 && staffID != "0")
        {
            HiddenBILLABLECAPAR_CREATOR_USER_ID.Value = staffID;
            ActivityRecordsMisc.updateLabelUserName(LabelUserNameWAI, RadComboBoxConsumerWAI, HiddenACTION_TYPEWAI, HiddenCURRENTWINDOWUSERID, HiddenBILLABLECAPAR_CREATOR_USER_ID);
            RadTimePickerFromWAI.SelectedDate = activityRecord2.getStartDate;
            RadTimePickerToWAI.SelectedDate = activityRecord2.getEndDate;
            updateRadSchedulerDayByDateWAI(activityRecord2.getStartDate, staffID);
        }
        else
        {
            HiddenBILLABLECAPAR_CREATOR_USER_ID.Value = activityRecord2.getCreatorUserID;
            ActivityRecordsMisc.updateLabelUserName(LabelUserNameWAI, RadComboBoxConsumerWAI, HiddenACTION_TYPEWAI, HiddenCURRENTWINDOWUSERID, HiddenBILLABLECAPAR_CREATOR_USER_ID);
            RadTimePickerFromWAI.SelectedDate = activityRecord2.getStartDate;
            RadTimePickerToWAI.SelectedDate = activityRecord2.getEndDate;

            updateRadSchedulerDayByDateWAI(activityRecord2.getEndDate, activityRecord2.getCreatorUserID);
        }

        if (Privilege.isPriv6True(iAccess, Privileges6.ENABLE_SERVICE_PHASES))
        {
            int servicePhaseID = BOActivityRecord.getServicePhaseIDByActivityRecordID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

            if (servicePhaseID != 0)
            {
                ControlHelper.assignComboBoxValue(RadComboBoxIndividualServicePhase, servicePhaseID.ToString(), asdf => BOActivityRecord.getServicePhaseDescriptionByActivityRecordID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value)));
            }
        }
        TravelTimeHours.Value = 0;
        TravelTimeMinutes.Value = 0;
        DSActivityRecordAdditional dsARAdditional = BOActivityRecordAdditional.getActivityRecordAdditional(activityRecord2.getActivityRecordID.ToString(), getClientID().ToString());
        if(dsARAdditional !=null)
        {
            //set night shift?
            int travelTimeHoursValue = dsARAdditional.TravelTimeMinutes / 60;
            TravelTimeHours.Value = travelTimeHoursValue;
            int travelTimeMinutesValue = dsARAdditional.TravelTimeMinutes % 60;
            TravelTimeMinutes.Value = travelTimeMinutesValue;

            FaceToFaceTimeHours.Value = dsARAdditional.FaceToFaceTimeMinutes / 60;
            FaceToFaceTimeMinutes.Value = dsARAdditional.FaceToFaceTimeMinutes % 60;
            RecordKeepingTimeHours.Value = dsARAdditional.RecordKeepingTimeMinutes / 60;
            RecordKeepingTimeMinutes.Value = dsARAdditional.RecordKeepingTimeMinutes % 60;
            Miles.Value = dsARAdditional.Miles;
            CollaborationTimeHours.Value = dsARAdditional.CollaborationTimeMinutes / 60;
            CollaborationTimeMinutes.Value = dsARAdditional.CollaborationTimeMinutes % 60;
            TotalBillableTimeHours.Value = dsARAdditional.TotalBillableTimeMinutes / 60;
            TotalBillableTimeMinutes.Value = dsARAdditional.TotalBillableTimeMinutes % 60;
        }
        SurveyGoalsWithEvaluations.Controls.Clear();

        Boolean userIsManager = Privilege.isPriv1True(iAccess, Privileges.SIGN_ARS_AS_MANAGER);
        Boolean userIsCreator = activityRecord2.getCreatorUserID.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0 || HiddenTRANSFORMATION.Value == "TRUE";
        Boolean disableARWindow = true;
        if (userIsManager)
        {
            if (activityRecord2.getManagerSign_ID == null)
            {
                // no manager signed, enable everything
                disableARWindow = false;
            }
            else
            {
                if (activityRecord2.getManagerSign_ID != null && activityRecord2.getManagerSign_ID.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0)
                {
                    disableARWindow = false;
                }
                else
                {
                    disableARWindow = true;
                }
            }

            if (privAdministratorOverrideEnabled)
            {
                disableARWindow = false;
            }

        }
        else
        {
            // user is staff or not manager
            if (userIsCreator)
            {
                if (activityRecord2.getManagerSign_ID == null)
                {
                    disableARWindow = false;
                }
            }
            else
            {
				disableARWindow = !(HiddenTRANSFORMATION.Value == "TRUE");
            }
        }

        if (disallowEditingARDueToBilledStatus(activityRecordID, getClientID().ToString()))
        {
            disableARWindow = true;
        }

        // remove all controls build back up
        SurveyGoalsWithEvaluations.Controls.Clear();
        AutoReviewAdditionalCommentsPanel.Controls.Clear();
        rebuildDynamicControlsWithCurrentSelection(disableARWindow);
        SurveyGoalsWithEvaluations.Enabled = !disableARWindow;

        RadComboBoxAuthorizationWAI.Enabled = !disableARWindow;
        LabelUserNameWAI.Enabled = !disableARWindow;
        RadComboBoxDepartmentWAI.Enabled = !disableARWindow;
        RadComboBoxConsumerWAI.Enabled = !disableARWindow;
        RadTimePickerFromWAI.Enabled = !disableARWindow;
        RadTimePickerToWAI.Enabled = !disableARWindow;
        RadComboBoxPlacementWAI.Enabled = !disableARWindow;

        RadTextBoxComment.ReadOnly = disableARWindow;
        RadTextBoxAdditionalComments.ReadOnly = disableARWindow;
        RadSchedulerDayWAI.Enabled = !disableARWindow;
        HiddenEXPENSEDISABLECAP.Value = disableARWindow.ToString();
        RadComboBoxPlaceOfService.Enabled = !disableARWindow;
        
        bool updateAndSignVisible = (ActivityRecordsSignatures.staffUpdateAndSignVisibility(disableARWindow, activityRecord2.getStaffSign_ID, getUserID(), getClientID(), activityRecord2.getStartDate, iAccess, userIsCreator) && Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_SIGN_AND_UPDATE_AND_SIGN_ON_BILLABLE_CAP));
        LinkButtonStaffUpdateAndSign.Visible = updateAndSignVisible;
        UpdateAndSignListItem.Visible = updateAndSignVisible;
        bool updateAndManagerSignVisible =  (ActivityRecordsSignatures.managerUpdateAndSignVisibility(disableARWindow, activityRecord2.getStaffSign_ID, getUserID(), getClientID(), activityRecord2.getStartDate, iAccess, userIsCreator) && Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_SIGN_AND_UPDATE_AND_SIGN_ON_BILLABLE_CAP));
        LinkButtonUpdate.Visible = (!disableARWindow && Privilege.isPrivTrue(iAccess, Privileges8.SHOW_SAVE_AND_UPDATE_ON_BILLABLE_CAP));
        LinkButtonManagerUpdateAndSign.Visible = updateAndManagerSignVisible;
        UpdateAndManagerSignListItem.Visible = updateAndManagerSignVisible;

        if (activityRecord2.getAppointmentID != null && Privilege.isPrivTrue(iAccess, Privileges12.COPY_APPOINTMENT_SUBJECT_WHEN_CONVERTING)) 
        {
            DSAppointment appointment = BOAppointment.getAppointmentByAppointmentIDAndClientID(Int32.Parse(activityRecord2.getAppointmentID), getClientID(), activityRecord2.getActivityRecordID);
            RadTextBoxAdditionalComments.Text += "Appointment Subject: " + ReportsEncodingHelper.beautifulEncodingFix(appointment.getSubject) + "\n";
        }

        if (useSingleRecordPerDayPrivOrForClient_CO(iAccess, activityRecord2.getStartDate))
        {
            // find "primary" Activity Record for the day which IAKeys ANd Goals are tied to - it's the FIRST Record created for the day

            DateTime fromDateRange = new DateTime(activityRecord2.getStartDate.Year, activityRecord2.getStartDate.Month, activityRecord2.getStartDate.Day, 0, 0, 0);
            openActivityRecordPopulateKeysAndGoalComments(getMinARIDPrimary(activityRecord2.getConsumerID.ToString(), activityRecord2.getAuth_ID.ToString(), activityRecord2.getCreatorUserID, fromDateRange).ToString());
        }
        else
        {
            openActivityRecordPopulateKeysAndGoalComments(activityRecord2.getActivityRecordID.ToString());
        }
        expandOrCollapseDocumentedOutcomes();
    }

    private List<GoalKeyValue> getInterventionAndAssessmentKeyValues(String _activityRecord)
    {
        var goalKeyAndValues = new List<GoalKeyValue>();
        // For rest of I/A controls
        DSActivityRecordGoal[] activityRecordGoals = BOActivityRecordGoal.getActivityRecordGoalByActivityRecordID(_activityRecord);
        for (int i = 0; i < activityRecordGoals.Length; i++)
        {
            DSActivityRecordGoal activityRecordGoal = activityRecordGoals[i];

            for (int k = 0; k < activityRecordGoal.getAssessmentKeys.Length; k++)
            {
                var newGoalKeyValue = new GoalKeyValue();
                newGoalKeyValue.GoalID = int.Parse(activityRecordGoals[i].getGoal_ID);
                newGoalKeyValue.KeyType = "Assessment Key";
                for (int j = 0; j < SurveyGoalsWithEvaluations.Controls.Count; j++)
                {
                    String controlID = activityRecordGoal.getGoal_ID + "_ASSESSMENT_" + activityRecordGoal.getAssessmentKeys[k].getAssessmentKeyID;
                    String foundControlID = SurveyGoalsWithEvaluations.Controls[j].ID;
                    if (foundControlID == null)
                        continue;

                    if (controlID.CompareTo(foundControlID) == 0)
                    {

                        if (Int32.Parse(activityRecordGoal.getAssessmentKeys[k].getMaxCount) > 1)
                        {
                            if (SurveyGoalsWithEvaluations.Controls[j].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
                            {
                                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[j];
                                newGoalKeyValue.KeyDescription = activityRecordGoal.getAssessmentKeys[k].getDescription;
                                newGoalKeyValue.KeyValue = activityRecordGoal.getAssessmentKeys[k].getKeyvalue.ToString();
                                newGoalKeyValue.IsKeySelected = !string.IsNullOrEmpty(newGoalKeyValue.KeyValue) && newGoalKeyValue.KeyValue != "0";
                                goalKeyAndValues.Add(newGoalKeyValue);
                            }
                        }
                        else if (Int32.Parse(activityRecordGoal.getAssessmentKeys[k].getMaxCount) == 1)
                        {
                            System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[j];
                            newGoalKeyValue.KeyDescription = activityRecordGoal.getAssessmentKeys[k].getDescription;
                            newGoalKeyValue.KeyValue = tempCheckbox.Checked == true ? "Yes" : "No";
                            newGoalKeyValue.IsKeySelected = true;
                            goalKeyAndValues.Add(newGoalKeyValue);
                        }
                    }
                }
            }
            
            for (int k = 0; k < activityRecordGoal.getInterventionKeys.Length; k++)
            {
                var newGoalKeyValue = new GoalKeyValue();
                for (int j = 0; j < SurveyGoalsWithEvaluations.Controls.Count; j++)
                {
                    String controlID = activityRecordGoal.getGoal_ID + "_INTERVENTION_" + activityRecordGoal.getInterventionKeys[k].getInterventionKeyID;
                    String foundControlID = SurveyGoalsWithEvaluations.Controls[j].ID;
                    if (foundControlID == null)
                        continue;

                    if (controlID.CompareTo(foundControlID) == 0)
                    {
                        if (Int32.Parse(activityRecordGoal.getInterventionKeys[k].getMaxCount) > 1)
                        {
                            if (SurveyGoalsWithEvaluations.Controls[j].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
                            {
                                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[j];
                                newGoalKeyValue.KeyDescription = activityRecordGoal.getInterventionKeys[k].getDescription;
                                newGoalKeyValue.KeyValue = activityRecordGoal.getInterventionKeys[k].getKeyvalue.ToString();
                                newGoalKeyValue.IsKeySelected = !string.IsNullOrEmpty(newGoalKeyValue.KeyValue) && newGoalKeyValue.KeyValue != "0";
                                goalKeyAndValues.Add(newGoalKeyValue);
                            }
                        }
                        else if (Int32.Parse(activityRecordGoal.getInterventionKeys[k].getMaxCount) == 1)
                        {
                            System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[j];
                            newGoalKeyValue.KeyDescription = activityRecordGoal.getInterventionKeys[k].getDescription;
                            newGoalKeyValue.KeyValue = tempCheckbox.Checked == true ? "Yes" : "No";
                            newGoalKeyValue.IsKeySelected = true;
                            goalKeyAndValues.Add(newGoalKeyValue);
                        }
                    }
                }
            }
        }

        return goalKeyAndValues;
    }

    private void openActivityRecordPopulateKeysAndGoalComments(String _activityRecord)
    {
        long[] iAccess = getUserPrivs();

        // Add goal comments into textboxes
        if (Privilege.isPriv4True(iAccess, Privileges4.ACTIVITY_RECORD_CAP_HAVE_GOAL_SPECIFIC_COMMENT))
        {
            DSActivityRecordMultiGoal[] arMGs = BOActivityRecordMultiGoal.getActivityRecordMultiGoal(_activityRecord);
            if (arMGs != null)
            {
                for (int i = 0; i < arMGs.Length; i++)
                {
                    for (int j = 0; j < SurveyGoalsWithEvaluations.Controls.Count; j++)
                    {
                        //                radTextGoalComment.ID = "GOALID_" + goals[i].getGoalID;
                        String controlID = "GOALID_" + arMGs[i].getGoalID;
                        String foundControlID = SurveyGoalsWithEvaluations.Controls[j].ID;
                        if (foundControlID == null)
                            continue;

                        if (controlID.CompareTo(foundControlID) == 0)
                        {
                            RadTextBox tempRadTextBox = (RadTextBox)SurveyGoalsWithEvaluations.Controls[j];
                            tempRadTextBox.Text = arMGs[i].getSummary;
                        }
                    }
                }
            }
        }

        // For rest of I/A controls
        DSActivityRecordGoal[] activityRecordGoals = BOActivityRecordGoal.getActivityRecordGoalByActivityRecordID(_activityRecord);
        for (int i = 0; i < activityRecordGoals.Length; i++)
        {
            DSActivityRecordGoal activityRecordGoal = activityRecordGoals[i];

            for (int k = 0; k < activityRecordGoal.getAssessmentKeys.Length; k++)
            {
                for (int j = 0; j < SurveyGoalsWithEvaluations.Controls.Count; j++)
                {
                    String controlID = activityRecordGoal.getGoal_ID + "_ASSESSMENT_" + activityRecordGoal.getAssessmentKeys[k].getAssessmentKeyID;
                    String foundControlID = SurveyGoalsWithEvaluations.Controls[j].ID;
                    if (foundControlID == null)
                        continue;

                    if (controlID.CompareTo(foundControlID) == 0)
                    {

                        if (Int32.Parse(activityRecordGoal.getAssessmentKeys[k].getMaxCount) > 1)
                        {
                            if (SurveyGoalsWithEvaluations.Controls[j].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
                            {

                                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[j];
                                tempNumericBox.Text = activityRecordGoal.getAssessmentKeys[k].getKeyvalue;
                            }
                        }
                        else if (Int32.Parse(activityRecordGoal.getAssessmentKeys[k].getMaxCount) == 1)
                        {
                            System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[j];
                            tempCheckbox.Checked = true;
                        }
                    }
                }
            }

            for (int k = 0; k < activityRecordGoal.getInterventionKeys.Length; k++)
            {
                for (int j = 0; j < SurveyGoalsWithEvaluations.Controls.Count; j++)
                {
                    String controlID = activityRecordGoal.getGoal_ID + "_INTERVENTION_" + activityRecordGoal.getInterventionKeys[k].getInterventionKeyID;
                    String foundControlID = SurveyGoalsWithEvaluations.Controls[j].ID;
                    if (foundControlID == null)
                        continue;

                    if (controlID.CompareTo(foundControlID) == 0)
                    {
                        if (Int32.Parse(activityRecordGoal.getInterventionKeys[k].getMaxCount) > 1)
                        {
                            if (SurveyGoalsWithEvaluations.Controls[j].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
                            {
                                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[j];
                                tempNumericBox.Text = activityRecordGoal.getInterventionKeys[k].getKeyvalue;
                            }
                        }
                        else if (Int32.Parse(activityRecordGoal.getInterventionKeys[k].getMaxCount) == 1)
                        {
                            System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[j];
                            tempCheckbox.Checked = true;
                        }
                    }
                }
            }

        }

        RadComboBoxPlaceOfService.DataBind();
        DSActivityRecordPlaceOfService[] arPlaceOfServices = BOActivityRecordPlaceOfService.getActivityRecordPlaceOfService(_activityRecord, getClientID());
        if (arPlaceOfServices != null)
        {
            RadComboBoxPlaceOfService.SelectedValue = arPlaceOfServices[0].PlaceOfServiceID;
        }
    }

    private void duplicateActivityRecordWAI()
    {
        setError("");
        setSiteLabels();
        SurveyGoalsWithEvaluations.Controls.Clear();
        rebuildDynamicControlsWithCurrentSelection(false);
        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (foundControlID == null)
                continue;

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadTextBox") == 0)
            {
                RadTextBox tempTextBox = (RadTextBox)SurveyGoalsWithEvaluations.Controls[i];
                tempTextBox.Text = "";
                SurveyGoalsWithEvaluations.Controls.RemoveAt(i);
                SurveyGoalsWithEvaluations.Controls.AddAt(i, tempTextBox);
            }
        }

        long[] iAccess = getUserPrivs();
        bool disableAuthPriv = false;
        if (Privilege.isPriv1True(iAccess, Privileges.DISABLE_AUTHORIZATION_WHEN_HOURS_EXCEEDED))
        {
            disableAuthPriv = true;
        }
        BOAuthorization.getElligibleAuthorizationsByConsumerIDWithHoursWithoutTCM(System.Convert.ToInt32(RadComboBoxConsumerWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), RadComboBoxAuthorizationWAI, RadSchedulerDayWAI.SelectedDate, "", disableAuthPriv, RadComboBoxDepartmentWAI.SelectedValue, HiddenCURRENTWINDOWUSERID.Value, iAccess);
        DSActivityRecord activityRecord2 = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
        String authID = activityRecord2.getAuth_ID.ToString();
        if (RadComboBoxAuthorizationWAI.FindItemByValue(authID) != null)
        {
            RadComboBoxAuthorizationWAI.SelectedValue = authID;
        }
        RadTextBoxAdditionalComments.Text = "";
        RadComboBoxAdditionalSupports.ClearCheckedItems();
        TravelTimeHours.Text = "";
        TravelTimeMinutes.Text = "";
        FaceToFaceTimeHours.Text = "";
        FaceToFaceTimeMinutes.Text = "";
        RecordKeepingTimeHours.Text = "";
        RecordKeepingTimeMinutes.Text = "";
        Miles.Text = "";
        CollaborationTimeHours.Text = "";
        CollaborationTimeMinutes.Text = "";
        TotalBillableTimeHours.Text = "";
        TotalBillableTimeMinutes.Text = "";
        chkBoxCreateConsumerAtWorkRecord.Checked = false;
        PanelConsumerAtWork.Visible = false;
        chkBoxFaceToFace.Checked = false;
        RadComboBoxIndividualServicePhase.ClearSelection();
    }

    private void SetConvertableARTypes()
    {
        DSActivityRecordType[] types = BOActivityRecord.getActivityRecordTypesByClientIDAndUserDept(getClientID(), getUserID());
        foreach (var type in types)
        {
            if (type.getActive)
            {
                switch (type.getCode)
                {
                    case "ACTIVITYRECORD_NONBILLABLEWITHCONSUMER":
                        ConvertToNonBillableConsumer.Visible = true;
                        LinkButtonNonBillableWithConsumer.Text = type.getDescription;
                        break;
                }
            }
        }
    }
    #endregion
    
    #region Saving
    private void autosaveData()
    {
        String blob = assembleAutosaveDataBlob();

        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
        {
            if (HiddenAUTOSAVEID.Value.Equals("False"))
            {
                int autosaveID = BOAutosavedData.CreateAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value), 0, "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername(), DateTime.UtcNow);
                HiddenAUTOSAVEID.Value = autosaveID.ToString();
            }
            else
            {
                BOAutosavedData.SetAutosavedData(Int32.Parse(HiddenAUTOSAVEID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value),
                    0, "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername());
            }
        }
        else
        {
            if (!HiddenAUTOSAVEID.Value.Equals("False"))
            {
                BOAutosavedData.SetAutosavedData(Int32.Parse(HiddenAUTOSAVEID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value),
                    Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername());
            }
            else
            {
                DSAutosavedData autosavedData = BOAutosavedData.GetAutosavedDataByEntityNameAndEntityID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), "ACTIVITY_RECORD_ID", Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

                if (autosavedData == null)
                {
                    int autosaveID = BOAutosavedData.CreateAutosavedData(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value), 0, "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername(), DateTime.UtcNow);
                    HiddenAUTOSAVEID.Value = autosaveID.ToString();
                }
                else
                {
                    HiddenAUTOSAVEID.Value = autosavedData.AutosavedDataID.ToString();

                    BOAutosavedData.SetAutosavedData(Int32.Parse(HiddenAUTOSAVEID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), Guid.Parse(HiddenCURRENTWINDOWUSERID.Value),
                        Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), "ACTIVITY_RECORD_ID", "ACTIVITY_RECORD_INDIVIDUAL", blob, getUsername());
                }
            }
        }
    }

    private String assembleAutosaveDataBlob()
    {
        String blob = "<b>Billable Individual Activity Record</b><br />";

        if (TimeWithTimePicker.Visible)
        {
            blob += "<u>Date/Time:</u> " + RadTimePickerFromWAI.SelectedDate.Value.ToShortDateString() + " " + RadTimePickerFromWAI.SelectedDate.Value.ToShortTimeString() + " - " + RadTimePickerToWAI.SelectedDate.Value.ToShortTimeString() + "<br />";
        }
        else
        {
            blob += "<u>Date/Time:</u> " + RadNumericTextFromHours.Text + "h " + RadNumericTextFromMinutes.Text + "m " + RadioButtonListFromAMPM.SelectedItem.Text + " - " + RadNumericTextToHours.Text + "h " + RadNumericTextToMinutes.Text + "m " + RadioButtonListToAMPM.SelectedItem.Text + "<br />";
        }

        if (RadComboBoxConsumerWAI.SelectedItem != null)
            blob += "<u>Consumer:</u> " + RadComboBoxConsumerWAI.SelectedItem.Text + "<br />";

        if (RadComboBoxAuthorizationWAI.SelectedItem != null)
            blob += "<u>Authorization:</u> " + RadComboBoxAuthorizationWAI.SelectedItem.Text + "<br />";

        if (RadComboBoxIndividualServicePhase.SelectedItem != null)
            blob += "<u>Service Phase:</u> " + RadComboBoxIndividualServicePhase.SelectedItem.Text + "<br />";

        if (RadComboBoxPlaceOfService.SelectedItem != null)
            blob += "<u>Place of Service:</u> " + RadComboBoxPlaceOfService.SelectedItem.Text + "<br />";

        blob += "<u>Additional Comment:</u><br />" + RadTextBoxAdditionalComments.Text + "<br />";

        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("System.Web.UI.LiteralControl") == 0)
            {
                LiteralControl litControl = (LiteralControl)SurveyGoalsWithEvaluations.Controls[i];

                if (litControl != null && litControl.Text != null && (litControl.Text.Contains("autosaveoutcome") || litControl.Text.Contains("autosavegoal")))
                {
                    HtmlDocument doc = new HtmlDocument();
                    doc.LoadHtml(litControl.Text);
                    string deHTMLified = doc.DocumentNode.InnerText;
                    blob += deHTMLified + "<br />";
                }
            }

            String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (foundControlID == null)
                continue;

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadTextBox") == 0)
            {
                RadTextBox tempTextBox = (RadTextBox)SurveyGoalsWithEvaluations.Controls[i];
                String goal = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                String goalID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));
                if (goal.CompareTo("GOALID") == 0)
                {
                    blob += "<u>Goal Comment:</u><br />" + tempTextBox.Text + "<br />";
                }
            }

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("System.Web.UI.WebControls.CheckBox") == 0)
            {
                System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[i];

                String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                String keyID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));
                if (tempCheckbox.Checked)
                {
                    blob += tempCheckbox.Text + ": " + (tempCheckbox.Checked ? "Yes" : "No") + "<br />";
                }
            }

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
            {
                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[i];

                String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                String keyID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));

                blob += tempNumericBox.Label + ": " + tempNumericBox.Text + "<br />";
            }
        }

        if (RadComboBoxAdditionalSupports.CheckedItems.Count > 0)
        {
            blob += "<br /><u>Additional Supports:</u>";

            foreach (RadComboBoxItem item in RadComboBoxAdditionalSupports.CheckedItems)
            {
                blob += "<br />" + item.Text;
            }
        }

        return blob;
    }

    private bool saveActivityRecordWAI()
    {
        if (!isValidatorsValid() || !validateFileAcknowledgement())
            return false;

        long[] iAccess = getUserPrivs();

        Boolean userIsManager = false;
        Boolean userIsCreator = false;
        String clientID = HiddenCURRENTWINDOWCLIENTID.Value;
        String userName = getUsername();
        DateTime startTime = getStartTime();
        DateTime endTime = getEndTime();
        if(!isValidTime(ref startTime, ref endTime)) { return false; }
        if (Privilege.isPrivTrue(iAccess, Privileges17.ENABLE_TWELVE_TO_TWELVE_TWENTY_FOUR_HOUR_APPOINTMENTS))
            endTime = fixMidnightEndDate(startTime, endTime);
        double hours = ActivityRecordsMisc.calculateHrs(startTime, endTime);

        if (HiddenBILLABLECAPAR_CREATOR_USER_ID.Value.CompareTo(HiddenCURRENTWINDOWUSERID.Value) == 0)
        {
            userIsCreator = true;
        }

        if (Privilege.isPriv1True(iAccess, Privileges.SIGN_ARS_AS_MANAGER))
        {
            userIsManager = true;
        }

        String authorizationValue = RadComboBoxAuthorizationWAI.SelectedValue;
        String placementID = getPlacementID(authorizationValue, startTime);

        String activityRecordTypeID = getActivityRecordTypeID();

        String additionalCommentsTemp = RadTextBoxAdditionalComments.Text;
        if (RadTextBoxAdditionalComments.EmptyMessage.CompareTo(RadTextBoxAdditionalComments.Text) == 0)
        {
            // content not modified, don't save off empty message
            additionalCommentsTemp = "";
        }
        bool faceToFacechk = false;
        if (Privilege.isPrivTrue(iAccess, Privileges10.FACE_TO_FACE_ON_BILLABLE_CAP))
        {
            faceToFacechk = chkBoxFaceToFace.Checked;
        }
        if (HiddenTRANSFORMATION.Value == "TRUE")
        {
            BOAppointment.deleteAppointmentByAppointmentIDAndClientID(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenCURRENTWINDOWCLIENTID.Value);
            BOActivityRecord.modifyActivityRecordAndCreatorUserID(Int32.Parse(clientID), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, RadComboBoxConsumerWAI.SelectedValue,
                startTime, endTime, hours, authorizationValue, "",
                true, "", "", HttpUtility.HtmlEncode(additionalCommentsTemp), "", userName, HttpUtility.HtmlEncode(RadTextBoxComment.Text), chckBoxOffSite.Checked, false, false, placementID, activityRecordTypeID, "", "", "", "", faceToFacechk, "", HiddenCURRENTWINDOWUSERID.Value, (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
        }
        else
        {
            BOActivityRecord.modifyActivityRecord(Int32.Parse(clientID), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, RadComboBoxConsumerWAI.SelectedValue,
                startTime, endTime, hours, authorizationValue, "",
                true, "", "", HttpUtility.HtmlEncode(additionalCommentsTemp), "", userName, HttpUtility.HtmlEncode(RadTextBoxComment.Text), chckBoxOffSite.Checked, false, false, placementID, activityRecordTypeID, "", "", "", "", faceToFacechk, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, getUserID());
        }

        // If Night Attendence or Travel Time is enabled, create a new record, otherwise no additional record
        if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_MANUALLY_SETTING_NIGHT_ATTENDANCE_ON_ARS)||Privilege.isPrivTrue(iAccess,Privileges10.TRAVEL_TIME_ON_BILLABLE_CAP))
        {
            DSActivityRecordAdditional dsARAdditional = BOActivityRecordAdditional.getActivityRecordAdditional(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getClientID().ToString());
            DSAuthorization dsAuth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(authorizationValue), Int32.Parse(clientID));
            if (dsARAdditional==null)
            {
                createARAdditional(dsAuth, userName);
            }
            else
            {
                updateARAdditional(dsAuth, userName);
            }
        }

        if (Privilege.isPriv6True(iAccess, Privileges6.ENABLE_SERVICE_PHASES))
        {
            if (RadComboBoxIndividualServicePhase.SelectedValue.Length > 0)
            {
                BOActivityRecord.updateActivityRecordServicePhase(System.Convert.ToInt32(clientID), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), RadComboBoxIndividualServicePhase.SelectedValue, getUsername());
            }
            else
            {
                BOActivityRecord.deleteActivityRecordServicePhase(getClientID(), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));
            }
        }
        
        if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_LOGGING_ON_ADDITIONAL_SUPPORTS))
        {
            string[] additionalSupportIDs = RadComboBoxAdditionalSupports.CheckedItems.Select(item => item.Value).ToArray();
            BOAdditionalSupports.setActivityRecordsAdditionalSupports(System.Convert.ToInt32(clientID), Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), additionalSupportIDs, userName);
        }

        if (useSingleRecordPerDayPrivOrForClient_CO(iAccess, startTime))
        {
            DSActivityRecord record = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getClientID());
            DateTime fromDateRange = new DateTime(startTime.Year, startTime.Month, startTime.Day, 0, 0, 0);
            int minARIDPrimary = getMinARIDPrimary(RadComboBoxConsumerWAI.SelectedValue, authorizationValue, record.getCreatorUserID, fromDateRange);

            if (minARIDPrimary != 0)
            {
                // no record - must have moved to another day - in this case use the original ID
                saveARIAKeysAndGoals(minARIDPrimary.ToString());
                BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "ACTIVITYRECORDS_ID", minARIDPrimary.ToString(), getUsername(), Session.SessionID);
            }
            else
            {
                saveARIAKeysAndGoals(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
                BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "ACTIVITYRECORDS_ID", HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), Session.SessionID);
            }
        }
        else
        {
            saveARIAKeysAndGoals(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
            BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "ACTIVITYRECORDS_ID", HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), Session.SessionID);
        }

        BOActivityRecordPlaceOfService.deleteActivityRecordPlaceOfServiceByActivityRecordID(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        if (RadComboBoxPlaceOfService.SelectedValue != null && RadComboBoxPlaceOfService.SelectedValue.Length > 0)
        {
            DSActivityRecordPlaceOfService dsARPlaceOfService = new DSActivityRecordPlaceOfService();
            dsARPlaceOfService.ActivityRecordID = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
            dsARPlaceOfService.LstUpdateUsername = getUsername();
            dsARPlaceOfService.PlaceOfServiceID = RadComboBoxPlaceOfService.SelectedValue;
            BOActivityRecordPlaceOfService.createActivityRecordPlaceOfService(dsARPlaceOfService, getClientID());
        }

        if (Privilege.isPrivTrue(iAccess, Privileges7.UPDATE_COST_CENTERS_BASED_ON_ARS))
        {
            BOActivityRecord.setARUpdateCostCenterForNonBillArFollowingBillableByARID(getClientID(), HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        }

        BOFile.setFileUpdateTemporary(getClientID().ToString(), HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, "BILLABLE_CAP_FILE", getUsername());

        BOForms.setFormResultUpdateTemporary(HiddenCURRENTWINDOWCLIENTID.Value, HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, "ACTIVITY_RECORD", "ACTIVITY_RECORD", getUsername());

        updateTempSignatures();

        BOJob.setJobContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);
        BOConsumerNetworkContact.setNetworkContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);
        BOConsumerFundingSourceContact.setFundingSourceContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);
        BOConsumerConsumerContact.setConsumerConsumerContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);

        ActivityRecordsMisc.updateSignatures(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getClientID().ToString(), getUsername(), getUserID());

        return true;
    }

    private bool createActivityRecordWAI()
    {
        if (!isValidatorsValid() || !validateFileAcknowledgement())
            return false;

        long[] iAccess = getUserPrivs();
        String clientID = HiddenCURRENTWINDOWCLIENTID.Value;
        String userName = getUsername();

        String activityRecordIDOfNewItem = "";

        DateTime startTime = getStartTime();
        DateTime endTime = getEndTime();

        if(!isValidTime(ref startTime, ref endTime))
        {
            return false;
        }
        double hours = ActivityRecordsMisc.calculateHrs(startTime, endTime);

        if (Privilege.isPriv5True(iAccess, Privileges5.MAKE_ACTIVITY_RECORDS_CAP_TYPE_ADDITIONAL_COMMENTS_REQUIRED))
        {
            if (RadTextBoxAdditionalComments.Text.CompareTo(RadTextBoxAdditionalComments.EmptyMessage) == 0 || RadTextBoxAdditionalComments.Text.Length == 0)
            {
                setError("Additional Comments are required.");
                return false;
            }
        }
        Boolean isFaceToFace = false;
        if (Privilege.isPrivTrue(iAccess, Privileges10.FACE_TO_FACE_ON_BILLABLE_CAP))
        {
            isFaceToFace = chkBoxFaceToFace.Checked;
        }

        String authorizationDropDownValue = RadComboBoxAuthorizationWAI.SelectedValue;

        String placementID = getPlacementID(authorizationDropDownValue,startTime);

        String additionalCommentsTemp = RadTextBoxAdditionalComments.Text;
        if (RadTextBoxAdditionalComments.EmptyMessage.CompareTo(RadTextBoxAdditionalComments.Text) == 0)
        {
            // content not modified, don't save off empty message
            additionalCommentsTemp = "";
        }

        // get the billable ActivityRecord Type
        String activityRecordTypeID =getActivityRecordTypeID();

        if (Privilege.isPrivTrue(iAccess, Privileges17.ENABLE_TWELVE_TO_TWELVE_TWENTY_FOUR_HOUR_APPOINTMENTS))
            endTime = fixMidnightEndDate(startTime, endTime);

        activityRecordIDOfNewItem = BOActivityRecord.createActivityRecord(System.Convert.ToInt32(clientID), RadComboBoxConsumerWAI.SelectedValue, startTime,
            endTime, hours, authorizationDropDownValue, "", true, "",
            "", HttpUtility.HtmlEncode(additionalCommentsTemp), "", userName, HiddenCURRENTWINDOWUSERID.Value, HttpUtility.HtmlEncode(RadTextBoxComment.Text), chckBoxOffSite.Checked, false, false, placementID,
            activityRecordTypeID, "", "", "", "", isFaceToFace, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, "");

        HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value = activityRecordIDOfNewItem;
        
        BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "ACTIVITYRECORDS_ID_AND_CONSUMER_AT_WORK", activityRecordIDOfNewItem.ToString() + "_" + chkBoxCreateConsumerAtWorkRecord.Checked.ToString(), getUsername(), Session.SessionID);

        BOFile.setFileUpdateTemporary(getClientID().ToString(), HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, activityRecordIDOfNewItem, "BILLABLE_CAP_FILE", getUsername());

        updateTempSignatures();
        updateActivityRecordsGroup_Locations();
        BOJob.setJobContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);
        BOConsumerNetworkContact.setNetworkContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);
        BOConsumerFundingSourceContact.setFundingSourceContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);
        BOConsumerConsumerContact.setConsumerConsumerContactUpdateTemporary(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, getUsername(), RadComboBoxConsumerWAI.SelectedValue);

        if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_LOGGING_ON_ADDITIONAL_SUPPORTS))
        {
            if (RadComboBoxAdditionalSupports.CheckedItems.Count > 0)
            {
                string[] additionalSupportIDs = RadComboBoxAdditionalSupports.CheckedItems.Select(item => item.Value).ToArray();
                BOAdditionalSupports.createActivityRecordsAdditionalSupports(System.Convert.ToInt32(clientID), Int32.Parse(activityRecordIDOfNewItem), additionalSupportIDs, userName);
            }
        }

        // Only create AR additional row if NA is turned on.  No other fields there are used for the cap record.
        if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_MANUALLY_SETTING_NIGHT_ATTENDANCE_ON_ARS)||Privilege.isPrivTrue(iAccess,Privileges10.TRAVEL_TIME_ON_BILLABLE_CAP))
        {
            DSAuthorization dsAuth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(authorizationDropDownValue), Int32.Parse(clientID));
            createARAdditional(dsAuth, userName);
        }

        // now find all expenses created iwth the temp ID and update - so pass in OLD AR ID, NEW AR ID, and code temp, and ClientID
        BOExpense.setExpenseUpdateTemporary(HiddenCURRENTWINDOWCLIENTID.Value, HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, activityRecordIDOfNewItem, "TEMP_MILEAGE", "MILEAGE", getUsername());
        BOMileage.setMileageUpdateTemporary(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value),
            Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), 
            Int32.Parse(activityRecordIDOfNewItem),
            HiddenCURRENTWINDOWUSERID.Value);

        if (useSingleRecordPerDayPrivOrForClient_CO(iAccess, startTime))
        {
            // find "primary" Activity Record for the day which IAKeys ANd Goals are tied to - it's the FIRST Record created for the day
            DateTime fromDateRange = new DateTime(startTime.Year, startTime.Month, startTime.Day, 0, 0, 0);
            int minARIDPrimary = getMinARIDPrimary(RadComboBoxConsumerWAI.SelectedValue, authorizationDropDownValue, HiddenCURRENTWINDOWUSERID.Value, fromDateRange);
            if (minARIDPrimary != 0)
            {
                saveARCommentsAndGoalsOnCreate(minARIDPrimary.ToString());
            }
            else
            {
                saveARCommentsAndGoalsOnCreate(activityRecordIDOfNewItem);
            }

        }
        else
        {
            saveARCommentsAndGoalsOnCreate(activityRecordIDOfNewItem);
        }

        if (Privilege.isPriv6True(iAccess, Privileges6.PLACE_OF_SERVICE_IS_APPLICABLE))
        {
            if (RadComboBoxPlaceOfService.SelectedValue != null && RadComboBoxPlaceOfService.SelectedValue.Length > 0)
            {
                DSActivityRecordPlaceOfService dsARPlaceOfService = new DSActivityRecordPlaceOfService();
                dsARPlaceOfService.ActivityRecordID = activityRecordIDOfNewItem;
                dsARPlaceOfService.LstUpdateUsername = getUsername();
                dsARPlaceOfService.PlaceOfServiceID = RadComboBoxPlaceOfService.SelectedValue;
                BOActivityRecordPlaceOfService.createActivityRecordPlaceOfService(dsARPlaceOfService, getClientID());
            }
        }

        if (Privilege.isPriv6True(iAccess, Privileges6.ENABLE_SERVICE_PHASES))
        {
            if (RadComboBoxIndividualServicePhase.SelectedValue.Length > 0)
                BOActivityRecord.createActivityRecordServicePhase(System.Convert.ToInt32(clientID), Int32.Parse(activityRecordIDOfNewItem), RadComboBoxIndividualServicePhase.SelectedValue, getUsername());
        }

        if (Privilege.isPrivTrue(iAccess, Privileges7.UPDATE_COST_CENTERS_BASED_ON_ARS))
        {
            BOActivityRecord.setARUpdateCostCenterForNonBillArFollowingBillableByARID(getClientID(), activityRecordIDOfNewItem);
        }
        
        // TODO:  This has been causing random deadlocks. Not sure why yet. Putting at bottom to prevent it from hurting everything else.
        BOForms.setFormResultUpdateTemporary(HiddenCURRENTWINDOWCLIENTID.Value, HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, "ACTIVITY_RECORD", "ACTIVITY_RECORD", getUsername());

        return true;
    }

    private bool createConsumerWorkActivityRecord()
    {
        if (!chkBoxCreateConsumerAtWorkRecord.Checked)
        {
            return true;
        }
        else
        {
            long[] iAccess = getUserPrivs();

            if (RadTimePickerFromConsumerAtWork == null || RadTimePickerFromConsumerAtWork.SelectedTime == null ||
                RadTimePickerToConsumerAtWork == null || RadTimePickerToConsumerAtWork.SelectedTime == null)
            {
                lblStatusUpdateWAI.Text = I18NHelper.I18NString("*Consumer at Work records must have valid time frame.");
                return false;
            }

            String clientID = HiddenCURRENTWINDOWCLIENTID.Value;
            String userName = getUsername();

            String activityRecordIDOfNewItem = "";

            DateTime startTime = ActivityRecordsMisc.getDateTimeByRadTimePickerAndRadScheduler(RadTimePickerFromConsumerAtWork, RadSchedulerDayWAI);
            DateTime endTime = ActivityRecordsMisc.getDateTimeByRadTimePickerAndRadScheduler(RadTimePickerToConsumerAtWork, RadSchedulerDayWAI);

            if (!ActivityRecordsMisc.isValidStartAndEndTime(startTime, ref endTime, ref lblStatusUpdateWAI))
            {
                return false;
            }

            String activityRecordTypeID = "";
            DSActivityRecordType[] activityRecordTypes = BOActivityRecord.getActivityRecordTypesAllByClientID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
            for (int i = 0; i < activityRecordTypes.Length; i++)
            {
                if (activityRecordTypes[i].getCode.CompareTo("CONSUMER_WORK") == 0)
                {
                    activityRecordTypeID = activityRecordTypes[i].getActivityRecordTypeID;
                }
            }

            if (BOActivityRecord.isActivityRecordsOverlap(clientID, "", startTime, endTime, HiddenBILLABLECAPAR_CONSUMER_ID.Value, getUserID(), activityRecordTypeID))
            {
                lblStatusUpdateWAI.Text = I18NHelper.I18NString("*Duplicate Consumer At Work Record detected, please adjust manually.");
                return false;
            }

            String justificationTxt = HttpUtility.HtmlEncode(RadTextBoxAdditionalComments.Text);
            double hours = ActivityRecordsMisc.calculateHrs(startTime, endTime);

            String selectedConsumerValue = "";
            if (RadComboBoxConsumerWAI.SelectedValue != null && RadComboBoxConsumerWAI.SelectedValue.Length > 0)
            {
                selectedConsumerValue = RadComboBoxConsumerWAI.SelectedValue;
            }

            String selectedPlacementValue = "";
            if (RadComboBoxPlacementWAI.SelectedValue != null && RadComboBoxPlacementWAI.SelectedValue.Length > 0)
            {
                selectedPlacementValue = RadComboBoxPlacementWAI.SelectedValue;
            }
            String authorizationDropDownValue = RadComboBoxAuthorizationWAI.SelectedValue;

            if (Privilege.isPrivTrue(iAccess, Privileges17.ENABLE_TWELVE_TO_TWELVE_TWENTY_FOUR_HOUR_APPOINTMENTS))
                endTime = fixMidnightEndDate(startTime, endTime);

            activityRecordIDOfNewItem = BOActivityRecord.createActivityRecord(System.Convert.ToInt32(clientID), selectedConsumerValue, startTime,
                endTime, hours, "", "", false, "",
                "", "", justificationTxt, userName, getUserID(), "", false, false, false, selectedPlacementValue, activityRecordTypeID, "", "", "", "", false, "", (DateTime.UtcNow - DateTime.Parse(HiddenARTIMESPANSTART.Value)).Ticks, "");
            HiddenCONSUMERATWORKACTIVITYRECORDID.Value = activityRecordIDOfNewItem;

            DSActivityRecordAdditional dsActivityRecordAdditional = new DSActivityRecordAdditional
            {
                ClientID = getClientID().ToString(),
                ActivityRecordID = activityRecordIDOfNewItem,
                DepartmentID = RadComboBoxDepartmentWAI.SelectedValue,
                SecondaryAuthID = int.Parse(authorizationDropDownValue),
                LstUpdateUsername = getUsername()
            };

            BOActivityRecordAdditional.setActivityRecordAdditional(dsActivityRecordAdditional);

            DSAuthorization dsAuth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(authorizationDropDownValue), Int32.Parse(clientID));
            createARAdditional(dsAuth, userName);

            BOFile.setFileUpdateTemporary(getClientID().ToString(), HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, activityRecordIDOfNewItem, "BILLABLE_CAP_FILE", getUsername());

            BOForms.setFormResultUpdateTemporary(HiddenCURRENTWINDOWCLIENTID.Value, HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value, "ACTIVITY_RECORD", "ACTIVITY_RECORD", getUsername());

            updateTempSignatures();

            return true;
        }
    }

    private void saveARIAKeysAndGoals(String _activityRecordID)
    {

        // remove the current AR goals, add in all the new selections
        BOActivityRecordGoal.deleteActivityRecordGoalsByGoalID(_activityRecordID);

        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (foundControlID == null)
                continue;

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
            {
                //                    System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[i];
                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[i];
                if (tempNumericBox.Text.CompareTo("0") != 0)
                {
                    String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                    String keyID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));

                    if (foundControlID.Contains("ASSESSMENT"))
                    {
                        DSAssessmentKey tempKey = new DSAssessmentKey();
                        tempKey.getAssessmentKeyID = keyID;
                        tempKey.getKeyvalue = tempNumericBox.Text;
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID());
                    }
                    else if (foundControlID.Contains("INTERVENTION"))
                    {
                        DSInterventionKey tempKey = new DSInterventionKey();
                        tempKey.getInterventionKeyID = keyID;
                        tempKey.getKeyvalue = tempNumericBox.Text;
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID());
                    }
                }
            }
        }

        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (foundControlID == null)
                continue;

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("System.Web.UI.WebControls.CheckBox") == 0)
            {
                System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[i];
                //                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[i];
                if (tempCheckbox.Checked)
                {
                    String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                    String keyID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));

                    if (foundControlID.Contains("ASSESSMENT"))
                    {
                        DSAssessmentKey tempKey = new DSAssessmentKey();
                        tempKey.getAssessmentKeyID = keyID;
                        tempKey.getKeyvalue = "1";
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID());
                    }
                    else if (foundControlID.Contains("INTERVENTION"))
                    {
                        DSInterventionKey tempKey = new DSInterventionKey();
                        tempKey.getInterventionKeyID = keyID;
                        tempKey.getKeyvalue = "1";
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID()
                        );
                    }
                }
            }
        }

        BOActivityRecordMultiGoal.deleteActivityRecordMultiGoalByActivityRecordID(_activityRecordID);
        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (foundControlID == null)
                continue;

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadTextBox") == 0)
            {
                RadTextBox tempTextBox = (RadTextBox)SurveyGoalsWithEvaluations.Controls[i];
                // got the text box,  //                String controlID = "GOALID_" + activityRecordGoal.getGoal_ID;
                String goal = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                String goalID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));
                if (goal == "GOALID" && tempTextBox.Text.Length > 0 && tempTextBox.Text != tempTextBox.EmptyMessage)
                {
                    DSActivityRecordMultiGoal dsARMG = new DSActivityRecordMultiGoal();
                    dsARMG.getActivityRecordsID = _activityRecordID;
                    dsARMG.getGoalID = goalID;
                    dsARMG.getLstUpdateUsername = getUsername();
                    dsARMG.getSummary = tempTextBox.Text;
                    if (tempTextBox.Text.Length > 0)
                    {
                        BOActivityRecordMultiGoal.setActivityRecordMultiGoal(dsARMG, getClientID());
                    }
                }
            }
        }
    }

    private void saveNightShiftToHiddenField(DSStaffShift[] _staffShifts)
    {
        if (_staffShifts != null && _staffShifts.Length > 0)
        {
            foreach (DSStaffShift staffShift in _staffShifts)
            {
                DateTime shiftStartDate = getNightShift(staffShift, true);
                DateTime shiftEndDate = getNightShift(staffShift, false);

                if (HiddenNIGHTSHIFTSTART.Value.Length > 0)
                {
                    HiddenNIGHTSHIFTSTART.Value += ";;" + shiftStartDate.ToShortTimeString();
                    HiddenNIGHTSHIFTEND.Value += ";;" + shiftEndDate.ToShortTimeString();
                }
                else
                {
                    HiddenNIGHTSHIFTSTART.Value = shiftStartDate.ToShortTimeString();
                    HiddenNIGHTSHIFTEND.Value = shiftEndDate.ToShortTimeString();
                }
            }
        }
    }

    private void saveARCommentsAndGoalsOnCreate(String _activityRecordID)
    {



        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (foundControlID == null)
                continue;

            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadNumericTextBox") == 0)
            {
                //                    System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[i];
                Telerik.Web.UI.RadNumericTextBox tempNumericBox = (Telerik.Web.UI.RadNumericTextBox)SurveyGoalsWithEvaluations.Controls[i];

                if (tempNumericBox.Text.CompareTo("0") != 0)
                {
                    //                    if (checkedBoxes.ContainsKey(foundControlID))
                    //                  {
                    String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                    String keyID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));

                    if (foundControlID.Contains("ASSESSMENT"))
                    {
                        DSAssessmentKey tempKey = new DSAssessmentKey();
                        tempKey.getAssessmentKeyID = keyID;
                        tempKey.getKeyvalue = tempNumericBox.Text;
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID());
                    }
                    else if (foundControlID.Contains("INTERVENTION"))
                    {
                        DSInterventionKey tempKey = new DSInterventionKey();
                        tempKey.getInterventionKeyID = keyID;
                        tempKey.getKeyvalue = tempNumericBox.Text;
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID());
                    }
                }
            }
            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("System.Web.UI.WebControls.CheckBox") == 0)
            {
                System.Web.UI.WebControls.CheckBox tempCheckbox = (System.Web.UI.WebControls.CheckBox)SurveyGoalsWithEvaluations.Controls[i];

                if (tempCheckbox.Checked)
                {
                    String goalID = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                    String keyID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));

                    if (foundControlID.Contains("ASSESSMENT"))
                    {
                        DSAssessmentKey tempKey = new DSAssessmentKey();
                        tempKey.getAssessmentKeyID = keyID;
                        tempKey.getKeyvalue = "1";
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID());
                    }
                    else if (foundControlID.Contains("INTERVENTION"))
                    {
                        DSInterventionKey tempKey = new DSInterventionKey();
                        tempKey.getInterventionKeyID = keyID;
                        tempKey.getKeyvalue = "1";
                        BOActivityRecordGoal.createActivityRecordGoal(_activityRecordID, goalID, tempKey, getClientID());
                    }
                }
            }


        }



        //        BOActivityRecordMultiGoal.deleteActivityRecordMultiGoalByActivityRecordID(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);

        for (int i = 0; i < SurveyGoalsWithEvaluations.Controls.Count; i++)
        {
            String foundControlID = SurveyGoalsWithEvaluations.Controls[i].ID;
            if (foundControlID == null)
                continue;


            if (SurveyGoalsWithEvaluations.Controls[i].GetType().ToString().CompareTo("Telerik.Web.UI.RadTextBox") == 0)
            {
                RadTextBox tempTextBox = (RadTextBox)SurveyGoalsWithEvaluations.Controls[i];
                // got the text box,  //                String controlID = "GOALID_" + activityRecordGoal.getGoal_ID;
                String goal = foundControlID.Substring(0, foundControlID.IndexOf("_", 0));
                String goalID = foundControlID.Substring(foundControlID.LastIndexOf("_") + 1, (foundControlID.Length - (foundControlID.LastIndexOf("_") + 1)));
                if (goal.CompareTo("GOALID") == 0)
                {
                    DSActivityRecordMultiGoal dsARMG = new DSActivityRecordMultiGoal();
                    dsARMG.getActivityRecordsID = _activityRecordID;
                    dsARMG.getGoalID = goalID;
                    dsARMG.getLstUpdateUsername = getUsername();

                    if (tempTextBox.Text.CompareTo(tempTextBox.EmptyMessage) == 0)
                    {
                        // empty message text, blank out
                        tempTextBox.Text = "";
                    }
                    dsARMG.getSummary = tempTextBox.Text;

                    if (tempTextBox.Text.Length > 0)
                    {
                        BOActivityRecordMultiGoal.setActivityRecordMultiGoal(dsARMG, getClientID());
                    }
                }
            }
        }

    }

    private bool updateARAdditional(DSAuthorization _dsAuth, String _userName)
    {
        DSActivityRecordAdditional additional = new DSActivityRecordAdditional();
        additional.IsNightAttendance = CheckboxNightAttendance.Checked;
        additional.IsLateNote = isLateNote();
        additional.LstUpdateUsername = _userName;
        additional.ActivityRecordID = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
        additional.DepartmentID = _dsAuth.getDepartmentID.ToString();
        int min = TravelTimeMinutes.Value == null ? 0 : (int)TravelTimeMinutes.Value;
        int hr = TravelTimeHours.Value == null ? 0 : (int)TravelTimeHours.Value;
        int travelTimeMinutes = min + (hr * 60);
        additional.TravelTimeMinutes = travelTimeMinutes;
        additional.FaceToFaceTimeMinutes = FaceToFaceTimeMinutes.Value == null ? 0 : (int)FaceToFaceTimeMinutes.Value + 
            (FaceToFaceTimeHours.Value == null ? 0 : (int)FaceToFaceTimeHours.Value * 60);
        additional.RecordKeepingTimeMinutes = RecordKeepingTimeMinutes.Value == null ? 0 : (int)RecordKeepingTimeMinutes.Value +
            (RecordKeepingTimeHours.Value == null ? 0 : (int)RecordKeepingTimeHours.Value * 60);
        additional.Miles = Miles.Value == null ? 0 : (int)Miles.Value;
        additional.CollaborationTimeMinutes = CollaborationTimeMinutes.Value == null ? 0 : (int)CollaborationTimeMinutes.Value +
            (CollaborationTimeHours.Value == null ? 0 : (int)CollaborationTimeHours.Value * 60);
        additional.TotalBillableTimeMinutes = TotalBillableTimeMinutes.Value == null ? 0 : (int)TotalBillableTimeMinutes.Value +
            (TotalBillableTimeHours.Value == null ? 0 : (int)TotalBillableTimeHours.Value * 60);
        additional.ClientID = getClientID().ToString();
        BOActivityRecordAdditional.setActivityRecordAdditional(additional);
        return true;

    }
    
    private bool createARAdditional(DSAuthorization _dsAuth, String _userName)
    {
        DSActivityRecordAdditional additional = new DSActivityRecordAdditional();
        additional.IsNightAttendance = CheckboxNightAttendance.Checked;
        additional.IsLateNote = isLateNote();
        additional.LstUpdateUsername = _userName;
        additional.ActivityRecordID = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
        additional.DepartmentID = _dsAuth.getDepartmentID.ToString();
        int min = TravelTimeMinutes.Value == null ? 0 : (int)TravelTimeMinutes.Value;
        int hr = TravelTimeHours.Value == null ? 0 : (int)TravelTimeHours.Value;
        int travelTimeMinutes = min + (hr * 60);
        additional.TravelTimeMinutes = travelTimeMinutes;
        additional.FaceToFaceTimeMinutes = FaceToFaceTimeMinutes.Value == null ? 0 : (int)FaceToFaceTimeMinutes.Value +
            (FaceToFaceTimeHours.Value == null ? 0 : (int)FaceToFaceTimeHours.Value * 60);
        additional.RecordKeepingTimeMinutes = RecordKeepingTimeMinutes.Value == null ? 0 : (int)RecordKeepingTimeMinutes.Value +
            (RecordKeepingTimeHours.Value == null ? 0 : (int)RecordKeepingTimeHours.Value * 60);
        additional.Miles = Miles.Value == null ? 0 : (int)Miles.Value;
        additional.CollaborationTimeMinutes = CollaborationTimeMinutes.Value == null ? 0 : (int)CollaborationTimeMinutes.Value +
            (CollaborationTimeHours.Value == null ? 0 : (int)CollaborationTimeHours.Value * 60);
        additional.TotalBillableTimeMinutes = TotalBillableTimeMinutes.Value == null ? 0 : (int)TotalBillableTimeMinutes.Value +
            (TotalBillableTimeHours.Value == null ? 0 : (int)TotalBillableTimeHours.Value * 60);
        additional.ClientID = getClientID().ToString();
        BOActivityRecordAdditional.setActivityRecordAdditional(additional);
        return true;
    }

	private bool allowOverlappingConsumerRecords()
	{
		long[] iAccess = getUserPrivs();
		if (Privilege.isPrivTrue(iAccess, Privileges10.ALLOW_OVERLAPPING_CONSUMER_BILLABLE_CAP_RECORDS))
		{
			return true;
		}
		return false;
	}

    #endregion
    
    #region Checkers
    private bool isActivityRecordConsumerAvailableAndOverlapCheckWAI()
    {
        if (isRecordOverlappingForStaff() && !isOverlappingARsAllowedForStaff())
        {
            setError("Error:  New Record is overlapping with another record for this staff member");
            return false;
        }

        if (!RadTimePickerFromWAI.SelectedDate.HasValue || !RadTimePickerToWAI.SelectedDate.HasValue)
        {
            setError(I18NHelper.I18NString("Begin and End Times are required"));
            return false;
        }
        
        DateTime timePickerFromDateTime = RadTimePickerFromWAI.SelectedDate.Value;
        DateTime timePickerToDateTime   = RadTimePickerToWAI.SelectedDate.Value;

        DateTime schedulerFromDateTime = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToShortDateString());
        DateTime schedulerToDateTime   = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToShortDateString());

        schedulerFromDateTime = schedulerFromDateTime.AddHours(timePickerFromDateTime.Hour);
        schedulerFromDateTime = schedulerFromDateTime.AddMinutes(timePickerFromDateTime.Minute);

        schedulerToDateTime = schedulerToDateTime.AddHours(timePickerToDateTime.Hour);
        schedulerToDateTime = schedulerToDateTime.AddMinutes(timePickerToDateTime.Minute);

        DateTime startTime = schedulerFromDateTime;
        DateTime endTime   = schedulerToDateTime;

        // need to take this into account for all places we work with time
        if (AreBothTimesTheSameMidnight(startTime, endTime))
        {
            endTime = endTime.AddDays(1);
        }

        if (!allowOverlappingConsumerRecords())
        {
            if (isConsumerAvailableForActivityRecord(RadComboBoxConsumerWAI.SelectedValue, startTime, endTime))
            {
                setError("");
                return true;
            }
            setError(string.Format("Error: New Record is overlapping with another record for {0}", HiddenCONSUMER_TERMINOLOGY.Value));
            return false;
        }

        setError("");
        return true;
    }

    private bool isOverlappingARsAllowedForStaff()
    {
        long[] iAccess = getUserPrivs();
        if (Privilege.isPriv1True(iAccess, Privileges.ALLOW_OVERLAPPING_ACTIVITYRECORDS))
        {
            return true;
        }
        return false;
    }

	private bool isOverlappingNonBillableRecordsAllowed()
	{
		long[] iAccess = getUserPrivs();
		if (Privilege.isPrivTrue(iAccess, Privileges10.PREVENT_STAFF_FROM_OVERLAPPING_NONBILLABLES))
		{
			return true;
		}
		return false;
	}

    private bool isRecordOverlappingForStaff()
    {
        //        String activityRecordIDForCurrent = activityRecordID;

        String activityRecordIDForCurrent = "";
        if (HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value != null)
        {
            activityRecordIDForCurrent = HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value;
        }

        DateTime temp = RadSchedulerDayWAI.SelectedDate;
        DateTime curARStartTime = new DateTime(temp.Date.Year, temp.Date.Month, temp.Date.Day,
            RadTimePickerFromWAI.SelectedDate.Value.Hour, RadTimePickerFromWAI.SelectedDate.Value.Minute, 0);

        DateTime curAREndTime = new DateTime(temp.Date.Year, temp.Date.Month, temp.Date.Day,
            RadTimePickerToWAI.SelectedDate.Value.Hour, RadTimePickerToWAI.SelectedDate.Value.Minute, 0);

        // RETURN ONLY BILLABLE HERE!!  -- old comment
		//Now adding a priv to check for nonbillable overlap
        DataTable table = null;
		bool checkForNonBillableOverlap = isOverlappingNonBillableRecordsAllowed();

		if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
		{
			table = checkForNonBillableOverlap ? BOActivityRecord.getActivityRecordTimesheetByDateRangeAndUserID(Guid.Parse(getUserID()), temp.AddDays(-1), temp.AddDays(1), checkForNonBillableOverlap) : BOActivityRecord.getActivityRecordTimesheetByDateRangeAndUserID(Guid.Parse(getUserID()), temp.AddDays(-1), temp.AddDays(1));
			//table = BOActivityRecord.getActivityRecordTimesheetByDateRangeAndUserID(Guid.Parse(getUserID()), temp.AddDays(-1), temp.AddDays(1));
		}
		else
		{
			// Retrieve userId of creator of activity record, instead of logged in user
			if (activityRecordIDForCurrent == null || activityRecordIDForCurrent.Length == 0)
			{
				setError("An unknown exception occorred. Please close and reopen the record.");
			}
			else
			{
				DSActivityRecord record = BOActivityRecord.GetActivityRecord(Int32.Parse(activityRecordIDForCurrent), getClientID());
				String creatorUserId = record.getCreatorUserID;
				if (HiddenTRANSFORMATION.Value == "TRUE")
				{
                    creatorUserId = HiddenBILLABLECAPAR_CREATOR_USER_ID.Value;
				}
				table = checkForNonBillableOverlap ? BOActivityRecord.getActivityRecordTimesheetByDateRangeAndUserID(Guid.Parse(creatorUserId), temp.AddDays(-1), temp.AddDays(1), checkForNonBillableOverlap) : BOActivityRecord.getActivityRecordTimesheetByDateRangeAndUserID(Guid.Parse(creatorUserId), temp.AddDays(-1), temp.AddDays(1));
            }
		}

        int START_DATE = 0;
        int END_DATE = 1;
        int ACTIVITY_RECORD_ID = 3;

        if (table != null)
        {
            for (int i = 0; i < table.Rows.Count; i++)
            {
                DateTime otherARStartDate = DateTime.Parse(table.Rows[i].ItemArray[START_DATE].ToString());
                DateTime otherAREndDate = DateTime.Parse(table.Rows[i].ItemArray[END_DATE].ToString());

                otherARStartDate = new DateTime(otherARStartDate.Year, otherARStartDate.Month, otherARStartDate.Day, otherARStartDate.Hour, otherARStartDate.Minute, 0);
                otherAREndDate = new DateTime(otherAREndDate.Year, otherAREndDate.Month, otherAREndDate.Day, otherAREndDate.Hour, otherAREndDate.Minute, 0);

                String activityRecordIDFound = table.Rows[i].ItemArray[ACTIVITY_RECORD_ID].ToString();

                if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.CREATE) == 0)
                {
                    if (curARStartTime < otherAREndDate && curAREndTime > otherARStartDate)
                        return true;
                }
                else
                {
                    if (activityRecordIDForCurrent.CompareTo(activityRecordIDFound) != 0)
                    {
                        if (curARStartTime < otherAREndDate && curAREndTime > otherARStartDate)
                            return true;
                    }
                }
            }
        }
        return false;
    }

    private Boolean isConsumerAvailableForActivityRecord(String _consumerID, DateTime _fromDate, DateTime _toDate)
    {

        bool allowCreationOfCAPActivityRecordAtSameTime = false;
        long[] iAccess = getUserPrivs();
        if (Privilege.isPriv2True(iAccess, Privileges2.ACTIVITY_RECORD_ALLOW_CAP_AND_VR_OVERLAP_FOR_DIFFERENT_STAFF))
        {
            allowCreationOfCAPActivityRecordAtSameTime = true;
        }

        // at this start respecting privs around creating ARs that could po
        //        String activityRecordIDForCurrent = activityRecordID;
        DateTime temp = RadSchedulerDayWAI.SelectedDate;
        DateTime startTime = new DateTime(temp.Date.Year, temp.Date.Month, temp.Date.Day,
            RadTimePickerFromWAI.SelectedDate.Value.Hour, RadTimePickerFromWAI.SelectedDate.Value.Minute, 0);

        DateTime endTime = new DateTime(temp.Date.Year, temp.Date.Month, temp.Date.Day,
            RadTimePickerToWAI.SelectedDate.Value.Hour, RadTimePickerToWAI.SelectedDate.Value.Minute, 0);

        DataTable table = BOActivityRecord.getActivityRecordByConsumerIDAndDateTime(_consumerID, _fromDate, _toDate, 1);
        DSActivityRecordType[] activityRecordTypes = BOActivityRecord.getActivityRecordTypesAllByClientID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));

        int ACTIVITY_RECORD_ID = 3;
        if (table.Rows.Count > 0)
        {
            // filter out items that match the AR in context
            foreach (DataRow row in table.Rows)
            {
                DSActivityRecord tempFetchActivityRecord = BOActivityRecord.GetActivityRecord(Int32.Parse(row.ItemArray[ACTIVITY_RECORD_ID].ToString()), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));

                // Ignore overlap if TCM records or Group record (since group will split up around it automatically on save)
                bool ignoreAR = false;
                for (int i = 0; i < activityRecordTypes.Length; i++)
                {
                    if (activityRecordTypes[i].getCode.CompareTo("ACTIVITYRECORD_BILLABLE_CM_NOTE") == 0 ||
                        activityRecordTypes[i].getCode.CompareTo("ACTIVITYRECORD_NONBILLABLE_CM_NOTE") == 0 ||
                        activityRecordTypes[i].getCode.CompareTo("ACTIVITYRECORD_NONBILLABLE_WITH_CONSUMER_CM_NOTE") == 0 ||
                        activityRecordTypes[i].getCode.CompareTo("ACTIVITYRECORD_BILLABLE_GROUP_APPOINTMENT") == 0)
                    {
                        if (activityRecordTypes[i].getActivityRecordTypeID == tempFetchActivityRecord.getActivityRecordType.getActivityRecordTypeID)
                        {
                            ignoreAR = true;
                            break;
                        }
                    }
                }

                if (ignoreAR)
                    continue;

                // If modify
                if (HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value.Length > 0)
                {
                    if (row.ItemArray[ACTIVITY_RECORD_ID].ToString().CompareTo(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value) == 0)
                    {
                        continue;
                    }
                    else
                    {
                        // Figure out if overlapping
                        if (allowCreationOfCAPActivityRecordAtSameTime)
                        {
                            String capTypeID = "0";

                            for (int i = 0; i < activityRecordTypes.Length; i++)
                            {
                                if (activityRecordTypes[i].getCode.CompareTo("ACTIVITYRECORD_BILLABLE_INTER_ASSESS_KEY") == 0)
                                {
                                    capTypeID = activityRecordTypes[i].getActivityRecordTypeID;
                                    continue;
                                }
                            }

                            if (tempFetchActivityRecord.getActivityRecordType.getActivityRecordTypeID.CompareTo(capTypeID) == 0)
                            {
                                DSActivityRecord arBeingModified = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                                if (tempFetchActivityRecord.getCreatorUserID.CompareTo(arBeingModified.getCreatorUserID) == 0)
                                {

                                    if (isAuthAllowedToOverlap(RadComboBoxAuthorizationWAI.SelectedValue, tempFetchActivityRecord.getAuth_ID.ToString()))
                                        continue;

                                    // same staff person reject!
                                    setError("You have already created a record for the selected consumer at the specified time.");
                                    return false;
                                }
                                else if (!isAuthAllowedToOverlap(RadComboBoxAuthorizationWAI.SelectedValue, tempFetchActivityRecord.getAuth_ID.ToString()))
                                {
                                    setError("The selected authorization can not overlap with the authorization used on the already existing Activity Record.");
                                    return false;
                                    // double check if I can bill against this auth or not
                                }
                                else
                                {
                                    continue;
                                }
                            }
                        }
                        else
                        {
                            if (isAuthAllowedToOverlap(RadComboBoxAuthorizationWAI.SelectedValue, tempFetchActivityRecord.getAuth_ID.ToString()))
                                continue;

                            setError("A record for the selected consumer has already been created by " + row.ItemArray[5].ToString() + " at the specified time.");
                            return false;
                        }
                    }
                }
                else
                {
                    // I'm creating a new AR
                    // in this case validate the staff is different from the exisiting to the new AR
                    if (allowCreationOfCAPActivityRecordAtSameTime)
                    {
                        // go through all existing ones
                        String capTypeID = "0";
                        for (int i = 0; i < activityRecordTypes.Length; i++)
                        {
                            if (activityRecordTypes[i].getCode.CompareTo("ACTIVITYRECORD_BILLABLE_INTER_ASSESS_KEY") == 0)
                            {
                                capTypeID = activityRecordTypes[i].getActivityRecordTypeID;
                            }
                        }

                        if (tempFetchActivityRecord.getActivityRecordType.getActivityRecordTypeID.CompareTo(capTypeID) == 0)
                        {
                            if (HiddenCURRENTWINDOWUSERID.Value.CompareTo(tempFetchActivityRecord.getCreatorUserID) == 0)
                            {
                                if (isAuthAllowedToOverlap(RadComboBoxAuthorizationWAI.SelectedValue, tempFetchActivityRecord.getAuth_ID.ToString()))
                                    continue;

                                // staff is the same reject
                                setError("You have already created a record for the selected consumer at the specified time.");
                                return false;
                            }
                            else if (!isAuthAllowedToOverlap(RadComboBoxAuthorizationWAI.SelectedValue, tempFetchActivityRecord.getAuth_ID.ToString()))
                            {
                                setError("The selected authorization can not overlap with the authorization used on the already existing Activity Record.");
                                return false;
                                // double check if I can bill against this auth or not
                            }
                            else
                            {
                                continue;
                            }
                        }
                    }
                    else
                    {
                        if (isAuthAllowedToOverlap(RadComboBoxAuthorizationWAI.SelectedValue, tempFetchActivityRecord.getAuth_ID.ToString()))
                            continue;

                        setError("A record for the selected consumer has already been created by " + row.ItemArray[5].ToString() + " at the specified time.");
                        return false;
                    }
                }
            }
        }

        setError("");
        return true;

    }

    private bool isAuthAllowedToOverlap(String _selectedAuth, String _existingAuth)
    {
        bool authIsAllowedtoOverlap = false;
        String[] authsAllowedToOverlap = BOAuthOverlap.getAuthOverlapByAuthID(_selectedAuth);

        for (int i = 0; i < authsAllowedToOverlap.Length; i++)
        {
            if (authsAllowedToOverlap[i].CompareTo(_existingAuth) == 0)
            {
                // the auth is allowed to overlap, ignore this one
                authIsAllowedtoOverlap = true;
                continue;
            }
        }
        return authIsAllowedtoOverlap;
    }

    private void processNightAttendanceCheckbox(long[] _privs, int _departmentID)
    {
        LINightAttendance.Visible = false;
        if (Privilege.isPriv6True(_privs, Privileges6.ALLOW_MANUALLY_SETTING_NIGHT_ATTENDANCE_ON_ARS))
        {
            LINightAttendance.Visible = true;
            CheckboxNightAttendance.Checked = false;

            DSStaffShift[] staffShifts = BOStaffShift.getNightShiftsForDepartment(getClientID(), _departmentID);

            if (staffShifts != null && staffShifts.Length > 0)
            {
                saveNightShiftToHiddenField(staffShifts);

                foreach (DSStaffShift _dsStaffShift in staffShifts)
                {
                    DateTime shiftStartDate = getNightShift(_dsStaffShift, true);
                    DateTime shiftEndDate = getNightShift(_dsStaffShift, false);

                    if (shiftStartDate != DateTime.MinValue)
                    {
                        DateTime recFromTime = RadTimePickerFromWAI.SelectedDate.Value;
                        DateTime recEndTime = RadTimePickerToWAI.SelectedDate.Value;

                        if (shiftStartDate <= recFromTime && shiftEndDate >= recEndTime)
                        {
                            CheckboxNightAttendance.Checked = true;
                        }
                    }
                }
            }
        }
    }

    private bool checkForSave()
    {
        if (!RadTimePickerFromWAI.SelectedDate.HasValue || !RadTimePickerToWAI.SelectedDate.HasValue)
        {
            lblStatusUpdateWAI.Text = I18NHelper.I18NString("Begin and End Times are required") + "<br />";
            ErrorDIV.Style.Add("display", "block");
            InformationDIV.Style.Add("display", "none");

            return false;
        }
        
        DateTime timePickerFromDateTime = RadTimePickerFromWAI.SelectedDate.Value;
        DateTime timePickerToDateTime = RadTimePickerToWAI.SelectedDate.Value;
        DateTime schedulerFromDateTime = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToShortDateString());
        DateTime startTime = schedulerFromDateTime.AddHours(timePickerFromDateTime.Hour).AddMinutes(timePickerFromDateTime.Minute);
        DateTime endTime = schedulerFromDateTime.AddHours(timePickerToDateTime.Hour).AddMinutes(timePickerToDateTime.Minute);

        DateTime featureSuspensionStartTime = ActivityRecordsMisc.getActivityRecordLockDateTimePastBack(getUserPrivs(), getUserID(), getClientID().ToString(), "BILLABLE_INDIVIDUAL");
        DateTime featureSuspensionEndTime = ActivityRecordsMisc.getActivityRecordLockDateTimeFutureToFuture(getUserPrivs(), getUserID(), getClientID().ToString(), "BILLABLE_INDIVIDUAL");

        bool disableForRecordLock = false;
        DSRecordLock recordLock = BORecordLock.GetRecordLockByClientID(getClientID());
        if (recordLock != null)
        {
            if ((recordLock.LockDateTime > startTime || recordLock.LockDateTime >= endTime) && recordLock.LockARDates)
            {
                disableForRecordLock = true;

                lblStatusUpdateWAI.Text = "The system has been locked to prevent changing the dates of activity records prior to " + recordLock.LockDateTime.ToShortDateString() + " at " + recordLock.LockDateTime.ToShortTimeString();
                ErrorDIV.Style.Add("display", "block");
                InformationDIV.Style.Add("display", "none");

                if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                {
                    disableForRecordLock = false;
                    ErrorDIV.Style.Add("display", "none");
                    InformationDIV.Style.Add("display", "none");
                    lblStatusUpdateWAI.Text = "";
                }
            }
            else if (recordLock.LockDateTime > startTime || recordLock.LockDateTime >= endTime)
            {
                disableForRecordLock = true;

                lblStatusUpdateWAI.Text = "The system has been locked to prevent the modification or creation of activity records prior to " + recordLock.LockDateTime.ToShortDateString() + " at " + recordLock.LockDateTime.ToShortTimeString();
                ErrorDIV.Style.Add("display", "block");
                InformationDIV.Style.Add("display", "none");
            }
            else if (lblInformation.Text.Length > 0 || lblStatusUpdateWAI.Text.Length > 0)
            {
                disableCapRecordNoTimePicker(false);

                ErrorDIV.Style.Add("display", "none");
                InformationDIV.Style.Add("display", "none");
            }
        }

        DSUserFeatureSuspension[] featureSuspension = BOUserFeatureSuspension.getUserFeatureSuspensionByFeatureCodeAndUserID("AR_PRIV_DAYS_FORWARD_AND_BACK_SUSPEND", getUserID(), getClientID());
        foreach (DSUserFeatureSuspension fs in featureSuspension)
        {
            if (fs.getSuspendedFrom == DateTime.MinValue && fs.getSuspendedTo == DateTime.MinValue)
            {
                if ((featureSuspensionEndTime != DateTime.MinValue && startTime > featureSuspensionEndTime) || (featureSuspensionStartTime != DateTime.MinValue && endTime < featureSuspensionStartTime))
                {
                    if (!fs.getChangeAllOtherDetailsOfAR)
                    {
                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            disableForRecordLock = false;
                            ErrorDIV.Style.Add("display", "none");
                            InformationDIV.Style.Add("display", "none");
                            lblStatusUpdateWAI.Text = "";
                        }
                        else
                        {
                            disableForRecordLock = true;
                            lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            ErrorDIV.Style.Add("display", "block");
                            InformationDIV.Style.Add("display", "none");
                        }
                    }
                    if (!fs.getChangeDateAndTimeOfAR)
                    {
                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            disableForRecordLock = false;
                            ErrorDIV.Style.Add("display", "none");
                            InformationDIV.Style.Add("display", "none");
                            lblStatusUpdateWAI.Text = "";
                        }
                        else
                        {
                            disableForRecordLock = true;
                            lblStatusUpdateWAI.Text = "Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            ErrorDIV.Style.Add("display", "block");
                            InformationDIV.Style.Add("display", "none");
                        }

                    }
                    else
                    {
                        RadTimePickerFromWAI.Enabled = true;
                        RadTimePickerToWAI.Enabled = true;

                        if (fs.getChangeAllOtherDetailsOfAR)
                        {
                            lblStatusUpdateWAI.Text = "";
                            ErrorDIV.Style.Add("display", "none");
                        }
                    }

                    if (!fs.getChangeDateAndTimeOfAR && !fs.getChangeAllOtherDetailsOfAR)
                    {
                        lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString() + "<br/>Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                        InformationDIV.Style.Add("display", "none");
                    }

                    if (fs.getChangeAllOtherDetailsOfAR && fs.getChangeDateAndTimeOfAR)
                    {
                        disableForRecordLock = false;
                        ErrorDIV.Style.Add("display", "none");
                        InformationDIV.Style.Add("display", "none");
                        lblStatusUpdateWAI.Text = "";
                    }
                }
                else
                {
                    disableForRecordLock = false;
                    lblStatusUpdateWAI.Text = "";
                    ErrorDIV.Style.Add("display", "none");
                }
            }
            else if (fs.getSuspendedFrom <= startTime && fs.getSuspendedTo >= endTime)
            {
                if ((featureSuspensionEndTime != DateTime.MinValue && startTime > featureSuspensionEndTime) || (featureSuspensionStartTime != DateTime.MinValue && endTime < featureSuspensionStartTime))
                {
                    if (!fs.getChangeAllOtherDetailsOfAR)
                    {
                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            disableForRecordLock = false;
                        }
                        else
                        {
                            disableForRecordLock = true;

                            lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            ErrorDIV.Style.Add("display", "block");
                            InformationDIV.Style.Add("display", "none");
                        }
                    }
                    if (!fs.getChangeDateAndTimeOfAR)
                    {
                        if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                        {
                            disableForRecordLock = false;
                        }
                        else
                        {
                            disableForRecordLock = true;

                            lblStatusUpdateWAI.Text = "Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                            ErrorDIV.Style.Add("display", "block");
                            InformationDIV.Style.Add("display", "none");
                        }
                    }
                    else
                    {
                        RadTimePickerFromWAI.Enabled = true;
                        RadTimePickerToWAI.Enabled = true;

                        if (fs.getChangeAllOtherDetailsOfAR)
                        {
                            lblStatusUpdateWAI.Text = "";
                            ErrorDIV.Style.Add("display", "none");
                        }
                    }
                    if (!fs.getChangeDateAndTimeOfAR && !fs.getChangeAllOtherDetailsOfAR)
                    {
                        lblStatusUpdateWAI.Text = "Cannot modify the details of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString() + "<br/>Cannot modify the date of this activity record before " + featureSuspensionStartTime.ToShortDateString() + " at " + featureSuspensionStartTime.ToShortTimeString() + " or after " + featureSuspensionEndTime.ToShortDateString() + " at " + featureSuspensionEndTime.ToShortTimeString();
                        ErrorDIV.Style.Add("display", "block");
                    }

                    if (fs.getChangeDateAndTimeOfAR && fs.getChangeAllOtherDetailsOfAR)
                    {
                        disableForRecordLock = false;
                        ErrorDIV.Style.Add("display", "none");
                        InformationDIV.Style.Add("display", "none");
                        lblStatusUpdateWAI.Text = "";
                    }
                }
                else
                {
                    disableForRecordLock = false;
                    lblStatusUpdateWAI.Text = "";
                    ErrorDIV.Style.Add("display", "none");
                }
            }
            else if (lblStatusUpdateWAI.Text.Length > 0 || lblInformation.Text.Length > 0)
            {
                if (recordLock == null)
                {
                    lblStatusUpdateWAI.Text = "";
                }
                disableCapRecordNoTimePicker(false);
                ErrorDIV.Style.Add("display", "none");
                InformationDIV.Style.Add("display", "none");
            }
        }
        return disableForRecordLock;
    }

    private bool isValidatorsValid()
    {
        string error = "";
        long[] iAccess = getUserPrivs();

        if (RadComboBoxDepartmentWAI.Text == null || RadComboBoxDepartmentWAI.Text.Length == 0 || RadComboBoxDepartmentWAI.SelectedValue == null || RadComboBoxDepartmentWAI.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxDepartmentWAI.SelectedValue) >= 0))
            error = error + I18NHelper.I18NString("Department is required") + "<br />";

        if (RadComboBoxConsumerWAI.Text == null || RadComboBoxConsumerWAI.Text.Length == 0 || RadComboBoxConsumerWAI.SelectedValue == null || RadComboBoxConsumerWAI.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxConsumerWAI.SelectedValue) > 0))
            error = error + I18NHelper.I18NString("Consumer is required") + "<br />";

        if (RadComboBoxAuthorizationWAI.Text == null || RadComboBoxAuthorizationWAI.Text.Length == 0 || RadComboBoxAuthorizationWAI.SelectedValue == null || RadComboBoxAuthorizationWAI.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue) > 0))
            error = error + I18NHelper.I18NString("Authorization is required") + "<br />";

        if (RadTextBoxComment.Visible && RadTextBoxComment.Text.Length == 0)
            error = error + I18NHelper.I18NString("Comment is required") + "<br />";

        if (Privilege.isPriv6True(iAccess, Privileges6.PLACE_OF_SERVICE_IS_APPLICABLE) && Privilege.isPrivTrue(iAccess, Privileges8.PLACE_OF_SERVICE_REQUIRED_BILLABLE_CAP) && (RadComboBoxPlaceOfService.SelectedValue == null || RadComboBoxPlaceOfService.SelectedValue.Length == 0))
        {
            error = error + I18NHelper.I18NString("You must specify a valid place of service") + "<br />";
        }

        if (Privilege.isPriv6True(iAccess, Privileges6.ENABLE_SERVICE_PHASES))
        {
            if (Privilege.isPrivTrue(iAccess, Privileges8.ENABLE_VALIDATION_ON_SERVICE_PHASES))
            {
                if (RadComboBoxIndividualServicePhase.Items.Count > 0)
                {
                    if (RadComboBoxIndividualServicePhase.SelectedValue == null || RadComboBoxIndividualServicePhase.SelectedValue.CompareTo("") == 0 || !(Int32.Parse(RadComboBoxIndividualServicePhase.SelectedValue) > 0))
                        error = error + I18NHelper.I18NString("Phase is required") + "<br />";
                }
            }
        }
        
        if (TimeWithScrolling.Visible)
        {
            if (RadNumericTextFromHours.Text.Length == 0 || RadNumericTextFromMinutes.Text.Length == 0 || RadNumericTextToHours.Text.Length == 0 || RadNumericTextToMinutes.Text.Length == 0)
            {
                error = error + I18NHelper.I18NString("Begin and End Times are required") + "<br />";
            }
        }

        if (Privilege.isPriv5True(iAccess, Privileges5.MAKE_ACTIVITY_RECORDS_CAP_TYPE_ADDITIONAL_COMMENTS_REQUIRED))
        {
            if (RadTextBoxAdditionalComments.Text.CompareTo(RadTextBoxAdditionalComments.EmptyMessage) == 0 || RadTextBoxAdditionalComments.Text.Length == 0)
            {
                error = error + I18NHelper.I18NString("Additional Comments are required") + "<br />";
            }
        }

        // Only really possible when can type into the boxes
        if (RadTimePickerFromWAI.SelectedDate == null)
            error = error + I18NHelper.I18NString("The selected 'From' time is invalid") + "<br />";

        if (RadTimePickerToWAI.SelectedDate == null)
            error = error + I18NHelper.I18NString("The selected 'To' time is invalid") + "<br />";
        
        if (Privilege.isPriv6True(iAccess, Privileges6.ALLOW_LOGGING_ON_ADDITIONAL_SUPPORTS) && Privilege.isPrivTrue(iAccess, Privileges16.REQUIRE_AT_LEAST_ONE_ADDITIONAL_SUPPORTS))
        {
            string[] additionalSupportIDs = RadComboBoxAdditionalSupports.CheckedItems.Select(item => item.Value).ToArray();
            if (RadComboBoxAdditionalSupports.Items.Count > 0 && additionalSupportIDs.Length == 0)
            {
                error = error + I18NHelper.I18NString("At least one additional support must be selected") + "<br />";
            }
        }

        setError(error);

        return error.CompareTo("") == 0;
    }

    private bool isAbleToCreateConsumerWorkActivityRecord()
    {

        if (!chkBoxCreateConsumerAtWorkRecord.Checked)
        {
            lblStatusUpdateWAI.Text = "";
            return true;
        }

        if (RadTimePickerFromConsumerAtWork == null || RadTimePickerFromConsumerAtWork.SelectedTime == null || RadTimePickerFromConsumerAtWork.SelectedDate == null || RadTimePickerFromConsumerAtWork.SelectedDate.Value == null ||
            RadTimePickerToConsumerAtWork == null || RadTimePickerToConsumerAtWork.SelectedTime == null || RadTimePickerToConsumerAtWork.SelectedDate == null || RadTimePickerToConsumerAtWork.SelectedDate.Value == null)
        {
            lblStatusUpdateWAI.Text = I18NHelper.I18NString("*Consumer at Work records must have valid time frame.");
            return false;
        }

        DateTime startTime = ActivityRecordsMisc.getDateTimeByRadTimePickerAndRadScheduler(RadTimePickerFromConsumerAtWork, RadSchedulerDayWAI);
        DateTime endTime = ActivityRecordsMisc.getDateTimeByRadTimePickerAndRadScheduler(RadTimePickerToConsumerAtWork, RadSchedulerDayWAI);

        String[] arIDs = BOActivityRecord.getActivityRecordIDsByDateRangeAndConsumerIDAndActivityRecordTypeCode(getClientID().ToString(), RadComboBoxConsumerWAI.SelectedValue, startTime, endTime, "CONSUMER_WORK");
        if (arIDs.Length > 0)
        {
            lblStatusUpdateWAI.Text = I18NHelper.I18NString("*Consumer at Work records already exist for the selected time frame.");
            return false;
        }
        return true;
    }

    private bool isLateNote()
    {

        bool retVal = false;
        long[] iAccess = getUserPrivs();
        string[] roles = BORoles.GetRolesByUserID(Guid.Parse(getUserID()));
        String userRole = roles[0];
        String cmDaysBeforeRecordIsLate = BOPrivilege.getPrivilegeValueByPrivilegeCodeAndClientID("BILLABLE_CAP_NARRATIVE_INT_DAYS_FOR_LATE_RECORD", getClientID().ToString(), userRole);

        if (cmDaysBeforeRecordIsLate != null && cmDaysBeforeRecordIsLate.Length > 0)
        {
            int daysLimit = 0;
            if (Int32.TryParse(cmDaysBeforeRecordIsLate, out daysLimit))
            {
                DateTime endTime = RadSchedulerDayWAI.SelectedDate.Date;
                if (RadTimePickerToWAI.SelectedTime != null)
                    endTime = endTime.Add(RadTimePickerToWAI.SelectedTime.Value);
                DateTime relativeCurrentTime = TimeZoneProcessor.getLocalDateTime(getClientID().ToString());

                if (HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
                {
                    DSActivityRecord activityRecord = BOActivityRecord.GetActivityRecord(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getClientID());
                    if (!String.IsNullOrEmpty(activityRecord.getCreatedDateTime.ToString()))
                    {
                        relativeCurrentTime = TimeZoneProcessor.convertDateTimeToClientTimeZone(getClientID().ToString(), activityRecord.getCreatedDateTime);

                        if (String.IsNullOrEmpty(labelLate.Text))
                        {
                            endTime = activityRecord.getEndDate;
                        }
                    }
                }

                if (Privilege.isPrivTrue(iAccess, Privileges7.AR_LIMIT_PAST_DAYS_INTEGER_EXCLUDES_HOLIDAYS))
                {
                    daysLimit = BODateProcessor.getDateDiffHolidayOffset(getClientID().ToString(), relativeCurrentTime.Date, daysLimit, true);
                }

                if (relativeCurrentTime != DateTime.MinValue)
                {
                    relativeCurrentTime = relativeCurrentTime.AddDays(daysLimit * -1);
                }

                // start time is less than relative current time
                if (endTime <= relativeCurrentTime)
                {
                    retVal = true;
                }
                else
                {
                    retVal = false;
                }
            }
        }
        return retVal;
    }

    private bool useSingleRecordPerDayPrivOrForClient_CO(long[] iAccess, DateTime _datetime)
    {
        // Either if priv is on, or if Community Opportunities, then have it act as if on prior to 10/1/2015
        return Privilege.isPriv5True(iAccess, Privileges5.CAP_AR_DAY_KEEPS_STICKING_TO_PRIMARY_RECORD) || (HiddenCURRENTWINDOWCLIENTID.Value.Equals("86") && _datetime < new DateTime(2015, 10, 1));
    }

    private bool isValidTime(ref DateTime _startTime, ref DateTime _endTime)
    {
        if (_startTime > _endTime)
        {
            if (_endTime.Hour == 0 && _endTime.Minute == 0 && _endTime.Second == 0)
            {
                // it's OK if the end time is midnight, otherwise bail
                _endTime = _endTime.AddDays(1);
            }
            else
            {
                setError("The start time is greater than the end time");
                return false;
            }
        }
        else
        {
            setError("");
        }
        return true;
    }
#endregion

    #region  GettersAndSetters
    
    private void setError(bool isError)
    {
        if (isError)
        {
            ErrorDIV.Style.Add("display", "block");
        }
        else
        {
            ErrorDIV.Style.Add("display", "none");
        }
    }

    private void setError(string error)
    {
        if (error == "")
        {
            ErrorDIV.Style.Add("display", "none");
            lblStatusUpdateWAI.Text = error;
        }
        else
        {
            setInformationLabelVisibility(false);
            ErrorDIV.Style.Add("display", "block");
            lblStatusUpdateWAI.Text = error;
        }
    }
    
    private void setInformation(string information)
    {
        if (information == "")
        {
            InformationDIV.Style.Add("display", "none");
            lblInformation.Text = information;
        }
        else
        {
            InformationDIV.Style.Add("display", "block");
            lblInformation.Text = information;
        }
    }

    private void setLate(DSActivityRecordAdditional dsActivityRecordAdditional)
    {
        if (String.IsNullOrEmpty(labelLate.Text))
        {
            if (dsActivityRecordAdditional != null && dsActivityRecordAdditional.IsLateNote && HiddenACTION_TYPEWAI.Value.CompareTo(Constants.MODIFY) == 0)
            {
                lateRecordDiv.Style.Add("display", "block");
                labelLate.Text = "This note was submitted late";
            }
            else if (isLateNote())
            {
                lateRecordDiv.Style.Add("display", "block");
                labelLate.Text = "This note is late";
            }
            else
            {
                lateRecordDiv.Style.Add("display", "none");
                labelLate.Text = "This note is not late";
            }
        }
        else
        {
            if (isLateNote())
            {
                lateRecordDiv.Style.Add("display", "block");
                labelLate.Text = "This note is late";
            }
            else
            {
                lateRecordDiv.Style.Add("display", "none");
                labelLate.Text = "This note is not late";
            }
        }
    }
    
    protected void setInformationLabelVisibility(bool _show)
    {
        if (_show)
        {
            InformationDIV.Style.Add("display", "block");
        }
        else
        {
            InformationDIV.Style.Add("display", "none");
        }
    }

    private DateTime getStartTime()
    {
        DateTime timePickerFromDateTime = RadTimePickerFromWAI.SelectedDate.Value;
        DateTime schedulerFromDateTime = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToShortDateString());
        schedulerFromDateTime = schedulerFromDateTime.AddHours(timePickerFromDateTime.Hour);
        schedulerFromDateTime = schedulerFromDateTime.AddMinutes(timePickerFromDateTime.Minute);

        return schedulerFromDateTime;
    }

    private DateTime getEndTime()
    {
        DateTime timePickerToDateTime = RadTimePickerToWAI.SelectedDate.Value;
        DateTime schedulerToDateTime = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToShortDateString());
        schedulerToDateTime = schedulerToDateTime.AddHours(timePickerToDateTime.Hour);
        schedulerToDateTime = schedulerToDateTime.AddMinutes(timePickerToDateTime.Minute);

        return schedulerToDateTime;
    }

    private DateTime getNightShift(DSStaffShift _staffShift, bool _startTime)
    {
        DateTime recDate = RadSchedulerDayWAI.SelectedDate;
        DateTime shiftStartDate = DateTime.MinValue;
        DateTime shiftEndDate = DateTime.MinValue;

        switch (recDate.DayOfWeek)
        {
            case DayOfWeek.Monday:
                shiftStartDate = _staffShift.getMonStartTime;
                shiftEndDate = _staffShift.getMonEndTime;
                break;
            case DayOfWeek.Tuesday:
                shiftStartDate = _staffShift.getTueStartTime;
                shiftEndDate = _staffShift.getTueEndTime;
                break;
            case DayOfWeek.Wednesday:
                shiftStartDate = _staffShift.getWedStartTime;
                shiftEndDate = _staffShift.getWedEndTime;
                break;
            case DayOfWeek.Thursday:
                shiftStartDate = _staffShift.getThuStartTime;
                shiftEndDate = _staffShift.getThuEndTime;
                break;
            case DayOfWeek.Friday:
                shiftStartDate = _staffShift.getFriStartTime;
                shiftEndDate = _staffShift.getFriEndTime;
                break;
            case DayOfWeek.Saturday:
                shiftStartDate = _staffShift.getSatStartTime;
                shiftEndDate = _staffShift.getSatEndTime;
                break;
            case DayOfWeek.Sunday:
                shiftStartDate = _staffShift.getSunStartTime;
                shiftEndDate = _staffShift.getSunEndTime;
                break;
        }

        return _startTime ? shiftStartDate : shiftEndDate;
    }

    private int getMinARIDPrimary(String _consumerID, String _authID, String _creatorUserID, DateTime _dateTime)
    {
        String[] activityRecordIDS = BOActivityRecord.getActivityRecordIDsByDateRangeAndConsumerIDAndAuthIDAndStaffIDCAPType(getClientID().ToString(), _consumerID,
            _authID, _creatorUserID, _dateTime, _dateTime);

        int smallestInt = 0;
        for (int i = 0; i < activityRecordIDS.Length; i++)
        {
            String foundARID = activityRecordIDS[i];
            int arID = Int32.Parse(foundARID);
            if (smallestInt == 0)
            {
                smallestInt = arID;
            }
            else if (arID < smallestInt)
            {
                smallestInt = arID;
            }
        }
        return smallestInt;
    }

    private String getPlacementID(String _authorizationValue, DateTime _startTime)
    {
        String placementID = "NULL";
        // take the placementID from the placementCombo
        if (RadComboBoxPlacementWAI.SelectedValue.Length > 0)
        {
            placementID = RadComboBoxPlacementWAI.SelectedValue;
        }
        else
        {
            DataTable dt = BOPlacement.getPlacementsByConsumerIDAndDateOnlyActive(_startTime, Int32.Parse(RadComboBoxConsumerWAI.SelectedValue));
            int placementCount = dt.Rows.Count;
            if (placementCount == 0)
            {
                placementID = "NULL";
            }
        }

        return placementID;
    }

    private String getActivityRecordTypeID()
    {
        String activityRecordTypeID = "";
        DSActivityRecordType[] activityRecordTypes = BOActivityRecord.getActivityRecordTypesAllByClientID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
        for (int i = 0; i < activityRecordTypes.Length; i++)
        {
            if (activityRecordTypes[i].getCode.CompareTo(DSActivityRecordType.ACTIVITYRECORD_BILLABLE_INTER_ASSESS_KEY) == 0)
            {
                activityRecordTypeID = activityRecordTypes[i].getActivityRecordTypeID;
            }
        }
        return activityRecordTypeID;
    }
    
    
    
    #endregion
    
    #region Signatures
    private void LoadSignatures()
    {
        SignatureList.Attributes.Add("data-elementid", ActivityRecordID);
        SignatureList.Attributes.Add("data-userid", getUserID());
        RadAjaxManagerBillableWAI.ResponseScripts.Add("sw.signatures.refresh()");
    }

    protected void deleteTempSignatures()
    {
        IList<SignatureRequestDTO> signatures = SigService.GetSignatureRequestsForActivityRecord(getClientID(), Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

        foreach (SignatureRequestDTO sig in signatures)
        {
            SigService.DeleteARSignature(sig.SignatureRequestID);
        }
    }

    protected void updateTempSignatures()
    {
        IList<SignatureRequestDTO> signatures = SigService.GetSignatureRequestsForActivityRecord(getClientID(), Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value));

        foreach (SignatureRequestDTO sig in signatures)
        {
            SigService.UpdateTempSignatureID(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), sig.SignatureRequestID);
        }
    }

    #endregion
    
    protected void disableCapRecord(bool disableARWindow)
    {
        rebuildDynamicControlsWithCurrentSelection(disableARWindow);
        SurveyGoalsWithEvaluations.Enabled = !disableARWindow;
        RadComboBoxAuthorizationWAI.Enabled = !disableARWindow;
        LabelUserNameWAI.Enabled = !disableARWindow;
        RadComboBoxDepartmentWAI.Enabled = !disableARWindow;
        RadComboBoxConsumerWAI.Enabled = !disableARWindow;
        RadTimePickerFromWAI.Enabled = !disableARWindow;
        RadTimePickerToWAI.Enabled = !disableARWindow;
        RadComboBoxPlacementWAI.Enabled = !disableARWindow;
        RadTextBoxComment.ReadOnly = disableARWindow;
        RadTextBoxAdditionalComments.ReadOnly = disableARWindow;
        RadSchedulerDayWAI.ReadOnly = !disableARWindow;
        RadSchedulerDayWAI.Rebind();
        HiddenEXPENSEDISABLECAP.Value = disableARWindow.ToString();
        RadComboBoxPlaceOfService.Enabled = !disableARWindow;
        RadComboBoxIndividualServicePhase.Enabled = !disableARWindow;
        RadComboBoxAdditionalSupports.Enabled = !disableARWindow;
    }

    protected void disableCapRecordNoTimePicker(bool disableARWindow, bool disableLinks = true)
    {
        rebuildDynamicControlsWithCurrentSelection(disableARWindow);
        SurveyGoalsWithEvaluations.Enabled = !disableARWindow;
        RadComboBoxAuthorizationWAI.Enabled = !disableARWindow;
        LabelUserNameWAI.Enabled = !disableARWindow;
        RadComboBoxDepartmentWAI.Enabled = !disableARWindow;
        RadComboBoxConsumerWAI.Enabled = !disableARWindow;
        RadComboBoxPlacementWAI.Enabled = !disableARWindow;
        if (disableLinks)
        {
            LinkButtonSave.Enabled = !disableARWindow;
            LinkButtonUpdate.Enabled = !disableARWindow;
            LinkButtonUpdate.Visible = !disableARWindow;
            LinkButtonDuplicate.Enabled = !disableARWindow;
            LinkButtonUpdateAndDuplicate.Enabled = !disableARWindow;
            LinkButtonManagerUpdateAndSign.Enabled = !disableARWindow;
            LinkButtonStaffUpdateAndSign.Enabled = !disableARWindow;
            LinkButtonSaveAndDuplicate.Enabled = !disableARWindow;
            LinkButtonManagerSaveAndSign.Enabled = !disableARWindow;
            LinkButtonStaffSaveAndSign.Enabled = !disableARWindow;
        }
        else
        {
            LinkButtonSave.Enabled = true;
            LinkButtonUpdate.Enabled = true;
            LinkButtonUpdate.Visible = true;
            LinkButtonDuplicate.Enabled = true;
            LinkButtonUpdateAndDuplicate.Enabled = true;
            LinkButtonManagerUpdateAndSign.Enabled = true;
            LinkButtonStaffUpdateAndSign.Enabled = true;
            LinkButtonSaveAndDuplicate.Enabled = true;
            LinkButtonManagerSaveAndSign.Enabled = true;
            LinkButtonStaffSaveAndSign.Enabled = true;
        }

        RadTextBoxComment.ReadOnly = disableARWindow;
        RadTextBoxAdditionalComments.ReadOnly = disableARWindow;
        RadSchedulerDayWAI.ReadOnly = !disableARWindow;
        RadSchedulerDayWAI.Rebind();
        HiddenEXPENSEDISABLECAP.Value = disableARWindow.ToString();
        RadComboBoxPlaceOfService.Enabled = !disableARWindow;
        RadComboBoxIndividualServicePhase.Enabled = !disableARWindow;
        RadComboBoxAdditionalSupports.Enabled = !disableARWindow;
        chkBoxCreateConsumerAtWorkRecord.Enabled = !disableARWindow;
        RadNumericTextFromHours.Enabled = !disableARWindow;
        RadioButtonListFromAMPM.Enabled = !disableARWindow;
        RadNumericTextToHours.Enabled = !disableARWindow;
        RadNumericTextToMinutes.Enabled = !disableARWindow;
        RadioButtonListToAMPM.Enabled = !disableARWindow;
        CheckboxNightAttendance.Enabled = !disableARWindow;
        chckBoxOffSite.Enabled = !disableARWindow;
        chkBoxFaceToFace.Enabled = !disableARWindow;
        TravelTimeHours.Enabled = !disableARWindow;
        TravelTimeMinutes.Enabled = !disableARWindow;
        FaceToFaceTimeHours.Enabled = !disableARWindow;
        FaceToFaceTimeMinutes.Enabled = !disableARWindow;
        RecordKeepingTimeHours.Enabled = !disableARWindow;
        RecordKeepingTimeMinutes.Enabled = !disableARWindow;
        Miles.Enabled = !disableARWindow;
        CollaborationTimeHours.Enabled = !disableARWindow;
        CollaborationTimeMinutes.Enabled = !disableARWindow;
        TotalBillableTimeHours.Enabled = !disableARWindow;
        TotalBillableTimeMinutes.Enabled = !disableARWindow;
        RadTextBoxAdditionalComments.ReadOnly = disableARWindow;
        RadComboBoxAdditionalSupports.Enabled = !disableARWindow;
    }

    protected void deleteActivityRecordsGroup_Locations()
    {
        List<ActivityRecordsGroupLocationDTO>
            arges = ActivityRecordService.getActivityRecordsGroupLocation(Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getClientID());
        if (arges.Count > 0)
        {
            setInformation("Any previously created Community Outings have been deleted");
            ActivityRecordService.deleteActivityRecordsGroupLocationByArID(Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value),getClientID());
        }
    }

    protected void updateActivityRecordsGroup_Locations()
    {
        List<ActivityRecordsGroupLocationDTO> arges = 
            ActivityRecordService.getActivityRecordsGroupLocation(
                Int32.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), getClientID());
        foreach (ActivityRecordsGroupLocationDTO arge in arges)
        {
            ActivityRecordService.updateTempActivityRecordsGroupLocation(Int32.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value), arge.ActivityRecordsGroupLocationId);
        }
    }

    protected bool isGoalsDocumentedComplete(String goalsDocumented)
    {
        bool recordIsComplete = true;
        if (goalsDocumented.Length <= 200)
        {
            recordIsComplete = !goalsDocumented.ToUpper().Contains(">INCOMPLETE<");
        }
        else
        {
            recordIsComplete = !goalsDocumented.Substring(0, 200).ToUpper().Contains(">INCOMPLETE<");
        }

        return recordIsComplete;
    }

    protected bool validateSignStatus_IsRecordInFuture(int _clientID, long[] _userPrivs, DateTime _AREndTime)
    {
        if (Privilege.isPriv3True(_userPrivs, Privileges3.AR_MUST_BE_PASSED_TIME_TO_SIGN))
        {
            DateTime dateTimeLocal = TimeZoneProcessor.getLocalDateTime(_clientID.ToString());
            if (_AREndTime == null)
                return false;

            if (_AREndTime > dateTimeLocal)
            {
                return true;
            }
        }
        return false;
    }

    protected bool ValidateFormRequirement()
    {
        Guid userID = Guid.Parse(getUserID());
        if(string.IsNullOrEmpty(RadComboBoxAuthorizationWAI.SelectedValue))
        {
            setError("Authorization is required");
            return false;
        }
        DSAuthorization authorization = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
        
        DSConsumer consumer = new DSConsumer();
        consumer.FullName = RadComboBoxConsumerWAI.Text;
        consumer.getID = Int32.Parse(RadComboBoxConsumerWAI.SelectedValue);

        List<DSProfile> users = new List<DSProfile>();
        DSProfile user = new DSProfile();
        user.User_ID = userID;
        user.FullName = BOUser.getUserLastFirstByUserID(userID, getClientID().ToString());
        users.Add(user);
        
        DateTime schedulerFromDateTime = DateTime.Parse(RadSchedulerDayWAI.SelectedDate.ToShortDateString());
        FormRequirementValidation formRequirementValidation = new FormRequirementValidation
        {
            ClientID = getClientID(),
            Consumer = consumer,
            Users = users.ToArray(),
            RecordDate = schedulerFromDateTime,
            ServiceID = authorization.getServiceID
        };
        FormRequirementValidationResponse response = FormRequirementService.ValidateFormRequirement(formRequirementValidation);

        if (!response.IsSuccessful)
        {
            string error = "";
            foreach (var errorMessage in response.ErrorMessages)
            {
                error += errorMessage;
            }
            setError(error);
            return false;
        }

        return true;
    }
    private static bool AreBothTimesTheSameMidnight(DateTime t1, DateTime t2)
    {
        DateTime midnight = new DateTime(t1.Year, t1.Month, t1.Day, 0, 0, 0);
        return t1 == midnight && t2 == midnight;
    }
    
    private Control getControl(Control _parentControl, String _IDOfControlToFind)
    {
        foreach (Control control in _parentControl.Controls)
        {
            if (control.ID == _IDOfControlToFind)
            {
                return control;
            }
        }

        return null;
    }
    
    protected void AutoReviewButton_Click(object sender, EventArgs e)
    {
        var activityRecordsId = 0;
        if (!string.IsNullOrEmpty(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value))
        {
            activityRecordsId = int.Parse(HiddenBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        }
                
        if (activityRecordsId == 0 && !string.IsNullOrEmpty(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value))
        {
            activityRecordsId = int.Parse(HiddenTEMPBILLABLECAPAR_ACTIVITY_RECORD_ID.Value);
        }

        BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "AI_ASSISTANT_ACTIVITY_RECORDS_INDIVIDUAL_AUTO_REVIEW_BEGIN", activityRecordsId.ToString(), getUsername(), Session.SessionID);
        
        var autoReviewButton = (LinkButton)sender;
        var parentControl = (Control)autoReviewButton.Parent;
        var consumerId = 0;
        var consumerName = "";
        var activityRecordsGroupId = 0;

        if (!string.IsNullOrEmpty(HiddenBILLABLECAPAR_CONSUMER_ID.Value))
        {
            consumerId = int.Parse(HiddenBILLABLECAPAR_CONSUMER_ID.Value);
            consumerName = BOConsumer.getConsumerNameById(getClientID(), consumerId, false, false, true);
        }
        
        var userCommentID = autoReviewButton.Attributes[USER_COMMENT_ID];
        var commentType = autoReviewButton.Attributes[COMMENT_TYPE];
        var panelID = autoReviewButton.Attributes[PARENT_CONTAINER];
        var goalID = autoReviewButton.Attributes[GOAL_ID];
        
        var commentTextBox = commentType == "GOAL" ? (RadTextBox)getControl(parentControl, $"GOALID_{goalID}") : (RadTextBox)RadTextBoxAdditionalComments;
        var autoReviewContainer = (HtmlGenericControl)getControl(parentControl, $"auto-review-container-{goalID}");
        var autoReviewTextBox = (HtmlGenericControl)getControl(autoReviewContainer, $"auto-review-textbox-{goalID}");
        var autoReviewFooter = (Label)getControl(autoReviewContainer, $"auto-review-footer-label-{goalID}");
        
        autoReviewFooter.Attributes.Add("style", "width:98.8%;display:flex;margin-bottom:10px;");
        autoReviewContainer.Style["display"] = "flex !important";
        autoReviewContainer.Style["width"] = "100% !important";
        autoReviewContainer.Style["margin-bottom"] = "0px !important";
        autoReviewTextBox.Style["width"] = "100%";
        autoReviewTextBox.Style["font-size"] = "11px !important";

        var comment = string.Empty;
        if (commentTextBox == null)
            return;
        
        if (string.IsNullOrEmpty(commentTextBox.Text))
        {
            autoReviewContainer.InnerHtml = "<p>There is no comment for the AI Assistant to review.</p>";
        }
        else
        {
            comment = commentTextBox.Text;
        }
        
        commentTextBox.Attributes.Add("style", "position:relative;");
        
        if (!string.IsNullOrEmpty(comment))
        {
            try
            {
                _aiSystemConfiguration.ClientID = getClientID();
                if (RadComboBoxAuthorizationWAI.SelectedValue == null || RadComboBoxAuthorizationWAI.SelectedValue == "")
                {
                    _aiSystemConfiguration.ServiceID = 0;
                }
                else
                {
                    DSAuthorization auth = BOAuthorization.GetAuthorizationByAuthIDAndClientID(Int32.Parse(RadComboBoxAuthorizationWAI.SelectedValue), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));
                    _aiSystemConfiguration.ServiceID = auth.getServiceID == null ? 0 : auth.getServiceID;
                }
                
                if (commentType == "GOAL")
                {
                    DSGoal goal = BOGoal.getGoalByGoalID(getClientID(), int.Parse(goalID));
                    var selectedGoal = goal.getShortDescription;

                    if (!String.IsNullOrEmpty(selectedGoal) && !string.IsNullOrEmpty(goal.getLongDescription))
                    {
                        selectedGoal += ". " + goal.getLongDescription;
                    }
                    else if (string.IsNullOrEmpty(selectedGoal) && string.IsNullOrEmpty(goal.getLongDescription))
                    {
                        selectedGoal = goal.getLongDescription;
                    }

                    var interventionAndAssessmentKeyValues = getInterventionAndAssessmentKeyValues(activityRecordsId.ToString());
                    
                    _aiSystemConfiguration.UserMessageContext =
                        comment + $". The name of the individual receiving the service is {consumerName}. " + 
                        "This is the particular targeted " +
                        $"outcomes-associated goal on which the" +
                        $" service provider is commenting: \"{selectedGoal}. " +
                        "An 'outcome' is a term that signifies a focused area of progress for the person " +
                        "receiving services to achieve. A 'goal' is a term that signifies a" +
                        " small achievable step toward the desired 'outcome.'";
                    
                    if (interventionAndAssessmentKeyValues.Count > 0)
                    {
                        _aiSystemConfiguration.UserMessageContext += "Additionally, the service provider used these" + 
                        " Intervention and Assessment Keys as part of their goal documentation. " +
                        "Intervention Keys is a term used to signify " +
                        "a meaningful interaction by the service provider while working toward a " +
                        "particular goal. Assessment Keys is a term that signifies areas of " +
                        "interest for purpose of tracking progress while " +
                        "working toward a particular goal." +
                        "Evaluate the comment to determine if it contains any relevant information " +
                        "regarding the use of the intervention and assessment keys." +
                        "These are the intervention and assessment keys provided in a list style " +
                        "json format: [ ";
                        
                        foreach (var goalKeyAndValue in interventionAndAssessmentKeyValues)
                        {
                            if (!string.IsNullOrEmpty(goalKeyAndValue.KeyValue) && goalKeyAndValue.IsKeySelected)
                            {
                                _aiSystemConfiguration.UserMessageContext += "{" + $"KeyType:{goalKeyAndValue.KeyType}, KeyDescription:{goalKeyAndValue.KeyDescription}, KeyValue:{goalKeyAndValue.KeyValue}" + "},";
                            }
                        }
                    }
                    
                    _aiSystemConfiguration.Entity =  SetWorks.Common.Tools.SETWorksAI.EntityType.ACTIVITYRECORDS_GOAL_COMMENTS;
                }
                else if (commentType == "CONSUMER")
                {
                    _aiSystemConfiguration.UserMessageContext = comment + $". The name of the individual receiving the service is {consumerName}. ";
                    _aiSystemConfiguration.Entity = SetWorks.Common.Tools.SETWorksAI.EntityType.ACTIVITYRECORDS_CONSUMER_COMMENTS;
                }

                _aAiConfiguration.SystemConfiguration = _aiSystemConfiguration;
                _aiClient.Config = _aAiConfiguration;
                _aiClient.MakeChatCompletionRequest();
                autoReviewTextBox.InnerHtml = _aiClient.GetChatResponseHtml();
                
                var activityRecordsGroupUserComment_ID = 0;
                var goalCommentId = 0;
                var entityType = _aiSystemConfiguration.Entity.ToString();

                if (activityRecordsId != 0)
                {
                    BOAIAssistantActivityRecordChatHistory.addOrUpdateAIAssistantActivityRecordChatHistory(
                        _aiSystemConfiguration.ClientID,
                        Guid.Parse(getUserID()),
                        entityType,
                        _aiSystemConfiguration.getAIAssistantSystemPrompt(),
                        _aiSystemConfiguration.getAIAssistantUserPrompt(),
                        _aiClient.GetChatResponsePlainText(),
                        consumerId,
                        activityRecordsGroupId,
                        activityRecordsGroupUserComment_ID,
                        activityRecordsId,
                        int.Parse(goalID),
                        goalCommentId,
                        _aiSystemConfiguration.ServiceID);
                }
            }
            catch (Exception ex)
            {
                var errorDashboardContext = $"AI Assistans Activity Records Individual Error: " + ex.Message;
                BOError.setError(getClientID(), getUserID().ToString(), "AI_ASSISTANT_ACTIVITY_RECORDS_INDIVIDUAL_AUTO_REVIEW_ERROR", ex.StackTrace, Session.SessionID, Session.Contents.ToString(), errorDashboardContext, ex.GetType().Name);
                autoReviewTextBox.InnerText =  "AI Assistant encountered an issue while reviewing this comment.";
            }
        }

        BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "AI_ASSISTANT_ACTIVITY_RECORDS_INDIVIDUAL_AUTO_REVIEW_END", activityRecordsId.ToString(), getUsername(), Session.SessionID);
    }

    private void CreateAutoReviewControls(string commentType, int goalID, Control parentControl)
    {
        var autoReviewContainer = createAutoReviewContainer(goalID);
        
        LinkButton autoReviewButton = createAutoReviewButton(goalID, commentType);
        autoReviewButton.EnableViewState = false;
        autoReviewButton.Attributes[PARENT_CONTAINER] = parentControl.ID;

        var cancelIcon = createCancelIcon(goalID.ToString());
        autoReviewContainer.Controls.Add(cancelIcon);
    
        var aiReviewHeaderLabel = createAutoReviewHeaderLabel(goalID);
        autoReviewContainer.Controls.Add(aiReviewHeaderLabel);
    
        var autoReviewTextBoxID = $"auto-review-textbox-{goalID}";
        var autoReviewTextBox = CreateAutoReviewTextBox(autoReviewTextBoxID);
        autoReviewContainer.Controls.Add(autoReviewTextBox);
    
        var aiReviewFooterLabel = createAutoReviewFooterLabel(goalID);
        autoReviewContainer.Controls.Add(aiReviewFooterLabel);
    
        autoReviewContainer.Style["display"] = "none";
        
        var buttonIndex = parentControl.Controls.IndexOf(autoReviewButton);
        if(buttonIndex < 0) parentControl.Controls.Add(autoReviewButton);
        var containerIndex = parentControl.Controls.IndexOf(autoReviewContainer);
        if(containerIndex < 0)parentControl.Controls.Add(autoReviewContainer);
        
        if (commentType != "GOAL")
        {
            ajaxify(autoReviewButton.ID, parentControl.ID);
            ajaxify(autoReviewButton.ID, autoReviewContainer.ID);
            ajaxify(autoReviewButton.ID, RadTextBoxAdditionalComments.ID);
            ajaxify(autoReviewButton.ID, autoReviewTextBox.ID);
            ajaxify(autoReviewContainer.ID, parentControl.ID);
            ajaxify(AutoReviewAdditionalCommentsPanel.ID, RadComboBoxConsumerWAI.ID);
            ajaxify(AutoReviewAdditionalCommentsPanel.ID, RadComboBoxAuthorizationWAI.ID);
        }
        else
        {
            ajaxify(autoReviewButton.ID, parentControl.ID);
            ajaxify(autoReviewButton.ID, autoReviewContainer.ID);
            ajaxify(autoReviewButton.ID, autoReviewTextBox.ID);
            ajaxify(autoReviewContainer.ID, parentControl.ID);
        }
        
        ajaxify(autoReviewButton.ID, RadComboBoxConsumerWAI.ID);
        ajaxify(autoReviewButton.ID, RadComboBoxAuthorizationWAI.ID);
        ajaxify(autoReviewContainer.ID, RadComboBoxConsumerWAI.ID);
        ajaxify(autoReviewContainer.ID, RadComboBoxAuthorizationWAI.ID);

        lblAdditionalComments.Style["position"] = "absolute";
    }
    
    private HtmlGenericControl createAutoReviewContainer(int goalID)
    {
        HtmlGenericControl autoReviewContainer = new HtmlGenericControl("div")
        {
            ID = $"auto-review-container-{goalID}"
        };
        autoReviewContainer.Attributes["class"] = "auto-review-container";
        autoReviewContainer.Attributes["review-container-id"] = autoReviewContainer.ID;
        
        return autoReviewContainer;
    }
    
    private LinkButton createAutoReviewButton(int _goalID, string commentType)
    {
        LinkButton autoReviewButton = new LinkButton();
        autoReviewButton.ID = "auto-review-button-" + _goalID;
        autoReviewButton.Text = "Auto-review";
        autoReviewButton.CssClass = "auto-review-button" + (commentType == "CONSUMER" ? " auto-review-button-additional-comments" : "");
        autoReviewButton.Attributes[USER_COMMENT_ID] = "";
        autoReviewButton.Attributes[COMMENT_TYPE] = commentType;
        autoReviewButton.Attributes[GOAL_ID] = _goalID.ToString();
        autoReviewButton.CausesValidation = false;
        autoReviewButton.Click += AutoReviewButton_Click;

        return autoReviewButton;
    }
    
    private HtmlGenericControl createCancelIcon(string goalID)
    {
        HtmlGenericControl cancelIcon = new HtmlGenericControl("span");
        cancelIcon.InnerText = "✖";
        cancelIcon.Attributes["class"] = "cancel-icon";
        cancelIcon.Attributes["onclick"] = $"removeAutoReview('{goalID}')";
        
        return cancelIcon;
    }
    
    private Label createAutoReviewHeaderLabel(int goalID)
    {
        Label autoReviewLabel = new Label
        {
            ID = $"auto-review-header-label-{goalID}",
            Text = "AI Assistant Review: Use this to improve your comment."
        };
        autoReviewLabel.Attributes["class"] = "auto-review-header-label";
        return autoReviewLabel;
    }

    private HtmlGenericControl CreateAutoReviewTextBox(string autoReviewID)
    {
        HtmlGenericControl autoReviewTextBox = new HtmlGenericControl
        {
            ID = autoReviewID,
            InnerHtml  = string.Empty,
        };

        autoReviewTextBox.Attributes["class"] = "auto-review-textbox";
        autoReviewTextBox.Style["height"] = "300px";
        autoReviewTextBox.Style["overflow-y"] = "auto";
        autoReviewTextBox.Style["resize"] = "none";
        autoReviewTextBox.Style["text-size"] = "11px";
        return autoReviewTextBox;
    }
    
    private Label createAutoReviewFooterLabel(int goalID)
    {
        Label autoReviewLabel = new Label
        {
            ID = $"auto-review-footer-label-{goalID}",
            Text = "The AI Assistant may make mistakes. Please verify your work."
        };
        autoReviewLabel.Attributes["class"] = "auto-review-footer-label";
        autoReviewLabel.Attributes["originalID"] = $"auto-review-footer-label-{goalID}"; 
        return autoReviewLabel;
    }
    
    protected void RadTextGoalComment_TextChanged(object sender, EventArgs e, int goalID)
    {
        var commentTextbox = (RadTextBox)sender;
        LinkButton autoReviewButton = (LinkButton)getControl(commentTextbox.Parent, $"auto-review-button-{goalID}");
        
        if (autoReviewButton != null)
        {
            if (string.IsNullOrEmpty(commentTextbox.Text))
            {
                autoReviewButton.Enabled = false;
            }
            else
            {
                autoReviewButton.Enabled = true;
            }
        }
    }
    
    private static DateTime fixMidnightEndDate(DateTime startDate, DateTime endDate)
    {
        if (startDate >= endDate)
        {
            if (endDate.Hour == 0 && endDate.Minute == 0 && endDate.Second == 0)
            {
                return endDate.AddDays(1);
            }
        }
        return endDate;
    }

    private class GoalKeyValue
    {
        public int GoalID { get; set; }
        public int GoalKeyID { get; set; }
        public string KeyType { get; set; }
        public string KeyDescription { get; set; }
        public string KeyValue { get; set; }
        public bool IsKeySelected { get; set; }
    }
}
