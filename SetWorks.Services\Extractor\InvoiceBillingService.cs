using System;
using System.Collections.Generic;
using System.Data;
using BOs;
using Newtonsoft.Json;
using SetWorks.Common.Tools.DataExtractor.Core;
using SetWorks.Common.Tools.DataExtractor.V3;
using SetWorks.Common.Tools.DataExtractor.V3.Core;
using SetWorks.Infrastructure;
using SetWorks.Services.Extractor;
using DataExtractor = SetWorks.Core.Models.DataExtractor;

namespace SetWorks.Services
{
    public class InvoiceBillingService : DataExtractorRunner
    {
        public void assignFilters(DateTime month, DateTime ServiceEndDate)
        {
            var filterJson = @"
            [
                {
                  'FilterID': 'Department_Multi_Select',
                  'FilterCode': 'Department_Multi_Select',
                  'FilterValue': [
                    '0'
                  ]
                },
                {
                  'FilterID': 'Staff',
                  'FilterCode': 'Staff',
                  'FilterValue': '0'
                },
                {
                  'FilterID': 'Consumer',
                  'FilterCode': 'Consumer',
                  'FilterValue': '0'
                },
                {
                  'FilterID': 'Consumer_Status',
                  'FilterCode': 'Consumer_Status',
                  'FilterValue': '2'
                },
                {
                  'FilterID': 'Date_Range_From',
                  'FilterCode': 'Date_Range_From',
                  'FilterValue': '6/1/2025 12:00:00 AM'
                },
                {
                  'FilterID': 'Date_Range_To',
                  'FilterCode': 'Date_Range_To',
                  'FilterValue': '6/30/2025 12:00:00 AM'
                },
                {
                  'FilterID': 'Funding_Source',
                  'FilterCode': 'Funding_Source',
                  'FilterValue': '0'
                },
                {
                  'FilterID': 'Funding_Source_Contact',
                  'FilterCode': 'Funding_Source_Contact',
                  'FilterValue': '0'
                },
                {
                  'FilterID': 'Activity_Record_Service_Multi_Select',
                  'FilterCode': 'Activity_Record_Service_Multi_Select',
                  'FilterValue': [
                    '0'
                  ]
                },
                {
                  'FilterID': 'Authorization',
                  'FilterCode': 'Authorization',
                  'FilterValue': '0'
                },
                {
                  'FilterID': 'Activity_Record_Phase_Multi_Select',
                  'FilterCode': 'Activity_Record_Phase_Multi_Select',
                  'FilterValue': [
                    '0'
                  ]
                }
              ]";

            List<DSDataExportFilter> filters = JsonConvert.DeserializeObject<List<DSDataExportFilter>>(filterJson);

            DateTime firstDayOfMonth = new DateTime(month.Year, month.Month, 1);
            DateTime lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            if (ServiceEndDate != DateTime.MinValue && ServiceEndDate < lastDayOfMonth)
            {
                lastDayOfMonth = ServiceEndDate;
            }

            foreach (var filter in filters)
            {
                if (filter.FilterID == "Date_Range_From")
                {
                    filter.FilterValue = firstDayOfMonth.ToString("M/d/yyyy hh:mm:ss tt");
                }
                else if (filter.FilterID == "Date_Range_To")
                {
                    filter.FilterValue = lastDayOfMonth.ToString("M/d/yyyy hh:mm:ss tt");
                }
            }

            reformatDateRangeFilters(filters);
            this.filters = filters;
            
        }
        

    }   
}