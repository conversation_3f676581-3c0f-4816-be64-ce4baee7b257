CREATE   PROCEDURE [dbo].[ACCOUNTING_PERIOD.getMinimumDateForInvoices_1.0.0] @Client_ID INT
    AS
    BEGIN

        Set NOCOUNT ON
        Set transaction isolation level READ UNCOMMITTED;



        SELECT MIN(InvoiceDate) FROM dbo.Invoice (NOLOCK) WHERE Client_ID = @Client_ID

    END
go

CREATE  PROCEDURE [dbo].[ACCOUNTS_RECEIVABLE.getAccountsReceivableConsumersByClientID_1.0.1]
    @Client_ID INT,
    @FromDate DATETIME = NULL,
    @ToDate DATETIME = NULL,
    @ConsumerID INT = NULL,
    @AccountsFilterMode INT = NULL,
    @PaymentApplicationMode INT = NULL,
    @Query varchar(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @consumerindex INT = 3;
    DECLARE @serviceindex INT = 2;
    DECLARE @dateindex INT = 1;

    IF EXISTS (
        SELECT 1
        FROM aspnetH_Client C WITH (NOLOCK)
                 INNER JOIN State S WITH (NOLOCK) ON S.State_ID = C.State_ID AND S.Client_ID = C.Client_ID
        WHERE C.Client_ID = @Client_ID AND S.State_Short_txt = 'PA'
    )
        BEGIN
            SET @consumerindex = 4;
            SET @serviceindex = 3;
            SET @dateindex = 2;
        END

    DECLARE @PaymentSums TABLE (
                                   ConsumerID INT,
                                   TotalPaidAmount DECIMAL(18, 2)
                               );

    INSERT INTO @PaymentSums (ConsumerID, TotalPaidAmount)
        SELECT
            I.Consumer_ID AS ConsumerID,
            SUM(ISNULL(CAST(ip.PaymentAmount AS DECIMAL(18, 2)), 0)) AS TotalPaidAmount
        FROM
            dbo.InvoicePayments ip (NOLOCK)
        INNER JOIN dbo.Invoice I (NOLOCK) ON ip.Invoice_ID = I.Invoice_ID AND ip.Client_ID = I.Client_ID
        WHERE
            ip.Client_ID = @Client_ID
            AND I.InvoiceDate <= @ToDate
            AND I.InvoiceDate >= @FromDate
            AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR I.Consumer_ID = @ConsumerID)
        GROUP BY
            I.Consumer_ID;

    IF @PaymentApplicationMode IS NOT NULL AND @PaymentApplicationMode = 1
        BEGIN
            INSERT INTO @PaymentSums (ConsumerID, TotalPaidAmount)
            SELECT
                HCPIT.Consumer_ID,
                SUM(ISNULL(CAST(HCPIT.MonetaryAmount2 AS DECIMAL(18, 2)), 0)) AS TotalPaidAmount
            FROM
                dbo.HealthCarePaymentPointInTime HCPIT (NOLOCK)
            WHERE
                    HCPIT.Client_ID = @Client_ID
              AND HCPIT.ServiceDate <= @ToDate
              AND HCPIT.ServiceDate >= @FromDate
              AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR HCPIT.Consumer_ID = @ConsumerID)
            GROUP BY
                HCPIT.Consumer_ID;
        END
    ELSE IF @PaymentApplicationMode IS NOT NULL AND @PaymentApplicationMode = 0
        BEGIN
            INSERT INTO @PaymentSums (ConsumerID, TotalPaidAmount)
            SELECT
                HCP.Consumer_ID,
                SUM(ISNULL(CAST(HCP.MonetaryAmount2 AS DECIMAL(18, 2)), 0)) AS TotalPaidAmount
            FROM
                dbo.HealthCarePayment HCP (NOLOCK)
            WHERE
                    HCP.Client_ID = @Client_ID
              AND HCP.ServiceDate <= @ToDate
              AND HCP.ServiceDate >= @FromDate
              AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR HCP.Consumer_ID = @ConsumerID)
            GROUP BY
                HCP.Consumer_ID;
        END

    SELECT
        CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END
            AS ConsumerID,
        (SELECT TOP 1 * FROM dbo.udf_GetConsumerFormattedWithMiddleNameIfApplicableByConsumerIDWithClientID_inline(CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END, @Client_ID)) AS ConsumerName,
        SUM(AR.Amount) AS Amount,
        SUM(AR.Amount) - ISNULL((SELECT SUM(TotalPaidAmount) FROM @PaymentSums PS WHERE PS.ConsumerID = CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END), 0) AS Balance
    FROM dbo.AccountsReceivable AR (NOLOCK)
             LEFT JOIN @PaymentSums PS ON PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex) = PS.ConsumerID
             LEFT JOIN Invoice I (NOLOCK) ON AR.AccountTypeValue = 1 and CAST(I.Invoice_ID as varchar(255)) = AR.[Key] and I.Client_ID = AR.Client_ID
    WHERE
            AR.Client_ID = @Client_ID
      AND TransactionTypeValue IN (0, 1, 2) and AR.AccountTypeValue IN (0, 1)
      AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR (CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END) = @ConsumerID)
      AND (@FromDate IS NULL OR CASE WHEN AR.AccountTypeValue = 0 THEN CONVERT(DATETIME, PARSENAME(REPLACE(AR.[Key], '|', '.'), @dateindex), 112) WHEN AR.AccountTypeValue = 1 THEN I.InvoiceDate ELSE NULL END >= @FromDate)
      AND (@ToDate IS NULL OR CASE WHEN AR.AccountTypeValue = 0 THEN CONVERT(DATETIME, PARSENAME(REPLACE(AR.[Key], '|', '.'), @dateindex), 112) WHEN AR.AccountTypeValue = 1 THEN I.InvoiceDate ELSE NULL END <= @ToDate)
      AND (@Query IS NULL OR AR.[Key] LIKE '%' + @Query + '%')
    GROUP BY
        CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END
    HAVING
        (@AccountsFilterMode IS NULL OR @AccountsFilterMode = 0) OR
        (@AccountsFilterMode = 1 AND SUM(AR.Amount) <> 0) OR
        (@AccountsFilterMode = 2 AND SUM(AR.Amount) - ISNULL((SELECT SUM(TotalPaidAmount) FROM @PaymentSums PS WHERE PS.ConsumerID = CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END), 0) <> 0)
END
go



CREATE PROCEDURE [dbo].[ACCOUNTS_RECEIVABLE.getARSummedByClientID_1.0.1] @Client_ID INT,
                                                                                @FromDate DATETIME = NULL,

                                                                                @ToDate DATETIME = NULL ,
                                                                                @ConsumerID INT = NULL,
                                                                                @AccountsFilterMode INT = NULL,
                                                                                @PaymentApplicationMode INT = NULL,
                                                                                @Query varchar(100) = NULL
    AS
    BEGIN

        SET NOCOUNT ON
        SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

        Declare @consumerindex int = 3
        Declare @serviceindex int = 2
        Declare @dateindex int = 1

            IF EXISTS (
                    SELECT 1
                    FROM aspnetH_Client C WITH (NOLOCK)
                    INNER JOIN State S WITH (NOLOCK) ON S.State_ID = C.State_ID AND S.Client_ID = C.Client_ID
                    WHERE C.Client_ID = @Client_ID AND S.State_Short_txt = 'PA'
                )

            BEGIN
                set @consumerindex = 4;
                set @serviceindex = 3;
                set @dateindex = 2;
            end

        DECLARE @PaymentSums TABLE (
        [CombinedKey] VARCHAR(255),
        TotalPaidAmount DECIMAL(18, 2)
        );

            INSERT INTO @PaymentSums ([CombinedKey], TotalPaidAmount)
        SELECT
            '1.' + Cast(ip.Invoice_ID as varchar(10)) AS [CombinedKey],
            SUM(ISNULL(CAST(ip.PaymentAmount AS DECIMAL(18, 2)), 0)) AS TotalPaidAmount
        FROM
            dbo.InvoicePayments ip (NOLOCK)
        INNER JOIN dbo.Invoice I (NOLOCK) ON ip.Invoice_ID = I.Invoice_ID AND ip.Client_ID = I.Client_ID
        WHERE
            ip.Client_ID = @Client_ID
            AND I.InvoiceDate <= @ToDate
            AND I.InvoiceDate >= @FromDate
            AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR I.Consumer_ID = @ConsumerID)
        GROUP BY
            ip.Invoice_ID;

        IF @PaymentApplicationMode IS NOT NULL AND @PaymentApplicationMode = 1
            BEGIN
                INSERT INTO @PaymentSums ([CombinedKey], TotalPaidAmount)
                SELECT
                    '0.' + ebk.UniqueBillingKey AS [Key],
                    SUM(ISNULL(CAST(HCPIT.MonetaryAmount2 AS DECIMAL(18, 2)), 0)) AS TotalPaidAmount
                FROM
                    dbo.HealthCarePaymentPointInTime HCPIT (NOLOCK)
                OUTER APPLY
                    dbo.udf_getElectronicBillingKey_inline(@Client_ID, HCPIT.Consumer_ID, HCPIT.MedicalProcedure,
                                                           HCPIT.ServiceModifier1, HCPIT.ServiceModifier2,
                                                           HCPIT.ServiceModifier3, HCPIT.ServiceModifier4,
                                                           HCPIT.ServiceModifier5, HCPIT.ServiceDate) ebk
                WHERE
                    HCPIT.Client_ID = @Client_ID
                    AND HCPIT.ServiceDate <= @ToDate
                    AND HCPIT.ServiceDate >= @FromDate
                    AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR HCPIT.Consumer_ID = @ConsumerID)
                GROUP BY
                    ebk.UniqueBillingKey;
            END
        ELSE IF @PaymentApplicationMode IS NOT NULL AND @PaymentApplicationMode = 0
            BEGIN
                INSERT INTO @PaymentSums ([CombinedKey], TotalPaidAmount)
                SELECT
                    '0.' + ebk.UniqueBillingKey AS [Key],
                    SUM(ISNULL(CAST(HCP.MonetaryAmount2 AS DECIMAL(18, 2)), 0)) AS TotalPaidAmount
                FROM
                    dbo.HealthCarePayment HCP (NOLOCK)
                OUTER APPLY
                    dbo.udf_getElectronicBillingKey_inline(@Client_ID, HCP.Consumer_ID, HCP.MedicalProcedure,
                                                           HCP.ServiceModifier1, HCP.ServiceModifier2,
                                                           HCP.ServiceModifier3, HCP.ServiceModifier4,
                                                           HCP.ServiceModifier5, HCP.ServiceDate) ebk
                WHERE
                    HCP.Client_ID = @Client_ID
                    AND HCP.ServiceDate <= @ToDate
                    AND HCP.ServiceDate >= @FromDate
                    AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR HCP.Consumer_ID = @ConsumerID)
                GROUP BY
                    ebk.UniqueBillingKey;
            END


        SELECT DISTINCT CAST(AR.AccountTypeValue AS VARCHAR(50))+ '.' + CAST(AR.[Key] AS VARCHAR(255)) AS CombinedKey,
              AR.[Key],
              CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END AS ConsumerID,
               (SELECT TOP 1 * FROM dbo.udf_GetConsumerFormattedWithMiddleNameIfApplicableByConsumerIDWithClientID_inline(CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END, @Client_ID)) AS [ConsumerName],

                    -- Extract the second part (Service)
            CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @serviceindex)
             WHEN AR.AccountTypeValue = 1 THEN 'Invoice'
             ELSE NULL END AS Service,

                    -- Extract the third part (Date)
             CASE WHEN AR.AccountTypeValue = 0 THEN  CONVERT(DATETIME, PARSENAME(REPLACE(AR.[Key], '|', '.'), @dateindex), 112)
             WHEN AR.AccountTypeValue = 1 THEN CONVERT(DATETIME, I.InvoiceDate, 112)
             ELSE NULL END AS Date,

               SUM(AR.Amount) AS Amount,
               SUM(AR.Amount) - ISNULL(PS.TotalPaidAmount, 0) AS Balance
        FROM dbo.AccountsReceivable AR (NOLOCK)
                LEFT JOIN @PaymentSums PS ON CAST(AR.AccountTypeValue AS VARCHAR(50))+ '.' + CAST(AR.[Key] AS VARCHAR(255)) = PS.[CombinedKey]
                LEFT JOIN Invoice I (NOLOCK) ON CAST(I.Invoice_ID as varchar(255)) = AR.[Key] and I.Client_ID = AR.Client_ID and AR.AccountTypeValue = 1
        WHERE AR.Client_ID = @Client_ID AND TransactionTypeValue IN (0, 1, 2)
        AND (@ConsumerID = 0 OR @ConsumerID IS NULL OR (CASE WHEN AR.AccountTypeValue = 0 THEN PARSENAME(REPLACE(AR.[Key], '|', '.'), @consumerindex)
             WHEN AR.AccountTypeValue = 1 THEN I.Consumer_ID
             ELSE NULL END) = @ConsumerID)
      AND (@FromDate IS NULL OR CASE WHEN AR.AccountTypeValue = 0 THEN CONVERT(DATETIME, PARSENAME(REPLACE(AR.[Key], '|', '.'), @dateindex), 112) WHEN AR.AccountTypeValue = 1 THEN I.InvoiceDate ELSE NULL END >= @FromDate)
      AND (@ToDate IS NULL OR CASE WHEN AR.AccountTypeValue = 0 THEN CONVERT(DATETIME, PARSENAME(REPLACE(AR.[Key], '|', '.'), @dateindex), 112) WHEN AR.AccountTypeValue = 1 THEN I.InvoiceDate ELSE NULL END <= @ToDate)
      AND (@Query IS NULL OR AR.[Key] LIKE '%' + @Query + '%')
        GROUP BY AR.[Key], AR.AccountTypeValue, PS.TotalPaidAmount, I.Consumer_ID, I.InvoiceDate
         HAVING
        (@AccountsFilterMode IS NULL OR @AccountsFilterMode = 0) OR
        (@AccountsFilterMode = 1 AND SUM(Amount) <> 0) OR
         (@AccountsFilterMode = 2 AND SUM(AR.Amount) - ISNULL(PS.TotalPaidAmount, 0) <> 0)
        ORDER BY ConsumerName, Service, Date;


    END
go


CREATE   PROCEDURE [dbo].[ACCOUNTS_RECEIVABLE.getTransactionsForAR_1.0.1] @Client_ID INT,
                                                                                 @CombinedKey VARCHAR(255),
                                                                                @PaymentApplicationMode INT = NULL,
                                                                                @ConsumerID INT = NULL,
                                                                                @Date DATETIME = NULL
    AS
    BEGIN

        SET NOCOUNT ON
        SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

        DECLARE @MostRecentClosingDate DATETIME;

        SELECT @MostRecentClosingDate = MAX(AP.ClosingDateTime)
        FROM dbo.AccountingPeriod AP (NOLOCK)
        WHERE AP.Client_ID = @Client_ID;

        SELECT
            CAST(AR.AccountTypeValue AS VARCHAR(50))+ '.' + CAST(AR.[Key] AS VARCHAR(255)) AS CombinedKey,
            AR.AccountingPeriod_ID,
            AP.Description,
            AR.Amount,
            AR.Memo,
            FS.Description AS FundingSourceDescription,
            (SELECT TOP 1 * FROM dbo.udf_MapTransactionType_inline(AR.TransactionTypeValue)) as TransactionType,
            STUFF((SELECT ', ' + DepartmentDescription.value
               FROM OPENJSON(AR.Metadata, '$."Department Description(s)"') AS DepartmentDescription
               FOR XML PATH('')), 1, 2, '') AS DepartmentDescriptions,
            S.Descriptions AS ServiceDescriptions,
            C.Descriptions AS ContractDescriptions,
            JSON_VALUE(ar.Metadata, '$."Batch Ids And Descriptions"') AS BatchIdsAndDescriptions,
            AR.Metadata
        FROM dbo.AccountsReceivable AR (NOLOCK)
            INNER JOIN dbo.AccountingPeriod AP (NOLOCK) ON AR.Client_ID = AP.Client_ID AND AR.AccountingPeriod_ID = AP.AccountingPeriod_ID
            LEFT JOIN dbo.FundingSource FS ON JSON_VALUE(ar.Metadata, '$."Funding Source ID"') = FS.FundingSource_ID AND FS.Client_ID = @Client_ID
        CROSS APPLY dbo.udf_GetServiceDescriptions_inline(@Client_ID, JSON_QUERY(ar.Metadata, '$."Service IDs"')) S
        CROSS APPLY dbo.udf_GetContractDescriptions_inline(@Client_ID, JSON_QUERY(ar.Metadata, '$."Contract IDs"')) C
        WHERE AR.Client_ID = @Client_ID AND
              CAST(AR.AccountTypeValue AS VARCHAR(50))+ '.' + CAST(AR.[Key] AS VARCHAR(255)) = @CombinedKey AND
              AR.TransactionTypeValue IN (0, 1, 2)

        UNION ALL

        SELECT
        '0.' + ebk.UniqueBillingKey AS CombinedKey,
        COALESCE(
            (SELECT TOP 1 AP.AccountingPeriod_ID
             FROM dbo.AccountingPeriod AP (NOLOCK)
             WHERE AP.Client_ID = @Client_ID
             AND AP.ClosingDateTime >= ISNULL(HCPIT.PaymentDateTime, HCPIT.RemittanceDate)
             AND AP.ServiceEndDate >= HCPIT.ServiceDate
             ORDER BY AP.ClosingDateTime ASC
            ),
            **********
        ) AS AccountingPeriod_ID,
        COALESCE(
            (SELECT TOP 1 AP.Description
             FROM dbo.AccountingPeriod AP (NOLOCK)
             WHERE AP.Client_ID = @Client_ID
             AND AP.ClosingDateTime >= ISNULL(HCPIT.PaymentDateTime, HCPIT.RemittanceDate)
             AND AP.ServiceEndDate >= HCPIT.ServiceDate
             ORDER BY AP.ClosingDateTime ASC
            ),
            'Current Period'
        ) AS Description,
        ISNULL(CAST(HCPIT.MonetaryAmount2 AS DECIMAL(18, 2)), 0) AS Amount,
        'Payment date: ' + FORMAT(HCPIT.PaymentDateTime, 'MM/dd/yyyy') AS Memo,
        NULL AS FundingSourceDescription,
        'Payment' AS TransactionType,
        NULL AS DepartmentDescriptions,
        NULL AS ServiceDescriptions,
        NULL AS ContractDescriptions,
        NULL AS BatchIdsAndDescriptions,
        (SELECT
        CAST(HCPIT.HealthCarePayment_ID AS VARCHAR(50)) AS PaymentID,
        FORMAT(ISNULL(HCPIT.PaymentDateTime, HCPIT.RemittanceDate), 'MM/dd/yyyy') AS PaymentDate,
        FORMAT(HCPIT.RemittanceDate, 'MM/dd/yyyy') AS RemittanceDate,
        CAST(HCPIT.ReferenceIdentification AS VARCHAR(50)) AS PaymentReferenceID,
        CAST(HCPIT.CheckEFTTraceNumber AS VARCHAR(50)) AS CheckEFTTraceNumber
     FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)  AS Metadata
    FROM
        dbo.HealthCarePaymentPointInTime HCPIT (NOLOCK)
    OUTER APPLY
        dbo.udf_getElectronicBillingKey_inline(@Client_ID, HCPIT.Consumer_ID, HCPIT.MedicalProcedure,
                                               HCPIT.ServiceModifier1, HCPIT.ServiceModifier2,
                                               HCPIT.ServiceModifier3, HCPIT.ServiceModifier4,
                                               HCPIT.ServiceModifier5, HCPIT.ServiceDate) ebk
    WHERE
        HCPIT.Client_ID = @Client_ID
        AND CAST(HCPIT.ServiceDate AS DATE) = CAST(@Date AS DATE)
        AND HCPIT.Consumer_ID = @ConsumerID
        AND '0.' + ebk.UniqueBillingKey = @CombinedKey
        AND @PaymentApplicationMode = 1


    UNION ALL

        SELECT
        '0.' + ebk.UniqueBillingKey AS CombinedKey,
        COALESCE(
            (SELECT TOP 1 AP.AccountingPeriod_ID
             FROM dbo.AccountingPeriod AP (NOLOCK)
             WHERE AP.Client_ID = @Client_ID
             AND AP.ClosingDateTime >= ISNULL(HCPIT.PaymentDateTime, HCPIT.RemittanceDate)
             AND AP.ServiceEndDate >= HCPIT.ServiceDate
             ORDER BY AP.ClosingDateTime ASC
            ),
            **********
        ) AS AccountingPeriod_ID,
        COALESCE(
            (SELECT TOP 1 AP.Description
             FROM dbo.AccountingPeriod AP (NOLOCK)
             WHERE AP.Client_ID = @Client_ID
             AND AP.ClosingDateTime >= ISNULL(HCPIT.PaymentDateTime, HCPIT.RemittanceDate)
             AND AP.ServiceEndDate >= HCPIT.ServiceDate
             ORDER BY AP.ClosingDateTime ASC
            ),
            'Current Period'
        ) AS Description,
        ISNULL(CAST(HCPIT.MonetaryAmount2 AS DECIMAL(18, 2)), 0) AS Amount,
        'Payment date: ' + FORMAT(HCPIT.PaymentDateTime, 'MM/dd/yyyy') AS Memo,
        NULL AS FundingSourceDescription,
        'Payment' AS TransactionType,
        NULL AS DepartmentDescriptions,
        NULL AS ServiceDescriptions,
        NULL AS ContractDescriptions,
        NULL AS BatchIdsAndDescriptions,
         (SELECT
        CAST(HCPIT.HealthCarePayment_ID AS VARCHAR(50)) AS PaymentID,
        FORMAT(ISNULL(HCPIT.PaymentDateTime, HCPIT.RemittanceDate), 'MM/dd/yyyy') AS PaymentDate,
        FORMAT(HCPIT.RemittanceDate, 'MM/dd/yyyy') AS RemittanceDate,
        CAST(HCPIT.ReferenceIdentification AS VARCHAR(50)) AS PaymentReferenceID,
        CAST(HCPIT.CheckEFTTraceNumber AS VARCHAR(50)) AS CheckEFTTraceNumber
     FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)  AS Metadata
    FROM
        dbo.HealthCarePaymentPointInTime HCPIT (NOLOCK)
    OUTER APPLY
        dbo.udf_getElectronicBillingKey_inline(@Client_ID, HCPIT.Consumer_ID, HCPIT.MedicalProcedure,
                                               HCPIT.ServiceModifier1, HCPIT.ServiceModifier2,
                                               HCPIT.ServiceModifier3, HCPIT.ServiceModifier4,
                                               HCPIT.ServiceModifier5, HCPIT.ServiceDate) ebk
    WHERE
        HCPIT.Client_ID = @Client_ID
        AND CAST(HCPIT.ServiceDate AS DATE) = CAST(@Date AS DATE)
        AND HCPIT.Consumer_ID = @ConsumerID
        AND '0.' + ebk.UniqueBillingKey = @CombinedKey
        AND @PaymentApplicationMode = 0

     UNION ALL

        SELECT
        '1.' + CAST(IP.Invoice_ID as Varchar(255)) AS CombinedKey,
        COALESCE(
            (SELECT TOP 1 AP.AccountingPeriod_ID
             FROM dbo.AccountingPeriod AP (NOLOCK)
             WHERE AP.Client_ID = @Client_ID
             AND AP.ClosingDateTime >= IP.PaymentDate
             AND AP.ServiceEndDate >= I.InvoiceDate
             ORDER BY AP.ClosingDateTime ASC
            ),
            **********
        ) AS AccountingPeriod_ID,
        COALESCE(
            (SELECT TOP 1 AP.Description
             FROM dbo.AccountingPeriod AP (NOLOCK)
             WHERE AP.Client_ID = @Client_ID
             AND AP.ClosingDateTime >= IP.PaymentDate
             AND AP.ServiceEndDate >= I.InvoiceDate
             ORDER BY AP.ClosingDateTime ASC
            ),
            'Current Period'
        ) AS Description,
        ISNULL(CAST(IP.PaymentAmount AS DECIMAL(18, 2)), 0) AS Amount,
        'Payment date: ' + FORMAT(IP.PaymentDate, 'MM/dd/yyyy') AS Memo,
        NULL AS FundingSourceDescription,
        'Payment' AS TransactionType,
        NULL AS DepartmentDescriptions,
        NULL AS ServiceDescriptions,
        NULL AS ContractDescriptions,
        NULL AS BatchIdsAndDescriptions,
         (SELECT
        CAST(IP.InvoicePayment_ID AS VARCHAR(50)) AS PaymentID,
        FORMAT(IP.PaymentDate, 'MM/dd/yyyy') AS PaymentDate,
        CAST(IP.PaymentNotes AS VARCHAR(2000)) AS PaymentNotes,
        CAST(IP.CheckNumber AS VARCHAR(50)) AS CheckNumber
     FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)  AS Metadata
    FROM
        dbo.InvoicePayments IP (NOLOCK)
    INNER JOIN dbo.Invoice I (NOLOCK) ON IP.Invoice_ID = I.Invoice_ID AND IP.Client_ID = I.Client_ID
    WHERE
        IP.Client_ID = @Client_ID
        AND CAST(I.InvoiceDate AS DATE) = CAST(@Date AS DATE)
        AND I.Consumer_ID = @ConsumerID
        AND '1.' + CAST(IP.Invoice_ID as Varchar(255)) = @CombinedKey

    ORDER BY
        AccountingPeriod_ID, CombinedKey;
    END
go
