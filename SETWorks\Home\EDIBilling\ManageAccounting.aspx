<%@ Page Language="C#" MasterPageFile="~/Masters/MasterPlain2.master" AutoEventWireup="true" Inherits="Home_EDIBilling_ManageAccounting"
    Title="Manage Accounts Receivable" Codebehind="ManageAccounting.aspx.cs" %>
<%@ Import Namespace="System.Data" %>
<%@ Import Namespace="System.Web.Optimization" %>
<asp:Content runat="server" ContentPlaceHolderID="ContentPlaceHolderHead">
    
    <%: Styles.Render(PathHelper.getAbsoluteUrl("~/Areas/MVC/css/SETWorks-Kendo-Theme.css", true)) %>
    <%: Styles.Render(PathHelper.getAbsoluteUrl("~/CSS/radgrid_forms.css", true)) %>
    
    <%: Styles.Render("~/bundles/css/responsive") %>
</asp:Content>
<asp:Content ID="Content1" ContentPlaceHolderID="MainPanelContentHolder" runat="Server">
    <head>
        <style>
            .rdpLabel {
                padding-right: 10px;
                line-height: 22px;
            }
            .flex-container {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-wrap: wrap;
                row-gap: 10px;
                padding-bottom: 5px;
            }
            .flex-container > * {
                padding:1em;
            }
            
            .data_window {
                width: auto;
                padding: 5px
            }
            
            .RadGridAccountingPeriod{
                width:100%;
                float:left;
                margin-top: 10px;
                margin-bottom: 10px;
            }
            
            .RadTextBoxDescription{
                width: 200% !important;
                height: 25px !important;
            }
        </style>
    </head>
    
    <telerik:RadCodeBlock ID="RadCodeBlock1" runat="server">

        <script type="text/javascript" language="javascript">
               
            function dateFromFilterOnKeyPress(sender, args) {
                if (args.get_keyCode() === 13) {
                    if (!fromValueChanged) {
                        args._cancel = true;
                    }
                    else {
                        fromValueChanged = false;
                    }
                }
                else {
                    fromValueChanged = true;
                }
            }

            function dateToFilterOnKeyPress(sender, args) {
                if (args.get_keyCode() === 13) {
                    if (!toValueChanged) {
                        args._cancel = true;
                    }
                    else {
                        toValueChanged = false;
                    }
                }
                else {
                    toValueChanged = true;
                }
            }
            
            function dateFromFilterOnKeyPress(sender, args) {
                if (args.get_keyCode() == 13) {
                    if (!fromValueChanged) {
                        args._cancel = true;
                    }
                    else {
                        fromValueChanged = false;
                    }
                }
                else {
                    fromValueChanged = true;
                }
            }

            function dateToFilterOnKeyPress(sender, args) {
                if (args.get_keyCode() == 13) {
                    if (!toValueChanged) {
                        args._cancel = true;
                    }
                    else {
                        toValueChanged = false;
                    }
                }
                else {
                    toValueChanged = true;
                }
            }

            function ToggleFromPopup() {
                $find("<%= RadDatePickerFromDate.ClientID %>").showPopup();
            }

            function ToggleToPopup() {
                $find("<%= RadDatePickerToDate.ClientID %>").showPopup();
            }
            
            function openRemittanceAdvice(claimId,serviceDate,claimAmount,healthCareClaimId) {
                var winW = window.parent.document.body.offsetWidth - 40;
                var winH = window.parent.document.body.offsetHeight - 40;
                setTimeout(function () {
                    if (Boolean(serviceDate)) {
                        openWindow("<%=PathHelper.getAbsoluteUrl() %>/Home/EDIBilling/RemittanceAdvice.aspx?ServiceDate=" 
                        + serviceDate + "&ClaimID=" + claimId + "&ClaimAmount=" + claimAmount + "&HealthCareClaimID=" + healthCareClaimId, "RadWindowRemittance",
                            { "autosize": false, "width": winW, "height": winH });
                    } else {                    
                        openWindow("<%=PathHelper.getAbsoluteUrl() %>/Home/EDIBilling/RemittanceAdvice.aspx?ClaimID="  
                        + claimId + "&ClaimAmount=" + claimAmount + "&HealthCareClaimID=" + healthCareClaimId, "RadWindowRemittance",
                            { "autosize": false, "width": winW, "height": winH });
                    }
                }, 1);
            }
        </script>

    </telerik:RadCodeBlock>
    <telerik:RadAjaxManager ID="RadAjaxManagerFilters" runat="server">
        <AjaxSettings>
            <telerik:AjaxSetting AjaxControlID="RadGridAccountingPeriod">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadGridAccountingPeriod" LoadingPanelID="LoadingPanel1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadGridAccountsReceivable">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadGridAccountsReceivable" LoadingPanelID="LoadingPanel1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadComboBoxConsumerStatus">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadComboBoxConsumer" LoadingPanelID="LoadingPanel1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadDatePickerFromDate">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadDatePickerToDate" LoadingPanelID="LoadingPanel1" />
                    <telerik:AjaxUpdatedControl ControlID="RadDatePickerFromDate" LoadingPanelID="LoadingPanel1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadDatePickerToDate">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="RadDatePickerFromDate" LoadingPanelID="LoadingPanel1" />
                    <telerik:AjaxUpdatedControl ControlID="RadDatePickerToDate" LoadingPanelID="LoadingPanel1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="btnApplyFilters">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="btnApplyFilters" LoadingPanelID="LoadingPanel1" />
                    <telerik:AjaxUpdatedControl ControlID="RadGridAccountsReceivable" LoadingPanelID="LoadingPanel1" />
                    <telerik:AjaxUpdatedControl ControlID="RadSearchTextBoxQuery" LoadingPanelID="LoadingPanel1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
            <telerik:AjaxSetting AjaxControlID="RadSearchTextBoxQuery">
                <UpdatedControls>
                    <telerik:AjaxUpdatedControl ControlID="btnApplyFilters" LoadingPanelID="LoadingPanel1" />
                    <telerik:AjaxUpdatedControl ControlID="RadSearchTextBoxQuery" LoadingPanelID="LoadingPanel1" />
                    <telerik:AjaxUpdatedControl ControlID="RadGridAccountsReceivable" LoadingPanelID="LoadingPanel1" />
                </UpdatedControls>
            </telerik:AjaxSetting>
        </AjaxSettings>
    </telerik:RadAjaxManager>
    <telerik:RadWindowManager ID="RadWindowManager1" ShowContentDuringLoad="false" VisibleStatusbar="false"
        ReloadOnShow="true" Modal="true" VisibleTitlebar="false" runat="server" Skin="Vista"
        DestroyOnClose="false" Behaviors="None" KeepInScreenBounds="true" AutoSize="true">
        <Windows>
            <telerik:RadWindow ID="RadWindowRemittance" runat="server" />
        </Windows>
    </telerik:RadWindowManager>
    <br />
    <telerik:RadAjaxLoadingPanel ID="LoadingPanel1" runat="server" Transparency="50" BackColor="#ffffff" />
    <asp:Panel ID="panFilters" runat="server" Width="100%"></asp:Panel>
    <br />
    <div  class="data_window" style="width: auto;" id="DIVMain" >
        <div id="filterDIV" runat="server" class="sw_container">
            <ul class="sw_filter_list">
                <li class="filter_column">
                    <telerik:RadGrid ID="RadGridAccountingPeriod" GridLines="None" AutoGenerateEditButton="true" runat="server" DataSourceID="SQLDataSourceAccountingPeriod"
                            AllowPaging="True" AutoGenerateColumns="false" AutoGenerateDeleteColumn="false" AllowSorting="true" AllowAutomaticInserts="False" AllowAutomaticUpdates="true" AllowAutomaticDeletes="false"
                            MasterTableView-CommandItemSettings-AddNewRecordText="add accounting period" PageSize="5" CssClass="RadGridAccountingPeriod" AutoGenerateEditColumn="true" OnInsertCommand="RadGridAccountingPeriod_OnInsertCommand">
                            <PagerStyle Mode="NextPrevAndNumeric" />
                            <MasterTableView DataKeyNames="AccountingPeriod_ID" CommandItemDisplay="Bottom" DataSourceID="SQLDataSourceAccountingPeriod" TableLayout="Auto" CellSpacing="-1" InsertItemPageIndexAction="ShowItemOnFirstPage">
                                <Columns>
                                    <telerik:GridBoundColumn DataField="AccountingPeriod_ID" Visible="true" ReadOnly="true">
                                    </telerik:GridBoundColumn>
                                    <telerik:GridTemplateColumn HeaderText="Description">
                                        <ItemTemplate>
                                            <%#DataBinder.Eval(Container.DataItem, "Description")%>
                                        </ItemTemplate>
                                        <InsertItemTemplate>
                                            <telerik:RadTextBox runat="server" ID="RadTextBoxDescription" CssClass="RadTextBoxDescription" TextMode="SingleLine"/>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidatorDescription" ControlToValidate="RadTextBoxDescription" Text="*" runat="server"></asp:RequiredFieldValidator>
                                        </InsertItemTemplate>
                                        <EditItemTemplate>
                                            <telerik:RadTextBox runat="server" ID="RadTextBoxDescription" Text='<%#Bind("Description") %>' CssClass="RadTextBoxDescription" TextMode="SingleLine"/>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidatorDescription" ControlToValidate="RadTextBoxDescription" Text="*" runat="server"></asp:RequiredFieldValidator>
                                        </EditItemTemplate>
                                    </telerik:GridTemplateColumn>
                                    <telerik:GridBoundColumn DataField="Status" ReadOnly="true" HeaderText="Status">
                                    </telerik:GridBoundColumn>
                                    <telerik:GridBoundColumn DataField="ClosingDateTime" ReadOnly="true" HeaderText="Closing Date">
                                    </telerik:GridBoundColumn>
                                    <telerik:GridTemplateColumn HeaderText="Service End Date">
                                        <ItemTemplate >
                                            <%#DataBinder.Eval(Container.DataItem, "ServiceEndDate", "{0:MM/dd/yyyy}")%>
                                        </ItemTemplate>
                                        <InsertItemTemplate>
                                            <telerik:RadDatePicker runat="server" ID="RadDatePickerServiceEndDate" MinDate='<%# (DateTime)ViewState["MaxServiceEndDate"] %>'></telerik:RadDatePicker>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidatorServiceEndDate" ControlToValidate="RadDatePickerServiceEndDate" Text="*" runat="server"></asp:RequiredFieldValidator>
                                            <asp:CustomValidator ID="CustomValidatorServiceEndDate" runat="server" 
                                                ControlToValidate="RadDatePickerServiceEndDate" 
                                                ErrorMessage="Service End Date must be equal or after the latest existing service end date."
                                                OnServerValidate="CustomValidatorServiceEndDate_ServerValidate">
                                            </asp:CustomValidator>
                                        </InsertItemTemplate>
                                    </telerik:GridTemplateColumn>
                                    <telerik:GridBoundColumn DataField="lst_update_userid" ReadOnly="true" HeaderText="Last Updated By">
                                    </telerik:GridBoundColumn>
                                    <telerik:GridBoundColumn DataField="lst_update_timestamp" ReadOnly="true" HeaderText="Last Updated">
                                    </telerik:GridBoundColumn>
                                </Columns>
                            </MasterTableView>
                        </telerik:RadGrid>
                        <asp:SqlDataSource ID="SQLDataSourceAccountingPeriod" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                            SelectCommand="[ACCOUNTING_PERIOD.getAccountingPeriodsByClientID_1.0.0]" SelectCommandType="StoredProcedure"
                            UpdateCommand="[ACCOUNTING_PERIOD.updateAccountingPeriod_1.0.0]" UpdateCommandType="StoredProcedure">
                            <SelectParameters>
                                <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                            </SelectParameters>
                            <UpdateParameters>
                                <asp:ControlParameter Name="AccountingPeriod_ID" Type="Int32" ControlID="RadGridAccountingPeriod"></asp:ControlParameter>
                                <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String" />
                                <asp:Parameter Name="Description" Type="String" />
                                <asp:ControlParameter Name="User_ID" ControlID="HiddenCURRENTWINDOWUSERID" Type="String" />
                            </UpdateParameters>
                        </asp:SqlDataSource>
                </li>
                
                <li class="filter_column">
                    <asp:Label ID="searchLabel" runat="server" CssClass="filter_header" >&nbsp;</asp:Label>
                    <telerik:RadSearchBox ID="RadSearchTextBoxQuery" runat="server" Width="200px" OnSearch="btnApplyFilters_OnClick" EnableAutoComplete="false" EmptyMessage="Type here to search..." SingleClick="true">
                    </telerik:RadSearchBox>
                </li>
                <li class="filter_column">
                    <label class="filter_header" for="RadDatePickerFromDate">From</label>
                    <telerik:RadDatePicker runat="server" ID="RadDatePickerFromDate" MinDate="2000-1-1" Width="80px" AutoPostBack="True" OnSelectedDateChanged="radDatePicker_EnforceMonthDateRange" >
                        <DatePopupButton Visible="false" />
                        <DateInput ID="DateInput1" runat="server" onclick="ToggleFromPopup()" ClientEvents-OnKeyPress="dateToFilterOnKeyPress" />
                    </telerik:RadDatePicker>
                    <label class="filter_header" for="RadDatePickerToDate">To</label>
                    <telerik:RadDatePicker runat="server" ID="RadDatePickerToDate" MinDate="2000-1-1" Width="80px" AutoPostBack="True"  OnSelectedDateChanged="radDatePicker_EnforceMonthDateRange">
                        <DatePopupButton Visible="false" />
                        <DateInput ID="DateInput2" runat="server" onclick="ToggleToPopup()" ClientEvents-OnKeyPress="dateToFilterOnKeyPress" />
                    </telerik:RadDatePicker>
                    
                </li>
                <li class="filter_column">
                    <asp:Label runat="server" ID="lblConsumerStatus" Text="Consumer Status" AssociatedControlID="RadComboBoxConsumerStatus" CssClass="filter_header"/>
                    <telerik:RadComboBox ID="RadComboBoxConsumerStatus" runat="server" Width="200px"
                                                ToolTip="Select a Status" MarkFirstMatch="True" Filter="Contains" AllowCustomText="false" AutoPostBack="true">
                        <Items>
                            <telerik:RadComboBoxItem runat="server" Value="0" Text="Inactive"/>                    
                            <telerik:RadComboBoxItem runat="server" Value="1" Text="Active" Selected="True"/>
                            <telerik:RadComboBoxItem runat="server" Value="2" Text="ALL"/>
                        </Items>
                    </telerik:RadComboBox>
                    <asp:Label runat="server" ID="lblConsumerDropdown" Text="Consumer" AssociatedControlID="RadComboBoxConsumer" CssClass="filter_header"/>
                    <telerik:RadComboBox ID="RadComboBoxConsumer" DataSourceID="sqlConsumers" runat="server" Width="200px" DataValueField="ConsumerID" DataTextField="ConsumerName" Filter="Contains" MinFilterLength="3" AllowCustomText="False" EmptyMessage="Select Consumer" OnDataBound="RadComboBoxConsumer_OnDataBound"/>
                    <asp:SqlDataSource ID="sqlConsumers" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                            SelectCommand="[dbo].[CONSUMER.getConsumersByClientIDAndDepartmentIDAndStaffIDAndStatus_1.0.0]"
                            SelectCommandType="StoredProcedure">
                        <SelectParameters>
                            <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String"/>
                            <asp:ControlParameter Name="User_ID" ControlID="HiddenCURRENTWINDOWUSERID" Type="String"/>
                            <asp:ControlParameter Name="ConsumerStatus" ControlID="RadComboBoxConsumerStatus" Type="String"/>
                        </SelectParameters>
                    </asp:SqlDataSource>
                </li>
                <li class="filter_column">
                    
                    <asp:Label runat="server" ID="DateFilterMode" Text="Accounts Filter Mode" AssociatedControlID="RadComboBoxARFilterMode" CssClass="filter_header"/>
                    <telerik:RadComboBox ID="RadComboBoxARFilterMode" runat="server" Width="200px"
                                                ToolTip="Select a Filter Mode" MarkFirstMatch="True" Filter="Contains" AllowCustomText="false" AutoPostBack="False">
                        <Items>
                            <telerik:RadComboBoxItem runat="server" Value="0" Text="All Accounts"/>                    
                            <telerik:RadComboBoxItem runat="server" Value="1" Text="Accounts with Charges" Selected="True"/>
                            <telerik:RadComboBoxItem runat="server" Value="2" Text="Accounts with Outstanding Balances" />
                        </Items>
                        
                    </telerik:RadComboBox>
                    
                    <asp:Label runat="server" ID="PaymentApplicationMode" Text="Payment Application Mode" AssociatedControlID="RadComboBoxARFilterMode" CssClass="filter_header"/>
                    <telerik:RadComboBox ID="RadCheckBoxPaymentApplicationMode" runat="server" Width="200px"
                                                ToolTip="Select a Payment Mode" MarkFirstMatch="True" Filter="Contains" AllowCustomText="false" AutoPostBack="False">
                        <Items>
                            <telerik:RadComboBoxItem runat="server" Value="0" Text="Only Latest Payment"/>                    
                            <telerik:RadComboBoxItem runat="server" Value="1" Text="Aggregate Payments" Selected="True"/>
                        </Items>
                        
                    </telerik:RadComboBox>
                </li>
                <li class="filter_column">
                    <div style="display: block; float: right; padding-top: 20px;">
                           <br/>
                           <br/>
                        <telerik:RadButton runat="server" Text="apply filters" RenderMode="Lightweight" SingleClick="true" ID="btnApplyFilters" ForeColor="White" BackColor="#af0006" OnClick="btnApplyFilters_OnClick" SingleClickText="applying filters..." Height="28px" Font-Size="13px" Skin="Metro" />
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div>
        <br />
            <div class="mobileRadGrid">
                <telerik:RadGrid runat="server" ID="RadGridAccountsReceivable" OnNeedDataSource="RadGridClaims_OnNeedDataSource" DataSourceID="sqlClientConsumers" OnDetailTableDataBind="RadGridAccountsReceivable_DetailTableDataBind"
                               AllowPaging="True" PageSize="50" ClientSettings-Selecting-EnableDragToSelectRows="false" ClientSettings-Selecting-UseClientSelectColumnOnly="true" AutoGenerateColumns="False">
                    <MasterTableView DataKeyNames="ConsumerID" AllowMultiColumnSorting="true" RetainExpandStateOnRebind="true" Name="TopLevel" AllowSorting="True">
                        <Columns>
                            <telerik:GridBoundColumn DataField="ConsumerID" SortExpression="ConsumerID" UniqueName="ConsumerID" HeaderText="ConsumerID" Visible="False"/>
                            <telerik:GridBoundColumn DataField="ConsumerName" SortExpression="ConsumerName" UniqueName="Consumer" HeaderText="Consumer" AllowSorting="True"/>
                            <telerik:GridBoundColumn DataField="Amount" SortExpression="Amount" UniqueName="Amount" HeaderText="Total Amount" AllowSorting="True" 
                                                     DataFormatString="{0:$###,##0.00}" HeaderStyle-HorizontalAlign="Right" ItemStyle-HorizontalAlign="Right"/>
                            <telerik:GridBoundColumn DataField="Balance" SortExpression="Balance" UniqueName="Balance" HeaderText="Total Balance" AllowSorting="True" 
                                                     DataFormatString="{0:$###,##0.00}" HeaderStyle-HorizontalAlign="Right" ItemStyle-HorizontalAlign="Right"/>
                        </Columns>
                        <DetailTables>
                            <telerik:GridTableView EnableHierarchyExpandAll="true" Width="100%" runat="server" AutoGenerateColumns="false" AllowSorting="true" 
                                RetainExpandStateOnRebind="true" Name="ConsumerLevel" DataKeyNames="CombinedKey, Date, ConsumerID">
                                <ParentTableRelation>
                                    <telerik:GridRelationFields DetailKeyField="ConsumerID" MasterKeyField="ConsumerID"></telerik:GridRelationFields>
                                </ParentTableRelation>
                            <Columns>
                                <telerik:GridBoundColumn DataField="CombinedKey" SortExpression="CombinedKey" UniqueName="CombinedKey" Visible="False" />
                                <telerik:GridBoundColumn DataField="Key" SortExpression="Key" UniqueName="Key" HeaderText="Key" />
                                <telerik:GridBoundColumn DataField="ConsumerName" SortExpression="Consumer" UniqueName="Consumer" HeaderText="Consumer"/>
                                <telerik:GridBoundColumn DataField="Service" SortExpression="Service" UniqueName="Service" HeaderText="Service"/>
                                <telerik:GridBoundColumn DataField="Date" SortExpression="Date" UniqueName="Date" HeaderText="Date" DataFormatString="{0:MM/dd/yyyy}" />
                                <telerik:GridBoundColumn DataField="Amount" SortExpression="Amount" UniqueName="Amount" HeaderText="Amount" DataFormatString="{0:$###,##0.00}" />
                                <telerik:GridBoundColumn DataField="Balance" SortExpression="Balance" UniqueName="Balance" HeaderText="Balance" DataFormatString="{0:$###,##0.00}" />

                                <%--<telerik:GridTemplateColumn UniqueName="ViewReport" HeaderText="">
                                    <ItemTemplate>
                                        <asp:LinkButton Draggable="false" Text="view/add remittance" ForeColor="Black" runat="server"
                                            ID="LinkButtonViewRemittance"></asp:LinkButton>
                                    </ItemTemplate>
                                </telerik:GridTemplateColumn>--%>
                            </Columns>
                            <DetailTables>
                                <telerik:GridTableView DataKeyNames="CombinedKey" EnableHierarchyExpandAll="true" Width="100%" runat="server" AutoGenerateColumns="False" 
                                                       AllowSorting="true" RetainExpandStateOnRebind="true" Name="TransactionLevel" >
                                    <ParentTableRelation>
                                        <telerik:GridRelationFields DetailKeyField="CombinedKey" MasterKeyField="CombinedKey"></telerik:GridRelationFields>
                                    </ParentTableRelation>
                                    <Columns>
                                        <telerik:GridBoundColumn DataField="CombinedKey" SortExpression="CombinedKey" UniqueName="CombinedKey" HeaderText="CombinedKey" Visible="False"/>
                                        <telerik:GridBoundColumn DataField="AccountingPeriod_ID" SortExpression="AccountingPeriod_ID" UniqueName="AccountingPeriod_ID" HeaderText="AccountingPeriod_ID" Visible="False" />
                                        <telerik:GridBoundColumn DataField="Description" SortExpression="Description" UniqueName="Description" HeaderText="Accounting Period" />
                                        <telerik:GridBoundColumn DataField="TransactionType" SortExpression="TransactionType" UniqueName="TransactionType" HeaderText="Transaction Type" />
                                        <telerik:GridBoundColumn DataField="Amount" SortExpression="Amount" UniqueName="Amount" HeaderText="Amount" DataFormatString="{0:$#,#.00}"/>
                                        <telerik:GridBoundColumn DataField="FundingSourceDescription" SortExpression="FundingSourceDescription" UniqueName="FundingSourceDescription" HeaderText="Funding Source" />
                                        <telerik:GridBoundColumn DataField="ContractDescriptions" SortExpression="ContractDescriptions" UniqueName="ContractDescriptions" HeaderText="Contract(s)" />
                                        <telerik:GridBoundColumn DataField="DepartmentDescriptions" SortExpression="DepartmentDescriptions" UniqueName="DepartmentDescriptions" HeaderText="Department(s)" />
                                        <telerik:GridBoundColumn DataField="ServiceDescriptions" SortExpression="ServiceDescriptions" UniqueName="ServiceDescriptions" HeaderText="Service Description(s)" />
                                        <telerik:GridBoundColumn DataField="BatchIdsAndDescriptions" SortExpression="BatchIdsAndDescriptions" UniqueName="BatchIdsAndDescriptions" HeaderText="Batches" />
                                        <telerik:GridBoundColumn DataField="Memo" SortExpression="Memo" UniqueName="Memo" HeaderText="Memo" />
                                        <telerik:GridBoundColumn DataField="Metadata" SortExpression="Metadata" UniqueName="Metadata" HeaderText="Metadata" DataFormatString="" />
                                        <%--<telerik:GridTemplateColumn UniqueName="ViewReport" HeaderText="">
                                            <ItemTemplate>
                                                <asp:LinkButton Draggable="false" Text="view/add remittance" ForeColor="Black" runat="server"
                                                    ID="LinkButtonViewRemittance"></asp:LinkButton>
                                            </ItemTemplate>
                                        </telerik:GridTemplateColumn>--%>
                                    </Columns>
                                </telerik:GridTableView>
                            </DetailTables>
                            </telerik:GridTableView>
                        </DetailTables>
                    </MasterTableView>
                </telerik:RadGrid>
                <asp:SqlDataSource ID="sqlClientConsumers" runat="server" ConnectionString="<%$ ConnectionStrings:SETPROFESSIONALConnectionString %>"
                                   SelectCommand="[dbo].[ACCOUNTS_RECEIVABLE.getAccountsReceivableConsumersByClientID_1.0.1]"
                                   SelectCommandType="StoredProcedure" >
                                    
                    <SelectParameters>
                        <asp:ControlParameter Name="Client_ID" ControlID="HiddenCURRENTWINDOWCLIENTID" Type="String"/>
                        <asp:ControlParameter Name="FromDate" ControlID="RadDatePickerFromDate" Type="DateTime" />
                        <asp:ControlParameter Name="ToDate" ControlID="RadDatePickerToDate" Type="DateTime" />
                        <asp:ControlParameter Name="ConsumerID" ControlID="RadComboBoxConsumer" PropertyName="SelectedValue" Type="Int32" DefaultValue="0" />
                        <asp:ControlParameter Name="AccountsFilterMode" ControlID="RadComboBoxARFilterMode" Type="Int32" PropertyName="SelectedValue"/>
                        <asp:ControlParameter Name="Query" ControlID="RadSearchTextBoxQuery" Type="String" PropertyName="Text" DefaultValue="%"/>
                        <asp:ControlParameter Name="PaymentApplicationMode" ControlID="RadCheckBoxPaymentApplicationMode" Type="Int32" PropertyName="SelectedValue"/>
                    </SelectParameters>
                </asp:SqlDataSource>
            </div>
        </div>
    <asp:HiddenField ID="HiddenCURRENTWINDOWUSERID" runat="server" Value="False"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCURRENTWINDOWCLIENTID" runat="server"></asp:HiddenField>
    <asp:HiddenField ID="HiddenBATCHID" runat="server" Value="0"></asp:HiddenField>
    <asp:HiddenField ID="HiddenCLAIMID" runat="server"></asp:HiddenField>

</asp:Content>

