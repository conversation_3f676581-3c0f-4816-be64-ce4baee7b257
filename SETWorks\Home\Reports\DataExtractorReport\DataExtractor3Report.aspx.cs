using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Data;
using System.Drawing;
using System.IO;
using System.Web;
using System.Web.UI;
using BOs;
using SetWorks.Common.Tools.DataExtractor.Core;
using SetWorks.Infrastructure;
using SetWorks.Services.Extractor;
using SW.UI;
using Telerik.Reporting;
using Telerik.Reporting.Drawing;
using DataExtractor = SetWorks.Core.Models.DataExtractor;
using Filter = Telerik.Reporting.Filter;
using System.Linq;
using System.Text;
using System.Web.UI.WebControls;
using Microsoft.Ajax.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SetWorks.Common.Tools.DataExtractor.V3.Core.GenericTransformations;
using SetWorks.Common.Tools.DataExtractor.V3;
using SetWorks.Common.Tools.DataExtractor.V3.Core;
using SetWorks.Common.Tools.DataExtractor.V3.Core.GenericTransformations.Helpers;
using SetWorks.Common.Tools.DataExtractor.V3.DataExtractors;
using Telerik.Web.UI;
using BorderType = Telerik.Reporting.Drawing.BorderType;
using DataExtractorV3FilterConfigurator = SetWorks.Common.Tools.DataExtractor.V3.Core.DataExtractorV3FilterConfigurator;
using Table = Telerik.Reporting.Table;
using TextBox = Telerik.Reporting.TextBox;
using Unit = Telerik.Reporting.Drawing.Unit;

public partial class Home_Reports_DataExtractor3Report : SWReportPage
{
    private const string internalSummaryRowIdentifierColName = "DATA_EXTRACTOR_REPORT_IS_SUMMARY_ROW";
    private const string internalConditionalFormattingFormat = "DATA_EXTRACTOR_REPORT_IS_CONDITIONAL_FORMATTING_ROW";
    private const string internalConditionalFormattingIndex = "DATA_EXTRACTOR_REPORT_CONDITIONAL_FORMATTER_INDEX";
    
    private const string CONFIG_DEPARTMENT_FILTER_APPLIES_TO_USER = "CONFIG_DEPARTMENT_FILTER_APPLIES_TO_USER";
    private const string CONFIG_FIELDCODE_DEPARTMENT_FILTER = "CONFIG_FIELDCODE_DEPARTMENT_FILTER";
    private const string CONFIG_INPUT_PAGINATION_FILTERS = "CONFIG_INPUT_PAGINATION_FILTERS";
    private string groupingCoulumn = string.Empty;
    private string redirectUrl = "~/Home/Settings/Utilities/DataExtractor/DataExtractorBuilder3.aspx#/detail/";
    protected void Page_PreInit(object sender, EventArgs e)
    {
        if (HttpContext.Current.Request.Params.Get("Iframe") != null)
            Page.MasterPageFile = "../../../Masters/MasterEmptyWithScriptManager.master";
        
    }
    
    protected void Page_Init(object sender, EventArgs e)
    {
        HIDDENUseDataExportService.Value = "false";
        
        // Previewing within the Data Extractor
        if (HttpContext.Current.Request.Params.Get("Preview") != null)
        {
            HIDDENDataExtractorID.Value = HttpContext.Current.Request.Params.Get("DataExtractorID"); //extractConfigJson.DataExtractor_ID;

            if (HttpContext.Current.Request.Params.Get("Step") != null)
            {
                int stepNum = Int32.Parse(HttpContext.Current.Request.Params.Get("Step"));
            
                Page.Title = "Up to step #" + stepNum;
                DIVReportHeader.InnerHtml = "Up to step #" + stepNum;
                prepareDataExtractor(Int32.Parse(HIDDENDataExtractorID.Value), true, stepNum);
            }
            else
            {
                Page.Title = "Report Preview";
                DIVReportHeader.InnerHtml = "Report Preview";
                prepareDataExtractor(Int32.Parse(HIDDENDataExtractorID.Value), true, null);
            }
        }
        // Main flow from reports
        else if (HttpContext.Current.Request.Params.Get("ReportID") != null)
        {
            string reportID = EncoderAndDecoder.Decode(HttpContext.Current.Request.Params.Get("ReportID"));
            string reportUtilitiesLinkDate = EncoderAndDecoder.Decode(HttpContext.Current.Request.Params.Get("ReportUtilitiesLink"));
            PageSecurityWrapper.isReportAccessible(Response, getClientID(), reportID, Guid.Parse(getUserID()), reportUtilitiesLinkDate);

            HIDDENReportID.Value = reportID;
            DSReport report = BOReports.GetReportByReportID(getClientID(), Convert.ToInt32(reportID));
            Page.Title = $"{report.ReportName} ({Page.Title})";

            if (report.DataExtractor_ID != null)
            {
                int dataExtractorID = (int) report.DataExtractor_ID;
                HIDDENDataExtractorID.Value = dataExtractorID.ToString();
                
                prepareDataExtractor(dataExtractorID);
            }
        }

        if (HttpContext.Current.Request.Params.Get("OutputType") != null)
        {
            HiddenOutputType.Value = HttpContext.Current.Request.Params.Get("OutputType");

            if (HiddenOutputType.Value.Equals(OutputType.PIPE_DELIMITED_CSV.ToString()))
            {
                System.Web.UI.WebControls.Panel panel = new System.Web.UI.WebControls.Panel();
                panel.CssClass = "ExportReportButtonDiv";

                Button exportButton = new Button();

                exportButton.ID = "ExportCSVFile";
                exportButton.Text = "Export CSV File";
                exportButton.CssClass = "GenerateReportButton";
                exportButton.Click += ExportPipeDelimitedCSVFile_Click;

                panel.Controls.Add(exportButton);
                filtersPanel.Parent.Controls.AddAt(10, panel);

                var genRepBtn = filtersPanel.FindControl("genRepBtn");
                if (genRepBtn != null)
                    genRepBtn.Visible = false;
            }
        }

        var deliverToMessageCenterPanel = GetDeliverToMessageCenterPanel();
        var deliverToForeignSystemPanel = GetDeliverToForeignSystemPanel();
        Control textBoxEmailBtn = filtersPanel.FindControl("textboxEmailButton");
        Control printBtn = filtersPanel.FindControl("Print");
        Control generateRepBtn = filtersPanel.FindControl("genRepBtn");

        if (textBoxEmailBtn != null && textBoxEmailBtn.ID != null)
        {
            AddNewControlAfterTargetControl(filtersPanel.Parent, textBoxEmailBtn.ID, deliverToMessageCenterPanel);
            AddNewControlAfterTargetControl(filtersPanel.Parent, "DeliverToMessageCenter", deliverToForeignSystemPanel);
        }
        else if(printBtn != null && printBtn.ID != null)
        {
            AddNewControlAfterTargetControl(filtersPanel.Parent, printBtn.ID, deliverToMessageCenterPanel);
            AddNewControlAfterTargetControl(filtersPanel.Parent, "DeliverToMessageCenter", deliverToForeignSystemPanel);
        }
        else if (generateRepBtn != null && generateRepBtn.ID != null)
        {
            AddNewControlAfterTargetControl(filtersPanel.Parent, generateRepBtn.ID, deliverToMessageCenterPanel);
            AddNewControlAfterTargetControl(filtersPanel.Parent, "DeliverToMessageCenter", deliverToForeignSystemPanel);
        }
        else
        {
            filtersPanel.Parent.Controls.Add(deliverToMessageCenterPanel);
            filtersPanel.Parent.Controls.Add(deliverToForeignSystemPanel);
        }
    }
    
    public void ExportPipeDelimitedCSVFile_Click(object sender, EventArgs e)
    {
        HIDDENUseDataExportService.Value = "false";
        processReport(true, false);
    }

    public void DeliverToMessageCenter_Click(object sender, EventArgs e)
    {
        HIDDENUseDataExportService.Value = "true";
        processReport(false, false);
        HIDDENUseDataExportService.Value = "false";
    }

    public void DeliverToForeignSystem_Click(object sender, EventArgs e)
    {
        HIDDENUseDataExportService.Value = "true";
        processReport(false, true);
        HIDDENUseDataExportService.Value = "false";
    }
    
    private void prepareDataExtractor(int dataExtractorID, bool preview = false, int? previewTransformationUpToStep = null) //, string previewJsonConfig = null)
    {
        long[] iAccess = getUserPrivs();
        SetWorksDataContext context = new SetWorksDataContext();
                
        DataExtractorService des = new DataExtractorService(context);
        DataExtractor dataExtractorModel = des.getDataExtractorByDataExtractorID(dataExtractorID);
        string jsonConfig = dataExtractorModel.JsonConfig;
        dynamic extractConfigJson = JsonConvert.DeserializeObject<dynamic>(jsonConfig);
        
        // Remove transformation steps from json if this is a report preview from the DE 
        if (preview)
        {
            if (previewTransformationUpToStep == null)
                previewTransformationUpToStep = extractConfigJson.transformations.Count;
            
            while (previewTransformationUpToStep < extractConfigJson.transformations.Count)
            {
                extractConfigJson.transformations.RemoveAt(extractConfigJson.transformations.Count - 1);
            }

            jsonConfig = extractConfigJson.ToString();
        }
        
        dataExtractor = DataExtractorV3Renderer.createDataExtractor(getClientID(), new Guid(getUserID()), jsonConfig);
        
        var configurationFields = dataExtractor.getSelectedConfigurationFields();

        updateGroupingColumnNameForPagination(configurationFields, extractConfigJson);

        var filterConfigurator = new DataExtractorV3FilterConfigurator(dataExtractor, getClientID(), getUserID(), extractConfigJson);
        var hasFilterConfig = filterConfigurator.HasFilterConfig();
        var inputParameters = dataExtractor.getInputParameters();
        if (hasFilterConfig && extractConfigJson.configurationFields.Count > 1)
        {
            inputParameters = filterConfigurator.ApplyFilterConfig();
        }

        List<Control> filterTypeControls = new List<Control>();
        foreach (var inputParam in inputParameters)
        {
            if (inputParam.parameterType != InputParameter.Type.SWFilter || inputParam.filterType == SWFilterType.NonFilter)
            {
                continue;
            }
            try
            {
                var control = dataExtractor.getReportFilterControl(inputParam.filterType);
                filterConfigurator.ApplyTooltip(control, inputParam);
                filterTypeControls.Add(control);
            }
            catch (Exception e)
            {
                if(filterConfigurator.ShouldGetFilterControl(e))
                {
                    var control = filterConfigurator.GetFilterControl(inputParam);
                    filterConfigurator.ApplyTooltip(control, inputParam);
                    filterTypeControls.Add(control);
                }
                else
                {
                    throw;
                }
            }
        }

        // Some filters have the same control id (e.g service select & multi-select), and we're more likely to run into collisions with v3 due to auto-joins
        var uniqueFilterTypeControls = filterTypeControls.DistinctBy(f => f.ID).ToArray();
        filters = SWFiltersManager.createReportFilters(filtersPanel, RadAjaxManager1, ReportViewerDataExtractor, processReport, DIVReportHeader, getUserID(), getReportID(),
            uniqueFilterTypeControls);
        
        // Anyone with access to the provider build tab will have access to edit the report
        if (Privilege.isPriv2True(iAccess, Privileges2.SHOW_PROVIDER_BUILD_TAB))
            createEditReportButton();
        
        UpdateDepartmentFilterLabelIfConfiguredToApplyToUserOnly();
    }

    protected void Page_Load(object sender, EventArgs e)
    {
    }

    private DSDataExportJob GetDataExportJob(List<DSDataExportFilter> filters)
    {
        return new DSDataExportJob
        {
            AssignedStaffType = "",
            ConsumerStatus = "",
            ClientID = getClientID(),
            ConsumerID = "",
            DepartmentID = "",
            FormID = "",
            FromDateString = "",
            ToDateString = "",
            FundingSourceID = "",
            IsComplete = false,
            PlacementStatus = "",
            StaffStatus = "",
            LstUpdateUsername = getUsername(),
            PayrollEarningsCode = "",
            ReportID = Convert.ToInt32(HIDDENReportID.Value),
            ServiceID = "",
            StaffID = "",
            FilterJson = filters.ToJson(),
            ReportType = ReportType.DATA_EXTRACTOR_V3.ToString(),
            DataExtractorID = Convert.ToInt32(HIDDENDataExtractorID.Value)
        };
    }

    private void processReport(bool exportToPipeDelimitedCSV, bool foreignSystemCall)
    {
        dataExtractor.setInputParameterValuesByReportFilters(filters);
        if (bool.Parse(HIDDENUseDataExportService.Value))
        {
            var inputParameters = dataExtractor.getInputParameters();
            var inputParamTypes = inputParameters.Select(ip => ip.filterType.ToString()).ToImmutableHashSet();
            var dataExportFilters = filters.Select(filter => new DSDataExportFilter { FilterID = ((WebControl)filter).Attributes["FilterType"] })
                .Where(filter => inputParamTypes.Contains(filter.FilterID)).ToList();
            foreach (var filter in filters)
            {
                foreach (var inputParam in inputParameters)
                {
                    var filterValue = dataExtractor.GetControlValue(filter, inputParam, true);
                    var dataRows = new List<string>();

                    var table = filterValue as DataTable;
                    if (table != null)
                    {
                        dataRows.AddRange(table.Rows.Cast<DataRow>().Select(dataRow => dataRow.ItemArray[0].ToString()));
                    }

                    var value = (filterValue is DataTable) ? dataRows : filterValue;

                    
                    var dsDataExportFilter = dataExportFilters.FirstOrDefault(x => x.FilterID == ((WebControl) filter).Attributes["FilterType"]);
                    if (dsDataExportFilter != null && dsDataExportFilter.FilterID == inputParam.filterType.ToString())
                    {
                        dsDataExportFilter.FilterCode = inputParam.parameterCode;
                    }

                    if ((!(value is DataTable) || ((DataTable)value).Rows.Count <= 0) &&
                        string.IsNullOrEmpty(value.ToString()))
                    {
                        continue;
                    }

                    if (dataExportFilters.Any(x => x.FilterID == ((WebControl) filter).Attributes["FilterType"]) && dsDataExportFilter != null)
                    {
                        dsDataExportFilter.FilterValue = value;
                    }
                    else
                    {
                        dataExportFilters.Add(new DSDataExportFilter
                        {
                            FilterID = ((WebControl) filter).Attributes["FilterType"],
                            FilterValue = value, FilterCode = ((WebControl) filter).Attributes["FilterType"]
                        });
                    }
                }
            }

            var dataExportJob = GetDataExportJob(dataExportFilters);
            string selectedForeignSystem = null;
            
            if (foreignSystemCall)
            {
                RadComboBox foreignSystem = FindControlRecursive<RadComboBox>(Page, "ForeignSystemsDropDown");
                selectedForeignSystem = foreignSystem.SelectedValue;
            }
            
            BODataExportJob.CreateDataExportJob(dataExportJob, getUsername(), selectedForeignSystem);
            informationDIV.Visible = true;
            return;
        }

        var translationTable = DataExtractorV3Manager.TranslatedFields(getClientID());
        translationTable = translationTable.Merge(dataExtractor.getTranslationTable());
        SetWorksDataContext context = new SetWorksDataContext();
                
        DataExtractorService des = new DataExtractorService(context);
        DataExtractor dataExtractorModel = des.getDataExtractorByDataExtractorID(Convert.ToInt32(HIDDENDataExtractorID.Value));
        string jsonConfig = dataExtractorModel.JsonConfig;
        dynamic extractConfigJson = JsonConvert.DeserializeObject<dynamic>(jsonConfig);
        var filterConfigurator = new DataExtractorV3FilterConfigurator(dataExtractor, getClientID(), getUserID(), extractConfigJson);
        filterConfigurator.FilterInputParams(dataExtractor);
        var outputResult = DataExtractorV3Renderer.runDataExtractor(dataExtractor, getClientID(), new Guid(getUserID()), false, translationTable);
        if (outputResult.getOutputStatus == OutputStatus.SUCCESS && (outputResult.getOutput as DataTable)?.Rows.Count > 2750)
        {
            var report = new GenericStringReport();
            var formattedReport = (GenericStringReport)formatReport(report);
            ReportViewerDataExtractor.ReportSource = formattedReport;
            var bo = new GenericStringReportBO("Too many records to display, either narrow the filters or click the 'Deliver to Message Center' button to run the report.");
            report.DataSource = bo.getDataSet();
        }
        else if (outputResult.getOutputStatus == OutputStatus.SUCCESS)
        {
            DataExtractorReport report = new DataExtractorReport();
            DataExtractorReport formattedReport = (DataExtractorReport)formatReport(report);
            ReportViewerDataExtractor.ReportSource = formattedReport;
            DataTable dataTable = (DataTable) outputResult.getOutput;
            var table = CreateTable(dataTable, DataExtractorV3Manager.v3ColumnNames(getClientID(), dataTable, translationTable));
            (formattedReport.Items.Find("detail", true)[0] as DetailSection).Items.Add(table);
            table.DataSource = dataTable;
            bool hasHeaderExpression = dataTable.ExtendedProperties.ContainsKey("HeaderComplexExpression");
            (formattedReport.Items.Find("reportHeaderSection", true)[0] as ReportHeaderSection).Visible = hasHeaderExpression;
            if (hasHeaderExpression)
            {
                var expContext = ExpressionHelperV3.ContextFromInputParameters(getClientID().ToString(), dataExtractor.getInputParameters());
                var headerValue = expContext.CompileGeneric<string>(
                        dataTable.ExtendedProperties["HeaderComplexExpression"].ToString()) .Evaluate();
                (formattedReport.Items.Find("reportHeaderBody", true)[0] as TextBox).Value = headerValue;
            }
            
            if (exportToPipeDelimitedCSV)
            {
                string fileName = "DDaPFile_" + TimeZoneProcessor.convertDateTimeToClientLocalDateTime(getClientID().ToString(), DateTime.UtcNow).ToString("MM_dd_yyyy_hh_mm_tt") + ".txt";

                MemoryStream ms = new MemoryStream();
                TextWriter tw = new StreamWriter(ms);

                var columns = new StringBuilder();
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    var dataColumn = dataTable.Columns[i];
                    
                    if (dataColumn.ColumnName.Equals(internalSummaryRowIdentifierColName) 
                        || dataColumn.ColumnName.Equals(internalConditionalFormattingFormat)
                        || dataColumn.ColumnName.Equals(internalConditionalFormattingIndex))
                        continue;
                    
                    if (i == dataTable.Columns.Count - 1)
                        columns.Append(dataColumn);
                    else
                        columns.Append(dataColumn + "|");
                }

                columns.Remove(columns.Length - 1, 1);
                tw.WriteLine(columns.ToString());
                    
                foreach (DataRow row in dataTable.Rows)
                {
                    var newRow = new StringBuilder();
                    

                    for (int i = 0; i < row.ItemArray.Length; i++)
                    {
                        var value = row.ItemArray[i];
                        if (i == dataTable.Columns.Count - 1)
                            newRow.Append(value);
                        else
                            newRow.Append(value + "|");
                    }
                    newRow.Remove(newRow.Length - 1, 1);
                    tw.WriteLine(newRow);
                }
                
                tw.Flush();
                byte[] bytes = ms.ToArray();
                ms.Close();
                
                Response.Clear();
                Response.ContentType = "application/force-download";
                Response.AddHeader("content-disposition", "attachment;    filename=" + fileName);
                Response.BinaryWrite(bytes);
                Response.End();    
            }
        }
        else if (outputResult.getOutputStatus == OutputStatus.ERROR) {
            GenericStringReport report = new GenericStringReport();
            GenericStringReport formattedReport = (GenericStringReport)formatReport(report);
            ReportViewerDataExtractor.ReportSource = formattedReport;
            
            if (outputResult.getOutputType == OutputType.OUTPUT_ERROR_ARRAY)
            {
                var errors = (OutputError[])outputResult.getOutput;
                var errorsString = string.Empty;

                foreach (var error in errors)
                {
                    if (errorsString.Length > 0)
                    {
                        errorsString += "<br />";
                    }

                    errorsString += error.getErrorString;

                    if (HttpContext.Current.Request.Params.Get("Preview") != null)
                    {
                        errorsString +=  error.getException == null ? "" : $"<br /><br />{error.getException}";
                    }
                }

                if (string.IsNullOrEmpty(errorsString))
                {
                    errorsString = "An unknown report error occurred. Please try again and contact SETWorks support if this persists.";
                }
                var bo = new GenericStringReportBO(errorsString);
                report.DataSource = bo.getDataSet();
            }
            else
            {
                var bo = new GenericStringReportBO("An unidentified report error occurred. Please try again and contact SETWorks support if this persists.");
                report.DataSource = bo.getDataSet();
            }
        }
    }
    
    private Table CreateTable(DataTable _dataSourceTable, Dictionary<string, string> v3ColumnNames)
    {
        //create a blank Table item
        Table table1 = new Table();
        table1.Location = new PointU(Unit.Inch(0.0), Unit.Inch(0.0));
        table1.Name = "Table1";
        table1.Size = new SizeU(Unit.Inch(4), Unit.Inch(1));

        table1.DataSource = _dataSourceTable;

        addIsSummaryRowColumn(_dataSourceTable);
        addDuplicateColumnForPaging(_dataSourceTable);
        
        List<ConditionalFormatter> conditionalFormatter = (List<ConditionalFormatter>)_dataSourceTable.ExtendedProperties[ConditionalFormattingV3.CONDITIONAL_FORMATTER_LIST];
        
        if (_dataSourceTable.ExtendedProperties.ContainsKey(ConditionalFormattingV3.CONDITIONAL_FORMATTER_LIST))
        {
            addIsRowToApplyConditionalFormattingToColumn(_dataSourceTable, conditionalFormatter.ToArray());
        }

        createDynamicRowGroup(_dataSourceTable, table1);
        
        //add a row container
        table1.Body.Rows.Add(new TableBodyRow(Unit.Inch(0.5)));

        //add columns        
        var columns = _dataSourceTable.Columns;
        
        for (var i = 0; i <= columns.Count - 1; i++)
        {
            // Don't render the internal summary row identifier column
            if (columns[i].ColumnName.Equals(internalSummaryRowIdentifierColName) 
                || columns[i].ColumnName.Equals(internalConditionalFormattingFormat)
                || columns[i].ColumnName.Equals(internalConditionalFormattingIndex) || (columns[i].ColumnName.Equals(groupingCoulumn) && groupingCoulumn.Contains("PagingDuplicate")))
            {
                continue;
            }

            var columnAlias = v3ColumnNames.GetValue(columns[i].ColumnName, columns[i].ColumnName);
            var colWidth = GetColumnWidth(_dataSourceTable, columns[i].ColumnName, columnAlias);
            
            table1.Body.Columns.Add(new TableBodyColumn(Unit.Inch(colWidth)));
            
            // Add a static column group per data field, may not be needed
            TableGroup columnGroup = new TableGroup();
            table1.ColumnGroups.Add(columnGroup);

            // Header textbox
            TextBox headerTextBox = new TextBox();
            headerTextBox.Name = $"headerTextBox{i}";
            headerTextBox.Size = new SizeU(Unit.Inch(colWidth), Unit.Inch(0.2));
            headerTextBox.Value = columnAlias;
            headerTextBox.CanGrow = true;
            headerTextBox.Style.Font.Bold = true;
            headerTextBox.Style.BorderStyle.Default = BorderType.Solid;
            headerTextBox.Style.BorderColor.Default = Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(212)))), ((int)(((byte)(212)))));
            headerTextBox.Style.Font.Size = Unit.Point(8D);
            headerTextBox.Style.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(241)))), ((int)(((byte)(241)))), ((int)(((byte)(241)))));
            headerTextBox.Style.Padding.Left = Unit.Inch(0.0099999997764825821D);
            headerTextBox.Style.Padding.Top = Unit.Inch(0.019999999552965164D);
            columnGroup.ReportItem = headerTextBox;

            // Detail rows
            TextBox detailRowTextBox = new TextBox();
            detailRowTextBox.Name = columns[i].ColumnName;
            detailRowTextBox.Size = new SizeU(Unit.Inch(colWidth), Unit.Inch(0.2));
            detailRowTextBox.Value = $@"= Fields.[{columns[i].ColumnName}]";
            detailRowTextBox.CanGrow = true;
            detailRowTextBox.Style.BorderStyle.Default = BorderType.Solid;
            detailRowTextBox.Style.BorderColor.Default = Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(212)))), ((int)(((byte)(212)))));
            detailRowTextBox.Style.Padding.Left = Unit.Inch(0.0099999997764825821D);
            detailRowTextBox.Style.Padding.Top = Unit.Inch(0.019999999552965164D);
            detailRowTextBox.Style.Font.Size = Unit.Point(8D);
            
            // Formatting rule to highlight summary rows in a gray background color and bold from the AddSummaryRows transformation
            FormattingRule formattingTotalRule = new FormattingRule();
            formattingTotalRule.Filters.Add(new Filter($"Fields.{internalSummaryRowIdentifierColName}", FilterOperator.Equal, "= \"True\""));
            formattingTotalRule.Style.Font.Bold = true;
            formattingTotalRule.Style.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(241)))), ((int)(((byte)(241)))), ((int)(((byte)(241)))));
            detailRowTextBox.ConditionalFormatting.Add(formattingTotalRule);
            
            // Apply conditional formatting from the ConditionalFormatting transformation
            if (conditionalFormatter != null)
            {
                var conditionalFormattingArray = conditionalFormatter.ToArray();
                for (int x = 0; x < conditionalFormattingArray.Length; x++)
                {
                    if (conditionalFormattingArray[x].Column.Equals(columns[i].ColumnName))
                    {
                        FormattingRule conditionalFormattingRule = new FormattingRule();
                        conditionalFormattingRule.Filters.Add(new Filter(
                            "Fields." + internalConditionalFormattingFormat, FilterOperator.Equal, "= \"True\""));
                        conditionalFormattingRule.Filters.Add(new Filter("Fields." + internalConditionalFormattingIndex,
                            FilterOperator.Equal, "= " + x.ToString()));

                        if (!conditionalFormattingArray[x].BackgroundColor.Equals("0"))
                            conditionalFormattingRule.Style.BackgroundColor =
                                System.Drawing.ColorTranslator.FromHtml(conditionalFormattingArray[x].BackgroundColor);
                        if (!conditionalFormattingArray[x].ForegroundColor.Equals("0"))
                            conditionalFormattingRule.Style.Color =
                                System.Drawing.ColorTranslator.FromHtml(conditionalFormattingArray[x].ForegroundColor);

                        detailRowTextBox.ConditionalFormatting.Add(conditionalFormattingRule);
                    }
                }
            }
            
            table1.Body.SetCellContent(0, i, detailRowTextBox);
            table1.Items.AddRange(new ReportItemBase[] {
                headerTextBox,
               detailRowTextBox
           });
        }
        
        return table1;
    }
    
    protected override void processReport()
    {
        processReport(false, false);
    }

    #region Helpers
    
    private void addIsSummaryRowColumn(DataTable _dataTable)
    {
        List<int> summaryRowIndexes = (List<int>)_dataTable.ExtendedProperties["SummaryRowIndexes"];
        
        _dataTable.Columns.Add(internalSummaryRowIdentifierColName, typeof(string));

        if (summaryRowIndexes == null || summaryRowIndexes.Count == 0)
            return;

        foreach (DataRow row in _dataTable.Rows)
        {
            int indexOfLastRow = _dataTable.Rows.IndexOf(row);
            bool isSummaryRow = false;
            
            foreach (int summaryRowIndex in summaryRowIndexes)
            {
                if (summaryRowIndex == indexOfLastRow)
                    isSummaryRow = true;
            }
            row[internalSummaryRowIdentifierColName] = isSummaryRow.ToString();
        }
    }
    
    private void addDuplicateColumnForPaging(DataTable _dataTable)
    {
        if (groupingCoulumn.Contains("PagingDuplicate"))
        {
            var columnToDuplicate = groupingCoulumn.Replace("PagingDuplicate", "");
            List<int> summaryRowIndexes = (List<int>)_dataTable.ExtendedProperties["SummaryRowIndexes"];
        
            _dataTable.Columns.Add(groupingCoulumn, typeof(string));
            
            if (summaryRowIndexes == null || summaryRowIndexes.Count == 0 || !_dataTable.Columns.Contains(columnToDuplicate))
                return;
            
            var grandTotalExists = _dataTable.Rows[_dataTable.Rows.Count - 1][columnToDuplicate].ToString() == "Grand Total";
            var grandTotalRowsAndIndexes = new Dictionary<int, DataRow>();
            
            foreach (DataRow row in _dataTable.Rows)
            {
                // Add grand total rows to each paging group after each summary row
                if (grandTotalExists)
                {
                    int indexOfLastRow = _dataTable.Rows.IndexOf(row);
                    if (row[internalSummaryRowIdentifierColName].ToString().Equals("True"))
                    {
                        DataRow newGrandTotalRow = _dataTable.NewRow();
                        newGrandTotalRow.ItemArray = _dataTable.Rows[_dataTable.Rows.Count - 1].ItemArray;
                        newGrandTotalRow[groupingCoulumn] = _dataTable.Rows[indexOfLastRow - 1][groupingCoulumn].ToString();
                        if(grandTotalRowsAndIndexes.Values.FirstOrDefault(r => r[groupingCoulumn].ToString() == newGrandTotalRow[groupingCoulumn].ToString()) == null)
                            grandTotalRowsAndIndexes.Add(indexOfLastRow + 2, newGrandTotalRow);
                    }
                }
                row[groupingCoulumn] = row[columnToDuplicate].ToString().Replace("Subtotal for ", "");
            }
            
            // Remove the original grand total row and insert updated grand total for each paging group 
            if (grandTotalExists && grandTotalRowsAndIndexes.Count > 0)
            {
                _dataTable.Rows.RemoveAt(_dataTable.Rows.Count - 1);
                var newRowsCount = 0; 
                foreach (var item in grandTotalRowsAndIndexes)
                {
                    _dataTable.Rows.InsertAt(item.Value, item.Key + newRowsCount);
                    newRowsCount++;
                }
            }
        }
    }

    private void addIsRowToApplyConditionalFormattingToColumn(DataTable _dataTable, ConditionalFormatter[] _conditionalFormatter)
    {
        _dataTable.Columns.Add(internalConditionalFormattingIndex, typeof(string));
        _dataTable.Columns.Add(internalConditionalFormattingFormat, typeof(string));
        
        for (int i = 0; i < _conditionalFormatter.Length; i++)
        {
            foreach (DataRow row in _dataTable.Rows)
            {
                int rowIndex = _dataTable.Rows.IndexOf(row);
                bool isRowToFormat = false;
            
                foreach (int conditionalFormattingRowIndex in _conditionalFormatter[i].RowIndices)
                {
                    if (conditionalFormattingRowIndex == rowIndex)
                    {
                        isRowToFormat = true;
                        row[internalConditionalFormattingIndex] = i.ToString();
                        row[internalConditionalFormattingFormat] = isRowToFormat.ToString();
                    }
                }

            }
        }
    }

    private static double GetColumnWidth(DataTable dataTable, string columnName, string columnAlias)
    {
        var longestWidth = columnAlias.Length; //characters
        
        foreach (DataRow row in dataTable.Rows)
        {
            var cellCharWidth = row[columnName].ToString().Length;

            if (cellCharWidth > longestWidth)
            {
                longestWidth = cellCharWidth;
            }
        }

        if (longestWidth <= 10)
        {
            return 1;
        }

        if (longestWidth < 25)
        {
            return 2;
        }

        return 2.5;
    }
    private void UpdateDepartmentFilterLabelIfConfiguredToApplyToUserOnly()
    {
        foreach (var config in dataExtractor.getSelectedConfigurationFields())
        {
            if (config.getFieldCode == CONFIG_FIELDCODE_DEPARTMENT_FILTER && config.getFieldValue == CONFIG_DEPARTMENT_FILTER_APPLIES_TO_USER)
            { 
                Control filterControl = SWFiltersManager.getFilter(filters, SWFilterType.Department);
                var parentControls = filterControl.Parent.Controls;
                
                foreach (Control control in parentControls)
                {
                    if (control.GetType() == typeof(System.Web.UI.WebControls.Label) && control.Visible == true)
                    {
                        var label = (Label)control;

                        if (!label.Attributes["SWFilterType"].IsNullOrWhiteSpace() && label.Attributes["SWFilterType"] == SWFilterType.Department.ToString())
                        {
                            label.Text = "Staff Department";
                            return;
                        }
                    }
                }
            }
        }
    }

    private void updateGroupingColumnNameForPagination(ConfigurationField[] _configurationFields, dynamic _extractConfigJson)
    {
        if (_configurationFields.FirstOrDefault(c => c.getFieldCode == CONFIG_INPUT_PAGINATION_FILTERS && !string.IsNullOrEmpty(c.getFieldValue)) != null)
        {
            groupingCoulumn = _configurationFields.First(c => c.getFieldCode == CONFIG_INPUT_PAGINATION_FILTERS && !string.IsNullOrEmpty(c.getFieldValue)).getFieldValue;
            JObject jObject = JObject.Parse(_extractConfigJson.ToString());
            if (jObject.ContainsKey("transformations"))
            {
                var transformations = jObject["transformations"];
                foreach (var transformation in transformations)
                {
                    if (transformation["transformationType"].ToString() == "ADD_SUMMARY_ROWS")
                    {
                        groupingCoulumn = "PagingDuplicate" + groupingCoulumn;
                        break;
                    }
                }
            }
        }
    }

    private void createDynamicRowGroup(DataTable _dataTable ,Table table1)
    {
        Telerik.Reporting.TableGroup dynamicRowGroup = new Telerik.Reporting.TableGroup();
        dynamicRowGroup.Name = "dynamicRowGroup";
        if (!string.IsNullOrEmpty(groupingCoulumn) && _dataTable.Columns.Contains(groupingCoulumn))
        {
            dynamicRowGroup.Groupings.Add(new Grouping("= Fields.[" + groupingCoulumn + "]"));
            dynamicRowGroup.Sortings.Add(new Sorting("= Fields.[" + groupingCoulumn  + "]", Telerik.Reporting.SortDirection.Asc));
            Telerik.Reporting.TableGroup detailRowGroup = new Telerik.Reporting.TableGroup();
            detailRowGroup.Name = "detailRowGroup";
            detailRowGroup.Groupings.Add(new Grouping(null));
            dynamicRowGroup.ChildGroups.Add(detailRowGroup);
            dynamicRowGroup.GroupKeepTogether = true;
            dynamicRowGroup.PageBreak = PageBreak.After;
            table1.ColumnHeadersPrintOnEveryPage = true;
        }
        else
        {
            dynamicRowGroup.Groupings.Add(new Telerik.Reporting.Grouping(null));
        }
        
        table1.RowGroups.Add(dynamicRowGroup);
    }

    #endregion

    private void createEditReportButton()
    {
        Button editButton = new Button();
        editButton.ID = "edit-report";
        editButton.Text = "Edit Report";
        editButton.Click += EditButton_Click;
        editButtonPlaceholder.Controls.Add(editButton);
    }

    protected void EditButton_Click(object sender, EventArgs e)
    {
        string dataExtractorID = HIDDENDataExtractorID.Value;
        string redirectUrl = "~/Home/Settings/Utilities/DataExtractor/DataExtractorBuilder3.aspx#/detail/" + dataExtractorID;
        Response.Redirect(redirectUrl);
    }
    
    private void ajaxify(String _sourceControlID, params String[] _toAjaxifyControlIDs)
    {
        AjaxSetting setting = new AjaxSetting(_sourceControlID);

        foreach (String toAjaxifyControlID in _toAjaxifyControlIDs)
        {
            AjaxUpdatedControl ajaxifyControl = new AjaxUpdatedControl(toAjaxifyControlID, "RadAjaxManager1");
            ajaxifyControl.UpdatePanelRenderMode = UpdatePanelRenderMode.Inline;
            setting.UpdatedControls.Add(ajaxifyControl);
        }

        RadAjaxManager1.AjaxSettings.Add(setting);
    }

    private System.Web.UI.WebControls.Panel GetDeliverToMessageCenterPanel()
    {
        System.Web.UI.WebControls.Panel newPanel = new System.Web.UI.WebControls.Panel();
        newPanel.CssClass = "DeliverToMessageCenterDiv";
        
        Button asyncButton = new Button();
        asyncButton.ID = "DeliverToMessageCenter";
        asyncButton.Text = "Deliver to Message Center";
        asyncButton.CssClass = "DeliverToMessageCenterButton";
        asyncButton.ToolTip = "For large reports: Have it delivered to your internal messages!";
        newPanel.Controls.Add(asyncButton);
        asyncButton.Click += DeliverToMessageCenter_Click;
        newPanel.Controls.Add(asyncButton);

        return newPanel;
    }

    private System.Web.UI.WebControls.Panel GetDeliverToForeignSystemPanel()
    {
        System.Web.UI.WebControls.Panel newPanel = new System.Web.UI.WebControls.Panel();
        newPanel.CssClass = "DeliverToForeignSystemDiv";

        RadComboBox foreignSystems = new RadComboBox();
        DSForeignSystem[] systems = (BOForeignSystem
                .getForeignSystemsByClientID(getClientID()) ?? Enumerable.Empty<DSForeignSystem>())
            .Where(x => x.Code == "V3_REPORT_EXPORT")
            .OrderBy(x => x.Description)
            .ToArray();
        
        foreignSystems.DataSource = systems;
        foreignSystems.DataTextField = "Description";
        foreignSystems.DataValueField = "ForeignSystemID";
        foreignSystems.Filter = RadComboBoxFilter.Contains;
        foreignSystems.MarkFirstMatch = true;
        foreignSystems.AllowCustomText = false;
        foreignSystems.ID = "ForeignSystemsDropDown";
        foreignSystems.CssClass = "ForeignSystemsDropDown";
        foreignSystems.DataBind();
        
        newPanel.Controls.Add(foreignSystems);
        
        
        Button asyncButton = new Button();
        asyncButton.ID = "DeliverToForeignSystem";
        asyncButton.Text = "Deliver to External";
        asyncButton.CssClass = "DeliverToForeignSystemButton";
        asyncButton.Click += DeliverToForeignSystem_Click;
        if (!systems.Any())
        {
            asyncButton.Enabled = false;
            foreignSystems.Enabled = false;
        }

        newPanel.Controls.Add(asyncButton);

        return newPanel;
    }
    
    
    private bool AddNewControlAfterTargetControl(Control container, string controlId, System.Web.UI.WebControls.Panel newPanel)
    {
        var success = false;

        foreach (Control control in container.Controls)
        {
            if (control.ID != null && control.ID.Equals(controlId, StringComparison.OrdinalIgnoreCase))
            {
                var index = container.Parent.Controls.IndexOf(container) + 1;
                container.Parent.Controls.AddAt(index, newPanel);
                return true;
            }

            if (control.HasControls())
            {
                success = AddNewControlAfterTargetControl(control, controlId, newPanel);
                if (!success) { continue; }

                return success;
            }
        }

        return success;
    }
    
    public T FindControlRecursive<T>(Control root, string id) where T : Control
    {
        if (root.ID == id && root is T)
            return (T)root;

        foreach (Control child in root.Controls)
        {
            T found = FindControlRecursive<T>(child, id);
            if (found != null)
                return found;
        }

        return null;
    }
}