C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\ApplicationInsights.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\csc.exe
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\csc.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\csc.rsp
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\csi.exe
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\csi.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\csi.rsp
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.CSharp.Core.targets
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.VisualBasic.Core.targets
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.AppContext.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Console.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.IO.Compression.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.IO.Pipes.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Reflection.Metadata.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Security.AccessControl.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Security.Claims.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Security.Principal.Windows.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Threading.Thread.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Xml.XPath.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\vbc.exe
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\vbc.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\vbc.rsp
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\VBCSCompiler.exe
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\roslyn\VBCSCompiler.exe.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\REPORTS\Utilities\SW-LOGO.png
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\REPORTS\Utilities\testpdf.pdf
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.TestPlatform.AdapterUtilities.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\app.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Config\appSettings.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Config\connectionStrings.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.IntegrationTests.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.IntegrationTests.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.IntegrationTests.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Castle.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Effort.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\EntityFramework.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\EntityFramework.SqlServer.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\LazyCache.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\log4net.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.VisualStudio.TestPlatform.TestFramework.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Moq.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\NMemory.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\RestSharp.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorks.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Infrastructure.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Services.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Web.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorksDAO.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorksREPORTS.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\UAParser.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Web.UI.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Azure.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Azure.AI.OpenAI.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\QuikGraph.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Flee.Net45.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Reporting.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Markdig.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Twilio.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\WebGrease.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.XLS.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Renci.SshNet.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.Pdf.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Ninject.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\WebActivatorEx.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Optimization.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Mvc.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.AspNet.Identity.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.AspNet.Identity.Owin.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.Security.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Owin.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Http.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AutoMapper.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Net.Http.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.AspNet.Identity.EntityFramework.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Net.Http.Formatting.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Ninject.Web.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.Security.Cookies.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Http.WebHost.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Datadog.Trace.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Web.Infrastructure.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.S3.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.SimpleEmail.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.ReportViewer.WebForms.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Zip.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Text.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.EC2.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AjaxControlToolkit.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\WebSignature.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\EPPlus.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.Doc.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\GleamTech.DocumentUltimate.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\ComponentSpace.SAML2.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\IronPdf.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Flow.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\GleamTech.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.License.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\FuzzyString.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Datadog.Trace.AspNet.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Ninject.Web.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.ClientModel.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Memory.Data.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Antlr3.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.mshtml.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Razor.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.WebPages.Razor.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.WebPages.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.Security.OAuth.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\MsgPack.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Reflection.Metadata.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Fixed.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.WebPages.Deployment.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Common.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Common.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Core.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Infrastructure.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Infrastructure.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Services.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Services.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Web.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Web.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorksDAO.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorksDAO.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorksREPORTS.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorksREPORTS.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorks.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SETWorks.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Castle.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Effort.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\EntityFramework.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\EntityFramework.SqlServer.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\log4net.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Caching.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Caching.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Options.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Extensions.Primitives.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Tokens.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.VisualStudio.TestPlatform.TestFramework.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Moq.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\NMemory.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\RestSharp.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Buffers.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.IdentityModel.Tokens.Jwt.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Numerics.Vectors.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Threading.Tasks.Extensions.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\UAParser.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Azure.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Azure.AI.OpenAI.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\QuikGraph.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\SetWorks.Reporting.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Markdig.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Twilio.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.XLS.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Renci.SshNet.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.Pdf.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Ninject.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Optimization.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Mvc.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.AspNet.Identity.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.AspNet.Identity.Owin.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.Security.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Http.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AutoMapper.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Protocols.OpenIdConnect.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Protocols.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.AspNet.Identity.EntityFramework.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Net.Http.Formatting.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Ninject.Web.Common.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.Security.Cookies.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Http.WebHost.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Datadog.Trace.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.S3.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.S3.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.SimpleEmail.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.SimpleEmail.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.ReportViewer.WebForms.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.ReportViewer.WebForms.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Zip.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Text.Json.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Collections.Immutable.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.EC2.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\AWSSDK.EC2.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\EPPlus.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.Doc.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\GleamTech.DocumentUltimate.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\ComponentSpace.SAML2.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.Scripting.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.Scripting.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\IronPdf.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Flow.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\GleamTech.Common.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Spire.License.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Datadog.Trace.AspNet.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Ninject.Web.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.CSharp.Scripting.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.CSharp.Scripting.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.ClientModel.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Memory.Data.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Diagnostics.DiagnosticSource.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Antlr3.Runtime.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.JsonWebTokens.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.IdentityModel.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.Razor.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.WebPages.Razor.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.WebPages.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.Owin.Security.OAuth.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\MsgPack.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Reflection.Metadata.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Telerik.Windows.Documents.Fixed.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.CSharp.pdb
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\Microsoft.CodeAnalysis.CSharp.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\System.Web.WebPages.Deployment.xml
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\cs\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\de\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\es\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\fr\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\it\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\ja\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\ko\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\pl\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\pt-BR\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\ru\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\tr\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\zh-Hans\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\bin\Debug\zh-Hant\Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\obj\Debug\SetWorks.IntegrationTests.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\obj\Debug\SetWorks.IntegrationTests.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\obj\Debug\SetWorks.IntegrationTests.csproj.CopyComplete
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\obj\Debug\SetWorks.IntegrationTests.dll
C:\Users\<USER>\Documents\SW\SetWorks.IntegrationTests\obj\Debug\SetWorks.IntegrationTests.pdb
