C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Common.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Common.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\netstandard.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.AppContext.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Collections.Concurrent.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Collections.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Collections.NonGeneric.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Collections.Specialized.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ComponentModel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Console.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Data.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.Debug.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.Process.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.Tools.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Drawing.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Dynamic.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Globalization.Calendars.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Globalization.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Globalization.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.Compression.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.FileSystem.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.Pipes.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Linq.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Linq.Expressions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Linq.Parallel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Linq.Queryable.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.Http.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.NameResolution.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.NetworkInformation.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.Ping.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.Requests.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.Security.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.Sockets.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Net.WebSockets.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ObjectModel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Reflection.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Reflection.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Reflection.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Resources.Reader.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Resources.ResourceManager.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Resources.Writer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.Handles.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.InteropServices.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.Numerics.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.Claims.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.Principal.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Security.SecureString.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Text.Encoding.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Text.RegularExpressions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.Overlapped.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.Tasks.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.Thread.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.ThreadPool.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.Timer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Xml.XDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Xml.XmlDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Xml.XPath.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Azure.AI.OpenAI.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Azure.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\EntityFramework.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\EntityFramework.SqlServer.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Flee.Net45.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\log4net.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Markdig.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Http.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\OpenAI.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\QuikGraph.Data.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\QuikGraph.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\QuikGraph.Graphviz.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Infrastructure.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Reporting.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SETWorksDAO.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ClientModel.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Memory.Data.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Text.Json.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ValueTuple.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\UAParser.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Twilio.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\WebGrease.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.XLS.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.Pdf.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.Doc.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.S3.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.Core.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.SimpleEmail.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.ReportViewer.WebForms.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.Reporting.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\LazyCache.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.Windows.Zip.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\RestSharp.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Antlr3.Runtime.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.License.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.mshtml.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Core.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Infrastructure.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Infrastructure.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SetWorks.Reporting.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SETWorksDAO.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\SETWorksDAO.dll.config
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Azure.AI.OpenAI.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Azure.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\EntityFramework.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\EntityFramework.SqlServer.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\log4net.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Markdig.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Bcl.AsyncInterfaces.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Configuration.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Configuration.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Configuration.Binder.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.DependencyInjection.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Http.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Logging.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Logging.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Options.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Primitives.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\OpenAI.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\QuikGraph.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\QuikGraph.Data.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\QuikGraph.Graphviz.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Buffers.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ClientModel.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Diagnostics.DiagnosticSource.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Memory.Data.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Numerics.Vectors.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Runtime.CompilerServices.Unsafe.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Text.Encodings.Web.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Text.Json.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.Threading.Tasks.Extensions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.ValueTuple.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\UAParser.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Twilio.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.XLS.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.Pdf.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.Doc.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.S3.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.S3.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.Core.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.Core.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.SimpleEmail.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\AWSSDK.SimpleEmail.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.ReportViewer.WebForms.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.ReportViewer.WebForms.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.Reporting.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.Reporting.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Telerik.Windows.Zip.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\RestSharp.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\System.IdentityModel.Tokens.Jwt.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.IdentityModel.Tokens.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Antlr3.Runtime.pdb
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Spire.License.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Caching.Abstractions.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\bin\Debug\Microsoft.Extensions.Caching.Memory.xml
C:\Users\<USER>\Documents\SW\SetWorks.Common\obj\Debug\SetWorks.Common.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\SW\SetWorks.Common\obj\Debug\SetWorks.Common.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\SW\SetWorks.Common\obj\Debug\SetWorks.Common.csproj.CopyComplete
C:\Users\<USER>\Documents\SW\SetWorks.Common\obj\Debug\SetWorks.Common.dll
C:\Users\<USER>\Documents\SW\SetWorks.Common\obj\Debug\SetWorks.Common.pdb
