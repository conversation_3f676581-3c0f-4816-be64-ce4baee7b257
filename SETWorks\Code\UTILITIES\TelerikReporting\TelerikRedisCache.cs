using System;
using System.Collections;
using StackExchange.Redis;
using Telerik.Reporting.Cache.Interfaces;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Custom Redis cache implementation for Telerik Reporting following official documentation
    /// https://docs.telerik.com/reporting/embedding-reports/cache-management/configuring-custom-cache-provider
    /// </summary>
    public class TelerikRedisCache : ICache
    {
        private readonly IDatabase _database;
        private readonly ConnectionMultiplexer _connection;

        public TelerikRedisCache(IDictionary parameters)
        {
            // The 'parameters' dictionary is initialized from the Telerik.Reporting/Cache configuration section
            string connectionString = "127.0.0.1:6379,ssl=False,abortConnect=False,connectTimeout=60000,syncTimeout=20000,asyncTimeout=10000,connectRetry=3";
            int databaseNumber = 1; // Use database 1 for reports, database 0 for sessions

            // Check if connection string is provided in parameters
            if (parameters != null && parameters.Contains("ConnectionString"))
            {
                var connectionStringKey = parameters["ConnectionString"]?.ToString();
                if (!string.IsNullOrEmpty(connectionStringKey))
                {
                    // Try to get connection string from app settings
                    var appSettingsConnectionString = System.Configuration.ConfigurationManager.AppSettings[connectionStringKey];
                    if (!string.IsNullOrEmpty(appSettingsConnectionString))
                    {
                        connectionString = appSettingsConnectionString;
                    }
                }
            }

            // Parse database number from connection string if specified
            var configOptions = ConfigurationOptions.Parse(connectionString);
            if (configOptions.DefaultDatabase.HasValue)
            {
                databaseNumber = configOptions.DefaultDatabase.Value;
            }
            else
            {
                configOptions.DefaultDatabase = databaseNumber;
            }

            try
            {
                _connection = ConnectionMultiplexer.Connect(configOptions);
                _database = _connection.GetDatabase(databaseNumber);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to connect to Redis for Telerik Reporting cache: {ex.Message}", ex);
            }
        }

        public bool HasValue(string key)
        {
            try
            {
                return _database.KeyExists(key);
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking reports
                System.Diagnostics.Debug.WriteLine($"TelerikRedisCache.HasValue error for key '{key}': {ex.Message}");
                return false;
            }
        }

        public byte[] GetValue(string key)
        {
            try
            {
                return _database.StringGet(key);
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking reports
                System.Diagnostics.Debug.WriteLine($"TelerikRedisCache.GetValue error for key '{key}': {ex.Message}");
                return null;
            }
        }

        public void SetValue(string key, byte[] value)
        {
            try
            {
                // Set with a reasonable expiration time (e.g., 1 hour) to prevent Redis from growing indefinitely
                _database.StringSet(key, value, TimeSpan.FromHours(1));
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking reports
                System.Diagnostics.Debug.WriteLine($"TelerikRedisCache.SetValue error for key '{key}': {ex.Message}");
            }
        }

        public void Clear()
        {
            try
            {
                // Clear all keys in the current database
                var server = _connection.GetServer(_connection.GetEndPoints()[0]);
                var database = _database.Database;
                var keys = server.Keys(database, pattern: "*");
                
                foreach (var key in keys)
                {
                    _database.KeyDelete(key);
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking reports
                System.Diagnostics.Debug.WriteLine($"TelerikRedisCache.Clear error: {ex.Message}");
            }
        }

        public void Dispose()
        {
            try
            {
                _connection?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TelerikRedisCache.Dispose error: {ex.Message}");
            }
        }
    }
}
