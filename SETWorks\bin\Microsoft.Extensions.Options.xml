<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Options</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`1">
            <summary>
            Implementation of IConfigureNamedOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`1.#ctor(System.String,System.Action{`0})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`1.Configure(System.String,`0)">
            <summary>
            Invokes the registered configure Action if the name matches.
            </summary>
            <param name="name"></param>
            <param name="options"></param>
        </member>
        <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`2">
            <summary>
            Implementation of IConfigureNamedOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`2.#ctor(System.String,`1,System.Action{`0,`1})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency">A dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`2.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`3">
            <summary>
            Implementation of IConfigureNamedOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`3.#ctor(System.String,`1,`2,System.Action{`0,`1,`2})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`3.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`4">
            <summary>
            Implementation of IConfigureNamedOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
            <typeparam name="TDep3"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`4.#ctor(System.String,`1,`2,`3,System.Action{`0,`1,`2,`3})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="dependency3">A third dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`4.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`5">
            <summary>
            Implementation of IConfigureNamedOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
            <typeparam name="TDep3"></typeparam>
            <typeparam name="TDep4"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`5.#ctor(System.String,`1,`2,`3,`4,System.Action{`0,`1,`2,`3,`4})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency1">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="dependency3">A third dependency.</param>
            <param name="dependency4">A fourth dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`5.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.ConfigureNamedOptions`6">
            <summary>
            Implementation of IConfigureNamedOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
            <typeparam name="TDep3"></typeparam>
            <typeparam name="TDep4"></typeparam>
            <typeparam name="TDep5"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureNamedOptions`6.#ctor(System.String,`1,`2,`3,`4,`5,System.Action{`0,`1,`2,`3,`4,`5})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency1">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="dependency3">A third dependency.</param>
            <param name="dependency4">A fourth dependency.</param>
            <param name="dependency5">A fifth dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureNamedOptions`6.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.ConfigureOptions`1">
            <summary>
            Implementation of IConfigureOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureOptions`1.#ctor(System.Action{`0})">
            <summary>
            Constructor.
            </summary>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.ConfigureOptions`1.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.ConfigureOptions`1.Configure(`0)">
            <summary>
            Invokes the registered configure Action if the name matches.
            </summary>
            <param name="options"></param>
        </member>
        <member name="T:Microsoft.Extensions.Options.IConfigureNamedOptions`1">
            <summary>
            Represents something that configures the TOptions type.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.IConfigureNamedOptions`1.Configure(System.String,`0)">
            <summary>
            Invoked to configure a TOptions instance.
            </summary>
            <param name="name">The name of the options instance being configured.</param>
            <param name="options">The options instance to configure.</param>
        </member>
        <member name="T:Microsoft.Extensions.Options.IConfigureOptions`1">
            <summary>
            Represents something that configures the TOptions type.
            Note: These are run before all <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1"/>.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.IConfigureOptions`1.Configure(`0)">
            <summary>
            Invoked to configure a TOptions instance.
            </summary>
            <param name="options">The options instance to configure.</param>
        </member>
        <member name="T:Microsoft.Extensions.Options.IOptions`1">
            <summary>
            Used to retrieve configured TOptions instances.
            </summary>
            <typeparam name="TOptions">The type of options being requested.</typeparam>
        </member>
        <member name="P:Microsoft.Extensions.Options.IOptions`1.Value">
            <summary>
            The default configured TOptions instance, equivalent to Get(string.Empty).
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.IOptionsChangeTokenSource`1">
            <summary>
            Used to fetch IChangeTokens used for tracking options changes.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsChangeTokenSource`1.GetChangeToken">
            <summary>
            Returns a IChangeToken which can be used to register a change notification callback.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Extensions.Options.IOptionsChangeTokenSource`1.Name">
            <summary>
            The name of the option instance being changed.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.IOptionsFactory`1">
            <summary>
            Used to create TOptions instances.
            </summary>
            <typeparam name="TOptions">The type of options being requested.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsFactory`1.Create(System.String)">
            <summary>
            Returns a configured TOptions instance with the given name.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.IOptionsMonitor`1">
            <summary>
            Used for notifications when TOptions instances change.
            </summary>
            <typeparam name="TOptions">The options type.</typeparam>
        </member>
        <member name="P:Microsoft.Extensions.Options.IOptionsMonitor`1.CurrentValue">
            <summary>
            Returns the current TOptions instance with the <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsMonitor`1.Get(System.String)">
            <summary>
            Returns a configured TOptions instance with the given name.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsMonitor`1.OnChange(System.Action{`0,System.String})">
            <summary>
            Registers a listener to be called whenever a named TOptions changes.
            </summary>
            <param name="listener">The action to be invoked when TOptions has changed.</param>
            <returns>An IDisposable which should be disposed to stop listening for changes.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Options.IOptionsMonitorCache`1">
            <summary>
            Used by <see cref="T:Microsoft.Extensions.Options.IOptionsMonitor`1"/> to cache TOptions instances.
            </summary>
            <typeparam name="TOptions">The type of options being requested.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.GetOrAdd(System.String,System.Func{`0})">
            <summary>
            Gets a named options instance, or adds a new instance created with createOptions.
            </summary>
            <param name="name">The name of the options instance.</param>
            <param name="createOptions">The func used to create the new instance.</param>
            <returns>The options instance.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.TryAdd(System.String,`0)">
            <summary>
            Tries to adds a new option to the cache, will return false if the name already exists.
            </summary>
            <param name="name">The name of the options instance.</param>
            <param name="options">The options instance.</param>
            <returns>Whether anything was added.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.TryRemove(System.String)">
            <summary>
            Try to remove an options instance.
            </summary>
            <param name="name">The name of the options instance.</param>
            <returns>Whether anything was removed.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsMonitorCache`1.Clear">
            <summary>
            Clears all options instances from the cache.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.IOptionsSnapshot`1">
            <summary>
            Used to access the value of TOptions for the lifetime of a request.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.IOptionsSnapshot`1.Get(System.String)">
            <summary>
            Returns a configured TOptions instance with the given name.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.IPostConfigureOptions`1">
            <summary>
            Represents something that configures the TOptions type.
            Note: These are run after all <see cref="T:Microsoft.Extensions.Options.IConfigureOptions`1"/>.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.IPostConfigureOptions`1.PostConfigure(System.String,`0)">
            <summary>
            Invoked to configure a TOptions instance.
            </summary>
            <param name="name">The name of the options instance being configured.</param>
            <param name="options">The options instance to configured.</param>
        </member>
        <member name="T:Microsoft.Extensions.Options.Options">
            <summary>
            Helper class.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.Options.Create``1(``0)">
            <summary>
            Creates a wrapper around an instance of TOptions to return itself as an IOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <param name="options"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Extensions.Options.OptionsBuilder`1">
            <summary>
            Used to configure TOptions instances.
            </summary>
            <typeparam name="TOptions">The type of options being requested.</typeparam>
        </member>
        <member name="P:Microsoft.Extensions.Options.OptionsBuilder`1.Name">
            <summary>
            The default name of the TOptions instance.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.OptionsBuilder`1.Services">
            <summary>
            The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for the options being configured.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.#ctor(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Constructor.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for the options being configured.</param>
            <param name="name">The default name of the TOptions instance, if null Options.DefaultName is used.</param>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})">
            <summary>
            Registers an action used to configure a particular type of options.
            Note: These are run before all <seealso cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})"/>.
            </summary>
            <param name="configureOptions">The action used to configure the options.</param>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsBuilder`1.PostConfigure(System.Action{`0})">
            <summary>
            Registers an action used to configure a particular type of options.
            Note: These are run after all <seealso cref="M:Microsoft.Extensions.Options.OptionsBuilder`1.Configure(System.Action{`0})"/>.
            </summary>
            <param name="configureOptions">The action used to configure the options.</param>
        </member>
        <member name="T:Microsoft.Extensions.Options.OptionsCache`1">
            <summary>
            Used to cache TOptions instances.
            </summary>
            <typeparam name="TOptions">The type of options being requested.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsCache`1.Clear">
            <summary>
            Clears all options instances from the cache.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsCache`1.GetOrAdd(System.String,System.Func{`0})">
            <summary>
            Gets a named options instance, or adds a new instance created with createOptions.
            </summary>
            <param name="name">The name of the options instance.</param>
            <param name="createOptions">The func used to create the new instance.</param>
            <returns>The options instance.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsCache`1.TryAdd(System.String,`0)">
            <summary>
            Tries to adds a new option to the cache, will return false if the name already exists.
            </summary>
            <param name="name">The name of the options instance.</param>
            <param name="options">The options instance.</param>
            <returns>Whether anything was added.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsCache`1.TryRemove(System.String)">
            <summary>
            Try to remove an options instance.
            </summary>
            <param name="name">The name of the options instance.</param>
            <returns>Whether anything was removed.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Options.OptionsFactory`1">
            <summary>
            Implementation of IOptionsFactory.
            </summary>
            <typeparam name="TOptions">The type of options being requested.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsFactory`1.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IConfigureOptions{`0}},System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IPostConfigureOptions{`0}})">
            <summary>
            Initializes a new instance with the specified options configurations.
            </summary>
            <param name="setups">The configuration actions to run.</param>
            <param name="postConfigures">The initialization actions to run.</param>
        </member>
        <member name="T:Microsoft.Extensions.Options.OptionsManager`1">
            <summary>
            Implementation of IOptions and IOptionsSnapshot.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsManager`1.#ctor(Microsoft.Extensions.Options.IOptionsFactory{`0})">
            <summary>
            Initializes a new instance with the specified options configurations.
            </summary>
            <param name="factory">The factory to use to create options.</param>
        </member>
        <member name="T:Microsoft.Extensions.Options.OptionsMonitor`1">
            <summary>
            Implementation of IOptionsMonitor.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsMonitor`1.#ctor(Microsoft.Extensions.Options.IOptionsFactory{`0},System.Collections.Generic.IEnumerable{Microsoft.Extensions.Options.IOptionsChangeTokenSource{`0}},Microsoft.Extensions.Options.IOptionsMonitorCache{`0})">
            <summary>
            Constructor.
            </summary>
            <param name="factory">The factory to use to create options.</param>
            <param name="sources">The sources used to listen for changes to the options instance.</param>
            <param name="cache">The cache used to store options.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.OptionsMonitor`1.CurrentValue">
            <summary>
            The present value of the options.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsMonitor`1.OnChange(System.Action{`0,System.String})">
            <summary>
            Registers a listener to be called whenever TOptions changes.
            </summary>
            <param name="listener">The action to be invoked when TOptions has changed.</param>
            <returns>An IDisposable which should be disposed to stop listening for changes.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Options.OptionsMonitorExtensions">
            <summary>
            Extension methods for IOptionsMonitor.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsMonitorExtensions.OnChange``1(Microsoft.Extensions.Options.IOptionsMonitor{``0},System.Action{``0})">
            <summary>
            Registers a listener to be called whenever TOptions changes.
            </summary>
            <param name="monitor">The IOptionsMonitor.</param>
            <param name="listener">The action to be invoked when TOptions has changed.</param>
            <returns>An IDisposable which should be disposed to stop listening for changes.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Options.OptionsWrapper`1">
            <summary>
            IOptions wrapper that returns the options instance.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsWrapper`1.#ctor(`0)">
            <summary>
            Intializes the wrapper with the options instance to return.
            </summary>
            <param name="options">The options instance to return.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.OptionsWrapper`1.Value">
            <summary>
            The options instance.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsWrapper`1.Add(System.String,`0)">
            <summary>
            This method is obsolete and will be removed in a future version.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsWrapper`1.Get(System.String)">
            <summary>
            This method is obsolete and will be removed in a future version.
            </summary>
            <param name="name">This parameter is ignored.</param>
            <returns>The <see cref="P:Microsoft.Extensions.Options.OptionsWrapper`1.Value"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Options.OptionsWrapper`1.Remove(System.String)">
            <summary>
            This method is obsolete and will be removed in a future version.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`1">
            <summary>
            Implementation of <see cref="T:Microsoft.Extensions.Options.IPostConfigureOptions`1"/>.
            </summary>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`1.#ctor(System.String,System.Action{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Extensions.Options.PostConfigureOptions`1"/>.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`1.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`1.Action">
            <summary>
            The initialization action.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`1.PostConfigure(System.String,`0)">
            <summary>
            Invokes the registered initialization Action if the name matches.
            </summary>
            <param name="name"></param>
            <param name="options"></param>
        </member>
        <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`2">
            <summary>
            Implementation of IPostConfigureOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`2.#ctor(System.String,`1,System.Action{`0,`1})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency">A dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`2.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`2.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`3">
            <summary>
            Implementation of IPostConfigureOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`3.#ctor(System.String,`1,`2,System.Action{`0,`1,`2})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`3.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`3.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`4">
            <summary>
            Implementation of IPostConfigureOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
            <typeparam name="TDep3"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`4.#ctor(System.String,`1,`2,`3,System.Action{`0,`1,`2,`3})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="dependency3">A third dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`4.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`5">
            <summary>
            Implementation of IPostConfigureOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
            <typeparam name="TDep3"></typeparam>
            <typeparam name="TDep4"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`5.#ctor(System.String,`1,`2,`3,`4,System.Action{`0,`1,`2,`3,`4})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency1">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="dependency3">A third dependency.</param>
            <param name="dependency4">A fourth dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`5.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Options.PostConfigureOptions`6">
            <summary>
            Implementation of IPostConfigureOptions.
            </summary>
            <typeparam name="TOptions"></typeparam>
            <typeparam name="TDep1"></typeparam>
            <typeparam name="TDep2"></typeparam>
            <typeparam name="TDep3"></typeparam>
            <typeparam name="TDep4"></typeparam>
            <typeparam name="TDep5"></typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Options.PostConfigureOptions`6.#ctor(System.String,`1,`2,`3,`4,`5,System.Action{`0,`1,`2,`3,`4,`5})">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the options.</param>
            <param name="dependency1">A dependency.</param>
            <param name="dependency2">A second dependency.</param>
            <param name="dependency3">A third dependency.</param>
            <param name="dependency4">A fourth dependency.</param>
            <param name="dependency5">A fifth dependency.</param>
            <param name="action">The action to register.</param>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Name">
            <summary>
            The options name.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.PostConfigureOptions`6.Action">
            <summary>
            The configuration action.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.Resources.Error_CannotActivateAbstractOrInterface">
            <summary>
            Cannot create instance of type '{0}' because it is either abstract or an interface.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.Resources.FormatError_CannotActivateAbstractOrInterface(System.Object)">
            <summary>
            Cannot create instance of type '{0}' because it is either abstract or an interface.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.Resources.Error_FailedBinding">
            <summary>
            Failed to convert '{0}' to type '{1}'.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.Resources.FormatError_FailedBinding(System.Object,System.Object)">
            <summary>
            Failed to convert '{0}' to type '{1}'.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.Resources.Error_FailedToActivate">
            <summary>
            Failed to create instance of type '{0}'.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.Resources.FormatError_FailedToActivate(System.Object)">
            <summary>
            Failed to create instance of type '{0}'.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.Resources.Error_MissingParameterlessConstructor">
            <summary>
            Cannot create instance of type '{0}' because it is missing a public parameterless constructor.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.Resources.FormatError_MissingParameterlessConstructor(System.Object)">
            <summary>
            Cannot create instance of type '{0}' because it is missing a public parameterless constructor.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.Resources.Error_NoIConfigureOptions">
            <summary>
            No IConfigureOptions&lt;&gt; or IPostConfigureOptions&lt;&gt; implementations were found.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.Resources.FormatError_NoIConfigureOptions">
            <summary>
            No IConfigureOptions&lt;&gt; or IPostConfigureOptions&lt;&gt; implementations were found.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Options.Resources.Error_NoIConfigureOptionsAndAction">
            <summary>
            No IConfigureOptions&lt;&gt; or IPostConfigureOptions&lt;&gt; implementations were found, did you mean to call Configure&lt;&gt; or PostConfigure&lt;&gt;?
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Options.Resources.FormatError_NoIConfigureOptionsAndAction">
            <summary>
            No IConfigureOptions&lt;&gt; or IPostConfigureOptions&lt;&gt; implementations were found, did you mean to call Configure&lt;&gt; or PostConfigure&lt;&gt;?
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions">
            <summary>
            Extension methods for adding options services to the DI container.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.AddOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds services required for using options.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
            <summary>
            Registers an action used to configure a particular type of options.
            Note: These are run before all <seealso cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})"/>.
            </summary>
            <typeparam name="TOptions">The options type to be configured.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="configureOptions">The action used to configure the options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{``0})">
            <summary>
            Registers an action used to configure a particular type of options.
            Note: These are run before all <seealso cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})"/>.
            </summary>
            <typeparam name="TOptions">The options type to be configured.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="name">The name of the options instance.</param>
            <param name="configureOptions">The action used to configure the options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureAll``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
            <summary>
            Registers an action used to configure all instances of a particular type of options.
            </summary>
            <typeparam name="TOptions">The options type to be configured.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="configureOptions">The action used to configure the options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
            <summary>
            Registers an action used to initialize a particular type of options.
            Note: These are run after all <seealso cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})"/>.
            </summary>
            <typeparam name="TOptions">The options type to be configured.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="configureOptions">The action used to configure the options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{``0})">
            <summary>
            Registers an action used to configure a particular type of options.
            Note: These are run after all <seealso cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})"/>.
            </summary>
            <typeparam name="TOptions">The options type to be configure.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="name">The name of the options instance.</param>
            <param name="configureOptions">The action used to configure the options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.PostConfigureAll``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
            <summary>
            Registers an action used to post configure all instances of a particular type of options.
            Note: These are run after all <seealso cref="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.Configure``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})"/>.
            </summary>
            <typeparam name="TOptions">The options type to be configured.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="configureOptions">The action used to configure the options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers a type that will have all of its I[Post]ConfigureOptions registered.
            </summary>
            <typeparam name="TConfigureOptions">The type that will configure options.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Type)">
            <summary>
            Registers a type that will have all of its I[Post]ConfigureOptions registered.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="configureType">The type that will configure options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.ConfigureOptions(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Object)">
            <summary>
            Registers an object that will have all of its I[Post]ConfigureOptions registered.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="configureInstance">The instance that will configure options.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.AddOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Gets an options builder that forwards Configure calls for the same <typeparamref name="TOptions"/> to the underlying service collection.
            </summary>
            <typeparam name="TOptions">The options type to be configured.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1"/> so that configure calls can be chained in it.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.OptionsServiceCollectionExtensions.AddOptions``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Gets an options builder that forwards Configure calls for the same named <typeparamref name="TOptions"/> to the underlying service collection.
            </summary>
            <typeparam name="TOptions">The options type to be configured.</typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> to add the services to.</param>
            <param name="name">The name of the options instance.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Options.OptionsBuilder`1"/> so that configure calls can be chained in it.</returns>
        </member>
    </members>
</doc>
