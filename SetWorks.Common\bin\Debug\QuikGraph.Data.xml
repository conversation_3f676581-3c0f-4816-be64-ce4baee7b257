<?xml version="1.0"?>
<doc>
    <assembly>
        <name>QuikGraph.Data</name>
    </assembly>
    <members>
        <member name="T:JetBrains.Annotations.CanBeNullAttribute">
            <summary>
            Indicates that the value of the marked element could be <c>null</c> sometimes,
            so the check for <c>null</c> is necessary before its usage.
            </summary>
            <example><code>
            [CanBeNull] object Test() => null;
            
            void UseTest() {
              var p = Test();
              var s = p.ToString(); // Warning: Possible 'System.NullReferenceException'
            }
            </code></example>
        </member>
        <member name="T:JetBrains.Annotations.NotNullAttribute">
            <summary>
            Indicates that the value of the marked element could never be <c>null</c>.
            </summary>
            <example><code>
            [NotNull] object Foo() {
              return null; // Warning: Possible 'null' assignment
            }
            </code></example>
        </member>
        <member name="T:JetBrains.Annotations.ItemNotNullAttribute">
            <summary>
            Can be appplied to symbols of types derived from IEnumerable as well as to symbols of Task
            and Lazy classes to indicate that the value of a collection item, of the Task.Result property
            or of the Lazy.Value property can never be null.
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.ItemCanBeNullAttribute">
            <summary>
            Can be appplied to symbols of types derived from IEnumerable as well as to symbols of Task
            and Lazy classes to indicate that the value of a collection item, of the Task.Result property
            or of the Lazy.Value property can be null.
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.ContractAnnotationAttribute">
            <summary>
            Describes dependency between method input and output.
            </summary>
            <syntax>
            <p>Function Definition Table syntax:</p>
            <list>
            <item>FDT      ::= FDTRow [;FDTRow]*</item>
            <item>FDTRow   ::= Input =&gt; Output | Output &lt;= Input</item>
            <item>Input    ::= ParameterName: Value [, Input]*</item>
            <item>Output   ::= [ParameterName: Value]* {halt|stop|void|nothing|Value}</item>
            <item>Value    ::= true | false | null | notnull | canbenull</item>
            </list>
            If method has single input parameter, it's name could be omitted.<br/>
            Using <c>halt</c> (or <c>void</c>/<c>nothing</c>, which is the same) for method output
            means that the methos doesn't return normally (throws or terminates the process).<br/>
            Value <c>canbenull</c> is only applicable for output parameters.<br/>
            You can use multiple <c>[ContractAnnotation]</c> for each FDT row, or use single attribute
            with rows separated by semicolon. There is no notion of order rows, all rows are checked
            for applicability and applied per each program state tracked by R# analysis.<br/>
            </syntax>
            <examples><list>
            <item><code>
            [ContractAnnotation("=&gt; halt")]
            public void TerminationMethod()
            </code></item>
            <item><code>
            [ContractAnnotation("halt &lt;= condition: false")]
            public void Assert(bool condition, string text) // regular assertion method
            </code></item>
            <item><code>
            [ContractAnnotation("s:null =&gt; true")]
            public bool IsNullOrEmpty(string s) // string.IsNullOrEmpty()
            </code></item>
            <item><code>
            // A method that returns null if the parameter is null,
            // and not null if the parameter is not null
            [ContractAnnotation("null =&gt; null; notnull =&gt; notnull")]
            public object Transform(object data) 
            </code></item>
            <item><code>
            [ContractAnnotation("=&gt; true, result: notnull; =&gt; false, result: null")]
            public bool TryParse(string s, out Person result)
            </code></item>
            </list></examples>
        </member>
        <member name="T:JetBrains.Annotations.UsedImplicitlyAttribute">
            <summary>
            Indicates that the marked symbol is used implicitly (e.g. via reflection, in external library),
            so this symbol will not be marked as unused (as well as by other usage inspections).
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.MeansImplicitUseAttribute">
            <summary>
            Should be used on attributes and causes ReSharper to not mark symbols marked with such attributes
            as unused (as well as by other usage inspections)
            </summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.Access">
            <summary>Only entity marked with attribute considered used.</summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.Assign">
            <summary>Indicates implicit assignment to a member.</summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.InstantiatedWithFixedConstructorSignature">
            <summary>
            Indicates implicit instantiation of a type with fixed constructor signature.
            That means any unused constructor parameters won't be reported as such.
            </summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.InstantiatedNoFixedConstructorSignature">
            <summary>Indicates implicit instantiation of a type.</summary>
        </member>
        <member name="T:JetBrains.Annotations.ImplicitUseTargetFlags">
            <summary>
            Specify what is considered used implicitly when marked
            with <see cref="T:JetBrains.Annotations.MeansImplicitUseAttribute"/> or <see cref="T:JetBrains.Annotations.UsedImplicitlyAttribute"/>.
            </summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseTargetFlags.Members">
            <summary>Members of entity marked with attribute are considered used.</summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseTargetFlags.WithMembers">
            <summary>Entity marked with attribute and all its members considered used.</summary>
        </member>
        <member name="T:JetBrains.Annotations.PublicAPIAttribute">
            <summary>
            This attribute is intended to mark publicly available API
            which should not be removed and so is treated as used.
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.InstantHandleAttribute">
            <summary>
            Tells code analysis engine if the parameter is completely handled when the invoked method is on stack.
            If the parameter is a delegate, indicates that delegate is executed while the method is executed.
            If the parameter is an enumerable, indicates that it is enumerated while the method is executed.
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.PureAttribute">
            <summary>
            Indicates that a method does not make any observable state changes.
            The same as <c>System.Diagnostics.Contracts.PureAttribute</c>.
            </summary>
            <example><code>
            [Pure] int Multiply(int x, int y) => x * y;
            
            void M() {
              Multiply(123, 42); // Waring: Return value of pure method is not used
            }
            </code></example>
        </member>
        <member name="T:QuikGraph.Utils.DisposableHelpers">
            <summary>
            Helpers to work with <see cref="T:System.IDisposable"/>.
            </summary>
        </member>
        <member name="M:QuikGraph.Utils.DisposableHelpers.Finally(System.Action)">
            <summary>
            Calls an action when going out of scope.
            </summary>
            <param name="action">The action to call.</param>
            <returns>A <see cref="T:System.IDisposable"/> object to give to a using clause.</returns>
        </member>
        <member name="M:QuikGraph.Utils.DisposableHelpers.FinallyScope.Dispose">
            <inheritdoc />
        </member>
        <member name="T:QuikGraph.Utils.MathUtils">
            <summary>
            Math utilities.
            </summary>
        </member>
        <member name="F:QuikGraph.Utils.MathUtils.DoubleEpsilon">
            <summary>
            Smallest value such that 1.0+<see cref="F:QuikGraph.Utils.MathUtils.DoubleEpsilon"/> != 1.0
            </summary>
        </member>
        <member name="M:QuikGraph.Utils.MathUtils.IsZero(System.Double)">
            <summary>
            Returns whether or not the double is "close" to 0, but this is faster.
            </summary>
            <returns>The result of the comparision.</returns>
            <param name="a">The double to compare to 0.</param>
        </member>
        <member name="M:QuikGraph.Utils.MathUtils.NearEqual(System.Double,System.Double)">
            <summary>
            Returns whether or not two <see cref="T:System.Double"/>s are "equal". That is, whether or
            not they are within epsilon of each other.
            </summary>
            <param name="a">The first <see cref="T:System.Double"/> to compare.</param>
            <param name="b">The second <see cref="T:System.Double"/> to compare.</param>
            <returns>The result of the comparision.</returns>
        </member>
        <member name="T:QuikGraph.Data.DataRelationEdge">
            <summary>
            Represents a relation between <see cref="T:System.Data.DataTable"/>s.
            </summary>
        </member>
        <member name="M:QuikGraph.Data.DataRelationEdge.#ctor(System.Data.DataRelation)">
            <summary>
            Initializes a new instance of the <see cref="T:QuikGraph.Data.DataRelationEdge"/> class.
            </summary>
            <param name="relation">Data relation.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="relation"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:QuikGraph.Data.DataRelationEdge.Relation">
            <summary>
            Data relation hold by this edge.
            </summary>
        </member>
        <member name="P:QuikGraph.Data.DataRelationEdge.Source">
            <inheritdoc />
        </member>
        <member name="P:QuikGraph.Data.DataRelationEdge.Target">
            <inheritdoc />
        </member>
        <member name="T:QuikGraph.Data.DataSetGraph">
            <summary>
            Represents a set of data as a graph.
            </summary>
        </member>
        <member name="P:QuikGraph.Data.DataSetGraph.DataSet">
            <summary>
            Wrapped <see cref="T:System.Data.DataSet"/>.
            </summary>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraph.#ctor(System.Data.DataSet)">
            <summary>
            Initializes a new instance of the <see cref="T:QuikGraph.Data.DataSetGraph"/> class.
            </summary>
            <param name="dataSet">Set of data.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dataSet"/> is <see langword="null"/>.</exception>
        </member>
        <member name="T:QuikGraph.Data.DataSetGraphExtensions">
            <summary>
            Extensions related to <see cref="T:System.Data.DataSet"/> and <see cref="T:QuikGraph.Data.DataSetGraph"/>.
            </summary>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphExtensions.ToGraph(System.Data.DataSet)">
            <summary>
            Converts this <paramref name="dataSet"/> into a graph representation of it.
            </summary>
            <param name="dataSet"><see cref="T:System.Data.DataSet"/> to convert to graph.</param>
            <returns>Graph representing the <see cref="T:System.Data.DataSet"/>.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="dataSet"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphExtensions.ToGraphviz(QuikGraph.Data.DataSetGraph)">
            <summary>
            Renders a graph to the Graphviz DOT format.
            </summary>
            <param name="graph">Graph to convert.</param>
            <returns>Graph serialized in DOT format.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="graph"/> is <see langword="null"/>.</exception>
        </member>
        <member name="T:QuikGraph.Data.DataSetGraphPopulatorAlgorithm">
            <summary>
            Algorithm that take a <see cref="T:System.Data.DataSet"/> and convert it as a graph representation.
            </summary>
        </member>
        <member name="P:QuikGraph.Data.DataSetGraphPopulatorAlgorithm.DataSet">
            <summary>
            <see cref="T:System.Data.DataSet"/> to represent as a graph.
            </summary>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphPopulatorAlgorithm.#ctor(QuikGraph.IMutableVertexAndEdgeSet{System.Data.DataTable,QuikGraph.Data.DataRelationEdge},System.Data.DataSet)">
            <summary>
            Initializes a new instance of the <see cref="T:QuikGraph.Data.DataSetGraphPopulatorAlgorithm"/> class.
            </summary>
            <param name="visitedGraph">Graph to fill from <paramref name="dataSet"/>.</param>
            <param name="dataSet"><see cref="T:System.Data.DataSet"/> to use to fill <paramref name="visitedGraph"/>.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="visitedGraph"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentNullException"><paramref name="dataSet"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphPopulatorAlgorithm.InternalCompute">
            <inheritdoc />
        </member>
        <member name="T:QuikGraph.Data.DataSetGraphvizAlgorithm">
            <summary>
            An algorithm that renders a DataSet graph to the Graphviz DOT format.
            </summary>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphvizAlgorithm.#ctor(QuikGraph.Data.DataSetGraph)">
            <summary>
            Initializes a new instance of the <see cref="T:QuikGraph.Data.DataSetGraphvizAlgorithm"/> class.
            </summary>
            <param name="visitedGraph">Graph to convert to DOT.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="visitedGraph"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphvizAlgorithm.#ctor(QuikGraph.Data.DataSetGraph,QuikGraph.Graphviz.Dot.GraphvizImageType)">
            <summary>
            Initializes a new instance of the <see cref="T:QuikGraph.Data.DataSetGraphvizAlgorithm"/> class.
            </summary>
            <param name="visitedGraph">Graph to convert to DOT.</param>
            <param name="imageType">Target output image type.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="visitedGraph"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphvizAlgorithm.FormatTable(System.Object,QuikGraph.Graphviz.FormatVertexEventArgs{System.Data.DataTable})">
            <summary>
            Formats a <see cref="T:System.Data.DataTable"/> (a table).
            </summary>
            <param name="sender">The <see cref="T:QuikGraph.Graphviz.GraphvizAlgorithm`2"/> performing the formatting.</param>
            <param name="args">Vertex event arguments.</param>
        </member>
        <member name="M:QuikGraph.Data.DataSetGraphvizAlgorithm.FormatRelation(System.Object,QuikGraph.Graphviz.FormatEdgeEventArgs{System.Data.DataTable,QuikGraph.Data.DataRelationEdge})">
            <summary>
            Formats a <see cref="T:QuikGraph.Data.DataRelationEdge"/> (a relation between tables).
            </summary>
            <param name="sender">The <see cref="T:QuikGraph.Graphviz.GraphvizAlgorithm`2"/> performing the formatting.</param>
            <param name="args">Edge event arguments.</param>
        </member>
    </members>
</doc>
