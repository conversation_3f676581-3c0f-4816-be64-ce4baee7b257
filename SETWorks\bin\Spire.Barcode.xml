<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Spire.Barcode</name>
    </assembly>
    <members>
        <member name="F:Spire.Barcode.CheckSumMode.Auto">
            <summary>
            Enable or disable checksum according barcode type.
            </summary>
        </member>
        <member name="F:Spire.Barcode.CheckSumMode.ForceEnable">
            <summary>
            Force enable checksum.
            </summary>
        </member>
        <member name="F:Spire.Barcode.CheckSumMode.ForceDisable">
            <summary>
            Force disable checksum.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.ByteArray.at(System.Int32)">
            <summary> Access an unsigned byte at location index.</summary>
            <param name="index">The index in the array to access.
            </param>
            <returns> The unsigned value of the byte as an int.
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.CharacterSetECI.getCharacterSetECIByValue(System.Int32)">
            <param name="value">character set ECI value</param>
            <returns><see cref="T:Spire.Barcode.Implementation.Common.CharacterSetECI"/> representing ECI of given value, or null if it is legal but unsupported</returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.CharacterSetECI.getCharacterSetECIByName(System.String)">
            <param name="name">character set ECI encoding name</param>
            <returns><see cref="T:Spire.Barcode.Implementation.Common.CharacterSetECI"/> representing ECI for character encoding, or null if it is legalbut unsupported</returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.ECI.getECIByValue(System.Int32)">
            <param name="value">ECI value
            </param>
            <returns> {@link ECI} representing ECI of given value, or null if it is legal but unsupported
            </returns>
            <throws>  IllegalArgumentException if ECI value is invalid </throws>
        </member>
        <member name="T:Spire.Barcode.Implementation.Common.ResultPointCallback">
            <summary> Callback which is invoked when a possible result point (significant
            point in the barcode image such as a corner) is found.
            
            </summary>
            <seealso cref="F:Spire.Barcode.Implementation.DecodeHintType.NEED_RESULT_POINT_CALLBACK">
            </seealso>
        </member>
        <member name="T:Spire.Barcode.Implementation.Common.StringUtils">
            <summary>
            Common string-related functions.
            </summary>
            <author>Sean Owen</author>
            <author>Alex Dupre</author>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.StringUtils.guessEncoding(System.Byte[],System.Collections.Generic.IDictionary{Spire.Barcode.Implementation.DecodeHintType,System.Object})">
            <summary>
            Guesses the encoding.
            </summary>
            <param name="bytes">bytes encoding a string, whose encoding should be guessed</param>
            <param name="hints">decode hints if applicable</param>
            <returns>name of guessed encoding; at the moment will only guess one of:
            {@link #SHIFT_JIS}, {@link #UTF8}, {@link #ISO88591}, or the platform
            default encoding if none of these can possibly be correct</returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.SupportClass.URShift(System.Int32,System.Int32)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Common.SupportClass.Identity(System.Double)">
            <summary>
            This method returns the literal value received
            </summary>
            <param name="literal">The literal to return</param>
            <returns>The received value</returns>
        </member>
        <member name="T:Spire.Barcode.Implementation.Dimension">
            <summary>
            Simply encapsulates a width and height.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.#ctor(System.Int32,System.Int32)">
            <summary>
            initializing constructor
            </summary>
            <param name="width"></param>
            <param name="height"></param>
        </member>
        <member name="P:Spire.Barcode.Implementation.Dimension.Width">
            <summary>
            the width
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Dimension.Height">
            <summary>
            the height
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Dimension.ToString">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.DataMatrix.Encoder.SymbolInfo.overrideSymbolSet(Spire.Barcode.Implementation.Generator.Encoder.DataMatrix.Encoder.SymbolInfo[])">
             Overrides the symbol info set used by this class. Used for testing purposes.
            
             @param override the symbol info set to use
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.#ctor(System.Int32)">
             Create a representation of GF(256) using the given primitive polynomial.
            
             @param primitive irreducible polynomial whose coefficients are represented by
              the bits of an int, where the least-significant bit represents the constant
              coefficient
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.BuildMonomial(System.Int32,System.Int32)">
            @return the monomial representing coefficient * x^degree
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.AddOrSubtract(System.Int32,System.Int32)">
             Implements both addition and subtraction -- they are the same in GF(256).
            
             @return sum/difference of a and b
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Exp(System.Int32)">
            @return 2 to the power of a in GF(256)
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Log(System.Int32)">
            @return base 2 log of a in GF(256)
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Inverse(System.Int32)">
            @return multiplicative inverse of a
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256.Multiply(System.Int32,System.Int32)">
            @param a
            @param b
            @return product of a and b in GF(256)
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser">
            <author>Sean Owen</author>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser.createBitMatrixParser(Spire.Barcode.Implementation.Common.BitMatrix)">
            <param name="bitMatrix">{@link BitMatrix} to parse</param>
            <throws>  ReaderException if dimension is not >= 11 and 1 mod 2 </throws>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser.readFormatInformation">
            <summary> <p>Reads format information from one of its two locations within the QR Code.</p>
            
            </summary>
            <returns> {@link FormatInformation} encapsulating the QR Code's format info
            </returns>
            <throws>  ReaderException if both format information locations cannot be parsed as </throws>
            <summary> the valid encoding of format information
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser.readVersion">
            <summary> <p>Reads version information from one of its two locations within the QR Code.</p>
            
            </summary>
            <returns> {@link Version} encapsulating the QR Code's version
            </returns>
            <throws>  ReaderException if both version information locations cannot be parsed as </throws>
            <summary> the valid encoding of version information
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser.readCodewords">
            <summary> <p>Reads the bits in the {@link BitMatrix} representing the finder pattern in the
            correct order in order to reconstruct the codewords bytes contained within the
            QR Code.</p>
            
            </summary>
            <returns> bytes encoded within the QR Code
            </returns>
            <throws>  ReaderException if the exact number of bytes expected is not read </throws>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser.remask">
            Revert the mask removal done while reading the code words. The bit matrix should revert to its original state.
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser.setMirror(System.Boolean)">
            Prepare the parser for a mirrored operation.
            This flag has effect only on the {@link #readFormatInformation()} and the
            {@link #readVersion()}. Before proceeding with {@link #readCodewords()} the
            {@link #mirror()} method should be called.
            
            @param mirror Whether to read version and format information mirrored.
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.BitMatrixParser.mirror">
            Mirror the bit matrix in order to attempt a second reading. 
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataBlock.getDataBlocks(System.Byte[],Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel)">
            <summary> <p>When QR Codes use multiple data blocks, they are actually interleaved.
            That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This
            method will separate the data into original blocks.</p>
            
            </summary>
            <param name="rawCodewords">bytes as read directly from the QR Code
            </param>
            <param name="version">version of the QR Code
            </param>
            <param name="ecLevel">error-correction level of the QR Code
            </param>
            <returns> {@link DataBlock}s containing original bytes, "de-interleaved" from representation in the
            QR Code
            </returns>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataMask.DATA_MASKS">
            <summary> See ISO 18004:2006 6.8.1</summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataMask.unmaskBitMatrix(Spire.Barcode.Implementation.Common.BitMatrix,System.Int32)">
            <summary> <p>Implementations of this method reverse the data masking process applied to a QR Code and
            make its bits ready to read.</p>
            
            </summary>
            <param name="bits">representation of QR Code bits
            </param>
            <param name="dimension">dimension of QR Code, represented by bits, being unmasked
            </param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataMask.forReference(System.Int32)">
            <param name="reference">a value between 0 and 7 indicating one of the eight possible
            data mask patterns a QR Code may use
            </param>
            <returns> {@link DataMask} encapsulating the data mask pattern
            </returns>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataMask.DataMask00">
            <summary> 00: mask bits for which x mod 2 == 0</summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataMask.DataMask01">
            <summary> 01: mask bits for which (x/2 + y/3) mod 2 == 0</summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataMask.DataMask10">
            <summary> 10: mask bits for which xy mod 2 + xy mod 3 == 0</summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DataMask.DataMask11">
            <summary> 11: mask bits for which ((x+y)mod 2 + xy mod 3) mod 2 == 0</summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DecodedBitStreamParser">
            <summary> <p>QR Codes can encode text as bits in one of several modes, and can use multiple modes
            in one QR Code. This class decodes the bits back into text.</p>
            
            <p>See ISO 18004:2006, 6.4.3 - 6.4.7</p>
            <author>Sean Owen</author>
            </summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DecodedBitStreamParser.ALPHANUMERIC_CHARS">
            <summary>
            See ISO 18004:2006, 6.4.4 Table 5
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.DecodedBitStreamParser.decodeHanziSegment(Spire.Barcode.Implementation.Common.BitSource,System.Text.StringBuilder,System.Int32)">
            <summary>
            See specification GBT 18284-2000
            </summary>
            <param name="bits">The bits.</param>
            <param name="result">The result.</param>
            <param name="count">The count.</param>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Decoder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Decoder"/> class.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Decoder.decode(System.Boolean[][],System.Collections.Generic.IDictionary{Spire.Barcode.Implementation.DecodeHintType,System.Object})">
            <summary>
              <p>Convenience method that can decode a QR Code represented as a 2D array of booleans.
            "true" is taken to mean a black module.</p>
            </summary>
            <param name="image">booleans representing white/black QR Code modules</param>
            <param name="hints">decoding hints that should be used to influence decoding</param>
            <returns>
            text and bytes encoded within the QR Code
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Decoder.decode(Spire.Barcode.Implementation.Common.BitMatrix,System.Collections.Generic.IDictionary{Spire.Barcode.Implementation.DecodeHintType,System.Object})">
            <summary>
              <p>Decodes a QR Code represented as a {@link BitMatrix}. A 1 or "true" is taken to mean a black module.</p>
            </summary>
            <param name="bits">booleans representing white/black QR Code modules</param>
            <param name="hints">decoding hints that should be used to influence decoding</param>
            <returns>
            text and bytes encoded within the QR Code
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Decoder.correctErrors(System.Byte[],System.Int32)">
            <summary>
              <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to
            correct the errors in-place using Reed-Solomon error correction.</p>
            </summary>
            <param name="codewordBytes">data and error correction codewords</param>
            <param name="numDataCodewords">number of codewords that are data bytes</param>
            <returns></returns>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.L">
            <summary> L = ~7% correction</summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.M">
            <summary> M = ~15% correction</summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.Q">
            <summary> Q = ~25% correction</summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.H">
            <summary> H = ~30% correction</summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.Bits">
            <summary>
            Gets the bits.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.ordinal">
            <summary>
            Ordinals this instance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel.forBits(System.Int32)">
            <summary>
            Fors the bits.
            </summary>
            <param name="bits">int containing the two bits encoding a QR Code's error correction level</param>
            <returns>
              <see cref="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel"/> representing the encoded error correction level
            </returns>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.ErrorCorrectionLevel">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.Version">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.DataMask">
            <summary>
            
            </summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.FORMAT_INFO_DECODE_LOOKUP">
            <summary> See ISO 18004:2006, Annex C, Table C.1</summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.BITS_SET_IN_HALF_BYTE">
            <summary> Offset i holds the number of 1 bits in the binary representation of i</summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.SYMBOL_NUMBERS_INFO">
            <summary>
            Micro QR Code Format information  Symbol numbers
            0 M1 1 M2 2 M2 3 M3 4 M3 5 M4 6 M4 7 M4 
            ISO/IEC 18004:2006(E) Table 13 — Symbol numbers for Micro QR Code symbols 
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.decodeFormatInformation(System.Int32)">
            <param name="maskedFormatInfo">format info indicator, with mask still applied
            </param>
            <returns> information about the format it specifies, or <code>null</code>
            if doesn't seem to match any known pattern
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FormatInformation.doDecodeFormatInformation(System.Int32)">
            <summary>
            
            </summary>
            <param name="maskedFormatInfo"></param>
            <returns></returns>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.TERMINATOR">
            <summary>
            Tables 2
            M1  M2    M3      M4
            000 00000 0000000 000000000 
            </summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.bitsLength">
            <summary>
            Micro QR Code 位长度
            </summary>
            <remarks>
            Micro QR Code 每种版本位长度一样
            Table 2 — Mode indicators for QR Code 2005 
            </remarks>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.#ctor(System.Int32[],System.Int32,System.Int32[],System.String)">
            <summary>
            
            </summary>
            <param name="characterCountBitsForVersions">
            Table 3 — Number of bits in character count indicator for QR Code 2005 
            </param>
            <param name="bits">
            Table 2 — Mode indicators for QR Code 2005 
            </param>
            <param name="name"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.forBits(System.Int32)">
            <param name="bits">
            four bits encoding a Micro QR Code data mode
            </param>
            <returns> {@link Mode} encoded by these bits
            </returns>
            <throws>  IllegalArgumentException if bits do not correspond to a known mode </throws>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.getCharacterCountBits(Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version)">
            <param name="version">
            version in question
            </param>
            <returns> 
            number of bits used, in this Micro QR Code symbol {@link Version}, to encode the
            count of characters that will follow encoded in this {@link Mode}
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.getBitsLength(Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version)">
            <param name="version">
            Mode Bits length
            </param>
            <returns> 
            current Mode Bits length
            </returns>
            <remarks>
            Table 2 — Mode indicators for QR Code 2005 
            </remarks>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.getBitsLength(System.Int32)">
            <param name="version">
            Mode Bits length
            </param>
            <returns> 
            current Mode Bits length
            </returns>
            <remarks>
            Table 2 — Mode indicators for QR Code 2005 
            </remarks>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode.ToString">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.QRCodeDecoderMetaData">
            <summary>
            Meta-data container for QR Code decoding. Instances of this class may be used to convey information back to the
            decoding caller. Callers are expected to process this.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.QRCodeDecoderMetaData.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.QRCodeDecoderMetaData"/> class.
            </summary>
            <param name="mirrored">if set to <c>true</c> [mirrored].</param>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.QRCodeDecoderMetaData.IsMirrored">
            <summary>
            true if the QR Code was mirrored. 
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.QRCodeDecoderMetaData.applyMirroredCorrection(Spire.Barcode.Implementation.Common.ResultPoint[])">
            <summary>
            Apply the result points' order correction due to mirroring.
            </summary>
            <param name="points">Array of points to apply mirror correction to.</param>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version">
            <summary> 
            See ISO 18004:2006 Annex D
            </summary>
            <author>  Sean Owen
            </author>
            <author>www.Redivivus.in (<EMAIL>) - Ported from ZXING Java Source 
            </author>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version.VERSIONS">
            <summary> 
            See ISO 18004:2006 Annex D.
            Element i represents the raw version bits that specify version i + 7
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version.getProvisionalVersionForDimension(System.Int32)">
            <summary> <p>Deduces version information purely from QR Code dimensions.</p>
            
            </summary>
            <param name="dimension">dimension in modules
            </param>
            <returns> {@link Version} for a QR Code of that dimension
            </returns>
            <throws>  ReaderException if dimension is not 1 mod 4 </throws>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version.getVersionForNumber(System.Int32)">
            <summary>
            
            </summary>
            <param name="versionNumber"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version.buildFunctionPattern">
            <summary> See ISO 18004:2006 Annex E</summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version.ECBlocks">
            <summary> <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will
            use blocks of differing sizes within one version, so, this encapsulates the parameters for
            each set of blocks. It also holds the number of error-correction codewords per block since it
            will be the same across all blocks within one version.</p>
            </summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version.ECB">
            <summary> <p>Encapsualtes the parameters for one error-correction block in one symbol version.
            This includes the number of data codewords, and the number of times a block with these
            parameters is used consecutively in the QR code version's format.</p>
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Version.buildVersions">
            <summary> See ISO 18004:2006 6.5.1 Table 9</summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPattern">
            <summary> <p>Encapsulates an alignment pattern, which are the smaller square patterns found in
            all but the simplest QR Codes.</p>
            
            </summary>
            <author>  Sean Owen
            </author>
            <author>www.Redivivus.in (<EMAIL>) - Ported from ZXING Java Source 
            </author>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPattern.aboutEquals(System.Single,System.Single,System.Single)">
            <summary> <p>Determines if this alignment pattern "about equals" an alignment pattern at the stated
            position and size -- meaning, it is at nearly the same center with nearly the same size.</p>
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPattern.combineEstimate(System.Single,System.Single,System.Single)">
            <summary>
            Combines this object's current estimate of a finder pattern position and module size
            with a new estimate. It returns a new {@code FinderPattern} containing an average of the two.
            </summary>
            <param name="i">The i.</param>
            <param name="j">The j.</param>
            <param name="newModuleSize">New size of the module.</param>
            <returns></returns>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPatternFinder">
            <summary> <p>This class attempts to find alignment patterns in a QR Code. Alignment patterns look like finder
            patterns but are smaller and appear at regular intervals throughout the image.</p>
            
            <p>At the moment this only looks for the bottom-right alignment pattern.</p>
            
            <p>This is mostly a simplified copy of {@link FinderPatternFinder}. It is copied,
            pasted and stripped down here for maximum performance but does unfortunately duplicate
            some code.</p>
            
            <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.</p>
            
            </summary>
            <author>  Sean Owen
            </author>
            <author>www.Redivivus.in (<EMAIL>) - Ported from ZXING Java Source 
            </author>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPatternFinder.#ctor(Spire.Barcode.Implementation.Common.BitMatrix,System.Int32,System.Int32,System.Int32,System.Int32,System.Single,Spire.Barcode.Implementation.Common.ResultPointCallback)">
            <summary> <p>Creates a finder that will look in a portion of the whole image.</p>
            
            </summary>
            <param name="image">image to search
            </param>
            <param name="startX">left column from which to start searching
            </param>
            <param name="startY">top row from which to start searching
            </param>
            <param name="width">width of region to search
            </param>
            <param name="height">height of region to search
            </param>
            <param name="moduleSize">estimated module size so far
            </param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPatternFinder.find">
            <summary> <p>This method attempts to find the bottom-right alignment pattern in the image. It is a bit messy since
            it's pretty performance-critical and so is written to be fast foremost.</p>
            
            </summary>
            <returns> {@link AlignmentPattern} if found
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPatternFinder.centerFromEnd(System.Int32[],System.Int32)">
            <summary> Given a count of black/white/black pixels just seen and an end position,
            figures the location of the center of this black/white/black run.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPatternFinder.foundPatternCross(System.Int32[])">
            <param name="stateCount">count of black/white/black pixels just read
            </param>
            <returns> true iff the proportions of the counts is close enough to the 1/1/1 ratios
            used by alignment patterns to be considered a match
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPatternFinder.crossCheckVertical(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
              <p>After a horizontal scan finds a potential alignment pattern, this method
            "cross-checks" by scanning down vertically through the center of the possible
            alignment pattern to see if the same proportion is detected.</p>
            </summary>
            <param name="startI">row where an alignment pattern was detected</param>
            <param name="centerJ">center of the section that appears to cross an alignment pattern</param>
            <param name="maxCount">maximum reasonable number of modules that should be
            observed in any reading state, based on the results of the horizontal scan</param>
            <param name="originalStateCountTotal">The original state count total.</param>
            <returns>
            vertical center of alignment pattern, or null if not found
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.AlignmentPatternFinder.handlePossibleCenter(System.Int32[],System.Int32,System.Int32)">
            <summary> <p>This is called when a horizontal scan finds a possible alignment pattern. It will
            cross check with a vertical scan, and if successful, will see if this pattern had been
            found on a previous horizontal scan. If so, we consider it confirmed and conclude we have
            found the alignment pattern.</p>
            
            </summary>
            <param name="stateCount">reading state module counts from horizontal scan
            </param>
            <param name="i">row where alignment pattern may be found
            </param>
            <param name="j">end of possible alignment pattern in row
            </param>
            <returns> {@link AlignmentPattern} if we have found the same pattern twice, or null if not
            </returns>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPattern.EstimatedModuleSize">
            <summary>
            Gets the size of the estimated module.
            </summary>
            <value>
            The size of the estimated module.
            </value>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPattern.aboutEquals(System.Single,System.Single,System.Single)">
            <summary> <p>Determines if this finder pattern "about equals" a finder pattern at the stated
            position and size -- meaning, it is at nearly the same center with nearly the same size.</p>
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPattern.combineEstimate(System.Single,System.Single,System.Single)">
            <summary>
            Combines this object's current estimate of a finder pattern position and module size
            with a new estimate. It returns a new {@code FinderPattern} containing a weighted average
            based on count.
            </summary>
            <param name="i">The i.</param>
            <param name="j">The j.</param>
            <param name="newModuleSize">New size of the module.</param>
            <returns></returns>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.MIN_SKIP">
            <summary>
            1 pixel/module times 3 modules/center
            </summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.MAX_MODULES">
            <summary>
            support up to version 10 for mobile clients
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.#ctor(Spire.Barcode.Implementation.Common.BitMatrix)">
            <summary>
            <p>Creates a finder that will search the image for three finder patterns.</p>
            </summary>
            <param name="image">image to search</param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.#ctor(Spire.Barcode.Implementation.Common.BitMatrix,Spire.Barcode.Implementation.Common.ResultPointCallback)">
            <summary>
            Initializes a new instance of the <see cref="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder"/> class.
            </summary>
            <param name="image">The image.</param>
            <param name="resultPointCallback">The result point callback.</param>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.Image">
            <summary>
            Gets the image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.PossibleCenters">
            <summary>
            Gets the possible centers.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.centerFromEnd(System.Int32[],System.Int32)">
            <summary> Given a count of black/white/black/white/black pixels just seen and an end position,
            figures the location of the center of this run.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.foundPatternCross(System.Int32[])">
            <param name="stateCount">count of black/white/black/white/black pixels just read
            </param>
            <returns> true iff the proportions of the counts is close enough to the 1/1/3/1/1 ratios
            used by finder patterns to be considered a match
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.crossCheckDiagonal(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            After a vertical and horizontal scan finds a potential finder pattern, this method
            "cross-cross-cross-checks" by scanning down diagonally through the center of the possible
            finder pattern to see if the same proportion is detected.
            </summary>
            <param name="startI">row where a finder pattern was detected</param>
            <param name="centerJ">center of the section that appears to cross a finder pattern</param>
            <param name="maxCount">maximum reasonable number of modules that should be observed in any reading state, based on the results of the horizontal scan</param>
            <param name="originalStateCountTotal">The original state count total.</param>
            <returns>true if proportions are withing expected limits</returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.crossCheckVertical(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
              <p>After a horizontal scan finds a potential finder pattern, this method
            "cross-checks" by scanning down vertically through the center of the possible
            finder pattern to see if the same proportion is detected.</p>
            </summary>
            <param name="startI">row where a finder pattern was detected</param>
            <param name="centerJ">center of the section that appears to cross a finder pattern</param>
            <param name="maxCount">maximum reasonable number of modules that should be
            observed in any reading state, based on the results of the horizontal scan</param>
            <param name="originalStateCountTotal">The original state count total.</param>
            <returns>
            vertical center of finder pattern, or null if not found
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.crossCheckHorizontal(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary> <p>Like {@link #crossCheckVertical(int, int, int, int)}, and in fact is basically identical,
            except it reads horizontally instead of vertically. This is used to cross-cross
            check a vertical cross check and locate the real center of the alignment pattern.</p>
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.handlePossibleCenter(System.Int32[],System.Int32,System.Int32,System.Boolean)">
            <summary>
              <p>This is called when a horizontal scan finds a possible alignment pattern. It will
            cross check with a vertical scan, and if successful, will, ah, cross-cross-check
            with another horizontal scan. This is needed primarily to locate the real horizontal
            center of the pattern in cases of extreme skew.
            And then we cross-cross-cross check with another diagonal scan.</p>
            If that succeeds the finder pattern location is added to a list that tracks
            the number of times each location has been nearly-matched as a finder pattern.
            Each additional find is more evidence that the location is in fact a finder
            pattern center
            </summary>
            <param name="stateCount">reading state module counts from horizontal scan</param>
            <param name="i">row where finder pattern may be found</param>
            <param name="j">end of possible finder pattern in row</param>
            <param name="pureBarcode">true if in "pure barcode" mode</param>
            <returns>
            true if a finder pattern candidate was found this time
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.findRowSkip">
            <returns> number of rows we could safely skip during scanning, based on the first
            two finder patterns that have been located. In some cases their position will
            allow us to infer that the third pattern must lie below a certain point farther
            down in the image.
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.haveMultiplyConfirmedCenters">
            <returns> true iff we have found at least 3 finder patterns that have been detected
            at least {@link #CENTER_QUORUM} times each, and, the estimated module size of the
            candidates is "pretty similar"
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.selectBestPatterns">
            <returns> the 3 best {@link FinderPattern}s from our list of candidates. The "best" are
            those that have been detected at least {@link #CENTER_QUORUM} times, and whose module
            size differs from the average among those patterns the least
            </returns>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.FurthestFromAverageComparator">
            <summary>
            Orders by furthest from average
            </summary>
        </member>
        <member name="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternFinder.CenterComparator">
            <summary> <p>Orders by {@link FinderPattern#getCount()}, descending.</p></summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternInfo.#ctor(Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPattern[])">
            <summary>
            Initializes a new instance of the <see cref="T:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternInfo"/> class.
            </summary>
            <param name="patternCenters">The pattern centers.</param>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternInfo.BottomLeft">
            <summary>
            Gets the bottom left.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternInfo.TopLeft">
            <summary>
            Gets the top left.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.FinderPatternInfo.TopRight">
            <summary>
            Gets the top right.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.encode(System.String,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MicroQRCode,System.Int32)">
            <summary>  Encode "bytes" with the error correction level "ecLevel". The encoding mode will be chosen
            internally by chooseMode(). On success, store the result in "qrCode".
            
            We recommend you to use QRCode.EC_LEVEL_L (the lowest level) for
            "getECLevel" since our primary use is to show QR code on desktop screens. We don't need very
            strong error correction for this purpose.
            
            Note that there is no way to encode bytes in MODE_KANJI. We might want to add EncodeWithMode()
            with which clients can specify the encoding mode. For now, we don't need the functionality.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.encode(System.String,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel,System.Collections.Generic.IDictionary{Spire.Barcode.Implementation.EncodeHintType,System.Object},Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MicroQRCode,System.Int32)">
            <summary>
            
            </summary>
            <param name="content"></param>
            <param name="ecLevel"></param>
            <param name="hints"></param>
            <param name="qrCode"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.getAlphanumericCode(System.Int32)">
            <returns> the code point of the table used in alphanumeric mode or
            -1 if there is no corresponding code in the table.
            </returns>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.chooseMode(System.String,System.String)">
            <summary> Choose the best mode by examining the content. Note that 'encoding' is used as a hint;
            if it is Shift_JIS, and the input is only double-byte Kanji, then we return {@link Mode#KANJI}.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.initQRCode(System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MicroQRCode,System.Int32)">
            <summary> Initialize "qrCode" according to "numInputBytes", "ecLevel", and "mode". On success,
            modify "qrCode".
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.terminateBits(System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector,System.Int32)">
            <summary> Terminate bits as described in 8.4.8 and 8.4.9 of JISX0510:2004 (p.24).</summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.getNumDataBytesAndNumECBytesForBlockID(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32[],System.Int32[])">
            <summary> Get number of data bytes and number of error correction bytes for block id "blockID". Store
            the result in "numDataBytesInBlock", and "numECBytesInBlock". See table 12 in 8.5.1 of
            JISX0510:2004 (p.30)
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.interleaveWithECBytes(Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector,System.Int32,System.Int32,System.Int32,System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector)">
            <summary> Interleave "bits" with corresponding error correction bytes. On success, store the result in
            "result". The interleave rule is complicated. See 8.6 of JISX0510:2004 (p.37) for details.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.appendModeInfo(Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector,System.Int32)">
            <summary> Append mode info. On success, store the result in "bits".</summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.appendLengthInfo(System.Int32,System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector)">
            <summary> Append length info. On success, store the result in "bits".</summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.appendBytes(System.String,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.Mode,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector,System.String,System.Int32)">
            <summary> Append "bytes" in "mode" mode (encoding) into "bits". On success, store the result in "bits".</summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.appendNumericBytes(System.String,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector)">
            <summary>
            
            </summary>
            <param name="content"></param>
            <param name="bits"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.appendAlphanumericBytes(System.String,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector)">
            <summary>
            
            </summary>
            <param name="content"></param>
            <param name="bits"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.append8BitBytes(System.String,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector,System.String)">
            <summary>
            
            </summary>
            <param name="content"></param>
            <param name="bits"></param>
            <param name="encoding"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.Encoder.appendKanjiBytes(System.String,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector)">
            <summary>
            
            </summary>
            <param name="content"></param>
            <param name="bits"></param>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MatrixUtil.TYPE_INFO_COORDINATES">
            <summary>
            Type info cells at the left top corner.
            ISO/IEC 18004:2006(E)  6.9.2 Micro QR Code symbols 
            </summary>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MatrixUtil.SYMBOL_NUMBERS_INFO">
            <summary>
            Micro QR Code Format information  Symbol numbers
            0 M1 1 M2 2 M2 3 M3 4 M3 5 M4 6 M4 7 M4 
            ISO/IEC 18004:2006(E) Table 13 — Symbol numbers for Micro QR Code symbols 
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MatrixUtil.makeTypeInfoBits(Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Internal.ErrorCorrectionLevel,System.Int32,System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.BitVector)">
            <summary>
            Make bit vector of type information. On success, store the result in "bits" and return true.
            Encode error correction level and mask pattern. See 8.9 of
            JISX0510:2004 (p.45) for details.
            </summary>
            <param name="ecLevel"></param>
            <param name="maskPattern"></param>
            <param name="bits"></param>
            <remarks>
            ISO/IEC 18004:2006(E)  6.9 Format information 
            </remarks>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MatrixUtil.embedTimingPatterns(Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.ByteMatrix)">
            <summary>
            
            </summary>
            <param name="matrix"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MatrixUtil.embedHorizontalSeparationPattern(System.Int32,System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.ByteMatrix)">
            <summary>
            
            </summary>
            <param name="xStart"></param>
            <param name="yStart"></param>
            <param name="matrix"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MatrixUtil.embedVerticalSeparationPattern(System.Int32,System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.ByteMatrix)">
            <summary>
            
            </summary>
            <param name="xStart"></param>
            <param name="yStart"></param>
            <param name="matrix"></param>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MatrixUtil.embedPositionDetectionPattern(System.Int32,System.Int32,Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.ByteMatrix)">
            <summary>
            
            </summary>
            <param name="xStart"></param>
            <param name="yStart"></param>
            <param name="matrix"></param>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MicroQrCodeEncodingOptions.ErrorCorrection">
            <summary>
            Specifies what degree of error correction to use, for example in QR Codes.
            Type depends on the encoder. For example for QR codes it's type
            {@link com.google.qrcode.decoder.ErrorCorrectionLevel ErrorCorrectionLevel}.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MicroQrCodeEncodingOptions.CharacterSet">
            <summary>
            Specifies what character encoding to use where applicable (type {@link String})
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MicroQrCodeEncodingOptions.Version">
            <summary>
            Specifies what character encoding to use where applicable (type {@link String})
            </summary>
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.MicroQR.Encoder.MicroQrCodeEncodingOptions.DisableECI">
            <summary>
            Explicitly disables ECI segment when generating QR Code
            That is against the specification of QR Code but some
            readers have problems if the charset is switched from
            ISO-8859-1 (default) to UTF-8 with the necessary ECI segment.
            If you set the property to true you can use UTF-8 encoding
            and the ECI segment is omitted.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.MicroQRCodeWriter.encode(System.String,Spire.Barcode.Implementation.Generator.SymbologyType,System.Int32,System.Int32,System.Collections.Generic.IDictionary{Spire.Barcode.Implementation.EncodeHintType,System.Object})">
            <summary>
            
            </summary>
            <param name="contents"></param>
            <param name="format"></param>
            <param name="width"></param>
            <param name="height"></param>
            <param name="hints"></param>
            <returns></returns>
        </member>
        <member name="F:Spire.Barcode.Implementation.Generator.Encoder.Codes.NewRowToSeparator">
            For RSS.When 2D part finished and need to change to draw the separator part
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.#ctor(System.Int32[])">
            @param field the {@link GF256} instance representing the field to use
            to perform computations
            @param coefficients coefficients as ints representing elements of GF(256), arranged
            from most significant (highest-power term) coefficient to least significant
            @throws IllegalArgumentException if argument is null or empty,
            or if leading coefficient is 0 and this is not a
            constant polynomial (that is, it is not the monomial "0")
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.evaluateAt(System.Int32)">
            @return evaluation of this polynomial at a given point
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.Degree">
            @return degree of this polynomial
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.IsZero">
            @return true iff this polynomial is the monomial "0"
        </member>
        <member name="M:Spire.Barcode.Implementation.Generator.Encoder.GF256Poly.GetCoefficient(System.Int32)">
            @return coefficient of x^degree term in this polynomial
        </member>
        <member name="P:Spire.Barcode.Implementation.Generator.Generation.BarcodeCreator.AztecLayers">
            <summary>
            Layers of Aztect type of barcode. value should between -4 to 32.
            </summary>
        </member>
        <!-- 对于成员“M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.RectangleImprove(Spire.Barcode.Implementation.Generator.Types.BinaryBitmap.FloatBitmap)”忽略有格式错误的 XML 注释 -->
        <member name="M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.CalculateNFA(System.Int32,System.Int32,System.Double)">
            Computes -log10(NFA).
            
                        NFA stands for Number of False Alarms:
                        @f[
                            \mathrm{NFA} = NT \cdot B(n,k,p)
                        @f]
            
                        - NT       - number of tests
                        - B(n,k,p) - tail of binomial distribution with parameters n,k and p:
                        @f[
                            B(n,k,p) = \sum_{j=k}^n
                                       \left(\begin{array}{c}n\\j\end{array}\right)
                                       p^{j} (1-p)^{n-j}
                        @f]
            
                        The value -log10(NFA) is equivalent but more intuitive than NFA:
                        - -1 corresponds to 10 mean false alarms
                        -  0 corresponds to 1 mean false alarm
                        -  1 corresponds to 0.1 mean false alarms
                        -  2 corresponds to 0.01 mean false alarms
                        -  ...
            
                        Used this way, the bigger the value, better the detection,
                        and a logarithmic scale is used.
            
                        @param n,k,p binomial parameters.
                        @param logNT logarithm of Number of Tests
            
                        The computation is based in the gamma function by the following
                        relation:
                        @f[
                            \left(\begin{array}{c}n\\k\end{array}\right)
                            = \frac{ \Gamma(n+1) }{ \Gamma(k+1) \cdot \Gamma(n-k+1) }.
                        @f]
                        We use efficient algorithms to compute the logarithm of
                        the gamma function.
            
                        To make the computation faster, not all the sum is computed, part
                        of the terms are neglected based on a bound to the error obtained
                        (an error of 10% in the result is accepted).
        </member>
        <member name="M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.InterpolateToLowValue(System.Double,System.Double,System.Double,System.Double,System.Double)">
            Interpolate y value corresponding to 'x' value given 
        </member>
        <member name="M:Spire.Barcode.Implementation.Scanner.Reader.DataMatrix.Utils.NFAUtils.InterpolateToHighValue(System.Double,System.Double,System.Double,System.Double,System.Double)">
            Interpolate y value corresponding to 'x' value given 
        </member>
        <member name="T:Spire.Barcode.IDataEncoder">
            <summary>
            Encoder interface for barcode symbols.
            </summary>
            <remarks>
            Classes that encode barcode data into arrays of bits (<see cref="T:System.Collections.BitArray"/>) should
            implement this interface.
            </remarks>
        </member>
        <member name="M:Spire.Barcode.IDataEncoder.Encode(System.String)">
            <summary>
            Encodes a string of barcode data.
            </summary>
            <param name="data">The string of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="M:Spire.Barcode.IDataEncoder.Encode(System.Char)">
            <summary>
            Encodes a character of barcode data.
            </summary>
            <param name="datum">The character of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="T:Spire.Barcode.TableEncoder">
            <summary>
            Encoder base class for table-lookup like encoders.
            </summary>
        </member>
        <member name="M:Spire.Barcode.TableEncoder.LookUp(System.Int32)">
            <summary>
            Looks up the sought value and returns its encoded equivalent.
            Should be properly overriden in derived classes.
            </summary>
            <param name="index">Index of the value to look up.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="M:Spire.Barcode.TableEncoder.Encode(System.String)">
            <summary>
            Encodes a string of barcode data.
            </summary>
            <param name="data">The string of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="M:Spire.Barcode.TableEncoder.Encode(System.Char)">
            <summary>
            Encodes a character of barcode data. 
            </summary>
            <remarks>
            This method uses the <see cref="M:Spire.Barcode.TableEncoder.LookUp(System.Int32)"/> method to resolve 
            the character of data to an encoded entity (<see cref="T:System.Collections.BitArray"/>).
            It assumes the datum passed represents a number and converts it before
            looking it up.
            </remarks>
            <param name="datum">The character of data to be encoded.</param>
            <returns>The encoded data.</returns>
        </member>
        <member name="T:Spire.Barcode.BarCodeFormatException">
            <summary>
            The exception thrown when the barcode has an incorrect format.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarCodeFormatException.#ctor">
            <overloads>
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class.
            </summary>
            </overloads>
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarCodeFormatException.#ctor(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class with the given message.
            </summary>
            <param name="message">Error message of the exception.</param>
        </member>
        <member name="M:Spire.Barcode.BarCodeFormatException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeFormatException"/> class with the given message and 
            the given inner exception.
            </summary>
            <param name="message">Error message of the exception.</param>
            <param name="innerException">Inner exception.</param>
        </member>
        <member name="T:Spire.Barcode.BarCodeType">
            <summary>
            Identifies the barcode types.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Codabar">
            <summary>
            Code bar Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code11">
            <summary>
            Code 1 of 1 Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code25">
            <summary>
            Standard 2 of 5 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Interleaved25">
            <summary>
            Interleaved 2 of 5 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code39">
            <summary>
            Code 3 of 9 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code39Extended">
            <summary>
            Extended Code 3 of 9 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code93">
            <summary>
            Code 9 of 3 Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code93Extended">
            <summary>
            Extended Code 9 of 3 Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Code128">
            <summary>
            Code 128 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN8">
            <summary>
            EAN-8 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN13">
            <summary>
            EAN-13 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN128">
            <summary>
            EAN-128 barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.EAN14">
            <summary>
            EAN-14 barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SCC14">
            <summary>
            SCC14 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SSCC18">
            <summary>
            SSCC18 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.ITF14">
            <summary>
            ITF14 Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.ITF6">
            <summary>
            ITF-6 Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.UPCA">
            <summary>
            UPCA barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.UPCE">
            <summary>
            UPCE barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.PostNet">
            <summary>
            Postnet barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Planet">
            <summary>
            Planet barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.MSI">
            <summary>
            MSI barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.DataMatrix">
            <summary>
            2D Barcode DataMatrix
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.QRCode">
            <summary>
            QR Code barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Pdf417">
            <summary>
            Pdf417 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Pdf417Macro">
            <summary>
            Pdf417 Macro barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSS14">
            <summary>
            RSS14 barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSS14Truncated">
            <summary>
            RSS-14 Truncated barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSSLimited">
            <summary>
            RSS Limited Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RSSExpanded">
            <summary>
            RSS Expanded Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.USPS">
            <summary>
            USPS OneCode barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SwissPostParcel">
            <summary>
            Swiss Post Parcel Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.PZN">
            <summary>
            PZN Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.OPC">
            <summary>
            OPC(Optical Product Code) Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.DeutschePostIdentcode">
            <summary>
            Deutschen Post Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.DeutschePostLeitcode">
            <summary>
            Deutsche Post Leitcode Barcode.
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.RoyalMail4State">
            <summary>
            Royal Mail 4-state Customer Code Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.SingaporePost4State">
            <summary>
            Singapore Post Barcode. 
            </summary>
        </member>
        <member name="F:Spire.Barcode.BarCodeType.Aztec">
            
              Aztec Barcode
            
        </member>
        <member name="F:Spire.Barcode.BarCodeType.MicroQR">
            <summary>
            Micro QRCode
            </summary>
        </member>
        <member name="T:Spire.Barcode.IChecksum">
            <summary>
            Interface to calculate checksums on barcode data.
            </summary>
            <remarks>
            Classes that calculate checksums on barcode data should implement this interface.
            It makes for a pluggable checksum architecture.
            </remarks>
        </member>
        <member name="M:Spire.Barcode.IChecksum.Calculate(System.String)">
            <summary>
            Calculates the checksum of a string of data.
            </summary>
            <param name="data">The data to calculate the checksum for.</param>
            <returns>The calculated checksum.</returns>
        </member>
        <member name="T:Spire.Barcode.IOptionalChecksum">
            <summary>
            Determines whether the checksum calculation in a barcode is optional.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IOptionalChecksum.UseChecksum">
            <summary>
            Determines whether the barcode will calculate a checksum on its data.
            </summary>
        </member>
        <member name="T:Spire.Barcode.Forms.BarCodeControl">
            <summary>
            Control to render bar codes.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.#ctor">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.Forms.BarCodeControl"/>.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.#ctor(System.String)">
            <summary>
             Creates a new instance of the <see cref="T:Spire.Barcode.Forms.BarCodeControl"/>.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.#ctor(System.String,Spire.Barcode.BarCodeType)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.Forms.BarCodeControl"/>.
            </summary>
            <param name="text">text</param>
            <param name="barcodeType">barcode type </param>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Type">
            <summary>
            The type of barcode to render.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Data">
            <summary>
            The data to render in the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Unit">
            <summary>
            The unit to use when rendering the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BackColor">
            <summary>
            The back color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TextColor">
            <summary>
            The color of the bar of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BarHeight">
            <summary>
            The height of the bar.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TopMargin">
            <summary>
            The top margin of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.LeftMargin">
            <summary>
            The width offset of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TextFont">
            <summary>
            The font of the barcode text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TextAlignment">
            <summary>
            The position of the text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ITF14BearerBars">
            <summary>
            ITF14 barcode Bearer Bars
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.UseChecksum">
            <summary>
            Indicates whether to use a checksum on barcodes where it is optional.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
            Renders the barcode.
            </summary>
            <param name="e">Paint event arguments.</param>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.DrawBarCode(System.Drawing.Graphics)">
            <summary>
            Draws the barcode in the canvas passed.
            </summary>
            <param name="canvas">Canvas to draw barcode into.</param>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.AutoResize">
            <summary>
            The size of image automatically.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Data2D">
            <summary>
             The data to render with the 2D barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TopText">
            <summary>
            Text above barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TopTextColor">
            <summary>
            Text above barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TopTextFont">
            <summary>
            Text above barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ShowTopText">
            <summary>
            Indicates whether shows top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TextRenderingHint">
             <summary>
            Quality of text rendering. 
             </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Rotate">
            <summary>
            Rotation angle of Barcode image. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ForeColor">
            <summary>
            The fore color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ShowText">
            <summary>
            Indicates whether display barcode data text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ShowTextOnBottom">
            <summary>
            Indicates whether display barcode data text on bottom.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BottomMargin">
            <summary>
            The bottom margin of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TextMargin">
            <summary>
            Space between barcode and text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.RightMargin">
            <summary>
            The right margin of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.UseAntiAlias">
            <summary>
            Inidcates whether use anti alias mode to render image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ImageHeight">
            <summary>
            Height of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ImageWidth">
            <summary>
            Width of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ColumnCount">
            <summary>
            Columns of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.RowCount">
            <summary>
            Rows of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.DpiX">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.DpiY">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ResolutionType">
            <summary>
            Gets or sets the resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ShowCheckSumChar">
            <summary>
            Indicates whether shows checksum digit in Code128 and EAN128 Barcodes.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.CodabarStartChar">
            <summary>
            Start character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.CodabarStopChar">
            <summary>
            Stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ShowStartCharAndStopChar">
            <summary>
            Show start character and stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.SupData">
            <summary>
            Supplement data.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.SupSpace">
            <summary>
            Space between main and supplement Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.WideNarrowRatio">
            <summary>
            Wide/narrow ratio.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.HasBorder">
            <summary>
            Indicates whether has border;
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BorderWidth">
            <summary>
            Borders's width of barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BorderColor">
            <summary>
            Border's color.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BorderDashStyle">
            <summary>
            Border's Dash style.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.X">
            <summary>
            Width of barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Y">
            <summary>
            Height of 2D barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.XYRatio">
            <summary>
            Height/width ratio of 2D Barcode's module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Pdf417DataMode">
            <summary>
            Gets or sets data mode of Pdf417 barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Pdf417ECL">
            <summary>
            Error correction level of pdf417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.Pdf417Truncated">
            <summary>
            Indicates wheter has been truncated of pdf 417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.MacroFileIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's file index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.MacroSegmentIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's segment index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.QRCodeDataMode">
            <summary>
            Gets or sets of QRCode Barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.QRCodeLogoImage">
            <summary>
            Gets or sets logo image of QRCode module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.QRCodeECL">
            <summary>
            Error correction level of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.AztecLayers">
            <summary>
            Gets or sets a value specifies the required number of layers for an Aztec code.
            A negative number(-1, -2, -3, -4) specifies a compact Aztec code
            0 indicates to use the minimum nuber for layers (the default)
            A positive number (1, 2, ... 32) specifies a normal (non-compact) Aztec code.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.AztecErrorCorrection">
            <summary>
            Gets or sets a value specifies what degree of error correction. the default is 23.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.DataMatrixSymbolShape">
            <summary>
            Gets or sets a value specifies the symbol shape hint for DataMatrix barcode. the default is Auto.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.GenerateImage">
            <summary>
            Generate barcode image.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.SaveToFile(System.String)">
            <summary>
            Save image to disk file.
            </summary>
            <param name="filename">file name</param>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.SaveToStream(System.IO.Stream,System.Drawing.Imaging.ImageFormat)">
            <summary>
            Save image to stream.
            </summary>
            <param name="stream">stream</param>
            <param name="format">image format</param>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.SaveToFile(System.String,System.Drawing.Imaging.ImageFormat)">
            <summary>
            Save image to disk file.
            </summary>
            <param name="filename">file name</param>
            <param name="format">image format</param>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.Print(System.String)">
            <summary>
            Print barcode.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControl.Print">
            <summary>
            Print barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TopTextAligment">
            <summary>
            The position of the top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.TopTextMargin">
            <summary>
            The top margin of the top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BottomText">
            <summary>
            Text bottom barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BottomTextColor">
            <summary>
            Text bottom barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.BottomTextFont">
            <summary>
            Text bottom barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControl.ShowBottomText">
            <summary>
            Indicates whether shows bottom text.
            </summary>
        </member>
        <member name="T:Spire.Barcode.Forms.BarCodeControlDesigner">
            <summary>
            Designer class for the <see cref="T:Spire.Barcode.Forms.BarCodeControl"/> class.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.BarCodeControlDesigner.PostFilterProperties(System.Collections.IDictionary)">
            <summary>
            Remove some basic properties that are not supported by the 
            <see cref="T:Spire.Barcode.Forms.BarCodeControl"/>.
            </summary>
            <param name="properties">Collection of the control's properties.</param>
        </member>
        <member name="P:Spire.Barcode.Forms.BarCodeControlDesigner.SelectionRules">
            <summary>
            Defines the selection rules for the <see cref="T:Spire.Barcode.Forms.BarCodeControl"/>.
            In particular, does not allow any resizing to occur.
            </summary>
        </member>
        <member name="F:Spire.Barcode.Forms.KeyRequirementForm2.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Spire.Barcode.Forms.KeyRequirementForm2.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Spire.Barcode.Forms.KeyRequirementForm2.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:Spire.Barcode.ITF14BorderType.None">
            <summary>
            No border enclosing the Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.ITF14BorderType.Frame">
            <summary>
            Frame enclosing the Barcode
            </summary>
        </member>
        <member name="F:Spire.Barcode.ITF14BorderType.Bar">
            <summary>
            Tow horizontal bars enclosing the Barcode
            </summary>
        </member>
        <member name="T:Spire.Barcode.BarCodeGenerator">
            <summary>
            Operates on <see cref="T:Spire.Barcode.IBarcodeSettings"/>
            to provide barcode rendering and conversion services.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.#ctor(Spire.Barcode.IBarcodeSettings)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.BarCodeGenerator"/> class.
            </summary>
            <param name="settings">The settings to use.</param>
            <exception cref="T:System.ArgumentNullException">
            If the settings passed are <c>null</c>.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.GenerateImage">
            <summary>
            Generates an image with the rendered barcode based on the settings (<see cref="T:Spire.Barcode.IBarcodeSettings"/>).
            </summary>
            <returns>The generated barcode image.</returns>
            <exception cref="T:Spire.Barcode.BarCodeFormatException">
            If the data in the settings can't be rendered by the selected barcode.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.GenerateImage(System.Drawing.Size)">
            <summary>
            Generates an image with the rendered barcode based on the settings (<see cref="T:Spire.Barcode.IBarcodeSettings"/>).
            </summary>
            <returns>The generated barcode image.</returns>
            <exception cref="T:Spire.Barcode.BarCodeFormatException">
            If the data in the settings can't be rendered by the selected barcode.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.GetBarCodeType(Spire.Barcode.BarCodeType)">
            <summary>
            Instantiates the barcode type asked by the settings.
            </summary>
            <returns>The instantiated barcode type.</returns>
            <exception cref="T:System.InvalidOperationException">
            If the settings contain an invalid barcode type.
            </exception>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.AssembleBarCode">
            <summary>
            Instantiates and sets the barcode properties based on the settings.
            </summary>
            <returns>Instantiated and set up barcode.</returns>
        </member>
        <member name="M:Spire.Barcode.BarCodeGenerator.CopySettings(Spire.Barcode.IBarcodeSettings,Spire.Barcode.IBarcodeSettings)">
            <summary>
            Copy settings between <see cref="T:Spire.Barcode.IBarcodeSettings"/>.
            </summary>
            <param name="srcSetting">Settings to copy from.</param>
            <param name="settingsCopyTo">Settings to copy to.</param>
        </member>
        <member name="T:Spire.Barcode.BarcodeScanner">
            <summary>
             Provide barcode scanning services.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String,Spire.Barcode.BarCodeType)">
            <summary>
            Scans barcode from image file.
            </summary>
            <param name="fileName">file Name</param>
            <param name="barcodeType">barcode type</param>
            <returns>barcode text list</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap,Spire.Barcode.BarCodeType)">
            <summary>
            Scan barcode from image.
            </summary>
            <param name="image">image object</param>
            <param name="barcodeType">barcode type</param>
            <returns>Barcode text list</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.String,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.String)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.String,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.String,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image file.
            </summary>
            <param name="fileName">Image file path.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.IO.Stream)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.IO.Stream,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.IO.Stream,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.IO.Stream)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.IO.Stream,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.IO.Stream,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scan barcode from image stream.
            </summary>
            <param name="stream">Image stream.</param>
            <param name="barcodeType">The barcode type.</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap)">
            <summary>
            Scan barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.ScanOne(System.Drawing.Bitmap)">
            <summary>
            Scan barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <returns>Barcode text.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap,System.Drawing.Rectangle,Spire.Barcode.BarCodeType)">
            <summary>
            Scans barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <param name="rect">Scan rectangle</param>
            <param name="barcodeType">Barcode Type</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="M:Spire.Barcode.BarcodeScanner.Scan(System.Drawing.Bitmap,System.Drawing.Rectangle,Spire.Barcode.BarCodeType,System.Boolean)">
            <summary>
            Scans barcode from bitmap.
            </summary>
            <param name="bitmap">Bitmap object.</param>
            <param name="rect">Scan rectangle</param>
            <param name="barcodeType">Barcode Type</param>
            <param name="IncludeCheckSum">The include check sum.</param>
            <returns>Barcode text list.</returns>
        </member>
        <member name="T:Spire.Barcode.BarcodeSettings">
            <summary>
            Packages settings for barcodes.
            Canonical implementation of <see cref="T:Spire.Barcode.IBarcodeSettings"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Type">
            <summary>
            The type of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Data">
            <summary>
            The data to render with the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BackColor">
            <summary>
            The back color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextColor">
            <summary>
            The color of the bar of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BarHeight">
            <summary>
            The height of the barcode. Affected by <see cref="P:Spire.Barcode.BarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.FontColor">
            <summary>
            The color of the font to render text in the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopMargin">
            <summary>
            The vertical top  offset height of the barcode to the border.
            Affected by <see cref="P:Spire.Barcode.BarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.LeftMargin">
            <summary>
            The horizontal (left and right) offset width of the barcode to the border.
            Affected by <see cref="P:Spire.Barcode.BarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextMargin">
            <summary>
            The top margin of top text
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextFont">
            <summary>
            The font used to render the text inside the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.UseChecksum">
            <summary>
            Whether the barcode will use an (optional) checksum.
            Not every barcode requires a checksum, and others mandate it.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.AutoResize">
            <summary>
            Gets or sets adjust size of barcode image automatically.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Data2D">
            <summary>
            Gets or sets text of 2D barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopText">
            <summary>
            Top text of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextColor">
            <summary>
            Top text color of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ITF14BearerBars">
            <summary>
            ITF14 Bearer bar
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextFont">
            <summary>
            Top text font of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowTopText">
            <summary>
            Indicates whether displays top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Unit">
            <summary>
            Measurement unit.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextRenderingHint">
            <summary>
            Gets or sets quality of barcode text rendering.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Rotate">
            <summary>
            Gets or set rotation angle of BarCode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ForeColor">
            <summary>
            Gets or sets foreground color of the barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowText">
            <summary>
            Indicates whether display barcode's text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowTextOnBottom">
            <summary>
            Indicates whether display barcode's text on bottom.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomMargin">
            <summary>
            The vertical bottom offset height of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextMargin">
            <summary>
            Space between barcode and text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.RightMargin">
            <summary>
            The horizontal right offset width of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TextAlignment">
            <summary>
            The position of the text rendered in the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.UseAntiAlias">
            <summary>
            Inidcates whether use anti alias mode to render image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ImageHeight">
            <summary>
             Height of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ImageWidth">
            <summary>
            Width of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ColumnCount">
            <summary>
            Columns of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.RowCount">
            <summary>
            Rows of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.DpiX">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.DpiY">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ResolutionType">
            <summary>
            Gets or sets the resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowCheckSumChar">
            <summary>
            Indicates whether shows checksum digit in Code128 and EAN128 Barcodes.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.CodabarStartChar">
            <summary>
            Start character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.CodabarStopChar">
            <summary>
            Stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowStartCharAndStopChar">
            <summary>
            Show start character and stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.SupData">
            <summary>
            Supplement data.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.SupSpace">
            <summary>
            Space between main and supplement Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.WideNarrowRatio">
            <summary>
            Wide/narrow ratio.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.HasBorder">
            <summary>
            Indicates whether has border;
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BorderWidth">
            <summary>
            Borders's width of barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BorderColor">
            <summary>
            Border's color
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BorderDashStyle">
            <summary>
            Border's Dash style.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.X">
            <summary>
            Width of barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Y">
            <summary>
            Height of 2D barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.XYRatio">
            <summary>
            height/width ratio of 2D Barcode's module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Code128SetMode">
            <summary>
            Gets or sets code set of Barcode128 barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Pdf417DataMode">
            <summary>
            Gets or sets data mode of Pdf417 barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Pdf417ECL">
            <summary>
            Error correction level of pdf417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.Pdf417Truncated">
            <summary>
            Indicates wheter has been truncated of pdf 417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.AztecLayers">
            <summary>
            Gets or sets a value specifies the required number of layers for an Aztec code.
            A negative number(-1, -2, -3, -4) specifies a compact Aztec code
            0 indicates to use the minimum nuber for layers (the default)
            A positive number (1, 2, ... 32) specifies a normal (non-compact) Aztec code.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.AztecErrorCorrection">
            <summary>
            Gets or sets a value specifies what degree of error correction. the default is 23.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.DataMatrixSymbolShape">
            <summary>
            Gets or sets a value specifies the symbol shape hint for DataMatrix barcode. the default is Auto.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.MacroFileIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's file index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.MacroSegmentIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's segment index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.QRCodeDataMode">
            <summary>
            Gets or sets of QRCode Barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.QRCodeECL">
            <summary>
            Error correction level of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.QRCodeLogoImage">
            <summary>
            Gets or sets logo image of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.TopTextAligment">
            <summary>
            Indicate top text aligment.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BarcodeSettings.ApplyKey(System.String)">
            <summary>
            Provide a valid key to remove the logo of E-iceblue in
            the barcode view or image generated by this component
            </summary>
            <param name="key">Barcode key</param>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomTextColor">
            <summary>
            Bottom text color of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomTextFont">
            <summary>
            Bottom text font of barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.ShowBottomText">
            <summary>
            Indicates whether displays bottom text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.BarcodeSettings.BottomTextAligment">
            <summary>
            Indicate bottom text aligment.
            </summary>
        </member>
        <member name="T:Spire.Barcode.IBarcodeSettings">
            <summary>
            Defines barcode settings.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.AutoResize">
            <summary>
            The size of image automatically.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Type">
            <summary>
            The type of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Data">
            <summary>
            The data to render with the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Data2D">
            <summary>
            The data to render with the 2D barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopText">
            <summary>
            Top text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextColor">
            <summary>
            Text color of top text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextFont">
            <summary>
            Text font of top text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowTopText">
            <summary>
            Indicates whether shows top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextAligment">
            <summary>
            Indicate top text aligment.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopTextMargin">
            <summary>
            Indicate top text margin.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Unit">
            <summary>
            The unit of measure of the barcode's measurable properties.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextRenderingHint">
            <summary>
            Quality of text rendering. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Rotate">
            <summary>
            Rotation angle of Barcode image. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ITF14BearerBars">
            <summary>
            ITF14 barcode Bearer Bars
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BackColor">
            <summary>
            The back color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ForeColor">
            <summary>
            The fore color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowText">
            <summary>
            Indicates whether display barcode data text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowTextOnBottom">
            <summary>
            Indicates whether display barcode data text on bottom.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextColor">
            <summary>
            The color of the bar of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BarHeight">
            <summary>
            The height of the barcode. Affected by <see cref="P:Spire.Barcode.IBarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TopMargin">
            <summary>
            The vertical top  offset height of the barcode to the border.
            Affected by <see cref="P:Spire.Barcode.IBarcodeSettings.Unit"/>.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomMargin">
            <summary>
            The vertical bottom offset height of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextMargin">
            <summary>
            Space between barcode and text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.LeftMargin">
            <summary>
            The horizontal left offset width of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.RightMargin">
            <summary>
            The horizontal right offset width of the barcode to the border.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextFont">
            <summary>
            The font used to render the text inside the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.TextAlignment">
            <summary>
            The position of the text rendered in the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.UseChecksum">
            <summary>
            Whether the barcode will use an (optional) checksum.
            Not every barcode requires a checksum, and others mandate it.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.UseAntiAlias">
            <summary>
            Inidcates whether use anti alias mode to render image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ImageHeight">
            <summary>
            Height of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ImageWidth">
            <summary>
            Width of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ColumnCount">
            <summary>
            Columns of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.RowCount">
            <summary>
            Rows of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.DpiX">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.DpiY">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ResolutionType">
            <summary>
            Gets or sets the resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowCheckSumChar">
            <summary>
            Indicates whether shows checksum digit in Code128 and EAN128 Barcodes.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.CodabarStartChar">
            <summary>
            Start character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.CodabarStopChar">
            <summary>
            Stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowStartCharAndStopChar">
            <summary>
            Show start character and stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.SupData">
            <summary>
            Supplement data.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.SupSpace">
            <summary>
            Space between main and supplement Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.WideNarrowRatio">
            <summary>
            Wide/narrow ratio.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.HasBorder">
            <summary>
            Indicates whether has border;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BorderWidth">
            <summary>
            Borders's width of barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BorderColor">
            <summary>
            Border's color
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BorderDashStyle">
            <summary>
            Border's Dash style.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.X">
            <summary>
            Width of barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Y">
            <summary>
            Height of 2D barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.XYRatio">
            <summary>
             height/width ratio of 2D Barcode's module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Pdf417DataMode">
            <summary>
             Gets or sets data mode of Pdf417 barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Pdf417ECL">
            <summary>
            Error correction level of pdf417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.Pdf417Truncated">
            <summary>
            Indicates wheter has been truncated of pdf 417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.MacroFileIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's file index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.MacroSegmentIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's segment index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.QRCodeDataMode">
            <summary>
             Gets or sets of QRCode Barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.QRCodeECL">
            <summary>
            Error correction level of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.QRCodeLogoImage">
            <summary>
            Gets or sets logo image of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.AztecErrorCorrection">
            <summary>
            Gets or sets a value specifies what degree of error correction. the default is 23.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.AztecLayers">
            <summary>
            Gets or sets a value specifies the required number of layers for an Aztec code.
            A negative number(-1, -2, -3, -4) specifies a compact Aztec code
            0 indicates to use the minimum nuber for layers (the default)
            A positive number (1, 2, ... 32) specifies a normal (non-compact) Aztec code.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.DataMatrixSymbolShape">
            <summary>
            Gets or sets a value specifies the symbol shape hint for DataMatrix barcode. the default is Auto.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomText">
            <summary>
            Bottom text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomTextColor">
            <summary>
            Text color of Bottom text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomTextFont">
            <summary>
            Text font of Bottom text;
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.ShowBottomText">
            <summary>
            Indicates whether shows Bottom text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.IBarcodeSettings.BottomTextAligment">
            <summary>
            Indicate Bottom text aligment.
            </summary>
        </member>
        <member name="T:Spire.Barcode.BitArrayHelper">
            <summary>
            Utility class for BitArray operations.
            </summary>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.PopFront(System.Collections.BitArray[]@)">
            <summary>
            Removes the first <see cref="T:System.Collections.BitArray"/> of an array of
            BitArrays and returns it.
            </summary>
            <param name="bits">Array of bit arrays to work on. On return, will lack the first element.</param>
            <returns>The first element of the BitArray array parameter.</returns>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.PopBack(System.Collections.BitArray[]@)">
            <summary>
            Removes the last <see cref="T:System.Collections.BitArray"/> of an array of
            BitArrays and returns it.
            </summary>
            <param name="bits">Array of bit arrays to work on. On return, will lack the last element.</param>
            <returns>The last element of the BitArray array parameter.</returns>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.ToBitArray(System.String)">
            <summary>
            Converts a string of data consisting of '1's and '0's
            into a <see cref="T:System.Collections.BitArray"/>.
            </summary>
            <param name="data">Input data.</param>
            <returns>BitArray of input data.</returns>
        </member>
        <member name="M:Spire.Barcode.BitArrayHelper.ToBitMatrix(System.String[])">
            <summary>
            Converts an array of strings of data consisting of '1's and '0's
            into an array of corresponding <see cref="T:System.Collections.BitArray"/>s.
            </summary>
            <param name="data">Input strings.</param>
            <returns>Bit matrix (array of BitArrays) created.</returns>
        </member>
        <member name="M:Spire.Barcode.MathMethods.CompareFloat(System.Single,System.Single,System.Single)">
            <summary>
            Compares the floating number.
            </summary>
            <param name="val1">The value1.</param>
            <param name="val2">The value2.</param>
            <param name="accuracy">The accuracy.</param>
            <returns><c>return 0,val1 equal val2;return 1,val1 greater than val2;return -1,val1 less than val2;</c></returns>
        </member>
        <member name="M:Spire.Barcode.MathMethods.CompareDouble(System.Double,System.Double,System.Double)">
            <summary>
            Compares the double number.
            </summary>
            <param name="val1">The value1.</param>
            <param name="val2">The value2.</param>
            <param name="accuracy">The accuracy.</param>
            <returns><c>return 0,val1 equal val2;return 1,val1 greater than val2;return -1,val1 less than val2;</c></returns>
        </member>
        <member name="T:Spire.Barcode.DebugStream">
            <summary>
            A <see cref="T:System.IO.Stream"/> decorator used to trace stream reads and writes.
            </summary>
        </member>
        <member name="M:Spire.Barcode.DebugStream.#ctor(System.String,System.IO.Stream)">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.DebugStream"/> class.
            </summary>
            <param name="title">A name to distinguish this stream.</param>
            <param name="baseStream">The stream to be decorated by this stream.</param>
            <exception cref="T:System.ArgumentNullException">
            If the title or the base stream are null or the title is empty.
            </exception>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Flush">
            <summary>
            Flushes the base stream's contents.
            </summary>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a chunk from the base stream.
            </summary>
            <param name="buffer">Buffer to store bytes read.</param>
            <param name="offset">Offset into buffer to begin storing bytes read.</param>
            <param name="count">Number of bytes to read.</param>
            <returns>Number of bytes actually read.</returns>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the base stream.
            </summary>
            <param name="offset">Size of the position seek operation.</param>
            <param name="origin">Where to seek from.</param>
            <returns>The position set on the base stream.</returns>
        </member>
        <member name="M:Spire.Barcode.DebugStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the base stream.
            </summary>
            <param name="value">New length to set.</param>
        </member>
        <member name="M:Spire.Barcode.DebugStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a number of bytes to the base stream.
            </summary>
            <param name="buffer">Buffer with bytes for writing.</param>
            <param name="offset">Offset into the buffer.</param>
            <param name="count">Number of bytes to read from the buffer and write to the stream.</param>
        </member>
        <member name="P:Spire.Barcode.DebugStream.CanRead">
            <summary>Determines whether the stream supports reading.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.CanSeek">
            <summary>Determines whether the stream supports seeking.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.CanWrite">
            <summary>Determines whether the stream supports writing.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.Length">
            <summary>The length of the stream.</summary>
        </member>
        <member name="P:Spire.Barcode.DebugStream.Position">
            <summary>The current position of the stream.</summary>
        </member>
        <member name="M:Spire.Barcode.DebugStream.LogMessage(System.String,System.Object[])">
            <summary>
            Logs a message to the <see cref="T:System.Diagnostics.Debug"/> class.
            </summary>
            <param name="message">Message to log, in the format of <see cref="M:System.String.Format(System.String,System.Object)"/>.</param>
            <param name="parameters">Parameters to the positional elements of the message.</param>
        </member>
        <member name="F:Spire.Barcode.DebugStream._baseStream">
            <summary>The decorated base stream.</summary>
        </member>
        <member name="F:Spire.Barcode.DebugStream._title">
            <summary>The title of this stream.</summary>
        </member>
        <member name="T:Spire.Barcode.StringHelper">
            <summary>
            Utility class for common string operations.
            </summary>
        </member>
        <member name="M:Spire.Barcode.StringHelper.IsNullOrEmpty(System.String)">
            <summary>
            Determines if a string is null of empty. It's emptiness is
            determined by first trimming it.
            </summary>
            <param name="stringToCheck">String to check for nullness or emptiness.</param>
            <returns><c>True</c> if the string is null or empty, <c>false</c> otherwise.</returns>
        </member>
        <member name="T:Spire.Barcode.WebUI.BarCodeControl">
            <summary>
            Control to render bar codes.
            </summary>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeControl.#ctor">
            <summary>
            Creates a new instance of the <see cref="T:Spire.Barcode.WebUI.BarCodeControl"/> class.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ImageFormat">
            <summary>
            Gets and sets the format of the rendered barcode image
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Type">
            <summary>
            The type of the barcode to render.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Data">
            <summary>
            The data to render with the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Unit">
            <summary>
            The unit to use when rendering the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BackColor">
            <summary>
            The back color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TextColor">
            <summary>
            The color of the text of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BarHeight">
            <summary>
            The height of the bar.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.FontColor">
            <summary>
            The font color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TopMargin">
            <summary>
            The top margin of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.LeftMargin">
            <summary>
            The width offset of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TextFont">
            <summary>
            The font of the barcode text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TextAlignment">
            <summary>
            The position of the text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ITF14BearerBars">
            <summary>
            ITF14 Barcode's bearer bars
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.UseChecksum">
            <summary>
            Indicates whether to use a checksum on barcodes where it is optional.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BarCodeHandlerUrl">
            <summary>
            Barcode HTTP handler URL.
            </summary>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeControl.Render(System.Web.UI.HtmlTextWriter)">
            <summary> 
            Render this control to the output parameter specified.
            </summary>
            <param name="output">The HTML generator to write out to.</param>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeControl.OnInit(System.EventArgs)">
            <summary>
            init function
            </summary>
            <param name="e">EventArgs</param>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeControl.GenerateImage">
            <summary>
            Generate barcode image.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeControl.SaveToFile(System.String)">
            <summary>
            Save image to disk file.
            </summary>
            <param name="filename">file name</param>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeControl.SaveToStream(System.IO.Stream,System.Drawing.Imaging.ImageFormat)">
            <summary>
            Save image to stream.
            </summary>
            <param name="stream">stream</param>
            <param name="format">image format</param>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeControl.SaveToFile(System.String,System.Drawing.Imaging.ImageFormat)">
            <summary>
            Save image to disk file.
            </summary>
            <param name="filename">file name</param>
            <param name="format">image format</param>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.AutoResize">
            <summary>
            The size of image automatically.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Data2D">
            <summary>
             The data to render with the 2D barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TopText">
            <summary>
            Text above barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TopTextColor">
            <summary>
            Text above barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TopTextFont">
            <summary>
            Text above barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ShowTopText">
            <summary>
            Indicates whether shows top text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TextRenderingHint">
             <summary>
            Quality of text rendering. 
             </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Rotate">
            <summary>
            Rotation angle of Barcode image. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ForeColor">
            <summary>
            The fore color of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ShowText">
            <summary>
            Indicates whether display barcode data text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ShowTextOnBottom">
            <summary>
            Indicates whether display barcode data text on bottom.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BottomMargin">
            <summary>
            The bottom margin of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.TextMargin">
            <summary>
            Space between barcode and text.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.RightMargin">
            <summary>
            The right margin of the barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.UseAntiAlias">
            <summary>
            Inidcates whether use anti alias mode to render image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ImageHeight">
            <summary>
            Height of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ImageWidth">
            <summary>
            Width of Barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ColumnCount">
            <summary>
            Columns of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.RowCount">
            <summary>
            Rows of 2D Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.DpiX">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.DpiY">
            <summary>
            Gets or sets the horizontal resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ResolutionType">
            <summary>
            Gets or sets the resolution.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.UseHttpHandlerMode">
            <summary>
            Indicates whether use httphandler mode to render barcode.
            <code>
            <system.web>
                <httpHandlers>
                     <add verb="*" path ="BarCodeHandler.axd" type="Spire.Barcode.WebUI.BarCodeHandler,Spire.Barcode"/>
                 </httpHandlers>
            </system.web>
            </code>
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ShowCheckSumChar">
            <summary>
            Indicates whether shows checksum digit in Code128 and EAN128 Barcodes.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.CodabarStartChar">
            <summary>
            Start character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.CodabarStopChar">
            <summary>
            Stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ShowStartCharAndStopChar">
            <summary>
            Show start character and stop character of codabar barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.SupData">
            <summary>
            Supplement data.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.SupSpace">
            <summary>
            Space between main and supplement Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.WideNarrowRatio">
            <summary>
            Wide/narrow ratio.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.HasBorder">
            <summary>
            Indicates whether has border;
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BorderWidth">
            <summary>
            Borders's width of barcode image.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BorderColor">
            <summary>
            Border's color.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BorderDashStyle">
            <summary>
            Border's Dash style.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.X">
            <summary>
            Width of barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Y">
            <summary>
            Height of 2D barcode bar module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.XYRatio">
            <summary>
            Height/width ratio of 2D Barcode's module.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Pdf417DataMode">
            <summary>
            Gets or sets data mode of Pdf417 barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Pdf417ECL">
            <summary>
            Error correction level of pdf417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.Pdf417Truncated">
            <summary>
            Indicates wheter has been truncated of pdf 417 Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.MacroFileIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's file index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.MacroSegmentIndex">
            <summary>
            Gets or sets macro pdf417 Barcode's segment index.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.QRCodeDataMode">
            <summary>
            Gets or sets of QRCode Barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.QRCodeECL">
            <summary>
            Error correction level of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.QRCodeLogoImage">
            <summary>
            Gets or sets logo image of QRCode Barcode.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.AztecLayers">
            <summary>
            Gets or sets a value specifies the required number of layers for an Aztec code.
            A negative number(-1, -2, -3, -4) specifies a compact Aztec code
            0 indicates to use the minimum nuber for layers (the default)
            A positive number (1, 2, ... 32) specifies a normal (non-compact) Aztec code.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.AztecErrorCorrection">
            <summary>
            Gets or sets a value specifies what degree of error correction. the default is 23.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.DataMatrixSymbolShape">
            <summary>
            Gets or sets a value specifies the symbol shape hint for DataMatrix barcode. the default is Auto.
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BottomText">
            <summary>
            Text bottom barcode. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BottomTextColor">
            <summary>
            Text bottom barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.BottomTextFont">
            <summary>
            Text bottom barcode style. 
            </summary>
        </member>
        <member name="P:Spire.Barcode.WebUI.BarCodeControl.ShowBottomText">
            <summary>
            Indicates whether shows bottom text.
            </summary>
        </member>
        <member name="T:Spire.Barcode.WebUI.BarCodeHandler">
            <summary>
            Http handler to generate bar code images.
            </summary>
        </member>
        <member name="T:Spire.Barcode.WebUI.BarCodeHtmlHelper">
            <summary>
            Helper class to render HTML tags for barcodes. Useful from ASP.NET MVC.
            </summary>
        </member>
        <member name="M:Spire.Barcode.WebUI.BarCodeHtmlHelper.BarCode(Spire.Barcode.IBarcodeSettings,System.String)">
            <summary>
            Generates the barcode tags to render to barcode expressed in the input <paramref name="settings"/>.
            </summary>
            <param name="settings">Settings for the barcode to render.</param>
            <param name="barCodeHandlerUrl">
            URL for the barcode image rendering HTTP handler. 
            Must be registered in web.config, pointing to <see cref="T:Spire.Barcode.WebUI.BarCodeHandler"/>.
            </param>
            <returns></returns>
        </member>
        <member name="T:Spire.Barcode.WebUI.QueryStringSerializer">
            <summary>
            Parses and formats bar code settings (<see cref="T:Spire.Barcode.IBarcodeSettings"/>)
            as query strings.
            </summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.TYPE_KEY">
            <summary>Type of barcode key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.DATA_KEY">
            <summary>Data key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.UNIT_KEY">
            <summary>Unit key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.BACKCOLOR_KEY">
            <summary>Back color key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.TEXTCOLOR_KEY">
            <summary>Bar color key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.BARHEIGHT_KEY">
            <summary>Bar haight key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.TOPMARGIN_KEY">
            <summary>Offset height key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.LEFTMARGIN_KEY">
            <summary>Offset width key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.TEXTFONT_KEY">
            <summary>Font key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.TEXTALIGN_KEY">
            <summary>Text position key.</summary>
        </member>
        <member name="F:Spire.Barcode.WebUI.QueryStringSerializer.USECHECKSUM_KEY">
            <summary>Use checksum key.</summary>
        </member>
        <member name="M:Spire.Barcode.WebUI.QueryStringSerializer.ParseQueryString(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Parses a querystring-type parameter for <see cref="T:Spire.Barcode.IBarcodeSettings"/> properties.
            The minimum properties expected are "Type", for the type of the barcode,
            and "Data", for the data to render with the barcode.
            </summary>
            <param name="queryString">The collection of key-value pairs for parsing.</param>
            <returns>The assembled <see cref="T:Spire.Barcode.IBarcodeSettings"/>.</returns>
            <exception cref="T:System.ArgumentNullException">
            If the querystring parameter is <c>null</c>.
            </exception>
            <exception cref="T:System.InvalidOperationException">
            If the querystring parameter has any invalid property for the settings.
            </exception>
        </member>
        <member name="M:Spire.Barcode.WebUI.QueryStringSerializer.ToQueryString(Spire.Barcode.IBarcodeSettings)">
            <summary>
            Formats and returns a query string for the input settings (<see cref="T:Spire.Barcode.IBarcodeSettings"/>).
            </summary>
            <param name="settings">Input settings.</param>
            <returns>Assembled querystring.</returns>
        </member>
        <member name="M:Spire.Barcode.WebUI.QueryStringSerializer.UrlEncode(System.Object)">
            <summary>
            Url-encodes the input data.
            </summary>
            <param name="dataToEncode">Data to encode.</param>
            <returns>Encoded data.</returns>
        </member>
        <member name="T:Spire.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Spire.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Spire.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_data_1">
            <summary>
              Looks up a localized resource of type System.Drawing.Bitmap.
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_data_2">
            <summary>
             Looks up a localized resource of type System.Drawing.Bitmap. 
             </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_doc_1">
            <summary>       
            Looks up a localized resource of type System.Drawing.Bitmap.       
            </summary>  
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_doc_2">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_pdf_1">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_pdf_2">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_ppt_1">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_ppt_2">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_xls_1">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_1_xls_2">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.       
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_bg1">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.KeyRequirementForm_bg2">
            <summary>        
            Looks up a localized resource of type System.Drawing.Bitmap.        
            </summary>       
        </member>
        <member name="P:Spire.Properties.Resources.logo1">
            <summary>
              Looks up a localized resource of type System.Drawing.Bitmap.
            </summary>        
        </member>
        <member name="P:Spire.Properties.Resources.logo2">
            <summary>
              Looks up a localized resource of type System.Drawing.Bitmap.
            </summary>        
        </member>
    </members>
</doc>
