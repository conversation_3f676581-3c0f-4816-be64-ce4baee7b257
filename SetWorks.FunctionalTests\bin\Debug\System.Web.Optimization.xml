﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Optimization</name>
  </assembly>
  <members>
    <member name="T:System.Web.Optimization.Bundle">
      <summary>Represents a list of file references to be bundled together as a single resource.</summary>
    </member>
    <member name="M:System.Web.Optimization.Bundle.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.Bundle" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.Bundle.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.Bundle" /> class.</summary>
      <param name="virtualPath">The virtual path used to reference the <see cref="T:System.Web.Optimization.Bundle" /> from within a view or Web page.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.Bundle" /> class.</summary>
      <param name="virtualPath">The virtual path used to reference the <see cref="T:System.Web.Optimization.Bundle" /> from within a view or Web page.</param>
      <param name="cdnPath">An alternate url for the bundle when it is stored in a content delivery network.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.#ctor(System.String,System.String,System.Web.Optimization.IBundleTransform[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.Bundle" /> class.</summary>
      <param name="virtualPath">The virtual path used to reference the <see cref="T:System.Web.Optimization.Bundle" /> from within a view or Web page.</param>
      <param name="cdnPath">An alternate url for the bundle when it is stored in a content delivery network.</param>
      <param name="transforms">A list of <see cref="T:System.Web.Optimization.IBundleTransform" /> objects which process the contents of the bundle in the order which they are added.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.#ctor(System.String,System.Web.Optimization.IBundleTransform[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.Bundle" /> class.</summary>
      <param name="virtualPath">The virtual path used to reference the <see cref="T:System.Web.Optimization.Bundle" /> from within a view or Web page.</param>
      <param name="transforms">A list of <see cref="T:System.Web.Optimization.IBundleTransform" /> objects which process the contents of the bundle in the order which they are added.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.ApplyTransforms(System.Web.Optimization.BundleContext,System.String,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="P:System.Web.Optimization.Bundle.Builder">
      <summary>Builds the bundle content from the individual files included in the <see cref="T:System.Web.Optimization.Bundle" /> object.</summary>
      <returns>The object used to build the bundle content.</returns>
    </member>
    <member name="M:System.Web.Optimization.Bundle.CacheLookup(System.Web.Optimization.BundleContext)">
      <summary>Overrides this to implement own caching logic.</summary>
      <returns>A bundle response.</returns>
      <param name="context">The bundle context.</param>
    </member>
    <member name="P:System.Web.Optimization.Bundle.CdnFallbackExpression">
      <summary>Script expression rendered by the <see cref="T:System.Web.Optimization.Scripts" /> helper class to reference the local bundle file if the CDN is unavailable.</summary>
      <returns>The script expression rendered by the <see cref="T:System.Web.Optimization.Scripts" /> helper class to reference the local bundle file if the CDN is unavailable.</returns>
    </member>
    <member name="P:System.Web.Optimization.Bundle.CdnPath">
      <summary>Gets or sets an alternate url for the bundle when it is stored in a content delivery network.</summary>
      <returns>An alternate url for the bundle when it is stored in a content delivery network.</returns>
    </member>
    <member name="P:System.Web.Optimization.Bundle.ConcatenationToken">
      <summary>The token inserted between bundled files to ensure that the final bundle content is valid.</summary>
      <returns>By default, if <see cref="P:System.Web.Optimization.Bundle.ConcatenationToken" /> is not specified, the Web optimization framework inserts a new line.</returns>
    </member>
    <member name="P:System.Web.Optimization.Bundle.EnableFileExtensionReplacements">
      <summary>Specifies whether to use the <see cref="P:System.Web.Optimization.BundleCollection.FileExtensionReplacementList" />.</summary>
      <returns>true if the <see cref="P:System.Web.Optimization.BundleCollection.FileExtensionReplacementList" /> is used; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Optimization.Bundle.EnumerateFiles(System.Web.Optimization.BundleContext)">
      <summary>Generates an enumeration of <see cref="T:System.Web.Hosting.VirtualFile" /> objects that represent the contents of the bundle.</summary>
      <returns>An enumeration of <see cref="T:System.Web.Hosting.VirtualFile" /> objects that represent the contents of the bundle.</returns>
      <param name="context">The <see cref="T:System.Web.Optimization.BundleContext" /> object that contains state for both the framework configuration and the HTTP request.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.GenerateBundleResponse(System.Web.Optimization.BundleContext)">
      <summary>Processes the bundle request to generate the response.</summary>
      <returns>A <see cref="T:System.Web.Optimization.BundleResponse" /> object containing the processed bundle contents.</returns>
      <param name="context">The <see cref="T:System.Web.Optimization.BundleContext" /> object that contains state for both the framework configuration and the HTTP request.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.GetCacheKey(System.Web.Optimization.BundleContext)"></member>
    <member name="M:System.Web.Optimization.Bundle.Include(System.String,System.Web.Optimization.IItemTransform[])"></member>
    <member name="M:System.Web.Optimization.Bundle.Include(System.String[])">
      <summary>Specifies a set of files to be included in the <see cref="T:System.Web.Optimization.Bundle" />.</summary>
      <returns>The <see cref="T:System.Web.Optimization.Bundle" /> object itself for use in subsequent method chaining.</returns>
      <param name="virtualPaths">The virtual path of the file or file pattern to be included in the bundle.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.IncludeDirectory(System.String,System.String)">
      <summary>Includes all files in a directory that match a search pattern.</summary>
      <returns>The <see cref="T:System.Web.Optimization.Bundle" /> object itself for use in subsequent method chaining.</returns>
      <param name="directoryVirtualPath">The virtual path to the directory from which to search for files.</param>
      <param name="searchPattern">The search pattern to use in selecting files to add to the bundle.</param>
    </member>
    <member name="M:System.Web.Optimization.Bundle.IncludeDirectory(System.String,System.String,System.Boolean)">
      <summary>Includes all files in a directory that match a search pattern.</summary>
      <returns>The <see cref="T:System.Web.Optimization.Bundle" /> object itself for use in subsequent method chaining.</returns>
      <param name="directoryVirtualPath">The virtual path to the directory from which to search for files.</param>
      <param name="searchPattern">The search pattern to use in selecting files to add to the bundle.</param>
      <param name="searchSubdirectories">Specifies whether to recursively search subdirectories of <paramref name="directoryVirtualPath" />.</param>
    </member>
    <member name="P:System.Web.Optimization.Bundle.Orderer">
      <summary>Determines the order of files in a bundle.</summary>
      <returns>The order of files in a bundle.</returns>
    </member>
    <member name="P:System.Web.Optimization.Bundle.Path">
      <summary>Virtual path used to reference the <see cref="T:System.Web.Optimization.Bundle" /> from within a view or Web page.</summary>
      <returns>The virtual path.</returns>
    </member>
    <member name="P:System.Web.Optimization.Bundle.Transforms">
      <summary>Transforms the contents of a bundle.</summary>
      <returns>The list of transforms for the bundle.</returns>
    </member>
    <member name="M:System.Web.Optimization.Bundle.UpdateCache(System.Web.Optimization.BundleContext,System.Web.Optimization.BundleResponse)"></member>
    <member name="T:System.Web.Optimization.BundleCollection">
      <summary>Contains and manages the set of registered <see cref="T:System.Web.Optimization.Bundle" /> objects in an ASP.NET application.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleCollection" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.Add(System.Web.Optimization.Bundle)">
      <summary>Adds a bundle to the collection.</summary>
      <param name="bundle">The bundle to add.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.AddDefaultFileExtensionReplacements(System.Web.Optimization.FileExtensionReplacementList)">
      <summary>Adds the default file extension replacements for common conventions.</summary>
      <param name="list">The list to populate with default values.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.AddDefaultFileOrderings(System.Collections.Generic.IList{System.Web.Optimization.BundleFileSetOrdering})">
      <summary>Adds default file order specifications to use with bundles in the collection.</summary>
      <param name="list">The list to populate with default values.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.AddDefaultIgnorePatterns(System.Web.Optimization.IgnoreList)">
      <summary>Adds the default file ignore patterns.</summary>
      <param name="ignoreList">The ignore list to populate with default values.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.Clear">
      <summary>Removes all bundles from the collection.</summary>
    </member>
    <member name="P:System.Web.Optimization.BundleCollection.Count">
      <summary>Gets the count of registered bundles in the collection.</summary>
      <returns>The number of bundles.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleCollection.DirectoryFilter">
      <summary>Gets a list of file patterns which are ignored when including files using wildcards or substitution tokens.</summary>
      <returns>A list of file patterns.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleCollection.FileExtensionReplacementList">
      <summary>Gets the file extension replacement list.</summary>
      <returns>The file extension replacement list.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleCollection.FileSetOrderList">
      <summary>Gets a list that specifies default file orderings to use for files in the registered bundles.</summary>
      <returns>The list of file orderings.</returns>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.GetBundleFor(System.String)">
      <summary>Returns a bundle in the collection using the specified virtual path.</summary>
      <returns>The bundle for the virtual path or null if no bundle exists at the path.</returns>
      <param name="bundleVirtualPath">The virtual path of the bundle to return.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.GetEnumerator">
      <summary>Returns the bundle enumerator.</summary>
      <returns>The bundle enumerator.</returns>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.GetRegisteredBundles">
      <summary>Returns the collection of all registered bundles.</summary>
      <returns>The collection of registered bundles.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleCollection.IgnoreList">
      <summary>Gets the list of files to ignore.</summary>
      <returns>The list of files to ignore.</returns>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.Remove(System.Web.Optimization.Bundle)">
      <summary>Removes a bundle from the collection.</summary>
      <returns>true if the bundle was removed; otherwise, false.</returns>
      <param name="bundle">The bundle to remove.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.ResetAll">
      <summary>Clears the bundles and resets all the defaults.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.ResolveBundleUrl(System.String)">
      <summary>Returns the bundle URL for the specified virtual path.</summary>
      <returns>The bundle URL or null if the bundle cannot be found.</returns>
      <param name="bundleVirtualPath">The bundle virtual path.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.ResolveBundleUrl(System.String,System.Boolean)">
      <summary>Returns the bundle URL for the specified virtual path, including a content hash if requested.</summary>
      <returns>The bundle URL or null if the bundle cannot be found.</returns>
      <param name="bundleVirtualPath">The virtual path of the bundle.</param>
      <param name="includeContentHash">true to include a hash code for the content; otherwise, false. The default is true.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that can be used to iterate through the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Web.Optimization.BundleCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that can be used to iterate through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleCollection.UseCdn">
      <summary>Gets or sets whether the collection will try to use <see cref="P:System.Web.Optimization.Bundle.CdnPath" /> if specified.</summary>
      <returns>true if the collection will try to use Bundle.CdnPath if specified; Otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Optimization.BundleContext">
      <summary>Encapsulates the info needed to process a bundle request</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleContext.#ctor(System.Web.HttpContextBase,System.Web.Optimization.BundleCollection,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleContext" /> class.</summary>
      <param name="context">The context.</param>
      <param name="collection">The collection of bundles.</param>
      <param name="bundleVirtualPath">The virtual path of the bundles.</param>
    </member>
    <member name="P:System.Web.Optimization.BundleContext.BundleCollection">
      <summary>Gets or sets the collection of bundles.</summary>
      <returns>The collection of bundles.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleContext.BundleVirtualPath">
      <summary>Gets or sets the virtual path for the bundle request</summary>
      <returns>The virtual path for the bundle request.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleContext.EnableInstrumentation">
      <summary>Gets or sets whether the instrumentation output is requested.</summary>
      <returns>true if instrumentation output is requested; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleContext.EnableOptimizations">
      <summary>Gets or sets whether optimizations are enabled via <see cref="P:System.Web.Optimization.BundleTable.EnableOptimizations" />.</summary>
      <returns>true if optimizations are enabled via <see cref="P:System.Web.Optimization.BundleTable.EnableOptimizations" />; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleContext.HttpContext">
      <summary>Gets or sets the HTTP context associated with the bundle context.</summary>
      <returns>The HTTP context associated with the bundle context.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleContext.UseServerCache">
      <summary>Gets or sets whether the bindle context will store the bundle response in the HttpContext.Cache.</summary>
      <returns>true if the bindle context will store the bundle response in the cache; Otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Optimization.BundleDefinition">
      <summary>Represents a bundle definition as specified by the bundle manifest.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleDefinition" /> class.</summary>
    </member>
    <member name="P:System.Web.Optimization.BundleDefinition.CdnFallbackExpression">
      <summary>Gets or sets the CDN fallback expression for the bundle.</summary>
      <returns>The CDN fallback expression for the bundle.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleDefinition.CdnPath">
      <summary>Gets or sets the CDN path for the bundle.</summary>
      <returns>The CDN path for the bundle.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleDefinition.Includes">
      <summary>Gets the files included in the bundle.</summary>
      <returns>The files included in the bundle.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleDefinition.Path">
      <summary>Gets or sets the virtual path for the bundle.</summary>
      <returns>The virtual path for the bundle.</returns>
    </member>
    <member name="T:System.Web.Optimization.BundleFile"></member>
    <member name="M:System.Web.Optimization.BundleFile.#ctor(System.String,System.Web.Hosting.VirtualFile)"></member>
    <member name="M:System.Web.Optimization.BundleFile.#ctor(System.String,System.Web.Hosting.VirtualFile,System.Collections.Generic.IList{System.Web.Optimization.IItemTransform})"></member>
    <member name="M:System.Web.Optimization.BundleFile.ApplyTransforms"></member>
    <member name="P:System.Web.Optimization.BundleFile.IncludedVirtualPath"></member>
    <member name="P:System.Web.Optimization.BundleFile.Transforms"></member>
    <member name="P:System.Web.Optimization.BundleFile.VirtualFile"></member>
    <member name="T:System.Web.Optimization.BundleFileSetOrdering">
      <summary>Encapsulates a named set of files with relative orderings, for example jquery or modernizer.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleFileSetOrdering.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleFileSetOrdering" /> class.</summary>
      <param name="name">The name used to help identify the file ordering.</param>
    </member>
    <member name="P:System.Web.Optimization.BundleFileSetOrdering.Files">
      <summary>Gets or sets the ordered list of file name patterns (allows one prefix/suffix wildcard '*') that determines the relative ordering of these files in the bundle. For example, ["z.js", "b*", "*a", "a.js"].</summary>
      <returns>The ordered list of file name patterns that determines the relative ordering of these files in the bundle.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleFileSetOrdering.Name">
      <summary> Gets or sets the name used to help identify the file ordering, for example, jquery. </summary>
      <returns>The name used to help identify the file ordering.</returns>
    </member>
    <member name="T:System.Web.Optimization.BundleManifest">
      <summary> Represents the XML configuration to configure the <see cref="P:System.Web.Optimization.BundleTable.Bundles" /> bundle collection. </summary>
    </member>
    <member name="P:System.Web.Optimization.BundleManifest.BundleManifestPath">
      <summary>Gets or sets the path to the bundle manifest file that sets up the <see cref="T:System.Web.Optimization.BundleCollection" />.</summary>
      <returns>The path to the bundle manifest file that sets up the <see cref="T:System.Web.Optimization.BundleCollection" />.</returns>
    </member>
    <member name="M:System.Web.Optimization.BundleManifest.ReadBundleManifest">
      <summary>Reads the bundle manifest using the default bundle configuration.</summary>
      <returns>The bundle manifest.</returns>
    </member>
    <member name="M:System.Web.Optimization.BundleManifest.ReadBundleManifest(System.IO.Stream)">
      <summary> Reads the bundle manifest from a given stream. </summary>
      <returns>The bundle manifest.</returns>
      <param name="bundleStream">The bundle stream to read from.</param>
    </member>
    <member name="P:System.Web.Optimization.BundleManifest.ScriptBundles">
      <summary>Gets the <see cref="T:System.Web.Optimization.ScriptBundle" /> objects specified by the manifest file.</summary>
      <returns>The <see cref="T:System.Web.Optimization.ScriptBundle" /> objects specified by the manifest file.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleManifest.StyleBundles">
      <summary>Gets or sets the registered style bundles. </summary>
      <returns>The registered style bundles.</returns>
    </member>
    <member name="T:System.Web.Optimization.BundleModule">
      <summary> Represents a module that enables bundling to intercept requests to bundle URLs. </summary>
    </member>
    <member name="M:System.Web.Optimization.BundleModule.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleModule" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleModule.Dispose">
      <summary> Disposes any resources used by the <see cref="T:System.Web.Optimization.BundleModule" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleModule.Init(System.Web.HttpApplication)">
      <summary> Hooks the OnApplicationPostResolveRequestCache event to remap to the bundle handler. </summary>
      <param name="application">The application that will receive the registration of the event.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleModule.System#Web#IHttpModule#Dispose">
      <summary>Calls the Dispose() method.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleModule.System#Web#IHttpModule#Init(System.Web.HttpApplication)">
      <summary>Calls the Init method.</summary>
      <param name="application">The application that will receive the registration of the event.</param>
    </member>
    <member name="T:System.Web.Optimization.BundleResolver">
      <summary>Represents a class that determine if a script reference is a bundle, and what it contains to prevent duplicate script references.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleResolver.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleResolver" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleResolver.#ctor(System.Web.Optimization.BundleCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleResolver" /> class with the specified bundle.</summary>
      <param name="bundles">The bundles of objects.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleResolver.#ctor(System.Web.Optimization.BundleCollection,System.Web.HttpContextBase)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleResolver" /> class with the specified bundle and context.</summary>
      <param name="bundles">The bundles of object.</param>
      <param name="context">The HttpContextBase.</param>
    </member>
    <member name="P:System.Web.Optimization.BundleResolver.Current">
      <summary>Gets or sets the ScriptManager that reflects against <see cref="P:System.Web.Optimization.BundleResolver.Current" />.</summary>
      <returns>The ScriptManager that reflects against <see cref="P:System.Web.Optimization.BundleResolver.Current" />.</returns>
    </member>
    <member name="M:System.Web.Optimization.BundleResolver.GetBundleContents(System.String)">
      <summary>Returns an enumeration of actual file paths to the contents of the bundle.</summary>
      <returns>The actual file paths to the contents of the bundle.</returns>
      <param name="virtualPath">The virtual file path.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleResolver.GetBundleUrl(System.String)">
      <summary>Gets the versioned url for the bundle or returns the virtualPath unchanged if it does not point to a bundle.</summary>
      <returns>The versioned url for the bundle.</returns>
      <param name="virtualPath">The virtual file path.</param>
    </member>
    <member name="M:System.Web.Optimization.BundleResolver.IsBundleVirtualPath(System.String)">
      <summary>Determines if the virtualPath is to a bundle.</summary>
      <returns>The virtualPath.</returns>
      <param name="virtualPath">The virtual file path.</param>
    </member>
    <member name="T:System.Web.Optimization.BundleResponse">
      <summary>Encapsulates the response data that will be sent for a bundle request.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleResponse.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.BundleResponse" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.BundleResponse.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="P:System.Web.Optimization.BundleResponse.Cacheability">
      <summary>Gets or sets a value that is used to set the Cache-Control HTTP header.</summary>
      <returns>A value that is used to set the Cache-Control HTTP header.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleResponse.Content">
      <summary>Gets or sets the content of the bundle which is sent as the response body.</summary>
      <returns>The content of the bundle.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleResponse.ContentType">
      <summary>Gets or sets the media type that is sent in the HTTP content/type header.</summary>
      <returns>The media type that is sent in the HTTP content/type header.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleResponse.Files">
      <summary>Gets or sets the list of files in the bundle.</summary>
      <returns>The list of files in the bundle.</returns>
    </member>
    <member name="T:System.Web.Optimization.BundleTable">
      <summary> Static holder class for the default bundle collection. </summary>
    </member>
    <member name="P:System.Web.Optimization.BundleTable.Bundles">
      <summary>Gets the default bundle collection. </summary>
      <returns>The default bundle collection.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleTable.EnableOptimizations">
      <summary>Gets or sets whether bundling and minification of bundle references is enabled.</summary>
      <returns>true if bundling and minification of bundle references is enabled; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Optimization.BundleTable.VirtualPathProvider">
      <summary>Gets or sets the provider to be used in resolving bundle files.</summary>
      <returns>The provider to be used in resolving bundle files.</returns>
    </member>
    <member name="T:System.Web.Optimization.CssMinify">
      <summary> Represents a <see cref="T:System.Web.Optimization.IBundleTransform" /> that does CSS minification.</summary>
    </member>
    <member name="M:System.Web.Optimization.CssMinify.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.CssMinify" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.CssMinify.Process(System.Web.Optimization.BundleContext,System.Web.Optimization.BundleResponse)">
      <summary> Transforms the bundle contents by applying CSS minification. </summary>
      <param name="context">The bundle context.</param>
      <param name="response">The bundle response object</param>
    </member>
    <member name="T:System.Web.Optimization.CssRewriteUrlTransform"></member>
    <member name="M:System.Web.Optimization.CssRewriteUrlTransform.#ctor"></member>
    <member name="M:System.Web.Optimization.CssRewriteUrlTransform.Process(System.String,System.String)"></member>
    <member name="T:System.Web.Optimization.DefaultBundleBuilder">
      <summary>Represents the default logic which combines files in the bundle.</summary>
    </member>
    <member name="M:System.Web.Optimization.DefaultBundleBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.DefaultBundleBuilder" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.DefaultBundleBuilder.BuildBundleContent(System.Web.Optimization.Bundle,System.Web.Optimization.BundleContext,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="T:System.Web.Optimization.DefaultBundleOrderer">
      <summary>Default <see cref="T:System.Web.Optimization.IBundleOrderer" /> which orders files in a bundled using <see cref="P:System.Web.Optimization.BundleCollection.FileSetOrderList" />.</summary>
    </member>
    <member name="M:System.Web.Optimization.DefaultBundleOrderer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.DefaultBundleOrderer" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.DefaultBundleOrderer.OrderFiles(System.Web.Optimization.BundleContext,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="T:System.Web.Optimization.DynamicFolderBundle">
      <summary>Represents a <see cref="T:System.Web.Optimization.Bundle" /> object that ASP.NET creates from a folder that contains files of the same type.</summary>
    </member>
    <member name="M:System.Web.Optimization.DynamicFolderBundle.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.DynamicFolderBundle" /> class.</summary>
      <param name="pathSuffix">The path suffix.</param>
      <param name="searchPattern">The search pattern.</param>
    </member>
    <member name="M:System.Web.Optimization.DynamicFolderBundle.#ctor(System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.DynamicFolderBundle" /> class.</summary>
      <param name="pathSuffix">The path suffix.</param>
      <param name="searchPattern">The search pattern.</param>
      <param name="searchSubdirectories">The search subdirectories.</param>
    </member>
    <member name="M:System.Web.Optimization.DynamicFolderBundle.#ctor(System.String,System.String,System.Boolean,System.Web.Optimization.IBundleTransform[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.DynamicFolderBundle" /> class.</summary>
      <param name="pathSuffix">The path suffix.</param>
      <param name="searchPattern">The search pattern.</param>
      <param name="searchSubdirectories">The search subdirectories.</param>
      <param name="transforms">The transform parameter.</param>
    </member>
    <member name="M:System.Web.Optimization.DynamicFolderBundle.#ctor(System.String,System.String,System.Web.Optimization.IBundleTransform[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.DynamicFolderBundle" /> class.</summary>
      <param name="pathSuffix">The path suffix.</param>
      <param name="searchPattern">The search pattern.</param>
      <param name="transforms">The transform parameter.</param>
    </member>
    <member name="P:System.Web.Optimization.DynamicFolderBundle.CdnPath">
      <summary>Gets or set the path of a Content Delivery Network (CDN) that contains the folder bundle.</summary>
      <returns>The path of a Content Delivery Network (CDN)</returns>
    </member>
    <member name="M:System.Web.Optimization.DynamicFolderBundle.EnumerateFiles(System.Web.Optimization.BundleContext)">
      <summary>Returns all the base methods files and any dynamic files found in the requested directory.</summary>
      <returns>All the base methods files and any dynamic files found in the requested directory.</returns>
      <param name="context">The bundle context.</param>
    </member>
    <member name="P:System.Web.Optimization.DynamicFolderBundle.SearchPattern">
      <summary>Gets or sets the search pattern for the folder bundle.</summary>
      <returns>The search pattern for the folder bundle.</returns>
    </member>
    <member name="P:System.Web.Optimization.DynamicFolderBundle.SearchSubdirectories">
      <summary>Gets or sets whether the search pattern is applied to subdirectories.</summary>
      <returns>true if the search pattern is applied to subdirectories; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Optimization.FileExtensionReplacementList">
      <summary>A set of file extensions that will be used to select different files based on the <see cref="T:System.Web.Optimization.OptimizationMode" />.</summary>
    </member>
    <member name="M:System.Web.Optimization.FileExtensionReplacementList.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.FileExtensionReplacementList" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.FileExtensionReplacementList.Add(System.String)">
      <summary>Adds a file extension which will be applied regardless of <see cref="T:System.Web.Optimization.OptimizationMode" />.</summary>
      <param name="extension">File extension string.</param>
    </member>
    <member name="M:System.Web.Optimization.FileExtensionReplacementList.Add(System.String,System.Web.Optimization.OptimizationMode)">
      <summary>Add a file extension for a specified <see cref="T:System.Web.Optimization.OptimizationMode" />.</summary>
      <param name="extension">File extension string.</param>
      <param name="mode">
        <see cref="T:System.Web.Optimization.OptimizationMode" /> in which to apply the file extension replacement.</param>
    </member>
    <member name="M:System.Web.Optimization.FileExtensionReplacementList.Clear">
      <summary>Clears file extension replacements.</summary>
    </member>
    <member name="M:System.Web.Optimization.FileExtensionReplacementList.ReplaceFileExtensions(System.Web.Optimization.BundleContext,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="T:System.Web.Optimization.IBundleBuilder">
      <summary>Specifies the building of the bundle from the individual file contents.</summary>
    </member>
    <member name="M:System.Web.Optimization.IBundleBuilder.BuildBundleContent(System.Web.Optimization.Bundle,System.Web.Optimization.BundleContext,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="T:System.Web.Optimization.IBundleOrderer">
      <summary>Defines methods for ordering files within a <see cref="T:System.Web.Optimization.Bundle" />.</summary>
    </member>
    <member name="M:System.Web.Optimization.IBundleOrderer.OrderFiles(System.Web.Optimization.BundleContext,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="T:System.Web.Optimization.IBundleResolver">
      <summary>Represents an interface used to query the BundleCollection for metadata.</summary>
    </member>
    <member name="M:System.Web.Optimization.IBundleResolver.GetBundleContents(System.String)">
      <summary>Returns a list of all the virtualPaths of the contents of the bundle.</summary>
      <returns>The list of virtual path.</returns>
      <param name="virtualPath">The virtual path for the bundle.</param>
    </member>
    <member name="M:System.Web.Optimization.IBundleResolver.GetBundleUrl(System.String)">
      <summary>Returns the versioned URL of the bundle.</summary>
      <returns>The versioned URL of the bundle.</returns>
      <param name="virtualPath">The virtual path.</param>
    </member>
    <member name="M:System.Web.Optimization.IBundleResolver.IsBundleVirtualPath(System.String)">
      <summary>Specifies whether the virtual path is to a bundle.</summary>
      <returns>true if the virtual path is to a bundle; Otherwise, false.</returns>
      <param name="virtualPath">The virtual path.</param>
    </member>
    <member name="T:System.Web.Optimization.IBundleTransform">
      <summary>Defines a method that transforms the files in a <see cref="T:System.Web.Optimization.BundleResponse" /> object.</summary>
    </member>
    <member name="M:System.Web.Optimization.IBundleTransform.Process(System.Web.Optimization.BundleContext,System.Web.Optimization.BundleResponse)">
      <summary>Transforms the content in the <see cref="T:System.Web.Optimization.BundleResponse" /> object.</summary>
      <param name="context">The bundle context.</param>
      <param name="response">The bundle response.</param>
    </member>
    <member name="T:System.Web.Optimization.IgnoreList">
      <summary>A list of filename patterns to be ignored and thereby excluded from bundles.</summary>
    </member>
    <member name="M:System.Web.Optimization.IgnoreList.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.IgnoreList" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.IgnoreList.Clear">
      <summary>Clears entire ignore list.</summary>
    </member>
    <member name="M:System.Web.Optimization.IgnoreList.FilterIgnoredFiles(System.Web.Optimization.BundleContext,System.Collections.Generic.IEnumerable{System.Web.Optimization.BundleFile})"></member>
    <member name="M:System.Web.Optimization.IgnoreList.Ignore(System.String)">
      <summary>Ignores the specified pattern regardless of the value set in <see cref="P:System.Web.Optimization.BundleTable.EnableOptimizations" />.</summary>
      <param name="item">The ignore pattern.</param>
    </member>
    <member name="M:System.Web.Optimization.IgnoreList.Ignore(System.String,System.Web.Optimization.OptimizationMode)">
      <summary>Ignores the specified pattern when in the appropriate <see cref="T:System.Web.Optimization.OptimizationMode" />.</summary>
      <param name="pattern">The ignore pattern.</param>
      <param name="mode">The <see cref="T:System.Web.Optimization.OptimizationMode" /> in which to apply the ignore pattern.</param>
    </member>
    <member name="M:System.Web.Optimization.IgnoreList.ShouldIgnore(System.Web.Optimization.BundleContext,System.String)">
      <summary>Determines whether a file should be ignored based on the ignore list.</summary>
      <returns>true if the filename matches a pattern in the <see cref="T:System.Web.Optimization.IgnoreList" />; otherwise, false.</returns>
      <param name="context">The <see cref="T:System.Web.Optimization.BundleContext" /> object that contains state for both the framework configuration and the HTTP request.</param>
      <param name="fileName">The name of the file to compare with the ignore list.</param>
    </member>
    <member name="T:System.Web.Optimization.IItemTransform"></member>
    <member name="M:System.Web.Optimization.IItemTransform.Process(System.String,System.String)"></member>
    <member name="T:System.Web.Optimization.JsMinify">
      <summary>Represents a BundleTransform that does CSS Minification.</summary>
    </member>
    <member name="M:System.Web.Optimization.JsMinify.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.JsMinify" /> class.</summary>
    </member>
    <member name="M:System.Web.Optimization.JsMinify.Process(System.Web.Optimization.BundleContext,System.Web.Optimization.BundleResponse)">
      <summary>Transforms the bundle contents by applying javascript minification.</summary>
      <param name="context">The context associated with the bundle.</param>
      <param name="response">The <see cref="T:System.Web.Optimization.BundleResponse" />.</param>
    </member>
    <member name="T:System.Web.Optimization.OptimizationMode">
      <summary> OptimizationMode used by IgnoreList and FileExtensionReplacement. </summary>
    </member>
    <member name="F:System.Web.Optimization.OptimizationMode.Always">
      <summary> Always: Always ignore </summary>
    </member>
    <member name="F:System.Web.Optimization.OptimizationMode.WhenDisabled">
      <summary> WhenDisabled: Only when BundleTable.EnableOptimization = false </summary>
    </member>
    <member name="F:System.Web.Optimization.OptimizationMode.WhenEnabled">
      <summary> WhenEnabled: Only when BundleTable.EnableOptimization = true </summary>
    </member>
    <member name="T:System.Web.Optimization.OptimizationSettings">
      <summary>Configuration settings used by the <see cref="T:System.Web.Optimization.Optimizer" /> class to generate bundle responses outside of ASP.NET applications.</summary>
    </member>
    <member name="M:System.Web.Optimization.OptimizationSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.OptimizationSettings" /> class.</summary>
    </member>
    <member name="P:System.Web.Optimization.OptimizationSettings.ApplicationPath">
      <summary>The physical file path to resolve the ‘~’ token in virtual paths.</summary>
      <returns>The physical file path.</returns>
    </member>
    <member name="P:System.Web.Optimization.OptimizationSettings.BundleManifestPath">
      <summary>The path to the bundle manifest file that sets up the <see cref="T:System.Web.Optimization.BundleCollection" />.</summary>
      <returns>The path to the bundle manifest file that sets up the <see cref="T:System.Web.Optimization.BundleCollection" />.</returns>
    </member>
    <member name="P:System.Web.Optimization.OptimizationSettings.BundleSetupMethod">
      <summary>Gets or sets a callback function which is invoked after the bundle manifest is loaded to allow further customization of the bundle collection.</summary>
      <returns>A callback function which is invoked after the bundle manifest is loaded to allow further customization of the bundle collection.</returns>
    </member>
    <member name="P:System.Web.Optimization.OptimizationSettings.BundleTable"></member>
    <member name="T:System.Web.Optimization.Optimizer">
      <summary>Represents a standalone class for generating bundle responses outside of ASP.NET</summary>
    </member>
    <member name="M:System.Web.Optimization.Optimizer.BuildAllBundles(System.Web.Optimization.OptimizationSettings)"></member>
    <member name="M:System.Web.Optimization.Optimizer.BuildBundle(System.String,System.Web.Optimization.OptimizationSettings)">
      <summary>Builds a <see cref="T:System.Web.Optimization.BundleResponse" /> object from the declarations found in a bundle manifest file.</summary>
      <returns>The bundle response for specified <paramref name="bundlePath" />.</returns>
      <param name="bundlePath">The path to the bundle being requested.</param>
      <param name="settings">An <see cref="T:System.Web.Optimization.OptimizationSettings" /> object containing configuration settings for optimization.</param>
    </member>
    <member name="T:System.Web.Optimization.PreApplicationStartCode">
      <summary> Hooks up the BundleModule </summary>
    </member>
    <member name="M:System.Web.Optimization.PreApplicationStartCode.Start">
      <summary> Hooks up the BundleModule </summary>
    </member>
    <member name="T:System.Web.Optimization.ScriptBundle">
      <summary>Represents a bundle that does Js Minification.</summary>
    </member>
    <member name="M:System.Web.Optimization.ScriptBundle.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.ScriptBundle" /> class that takes a virtual path for the bundle.</summary>
      <param name="virtualPath">The virtual path for the bundle.</param>
    </member>
    <member name="M:System.Web.Optimization.ScriptBundle.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.ScriptBundle" /> class that takes virtual path and cdnPath for the bundle.</summary>
      <param name="virtualPath">The virtual path for the bundle.</param>
      <param name="cdnPath">The path of a Content Delivery Network (CDN).</param>
    </member>
    <member name="T:System.Web.Optimization.Scripts">
      <summary>Represents a type that allows queuing and rendering script elements.</summary>
    </member>
    <member name="P:System.Web.Optimization.Scripts.DefaultTagFormat">
      <summary>Gets or sets the default format string for defining how script tags are rendered.</summary>
      <returns>The default format string for defining how script tags are rendered.</returns>
    </member>
    <member name="M:System.Web.Optimization.Scripts.Render(System.String[])">
      <summary>Renders script tags for the following paths.</summary>
      <returns>The HTML string containing the script tag or tags for the bundle.</returns>
      <param name="paths">Set of virtual paths for which to generate script tags.</param>
    </member>
    <member name="M:System.Web.Optimization.Scripts.RenderFormat(System.String,System.String[])">
      <summary>Renders script tags for a set of paths based on a format string.</summary>
      <returns>The HTML string containing the script tag or tags for the bundle.</returns>
      <param name="tagFormat">The format string for defining the rendered script tags.</param>
      <param name="paths">Set of virtual paths for which to generate script tags.</param>
    </member>
    <member name="M:System.Web.Optimization.Scripts.Url(System.String)">
      <summary>Returns a fingerprinted URL if the <paramref name="virtualPath" /> is to a bundle, otherwise returns the resolve URL.</summary>
      <returns>A <see cref="T:System.Web.IHtmlString" /> that represents the URL.</returns>
      <param name="virtualPath">The virtual path.</param>
    </member>
    <member name="T:System.Web.Optimization.StyleBundle">
      <summary> Represents a bundle that does CSS minification. </summary>
    </member>
    <member name="M:System.Web.Optimization.StyleBundle.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.StyleBundle" /> class with a virtual path for the bundle. </summary>
      <param name="virtualPath">A virtual path for the bundle.</param>
    </member>
    <member name="M:System.Web.Optimization.StyleBundle.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Optimization.StyleBundle" /> class with virtual path and CDN path for the bundle. </summary>
      <param name="virtualPath">A virtual path for the bundle.</param>
      <param name="cdnPath">A CDN path for the bundle.</param>
    </member>
    <member name="T:System.Web.Optimization.Styles">
      <summary>Represents a helper class for rendering link elements.</summary>
    </member>
    <member name="P:System.Web.Optimization.Styles.DefaultTagFormat">
      <summary>Gets or sets the default format string for defining how link tags are rendered.</summary>
      <returns>The default format string for defining how link tags are rendered.</returns>
    </member>
    <member name="M:System.Web.Optimization.Styles.Render(System.String[])">
      <summary>Renders link tags for a set of paths.</summary>
      <returns>A HTML string containing the link tag or tags for the bundle.</returns>
      <param name="paths">Set of virtual paths for which to generate link tags.</param>
    </member>
    <member name="M:System.Web.Optimization.Styles.RenderFormat(System.String,System.String[])">
      <summary>Renders link tags for a set of paths based on a format string.</summary>
      <returns>A HTML string containing the link tag or tags for the bundle.</returns>
      <param name="tagFormat">Format string for defining the rendered link tags.</param>
      <param name="paths">Set of virtual paths for which to generate link tags.</param>
    </member>
    <member name="M:System.Web.Optimization.Styles.Url(System.String)">
      <summary>Generates a version-stamped URL for a bundle.</summary>
      <returns>A fingerprinted URL.</returns>
      <param name="virtualPath">The virtual file path.</param>
    </member>
  </members>
</doc>