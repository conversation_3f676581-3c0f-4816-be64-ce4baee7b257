﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------



public partial class Home_EDIBilling_ManageAccounting
{

    /// <summary>
    /// RadCodeBlock1 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadCodeBlock RadCodeBlock1;

    /// <summary>
    /// RadAjaxManagerFilters control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadAjaxManager RadAjaxManagerFilters;

    /// <summary>
    /// RadWindowManager1 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadWindowManager RadWindowManager1;

    /// <summary>
    /// RadWindowRemittance control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadWindow RadWindowRemittance;

    /// <summary>
    /// LoadingPanel1 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadAjaxLoadingPanel LoadingPanel1;

    /// <summary>
    /// panFilters control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel panFilters;

    /// <summary>
    /// filterDIV control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.HtmlControls.HtmlGenericControl filterDIV;

    /// <summary>
    /// RadGridAccountingPeriod control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadGrid RadGridAccountingPeriod;

    /// <summary>
    /// SQLDataSourceAccountingPeriod control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.SqlDataSource SQLDataSourceAccountingPeriod;

    /// <summary>
    /// searchLabel control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label searchLabel;

    /// <summary>
    /// RadSearchTextBoxQuery control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadSearchBox RadSearchTextBoxQuery;

    /// <summary>
    /// RadDatePickerFromDate control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadDatePicker RadDatePickerFromDate;

    /// <summary>
    /// DateInput1 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadDateInput DateInput1;

    /// <summary>
    /// RadDatePickerToDate control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadDatePicker RadDatePickerToDate;

    /// <summary>
    /// DateInput2 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadDateInput DateInput2;

    /// <summary>
    /// lblConsumerStatus control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblConsumerStatus;

    /// <summary>
    /// RadComboBoxConsumerStatus control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadComboBox RadComboBoxConsumerStatus;

    /// <summary>
    /// lblConsumerDropdown control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label lblConsumerDropdown;

    /// <summary>
    /// RadComboBoxConsumer control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadComboBox RadComboBoxConsumer;

    /// <summary>
    /// sqlConsumers control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.SqlDataSource sqlConsumers;

    /// <summary>
    /// DateFilterMode control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label DateFilterMode;

    /// <summary>
    /// RadComboBoxARFilterMode control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadComboBox RadComboBoxARFilterMode;

    /// <summary>
    /// PaymentApplicationMode control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label PaymentApplicationMode;

    /// <summary>
    /// RadCheckBoxPaymentApplicationMode control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadComboBox RadCheckBoxPaymentApplicationMode;

    /// <summary>
    /// btnApplyFilters control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadButton btnApplyFilters;

    /// <summary>
    /// RadGridAccountsReceivable control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadGrid RadGridAccountsReceivable;

    /// <summary>
    /// sqlClientConsumers control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.SqlDataSource sqlClientConsumers;

    /// <summary>
    /// HiddenCURRENTWINDOWUSERID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCURRENTWINDOWUSERID;

    /// <summary>
    /// HiddenCURRENTWINDOWCLIENTID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCURRENTWINDOWCLIENTID;

    /// <summary>
    /// HiddenBATCHID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenBATCHID;

    /// <summary>
    /// HiddenCLAIMID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCLAIMID;

    /// <summary>
    /// Master property.
    /// </summary>
    /// <remarks>
    /// Auto-generated property.
    /// </remarks>
    public new MasterPlain2 Master
    {
        get
        {
            return ((MasterPlain2)(base.Master));
        }
    }
}
