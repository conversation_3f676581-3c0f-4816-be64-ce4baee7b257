<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNet.Identity.Owin</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions">
            <summary>
                Extension methods for SignInManager/>
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.CreateUserIdentity``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1},``0)">
            <summary>
            Called to generate the ClaimsIdentity for the user, override to add additional claims before SignIn
            </summary>
            <param name="manager"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.SignIn``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1},``0,System.Boolean,System.Boolean)">
            <summary>
            Creates a user identity and then signs the identity using the AuthenticationManager
            </summary>
            <param name="manager"></param>
            <param name="user"></param>
            <param name="isPersistent"></param>
            <param name="rememberBrowser"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.SendTwoFactorCode``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1},System.String)">
            <summary>
            Send a two factor code to a user
            </summary>
            <param name="manager"></param>
            <param name="provider"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.GetVerifiedUserId``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1})">
            <summary>
            Get the user id that has been verified already or null.
            </summary>
            <param name="manager"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.HasBeenVerified``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1})">
            <summary>
            Has the user been verified (ie either via password or external login)
            </summary>
            <param name="manager"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.TwoFactorSignIn``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1},System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Two factor verification step
            </summary>
            <param name="manager"></param>
            <param name="provider"></param>
            <param name="code"></param>
            <param name="isPersistent"></param>
            <param name="rememberBrowser"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.ExternalSignIn``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1},Microsoft.AspNet.Identity.Owin.ExternalLoginInfo,System.Boolean)">
            <summary>
            Sign the user in using an associated external login
            </summary>
            <param name="manager"></param>
            <param name="loginInfo"></param>
            <param name="isPersistent"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManagerExtensions.PasswordSignIn``2(Microsoft.AspNet.Identity.Owin.SignInManager{``0,``1},System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Sign in the user in using the user name and password
            </summary>
            <param name="manager"></param>
            <param name="userName"></param>
            <param name="password"></param>
            <param name="isPersistent"></param>
            <param name="shouldLockout"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.IdentityFactoryMiddleware`2">
            <summary>
                OwinMiddleware that initializes an object for use in the OwinContext via the Get/Set generic extensions method
            </summary>
            <typeparam name="TResult"></typeparam>
            <typeparam name="TOptions"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.IdentityFactoryMiddleware`2.#ctor(Microsoft.Owin.OwinMiddleware,`1)">
            <summary>
                Constructor
            </summary>
            <param name="next">The next middleware in the OWIN pipeline to invoke</param>
            <param name="options">Configuration options for the middleware</param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.IdentityFactoryMiddleware`2.Invoke(Microsoft.Owin.IOwinContext)">
            <summary>
                Create an object using the Options.Provider, storing it in the OwinContext and then disposes the object when finished
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.IdentityFactoryMiddleware`2.Options">
            <summary>
                Configuration options
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions`1">
            <summary>
                Configuration options for a IdentityFactoryMiddleware
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions`1.DataProtectionProvider">
            <summary>
                Used to configure the data protection provider
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions`1.Provider">
            <summary>
                Provider used to Create and Dispose objects
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.IIdentityFactoryProvider`1">
            <summary>
                Interface used to create objects per request
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.IIdentityFactoryProvider`1.Create(Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions{`0},Microsoft.Owin.IOwinContext)">
            <summary>
                Called once per request to create an object
            </summary>
            <param name="options"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.IIdentityFactoryProvider`1.Dispose(Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions{`0},`0)">
            <summary>
                Called at the end of the request to dispose the object created
            </summary>
            <param name="options"></param>
            <param name="instance"></param>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.IdentityFactoryProvider`1">
            <summary>
                Used to configure how the IdentityFactoryMiddleware will create an instance of the specified type for each OwinContext
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.IdentityFactoryProvider`1.#ctor">
            <summary>
                Constructor
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.IdentityFactoryProvider`1.Create(Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions{`0},Microsoft.Owin.IOwinContext)">
            <summary>
                Calls the OnCreate Delegate
            </summary>
            <param name="options"></param>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.IdentityFactoryProvider`1.Dispose(Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions{`0},`0)">
            <summary>
                Calls the OnDispose delegate
            </summary>
            <param name="options"></param>
            <param name="instance"></param>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.IdentityFactoryProvider`1.OnCreate">
            <summary>
                A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.IdentityFactoryProvider`1.OnDispose">
            <summary>
                A delegate assigned to this property will be invoked when the related method is called
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`1">
            <summary>
                Token provider that uses an IDataProtector to generate encrypted tokens based off of the security stamp
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2">
            <summary>
                Token provider that uses an IDataProtector to generate encrypted tokens based off of the security stamp
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2.#ctor(Microsoft.Owin.Security.DataProtection.IDataProtector)">
            <summary>
                Constructor
            </summary>
            <param name="protector"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2.GenerateAsync(System.String,Microsoft.AspNet.Identity.UserManager{`0,`1},`0)">
            <summary>
                Generate a protected string for a user
            </summary>
            <param name="purpose"></param>
            <param name="manager"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2.ValidateAsync(System.String,System.String,Microsoft.AspNet.Identity.UserManager{`0,`1},`0)">
            <summary>
                Return false if the token is not valid
            </summary>
            <param name="purpose"></param>
            <param name="token"></param>
            <param name="manager"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2.IsValidProviderForUserAsync(Microsoft.AspNet.Identity.UserManager{`0,`1},`0)">
            <summary>
                Returns true if the provider can be used to generate tokens for this user
            </summary>
            <param name="manager"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2.NotifyAsync(System.String,Microsoft.AspNet.Identity.UserManager{`0,`1},`0)">
            <summary>
                This provider no-ops by default when asked to notify a user
            </summary>
            <param name="token"></param>
            <param name="manager"></param>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2.Protector">
            <summary>
                IDataProtector for the token
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`2.TokenLifespan">
            <summary>
                Lifespan after which the token is considered expired
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.DataProtectorTokenProvider`1.#ctor(Microsoft.Owin.Security.DataProtection.IDataProtector)">
            <summary>
                Constructor
            </summary>
            <param name="protector"></param>
        </member>
        <member name="T:Owin.AppBuilderExtensions">
            <summary>
                Extensions off of IAppBuilder to make it easier to configure the SignInCookies
            </summary>
        </member>
        <member name="M:Owin.AppBuilderExtensions.CreatePerOwinContext``1(Owin.IAppBuilder,System.Func{``0})">
            <summary>
                Registers a callback that will be invoked to create an instance of type T that will be stored in the OwinContext
                which can fetched via context.Get
            </summary>
            <typeparam name="T"></typeparam>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="createCallback">Invoked to create an instance of T</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
        <member name="M:Owin.AppBuilderExtensions.CreatePerOwinContext``1(Owin.IAppBuilder,System.Func{Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions{``0},Microsoft.Owin.IOwinContext,``0})">
            <summary>
                Registers a callback that will be invoked to create an instance of type T that will be stored in the OwinContext
                which can fetched via context.Get
            </summary>
            <typeparam name="T"></typeparam>
            <param name="app"></param>
            <param name="createCallback"></param>
            <returns></returns>
        </member>
        <member name="M:Owin.AppBuilderExtensions.CreatePerOwinContext``1(Owin.IAppBuilder,System.Func{Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions{``0},Microsoft.Owin.IOwinContext,``0},System.Action{Microsoft.AspNet.Identity.Owin.IdentityFactoryOptions{``0},``0})">
            <summary>
                Registers a callback that will be invoked to create an instance of type T that will be stored in the OwinContext
                which can fetched via context.Get
            </summary>
            <typeparam name="T"></typeparam>
            <param name="app"></param>
            <param name="createCallback"></param>
            <param name="disposeCallback"></param>
            <returns></returns>
        </member>
        <member name="M:Owin.AppBuilderExtensions.UseExternalSignInCookie(Owin.IAppBuilder)">
            <summary>
                Configure the app to use owin middleware based cookie authentication for external identities
            </summary>
            <param name="app"></param>
        </member>
        <member name="M:Owin.AppBuilderExtensions.UseExternalSignInCookie(Owin.IAppBuilder,System.String)">
            <summary>
                Configure the app to use owin middleware based cookie authentication for external identities
            </summary>
            <param name="app"></param>
            <param name="externalAuthenticationType"></param>
        </member>
        <member name="M:Owin.AppBuilderExtensions.UseTwoFactorSignInCookie(Owin.IAppBuilder,System.String,System.TimeSpan)">
            <summary>
                Configures a cookie intended to be used to store the partial credentials for two factor authentication
            </summary>
            <param name="app"></param>
            <param name="authenticationType"></param>
            <param name="expires"></param>
        </member>
        <member name="M:Owin.AppBuilderExtensions.UseTwoFactorRememberBrowserCookie(Owin.IAppBuilder,System.String)">
            <summary>
                Configures a cookie intended to be used to store whether two factor authentication has been done already
            </summary>
            <param name="app"></param>
            <param name="authenticationType"></param>
        </member>
        <member name="M:Owin.AppBuilderExtensions.UseOAuthBearerTokens(Owin.IAppBuilder,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions)">
            <summary>
                Configure the app to use owin middleware based oauth bearer tokens
            </summary>
            <param name="app"></param>
            <param name="options"></param>
        </member>
        <member name="T:Microsoft.Owin.Security.AuthenticationManagerExtensions">
            <summary>
                Extensions methods on IAuthenticationManager that add methods for using the default Application and External
                authentication type constants
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.GetExternalAuthenticationTypes(Microsoft.Owin.Security.IAuthenticationManager)">
            <summary>
                Return the authentication types which are considered external because they have captions
            </summary>
            <param name="manager"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.GetExternalIdentityAsync(Microsoft.Owin.Security.IAuthenticationManager,System.String)">
            <summary>
                Return the identity associated with the default external authentication type
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.GetExternalIdentity(Microsoft.Owin.Security.IAuthenticationManager,System.String)">
            <summary>
            Return the identity associated with the default external authentication type
            </summary>
            <param name="manager"></param>
            <param name="externalAuthenticationType"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.GetExternalLoginInfoAsync(Microsoft.Owin.Security.IAuthenticationManager)">
            <summary>
                Extracts login info out of an external identity
            </summary>
            <param name="manager"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.GetExternalLoginInfo(Microsoft.Owin.Security.IAuthenticationManager)">
            <summary>
                Extracts login info out of an external identity
            </summary>
            <param name="manager"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.GetExternalLoginInfo(Microsoft.Owin.Security.IAuthenticationManager,System.String,System.String)">
            <summary>
                Extracts login info out of an external identity
            </summary>
            <param name="manager"></param>
            <param name="xsrfKey">key that will be used to find the userId to verify</param>
            <param name="expectedValue">
                the value expected to be found using the xsrfKey in the AuthenticationResult.Properties
                dictionary
            </param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.GetExternalLoginInfoAsync(Microsoft.Owin.Security.IAuthenticationManager,System.String,System.String)">
            <summary>
                Extracts login info out of an external identity
            </summary>
            <param name="manager"></param>
            <param name="xsrfKey">key that will be used to find the userId to verify</param>
            <param name="expectedValue">
                the value expected to be found using the xsrfKey in the AuthenticationResult.Properties
                dictionary
            </param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.TwoFactorBrowserRememberedAsync(Microsoft.Owin.Security.IAuthenticationManager,System.String)">
            <summary>
                Returns true if there is a TwoFactorRememberBrowser cookie for a user
            </summary>
            <param name="manager"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.TwoFactorBrowserRemembered(Microsoft.Owin.Security.IAuthenticationManager,System.String)">
            <summary>
                Returns true if there is a TwoFactorRememberBrowser cookie for a user
            </summary>
            <param name="manager"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.AuthenticationManagerExtensions.CreateTwoFactorRememberBrowserIdentity(Microsoft.Owin.Security.IAuthenticationManager,System.String)">
            <summary>
                Creates a TwoFactorRememberBrowser cookie for a user
            </summary>
            <param name="manager"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.OwinContextExtensions">
            <summary>
                Extension methods for OwinContext/>
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.OwinContextExtensions.Set``1(Microsoft.Owin.IOwinContext,``0)">
            <summary>
                Stores an object in the OwinContext using a key based on the AssemblyQualified type name
            </summary>
            <typeparam name="T"></typeparam>
            <param name="context"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.OwinContextExtensions.Get``1(Microsoft.Owin.IOwinContext)">
            <summary>
                Retrieves an object from the OwinContext using a key based on the AssemblyQualified type name
            </summary>
            <typeparam name="T"></typeparam>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.OwinContextExtensions.GetUserManager``1(Microsoft.Owin.IOwinContext)">
            <summary>
                Get the user manager from the context
            </summary>
            <typeparam name="TManager"></typeparam>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.ExternalLoginInfo">
            <summary>
                Used to return information needed to associate an external login
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.ExternalLoginInfo.Login">
            <summary>
                Associated login data
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.ExternalLoginInfo.DefaultUserName">
            <summary>
                Suggested user name for a user
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.ExternalLoginInfo.Email">
            <summary>
                Email claim from the external identity
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.ExternalLoginInfo.ExternalIdentity">
            <summary>
                The external identity
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.SecurityStampValidator">
            <summary>
                Static helper class used to configure a CookieAuthenticationProvider to validate a cookie against a user's security
                stamp
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SecurityStampValidator.OnValidateIdentity``2(System.TimeSpan,System.Func{``0,``1,System.Threading.Tasks.Task{System.Security.Claims.ClaimsIdentity}})">
            <summary>
                Can be used as the ValidateIdentity method for a CookieAuthenticationProvider which will check a user's security
                stamp after validateInterval
                Rejects the identity if the stamp changes, and otherwise will call regenerateIdentity to sign in a new
                ClaimsIdentity
            </summary>
            <typeparam name="TManager"></typeparam>
            <typeparam name="TUser"></typeparam>
            <param name="validateInterval"></param>
            <param name="regenerateIdentity"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SecurityStampValidator.OnValidateIdentity``3(System.TimeSpan,System.Func{``0,``1,System.Threading.Tasks.Task{System.Security.Claims.ClaimsIdentity}},System.Func{System.Security.Claims.ClaimsIdentity,``2})">
            <summary>
                Can be used as the ValidateIdentity method for a CookieAuthenticationProvider which will check a user's security
                stamp after validateInterval
                Rejects the identity if the stamp changes, and otherwise will call regenerateIdentity to sign in a new
                ClaimsIdentity
            </summary>
            <typeparam name="TManager"></typeparam>
            <typeparam name="TUser"></typeparam>
            <typeparam name="TKey"></typeparam>
            <param name="validateInterval"></param>
            <param name="regenerateIdentityCallback"></param>
            <param name="getUserIdCallback"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.SignInManager`2">
            <summary>
            Manages Sign In operations for users
            </summary>
            <typeparam name="TUser"></typeparam>
            <typeparam name="TKey"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.#ctor(Microsoft.AspNet.Identity.UserManager{`0,`1},Microsoft.Owin.Security.IAuthenticationManager)">
            <summary>
            Constructor
            </summary>
            <param name="userManager"></param>
            <param name="authenticationManager"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.CreateUserIdentityAsync(`0)">
            <summary>
            Called to generate the ClaimsIdentity for the user, override to add additional claims before SignIn
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.ConvertIdToString(`1)">
            <summary>
            Convert a TKey userId to a string, by default this just calls ToString()
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.ConvertIdFromString(System.String)">
            <summary>
            Convert a string id to the proper TKey using Convert.ChangeType
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.SignInAsync(`0,System.Boolean,System.Boolean)">
            <summary>
            Creates a user identity and then signs the identity using the AuthenticationManager
            </summary>
            <param name="user"></param>
            <param name="isPersistent"></param>
            <param name="rememberBrowser"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.SendTwoFactorCodeAsync(System.String)">
            <summary>
            Send a two factor code to a user
            </summary>
            <param name="provider"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.GetVerifiedUserIdAsync">
            <summary>
            Get the user id that has been verified already or null.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.HasBeenVerifiedAsync">
            <summary>
            Has the user been verified (ie either via password or external login)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.TwoFactorSignInAsync(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Two factor verification step
            </summary>
            <param name="provider"></param>
            <param name="code"></param>
            <param name="isPersistent"></param>
            <param name="rememberBrowser"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.ExternalSignInAsync(Microsoft.AspNet.Identity.Owin.ExternalLoginInfo,System.Boolean)">
            <summary>
            Sign the user in using an associated external login
            </summary>
            <param name="loginInfo"></param>
            <param name="isPersistent"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.PasswordSignInAsync(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Sign in the user in using the user name and password
            </summary>
            <param name="userName"></param>
            <param name="password"></param>
            <param name="isPersistent"></param>
            <param name="shouldLockout"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.Dispose">
            <summary>
                Dispose
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.Owin.SignInManager`2.Dispose(System.Boolean)">
            <summary>
                If disposing, calls dispose on the Context.  Always nulls out the Context
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.SignInManager`2.AuthenticationType">
            <summary>
            AuthenticationType that will be used by sign in, defaults to DefaultAuthenticationTypes.ApplicationCookie
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.SignInManager`2.UserManager">
            <summary>
            Used to operate on users
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.Owin.SignInManager`2.AuthenticationManager">
            <summary>
            Used to sign in identities
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.Owin.SignInStatus">
            <summary>
            Possible results from a sign in attempt
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Identity.Owin.SignInStatus.Success">
            <summary>
            Sign in was successful
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Identity.Owin.SignInStatus.LockedOut">
            <summary>
            User is locked out
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Identity.Owin.SignInStatus.RequiresVerification">
            <summary>
            Sign in requires addition verification (i.e. two factor)
            </summary>
        </member>
        <member name="F:Microsoft.AspNet.Identity.Owin.SignInStatus.Failure">
            <summary>
            Sign in failed
            </summary>
        </member>
    </members>
</doc>
