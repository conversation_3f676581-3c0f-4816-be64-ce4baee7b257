﻿using BOs;
using SetWorks.Common.Tools.DataExtractor.V3.Core.GenericTransformations.Helpers;

namespace SetWorks.Common.Tools.DataExtractor.V3.Core;

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;
using Flee.PublicTypes;
using SetWorks.Common.Tools.DataExtractor.Core;

public interface IComplexExpressionService
{
    DataTable Evaluate(string fieldToUpdate);
    T Evaluate<T>(DataRow row);
}

public class ComplexExpressionService : IComplexExpressionService
{
    private static readonly Regex WhitespaceRegex = new("[\\s]", RegexOptions.Compiled);
    private static readonly Regex V3VarRegex = new(@"(\[[^\]]*\])", RegexOptions.Compiled);
    private static readonly Regex InvalidVarCharRegex = new(@"\W", RegexOptions.Compiled);

    private readonly Dictionary<string, string> _varTable;
    private ExpressionContext _complexExpressionContext;
    private readonly string[] _selectedColumns;
    private readonly bool _datesAsStrings;
    private readonly FormatType[] _dateTimeFormatTypes = { FormatType.DATE, FormatType.DATE_AND_TIME, FormatType.TIME };
    private readonly string _expression;
    private readonly DataTable _dataTable;
    private readonly Dictionary<string, FormatType> _columnFormats;
    private readonly bool _manualMode;

    public ComplexExpressionService(string clientId, string[] selectedColumns, string expression, DataTable dataTable,
        Dictionary<string, string> varTable = null, bool customFunctions = true, bool datesAsStrings = false, bool manualMode = true)
    {
        _varTable = varTable ?? new Dictionary<string, string>();
        _selectedColumns = selectedColumns;
        _datesAsStrings = datesAsStrings;
        _expression = expression;
        _dataTable = dataTable;
        _manualMode = manualMode;
        _expression = _manualMode ? PrepareV3Expression(expression) : expression;
        _columnFormats = DataExtractorV3Manager.GetModelFieldsDefaultFormattingList()
            .Where(mf => selectedColumns.Contains(mf.Name)).Distinct().ToDictionary(k => k.Name, v => v.DefaultFormat);
        _complexExpressionContext = customFunctions ? new ExpressionContext(new CustomFunctionsV3(clientId)) : new ExpressionContext();
        _complexExpressionContext.Options.RealLiteralDataType = RealLiteralDataType.Decimal;
    }

    public DataTable Evaluate(string fieldToUpdate)
    {
        foreach (DataRow row in _dataTable.Rows)
        {
            string val;
            try
            {
                val = Evaluate(row);
            }
            catch(ExpressionCompileException e)
            {
                throw MoreReadableComplexExpressionError(_dataTable, e, _varTable);
            }
            if (string.IsNullOrEmpty(val) && _dataTable.Columns[fieldToUpdate].DataType != typeof(string))
            {
                row[fieldToUpdate] = DBNull.Value;
            }
            else if (_dataTable.Columns[fieldToUpdate].DataType == typeof(DateTime) && !string.IsNullOrEmpty(val))
            {
                row[fieldToUpdate] =  DateTime.ParseExact(val.Substring(1, val.Length-2), new ExpressionContext().ParserOptions.DateTimeFormat + " hh:mm:ss tt",
                    System.Globalization.CultureInfo.InvariantCulture);
            }
            else
            {
                row[fieldToUpdate] = val;
            }
            
        }
        return _dataTable;
    }
    
    public T Evaluate<T>(DataRow row)
    {
        _complexExpressionContext = RefreshContextFromDataRow(row);
        return _complexExpressionContext.CompileGeneric<T>(_expression).Evaluate();
    }

    public string Evaluate(DataRow row, string expression, bool manualMode = true)
    {
        if (manualMode)
        {
            expression = PrepareV3Expression(expression, row.Table);
        }
        _complexExpressionContext = RefreshContextFromDataRow(row);
        return _complexExpressionContext.CompileDynamic(expression).Evaluate().ToString();
    }

    private string Evaluate(DataRow row)
    {
        _complexExpressionContext = RefreshContextFromDataRow(row);
        try
        {
            var result = _complexExpressionContext.CompileDynamic(_expression).Evaluate();
            return result.ToString();
        }
        catch (ExpressionCompileException ece)
        {
            if (ece.Reason == CompileExceptionReason.UndefinedName)
            {
                throw;
            }
            throw new DataExtractorException(new[] { new OutputError($"Invalid expression: {ece.Message}", _isValidationError: true) });
        }
    }
    
    private ExpressionContext RefreshContextFromDataRow(DataRow row)
    {
        _complexExpressionContext.Variables.Clear();
        FillContextFromDataRow(row);
        return _complexExpressionContext;
    }
    
    private void FillContextFromDataRow(DataRow row)
    {
        var columns = row.Table.Columns.Cast<DataColumn>().Where(c => !_varTable.Values.Contains(c.ColumnName) && _selectedColumns.Contains(c.ColumnName));
        foreach (var col in columns)
        {
            var val = row[col.ColumnName].ToString();
            var varName = _varTable.ContainsKey(col.ColumnName) ? _varTable[col.ColumnName] : EscapeVariableName(col.ColumnName);
            var parsesDecimal = decimal.TryParse(val, out var numVal);
            var parsesDate = DateTime.TryParse(val, out var dateVal);
            var isFormattedNumeric = row.Table.Columns[col.ColumnName].ExtendedProperties["Numeric"]?.ToString() == bool.TrueString;
            var isFormattedDateTime = row.Table.Columns[col.ColumnName].ExtendedProperties["DateTime"]?.ToString() == bool.TrueString
                || _dateTimeFormatTypes.Contains(_columnFormats.GetValue(col.ColumnName));
            parsesDate = parsesDate && !parsesDecimal && !isFormattedNumeric;
            if (row.Table.Columns[col.ColumnName].ExtendedProperties.ContainsKey("OrigDateColumn"))
            {
                var origDateValCol = row.Table.Columns[col.ColumnName].ExtendedProperties["OrigDateColumn"].ToString();
                parsesDate = DateTime.TryParse(origDateValCol, out dateVal);
            }
            
            if (row.Table.Columns[col.Ordinal].DataType == typeof(DateTime) && _datesAsStrings && !string.IsNullOrEmpty(val))
            {
                _complexExpressionContext.Variables[varName] = $"\"{val}\"";
            }
            else if (row[col.ColumnName] is bool || col.DataType == typeof(bool))
            {
                _complexExpressionContext.Variables[varName] = !string.IsNullOrEmpty(row[col.ColumnName].ToString()) && (bool)row[col.ColumnName];
            }
            else if (row[col.ColumnName] is DBNull && (col.DataType == typeof(double) || col.DataType == typeof(decimal) || isFormattedNumeric))
            {
                _complexExpressionContext.Variables[varName] = default(decimal);
            }
            else if (isFormattedNumeric && string.IsNullOrEmpty(row[col.ColumnName]?.ToString()))
            {
                _complexExpressionContext.Variables[varName] = default(decimal);
            }
            else if (row[col.ColumnName] is DBNull && col.DataType.IsValueType && col.DataType != typeof(DateTime))
            {
                _complexExpressionContext.Variables[varName] = Activator.CreateInstance(col.DataType);
            }
            else if (row[col.ColumnName] is DBNull)
            {
                _complexExpressionContext.Variables[varName] = "";
            }
            else if ((row[col.ColumnName] is double || row[col.ColumnName] is decimal || isFormattedNumeric) && parsesDecimal)
            {
                // If it seems like the column is a number, promote to decimal so we can do arithmetic on it
                _complexExpressionContext.Variables[varName] = numVal; 
            }
            else if (parsesDate && row[col.ColumnName] is string && isFormattedDateTime)
            {
                _complexExpressionContext.Variables[varName] = dateVal;
            }
            else if (row[col.ColumnName] is string)
            {
                _complexExpressionContext.Variables[varName] = row[col.ColumnName];
            }
            else
            {
                _complexExpressionContext.Variables[varName] = row[col.ColumnName];
            }
        }
    }

    private string PrepareV3Expression(string expression, DataTable dataTable = null)
    {
        dataTable ??= _dataTable;
        expression = V3VarRegex.Replace(expression, (match) =>
        {
            var rawName = match.Value.Substring(1, match.Value.Length - 2);
            var name = $"Column:{rawName}";
            var inExtendedProps = dataTable.ExtendedProperties.ContainsKey(name);
            var exprCol = inExtendedProps ? dataTable.ExtendedProperties[name].ToString() : rawName;
            var col = !inExtendedProps ? dataTable.Columns.OfType<DataColumn>()
                .FirstOrDefault(c => c.ColumnName.Contains('|') ? c.ColumnName.Split('|').Last() == exprCol : c.ColumnName == exprCol)?.ColumnName : exprCol;
            if (col == null)
            {
                throw new DataExtractorException(new[] { new OutputError($"Couldn't find column: {exprCol}", _isValidationError: true) });
            }

            col = DataExtractorV3Manager.getCurrentColumnName(dataTable.ExtendedProperties, col);
            if (!dataTable.Columns.Contains(col))
            {
                throw new DataExtractorException(new[] { new OutputError($"Column not in table: {col}", _isValidationError: true) });
            }
            var hsh = EncoderAndDecoder.Encode(col);
            hsh = InvalidVarCharRegex.Replace(hsh, "_");
            _varTable[col] = hsh;
            return hsh;
        });
        return expression;
    }

    private static string EscapeVariableName(string name)
    {
        return WhitespaceRegex.Replace(name, "");
    }

    private static DataExtractorException MoreReadableComplexExpressionError(DataTable dataTable, ExpressionCompileException e,
        Dictionary<string, string> varTable)
    {
        var msg = e.Message;
        foreach (var varItem in varTable)
        {
            var nameKey = $"ColumnName:{varItem.Key}";
            var name = dataTable.ExtendedProperties.ContainsKey(nameKey) ? dataTable.ExtendedProperties[nameKey].ToString() 
                : varItem.Key;
            var nameParts = name.Split('|');
            name = name.Contains("|") ? nameParts[1] : name;
            msg = msg.Replace(varItem.Value, name);
        }
        return new DataExtractorException(msg);
    }
}
