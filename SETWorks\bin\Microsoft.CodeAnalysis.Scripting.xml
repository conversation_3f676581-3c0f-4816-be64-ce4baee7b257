<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.CodeAnalysis.Scripting</name>
    </assembly>
    <members>
        <member name="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">
            <summary>
            An exception thrown when the compilation stage of interactive execution produces compilation errors.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.CompilationErrorException.Diagnostics">
            <summary>
            The list of diagnostics produced by compilation.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.AssemblyLoadResult">
            <summary>
            The result of loading an assembly reference to the interactive session.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.AssemblyLoadResult.IsSuccessful">
            <summary>
            True if the assembly was loaded by the assembly loader, false if has been loaded before.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.AssemblyLoadResult.Path">
            <summary>
            Full path to the physical assembly file (might be a shadow-copy of the original assembly file).
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.AssemblyLoadResult.OriginalPath">
            <summary>
            Original assembly file path.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader">
            <summary>
            Implements an assembly loader for interactive compiler and REPL.
            </summary>
            <remarks>
            <para>
            The class is thread-safe.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader.LoadedAssembly.OriginalPath">
            <summary>
            The original path of the assembly before it was shadow-copied.
            For GAC'd assemblies, this is equal to Assembly.Location no matter what path was used to load them.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader.RegisterDependency(Microsoft.CodeAnalysis.AssemblyIdentity,System.String)">
            <summary>
            Notifies the assembly loader about a dependency that might be loaded in future.
            </summary>
            <param name="dependency">Assembly identity.</param>
            <param name="path">Assembly location.</param>
            <remarks>
            Associates a full assembly name with its location. The association is used when an assembly 
            is being loaded and its name needs to be resolved to a location.
            </remarks>
            <exception cref="T:System.ArgumentNullException"><paramref name="dependency"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="path"/> is not an existing path.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveAssemblyLoader.RegisterDependency(System.Reflection.Assembly)">
            <summary>
            Notifies the assembly loader about an in-memory dependency that should be available within the resolution context.
            </summary>
            <param name="dependency">Assembly identity.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="dependency"/> is null.</exception>
            <remarks>
            When another in-memory assembly references the <paramref name="dependency"/> the loader 
            responds with the specified dependency if the assembly identity matches the requested one.
            </remarks>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopy">
            <summary>
            Represents a shadow copy of an assembly or a standalone module.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopy.PrimaryModule">
            <summary>
            Assembly manifest module copy or a standalone module copy.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopy.DocumentationFile">
            <summary>
            Documentation file copy or null if there is none.
            </summary>
            <remarks>
            Documentation files are currently only supported for manifest modules, not modules included in an assembly.
            </remarks>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider">
            <summary>
            Implements shadow-copying metadata file cache.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String},System.Globalization.CultureInfo)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider"/>.
            </summary>
            <param name="directory">The directory to use to store file copies.</param>
            <param name="noShadowCopyDirectories">Directories to exclude from shadow-copying.</param>
            <param name="documentationCommentsCulture">Culture of documentation comments to copy. If not specified no doc comment files are going to be copied.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="directory"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="directory"/> is not an absolute path.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.IsShadowCopy(System.String)">
            <summary>
            Determine whether given path is under the shadow-copy directory managed by this shadow-copy provider.
            </summary>
            <param name="fullPath">Absolute path.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="fullPath"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="fullPath"/> is not an absolute path.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.Dispose">
            <summary>
            Clears shadow-copy cache, disposes all allocated metadata, and attempts to delete copied files.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.GetMetadata(System.String,Microsoft.CodeAnalysis.MetadataImageKind)">
            <summary>
            Gets or creates metadata for specified file path.
            </summary>
            <param name="fullPath">Full path to an assembly manifest module file or a standalone module file.</param>
            <param name="kind">Metadata kind (assembly or module).</param>
            <returns>Metadata for the specified file.</returns>
            <exception cref="T:System.IO.IOException">Error reading file <paramref name="fullPath"/>. See <see cref="P:System.Exception.InnerException"/> for details.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.GetMetadataShadowCopy(System.String,Microsoft.CodeAnalysis.MetadataImageKind)">
            <summary>
            Gets or creates a copy of specified assembly or standalone module.
            </summary>
            <param name="fullPath">Full path to an assembly manifest module file or a standalone module file.</param>
            <param name="kind">Metadata kind (assembly or module).</param>
            <returns>
            Copy of the specified file, or null if the file doesn't need a copy (<see cref="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.NeedsShadowCopy(System.String)"/>). 
            Returns the same object if called multiple times with the same path.
            </returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="fullPath"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="fullPath"/> is not an absolute path.</exception>
            <exception cref="T:System.IO.IOException">Error reading file <paramref name="fullPath"/>. See <see cref="P:System.Exception.InnerException"/> for details.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.SuppressShadowCopy(System.String)">
            <summary>
            Suppresses shadow-copying of specified path.
            </summary>
            <param name="originalPath">Full path.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="originalPath"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="originalPath"/> is not an absolute path.</exception>
            <remarks>
            Doesn't affect files that have already been shadow-copied.
            </remarks>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.MetadataShadowCopyProvider.NeedsShadowCopy(System.String)">
            <summary>
            Determines whether given file is a candidate for shadow-copy.
            </summary>
            <param name="fullPath">An absolute path.</param>
            <returns>True if the shadow-copy policy applies to the specified path.</returns>
            <exception cref="T:System.NullReferenceException"><paramref name="fullPath"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="fullPath"/> is not absolute.</exception>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.FileShadowCopy">
            <summary>
            Represents a shadow copy of a single file.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommandLineRunner.RunInteractive">
            <summary>
            csi.exe and vbi.exe entry point.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommandLineRunner.RunInteractiveCore(Microsoft.CodeAnalysis.ErrorLogger)">
            <summary>
            csi.exe and vbi.exe entry point.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.CommandLineScriptGlobals">
            <summary>
            Defines global members that common command line script hosts expose to the hosted scripts.
            </summary>
            <remarks>
            It is recommended for hosts to expose the members defined by this class and implement 
            the same semantics, so that they can run scripts written against standard hosts. 
            
            Specialized hosts that target niche scenarios might choose to not provide this functionality.
            </remarks>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.CommandLineScriptGlobals.Args">
            <summary>
            Arguments given to the script.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommandLineScriptGlobals.Print(System.Object)">
            <summary>
            Pretty-prints an object.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveScriptGlobals">
            <summary>
            Defines global members that common REPL (Read Eval Print Loop) hosts make available in 
            the interactive session.
            </summary>
            <remarks>
            It is recommended for hosts to expose the members defined by this class and implement 
            the same semantics, so that they can run scripts written against standard hosts. 
            
            Specialized hosts that target niche scenarios might choose to not provide this functionality.
            </remarks>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveScriptGlobals.Args">
            <summary>
            Arguments given to the script.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.InteractiveScriptGlobals.Print(System.Object)">
            <summary>
            Pretty-prints an object.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter">
            <summary>
            Object pretty printer.
            </summary>
            <summary>
            Object pretty printer.
            </summary>
            <summary>
            Object pretty printer.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Builder.AppendInfiniteRecursionMarker">
            <remarks>
            This is for conveying cyclic dependencies to the user, not for detecting them.
            </remarks>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.BuilderOptions">
            <remarks>
            Internal for testing.
            </remarks>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.FormatMethodSignature(System.Reflection.MethodBase)">
            <summary>
            Returns a method signature display string. Used to display stack frames.
            </summary>
            <returns>Null if the method is a compiler generated method that shouldn't be displayed to the user.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Visitor.FormatObjectMembers(Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Builder,System.Object,System.Reflection.TypeInfo,System.Boolean,System.Boolean)">
            <summary>
            Formats object members to a list.
            
            Inline == false:
            <code>
            { A=true, B=false, C=new int[3] { 1, 2, 3 } }
            </code>
            
            Inline == true:
            <code>
            {
              A: true,
              B: false,
              C: new int[3] { 1, 2, 3 }
            }
            </code>
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Visitor.FormatObjectMembersRecursive(System.Collections.Generic.List{Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Visitor.FormattedMember},System.Object,System.Boolean,System.Int32@)">
            <summary>
            Enumerates sorted object members to display.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Visitor.FormatWithEmbeddedExpressions(System.Int32,System.String,System.Object)">
            <summary>
            Evaluate a format string with possible member references enclosed in braces. 
            E.g. "goo = {GetGooString(),nq}, bar = {Bar}".
            </summary>
            <remarks>
            Although in theory any expression is allowed to be embedded in the string such behavior is in practice fundamentally broken.
            The attribute doesn't specify what language (VB, C#, F#, etc.) to use to parse these expressions. Even if it did all languages 
            would need to be able to evaluate each other language's expressions, which is not viable and the Expression Evaluator doesn't 
            work that way today. Instead it evaluates the embedded expressions in the language of the current method frame. When consuming 
            VB objects from C#, for example, the evaluation might fail due to language mismatch (evaluating VB expression using C# parser).
            
            Therefore we limit the expressions to a simple language independent syntax: {clr-member-name} '(' ')' ',nq', 
            where parentheses and ,nq suffix (no-quotes) are optional and the name is an arbitrary CLR field, property, or method name.
            We then resolve the member by name using case-sensitive lookup first with fallback to case insensitive and evaluate it.
            If parentheses are present we only look for methods.
            Only parameterless members are considered.
            </remarks>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Visitor.FormattedMember.MinimalLength">
            <remarks>
            Doesn't (and doesn't need to) reflect the number of digits in <see cref="F:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter.Visitor.FormattedMember.Index"/> since
            it's only used for a conservative approximation (shorter is more conservative when trying
            to determine the minimum number of members that will fill the output).
            </remarks>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.CommonPrimitiveFormatter.NullLiteral">
            <summary>
            String that describes "null" literal in the language.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommonPrimitiveFormatter.FormatPrimitive(System.Object,Microsoft.CodeAnalysis.Scripting.Hosting.CommonPrimitiveFormatterOptions)">
            <summary>
            Returns null if the type is not considered primitive in the target language.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.CommonPrimitiveFormatterOptions.NumberRadix">
            <remarks>
            Since <see cref="T:Microsoft.CodeAnalysis.Scripting.Hosting.CommonPrimitiveFormatter"/> is an extension point, we don't
            perform any validation on <see cref="P:Microsoft.CodeAnalysis.Scripting.Hosting.CommonPrimitiveFormatterOptions.NumberRadix"/> - it's up to the individual
            subtype.
            </remarks>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.CommonTypeNameFormatter.FormatArrayTypeName(System.Type,System.Array,Microsoft.CodeAnalysis.Scripting.Hosting.CommonTypeNameFormatterOptions)">
            <summary>
            Formats an array type name (vector or multidimensional).
            </summary>
        </member>
        <member name="F:Microsoft.CodeAnalysis.Scripting.Hosting.MemberDisplayFormat.SingleLine">
            <summary>
            Display structure of the object on a single line.
            </summary>
        </member>
        <member name="F:Microsoft.CodeAnalysis.Scripting.Hosting.MemberDisplayFormat.SeparateLines">
            <summary>
            Displays a simple description of the object followed by list of members. Each member is
            displayed on a separate line.
            </summary>
        </member>
        <member name="F:Microsoft.CodeAnalysis.Scripting.Hosting.MemberDisplayFormat.Hidden">
            <summary>
            Display just a simple description of the object, like type name or ToString(). Don't
            display any members of the object.
            </summary>
            <remarks>
            <see cref="T:Microsoft.CodeAnalysis.Scripting.Hosting.CommonObjectFormatter"/> does not apply this format to collections elements - 
            they are shown regardless.
            </remarks>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.ObjectFormatter">
            <summary>
            Object pretty printer.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.PrintOptions.IsValidRadix(System.Int32)">
            <remarks>
            Virtual so that extenders can support other radices.
            </remarks>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.NuGetPackageResolver.TryParsePackageReference(System.String,System.String@,System.String@)">
            <summary>
            Syntax is "nuget:name[/version]".
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.RuntimeMetadataReferenceResolver">
            <summary>
            Resolves metadata references for scripts.
            </summary>
            <remarks>
            Operates on runtime metadata artifacts.
            </remarks>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Hosting.GacFileResolver">
            <summary>
            Resolves assembly identities in Global Assembly Cache.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.GacFileResolver.IsAvailable">
            <summary>
            Returns true if GAC is available on the platform.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.GacFileResolver.Architectures">
            <summary>
            Architecture filter used when resolving assembly references.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Hosting.GacFileResolver.PreferredCulture">
            <summary>
            <see cref="T:System.Globalization.CultureInfo"/> used when resolving assembly references, or null to prefer no culture.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Hosting.GacFileResolver.#ctor(System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture},System.Globalization.CultureInfo)">
            <summary>
            Creates an instance of a <see cref="T:Microsoft.CodeAnalysis.Scripting.Hosting.GacFileResolver"/>, if available on the platform (check <see cref="P:Microsoft.CodeAnalysis.Scripting.Hosting.GacFileResolver.IsAvailable"/>).
            </summary>
            <param name="architectures">Supported architectures used to filter GAC assemblies.</param>
            <param name="preferredCulture">A culture to use when choosing the best assembly from 
            among the set filtered by <paramref name="architectures"/></param>
            <exception cref="T:System.PlatformNotSupportedException">The platform doesn't support GAC.</exception>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.Script">
            <summary>
            A class that represents a script that you can run.
            
            Create a script using a language specific script class such as CSharpScript or VisualBasicScript.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Script.Previous">
            <summary>
            A script that will run first when this script is run. 
            Any declarations made in the previous script can be referenced in this script.
            The end state from running this script includes all declarations made by both scripts.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Script.Options">
            <summary>
            The options used by this script.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Script.Code">
            <summary>
            The source code of the script.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Script.SourceText">
            <summary>
            The <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.SourceText"/> of the script.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType">
            <summary>
            The type of an object whose members can be accessed by the script as global variables.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.Script.ReturnType">
            <summary>
            The expected return type of the script.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.WithOptions(Microsoft.CodeAnalysis.Scripting.ScriptOptions)">
            <summary>
            Creates a new version of this script with the specified options.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.ContinueWith(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions)">
            <summary>
            Continues the script with given code snippet.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.ContinueWith(System.IO.Stream,Microsoft.CodeAnalysis.Scripting.ScriptOptions)">
            <summary>
            Continues the script with given <see cref="T:System.IO.Stream"/> representing code.
            </summary>
            <exception cref="T:System.ArgumentNullException">Stream is null.</exception>
            <exception cref="T:System.ArgumentException">Stream is not readable or seekable.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.ContinueWith``1(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions)">
            <summary>
            Continues the script with given code snippet.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.ContinueWith``1(System.IO.Stream,Microsoft.CodeAnalysis.Scripting.ScriptOptions)">
            <summary>
            Continues the script with given <see cref="T:System.IO.Stream"/> representing code.
            </summary>
            <exception cref="T:System.ArgumentNullException">Stream is null.</exception>
            <exception cref="T:System.ArgumentException">Stream is not readable or seekable.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.GetCompilation">
            <summary>
            Get's the <see cref="T:Microsoft.CodeAnalysis.Compilation"/> that represents the semantics of the script.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.EvaluateAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            Runs the script from the beginning and returns the result of the last code snippet.
            </summary>
            <param name="globals">
            An instance of <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/> holding on values of global variables accessible from the script.
            Must be specified if and only if the script was created with a <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The result of the last code snippet.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.RunAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            Runs the script from the beginning.
            </summary>
            <param name="globals">
            An instance of <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/> holding on values for global variables accessible from the script.
            Must be specified if and only if the script was created with <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.RunAsync(System.Object,System.Func{System.Exception,System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Runs the script from the beginning.
            </summary>
            <param name="globals">
            An instance of <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/> holding on values for global variables accessible from the script.
            Must be specified if and only if the script was created with <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.
            </param>
            <param name="catchException">
            If specified, any exception thrown by the script top-level code is passed to <paramref name="catchException"/>.
            If it returns true the exception is caught and stored on the resulting <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/>, otherwise the exception is propagated to the caller.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.RunFromAsync(Microsoft.CodeAnalysis.Scripting.ScriptState,System.Threading.CancellationToken)">
            <summary>
            Run the script from the specified state.
            </summary>
            <param name="previousState">
            Previous state of the script execution.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.RunFromAsync(Microsoft.CodeAnalysis.Scripting.ScriptState,System.Func{System.Exception,System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Run the script from the specified state.
            </summary>
            <param name="previousState">
            Previous state of the script execution.
            </param>
            <param name="catchException">
            If specified, any exception thrown by the script top-level code is passed to <paramref name="catchException"/>.
            If it returns true the exception is caught and stored on the resulting <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/>, otherwise the exception is propagated to the caller.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.Compile(System.Threading.CancellationToken)">
            <summary>
            Forces the script through the compilation step.
            If not called directly, the compilation step will occur on the first call to Run.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script.GetReferencesForCompilation(Microsoft.CodeAnalysis.CommonMessageProvider,Microsoft.CodeAnalysis.DiagnosticBag,Microsoft.CodeAnalysis.MetadataReference)">
            <summary>
            Gets the references that need to be assigned to the compilation.
            This can be different than the list of references defined by the <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.GetExecutor(System.Threading.CancellationToken)">
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Compilation has errors.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.GetPrecedingExecutors(System.Threading.CancellationToken)">
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Compilation has errors.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.TryGetPrecedingExecutors(Microsoft.CodeAnalysis.Scripting.Script,System.Threading.CancellationToken)">
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Compilation has errors.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.EvaluateAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            Runs the script from the beginning and returns the result of the last code snippet.
            </summary>
            <param name="globals">
            An instance of <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/> holding on values of global variables accessible from the script.
            Must be specified if and only if the script was created with a <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The result of the last code snippet.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.RunAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            Runs the script from the beginning.
            </summary>
            <param name="globals">
            An instance of <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/> holding on values for global variables accessible from the script.
            Must be specified if and only if the script was created with <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Compilation has errors.</exception>
            <exception cref="T:System.ArgumentException">The type of <paramref name="globals"/> doesn't match <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.RunAsync(System.Object,System.Func{System.Exception,System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Runs the script from the beginning.
            </summary>
            <param name="globals">
            An instance of <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/> holding on values for global variables accessible from the script.
            Must be specified if and only if the script was created with <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.
            </param>
            <param name="catchException">
            If specified, any exception thrown by the script top-level code is passed to <paramref name="catchException"/>.
            If it returns true the exception is caught and stored on the resulting <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/>, otherwise the exception is propagated to the caller.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Compilation has errors.</exception>
            <exception cref="T:System.ArgumentException">The type of <paramref name="globals"/> doesn't match <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.CreateDelegate(System.Threading.CancellationToken)">
            <summary>
            Creates a delegate that will run this script from the beginning when invoked.
            </summary>
            <remarks>
            The delegate doesn't hold on this script or its compilation.
            </remarks>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.RunFromAsync(Microsoft.CodeAnalysis.Scripting.ScriptState,System.Threading.CancellationToken)">
            <summary>
            Run the script from the specified state.
            </summary>
            <param name="previousState">
            Previous state of the script execution.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="previousState"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="previousState"/> is not a previous execution state of this script.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.Script`1.RunFromAsync(Microsoft.CodeAnalysis.Scripting.ScriptState,System.Func{System.Exception,System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Run the script from the specified state.
            </summary>
            <param name="previousState">
            Previous state of the script execution.
            </param>
            <param name="catchException">
            If specified, any exception thrown by the script top-level code is passed to <paramref name="catchException"/>.
            If it returns true the exception is caught and stored on the resulting <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/>, otherwise the exception is propagated to the caller.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="previousState"/> is null.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="previousState"/> is not a previous execution state of this script.</exception>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.ScriptBuilder">
            <summary>
            Represents a runtime execution context for scripts.
            </summary>
        </member>
        <member name="F:Microsoft.CodeAnalysis.Scripting.ScriptBuilder.s_globalAssemblyNamePrefix">
            <summary>
            Unique prefix for generated assemblies.
            </summary>
            <remarks>
            The full names of uncollectible assemblies generated by this context must be unique,
            so that we can resolve references among them. Note that CLR can load two different assemblies of the very same 
            identity into the same load context.
            
            We are using a certain naming scheme for the generated assemblies (a fixed name prefix followed by a number). 
            If we allowed the compiled code to add references that match this exact pattern it might happen that 
            the user supplied reference identity conflicts with the identity we use for our generated assemblies and 
            the AppDomain assembly resolve event won't be able to correctly identify the target assembly.
            
            To avoid this problem we use a prefix for assemblies we generate that is unlikely to conflict with user specified references.
            We also check that no user provided references are allowed to be used in the compiled code and report an error ("reserved assembly name").
            </remarks>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptBuilder.CreateExecutor``1(Microsoft.CodeAnalysis.Scripting.ScriptCompiler,Microsoft.CodeAnalysis.Compilation,System.Boolean,System.Threading.CancellationToken)">
            <exception cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException">Compilation has errors.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptBuilder.Build``1(Microsoft.CodeAnalysis.Compilation,Microsoft.CodeAnalysis.DiagnosticBag,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Builds a delegate that will execute just this scripts code.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.ScriptExecutionState">
            <summary>
            Represents the submission states and globals that get passed to a script entry point when run.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.ScriptingResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.AbsolutePathExpected">
            <summary>
              Looks up a localized string similar to Absolute path expected.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.AssemblyAlreadyLoaded">
            <summary>
              Looks up a localized string similar to Assembly &apos;{0}, Version={1}&apos; has already been loaded from &apos;{2}&apos;. A different assembly with the same name and version can&apos;t be loaded: &apos;{3}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.AssemblyAlreadyLoadedNotSigned">
            <summary>
              Looks up a localized string similar to Assembly &apos;{0}&apos; has already been loaded from &apos;{1}&apos;. A different assembly with the same name can&apos;t be loaded unless it&apos;s signed: &apos;{2}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.AtFileLine">
            <summary>
              Looks up a localized string similar to  at {0} : {1}.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.CannotSetConstantVariable">
            <summary>
              Looks up a localized string similar to Cannot set a constant variable.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.CannotSetReadOnlyVariable">
            <summary>
              Looks up a localized string similar to Cannot set a read-only variable.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.CantAssignTo">
            <summary>
              Looks up a localized string similar to Can&apos;t assign &apos;{0}&apos; to &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.DisplayNameOrPathCannotBe">
            <summary>
              Looks up a localized string similar to Display name or path cannot be empty..
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.ExpectedAnAssemblyReference">
            <summary>
              Looks up a localized string similar to Expected an assembly reference..
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.GlobalsNotAssignable">
            <summary>
              Looks up a localized string similar to The globals of type &apos;{0}&apos; is not assignable to &apos;{1}&apos;.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.GlobalVariablesWithoutGlobalType">
            <summary>
              Looks up a localized string similar to Global variables passed to a script without a global type.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.HelpPrompt">
            <summary>
              Looks up a localized string similar to Type &quot;#help&quot; for more information..
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.HelpText">
             <summary>
               Looks up a localized string similar to Keyboard shortcuts:
              Enter         If the current submission appears to be complete, evaluate it.  Otherwise, insert a new line.
              Escape        Clear the current submission.
              UpArrow       Replace the current submission with a previous submission.
              DownArrow     Replace the current submission with a subsequent submission (after having previously navigated backwards).
              Ctrl-C        Exit the REPL.
            REPL commands:
              #help         Display help on available commands and key bindings.
            Script directiv [rest of string was truncated]&quot;;.
             </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.InvalidAssemblyName">
            <summary>
              Looks up a localized string similar to Invalid assembly name.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.InvalidCharactersInAssemblyName">
            <summary>
              Looks up a localized string similar to Invalid characters in assemblyName.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.PlusAdditionalError">
            <summary>
              Looks up a localized string similar to + additional {0} error.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.PlusAdditionalErrors">
            <summary>
              Looks up a localized string similar to + additional {0} errors.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.ScriptRequiresGlobalVariables">
            <summary>
              Looks up a localized string similar to The script requires access to global variables but none were given.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.StackOverflowWhileEvaluating">
            <summary>
              Looks up a localized string similar to !&lt;Stack overflow while evaluating object&gt;.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptingResources.StartingStateIncompatible">
            <summary>
              Looks up a localized string similar to Starting state was incompatible with script..
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions">
            <summary>
            Options for creating and running scripts.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.MetadataReferences">
            <summary>
            An array of <see cref="T:Microsoft.CodeAnalysis.MetadataReference"/>s to be added to the script.
            </summary>
            <remarks>
            The array may contain both resolved and unresolved references (<see cref="T:Microsoft.CodeAnalysis.UnresolvedMetadataReference"/>).
            Unresolved references are resolved when the script is about to be executed 
            (<see cref="M:Microsoft.CodeAnalysis.Scripting.Script.RunAsync(System.Object,System.Threading.CancellationToken)"/>.
            Any resolution errors are reported at that point through <see cref="T:Microsoft.CodeAnalysis.Scripting.CompilationErrorException"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.MetadataResolver">
            <summary>
            <see cref="T:Microsoft.CodeAnalysis.MetadataReferenceResolver"/> to be used to resolve missing dependencies, unresolved metadata references and #r directives.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.SourceResolver">
            <summary>
            <see cref="T:Microsoft.CodeAnalysis.SourceReferenceResolver"/> to be used to resolve source of scripts referenced via #load directive.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.Imports">
            <summary>
            The namespaces, static classes and aliases imported by the script.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.EmitDebugInformation">
            <summary>
            Specifies whether debugging symbols should be emitted.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.FileEncoding">
            <summary>
            Specifies the encoding to be used when debugging scripts loaded from a file, or saved to a file for debugging purposes.
            If it's null, the compiler will attempt to detect the necessary encoding for debugging
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.FilePath">
            <summary>
            The path to the script source if it originated from a file, empty otherwise.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithFilePath(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.FilePath"/> changed.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithReferences(System.Collections.Immutable.ImmutableArray{Microsoft.CodeAnalysis.MetadataReference})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the references changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithReferences(System.Collections.Generic.IEnumerable{Microsoft.CodeAnalysis.MetadataReference})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the references changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithReferences(Microsoft.CodeAnalysis.MetadataReference[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the references changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddReferences(System.Collections.Generic.IEnumerable{Microsoft.CodeAnalysis.MetadataReference})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with references added.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddReferences(Microsoft.CodeAnalysis.MetadataReference[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with references added.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithReferences(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the references changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
            <exception cref="T:System.NotSupportedException">Specified assembly is not supported (e.g. it's a dynamic assembly).</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithReferences(System.Reflection.Assembly[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the references changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
            <exception cref="T:System.NotSupportedException">Specified assembly is not supported (e.g. it's a dynamic assembly).</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddReferences(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with references added.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
            <exception cref="T:System.NotSupportedException">Specified assembly is not supported (e.g. it's a dynamic assembly).</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddReferences(System.Reflection.Assembly[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with references added.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
            <exception cref="T:System.NotSupportedException">Specified assembly is not supported (e.g. it's a dynamic assembly).</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithReferences(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the references changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithReferences(System.String[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the references changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddReferences(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with references added.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="references"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddReferences(System.String[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with references added.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithMetadataResolver(Microsoft.CodeAnalysis.MetadataReferenceResolver)">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with specified <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.MetadataResolver"/>.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithSourceResolver(Microsoft.CodeAnalysis.SourceReferenceResolver)">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with specified <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.SourceResolver"/>.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithImports(System.Collections.Immutable.ImmutableArray{System.String})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.Imports"/> changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="imports"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithImports(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.Imports"/> changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="imports"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithImports(System.String[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with the <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.Imports"/> changed.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="imports"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddImports(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.Imports"/> added.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="imports"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.AddImports(System.String[])">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.Imports"/> added.
            </summary>
            <exception cref="T:System.ArgumentNullException"><paramref name="imports"/> is null or contains a null reference.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithEmitDebugInformation(System.Boolean)">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with debugging information enabled.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptOptions.WithFileEncoding(System.Text.Encoding)">
            <summary>
            Creates a new <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptOptions"/> with specified <see cref="P:Microsoft.CodeAnalysis.Scripting.ScriptOptions.FileEncoding"/>.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.ScriptRunner`1">
            <summary>
            A delegate that will run a script when invoked.
            </summary>
            <param name="globals">An object instance whose members can be accessed by the script as global variables.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <exception cref="T:System.ArgumentException">The type of <paramref name="globals"/> doesn't match the corresponding <see cref="P:Microsoft.CodeAnalysis.Scripting.Script.GlobalsType"/>.</exception>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.ScriptState">
            <summary>
            The result of running a script.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptState.Script">
            <summary>
            The script that ran to produce this result.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptState.Exception">
            <summary>
            Caught exception originating from the script top-level code.
            </summary>
            <remarks>
            Exceptions are only caught and stored here if the API returning the <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> is instructed to do so. 
            By default they are propagated to the caller of the API.
            </remarks>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptState.ReturnValue">
            <summary>
            The final value produced by running the script.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptState.Variables">
            <summary>
            Returns variables defined by the scripts in the declaration order.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptState.GetVariable(System.String)">
            <summary>
            Returns a script variable of the specified name. 
            </summary> 
            <remarks>
            If multiple script variables are defined in the script (in distinct submissions) returns the last one.
            Name lookup is case sensitive in C# scripts and case insensitive in VB scripts.
            </remarks>
            <returns><see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptVariable"/> or null, if no variable of the specified <paramref name="name"/> is defined in the script.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is null.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptState.ContinueWithAsync(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Threading.CancellationToken)">
            <summary>
            Continues script execution from the state represented by this instance by running the specified code snippet.
            </summary>
            <param name="code">The code to be executed.</param>
            <param name="options">Options.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running <paramref name="code"/>, including all declared variables and return value.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptState.ContinueWithAsync(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Func{System.Exception,System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Continues script execution from the state represented by this instance by running the specified code snippet.
            </summary>
            <param name="code">The code to be executed.</param>
            <param name="options">Options.</param>
            <param name="catchException">
            If specified, any exception thrown by the script top-level code is passed to <paramref name="catchException"/>.
            If it returns true the exception is caught and stored on the resulting <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/>, otherwise the exception is propagated to the caller.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running <paramref name="code"/>, including all declared variables, return value and caught exception (if applicable).</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptState.ContinueWithAsync``1(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Threading.CancellationToken)">
            <summary>
            Continues script execution from the state represented by this instance by running the specified code snippet.
            </summary>
            <param name="code">The code to be executed.</param>
            <param name="options">Options.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running <paramref name="code"/>, including all declared variables and return value.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.Scripting.ScriptState.ContinueWithAsync``1(System.String,Microsoft.CodeAnalysis.Scripting.ScriptOptions,System.Func{System.Exception,System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Continues script execution from the state represented by this instance by running the specified code snippet.
            </summary>
            <param name="code">The code to be executed.</param>
            <param name="options">Options.</param>
            <param name="catchException">
            If specified, any exception thrown by the script top-level code is passed to <paramref name="catchException"/>.
            If it returns true the exception is caught and stored on the resulting <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/>, otherwise the exception is propagated to the caller.
            </param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>A <see cref="T:Microsoft.CodeAnalysis.Scripting.ScriptState"/> that represents the state after running <paramref name="code"/>, including all declared variables, return value and caught exception (if applicable).</returns>
        </member>
        <member name="T:Microsoft.CodeAnalysis.Scripting.ScriptVariable">
            <summary>
            A variable declared by the script.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptVariable.Name">
            <summary>
            The name of the variable.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptVariable.Type">
            <summary>
            The type of the variable.
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptVariable.IsReadOnly">
            <summary>
            True if the variable can't be written to (it's declared as readonly or a constant).
            </summary>
        </member>
        <member name="P:Microsoft.CodeAnalysis.Scripting.ScriptVariable.Value">
            <summary>
            The value of the variable after running the script.
            </summary>
            <exception cref="T:System.InvalidOperationException">Variable is read-only or a constant.</exception>
            <exception cref="T:System.ArgumentException">The type of the specified <paramref name="value"/> isn't assignable to the type of the variable.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.RelativePathResolver.#ctor(System.Collections.Immutable.ImmutableArray{System.String},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.CodeAnalysis.RelativePathResolver"/> class.
            </summary>
            <param name="searchPaths">An ordered set of fully qualified 
            paths which are searched when resolving assembly names.</param>
            <param name="baseDirectory">Directory used when resolving relative paths.</param>
        </member>
        <member name="T:Microsoft.CodeAnalysis.CoreClrShim">
            <summary>
            Shim for APIs available only on CoreCLR.
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.ClrGlobalAssemblyCache">
            <summary>
            Provides APIs to enumerate and look up assemblies stored in the Global Assembly Cache.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.ClrGlobalAssemblyCache.GetAssemblyIdentities(System.Reflection.AssemblyName,System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture})">
            <summary>
            Enumerates assemblies in the GAC returning those that match given partial name and
            architecture.
            </summary>
            <param name="partialName">Optional partial name.</param>
            <param name="architectureFilter">Optional architecture filter.</param>
        </member>
        <member name="M:Microsoft.CodeAnalysis.ClrGlobalAssemblyCache.GetAssemblyIdentities(System.String,System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture})">
            <summary>
            Enumerates assemblies in the GAC returning those that match given partial name and
            architecture.
            </summary>
            <param name="partialName">The optional partial name.</param>
            <param name="architectureFilter">The optional architecture filter.</param>
        </member>
        <member name="M:Microsoft.CodeAnalysis.ClrGlobalAssemblyCache.GetAssemblySimpleNames(System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture})">
            <summary>
            Enumerates assemblies in the GAC returning their simple names.
            </summary>
            <param name="architectureFilter">Optional architecture filter.</param>
            <returns>Unique simple names of GAC assemblies.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.FusionAssemblyIdentity.ToAssemblyIdentity(Microsoft.CodeAnalysis.FusionAssemblyIdentity.IAssemblyName)">
            <summary>
            Converts <see cref="T:Microsoft.CodeAnalysis.FusionAssemblyIdentity.IAssemblyName"/> to <see cref="T:System.Reflection.AssemblyName"/> with all metadata fields filled.
            </summary>
            <returns>
            Assembly name with Version, Culture and PublicKeyToken components filled in:
            "SimpleName, Version=#.#.#.#, Culture=XXX, PublicKeyToken=XXXXXXXXXXXXXXXX".
            In addition Retargetable flag and ContentType are set.
            </returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.FusionAssemblyIdentity.ToAssemblyNameObject(System.Reflection.AssemblyName)">
            <summary>
            Converts <see cref="T:System.Reflection.AssemblyName"/> to an equivalent <see cref="T:Microsoft.CodeAnalysis.FusionAssemblyIdentity.IAssemblyName"/>.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.FusionAssemblyIdentity.ToAssemblyNameObject(System.String)">
            <summary>
            Creates <see cref="T:Microsoft.CodeAnalysis.FusionAssemblyIdentity.IAssemblyName"/> object by parsing given display name.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.FusionAssemblyIdentity.GetBestMatch(System.Collections.Generic.IEnumerable{Microsoft.CodeAnalysis.FusionAssemblyIdentity.IAssemblyName},System.String)">
            <summary>
            Selects the candidate assembly with the largest version number.  Uses culture as a tie-breaker if it is provided.
            All candidates are assumed to have the same name and must include versions and cultures.  
            </summary>
        </member>
        <member name="T:Microsoft.CodeAnalysis.GlobalAssemblyCache">
            <summary>
            Provides APIs to enumerate and look up assemblies stored in the Global Assembly Cache.
            </summary>
        </member>
        <member name="F:Microsoft.CodeAnalysis.GlobalAssemblyCache.CurrentArchitectures">
            <summary>
            Represents the current Processor architecture.
            </summary>
        </member>
        <member name="M:Microsoft.CodeAnalysis.GlobalAssemblyCache.GetAssemblyIdentities(System.Reflection.AssemblyName,System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture})">
            <summary>
            Enumerates assemblies in the GAC returning those that match given partial name and
            architecture.
            </summary>
            <param name="partialName">Optional partial name.</param>
            <param name="architectureFilter">Optional architecture filter.</param>
        </member>
        <member name="M:Microsoft.CodeAnalysis.GlobalAssemblyCache.GetAssemblyIdentities(System.String,System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture})">
            <summary>
            Enumerates assemblies in the GAC returning those that match given partial name and
            architecture.
            </summary>
            <param name="partialName">The optional partial name.</param>
            <param name="architectureFilter">The optional architecture filter.</param>
        </member>
        <member name="M:Microsoft.CodeAnalysis.GlobalAssemblyCache.GetAssemblySimpleNames(System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture})">
            <summary>
            Enumerates assemblies in the GAC returning their simple names.
            </summary>
            <param name="architectureFilter">Optional architecture filter.</param>
            <returns>Unique simple names of GAC assemblies.</returns>
        </member>
        <member name="M:Microsoft.CodeAnalysis.GlobalAssemblyCache.ResolvePartialName(System.String,System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture},System.Globalization.CultureInfo)">
            <summary>
            Looks up specified partial assembly name in the GAC and returns the best matching <see cref="T:Microsoft.CodeAnalysis.AssemblyIdentity"/>.
            </summary>
            <param name="displayName">The display name of an assembly</param>
            <param name="architectureFilter">The optional processor architecture</param>
            <param name="preferredCulture">The optional preferred culture information</param>
            <returns>An assembly identity or null, if <paramref name="displayName"/> can't be resolved.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="displayName"/> is null.</exception>
        </member>
        <member name="M:Microsoft.CodeAnalysis.GlobalAssemblyCache.ResolvePartialName(System.String,System.String@,System.Collections.Immutable.ImmutableArray{System.Reflection.ProcessorArchitecture},System.Globalization.CultureInfo)">
            <summary>
            Looks up specified partial assembly name in the GAC and returns the best matching <see cref="T:Microsoft.CodeAnalysis.AssemblyIdentity"/>.
            </summary>
            <param name="displayName">The display name of an assembly</param>
            <param name="location">Full path name of the resolved assembly</param>
            <param name="architectureFilter">The optional processor architecture</param>
            <param name="preferredCulture">The optional preferred culture information</param>
            <returns>An assembly identity or null, if <paramref name="displayName"/> can't be resolved.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="displayName"/> is null.</exception>
        </member>
        <member name="T:Microsoft.CodeAnalysis.MonoGlobalAssemblyCache">
            <summary>
            Provides APIs to enumerate and look up assemblies stored in the Global Assembly Cache.
            </summary>
        </member>
    </members>
</doc>
