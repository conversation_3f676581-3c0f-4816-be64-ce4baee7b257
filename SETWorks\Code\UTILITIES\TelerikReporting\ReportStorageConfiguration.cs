using System;
using System.Configuration;
using System.Web;
using Telerik.Reporting.Cache;
using Telerik.Reporting.Services;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Configuration class for setting up custom Telerik Report storage
    /// </summary>
    public static class ReportStorageConfiguration
    {
        private static bool _isInitialized = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// Initialize the custom report storage - call this in Application_Start
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            lock (_lockObject)
            {
                if (_isInitialized) return;

                try
                {
                    // Determine which storage to use based on configuration
                    IStorage reportStorage;
                    var useRedisStorage = UseRedisStorage();

                    if (useRedisStorage)
                    {
                        reportStorage = new RedisReportStorage();
                        LogInfo("Using Redis storage for Telerik Reports");
                    }
                    else
                    {
                        reportStorage = new CustomReportStorage();
                        LogInfo("Using database storage for Telerik Reports");
                    }

                    // Set up custom storage for report cache
                    ReportingEngineConfiguration.Storage = reportStorage;

                    // Optional: Set up cleanup timer for database storage only
                    if (!useRedisStorage && reportStorage is CustomReportStorage dbStorage)
                    {
                        SetupCleanupTimer(dbStorage);
                    }

                    _isInitialized = true;
                    LogInfo("Custom Telerik Report Storage initialized successfully");
                }
                catch (Exception ex)
                {
                    LogError("Failed to initialize custom report storage", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// Set up a timer to periodically clean up expired report storage entries
        /// </summary>
        private static void SetupCleanupTimer(CustomReportStorage storage)
        {
            try
            {
                // Clean up every 30 minutes
                var cleanupInterval = TimeSpan.FromMinutes(30);
                
                var timer = new System.Threading.Timer(
                    callback: _ => {
                        try
                        {
                            storage.CleanupExpiredEntries();
                        }
                        catch (Exception ex)
                        {
                            LogError("Error during scheduled cleanup", ex);
                        }
                    },
                    state: null,
                    dueTime: cleanupInterval,
                    period: cleanupInterval
                );

                // Store timer reference to prevent garbage collection
                HttpContext.Current.Application["ReportStorageCleanupTimer"] = timer;
            }
            catch (Exception ex)
            {
                LogError("Failed to setup cleanup timer", ex);
                // Don't throw - cleanup timer is optional
            }
        }

        /// <summary>
        /// Get configuration for report timeout based on app settings
        /// </summary>
        public static TimeSpan GetReportTimeout()
        {
            var timeoutMinutes = 120; // Default 2 hours
            
            var configValue = ConfigurationManager.AppSettings["TelerikReportTimeoutMinutes"];
            if (!string.IsNullOrEmpty(configValue) && int.TryParse(configValue, out var parsed))
            {
                timeoutMinutes = parsed;
            }

            return TimeSpan.FromMinutes(timeoutMinutes);
        }

        /// <summary>
        /// Check if we should use custom storage based on configuration
        /// </summary>
        public static bool UseCustomStorage()
        {
            var useCustom = ConfigurationManager.AppSettings["TelerikUseCustomStorage"];
            return string.IsNullOrEmpty(useCustom) ||
                   useCustom.Equals("true", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Check if we should use Redis storage for reports
        /// </summary>
        public static bool UseRedisStorage()
        {
            var useRedis = ConfigurationManager.AppSettings["TelerikUseRedisStorage"];
            return useRedis != null && useRedis.Equals("true", StringComparison.OrdinalIgnoreCase);
        }

        private static void LogError(string message, Exception ex)
        {
            try
            {
                var log = log4net.LogManager.GetLogger(typeof(ReportStorageConfiguration));
                log.Error($"ReportStorageConfiguration: {message}", ex);
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"ReportStorageConfiguration Error: {message} - {ex}");
            }
        }

        private static void LogInfo(string message)
        {
            try
            {
                var log = log4net.LogManager.GetLogger(typeof(ReportStorageConfiguration));
                log.Info($"ReportStorageConfiguration: {message}");
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"ReportStorageConfiguration Info: {message}");
            }
        }
    }

    /// <summary>
    /// Custom report service resolver that uses our custom storage
    /// </summary>
    public class CustomReportServiceResolver : IReportServiceConfiguration
    {
        public IStorage Storage { get; set; }
        public IReportSourceResolver ReportSourceResolver { get; set; }

        public CustomReportServiceResolver()
        {
            if (ReportStorageConfiguration.UseCustomStorage())
            {
                if (ReportStorageConfiguration.UseRedisStorage())
                {
                    Storage = new RedisReportStorage();
                }
                else
                {
                    Storage = new CustomReportStorage();
                }
            }

            // Use default report source resolver unless you need custom logic
            ReportSourceResolver = new TypeReportSourceResolver();
        }
    }
}
