using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Paginated data source for large Telerik reports to reduce memory usage
    /// </summary>
    public class PaginatedReportDataSource
    {
        private readonly Func<int, int, DataTable> _dataProvider;
        private readonly int _pageSize;
        private readonly int _totalRecords;
        private readonly Dictionary<int, DataTable> _pageCache;
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(10);
        private readonly Dictionary<int, DateTime> _pageCacheTimestamps;

        public PaginatedReportDataSource(Func<int, int, DataTable> dataProvider, int totalRecords, int pageSize = 1000)
        {
            _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
            _totalRecords = totalRecords;
            _pageSize = pageSize;
            _pageCache = new Dictionary<int, DataTable>();
            _pageCacheTimestamps = new Dictionary<int, DateTime>();
        }

        /// <summary>
        /// Get data for a specific page
        /// </summary>
        public DataTable GetPage(int pageNumber)
        {
            if (pageNumber < 0 || pageNumber >= TotalPages)
            {
                throw new ArgumentOutOfRangeException(nameof(pageNumber), $"Page number must be between 0 and {TotalPages - 1}");
            }

            // Check if page is cached and not expired
            if (_pageCache.ContainsKey(pageNumber) && 
                _pageCacheTimestamps.ContainsKey(pageNumber) &&
                DateTime.UtcNow - _pageCacheTimestamps[pageNumber] < _cacheExpiration)
            {
                return _pageCache[pageNumber];
            }

            // Load page data
            var skip = pageNumber * _pageSize;
            var take = Math.Min(_pageSize, _totalRecords - skip);
            
            var pageData = _dataProvider(skip, take);
            
            // Cache the page
            _pageCache[pageNumber] = pageData;
            _pageCacheTimestamps[pageNumber] = DateTime.UtcNow;
            
            // Clean up expired cache entries
            CleanupExpiredCache();
            
            return pageData;
        }

        /// <summary>
        /// Get data for a range of records
        /// </summary>
        public DataTable GetRange(int startRecord, int recordCount)
        {
            var startPage = startRecord / _pageSize;
            var endRecord = startRecord + recordCount - 1;
            var endPage = endRecord / _pageSize;

            if (startPage == endPage)
            {
                // Single page request
                var pageData = GetPage(startPage);
                var startIndex = startRecord % _pageSize;
                var endIndex = Math.Min(startIndex + recordCount - 1, pageData.Rows.Count - 1);
                
                return ExtractRows(pageData, startIndex, endIndex - startIndex + 1);
            }
            else
            {
                // Multi-page request - combine pages
                var combinedTable = CreateEmptyTable();
                
                for (int page = startPage; page <= endPage; page++)
                {
                    var pageData = GetPage(page);
                    
                    int pageStartIndex = 0;
                    int pageRecordCount = pageData.Rows.Count;
                    
                    if (page == startPage)
                    {
                        pageStartIndex = startRecord % _pageSize;
                        pageRecordCount = Math.Min(pageData.Rows.Count - pageStartIndex, recordCount);
                    }
                    else if (page == endPage)
                    {
                        var remainingRecords = recordCount - combinedTable.Rows.Count;
                        pageRecordCount = Math.Min(pageData.Rows.Count, remainingRecords);
                    }
                    
                    var pageSubset = ExtractRows(pageData, pageStartIndex, pageRecordCount);
                    MergeRows(combinedTable, pageSubset);
                }
                
                return combinedTable;
            }
        }

        /// <summary>
        /// Get all data (use with caution for large datasets)
        /// </summary>
        public DataTable GetAllData()
        {
            return GetRange(0, _totalRecords);
        }

        /// <summary>
        /// Preload specific pages for better performance
        /// </summary>
        public void PreloadPages(params int[] pageNumbers)
        {
            foreach (var pageNumber in pageNumbers)
            {
                if (pageNumber >= 0 && pageNumber < TotalPages)
                {
                    GetPage(pageNumber); // This will cache the page
                }
            }
        }

        /// <summary>
        /// Clear all cached pages
        /// </summary>
        public void ClearCache()
        {
            _pageCache.Clear();
            _pageCacheTimestamps.Clear();
        }

        public int TotalRecords => _totalRecords;
        public int PageSize => _pageSize;
        public int TotalPages => (int)Math.Ceiling((double)_totalRecords / _pageSize);
        public int CachedPageCount => _pageCache.Count;

        private void CleanupExpiredCache()
        {
            var expiredPages = _pageCacheTimestamps
                .Where(kvp => DateTime.UtcNow - kvp.Value > _cacheExpiration)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var pageNumber in expiredPages)
            {
                _pageCache.Remove(pageNumber);
                _pageCacheTimestamps.Remove(pageNumber);
            }
        }

        private DataTable CreateEmptyTable()
        {
            // Get the first page to determine schema
            if (_pageCache.Count > 0)
            {
                var sampleTable = _pageCache.Values.First();
                return sampleTable.Clone();
            }
            
            // Load first page to get schema
            var firstPage = GetPage(0);
            return firstPage.Clone();
        }

        private DataTable ExtractRows(DataTable source, int startIndex, int count)
        {
            var result = source.Clone();
            
            var endIndex = Math.Min(startIndex + count, source.Rows.Count);
            for (int i = startIndex; i < endIndex; i++)
            {
                result.ImportRow(source.Rows[i]);
            }
            
            return result;
        }

        private void MergeRows(DataTable target, DataTable source)
        {
            foreach (DataRow row in source.Rows)
            {
                target.ImportRow(row);
            }
        }
    }

    /// <summary>
    /// Factory for creating paginated data sources for common report scenarios
    /// </summary>
    public static class PaginatedReportDataSourceFactory
    {
        /// <summary>
        /// Create a paginated data source from a SQL query
        /// </summary>
        public static PaginatedReportDataSource CreateFromSql(
            string connectionString, 
            string baseQuery, 
            string countQuery = null,
            int pageSize = 1000)
        {
            // Get total count
            var totalRecords = GetTotalRecords(connectionString, countQuery ?? $"SELECT COUNT(*) FROM ({baseQuery}) AS CountQuery");
            
            // Create data provider function
            Func<int, int, DataTable> dataProvider = (skip, take) =>
            {
                var pagedQuery = $"{baseQuery} ORDER BY (SELECT NULL) OFFSET {skip} ROWS FETCH NEXT {take} ROWS ONLY";
                return ExecuteQuery(connectionString, pagedQuery);
            };
            
            return new PaginatedReportDataSource(dataProvider, totalRecords, pageSize);
        }

        private static int GetTotalRecords(string connectionString, string countQuery)
        {
            using (var connection = new System.Data.SqlClient.SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new System.Data.SqlClient.SqlCommand(countQuery, connection))
                {
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        private static DataTable ExecuteQuery(string connectionString, string query)
        {
            using (var connection = new System.Data.SqlClient.SqlConnection(connectionString))
            {
                connection.Open();
                using (var adapter = new System.Data.SqlClient.SqlDataAdapter(query, connection))
                {
                    var dataTable = new DataTable();
                    adapter.Fill(dataTable);
                    return dataTable;
                }
            }
        }
    }
}
