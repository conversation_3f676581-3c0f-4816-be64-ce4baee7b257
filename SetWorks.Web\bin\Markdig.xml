<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Markdig</name>
    </assembly>
    <members>
        <member name="T:Markdig.Extensions.Abbreviations.Abbreviation">
            <summary>
            An abbreviation object stored at the document level. See extension methods in <see cref="T:Markdig.Extensions.Abbreviations.AbbreviationHelper"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.LeafBlock" />
        </member>
        <member name="M:Markdig.Extensions.Abbreviations.Abbreviation.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Abbreviations.Abbreviation"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Extensions.Abbreviations.Abbreviation.Label">
            <summary>
            Gets or sets the label.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Abbreviations.Abbreviation.Text">
            <summary>
            The text associated to this label.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Abbreviations.Abbreviation.LabelSpan">
            <summary>
            The label span
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Abbreviations.AbbreviationExtension">
            <summary>
            Extension to allow abbreviations.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Abbreviations.AbbreviationHelper">
            <summary>
            Extension methods for <see cref="T:Markdig.Extensions.Abbreviations.Abbreviation"/>.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Abbreviations.AbbreviationInline">
            <summary>
            The inline abbreviation.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.LeafInline" />
        </member>
        <member name="M:Markdig.Extensions.Abbreviations.AbbreviationInline.#ctor(Markdig.Extensions.Abbreviations.Abbreviation)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Abbreviations.AbbreviationInline"/> class.
            </summary>
            <param name="abbreviation">The abbreviation.</param>
        </member>
        <member name="T:Markdig.Extensions.Abbreviations.AbbreviationParser">
            <summary>
            A block parser for abbreviations.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Extensions.Abbreviations.AbbreviationParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Abbreviations.AbbreviationParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Abbreviations.HtmlAbbreviationRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Abbreviations.AbbreviationInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Alerts.AlertBlock">
            <summary>
            A block representing an alert quote block.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Alerts.AlertBlock.#ctor(Markdig.Helpers.StringSlice)">
            <summary>
            Creates a new instance of this block.
            </summary>
            <param name="kind"></param>
        </member>
        <member name="P:Markdig.Extensions.Alerts.AlertBlock.Kind">
            <summary>
            Gets or sets the kind of the alert block (e.g `NOTE`, `TIP`, `IMPORTANT`, `WARNING`, `CAUTION`)
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Alerts.AlertBlock.TriviaSpaceAfterKind">
            <summary>
            Gets or sets the trivia space after the kind.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Alerts.AlertBlockRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Alerts.AlertBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="M:Markdig.Extensions.Alerts.AlertBlockRenderer.#ctor">
            <summary>
            Creates a new instance of this renderer.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Alerts.AlertBlockRenderer.RenderKind">
            <summary>
            Gets of sets a delegate to render the kind of the alert.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Alerts.AlertBlockRenderer.Write(Markdig.Renderers.HtmlRenderer,Markdig.Extensions.Alerts.AlertBlock)">
            <inheritdoc />
        </member>
        <member name="M:Markdig.Extensions.Alerts.AlertBlockRenderer.DefaultRenderKind(Markdig.Renderers.HtmlRenderer,Markdig.Helpers.StringSlice)">
            <summary>
            Renders the kind of the alert.
            </summary>
            <param name="renderer">The HTML renderer.</param>
            <param name="kind">The kind of the alert to render</param>
        </member>
        <member name="T:Markdig.Extensions.Alerts.AlertExtension">
            <summary>
            Extension for adding alerts to a Markdown pipeline.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Alerts.AlertExtension.RenderKind">
            <summary>
            Gets or sets the delegate to render the kind of the alert.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Alerts.AlertExtension.Setup(Markdig.MarkdownPipelineBuilder)">
            <inheritdoc />
        </member>
        <member name="M:Markdig.Extensions.Alerts.AlertExtension.Setup(Markdig.MarkdownPipeline,Markdig.Renderers.IMarkdownRenderer)">
            <inheritdoc />
        </member>
        <member name="T:Markdig.Extensions.Alerts.AlertInlineParser">
            <summary>
            An inline parser for an alert inline (e.g. `[!NOTE]`).
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Extensions.Alerts.AlertInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Alerts.AlertInlineParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.AutoIdentifiers.AutoIdentifierExtension">
            <summary>
            The auto-identifier extension
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="M:Markdig.Extensions.AutoIdentifiers.AutoIdentifierExtension.#ctor(Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.AutoIdentifiers.AutoIdentifierExtension"/> class.
            </summary>
            <param name="options">The options.</param>
        </member>
        <member name="M:Markdig.Extensions.AutoIdentifiers.AutoIdentifierExtension.HeadingBlockParser_Closed(Markdig.Parsers.BlockProcessor,Markdig.Syntax.Block)">
            <summary>
            Process on a new <see cref="T:Markdig.Syntax.HeadingBlock"/>
            </summary>
            <param name="processor">The processor.</param>
            <param name="block">The heading block.</param>
        </member>
        <member name="M:Markdig.Extensions.AutoIdentifiers.AutoIdentifierExtension.CreateLinkInlineForHeading(Markdig.Parsers.InlineProcessor,Markdig.Syntax.LinkReferenceDefinition,Markdig.Syntax.Inlines.Inline)">
            <summary>
            Callback when there is a reference to found to a heading.
            Note that reference are only working if they are declared after.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.AutoIdentifiers.AutoIdentifierExtension.HeadingBlock_ProcessInlinesEnd(Markdig.Parsers.InlineProcessor,Markdig.Syntax.Inlines.Inline)">
            <summary>
            Process the inlines of the heading to create a unique identifier
            </summary>
            <param name="processor">The processor.</param>
            <param name="inline">The inline.</param>
        </member>
        <member name="T:Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions">
            <summary>
            Options for the <see cref="T:Markdig.Extensions.AutoIdentifiers.AutoIdentifierExtension"/>.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions.None">
            <summary>
            No options: does not apply any additional formatting and/or transformations.  
            </summary>
        </member>
        <member name="F:Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions.Default">
            <summary>
            Default (<see cref="F:Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions.AutoLink"/>)
            </summary>
        </member>
        <member name="F:Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions.AutoLink">
            <summary>
            Allows to link to a header by using the same text as the header for the link label. Default is <c>true</c>
            </summary>
        </member>
        <member name="F:Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions.AllowOnlyAscii">
            <summary>
            Allows only ASCII characters in the url (HTML 5 allows to have UTF8 characters). Default is <c>true</c>
            </summary>
        </member>
        <member name="F:Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions.GitHub">
            <summary>
            Renders auto identifiers like GitHub.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.AutoIdentifiers.HeadingLinkReferenceDefinition">
            <summary>
            A link reference definition to a <see cref="T:Markdig.Syntax.HeadingBlock"/> stored at the <see cref="T:Markdig.Syntax.MarkdownDocument"/> level.
            </summary>
            <seealso cref="T:Markdig.Syntax.LinkReferenceDefinition" />
        </member>
        <member name="P:Markdig.Extensions.AutoIdentifiers.HeadingLinkReferenceDefinition.Heading">
            <summary>
            Gets or sets the heading related to this link reference definition.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.AutoLinks.AutoLinkExtension">
            <summary>
            Extension to automatically create <see cref="T:Markdig.Syntax.Inlines.LinkInline"/> when a link url http: or mailto: is found.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="M:Markdig.Extensions.AutoLinks.AutoLinkExtension.#ctor(Markdig.Extensions.AutoLinks.AutoLinkOptions)">
            <summary>
            Extension to automatically create <see cref="T:Markdig.Syntax.Inlines.LinkInline"/> when a link url http: or mailto: is found.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="P:Markdig.Extensions.AutoLinks.AutoLinkOptions.OpenInNewWindow">
            <summary>
            Should the link open in a new window when clicked (false by default)
            </summary>
        </member>
        <member name="P:Markdig.Extensions.AutoLinks.AutoLinkOptions.UseHttpsForWWWLinks">
            <summary>
            Should a www link be prefixed with https:// instead of http:// (false by default)
            </summary>
        </member>
        <member name="T:Markdig.Extensions.AutoLinks.AutoLinkParser">
            <summary>
            The inline parser used to for autolinks.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Extensions.AutoLinks.AutoLinkParser.#ctor(Markdig.Extensions.AutoLinks.AutoLinkOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.AutoLinks.AutoLinkParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Bootstrap.BootstrapExtension">
            <summary>
            Extension for tagging some HTML elements with bootstrap classes.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Citations.CitationExtension">
            <summary>
            Extension for cite ""...""
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.CustomContainers.CustomContainer">
            <summary>
            A block custom container.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
            <seealso cref="T:Markdig.Syntax.IFencedBlock" />
        </member>
        <member name="M:Markdig.Extensions.CustomContainers.CustomContainer.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.CustomContainers.CustomContainer"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.FencedChar">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.OpeningFencedCharCount">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.TriviaAfterFencedChar">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.Info">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.UnescapedInfo">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.TriviaAfterInfo">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.Arguments">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.UnescapedArguments">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.TriviaAfterArguments">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.InfoNewLine">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.TriviaBeforeClosingFence">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Extensions.CustomContainers.CustomContainer.ClosingFencedCharCount">
            <inheritdoc />
        </member>
        <member name="T:Markdig.Extensions.CustomContainers.CustomContainerExtension">
            <summary>
            Extension to allow custom containers.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.CustomContainers.CustomContainerInline">
            <summary>
            An inline custom container
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.ContainerInline" />
            <seealso cref="T:Markdig.Syntax.Inlines.EmphasisInline" />
        </member>
        <member name="T:Markdig.Extensions.CustomContainers.CustomContainerParser">
            <summary>
            The block parser for a <see cref="T:Markdig.Extensions.CustomContainers.CustomContainer"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.FencedBlockParserBase`1" />
        </member>
        <member name="M:Markdig.Extensions.CustomContainers.CustomContainerParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.CustomContainers.CustomContainerParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.CustomContainers.HtmlCustomContainerInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.CustomContainers.CustomContainerInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.CustomContainers.HtmlCustomContainerRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.CustomContainers.CustomContainer"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.DefinitionLists.DefinitionItem">
            <summary>
            A definition item contains zero to multiple <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionTerm"/> 
            and definitions (any <see cref="T:Markdig.Syntax.Block"/>)
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Extensions.DefinitionLists.DefinitionItem.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionItem"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Extensions.DefinitionLists.DefinitionItem.OpeningCharacter">
            <summary>
            Gets or sets the opening character for this definition item (either `:` or `~`)
            </summary>
        </member>
        <member name="T:Markdig.Extensions.DefinitionLists.DefinitionList">
            <summary>
            A definition list contains <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionItem"/> children.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Extensions.DefinitionLists.DefinitionList.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionList"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="T:Markdig.Extensions.DefinitionLists.DefinitionListExtension">
            <summary>
            Extension to allow definition lists
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.DefinitionLists.DefinitionListParser">
            <summary>
            The block parser for a <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionList"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Extensions.DefinitionLists.DefinitionListParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionListParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.DefinitionLists.DefinitionTerm">
            <summary>
            A definition term contains a single line with the term to define.
            </summary>
            <seealso cref="T:Markdig.Syntax.LeafBlock" />
        </member>
        <member name="M:Markdig.Extensions.DefinitionLists.DefinitionTerm.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionTerm"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="T:Markdig.Extensions.DefinitionLists.HtmlDefinitionListRenderer">
            <summary>
            A HTML renderer for <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionList"/>, <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionItem"/> and <see cref="T:Markdig.Extensions.DefinitionLists.DefinitionTerm"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Diagrams.DiagramExtension">
            <summary>
            Extension to allow diagrams.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Emoji.EmojiExtension">
            <summary>
            Extension to allow emoji shortcodes and smileys replacement.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Emoji.EmojiInline">
            <summary>
            An emoji inline.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.Inline" />
        </member>
        <member name="M:Markdig.Extensions.Emoji.EmojiInline.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Emoji.EmojiInline"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Emoji.EmojiInline.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Emoji.EmojiInline"/> class.
            </summary>
            <param name="content">The content.</param>
        </member>
        <member name="P:Markdig.Extensions.Emoji.EmojiInline.Match">
            <summary>
            Gets or sets the original match string (either an emoji shortcode or a text smiley)
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Emoji.EmojiMapping">
            <summary>
            An emoji shortcodes and smileys mapping, to be used by <see cref="T:Markdig.Extensions.Emoji.EmojiParser"/>.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Emoji.EmojiMapping.DefaultEmojisAndSmileysMapping">
            <summary>
            The default emoji shortcodes and smileys mapping.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Emoji.EmojiMapping.DefaultEmojisOnlyMapping">
            <summary>
            The default emoji shortcodes mapping, without smileys.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Emoji.EmojiMapping.GetDefaultEmojiShortcodeToUnicode">
            <summary>
            Returns a new instance of the default emoji shortcode to emoji unicode dictionary.
            It can be used to create a customized <see cref="T:Markdig.Extensions.Emoji.EmojiMapping"/>.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Emoji.EmojiMapping.GetDefaultSmileyToEmojiShortcode">
            <summary>
            Gets a new instance of the default smiley to emoji shortcode dictionary.
            It can be used to create a customized <see cref="T:Markdig.Extensions.Emoji.EmojiMapping"/>.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Emoji.EmojiMapping.#ctor(System.Boolean)">
            <summary>
            Constructs a mapping for the default emoji shortcodes and smileys.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Emoji.EmojiMapping.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Constructs a mapping from a dictionary of emoji shortcodes to unicode, and a dictionary of smileys to emoji shortcodes.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Emoji.EmojiParser">
            <summary>
            The inline parser used for emojis.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Extensions.Emoji.EmojiParser.#ctor(Markdig.Extensions.Emoji.EmojiMapping)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Emoji.EmojiParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.EmphasisExtras.EmphasisExtraExtension">
            <summary>
            Extension for strikethrough, subscript, superscript, inserted and marked.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="M:Markdig.Extensions.EmphasisExtras.EmphasisExtraExtension.#ctor(Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.EmphasisExtras.EmphasisExtraExtension"/> class.
            </summary>
            <param name="options">The options.</param>
        </member>
        <member name="P:Markdig.Extensions.EmphasisExtras.EmphasisExtraExtension.Options">
            <summary>
            Gets the options.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions">
            <summary>
            Options for enabling support for extra emphasis.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions.Default">
            <summary>
            Allows all extra emphasis (default).
            </summary>
        </member>
        <member name="F:Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions.Strikethrough">
            <summary>
            A text that can be strikethrough using the double character ~~
            </summary>
        </member>
        <member name="F:Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions.Subscript">
            <summary>
            A text that can be rendered as a subscript using the character ~
            </summary>
        </member>
        <member name="F:Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions.Superscript">
            <summary>
            A text that can be rendered as a superscript using the character ^
            </summary>
        </member>
        <member name="F:Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions.Inserted">
            <summary>
            A text that can be rendered as inserted using the double character ++
            </summary>
        </member>
        <member name="F:Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions.Marked">
            <summary>
            A text that can be rendered as marked using the double character ==
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Figures.Figure">
            <summary>
            Defines a figure container.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Extensions.Figures.Figure.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Figures.Figure"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Extensions.Figures.Figure.OpeningCharacterCount">
            <summary>
            Gets or sets the opening character count used to open this figure code block.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Figures.Figure.OpeningCharacter">
            <summary>
            Gets or sets the opening character used to open and close this figure code block.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Figures.FigureBlockParser">
            <summary>
            The block parser for a <see cref="T:Markdig.Extensions.Figures.Figure"/> block.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Extensions.Figures.FigureBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.FencedBlockParserBase"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Figures.FigureCaption">
            <summary>
            Defines a figure caption.
            </summary>
            <seealso cref="T:Markdig.Syntax.LeafBlock" />
        </member>
        <member name="M:Markdig.Extensions.Figures.FigureCaption.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Figures.FigureCaption"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="T:Markdig.Extensions.Figures.FigureExtension">
            <summary>
            Extension to allow usage of figures and figure captions.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Figures.HtmlFigureCaptionRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Figures.FigureCaption"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Figures.HtmlFigureRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Figures.Figure"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Footers.FooterBlock">
            <summary>
            A block element for a footer.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Extensions.Footers.FooterBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Footers.FooterBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Extensions.Footers.FooterBlock.OpeningCharacter">
            <summary>
            Gets or sets the opening character used to match this footer (by default it is ^)
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Footers.FooterBlockParser">
            <summary>
            A block parser for a <see cref="T:Markdig.Extensions.Footers.FooterBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Extensions.Footers.FooterBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Footers.FooterBlockParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Footers.FooterExtension">
            <summary>
            Extension that provides footer.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Footers.HtmlFooterBlockRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Footers.FooterBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Footnotes.Footnote">
            <summary>
            A block for a footnote.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="P:Markdig.Extensions.Footnotes.Footnote.Label">
            <summary>
            Gets or sets the label used by this footnote.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Footnotes.Footnote.Order">
            <summary>
            Gets or sets the order of this footnote (determined by the order of the <see cref="T:Markdig.Extensions.Footnotes.FootnoteLink"/> in the document)
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Footnotes.Footnote.Links">
            <summary>
            Gets the links referencing this footnote.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Footnotes.Footnote.LabelSpan">
            <summary>
            The label span
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Footnotes.FootnoteExtension">
            <summary>
            Extension to allow footnotes.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Footnotes.FootnoteGroup">
            <summary>
            A block that contains all the footnotes at the end of a <see cref="T:Markdig.Syntax.MarkdownDocument"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Extensions.Footnotes.FootnoteGroup.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Footnotes.FootnoteGroup"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="T:Markdig.Extensions.Footnotes.FootnoteLink">
            <summary>
            A inline link to a <see cref="P:Markdig.Extensions.Footnotes.FootnoteLink.Footnote"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.Inline" />
        </member>
        <member name="P:Markdig.Extensions.Footnotes.FootnoteLink.IsBackLink">
            <summary>
            Gets or sets a value indicating whether this instance is back link (from a footnote to the link)
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Footnotes.FootnoteLink.Index">
            <summary>
            Gets or sets the global index number of this link.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Footnotes.FootnoteLink.Footnote">
            <summary>
            Gets or sets the footnote this link refers to.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Footnotes.FootnoteLinkReferenceDefinition">
            <summary>
            A link reference definition stored at the <see cref="T:Markdig.Syntax.MarkdownDocument"/> level.
            </summary>
            <seealso cref="T:Markdig.Syntax.LinkReferenceDefinition" />
        </member>
        <member name="P:Markdig.Extensions.Footnotes.FootnoteLinkReferenceDefinition.Footnote">
            <summary>
            Gets or sets the footnote related to this link reference definition.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Footnotes.FootnoteParser">
            <summary>
            The block parser for a <see cref="T:Markdig.Extensions.Footnotes.Footnote"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="F:Markdig.Extensions.Footnotes.FootnoteParser.DocumentKey">
            <summary>
            The key used to store at the document level the pending <see cref="T:Markdig.Extensions.Footnotes.FootnoteGroup"/>
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Footnotes.FootnoteParser.Document_ProcessInlinesEnd(Markdig.Parsers.InlineProcessor,Markdig.Syntax.Inlines.Inline)">
            <summary>
            Add footnotes to the end of the document
            </summary>
            <param name="state">The processor.</param>
            <param name="inline">The inline.</param>
        </member>
        <member name="T:Markdig.Extensions.Footnotes.HtmlFootnoteGroupRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Footnotes.FootnoteGroup"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="M:Markdig.Extensions.Footnotes.HtmlFootnoteGroupRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Footnotes.HtmlFootnoteGroupRenderer"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Footnotes.HtmlFootnoteGroupRenderer.GroupClass">
            <summary>
            Gets or sets the CSS group class used when rendering the &lt;div&gt; of this instance.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Footnotes.HtmlFootnoteLinkRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Footnotes.FootnoteLink"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.GenericAttributes.GenericAttributesExtension">
            <summary>
            Extension that allows to attach HTML attributes to the previous <see cref="T:Markdig.Syntax.Inlines.Inline"/> or current <see cref="T:Markdig.Syntax.Block"/>.
            This extension should be enabled last after enabling other extensions.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.GenericAttributes.GenericAttributesParser">
            <summary>
            An inline parser used to parse a HTML attributes that can be attached to the previous <see cref="T:Markdig.Syntax.Inlines.Inline"/> or current <see cref="T:Markdig.Syntax.Block"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Extensions.GenericAttributes.GenericAttributesParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.GenericAttributes.GenericAttributesParser"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.GenericAttributes.GenericAttributesParser.TryParse(Markdig.Helpers.StringSlice@,Markdig.Renderers.Html.HtmlAttributes@)">
            <summary>
            Tries to extra from the current position of a slice an HTML attributes {...}
            </summary>
            <param name="slice">The slice to parse.</param>
            <param name="attributes">The output attributes or null if not found or invalid</param>
            <returns><c>true</c> if parsing the HTML attributes was successful</returns>
        </member>
        <member name="T:Markdig.Extensions.Globalization.GlobalizationExtension">
            <summary>
            Extension to add support for RTL content.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Hardlines.SoftlineBreakAsHardlineExtension">
            <summary>
            Extension to generate hardline break for softline breaks.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.JiraLinks.JiraLink">
            <summary>
            Model for a JIRA link item
            </summary>
        </member>
        <member name="P:Markdig.Extensions.JiraLinks.JiraLink.ProjectKey">
            <summary>
            JIRA Project Key
            </summary>
        </member>
        <member name="P:Markdig.Extensions.JiraLinks.JiraLink.Issue">
            <summary>
            JIRA Issue Number
            </summary>
        </member>
        <member name="T:Markdig.Extensions.JiraLinks.JiraLinkExtension">
            <summary>
            Simple inline parser extension for Markdig to find, and 
            automatically add links to JIRA issue numbers.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.JiraLinks.JiraLinkInlineParser">
            <summary>
            Finds and replaces JIRA links inline
            </summary>
        </member>
        <member name="T:Markdig.Extensions.JiraLinks.JiraLinkOptions">
            <summary>
            Available options for replacing JIRA links
            </summary>
        </member>
        <member name="P:Markdig.Extensions.JiraLinks.JiraLinkOptions.BaseUrl">
            <summary>
            The base Url (e.g. `https://mycompany.atlassian.net`)
            </summary>
        </member>
        <member name="P:Markdig.Extensions.JiraLinks.JiraLinkOptions.BasePath">
            <summary>
            The base path after the base url (default is `/browse`)
            </summary>
        </member>
        <member name="P:Markdig.Extensions.JiraLinks.JiraLinkOptions.OpenInNewWindow">
            <summary>
            Should the link open in a new window when clicked
            </summary>
        </member>
        <member name="M:Markdig.Extensions.JiraLinks.JiraLinkOptions.GetUrl">
            <summary>
            Gets the full url composed of the <see cref="P:Markdig.Extensions.JiraLinks.JiraLinkOptions.BaseUrl"/> and <see cref="P:Markdig.Extensions.JiraLinks.JiraLinkOptions.BasePath"/> with no trailing `/`
            </summary>
        </member>
        <member name="T:Markdig.Extensions.ListExtras.ListExtraExtension">
            <summary>
            Extension for adding new type of list items (a., A., i., I.)
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.ListExtras.ListExtraItemParser">
            <summary>
            Parser that adds supports for parsing alpha/roman list items (e.g: `a)` or `a.` or `ii.` or `II.`)
            </summary>
            <remarks>
            Note that we don't validate roman numbers.
            </remarks>
            <seealso cref="T:Markdig.Parsers.OrderedListItemParser" />
        </member>
        <member name="M:Markdig.Extensions.ListExtras.ListExtraItemParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.ListExtras.ListExtraItemParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Mathematics.HtmlMathBlockRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Mathematics.MathBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Mathematics.HtmlMathInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Mathematics.MathInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Mathematics.MathBlock">
            <summary>
            A math block.
            </summary>
            <seealso cref="T:Markdig.Syntax.FencedCodeBlock" />
        </member>
        <member name="M:Markdig.Extensions.Mathematics.MathBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Mathematics.MathBlock"/> class.
            </summary>
            <param name="parser">The parser.</param>
        </member>
        <member name="T:Markdig.Extensions.Mathematics.MathBlockParser">
            <summary>
            The block parser for a <see cref="T:Markdig.Extensions.Mathematics.MathBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Extensions.Mathematics.MathBlock" />
        </member>
        <member name="M:Markdig.Extensions.Mathematics.MathBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Mathematics.MathBlockParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Mathematics.MathExtension">
            <summary>
            Extension for adding inline mathematics $...$
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Mathematics.MathInline">
            <summary>
            A math inline element.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.EmphasisInline" />
        </member>
        <member name="P:Markdig.Extensions.Mathematics.MathInline.Delimiter">
            <summary>
            Gets or sets the delimiter character used by this code inline.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Mathematics.MathInline.DelimiterCount">
            <summary>
            Gets or sets the delimiter count.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Mathematics.MathInline.Content">
            <summary>
            The content as a <see cref="T:Markdig.Helpers.StringSlice"/>.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Mathematics.MathInlineParser">
            <summary>
            An inline parser for <see cref="T:Markdig.Extensions.Mathematics.MathInline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
            <seealso cref="T:Markdig.Parsers.IPostInlineProcessor" />
        </member>
        <member name="M:Markdig.Extensions.Mathematics.MathInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Mathematics.MathInlineParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Mathematics.MathInlineParser.DefaultClass">
            <summary>
            Gets or sets the default class to use when creating a math inline block.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.MediaLinks.HostProviderBuilder.Create(System.String,System.Func{System.Uri,System.String},System.Boolean,System.String)">
            <summary>
            Create a <see cref="T:Markdig.Extensions.MediaLinks.IHostProvider"/> with delegate handler.
            </summary>
            <param name="hostPrefix">Prefix of host that can be handled.</param>
            <param name="handler">Handler that generate iframe url, if uri cannot be handled, it can return <see langword="null"/>.</param>
            <param name="allowFullScreen">Should the generated iframe has allowfullscreen attribute.</param>
            <param name="iframeClass">"class" attribute of generated iframe.</param>
            <returns>A <see cref="T:Markdig.Extensions.MediaLinks.IHostProvider"/> with delegate handler.</returns>
        </member>
        <member name="T:Markdig.Extensions.MediaLinks.IHostProvider">
            <summary>
            Provides url for media links.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.MediaLinks.IHostProvider.Class">
            <summary>
            "class" attribute of generated iframe.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.MediaLinks.IHostProvider.TryHandle(System.Uri,System.Boolean,System.String@)">
            <summary>
            Generate url for iframe.
            </summary>
            <param name="mediaUri">Input media uri.</param>
            <param name="isSchemaRelative"><see langword="true"/> if <paramref name="mediaUri"/> is a schema relative uri, i.e. uri starts with "//".</param>
            <param name="iframeUrl">Generated url for iframe.</param>
            <seealso href="https://tools.ietf.org/html/rfc3986#section-4.2"/>
        </member>
        <member name="P:Markdig.Extensions.MediaLinks.IHostProvider.AllowFullScreen">
            <summary>
            Should the generated iframe has allowfullscreen attribute.
            </summary>
            <remarks>
            Should be false for audio embedding.
            </remarks>
        </member>
        <member name="T:Markdig.Extensions.MediaLinks.MediaLinkExtension">
            <summary>
            Extension for extending image Markdown links in case a video or an audio file is linked and output proper link.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.MediaLinks.MediaOptions">
            <summary>
            Options for the <see cref="T:Markdig.Extensions.MediaLinks.MediaLinkExtension"/>.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.NonAsciiNoEscape.NonAsciiNoEscapeExtension">
            <summary>
            Extension that will disable URI escape with % characters for non-US-ASCII characters in order to workaround a bug under IE/Edge with local file links containing non US-ASCII chars. DO NOT USE OTHERWISE.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.NoRefLinks.NoFollowLinksExtension">
            <summary>
            Extension to automatically render rel=nofollow to all links in an HTML output.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.PragmaLines.PragmaLineExtension">
            <summary>
            Extension to a span for each line containing the original line id (using id = pragma-line#line_number_zero_based)
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.SelfPipeline.SelfPipelineExtension">
            <summary>
            Extension to enable SelfPipeline, to configure a Markdown parsing/convertion to HTML automatically 
            from an embedded special tag in the input text <code>&lt;!--markdig:extensions--&gt;</code> where extensions is a string
            that specifies the extensions to use for the pipeline as exposed by <see cref="M:Markdig.MarkdownExtensions.Configure(Markdig.MarkdownPipelineBuilder,System.String)"/> extension method
            on the <see cref="T:Markdig.MarkdownPipelineBuilder"/>. This extension will invalidate all other extensions and will override them.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.SelfPipeline.SelfPipelineExtension.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.SelfPipeline.SelfPipelineExtension"/> class.
            </summary>
            <param name="tag">The matching start tag.</param>
            <param name="defaultExtensions">The default extensions.</param>
            <exception cref="T:System.ArgumentException">Tag cannot contain angle brackets</exception>
        </member>
        <member name="P:Markdig.Extensions.SelfPipeline.SelfPipelineExtension.DefaultExtensions">
            <summary>
            Gets the default pipeline to configure if no tag was found in the input text. Default is <c>null</c> (core pipeline).
            </summary>
        </member>
        <member name="P:Markdig.Extensions.SelfPipeline.SelfPipelineExtension.SelfPipelineHintTagStart">
            <summary>
            Gets the self pipeline hint tag start that will be matched.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.SelfPipeline.SelfPipelineExtension.CreatePipelineFromInput(System.String)">
            <summary>
            Creates a pipeline automatically configured from an input markdown based on the presence of the configuration tag.
            </summary>
            <param name="inputText">The input text.</param>
            <returns>The pipeline configured from the input</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Markdig.Extensions.SmartyPants.HtmlSmartyPantRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.SmartyPants.SmartyPant"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="M:Markdig.Extensions.SmartyPants.HtmlSmartyPantRenderer.#ctor(Markdig.Extensions.SmartyPants.SmartyPantOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.SmartyPants.HtmlSmartyPantRenderer"/> class.
            </summary>
            <param name="options">The options.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Markdig.Extensions.SmartyPants.SmartyPant">
            <summary>
            An inline for SmartyPant.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.SmartyPants.SmartyPant.ToString">
            <summary>
            Converts this instance to a literal text.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Markdig.Extensions.SmartyPants.SmartyPantOptions">
            <summary>
            The options used for <see cref="T:Markdig.Extensions.SmartyPants.SmartyPantsExtension"/>.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.SmartyPants.SmartyPantOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.SmartyPants.SmartyPantOptions"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.SmartyPants.SmartyPantOptions.Mapping">
            <summary>
            Gets the mapping between a <see cref="T:Markdig.Extensions.SmartyPants.SmartyPantType"/> and its textual representation
            (usually an HTML entity).
            </summary>
        </member>
        <member name="T:Markdig.Extensions.SmartyPants.SmartyPantsExtension">
            <summary>
            Extension to enable SmartyPants.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.SmartyPants.SmartyPantsExtension.#ctor(Markdig.Extensions.SmartyPants.SmartyPantOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.SmartyPants.SmartyPantsExtension"/> class.
            </summary>
            <param name="options">The options.</param>
        </member>
        <member name="P:Markdig.Extensions.SmartyPants.SmartyPantsExtension.Options">
            <summary>
            Gets the options.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.SmartyPants.SmartyPantsInlineParser">
            <summary>
            The inline parser for SmartyPants.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.SmartyPants.SmartyPantsInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.SmartyPants.SmartyPantsInlineParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.SmartyPants.SmartyPantType">
            <summary>
            Types of a <see cref="T:Markdig.Extensions.SmartyPants.SmartyPant"/>.
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.Quote">
            <summary>
            This is a single quote '
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.LeftQuote">
            <summary>
            This is a left single quote ' -gt; lsquo;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.RightQuote">
            <summary>
            This is a right single quote ' -gt; rsquo;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.DoubleQuote">
            <summary>
            This is a double quote "
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.LeftDoubleQuote">
            <summary>
            This is a left double quote " -gt; ldquo;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.RightDoubleQuote">
            <summary>
            This is a right double quote " -gt; rdquo;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.LeftAngleQuote">
            <summary>
            This is a right double quote &lt;&lt; -gt; laquo;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.RightAngleQuote">
            <summary>
            This is a right angle quote >> -gt;  raquo;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.Ellipsis">
            <summary>
            This is an ellipsis ... -gt; hellip;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.Dash2">
            <summary>
            This is a ndash -- -gt; ndash;
            </summary>
        </member>
        <member name="F:Markdig.Extensions.SmartyPants.SmartyPantType.Dash3">
            <summary>
            This is a mdash --- -gt; mdash;
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.GridTableExtension">
            <summary>
            Extension that allows to use grid tables.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Tables.GridTableState">
            <summary>
            Internal state used by the <see cref="T:Markdig.Extensions.Tables.GridTableParser"/>
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Tables.GridTableState.#ctor(System.Int32,System.Boolean)">
            <summary>
            Internal state used by the <see cref="T:Markdig.Extensions.Tables.GridTableParser"/>
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.GridTableState.ColumnSlice.Start">
            <summary>
            Gets or sets the index position of this column (after the |)
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.HtmlTableRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.Tables.Table"/>
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Tables.PipeTableBlockParser">
            <summary>
            This block parsers for pipe tables is used to by-pass list items that could start by a single '-'
            and would disallow to detect a pipe tables at inline parsing time, so we are basically forcing a line
            that starts by a '-' and have at least a '|' (and have optional spaces) and is a continuation of a
            paragraph.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Extensions.Tables.PipeTableBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.PipeTableBlockParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.PipeTableDelimiterInline">
            <summary>
            The delimiter used to separate the columns of a pipe table.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.DelimiterInline" />
        </member>
        <member name="P:Markdig.Extensions.Tables.PipeTableDelimiterInline.LocalLineIndex">
            <summary>
            Gets or sets the index of line where this delimiter was found relative to the current block.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.PipeTableExtension">
            <summary>
            Extension that allows to use pipe tables.
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="M:Markdig.Extensions.Tables.PipeTableExtension.#ctor(Markdig.Extensions.Tables.PipeTableOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.PipeTableExtension"/> class.
            </summary>
            <param name="options">The options.</param>
        </member>
        <member name="P:Markdig.Extensions.Tables.PipeTableExtension.Options">
            <summary>
            Gets the options.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.PipeTableOptions">
            <summary>
            Options for the extension <see cref="T:Markdig.Extensions.Tables.PipeTableExtension"/>
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Tables.PipeTableOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.PipeTableOptions"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.PipeTableOptions.RequireHeaderSeparator">
            <summary>
            Gets or sets a value indicating whether to require header separator. <c>true</c> by default (Kramdown is using <c>false</c>)
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.PipeTableOptions.UseHeaderForColumnCount">
             <summary>
             Defines whether table should be normalized to the amount of columns as defined in the table header.
             <c>false</c> by default
            
             If <c>true</c>, this will insert empty cells in rows with fewer tables than the header row and remove cells
             that are exceeding the header column count.
             If <c>false</c>, this will use the row with the most columns to determine how many cells should be inserted
             in all other rows (default behavior).
             </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.PipeTableParser">
            <summary>
            The inline parser used to transform a <see cref="T:Markdig.Syntax.ParagraphBlock"/> into a <see cref="T:Markdig.Extensions.Tables.Table"/> at inline parsing time.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
            <seealso cref="T:Markdig.Parsers.IPostInlineProcessor" />
        </member>
        <member name="M:Markdig.Extensions.Tables.PipeTableParser.#ctor(Markdig.Parsers.Inlines.LineBreakInlineParser,Markdig.Extensions.Tables.PipeTableOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.PipeTableParser" /> class.
            </summary>
            <param name="lineBreakParser">The line break parser to use</param>
            <param name="options">The options.</param>
        </member>
        <member name="P:Markdig.Extensions.Tables.PipeTableParser.Options">
            <summary>
            Gets the options.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.Table">
            <summary>
            Defines a table that contains an optional <see cref="T:Markdig.Extensions.Tables.TableRow"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Extensions.Tables.Table.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.Table"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Tables.Table.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.Table"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Extensions.Tables.Table.ColumnDefinitions">
            <summary>
            Gets or sets the column alignments. May be null.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Tables.Table.IsValid">
            <summary>
            Checks if the table structure is valid.
            </summary>
            <returns><c>True</c> if the table has rows and the number of cells per row is correct, other wise <c>false</c>.</returns>
        </member>
        <member name="M:Markdig.Extensions.Tables.Table.NormalizeUsingMaxWidth">
            <summary>
            Normalizes the number of columns of this table by taking the maximum columns and appending empty cells.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Tables.Table.NormalizeUsingHeaderRow">
            <summary>
            Normalizes the number of columns of this table by taking the amount of columns defined in the header
            and appending empty cells or removing extra cells as needed.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.TableCell">
            <summary>
            Defines a cell in a <see cref="T:Markdig.Extensions.Tables.TableRow"/>
            </summary>
            <seealso cref="T:Markdig.Syntax.LeafBlock" />
        </member>
        <member name="M:Markdig.Extensions.Tables.TableCell.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.TableCell"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Tables.TableCell.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.TableCell"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Extensions.Tables.TableCell.ColumnIndex">
            <summary>
            Gets or sets the index of the column to which this cell belongs.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.TableCell.ColumnSpan">
            <summary>
            Gets or sets the column span this cell is covering. Default is 1.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.TableCell.RowSpan">
            <summary>
            Gets or sets the row span this cell is covering. Default is 1.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.TableCell.AllowClose">
            <summary>
            Gets or sets whether this cell can be closed.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.TableColumnAlign">
            <summary>
            Defines the alignment of a column
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Tables.TableColumnAlign.Left">
            <summary>
            Align the column to the left
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Tables.TableColumnAlign.Center">
            <summary>
            Align the column to the center
            </summary>
        </member>
        <member name="F:Markdig.Extensions.Tables.TableColumnAlign.Right">
            <summary>
            Align the column to the right
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.TableColumnDefinition">
            <summary>
            Defines a column.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.TableColumnDefinition.Width">
            <summary>
            Gets or sets the width (in percentage) of this column. A value of 0 is unspecified.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.TableColumnDefinition.Alignment">
            <summary>
            Gets or sets the column alignment.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Tables.TableHelper">
            <summary>
            Helper methods for parsing tables.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Tables.TableHelper.ParseColumnHeader(Markdig.Helpers.StringSlice@,System.Char,System.Nullable{Markdig.Extensions.Tables.TableColumnAlign}@)">
            <summary>
            Parses a column header equivalent to the regexp: <code>\s*:\s*[delimiterChar]+\s*:\s*</code>
            </summary>
            <param name="slice">The text slice.</param>
            <param name="delimiterChar">The delimiter character (either `-` or `=`).</param>
            <param name="align">The alignment of the column.</param>
            <returns>
              <c>true</c> if parsing was successful
            </returns>
        </member>
        <member name="M:Markdig.Extensions.Tables.TableHelper.ParseColumnHeaderAuto(Markdig.Helpers.StringSlice@,System.Char@,System.Nullable{Markdig.Extensions.Tables.TableColumnAlign}@)">
            <summary>
            Parses a column header equivalent to the regexp: <code>\s*:\s*[delimiterChar]+\s*:\s*</code>
            </summary>
            <param name="slice">The text slice.</param>
            <param name="delimiterChar">The delimiter character (either `-` or `=`).</param>
            <param name="align">The alignment of the column.</param>
            <returns>
              <c>true</c> if parsing was successful
            </returns>
        </member>
        <member name="M:Markdig.Extensions.Tables.TableHelper.ParseColumnHeaderDetect(Markdig.Helpers.StringSlice@,System.Char@,System.Nullable{Markdig.Extensions.Tables.TableColumnAlign}@)">
            <summary>
            Parses a column header equivalent to the regexp: <code>\s*:\s*[delimiterChar]+\s*:\s*</code>
            </summary>
            <param name="slice">The text slice.</param>
            <param name="delimiterChar">The delimiter character (either `-` or `=`). If `\0`, it will detect the character (either `-` or `=`)</param>
            <param name="align">The alignment of the column.</param>
            <returns>
              <c>true</c> if parsing was successful
            </returns>
        </member>
        <member name="T:Markdig.Extensions.Tables.TableRow">
            <summary>
            Defines a row in a <see cref="T:Markdig.Extensions.Tables.Table"/>, contains <see cref="T:Markdig.Extensions.Tables.TableCell"/>, parent is <see cref="T:Markdig.Extensions.Tables.Table"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Extensions.Tables.TableRow.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Tables.TableRow"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Tables.TableRow.IsHeader">
            <summary>
            Gets or sets a value indicating whether this instance is header row.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.TaskLists.HtmlTaskListRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Extensions.TaskLists.TaskList"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.TaskLists.TaskList">
            <summary>
            An inline for TaskList.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.TaskLists.TaskListExtension">
            <summary>
            Extension to enable TaskList.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.TaskLists.TaskListInlineParser">
            <summary>
            The inline parser for SmartyPants.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.TaskLists.TaskListInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.TaskLists.TaskListInlineParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.TaskLists.TaskListInlineParser.ListClass">
            <summary>
            Gets or sets the list class used for a task list.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.TaskLists.TaskListInlineParser.ListItemClass">
            <summary>
            Gets or sets the list item class used for a task list.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.TextRenderer.ConfigureNewLineExtension">
            <summary>
            Extension that allows setting line-endings for any IMarkdownRenderer
            that inherits from <see cref="T:Markdig.Renderers.TextRendererBase"/>
            </summary>
            <seealso cref="T:Markdig.IMarkdownExtension" />
        </member>
        <member name="T:Markdig.Extensions.Yaml.YamlFrontMatterBlock">
            <summary>
            A YAML frontmatter block.
            </summary>
            <seealso cref="T:Markdig.Syntax.CodeBlock" />
        </member>
        <member name="M:Markdig.Extensions.Yaml.YamlFrontMatterBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Yaml.YamlFrontMatterBlock"/> class.
            </summary>
            <param name="parser">The parser.</param>
        </member>
        <member name="T:Markdig.Extensions.Yaml.YamlFrontMatterExtension">
            <summary>
            Extension to discard a YAML frontmatter at the beginning of a Markdown document.
            </summary>
        </member>
        <member name="P:Markdig.Extensions.Yaml.YamlFrontMatterExtension.AllowInMiddleOfDocument">
            <summary>
            Allows the <see cref="T:Markdig.Extensions.Yaml.YamlFrontMatterBlock"/> to appear in the middle of the markdown file.
            </summary>
        </member>
        <member name="T:Markdig.Extensions.Yaml.YamlFrontMatterHtmlRenderer">
            <summary>
            Empty renderer for a <see cref="T:Markdig.Extensions.Yaml.YamlFrontMatterBlock"/>
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Extensions.Yaml.YamlFrontMatterParser">
            <summary>
            Block parser for a YAML frontmatter.
            </summary>
            <seealso cref="T:Markdig.Extensions.Yaml.YamlFrontMatterBlock" />
        </member>
        <member name="P:Markdig.Extensions.Yaml.YamlFrontMatterParser.AllowInMiddleOfDocument">
            <summary>
            Allows the <see cref="T:Markdig.Extensions.Yaml.YamlFrontMatterBlock"/> to appear in the middle of the markdown file.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Yaml.YamlFrontMatterParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Extensions.Yaml.YamlFrontMatterParser"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Extensions.Yaml.YamlFrontMatterParser.CreateFrontMatterBlock(Markdig.Parsers.BlockProcessor)">
            <summary>
            Creates the front matter block.
            </summary>
            <param name="processor">The block processor</param>
            <returns>The front matter block</returns>
        </member>
        <member name="M:Markdig.Extensions.Yaml.YamlFrontMatterParser.TryOpen(Markdig.Parsers.BlockProcessor)">
            <summary>
            Tries to match a block opening.
            </summary>
            <param name="processor">The parser processor.</param>
            <returns>The result of the match</returns>
        </member>
        <member name="M:Markdig.Extensions.Yaml.YamlFrontMatterParser.TryContinue(Markdig.Parsers.BlockProcessor,Markdig.Syntax.Block)">
            <summary>
            Tries to continue matching a block already opened.
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="block">The block already opened.</param>
            <returns>The result of the match. By default, don't expect any newline</returns>
        </member>
        <member name="T:Markdig.Helpers.CharacterMap`1">
            <summary>
            Allows to associate characters to a data structures and query efficiently for them.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:Markdig.Helpers.CharacterMap`1.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.Char,`0}})">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.CharacterMap`1"/> class.
            </summary>
            <param name="maps">The states.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Markdig.Helpers.CharacterMap`1.OpeningCharacters">
            <summary>
            Gets all the opening characters defined.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CharacterMap`1.Item(System.UInt32)">
            <summary>
            Gets the list of parsers valid for the specified opening character.
            </summary>
            <param name="openingChar">The opening character.</param>
            <returns>A list of parsers valid for the specified opening character or null if no parsers registered.</returns>
        </member>
        <member name="M:Markdig.Helpers.CharacterMap`1.IndexOfOpeningCharacter(System.String,System.Int32,System.Int32)">
            <summary>
            Searches for an opening character from a registered parser in the specified string.
            </summary>
            <param name="text">The text.</param>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
            <returns>Index position within the string of the first opening character found in the specified text; if not found, returns -1</returns>
        </member>
        <member name="T:Markdig.Helpers.CharHelper">
            <summary>
            Helper class for handling characters.
            </summary>
        </member>
        <member name="T:Markdig.Helpers.CharNormalizer">
            <summary>
            Class used to simplify a unicode char to a simple ASCII string
            </summary>
        </member>
        <member name="M:Markdig.Helpers.CharNormalizer.ConvertToAscii(System.Char)">
            <summary>
            Converts a unicode char to a simple ASCII string.
            </summary>
            <param name="c">The input char.</param>
            <returns>The simple ASCII string or null if the char itself cannot be simplified</returns>
        </member>
        <member name="T:Markdig.Helpers.CompactPrefixTree`1">
            <summary>
            A compact insert-only key/value collection for fast prefix lookups
            <para>Something between a Trie and a full Radix tree, but stored linearly in memory</para>
            </summary>
            <typeparam name="TValue">The value associated with the key</typeparam>
        </member>
        <member name="T:Markdig.Helpers.CompactPrefixTree`1.InsertionBehavior">
            <summary>
            Used internally to control behavior of insertion
            <para>Copied from <see cref="T:System.Collections.Generic.Dictionary`2"/> internals</para>
            </summary>
        </member>
        <member name="F:Markdig.Helpers.CompactPrefixTree`1.InsertionBehavior.None">
            <summary>
            The default insertion behavior. Does not overwrite or throw.
            </summary>
        </member>
        <member name="F:Markdig.Helpers.CompactPrefixTree`1.InsertionBehavior.OverwriteExisting">
            <summary>
            Specifies that an existing entry with the same key should be overwritten if encountered.
            </summary>
        </member>
        <member name="F:Markdig.Helpers.CompactPrefixTree`1.InsertionBehavior.ThrowOnExisting">
            <summary>
            Specifies that if an existing entry with the same key is encountered, an exception should be thrown.
            </summary>
        </member>
        <member name="F:Markdig.Helpers.CompactPrefixTree`1.Node.Char">
            <summary>
            The character this node represents, should never be 0
            </summary>
        </member>
        <member name="F:Markdig.Helpers.CompactPrefixTree`1.Node.ChildChar">
            <summary>
            Will be 0 if this is a leaf node
            </summary>
        </member>
        <member name="F:Markdig.Helpers.CompactPrefixTree`1.Node.MatchIndex">
            <summary>
            Set to -1 if it does not point to a match
            </summary>
        </member>
        <member name="F:Markdig.Helpers.CompactPrefixTree`1.Node.Children">
            <summary>
            -1 if not present
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.TreeSize">
            <summary>
            Gets the number of nodes in the internal tree structure
            <para>You might be looking for <see cref="P:Markdig.Helpers.CompactPrefixTree`1.Count"/></para>
            <para>Exposing this might help in deducing more efficient initial parameters</para>
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.TreeCapacity">
            <summary>
            Gets or sets the capacity of the internal tree structure buffer
            <para>You might be looking for <see cref="P:Markdig.Helpers.CompactPrefixTree`1.Capacity"/></para>
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Count">
            <summary>
            Gets the number of key/value pairs contained in the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Capacity">
            <summary>
            Gets or sets the capacity of the internal key/value pair buffer
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.ChildrenCount">
            <summary>
            Gets the size of the children buffer in the internal tree structure
            <para>You might be looking for <see cref="P:Markdig.Helpers.CompactPrefixTree`1.Count"/></para>
            <para>Exposing this might help in deducing more efficient initial parameters</para>
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.ChildrenCapacity">
            <summary>
            Gets or sets the capacity of the internal children buffer
            <para>You might be looking for <see cref="P:Markdig.Helpers.CompactPrefixTree`1.Capacity"/></para>
            </summary>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Constructs a new <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/> with no initial prefixes
            </summary>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.#ctor(System.Collections.Generic.ICollection{System.Collections.Generic.KeyValuePair{System.String,`0}})">
            <summary>
            Constructs a new <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/> with the supplied matches
            </summary>
            <param name="input">Matches to initialize the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/> with. For best lookup performance, this collection should be sorted.</param>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Item(System.Int32)">
            <summary>
            Retrieves the key/value pair at the specified index (must be lower than <see cref="P:Markdig.Helpers.CompactPrefixTree`1.Count"/>)
            </summary>
            <param name="index">Index of pair to get, must be lower than <see cref="P:Markdig.Helpers.CompactPrefixTree`1.Count"/> (the order is the same as the order in which the elements were added)</param>
            <returns>The key/value pair of the element at the specified index</returns>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Item(System.String)">
            <summary>
            Gets or sets the value associated with the specified key
            </summary>
            <param name="key">The key of the value to get or set</param>
            <returns>The value of the element with the specified key</returns>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Item(System.ReadOnlySpan{System.Char})">
            <summary>
            Gets the value associated with the specified key
            </summary>
            <param name="key">The key of the value to get</param>
            <returns>The key/value pair of the element with the specified key</returns>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.Add(System.String,`0)">
            <summary>
            Adds the specified key/value pair to the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
            <param name="key">The key of the element to add</param>
            <param name="value">The value of the element to add</param>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.Add(System.Collections.Generic.KeyValuePair{System.String,`0})">
            <summary>
            Adds the specified key/value pair to the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
            <param name="pair">The key/value pair to add</param>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.TryAdd(System.String,`0)">
            <summary>
            Tries to add the key/value pair to the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/> if the key is not yet present
            </summary>
            <param name="key">The key of the element to add</param>
            <param name="value">The value of the element to add</param>
            <returns>True if the element was added, false otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.TryAdd(System.Collections.Generic.KeyValuePair{System.String,`0})">
            <summary>
            Tries to add the key/value pair to the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/> if the key is not yet present
            </summary>
            <param name="pair">The pair to add</param>
            <returns>True if the element was added, false otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.TryMatchLongest(System.ReadOnlySpan{System.Char},System.Collections.Generic.KeyValuePair{System.String,`0}@)">
            <summary>
            Tries to find the longest prefix of text, that is contained in this <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
            <param name="text">The text in which to search for the prefix</param>
            <param name="match">The found prefix and the corresponding value</param>
            <returns>True if a match was found, false otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.TryMatchExact(System.ReadOnlySpan{System.Char},System.Collections.Generic.KeyValuePair{System.String,`0}@)">
            <summary>
            Tries to find a prefix of text, that is contained in this <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/> and is exactly text.Length characters long
            </summary>
            <param name="text">The text in which to search for the prefix</param>
            <param name="match">The found prefix and the corresponding value</param>
            <returns>True if a match was found, false otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.TryMatchShortest(System.ReadOnlySpan{System.Char},System.Collections.Generic.KeyValuePair{System.String,`0}@)">
            <summary>
            Tries to find the shortest prefix of text, that is contained in this <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
            <param name="text">The text in which to search for the prefix</param>
            <param name="match">The found prefix and the corresponding value</param>
            <returns>True if a match was found, false otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.ContainsKey(System.String)">
            <summary>
            Determines whether the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/> contains the specified key
            </summary>
            <param name="key">The key to locate in this <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/></param>
            <returns>True if the key is contained in this PrefixTree, false otherwise.</returns>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.TryGetValue(System.String,`0@)">
            <summary>
            Gets the value associated with the specified key
            </summary>
            <param name="key">The key of the value to get</param>
            <param name="value">The value associated with the specified key</param>
            <returns>True if the key is contained in this PrefixTree, false otherwise.</returns>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Keys">
            <summary>
            Gets a collection containing the keys in this <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Values">
            <summary>
            Gets a collection containing the values in this <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.GetEnumerator">
            <summary>
            Returns an Enumerator that iterates through the <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>.
            <para>Use the index accessor instead (<see cref="P:Markdig.Helpers.CompactPrefixTree`1.Item(System.Int32)"/>)</para>
            </summary>
            <returns></returns>
        </member>
        <member name="T:Markdig.Helpers.CompactPrefixTree`1.Enumerator">
            <summary>
            Enumerates the elements of a <see cref="T:Markdig.Helpers.CompactPrefixTree`1"/>
            </summary>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.Enumerator.MoveNext">
            <summary>
            Increments the internal index
            </summary>
            <returns>True if the index is less than the length of the internal array</returns>
        </member>
        <member name="P:Markdig.Helpers.CompactPrefixTree`1.Enumerator.Current">
            <summary>
            Gets the <see cref="T:System.Collections.Generic.KeyValuePair`2"/> at the current position
            </summary>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.Enumerator.Dispose">
            <summary>
            Does nothing
            </summary>
        </member>
        <member name="M:Markdig.Helpers.CompactPrefixTree`1.Enumerator.Reset">
            <summary>
            Resets the internal index to the beginning of the array
            </summary>
        </member>
        <member name="T:Markdig.Helpers.DefaultObjectCache`1">
            <summary>
            A default object cache that expect the type {T} to provide a parameter less constructor
            </summary>
            <typeparam name="T">The type of item to cache</typeparam>
            <seealso cref="T:Markdig.Helpers.ObjectCache`1" />
        </member>
        <member name="T:Markdig.Helpers.EntityHelper">
            <summary>
            Helper class to decode an entity.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.EntityHelper.DecodeEntity(System.ReadOnlySpan{System.Char})">
            <summary>
            Decodes the given HTML entity to the matching Unicode characters.
            </summary>
            <param name="entity">The entity without <c>&amp;</c> and <c>;</c> symbols, for example, <c>copy</c>.</param>
            <returns>The unicode character set or <c>null</c> if the entity was not recognized.</returns>
        </member>
        <member name="M:Markdig.Helpers.EntityHelper.DecodeEntity(System.Int32)">
            <summary>
            Decodes the given UTF-32 character code to the matching set of UTF-16 characters.
            </summary>
            <returns>The unicode character set or <c>null</c> if the entity was not recognized.</returns>
        </member>
        <member name="F:Markdig.Helpers.EntityHelper.EntityMap">
            <summary>
            Source: http://www.w3.org/html/wg/drafts/html/master/syntax.html#named-character-references
            </summary>
        </member>
        <member name="T:Markdig.Helpers.HtmlHelper">
            <summary>
            Helper to parse several HTML tags.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.HtmlHelper.Unescape(System.String,System.Boolean)">
            <summary>
            Destructively unescape a string: remove backslashes before punctuation or symbol characters.
            </summary>
            <param name="text">The string data that will be changed by unescaping any punctuation or symbol characters.</param>
            <param name="removeBackSlash">if set to <c>true</c> [remove back slash].</param>
            <returns></returns>
        </member>
        <member name="M:Markdig.Helpers.HtmlHelper.ScanEntity``1(``0,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            Scans an entity.
            Returns number of chars matched.
            </summary>
        </member>
        <member name="T:Markdig.Helpers.ICharIterator">
            <summary>
            Provides a common interface for iterating characters 
            over a <see cref="T:Markdig.Helpers.StringSlice"/> or <see cref="T:Markdig.Helpers.StringLineGroup"/>.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.ICharIterator.Start">
            <summary>
            Gets the current start character position.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.ICharIterator.CurrentChar">
            <summary>
            Gets the current character.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.ICharIterator.End">
            <summary>
            Gets the end character position.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.ICharIterator.NextChar">
            <summary>
            Goes to the next character, incrementing the <see cref="P:Markdig.Helpers.ICharIterator.Start"/> position.
            </summary>
            <returns>The next character. `\0` is end of the iteration.</returns>
        </member>
        <member name="M:Markdig.Helpers.ICharIterator.SkipChar">
            <summary>
            Goes to the next character, incrementing the <see cref="P:Markdig.Helpers.ICharIterator.Start" /> position.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.ICharIterator.PeekChar">
            <summary>
            Peeks at the next character, without incrementing the <see cref="P:Markdig.Helpers.ICharIterator.Start"/> position.
            </summary>
            <returns>The next character. `\0` is end of the iteration.</returns>
        </member>
        <member name="M:Markdig.Helpers.ICharIterator.PeekChar(System.Int32)">
            <summary>
            Peeks at the next character, without incrementing the <see cref="P:Markdig.Helpers.ICharIterator.Start"/> position.
            </summary>
            <param name="offset"></param>
            <returns>The next character. `\0` is end of the iteration.</returns>
        </member>
        <member name="P:Markdig.Helpers.ICharIterator.IsEmpty">
            <summary>
            Gets a value indicating whether this instance is empty.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.ICharIterator.TrimStart">
            <summary>
            Trims whitespaces at the beginning of this slice starting from <see cref="P:Markdig.Helpers.ICharIterator.Start"/> position.
            </summary>
            <returns><c>true</c> if it has reaches the end of the iterator</returns>
        </member>
        <member name="T:Markdig.Helpers.LineReader">
            <summary>
            A line reader from a <see cref="T:System.IO.TextReader"/> that can provide precise source position
            </summary>
        </member>
        <member name="M:Markdig.Helpers.LineReader.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.LineReader"/> class.
            </summary>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentOutOfRangeException">bufferSize cannot be &lt;= 0</exception>
        </member>
        <member name="P:Markdig.Helpers.LineReader.SourcePosition">
            <summary>
            Gets the char position of the line. Valid for the next line before calling <see cref="M:Markdig.Helpers.LineReader.ReadLine"/>.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.LineReader.ReadLine">
            <summary>
            Reads a new line from the underlying <see cref="T:System.IO.TextReader"/> and update the <see cref="P:Markdig.Helpers.LineReader.SourcePosition"/> for the next line.
            </summary>
            <returns>A new line or null if the end of <see cref="T:System.IO.TextReader"/> has been reached</returns>
        </member>
        <member name="T:Markdig.Helpers.LinkHelper">
            <summary>
            Helpers to parse Markdown links.
            </summary>
        </member>
        <member name="T:Markdig.Helpers.NewLine">
            <summary>
            Represents a character or set of characters that represent a separation
            between two lines of text
            </summary>
        </member>
        <member name="T:Markdig.Helpers.ObjectCache`1">
            <summary>
            A simple object recycling system.
            </summary>
            <typeparam name="T">Type of the object to cache</typeparam>
        </member>
        <member name="M:Markdig.Helpers.ObjectCache`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.ObjectCache`1"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.ObjectCache`1.Clear">
            <summary>
            Clears this cache.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.ObjectCache`1.Get">
            <summary>
            Gets a new instance.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Markdig.Helpers.ObjectCache`1.Release(`0)">
            <summary>
            Releases the specified instance.
            </summary>
            <param name="instance">The instance.</param>
            <exception cref="T:System.ArgumentNullException">if instance is null</exception>
        </member>
        <member name="M:Markdig.Helpers.ObjectCache`1.NewInstance">
            <summary>
            Creates a new instance of {T}
            </summary>
            <returns>A new instance of {T}</returns>
        </member>
        <member name="M:Markdig.Helpers.ObjectCache`1.Reset(`0)">
            <summary>
            Resets the specified instance when <see cref="M:Markdig.Helpers.ObjectCache`1.Release(`0)"/> is called before storing back to this cache.
            </summary>
            <param name="instance">The instance.</param>
        </member>
        <member name="T:Markdig.Helpers.OrderedList`1">
            <summary>
            A List that provides methods for inserting/finding before/after. See remarks.
            </summary>
            <typeparam name="T">Type of the list item</typeparam>
            <seealso cref="T:System.Collections.Generic.List`1" />
            <remarks>We use a typed list and don't use extension methods because it would pollute all list implements and the top level namespace.</remarks>
        </member>
        <member name="M:Markdig.Helpers.OrderedList`1.Replace``1(`0)">
            <summary>
            Replaces <typeparamref name="TItem"/> with <paramref name="replacement"/>.
            </summary>
            <typeparam name="TItem">Item type to find in the list</typeparam>
            <param name="replacement">Object to replace this item with</param>
            <returns><c>true</c> if a replacement was made; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Markdig.Helpers.OrderedList`1.ReplaceOrAdd``1(`0)">
            <summary>
            Replaces <typeparamref name="TItem"/> with <paramref name="newItem"/> or adds <paramref name="newItem"/>.
            </summary>
            <typeparam name="TItem">Item type to find in the list</typeparam>
            <param name="newItem">Object to add/replace the found item with</param>
            <returns><c>true</c> if a replacement was made; otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Markdig.Helpers.OrderedList`1.TryRemove``1">
            <summary>
            Removes the first occurrence of <typeparamref name="TItem"/>
            </summary>
        </member>
        <member name="F:Markdig.Helpers.StringBuilderCache.local">
            <summary>
            A StringBuilder that can be used locally in a method body only.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringBuilderCache.Local">
            <summary>
            Provides a string builder that can only be used locally in a method. This StringBuilder MUST not be stored.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Markdig.Helpers.StringBuilderExtensions">
            <summary>
            Extensions for StringBuilder
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringBuilderExtensions.Append(System.Text.StringBuilder,Markdig.Helpers.StringSlice)">
            <summary>
            Appends the specified slice to this <see cref="T:System.Text.StringBuilder"/> instance.
            </summary>
            <param name="builder">The builder.</param>
            <param name="slice">The slice.</param>
        </member>
        <member name="T:Markdig.Helpers.StringLine">
            <summary>
            A struct representing a text line.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringLine.#ctor(Markdig.Helpers.StringSlice@)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringLine"/> struct.
            </summary>
            <param name="slice">The slice.</param>
        </member>
        <member name="M:Markdig.Helpers.StringLine.#ctor(Markdig.Helpers.StringSlice,System.Int32,System.Int32,System.Int32,Markdig.Helpers.NewLine)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringLine"/> struct.
            </summary>
            <param name="slice">The slice.</param>
            <param name="line">The line.</param>
            <param name="column">The column.</param>
            <param name="position">The position.</param>
            <param name="newLine">The line separation.</param>
        </member>
        <member name="M:Markdig.Helpers.StringLine.#ctor(Markdig.Helpers.StringSlice@,System.Int32,System.Int32,System.Int32,Markdig.Helpers.NewLine)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringLine"/> struct.
            </summary>
            <param name="slice">The slice.</param>
            <param name="line">The line.</param>
            <param name="column">The column.</param>
            <param name="position">The position.</param>
            <param name="newLine">The line separation.</param>
        </member>
        <member name="F:Markdig.Helpers.StringLine.Slice">
            <summary>
            The slice used for this line.
            </summary>
        </member>
        <member name="F:Markdig.Helpers.StringLine.Line">
            <summary>
            The line position.
            </summary>
        </member>
        <member name="F:Markdig.Helpers.StringLine.Position">
            <summary>
            The position of the start of this line within the original source code
            </summary>
        </member>
        <member name="F:Markdig.Helpers.StringLine.Column">
            <summary>
            The column position.
            </summary>
        </member>
        <member name="F:Markdig.Helpers.StringLine.NewLine">
            <summary>
            The newline.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringLine.op_Implicit(Markdig.Helpers.StringLine)~Markdig.Helpers.StringSlice">
            <summary>
            Performs an implicit conversion from <see cref="T:Markdig.Helpers.StringLine"/> to <see cref="T:Markdig.Helpers.StringSlice"/>.
            </summary>
            <param name="line">The line.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="T:Markdig.Helpers.StringLineGroup">
            <summary>
            A group of <see cref="T:Markdig.Helpers.StringLine"/>.
            </summary>
            <seealso cref="T:System.Collections.IEnumerable" />
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringLineGroup"/> class.
            </summary>
            <param name="capacity"></param>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringLineGroup"/> class.
            </summary>
            <param name="text">The text.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Markdig.Helpers.StringLineGroup.Lines">
            <summary>
            Gets the lines.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.StringLineGroup.Count">
            <summary>
            Gets the number of lines.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.RemoveAt(System.Int32)">
            <summary>
            Removes the line at the specified index.
            </summary>
            <param name="index">The index.</param>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.Add(Markdig.Helpers.StringLine@)">
            <summary>
            Adds the specified line to this instance.
            </summary>
            <param name="line">The line.</param>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.Add(Markdig.Helpers.StringSlice)">
            <summary>
            Adds the specified slice to this instance.
            </summary>
            <param name="slice">The slice.</param>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.ToSlice(System.Collections.Generic.List{Markdig.Helpers.StringLineGroup.LineOffset})">
            <summary>
            Converts the lines to a single <see cref="T:Markdig.Helpers.StringSlice"/> by concatenating the lines.
            </summary>
            <param name="lineOffsets">The position of the `\n` line offsets from the beginning of the returned slice.</param>
            <returns>A single slice concatenating the lines of this instance</returns>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.ToCharIterator">
            <summary>
            Converts this instance into a <see cref="T:Markdig.Helpers.ICharIterator"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Markdig.Helpers.StringLineGroup.Trim">
            <summary>
            Trims each lines of the specified <see cref="T:Markdig.Helpers.StringLineGroup"/>.
            </summary>
        </member>
        <member name="T:Markdig.Helpers.StringLineGroup.Iterator">
            <summary>
            The iterator used to iterate other the lines.
            </summary>
            <seealso cref="T:Markdig.Helpers.ICharIterator" />
        </member>
        <member name="T:Markdig.Helpers.StringSlice">
            <summary>
            A lightweight struct that represents a slice of a string.
            </summary>
            <seealso cref="T:Markdig.Helpers.ICharIterator" />
        </member>
        <member name="F:Markdig.Helpers.StringSlice.Empty">
            <summary>
            An empty string slice.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringSlice"/> struct.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.#ctor(System.String,Markdig.Helpers.NewLine)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringSlice"/> struct.
            </summary>
            <param name="text">The text.</param>
            <param name="newLine">The line separation.</param>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringSlice"/> struct.
            </summary>
            <param name="text">The text.</param>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.#ctor(System.String,System.Int32,System.Int32,Markdig.Helpers.NewLine)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Helpers.StringSlice"/> struct.
            </summary>
            <param name="text">The text.</param>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
            <param name="newLine">The line separation.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="F:Markdig.Helpers.StringSlice.Text">
            <summary>
            The text of this slice.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.StringSlice.Start">
            <summary>
            Gets or sets the start position within <see cref="F:Markdig.Helpers.StringSlice.Text"/>.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.StringSlice.End">
            <summary>
            Gets or sets the end position (inclusive) within <see cref="F:Markdig.Helpers.StringSlice.Text"/>.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.StringSlice.Length">
            <summary>
            Gets the length.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.StringSlice.CurrentChar">
            <summary>
            Gets the current character.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.StringSlice.IsEmpty">
            <summary>
            Gets a value indicating whether this instance is empty.
            </summary>
        </member>
        <member name="P:Markdig.Helpers.StringSlice.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:System.Char"/> at the specified index.
            </summary>
            <param name="index">The index.</param>
            <returns>A character in the slice at the specified index (not from <see cref="P:Markdig.Helpers.StringSlice.Start"/> but from the begining of the slice)</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.NextChar">
            <summary>
            Goes to the next character, incrementing the <see cref="P:Markdig.Helpers.StringSlice.Start" /> position.
            </summary>
            <returns>
            The next character. `\0` is end of the iteration.
            </returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.SkipChar">
            <summary>
            Goes to the next character, incrementing the <see cref="P:Markdig.Helpers.StringSlice.Start" /> position.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.PeekChar">
            <summary>
            Peeks a character at the offset of 1 from the current <see cref="P:Markdig.Helpers.StringSlice.Start"/> position
            inside the range <see cref="P:Markdig.Helpers.StringSlice.Start"/> and <see cref="P:Markdig.Helpers.StringSlice.End"/>, returns `\0` if outside this range.
            </summary>
            <returns>The character at offset, returns `\0` if none.</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.PeekChar(System.Int32)">
            <summary>
            Peeks a character at the specified offset from the current <see cref="P:Markdig.Helpers.StringSlice.Start"/> position
            inside the range <see cref="P:Markdig.Helpers.StringSlice.Start"/> and <see cref="P:Markdig.Helpers.StringSlice.End"/>, returns `\0` if outside this range.
            </summary>
            <param name="offset">The offset.</param>
            <returns>The character at offset, returns `\0` if none.</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.PeekCharAbsolute(System.Int32)">
            <summary>
            Peeks a character at the specified offset from the current beginning of the string, without taking into account <see cref="P:Markdig.Helpers.StringSlice.Start"/> and <see cref="P:Markdig.Helpers.StringSlice.End"/>
            </summary>
            <returns>The character at offset, returns `\0` if none.</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.PeekCharExtra(System.Int32)">
            <summary>
            Peeks a character at the specified offset from the current begining of the slice
            without using the range <see cref="P:Markdig.Helpers.StringSlice.Start"/> or <see cref="P:Markdig.Helpers.StringSlice.End"/>, returns `\0` if outside the <see cref="F:Markdig.Helpers.StringSlice.Text"/>.
            </summary>
            <param name="offset">The offset.</param>
            <returns>The character at offset, returns `\0` if none.</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.Match(System.String,System.Int32)">
            <summary>
            Matches the specified text.
            </summary>
            <param name="text">The text.</param>
            <param name="offset">The offset.</param>
            <returns><c>true</c> if the text matches; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.Match(System.String,System.Int32,System.Int32)">
            <summary>
            Matches the specified text.
            </summary>
            <param name="text">The text.</param>
            <param name="end">The end.</param>
            <param name="offset">The offset.</param>
            <returns><c>true</c> if the text matches; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.SkipSpacesToEndOfLineOrEndOfDocument">
            <summary>
            Expect spaces until a end of line. Return <c>false</c> otherwise.
            </summary>
            <returns><c>true</c> if whitespaces where matched until a end of line</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.MatchLowercase(System.String,System.Int32)">
            <summary>
            Matches the specified text using lowercase comparison.
            </summary>
            <param name="text">The text.</param>
            <param name="offset">The offset.</param>
            <returns><c>true</c> if the text matches; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.MatchLowercase(System.String,System.Int32,System.Int32)">
            <summary>
            Matches the specified text using lowercase comparison.
            </summary>
            <param name="text">The text.</param>
            <param name="end">The end.</param>
            <param name="offset">The offset.</param>
            <returns><c>true</c> if the text matches; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.IndexOf(System.String,System.Int32,System.Boolean)">
            <summary>
            Searches the specified text within this slice.
            </summary>
            <param name="text">The text.</param>
            <param name="offset">The offset.</param>
            <param name="ignoreCase">true if ignore case</param>
            <returns><c>true</c> if the text was found; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.IndexOf(System.Char)">
            <summary>
            Searches for the specified character within this slice.
            </summary>
            <returns>A value >= 0 if the character was found, otherwise &lt; 0</returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.TrimStart">
            <summary>
            Trims whitespaces at the beginning of this slice starting from <see cref="P:Markdig.Helpers.StringSlice.Start"/> position.
            </summary>
            <returns>
              <c>true</c> if it has reaches the end of the iterator
            </returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.TrimStart(System.Int32@)">
            <summary>
            Trims whitespaces at the beginning of this slice starting from <see cref="P:Markdig.Helpers.StringSlice.Start"/> position.
            </summary>
            <param name="spaceCount">The number of spaces trimmed.</param>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.TrimEnd">
            <summary>
            Trims whitespaces at the end of this slice, starting from <see cref="P:Markdig.Helpers.StringSlice.End"/> position.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.Trim">
            <summary>
            Trims whitespaces from both the start and end of this slice.
            </summary>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents this instance.
            </returns>
        </member>
        <member name="M:Markdig.Helpers.StringSlice.IsEmptyOrWhitespace">
            <summary>
            Determines whether this slice is empty or made only of whitespaces.
            </summary>
            <returns><c>true</c> if this slice is empty or made only of whitespaces; <c>false</c> otherwise</returns>
        </member>
        <member name="T:Markdig.Helpers.ThrowHelper">
            <summary>
            Inspired by CoreLib, taken from https://github.com/MihaZupan/SharpCollections, cc @MihaZupan
            </summary>
        </member>
        <member name="M:Markdig.Helpers.ValueStringBuilder.Grow(System.Int32)">
            <summary>
            Resize the internal buffer either by doubling current buffer size or
            by adding <paramref name="additionalCapacityBeyondPos"/> to
            <see cref="F:Markdig.Helpers.ValueStringBuilder._pos"/> whichever is greater.
            </summary>
            <param name="additionalCapacityBeyondPos">
            Number of chars requested beyond current position.
            </param>
        </member>
        <member name="T:Markdig.IMarkdownExtension">
            <summary>
            Base interface for an extension.
            </summary>
        </member>
        <member name="M:Markdig.IMarkdownExtension.Setup(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Setups this extension for the specified pipeline.
            </summary>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="M:Markdig.IMarkdownExtension.Setup(Markdig.MarkdownPipeline,Markdig.Renderers.IMarkdownRenderer)">
            <summary>
            Setups this extension for the specified renderer.
            </summary>
            <param name="pipeline">The pipeline used to parse the document.</param>
            <param name="renderer">The renderer.</param>
        </member>
        <member name="T:Markdig.Markdown">
            <summary>
            Provides methods for parsing a Markdown string to a syntax tree and converting it to other formats.
            </summary>
        </member>
        <member name="M:Markdig.Markdown.Normalize(System.String,Markdig.Renderers.Normalize.NormalizeOptions,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Normalizes the specified markdown to a normalized markdown text.
            </summary>
            <param name="markdown">The markdown.</param>
            <param name="options">The normalize options</param>
            <param name="pipeline">The pipeline.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>A normalized markdown text.</returns>
        </member>
        <member name="M:Markdig.Markdown.Normalize(System.String,System.IO.TextWriter,Markdig.Renderers.Normalize.NormalizeOptions,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Normalizes the specified markdown to a normalized markdown text.
            </summary>
            <param name="markdown">The markdown.</param>
            <param name="writer">The destination <see cref="T:System.IO.TextWriter"/> that will receive the result of the conversion.</param>
            <param name="options">The normalize options</param>
            <param name="pipeline">The pipeline.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>A normalized markdown text.</returns>
        </member>
        <member name="M:Markdig.Markdown.ToHtml(System.String,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Converts a Markdown string to HTML.
            </summary>
            <param name="markdown">A Markdown text.</param>
            <param name="pipeline">The pipeline used for the conversion.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>The result of the conversion</returns>
            <exception cref="T:System.ArgumentNullException">if markdown variable is null</exception>
        </member>
        <member name="M:Markdig.Markdown.ToHtml(Markdig.Syntax.MarkdownDocument,Markdig.MarkdownPipeline)">
            <summary>
            Converts a Markdown document to HTML.
            </summary>
            <param name="document">A Markdown document.</param>
            <param name="pipeline">The pipeline used for the conversion.</param>
            <returns>The result of the conversion</returns>
            <exception cref="T:System.ArgumentNullException">if markdown document variable is null</exception>
        </member>
        <member name="M:Markdig.Markdown.ToHtml(Markdig.Syntax.MarkdownDocument,System.IO.TextWriter,Markdig.MarkdownPipeline)">
            <summary>
            Converts a Markdown document to HTML.
            </summary>
            <param name="document">A Markdown document.</param>
            <param name="writer">The destination <see cref="T:System.IO.TextWriter"/> that will receive the result of the conversion.</param>
            <param name="pipeline">The pipeline used for the conversion.</param>
            <returns>The result of the conversion</returns>
            <exception cref="T:System.ArgumentNullException">if markdown document variable is null</exception>
        </member>
        <member name="M:Markdig.Markdown.ToHtml(System.String,System.IO.TextWriter,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Converts a Markdown string to HTML and output to the specified writer.
            </summary>
            <param name="markdown">A Markdown text.</param>
            <param name="writer">The destination <see cref="T:System.IO.TextWriter"/> that will receive the result of the conversion.</param>
            <param name="pipeline">The pipeline used for the conversion.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>The Markdown document that has been parsed</returns>
            <exception cref="T:System.ArgumentNullException">if reader or writer variable are null</exception>
        </member>
        <member name="M:Markdig.Markdown.Convert(System.String,Markdig.Renderers.IMarkdownRenderer,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Converts a Markdown string using a custom <see cref="T:Markdig.Renderers.IMarkdownRenderer"/>.
            </summary>
            <param name="markdown">A Markdown text.</param>
            <param name="renderer">The renderer to convert Markdown to.</param>
            <param name="pipeline">The pipeline used for the conversion.</param>
            <param name="context">A parser context used for the parsing.</param>
            <exception cref="T:System.ArgumentNullException">if markdown or writer variable are null</exception>
        </member>
        <member name="M:Markdig.Markdown.Parse(System.String,System.Boolean)">
            <summary>
            Parses the specified markdown into an AST <see cref="T:Markdig.Syntax.MarkdownDocument"/>
            </summary>
            <param name="markdown">The markdown text.</param>
            <param name="trackTrivia">Whether to parse trivia such as whitespace, extra heading characters and unescaped string values.</param>
            <returns>An AST Markdown document</returns>
            <exception cref="T:System.ArgumentNullException">if markdown variable is null</exception>
        </member>
        <member name="M:Markdig.Markdown.Parse(System.String,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Parses the specified markdown into an AST <see cref="T:Markdig.Syntax.MarkdownDocument"/>
            </summary>
            <param name="markdown">The markdown text.</param>
            <param name="pipeline">The pipeline used for the parsing.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>An AST Markdown document</returns>
            <exception cref="T:System.ArgumentNullException">if markdown variable is null</exception>
        </member>
        <member name="M:Markdig.Markdown.ToPlainText(System.String,System.IO.TextWriter,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Converts a Markdown string to Plain text and output to the specified writer.
            </summary>
            <param name="markdown">A Markdown text.</param>
            <param name="writer">The destination <see cref="T:System.IO.TextWriter"/> that will receive the result of the conversion.</param>
            <param name="pipeline">The pipeline used for the conversion.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>The Markdown document that has been parsed</returns>
            <exception cref="T:System.ArgumentNullException">if reader or writer variable are null</exception>
        </member>
        <member name="M:Markdig.Markdown.ToPlainText(System.String,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Converts a Markdown string to Plain text by using a <see cref="T:System.IO.StringWriter"/> .
            </summary>
            <param name="markdown">A Markdown text.</param>
            <param name="pipeline">The pipeline used for the conversion.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>The result of the conversion</returns>
            <exception cref="T:System.ArgumentNullException">if markdown variable is null</exception>
        </member>
        <member name="T:Markdig.MarkdownExtensions">
            <summary>
            Provides extension methods for <see cref="T:Markdig.MarkdownPipelineBuilder"/> to enable several Markdown extensions.
            </summary>
        </member>
        <member name="M:Markdig.MarkdownExtensions.Use``1(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Adds the specified extension to the extensions collection.
            </summary>
            <typeparam name="TExtension">The type of the extension.</typeparam>
            <returns>The instance of <see cref="T:Markdig.MarkdownPipelineBuilder" /></returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.Use``1(Markdig.MarkdownPipelineBuilder,``0)">
            <summary>
            Adds the specified extension instance to the extensions collection.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="extension">The instance of the extension to be added.</param>
            <typeparam name="TExtension">The type of the extension.</typeparam>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseAdvancedExtensions(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses all extensions except the BootStrap, Emoji, SmartyPants and soft line as hard line breaks extensions.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseAlertBlocks(Markdig.MarkdownPipelineBuilder,System.Action{Markdig.Renderers.HtmlRenderer,Markdig.Helpers.StringSlice})">
            <summary>
            Uses this extension to enable alert blocks.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="renderKind">Replace the default renderer for the kind with a custom renderer</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseAutoLinks(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.AutoLinks.AutoLinkOptions)">
            <summary>
            Uses this extension to enable autolinks from text `http://`, `https://`, `ftp://`, `mailto:`, `www.xxx.yyy`
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="options">The options.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseNonAsciiNoEscape(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses this extension to disable URI escape with % characters for non-US-ASCII characters in order to workaround a bug under IE/Edge with local file links containing non US-ASCII chars. DO NOT USE OTHERWISE.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseYamlFrontMatter(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses YAML frontmatter extension that will parse a YAML frontmatter into the MarkdownDocument. Note that they are not rendered by any default HTML renderer.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseSelfPipeline(Markdig.MarkdownPipelineBuilder,System.String,System.String)">
            <summary>
            Uses the self pipeline extension that will detect the pipeline to use from the markdown input that contains a special tag. See <see cref="T:Markdig.Extensions.SelfPipeline.SelfPipelineExtension"/>
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="defaultTag">The default tag to use to match the self pipeline configuration. By default, <see cref="F:Markdig.Extensions.SelfPipeline.SelfPipelineExtension.DefaultTag"/>, meaning that the HTML tag will be &lt;--markdig:extensions--&gt;</param>
            <param name="defaultExtensions">The default extensions to configure if no pipeline setup was found from the Markdown document</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UsePragmaLines(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses pragma lines to output span with an id containing the line number (pragma-line#line_number_zero_based`)
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseDiagrams(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the diagrams extension
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UsePreciseSourceLocation(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses precise source code location (useful for syntax highlighting).
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseTaskLists(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the task list extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseCustomContainers(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the custom container extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseMediaLinks(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.MediaLinks.MediaOptions)">
            <summary>
            Uses the media extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="options">The options.</param>
            <returns>
            The modified pipeline
            </returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseAutoIdentifiers(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.AutoIdentifiers.AutoIdentifierOptions)">
            <summary>
            Uses the auto-identifier extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="options">The options.</param>
            <returns>
            The modified pipeline
            </returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseSmartyPants(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.SmartyPants.SmartyPantOptions)">
            <summary>
            Uses the SmartyPants extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="options">The options.</param>
            <returns>
            The modified pipeline
            </returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseBootstrap(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the bootstrap extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseMathematics(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the math extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseFigures(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the figure extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseAbbreviations(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the custom abbreviation extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseDefinitionLists(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the definition lists extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UsePipeTables(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.Tables.PipeTableOptions)">
            <summary>
            Uses the pipe table extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="options">The options.</param>
            <returns>
            The modified pipeline
            </returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseGridTables(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the grid table extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseCitations(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the cite extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseFooters(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the footer extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseFootnotes(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the footnotes extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseSoftlineBreakAsHardlineBreak(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the softline break as hardline break extension
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseEmphasisExtras(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.EmphasisExtras.EmphasisExtraOptions)">
            <summary>
            Uses the strikethrough superscript, subscript, inserted and marked text extensions.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="options">The options to enable.</param>
            <returns>
            The modified pipeline
            </returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseListExtras(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the list extra extension to add support for `a.`, `A.`, `i.` and `I.` ordered list items.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>
            The modified pipeline
            </returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseGenericAttributes(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Uses the generic attributes extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseEmojiAndSmiley(Markdig.MarkdownPipelineBuilder,System.Boolean)">
            <summary>
            Uses the emojis and smileys extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="enableSmileys">Enable smileys in addition to emoji shortcodes, <c>true</c> by default.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseEmojiAndSmiley(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.Emoji.EmojiMapping)">
            <summary>
            Uses the emojis and smileys extension.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="customEmojiMapping">Enable customization of the emojis and smileys mapping.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseNoFollowLinks(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Add rel=nofollow to all links rendered to HTML.
            </summary>
            <param name="pipeline"></param>
            <returns></returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseJiraLinks(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.JiraLinks.JiraLinkOptions)">
            <summary>
            Automatically link references to JIRA issues
            </summary>
            <param name="pipeline">The pipeline</param>
            <param name="options">Set of required options</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.UseGlobalization(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Adds support for right-to-left content by adding appropriate html attribtues.
            </summary>
            <param name="pipeline">The pipeline</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.DisableHtml(Markdig.MarkdownPipelineBuilder)">
            <summary>
            This will disable the HTML support in the markdown processor (for constraint/safe parsing).
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.Configure(Markdig.MarkdownPipelineBuilder,System.String)">
            <summary>
            Configures the pipeline using a string that defines the extensions to activate.
            </summary>
            <param name="pipeline">The pipeline (e.g: advanced for <see cref="M:Markdig.MarkdownExtensions.UseAdvancedExtensions(Markdig.MarkdownPipelineBuilder)"/>, pipetables+gridtables for <see cref="M:Markdig.MarkdownExtensions.UsePipeTables(Markdig.MarkdownPipelineBuilder,Markdig.Extensions.Tables.PipeTableOptions)"/> and <see cref="M:Markdig.MarkdownExtensions.UseGridTables(Markdig.MarkdownPipelineBuilder)"/></param>
            <param name="extensions">The extensions to activate as a string</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.ConfigureNewLine(Markdig.MarkdownPipelineBuilder,System.String)">
            <summary>
            Configures the string to be used for line-endings, when writing.
            </summary>
            <param name="pipeline">The pipeline.</param>
            <param name="newLine">The string to be used for line-endings.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.DisableHeadings(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Disables parsing of ATX and Setex headings
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>The modified pipeline</returns>
        </member>
        <member name="M:Markdig.MarkdownExtensions.EnableTrackTrivia(Markdig.MarkdownPipelineBuilder)">
            <summary>
            Enables parsing and tracking of trivia characters
            </summary>
            <param name="pipeline">The pipeline.</param>
            <returns>he modified pipeline</returns>
        </member>
        <member name="T:Markdig.MarkdownParserContext">
            <summary>
            Provides a context that can be used as part of parsing Markdown documents.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownParserContext.Properties">
            <summary>
            Gets or sets the context property collection.
            </summary>
        </member>
        <member name="M:Markdig.MarkdownParserContext.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.MarkdownParserContext" /> class.
            </summary>
        </member>
        <member name="T:Markdig.MarkdownPipeline">
            <summary>
            This class is the Markdown pipeline build from a <see cref="T:Markdig.MarkdownPipelineBuilder"/>.
            <para>An instance of <see cref="T:Markdig.MarkdownPipeline"/> is immutable, thread-safe, and should be reused when parsing multiple inputs.</para>
            </summary>
        </member>
        <member name="M:Markdig.MarkdownPipeline.#ctor(Markdig.Helpers.OrderedList{Markdig.IMarkdownExtension},Markdig.Parsers.BlockParserList,Markdig.Parsers.InlineParserList,System.IO.TextWriter,Markdig.Parsers.ProcessDocumentDelegate)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.MarkdownPipeline" /> class.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipeline.Extensions">
            <summary>
            The read-only list of extensions used to build this pipeline.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipeline.TrackTrivia">
            <summary>
            True to parse trivia such as whitespace, extra heading characters and unescaped
            string values.
            </summary>
        </member>
        <member name="M:Markdig.MarkdownPipeline.Setup(Markdig.Renderers.IMarkdownRenderer)">
            <summary>
            Allows to setup a <see cref="T:Markdig.Renderers.IMarkdownRenderer"/>.
            </summary>
            <param name="renderer">The markdown renderer to setup</param>
        </member>
        <member name="T:Markdig.MarkdownPipelineBuilder">
            <summary>
            This class allows to modify the pipeline to parse and render a Markdown document.
            </summary>
            <remarks>NOTE: A pipeline is not thread-safe.</remarks>
        </member>
        <member name="M:Markdig.MarkdownPipelineBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.MarkdownPipeline" /> class.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipelineBuilder.BlockParsers">
            <summary>
            Gets the block parsers.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipelineBuilder.InlineParsers">
            <summary>
            Gets the inline parsers.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipelineBuilder.Extensions">
            <summary>
            Gets the register extensions.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipelineBuilder.PreciseSourceLocation">
            <summary>
            Gets or sets a value indicating whether to enable precise source location (slower parsing but accurate position for block and inline elements)
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipelineBuilder.DebugLog">
            <summary>
            Gets or sets the debug log.
            </summary>
        </member>
        <member name="P:Markdig.MarkdownPipelineBuilder.TrackTrivia">
            <summary>
            True to parse trivia such as whitespace, extra heading characters and unescaped
            string values.
            </summary>
        </member>
        <member name="E:Markdig.MarkdownPipelineBuilder.DocumentProcessed">
            <summary>
            Occurs when a document has been processed after the <see cref="M:Markdig.Parsers.MarkdownParser.Parse(System.String,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)"/> method.
            </summary>
        </member>
        <member name="M:Markdig.MarkdownPipelineBuilder.Build">
            <summary>
            Builds a pipeline from this instance. Once the pipeline is build, it cannot be modified.
            </summary>
            <exception cref="T:System.InvalidOperationException">An extension cannot be null</exception>
        </member>
        <member name="T:Markdig.Parsers.ProcessBlockDelegate">
            <summary>
            Delegates called when processing a block
            </summary>
        </member>
        <member name="T:Markdig.Parsers.BlockParser">
            <summary>
            Base class for a parser of a <see cref="T:Markdig.Syntax.Block"/>
            </summary>
            <seealso cref="T:Markdig.Parsers.ParserBase`1" />
        </member>
        <member name="M:Markdig.Parsers.BlockParser.HasOpeningCharacter(System.Char)">
            <summary>
            Determines whether the specified char is an opening character.
            </summary>
            <param name="c">The character.</param>
            <returns><c>true</c> if the specified char is an opening character.</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockParser.CanInterrupt(Markdig.Parsers.BlockProcessor,Markdig.Syntax.Block)">
            <summary>
            Determines whether this instance can interrupt the specified block being processed.
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="block">The block being processed.</param>
            <returns><c>true</c> if this parser can interrupt the specified block being processed.</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockParser.TryOpen(Markdig.Parsers.BlockProcessor)">
            <summary>
            Tries to match a block opening.
            </summary>
            <param name="processor">The parser processor.</param>
            <returns>The result of the match</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockParser.TryContinue(Markdig.Parsers.BlockProcessor,Markdig.Syntax.Block)">
            <summary>
            Tries to continue matching a block already opened.
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="block">The block already opened.</param>
            <returns>The result of the match. By default, don't expect any newline</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockParser.Close(Markdig.Parsers.BlockProcessor,Markdig.Syntax.Block)">
            <summary>
            Called when a block matched by this parser is being closed (to allow final computation on the block).
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="block">The block being closed.</param>
            <returns><c>true</c> to keep the block; <c>false</c> to remove it. True by default.</returns>
        </member>
        <member name="T:Markdig.Parsers.BlockParserList">
            <summary>
            A List of <see cref="T:Markdig.Parsers.BlockParser"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.ParserList`2" />
        </member>
        <member name="M:Markdig.Parsers.BlockParserList.#ctor(System.Collections.Generic.IEnumerable{Markdig.Parsers.BlockParser})">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.BlockParserList"/> class.
            </summary>
            <param name="parsers">The parsers.</param>
        </member>
        <member name="T:Markdig.Parsers.BlockProcessor">
            <summary>
            The block processor.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.#ctor(Markdig.Syntax.MarkdownDocument,Markdig.Parsers.BlockParserList,Markdig.MarkdownParserContext,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.BlockProcessor" /> class.
            </summary>
            <param name="document">The document to build blocks into.</param>
            <param name="parsers">The list of parsers.</param>
            <param name="context">A parser context used for the parsing.</param>
            <param name="trackTrivia">Whether to parse trivia such as whitespace, extra heading characters and unescaped string values.</param>
            <exception cref="T:System.ArgumentNullException">
            </exception>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.NewBlocks">
            <summary>
            Gets the new blocks to push. A <see cref="T:Markdig.Parsers.BlockParser"/> is required to push new blocks that it creates to this property.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.Parsers">
            <summary>
            Gets the list of <see cref="T:Markdig.Parsers.BlockParser"/>s configured with this parser state.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.Context">
            <summary>
            Gets the parser context or <c>null</c> if none is available.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.CurrentContainer">
            <summary>
            Gets the current active container.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.CurrentBlock">
            <summary>
            Gets the last block that is opened.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.LastBlock">
            <summary>
            Gets the last block that is created.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.NextContinue">
            <summary>
            Gets the next block in a <see cref="M:Markdig.Parsers.BlockParser.TryContinue(Markdig.Parsers.BlockProcessor,Markdig.Syntax.Block)"/>.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.Document">
            <summary>
            Gets the root document.
            </summary>
        </member>
        <member name="F:Markdig.Parsers.BlockProcessor.Line">
            <summary>
            The current line being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.CurrentLineStartPosition">
            <summary>
            Gets or sets the current line start position.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.LineIndex">
            <summary>
            Gets the index of the line in the source text.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.IsBlankLine">
            <summary>
            Gets a value indicating whether the line is blank (valid only after <see cref="M:Markdig.Parsers.BlockProcessor.ParseIndent"/> has been called).
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.CurrentChar">
            <summary>
            Gets the current character being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.Column">
            <summary>
            Gets or sets the column.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.Start">
            <summary>
            Gets the position of the current character in the line being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.Indent">
            <summary>
            Gets the current indent position (number of columns between the previous indent and the current position).
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.IsCodeIndent">
            <summary>
            Gets a value indicating whether a code indentation is at the beginning of the line being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.ColumnBeforeIndent">
            <summary>
            Gets the column position before the indent occurred.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.StartBeforeIndent">
            <summary>
            Gets the character position before the indent occurred.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.IsLazy">
            <summary>
            Gets a boolean indicating whether the current line being parsed is lazy continuation.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.OpenedBlocks">
            <summary>
            Gets the current stack of <see cref="T:Markdig.Syntax.Block"/> being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.TriviaStart">
            <summary>
            Gets or sets the position of the first character trivia is encountered
            and not yet assigned to a syntax node.
            Trivia: only used when <see cref="P:Markdig.Parsers.BlockProcessor.TrackTrivia"/> is enabled, otherwise 0.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.UseTrivia(System.Int32)">
            <summary>
            Returns trivia that has not yet been assigned to any node and
            advances the position of trivia to the ending position.
            </summary>
            <param name="end">End position of the trivia</param>
            <returns></returns>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.UseLinesBefore">
            <summary>
            Returns the current stack of <see cref="P:Markdig.Parsers.BlockProcessor.LinesBefore"/> to assign it to a <see cref="T:Markdig.Syntax.Block"/>.
            Afterwards, the <see cref="P:Markdig.Parsers.BlockProcessor.LinesBefore"/> is set to null.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.LinesBefore">
            <summary>
            Gets or sets the stack of empty lines not yet assigned to any <see cref="T:Markdig.Syntax.Block"/>.
            An entry may contain an empty <see cref="T:Markdig.Helpers.StringSlice"/>. In that case the
            <see cref="F:Markdig.Helpers.StringSlice.NewLine"/> is relevant. Otherwise, the <see cref="T:Markdig.Helpers.StringSlice"/>
            entry will contain trivia.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.BlockProcessor.TrackTrivia">
            <summary>
            True to parse trivia such as whitespace, extra heading characters and unescaped
            string values.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.GetCurrentContainerOpened">
            <summary>
            Get the current Container that is currently opened
            </summary>
            <returns>The current Container that is currently opened</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.NextChar">
            <summary>
            Returns the next character in the line being processed. Update <see cref="P:Markdig.Parsers.BlockProcessor.Start"/> and <see cref="P:Markdig.Parsers.BlockProcessor.Column"/>.
            </summary>
            <returns>The next character or `\0` if end of line is reached</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.NextColumn">
            <summary>
            Returns the next character in the line taking into space taken by tabs. Update <see cref="P:Markdig.Parsers.BlockProcessor.Start"/> and <see cref="P:Markdig.Parsers.BlockProcessor.Column"/>.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.PeekChar(System.Int32)">
            <summary>
            Peeks a character at the specified offset from the current position in the line.
            </summary>
            <param name="offset">The offset.</param>
            <returns>A character peeked at the specified offset</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.RestartIndent">
            <summary>
            Restarts the indent from the current position.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.ParseIndent">
            <summary>
            Parses the indentation from the current position in the line, updating <see cref="P:Markdig.Parsers.BlockProcessor.StartBeforeIndent"/>,
            <see cref="P:Markdig.Parsers.BlockProcessor.ColumnBeforeIndent"/>, <see cref="P:Markdig.Parsers.BlockProcessor.Start"/> and <see cref="P:Markdig.Parsers.BlockProcessor.Column"/> accordingly
            taking into account space taken by tabs.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.GoToColumn(System.Int32)">
            <summary>
            Moves to the position to the specified column position, taking into account spaces in tabs.
            </summary>
            <param name="newColumn">The new column position to move the cursor to.</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.UnwindAllIndents">
            <summary>
            Unwind any previous indent from the current character back to the first space.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.GoToCodeIndent(System.Int32)">
            <summary>
            Moves to the position to the code indent (<see cref="P:Markdig.Parsers.BlockProcessor.ColumnBeforeIndent"/> + 4 spaces).
            </summary>
            <param name="columnOffset">The column offset to apply to this indent.</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.Open(Markdig.Syntax.Block)">
            <summary>
            Opens the specified block.
            </summary>
            <param name="block">The block.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException">The block must be opened</exception>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.Close(Markdig.Syntax.Block)">
            <summary>
            Force closing the specified block.
            </summary>
            <param name="block">The block.</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.Discard(Markdig.Syntax.Block)">
            <summary>
            Discards the specified block from the stack, remove from its parent.
            </summary>
            <param name="block">The block.</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.ProcessLine(Markdig.Helpers.StringSlice)">
            <summary>
            Processes a new line.
            </summary>
            <param name="newLine">The new line.</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.Close(System.Int32)">
            <summary>
            Closes a block at the specified index.
            </summary>
            <param name="index">The index.</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.CloseAll(System.Boolean)">
            <summary>
            Closes all the blocks opened.
            </summary>
            <param name="force">if set to <c>true</c> [force].</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.OpenAll">
            <summary>
            Mark all blocks in the stack as opened.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.UpdateLastBlockAndContainer(System.Int32)">
            <summary>
            Updates the <see cref="P:Markdig.Parsers.BlockProcessor.CurrentBlock"/> and <see cref="P:Markdig.Parsers.BlockProcessor.CurrentContainer"/>.
            </summary>
            <param name="stackIndex">Index of a block in a stack considered as the last block to update from.</param>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.TryContinueBlocks">
            <summary>
            Tries to continue matching existing opened <see cref="T:Markdig.Syntax.Block"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException">
            A pending parser cannot add a new block when it is not the last pending block
            or
            The NewBlocks is not empty. This is happening if a LeafBlock is not the last to be pushed
            </exception>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.TryOpenBlocks">
            <summary>
            First phase of the process, try to open new blocks.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.TryOpenBlocks(Markdig.Parsers.BlockParser[])">
            <summary>
            Tries to open new blocks using the specified list of <see cref="T:Markdig.Parsers.BlockParser"/>
            </summary>
            <param name="parsers">The parsers.</param>
            <returns><c>true</c> to continue processing the current line</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockProcessor.ProcessNewBlocks(Markdig.Parsers.BlockState,System.Boolean)">
            <summary>
            Processes any new blocks that have been pushed to <see cref="P:Markdig.Parsers.BlockProcessor.NewBlocks"/>.
            </summary>
            <param name="result">The last result of matching.</param>
            <param name="allowClosing">if set to <c>true</c> the processing of a new block will close existing opened blocks].</param>
            <exception cref="T:System.InvalidOperationException">The NewBlocks is not empty. This is happening if a LeafBlock is not the last to be pushed</exception>
        </member>
        <member name="T:Markdig.Parsers.BlockState">
            <summary>
            Defines the result of parsing a line for a <see cref="T:Markdig.Parsers.BlockParser"/>.
            </summary>
        </member>
        <member name="F:Markdig.Parsers.BlockState.None">
            <summary>
            A line is not accepted by this parser.
            </summary>
        </member>
        <member name="F:Markdig.Parsers.BlockState.Skip">
            <summary>
            The parser is skipped.
            </summary>
        </member>
        <member name="F:Markdig.Parsers.BlockState.Continue">
            <summary>
            The parser accepts a line and instruct to continue.
            </summary>
        </member>
        <member name="F:Markdig.Parsers.BlockState.ContinueDiscard">
            <summary>
            The parser accepts a line, instruct to continue but discard the line (not stored on the block)
            </summary>
        </member>
        <member name="F:Markdig.Parsers.BlockState.Break">
            <summary>
            The parser is ending a block, instruct to stop and keep the line being processed.
            </summary>
        </member>
        <member name="F:Markdig.Parsers.BlockState.BreakDiscard">
            <summary>
            The parser is ending a block, instruct to stop and discard the line being processed.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.BlockStateExtensions">
            <summary>
            Extensions used by <see cref="T:Markdig.Parsers.BlockState"/>.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.BlockStateExtensions.IsDiscard(Markdig.Parsers.BlockState)">
            <summary>
            Determines whether this <see cref="T:Markdig.Parsers.BlockState"/> is discarded.
            </summary>
            <param name="blockState">State of the block.</param>
            <returns><c>true</c> if the block state is in discard state</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockStateExtensions.IsContinue(Markdig.Parsers.BlockState)">
            <summary>
            Determines whether this <see cref="T:Markdig.Parsers.BlockState"/> is in a continue state.
            </summary>
            <param name="blockState">State of the block.</param>
            <returns><c>true</c> if the block state is in continue state</returns>
        </member>
        <member name="M:Markdig.Parsers.BlockStateExtensions.IsBreak(Markdig.Parsers.BlockState)">
            <summary>
            Determines whether this <see cref="T:Markdig.Parsers.BlockState"/> is in a break state.
            </summary>
            <param name="blockState">State of the block.</param>
            <returns><c>true</c> if the block state is in break state</returns>
        </member>
        <member name="T:Markdig.Parsers.FencedBlockParserBase.InfoParserDelegate">
            <summary>
            Delegate used to parse the string on the first line after the fenced code block special characters (usually ` or ~)
            </summary>
            <param name="state">The parser processor.</param>
            <param name="line">The being processed line.</param>
            <param name="fenced">The fenced code block.</param>
            <param name="openingCharacter">The opening character for the fenced code block (usually ` or ~)</param>
            <returns><c>true</c> if parsing of the line is successfull; <c>false</c> otherwise</returns>
        </member>
        <member name="P:Markdig.Parsers.FencedBlockParserBase.InfoParser">
            <summary>
            Gets or sets the information parser.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.FencedBlockParserBase.TryParseAttributes">
            <summary>
            A delegates that allows to process attached attributes
            </summary>
        </member>
        <member name="T:Markdig.Parsers.FencedBlockParserBase`1">
            <summary>
            Base parser for fenced blocks (opened by 3 or more character delimiters on a first line, and closed by at least the same number of delimiters)
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Parsers.FencedBlockParserBase`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.FencedBlockParserBase`1"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.FencedBlockParserBase`1.InfoPrefix">
            <summary>
            Gets or sets the language prefix (default is "language-")
            </summary>
        </member>
        <member name="M:Markdig.Parsers.FencedBlockParserBase`1.RoundtripInfoParser(Markdig.Parsers.BlockProcessor,Markdig.Helpers.StringSlice@,Markdig.Syntax.IFencedBlock,System.Char)">
            <summary>
            The roundtrip parser for the information after the fenced code block special characters (usually ` or ~)
            </summary>
            <param name="blockProcessor">The parser processor.</param>
            <param name="line">The line.</param>
            <param name="fenced">The fenced code block.</param>
            <param name="openingCharacter">The opening character for this fenced code block.</param>
            <returns><c>true</c> if parsing of the line is successfull; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Parsers.FencedBlockParserBase`1.DefaultInfoParser(Markdig.Parsers.BlockProcessor,Markdig.Helpers.StringSlice@,Markdig.Syntax.IFencedBlock,System.Char)">
            <summary>
            The default parser for the information after the fenced code block special characters (usually ` or ~)
            </summary>
            <param name="state">The parser processor.</param>
            <param name="line">The line.</param>
            <param name="fenced">The fenced code block.</param>
            <param name="openingCharacter">The opening character for this fenced code block.</param>
            <returns><c>true</c> if parsing of the line is successfull; <c>false</c> otherwise</returns>
        </member>
        <member name="T:Markdig.Parsers.FencedCodeBlockParser">
            <summary>
            Parser for a <see cref="T:Markdig.Syntax.FencedCodeBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Parsers.FencedCodeBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.FencedCodeBlockParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.HeadingBlockParser">
            <summary>
            Block parser for a <see cref="T:Markdig.Syntax.HeadingBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Parsers.HeadingBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.HeadingBlockParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.HeadingBlockParser.MaxLeadingCount">
            <summary>
            Gets or sets the max count of the leading unescaped # characters
            </summary>
        </member>
        <member name="P:Markdig.Parsers.HeadingBlockParser.TryParseAttributes">
            <summary>
            A delegates that allows to process attached attributes after #
            </summary>
        </member>
        <member name="T:Markdig.Parsers.HtmlBlockParser">
            <summary>
            Block parser for a <see cref="T:Markdig.Syntax.HtmlBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Parsers.HtmlBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.HtmlBlockParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.TryParseAttributesDelegate">
            <summary>
            A delegates that allows to process attached attributes at <see cref="T:Markdig.Parsers.BlockParser"/> time.
            </summary>
            <param name="processor">The processor.</param>
            <param name="slice">The slice to look for attached attributes.</param>
            <param name="block">The block.</param>
            <returns><c>true</c> if attributes were found; otherwise <c>false</c></returns>
        </member>
        <member name="T:Markdig.Parsers.IAttributesParseable">
            <summary>
            An interface used to tag <see cref="T:Markdig.Parsers.BlockParser"/> that supports parsing <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/>
            </summary>
        </member>
        <member name="P:Markdig.Parsers.IAttributesParseable.TryParseAttributes">
            <summary>
            A delegates that allows to process attached attributes
            </summary>
        </member>
        <member name="T:Markdig.Parsers.IBlockParser`1">
            <summary>
            Base interface for a <see cref="T:Markdig.Parsers.BlockParser"/>.
            </summary>
            <typeparam name="TProcessor"></typeparam>
            <seealso cref="T:Markdig.Parsers.IMarkdownParser`1" />
        </member>
        <member name="M:Markdig.Parsers.IBlockParser`1.CanInterrupt(`0,Markdig.Syntax.Block)">
            <summary>
            Determines whether this instance can interrupt the specified block being processed.
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="block">The block being processed.</param>
            <returns><c>true</c> if this parser can interrupt the specified block being processed.</returns>
        </member>
        <member name="M:Markdig.Parsers.IBlockParser`1.TryOpen(`0)">
            <summary>
            Tries to match a block opening.
            </summary>
            <param name="processor">The parser processor.</param>
            <returns>The result of the match</returns>
        </member>
        <member name="M:Markdig.Parsers.IBlockParser`1.TryContinue(`0,Markdig.Syntax.Block)">
            <summary>
            Tries to continue matching a block already opened.
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="block">The block already opened.</param>
            <returns>The result of the match. By default, don't expect any newline</returns>
        </member>
        <member name="M:Markdig.Parsers.IBlockParser`1.Close(`0,Markdig.Syntax.Block)">
            <summary>
            Called when a block matched by this parser is being closed (to allow final computation on the block).
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="block">The block being closed.</param>
            <returns><c>true</c> to keep the block; <c>false</c> to remove it. True by default.</returns>
        </member>
        <member name="T:Markdig.Parsers.IInlineParser`1">
            <summary>
            Base interface for parsing an <see cref="T:Markdig.Syntax.Inlines.Inline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
            <seealso cref="T:Markdig.Parsers.InlineProcessor" />
        </member>
        <member name="M:Markdig.Parsers.IInlineParser`1.Match(Markdig.Parsers.InlineProcessor,Markdig.Helpers.StringSlice@)">
            <summary>
            Tries to match the specified slice.
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="slice">The text slice.</param>
            <returns><c>true</c> if this parser found a match; <c>false</c> otherwise</returns>
        </member>
        <member name="T:Markdig.Parsers.IMarkdownParser`1">
            <summary>
            Base interface for a block or inline parser.
            </summary>
            <typeparam name="TProcessor">The type of processor.</typeparam>
        </member>
        <member name="P:Markdig.Parsers.IMarkdownParser`1.OpeningCharacters">
            <summary>
            Gets the opening characters this parser will be triggered if the character is found.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.IMarkdownParser`1.Initialize">
            <summary>
            Initializes this parser with the specified parser processor.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.IMarkdownParser`1.Index">
            <summary>
            Gets the index of this parser in <see cref="T:Markdig.Parsers.BlockParserList"/> or <see cref="T:Markdig.Parsers.InlineParserList"/>.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.IndentedCodeBlockParser">
            <summary>
            Block parser for an indented <see cref="T:Markdig.Syntax.CodeBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="T:Markdig.Parsers.InlineParser">
            <summary>
            Base class for parsing an <see cref="T:Markdig.Syntax.Inlines.Inline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineProcessor" />
        </member>
        <member name="M:Markdig.Parsers.InlineParser.Match(Markdig.Parsers.InlineProcessor,Markdig.Helpers.StringSlice@)">
            <summary>
            Tries to match the specified slice.
            </summary>
            <param name="processor">The parser processor.</param>
            <param name="slice">The text slice.</param>
            <returns><c>true</c> if this parser found a match; <c>false</c> otherwise</returns>
        </member>
        <member name="T:Markdig.Parsers.InlineParserList">
            <summary>
            A list of <see cref="T:Markdig.Parsers.InlineParser"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.ParserList`2" />
        </member>
        <member name="P:Markdig.Parsers.InlineParserList.PostInlineProcessors">
            <summary>
            Gets the registered post inline processors.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.ProcessInlineDelegate">
            <summary>
            A delegate called at inline processing stage.
            </summary>
            <param name="processor">The processor.</param>
            <param name="inline">The inline being processed.</param>
        </member>
        <member name="T:Markdig.Parsers.InlineProcessor">
            <summary>
            The inline parser state used by all <see cref="T:Markdig.Parsers.InlineParser"/>.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.InlineProcessor.#ctor(Markdig.Syntax.MarkdownDocument,Markdig.Parsers.InlineParserList,System.Boolean,Markdig.MarkdownParserContext,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.InlineProcessor" /> class.
            </summary>
            <param name="document">The document.</param>
            <param name="parsers">The parsers.</param>
            <param name="preciseSourcelocation">A value indicating whether to provide precise source location.</param>
            <param name="context">A parser context used for the parsing.</param>
            <param name="trackTrivia">Whether to parse trivia such as whitespace, extra heading characters and unescaped string values.</param>
            <exception cref="T:System.ArgumentNullException">
            </exception>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.Block">
            <summary>
            Gets the current block being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.PreciseSourceLocation">
            <summary>
            Gets a value indicating whether to provide precise source location.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.BlockNew">
            <summary>
            Gets or sets the new block to replace the block being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.Inline">
            <summary>
            Gets or sets the current inline. Used by <see cref="T:Markdig.Parsers.InlineParser"/> to return a new inline if match was successfull
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.Root">
            <summary>
            Gets the root container of the current <see cref="P:Markdig.Parsers.InlineProcessor.Block"/>.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.Parsers">
            <summary>
            Gets the list of inline parsers.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.Context">
            <summary>
            Gets the parser context or <c>null</c> if none is available.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.Document">
            <summary>
            Gets the root document.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.LineIndex">
            <summary>
            Gets or sets the index of the line from the begining of the document being processed.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.ParserStates">
            <summary>
            Gets the parser states that can be used by <see cref="T:Markdig.Parsers.InlineParser"/> using their <see cref="P:Markdig.Parsers.ParserBase`1.Index"/> property.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.DebugLog">
            <summary>
            Gets or sets the debug log writer. No log if null.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.TrackTrivia">
            <summary>
            True to parse trivia such as whitespace, extra heading characters and unescaped
            string values.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.InlineProcessor.LiteralInlineParser">
            <summary>
            Gets the literal inline parser.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.InlineProcessor.GetSourcePosition(System.Int32,System.Int32@,System.Int32@)">
            <summary>
            Gets the source position for the specified offset within the current slice.
            </summary>
            <param name="sliceOffset">The slice offset.</param>
            <param name="lineIndex">The line index.</param>
            <param name="column">The column.</param>
            <returns>The source position</returns>
        </member>
        <member name="M:Markdig.Parsers.InlineProcessor.GetSourcePosition(System.Int32)">
            <summary>
            Gets the source position for the specified offset within the current slice.
            </summary>
            <param name="sliceOffset">The slice offset.</param>
            <returns>The source position</returns>
        </member>
        <member name="M:Markdig.Parsers.InlineProcessor.ReplaceParentContainer(Markdig.Syntax.ContainerBlock,Markdig.Syntax.ContainerBlock)">
            <summary>
            Replace a parent container. This method is experimental and should be used with caution.
            </summary>
            <param name="previousParentContainer">The previous parent container to replace</param>
            <param name="newParentContainer">The new parent container</param>
            <exception cref="T:System.InvalidOperationException">If a new parent container has been already setup.</exception>
        </member>
        <member name="M:Markdig.Parsers.InlineProcessor.ProcessInlineLeaf(Markdig.Syntax.LeafBlock)">
            <summary>
            Processes the inline of the specified <see cref="T:Markdig.Syntax.LeafBlock"/>.
            </summary>
            <param name="leafBlock">The leaf block.</param>
        </member>
        <member name="T:Markdig.Parsers.Inlines.AutolinkInlineParser">
            <summary>
            An inline parser for parsing <see cref="T:Markdig.Syntax.Inlines.AutolinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Parsers.Inlines.AutolinkInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.Inlines.AutolinkInlineParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.Inlines.AutolinkInlineParser.EnableHtmlParsing">
            <summary>
            Gets or sets a value indicating whether to enable HTML parsing. Default is <c>true</c>
            </summary>
        </member>
        <member name="T:Markdig.Parsers.Inlines.CodeInlineParser">
            <summary>
            An inline parser for a <see cref="T:Markdig.Syntax.Inlines.CodeInline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Parsers.Inlines.CodeInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.Inlines.CodeInlineParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.Inlines.EmphasisDescriptor">
            <summary>
            Descriptor for an emphasis.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.Inlines.EmphasisDescriptor.#ctor(System.Char,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.Inlines.EmphasisDescriptor"/> class.
            </summary>
            <param name="character">The character used for this emphasis.</param>
            <param name="minimumCount">The minimum number of character.</param>
            <param name="maximumCount">The maximum number of characters.</param>
            <param name="enableWithinWord">if set to <c>true</c> the emphasis can be used inside a word.</param>
        </member>
        <member name="P:Markdig.Parsers.Inlines.EmphasisDescriptor.Character">
            <summary>
            The character of this emphasis.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.Inlines.EmphasisDescriptor.MinimumCount">
            <summary>
            The minimum number of character this emphasis is expected to have (must be >=1)
            </summary>
        </member>
        <member name="P:Markdig.Parsers.Inlines.EmphasisDescriptor.MaximumCount">
            <summary>
            The maximum number of character this emphasis is expected to have (must be >=1 and >= minimumCount)
            </summary>
        </member>
        <member name="P:Markdig.Parsers.Inlines.EmphasisDescriptor.EnableWithinWord">
            <summary>
            This emphasis can be used within a word.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.Inlines.EmphasisInlineParser">
            <summary>
            An inline parser for <see cref="T:Markdig.Syntax.Inlines.EmphasisInline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
            <seealso cref="T:Markdig.Parsers.IPostInlineProcessor" />
        </member>
        <member name="M:Markdig.Parsers.Inlines.EmphasisInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.Inlines.EmphasisInlineParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.Inlines.EmphasisInlineParser.EmphasisDescriptors">
            <summary>
            Gets the emphasis descriptors.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.Inlines.EmphasisInlineParser.HasEmphasisChar(System.Char)">
            <summary>
            Determines whether this parser is using the specified character as an emphasis delimiter.
            </summary>
            <param name="c">The character to look for.</param>
            <returns><c>true</c> if this parser is using the specified character as an emphasis delimiter; otherwise <c>false</c></returns>
        </member>
        <member name="P:Markdig.Parsers.Inlines.EmphasisInlineParser.CreateEmphasisInline">
            <summary>
            Gets or sets the create emphasis inline delegate (allowing to create a different emphasis inline class)
            </summary>
        </member>
        <member name="T:Markdig.Parsers.Inlines.EscapeInlineParser">
            <summary>
            An inline parser for escape characters.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="T:Markdig.Parsers.Inlines.HtmlEntityParser">
            <summary>
            An inline parser for HTML entities.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Parsers.Inlines.HtmlEntityParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.Inlines.HtmlEntityParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.Inlines.LineBreakInlineParser">
            <summary>
            An inline parser for <see cref="T:Markdig.Syntax.Inlines.LineBreakInline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Parsers.Inlines.LineBreakInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.Inlines.LineBreakInlineParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.Inlines.LineBreakInlineParser.EnableSoftAsHard">
            <summary>
            Gets or sets a value indicating whether to interpret softline breaks as hardline breaks. Default is false
            </summary>
        </member>
        <member name="T:Markdig.Parsers.Inlines.LinkInlineParser">
            <summary>
            An inline parser for <see cref="T:Markdig.Syntax.Inlines.LinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Parsers.Inlines.LinkInlineParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.Inlines.LinkInlineParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.Inlines.LiteralInlineParser">
            <summary>
            An inline parser for parsing <see cref="T:Markdig.Syntax.Inlines.LiteralInline"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.InlineParser" />
        </member>
        <member name="M:Markdig.Parsers.Inlines.LiteralInlineParser.#ctor">
            <summary>
            We don't expect the LiteralInlineParser to be instantiated a end-user, as it is part
            of the default parser pipeline (and should always be the last), working as a literal character
            collector.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.Inlines.LiteralInlineParser.PostMatch">
            <summary>
            Gets or sets the post match delegate called after the inline has been processed.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.IPostInlineProcessor">
            <summary>
            A processor called at the end of processing all inlines.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.IPostInlineProcessor.PostProcess(Markdig.Parsers.InlineProcessor,Markdig.Syntax.Inlines.Inline,Markdig.Syntax.Inlines.Inline,System.Int32,System.Boolean)">
            <summary>
            Processes the delimiters.
            </summary>
            <param name="state">The parser state.</param>
            <param name="root">The root inline.</param>
            <param name="lastChild">The last child.</param>
            <param name="postInlineProcessorIndex">Index of this delimiter processor.</param>
            <param name="isFinalProcessing"></param>
            <returns><c>true</c> to continue to the next delimiter processor;
            <c>false</c> to stop the process (in case a processor is performing sub-sequent processor itself)</returns>
        </member>
        <member name="T:Markdig.Parsers.ListBlockParser">
            <summary>
            A parser for a list block and list item block.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Parsers.ListBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.ListBlockParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.ListBlockParser.ItemParsers">
            <summary>
            Gets the parsers for items.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.ListInfo">
            <summary>
            Defines list information returned when trying to parse a list item with <see cref="M:Markdig.Parsers.ListItemParser.TryParse(Markdig.Parsers.BlockProcessor,System.Char,Markdig.Parsers.ListInfo@)"/>
            </summary>
        </member>
        <member name="M:Markdig.Parsers.ListInfo.#ctor(System.Char)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.ListInfo"/> struct.
            </summary>
            <param name="bulletType">Type of the bullet (e.g: '1', 'a', 'A', 'i', 'I').</param>
        </member>
        <member name="M:Markdig.Parsers.ListInfo.#ctor(System.Char,System.String,System.Char,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.ListInfo"/> struct.
            </summary>
            <param name="bulletType">Type of the bullet (e.g: '1', 'a', 'A', 'i', 'I')</param>
            <param name="orderedStart">The string used as a starting sequence for an ordered list.</param>
            <param name="orderedDelimiter">The ordered delimiter found when parsing this list (e.g: the character `)` after `1)`)</param>
            <param name="defaultOrderedStart">The default string used as a starting sequence for the ordered list (e.g: '1' for an numbered ordered list)</param>
        </member>
        <member name="P:Markdig.Parsers.ListInfo.BulletType">
            <summary>
            Gets or sets the type of the bullet (e.g: '1', 'a', 'A', 'i', 'I').
            </summary>
        </member>
        <member name="P:Markdig.Parsers.ListInfo.OrderedStart">
            <summary>
            Gets or sets the string used as a starting sequence for an ordered list
            </summary>
        </member>
        <member name="P:Markdig.Parsers.ListInfo.OrderedDelimiter">
            <summary>
            Gets or sets the ordered delimiter found when parsing this list (e.g: the character `)` after `1)`)
            </summary>
        </member>
        <member name="P:Markdig.Parsers.ListInfo.DefaultOrderedStart">
            <summary>
            Gets or sets default string used as a starting sequence for the ordered list (e.g: '1' for an numbered ordered list)
            </summary>
        </member>
        <member name="T:Markdig.Parsers.ListItemParser">
            <summary>
            A parser base class for a list item.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.ListItemParser.OpeningCharacters">
            <summary>
            Defines the characters that are used for detecting this list item.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.ListItemParser.TryParse(Markdig.Parsers.BlockProcessor,System.Char,Markdig.Parsers.ListInfo@)">
            <summary>
            Tries to parse the current input as a list item for this particular instance.
            </summary>
            <param name="state">The block processor</param>
            <param name="pendingBulletType">The type of the current bullet type</param>
            <param name="result">The result of parsing</param>
            <returns><c>true</c> if parsing was successful; <c>false</c> otherwise</returns>
        </member>
        <member name="T:Markdig.Parsers.ProcessDocumentDelegate">
            <summary>
            Delegates called when processing a document
            </summary>
            <param name="document">The markdown document.</param>
        </member>
        <member name="T:Markdig.Parsers.MarkdownParser">
            <summary>
            The Markdown parser.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.MarkdownParser.Parse(System.String,Markdig.MarkdownPipeline,Markdig.MarkdownParserContext)">
            <summary>
            Parses the specified markdown into an AST <see cref="T:Markdig.Syntax.MarkdownDocument"/>
            </summary>
            <param name="text">A Markdown text</param>
            <param name="pipeline">The pipeline used for the parsing.</param>
            <param name="context">A parser context used for the parsing.</param>
            <returns>An AST Markdown document</returns>
            <exception cref="T:System.ArgumentNullException">if reader variable is null</exception>
        </member>
        <member name="M:Markdig.Parsers.MarkdownParser.FixupZero(System.String)">
            <summary>
            Fixups the zero character by replacing it to a secure character (Section 2.3 Insecure characters, CommonMark specs)
            </summary>
            <param name="text">The text to secure.</param>
        </member>
        <member name="T:Markdig.Parsers.NumberedListItemParser">
            <summary>
            The default parser for parsing numbered list item (e.g: 1) or 1.)
            </summary>
            <seealso cref="T:Markdig.Parsers.OrderedListItemParser" />
        </member>
        <member name="M:Markdig.Parsers.NumberedListItemParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.NumberedListItemParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.OrderedListItemParser">
            <summary>
            Base class for an ordered list item parser.
            </summary>
            <seealso cref="T:Markdig.Parsers.ListItemParser" />
        </member>
        <member name="M:Markdig.Parsers.OrderedListItemParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.OrderedListItemParser"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.OrderedListItemParser.OrderedDelimiters">
            <summary>
            Gets or sets the ordered delimiters used after a digit/number (by default `.` and `)`)
            </summary>
        </member>
        <member name="M:Markdig.Parsers.OrderedListItemParser.TryParseDelimiter(Markdig.Parsers.BlockProcessor,System.Char@)">
            <summary>
            Utility method that tries to parse the delimiter coming after an ordered list start (e.g: the `)` after `1)`).
            </summary>
            <param name="state">The state.</param>
            <param name="orderedDelimiter">The ordered delimiter found if this method is successful.</param>
            <returns><c>true</c> if parsing was successful; <c>false</c> otherwise.</returns>
        </member>
        <member name="T:Markdig.Parsers.ParagraphBlockParser">
            <summary>
            Block parser for a <see cref="T:Markdig.Syntax.ParagraphBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="T:Markdig.Parsers.ParserBase`1">
            <summary>
            Base class for a <see cref="T:Markdig.Parsers.BlockParser"/> or <see cref="T:Markdig.Parsers.InlineParser"/>.
            </summary>
            <typeparam name="TProcessor">Type of the parser processor</typeparam>
            <seealso cref="T:Markdig.Parsers.IMarkdownParser`1" />
        </member>
        <member name="P:Markdig.Parsers.ParserBase`1.OpeningCharacters">
            <summary>
            Gets the opening characters this parser will be triggered if the character is found.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.ParserBase`1.Initialize">
            <summary>
            Initializes this parser with the specified parser processor.
            </summary>
        </member>
        <member name="P:Markdig.Parsers.ParserBase`1.Index">
            <summary>
            Gets the index of this parser in <see cref="T:Markdig.Parsers.BlockParserList" /> or <see cref="T:Markdig.Parsers.InlineParserList" />.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.ParserList`2">
            <summary>
            Base class for a list of parsers.
            </summary>
            <typeparam name="T">Type of the parser</typeparam>
            <typeparam name="TState">The type of the parser state.</typeparam>
            <seealso cref="T:Markdig.Helpers.OrderedList`1" />
        </member>
        <member name="P:Markdig.Parsers.ParserList`2.GlobalParsers">
            <summary>
            Gets the list of global parsers (that don't have any opening characters defined)
            </summary>
        </member>
        <member name="P:Markdig.Parsers.ParserList`2.OpeningCharacters">
            <summary>
            Gets all the opening characters defined.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.ParserList`2.GetParsersForOpeningCharacter(System.UInt32)">
            <summary>
            Gets the list of parsers valid for the specified opening character.
            </summary>
            <param name="openingChar">The opening character.</param>
            <returns>A list of parsers valid for the specified opening character or null if no parsers registered.</returns>
        </member>
        <member name="M:Markdig.Parsers.ParserList`2.IndexOfOpeningCharacter(System.String,System.Int32,System.Int32)">
            <summary>
            Searches for an opening character from a registered parser in the specified string.
            </summary>
            <param name="text">The text.</param>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
            <returns>Index position within the string of the first opening character found in the specified text; if not found, returns -1</returns>
        </member>
        <member name="T:Markdig.Parsers.QuoteBlockParser">
            <summary>
            A block parser for a <see cref="T:Markdig.Syntax.QuoteBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="M:Markdig.Parsers.QuoteBlockParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.QuoteBlockParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.ThematicBreakParser">
            <summary>
            A block parser for a <see cref="T:Markdig.Syntax.ThematicBreakBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Parsers.BlockParser" />
        </member>
        <member name="F:Markdig.Parsers.ThematicBreakParser.Default">
            <summary>
            A singleton instance used by other parsers.
            </summary>
        </member>
        <member name="M:Markdig.Parsers.ThematicBreakParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.ThematicBreakParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Parsers.UnorderedListItemParser">
            <summary>
            The default parser used to parse unordered list item (-, +, *)
            </summary>
            <seealso cref="T:Markdig.Parsers.ListItemParser" />
        </member>
        <member name="M:Markdig.Parsers.UnorderedListItemParser.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Parsers.UnorderedListItemParser"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Html.CodeBlockRenderer">
            <summary>
            An HTML renderer for a <see cref="T:Markdig.Syntax.CodeBlock"/> and <see cref="T:Markdig.Syntax.FencedCodeBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="M:Markdig.Renderers.Html.CodeBlockRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.Html.CodeBlockRenderer"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Html.CodeBlockRenderer.BlocksAsDiv">
            <summary>
            Gets a map of fenced code block infos that should be rendered as div blocks instead of pre/code blocks.
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Html.HeadingRenderer">
            <summary>
            An HTML renderer for a <see cref="T:Markdig.Syntax.HeadingBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.HtmlAttributes">
            <summary>
            Attached HTML attributes to a <see cref="T:Markdig.Syntax.MarkdownObject"/>.
            </summary>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributes.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Html.HtmlAttributes.Id">
            <summary>
            Gets or sets the HTML id/identifier. May be null.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Html.HtmlAttributes.Classes">
            <summary>
            Gets or sets the CSS classes attached. May be null.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Html.HtmlAttributes.Properties">
            <summary>
            Gets or sets the additional properties. May be null.
            </summary>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributes.AddClass(System.String)">
            <summary>
            Adds a CSS class.
            </summary>
            <param name="name">The css class name.</param>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributes.AddProperty(System.String,System.String)">
            <summary>
            Adds a property.
            </summary>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributes.AddPropertyIfNotExist(System.String,System.Object)">
            <summary>
            Adds the specified property only if it does not already exist.
            </summary>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributes.CopyTo(Markdig.Renderers.Html.HtmlAttributes,System.Boolean,System.Boolean)">
            <summary>
            Copies/merge the values from this instance to the specified <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/> instance.
            </summary>
            <param name="htmlAttributes">The HTML attributes.</param>
            <param name="mergeIdAndProperties">If set to <c>true</c> it will merge properties to the target htmlAttributes. Default is <c>false</c></param>
            <param name="shared">If set to <c>true</c> it will try to share Classes and Properties if destination don't have them, otherwise it will make a copy. Default is <c>true</c></param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Markdig.Renderers.Html.HtmlAttributesExtensions">
            <summary>
            Extensions for a <see cref="T:Markdig.Syntax.MarkdownObject"/> to allow accessing <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/>
            </summary>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributesExtensions.TryGetAttributes(Markdig.Syntax.IMarkdownObject)">
            <summary>
            Tries the get <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/> stored on a <see cref="T:Markdig.Syntax.MarkdownObject"/>.
            </summary>
            <param name="obj">The markdown object.</param>
            <returns>The attached html attributes or null if not found</returns>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributesExtensions.GetAttributes(Markdig.Syntax.IMarkdownObject)">
            <summary>
            Gets or creates the <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/> stored on a <see cref="T:Markdig.Syntax.MarkdownObject"/>
            </summary>
            <param name="obj">The markdown object.</param>
            <returns>The attached html attributes</returns>
        </member>
        <member name="M:Markdig.Renderers.Html.HtmlAttributesExtensions.SetAttributes(Markdig.Syntax.IMarkdownObject,Markdig.Renderers.Html.HtmlAttributes)">
            <summary>
            Sets <see cref="T:Markdig.Renderers.Html.HtmlAttributes" /> to the <see cref="T:Markdig.Syntax.MarkdownObject" />
            </summary>
            <param name="obj">The markdown object.</param>
            <param name="attributes">The attributes to attach.</param>
        </member>
        <member name="T:Markdig.Renderers.Html.HtmlBlockRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.HtmlBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.HtmlObjectRenderer`1">
            <summary>
            A base class for HTML rendering <see cref="T:Markdig.Syntax.Block"/> and <see cref="T:Markdig.Syntax.Inlines.Inline"/> Markdown objects.
            </summary>
            <typeparam name="TObject">The type of the object.</typeparam>
            <seealso cref="T:Markdig.Renderers.IMarkdownObjectRenderer" />
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.AutolinkInlineRenderer">
            <summary>
            A HTML renderer for an <see cref="T:Markdig.Syntax.Inlines.AutolinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="P:Markdig.Renderers.Html.Inlines.AutolinkInlineRenderer.AutoRelNoFollow">
            <summary>
            Gets or sets a value indicating whether to always add rel="nofollow" for links or not.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Html.Inlines.AutolinkInlineRenderer.Rel">
            <summary>
            Gets or sets the literal string in property rel for links
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.CodeInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.Inlines.CodeInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.DelimiterInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.Inlines.DelimiterInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.EmphasisInlineRenderer">
            <summary>
            A HTML renderer for an <see cref="T:Markdig.Syntax.Inlines.EmphasisInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.EmphasisInlineRenderer.GetTagDelegate">
            <summary>
            Delegates to get the tag associated to an <see cref="T:Markdig.Syntax.Inlines.EmphasisInline"/> object.
            </summary>
            <param name="obj">The object.</param>
            <returns>The HTML tag associated to this <see cref="T:Markdig.Syntax.Inlines.EmphasisInline"/> object</returns>
        </member>
        <member name="M:Markdig.Renderers.Html.Inlines.EmphasisInlineRenderer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.Html.Inlines.EmphasisInlineRenderer"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Html.Inlines.EmphasisInlineRenderer.GetTag">
            <summary>
            Gets or sets the GetTag delegate.
            </summary>
        </member>
        <member name="M:Markdig.Renderers.Html.Inlines.EmphasisInlineRenderer.GetDefaultTag(Markdig.Syntax.Inlines.EmphasisInline)">
            <summary>
            Gets the default HTML tag for ** and __ emphasis.
            </summary>
            <param name="obj">The object.</param>
            <returns></returns>
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.HtmlEntityInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.Inlines.HtmlEntityInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.HtmlInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.Inlines.HtmlInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.LineBreakInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.Inlines.LineBreakInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="P:Markdig.Renderers.Html.Inlines.LineBreakInlineRenderer.RenderAsHardlineBreak">
            <summary>
            Gets or sets a value indicating whether to render this softline break as a HTML hardline break tag (&lt;br /&gt;)
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.LinkInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.Inlines.LinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="P:Markdig.Renderers.Html.Inlines.LinkInlineRenderer.AutoRelNoFollow">
            <summary>
            Gets or sets a value indicating whether to always add rel="nofollow" for links or not.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Html.Inlines.LinkInlineRenderer.Rel">
            <summary>
            Gets or sets the literal string in property rel for links
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Html.Inlines.LiteralInlineRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.Inlines.LiteralInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.ListRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.ListBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.ParagraphRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.ParagraphBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.QuoteBlockRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.QuoteBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Html.ThematicBreakRenderer">
            <summary>
            A HTML renderer for a <see cref="T:Markdig.Syntax.ThematicBreakBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Html.HtmlObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.HtmlRenderer">
            <summary>
            Default HTML renderer for a Markdown <see cref="T:Markdig.Syntax.MarkdownDocument"/> object.
            </summary>
            <seealso cref="T:Markdig.Renderers.TextRendererBase`1" />
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.HtmlRenderer"/> class.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="P:Markdig.Renderers.HtmlRenderer.EnableHtmlForInline">
            <summary>
            Gets or sets a value indicating whether to output HTML tags when rendering. See remarks.
            </summary>
            <remarks>
            This is used by some renderers to disable HTML tags when rendering some inline elements (for image links).
            </remarks>
        </member>
        <member name="P:Markdig.Renderers.HtmlRenderer.EnableHtmlForBlock">
            <summary>
            Gets or sets a value indicating whether to output HTML tags when rendering. See remarks.
            </summary>
            <remarks>
            This is used by some renderers to disable HTML tags when rendering some block elements (for image links).
            </remarks>
        </member>
        <member name="P:Markdig.Renderers.HtmlRenderer.ImplicitParagraph">
            <summary>
            Gets or sets a value indicating whether to use implicit paragraph (optional &lt;p&gt;)
            </summary>
        </member>
        <member name="P:Markdig.Renderers.HtmlRenderer.BaseUrl">
            <summary>
            Gets a value to use as the base url for all relative links
            </summary>
        </member>
        <member name="P:Markdig.Renderers.HtmlRenderer.LinkRewriter">
            <summary>
            Allows links to be rewritten
            </summary>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteEscape(System.String)">
            <summary>
            Writes the content escaped for HTML.
            </summary>
            <param name="content">The content.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteEscape(Markdig.Helpers.StringSlice@,System.Boolean)">
            <summary>
            Writes the content escaped for HTML.
            </summary>
            <param name="slice">The slice.</param>
            <param name="softEscape">Only escape &lt; and &amp;</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteEscape(Markdig.Helpers.StringSlice,System.Boolean)">
            <summary>
            Writes the content escaped for HTML.
            </summary>
            <param name="slice">The slice.</param>
            <param name="softEscape">Only escape &lt; and &amp;</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteEscape(System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Writes the content escaped for HTML.
            </summary>
            <param name="content">The content.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <param name="softEscape">Only escape &lt; and &amp;</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteEscape(System.ReadOnlySpan{System.Char},System.Boolean)">
            <summary>
            Writes the content escaped for HTML.
            </summary>
            <param name="content">The content.</param>
            <param name="softEscape">Only escape &lt; and &amp;</param>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteEscapeUrl(System.String)">
            <summary>
            Writes the URL escaped for HTML.
            </summary>
            <param name="content">The content.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteAttributes(Markdig.Syntax.MarkdownObject)">
            <summary>
            Writes the attached <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/> on the specified <see cref="T:Markdig.Syntax.MarkdownObject"/>.
            </summary>
            <param name="markdownObject">The object.</param>
            <returns></returns>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteAttributes(Markdig.Renderers.Html.HtmlAttributes,System.Func{System.String,System.String})">
            <summary>
            Writes the specified <see cref="T:Markdig.Renderers.Html.HtmlAttributes"/>.
            </summary>
            <param name="attributes">The attributes to render.</param>
            <param name="classFilter">A class filter used to transform a class into another class at writing time</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.HtmlRenderer.WriteLeafRawLines(Markdig.Syntax.LeafBlock,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Writes the lines of a <see cref="T:Markdig.Syntax.LeafBlock"/>
            </summary>
            <param name="leafBlock">The leaf block.</param>
            <param name="writeEndOfLines">if set to <c>true</c> write end of lines.</param>
            <param name="escape">if set to <c>true</c> escape the content for HTML</param>
            <param name="softEscape">Only escape &lt; and &amp;</param>
            <returns>This instance</returns>
        </member>
        <member name="T:Markdig.Renderers.IMarkdownObjectRenderer">
            <summary>
            Base interface for the renderer of a <see cref="T:Markdig.Syntax.MarkdownObject"/>.
            </summary>
        </member>
        <member name="M:Markdig.Renderers.IMarkdownObjectRenderer.Accept(Markdig.Renderers.RendererBase,System.Type)">
            <summary>
            Accepts the specified <see cref="T:Markdig.Syntax.MarkdownObject"/>.
            </summary>
            <param name="renderer">The renderer.</param>
            <param name="objectType">The <see cref="T:System.Type"/> of the Markdown object.</param>
            <returns><c>true</c> If this renderer is accepting to render the specified Markdown object</returns>
        </member>
        <member name="M:Markdig.Renderers.IMarkdownObjectRenderer.Write(Markdig.Renderers.RendererBase,Markdig.Syntax.MarkdownObject)">
            <summary>
            Writes the specified <see cref="T:Markdig.Syntax.MarkdownObject"/> to the <paramref name="renderer"/>.
            </summary>
            <param name="renderer">The renderer.</param>
            <param name="objectToRender">The object to render.</param>
        </member>
        <member name="T:Markdig.Renderers.IMarkdownRenderer">
            <summary>
            Base interface for a renderer for a Markdown <see cref="T:Markdig.Syntax.MarkdownDocument"/>.
            </summary>
        </member>
        <member name="E:Markdig.Renderers.IMarkdownRenderer.ObjectWriteBefore">
            <summary>
            Occurs when before writing an object.
            </summary>
        </member>
        <member name="E:Markdig.Renderers.IMarkdownRenderer.ObjectWriteAfter">
            <summary>
            Occurs when after writing an object.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.IMarkdownRenderer.ObjectRenderers">
            <summary>
            Gets the object renderers that will render <see cref="T:Markdig.Syntax.Block"/> and <see cref="T:Markdig.Syntax.Inlines.Inline"/> elements.
            </summary>
        </member>
        <member name="M:Markdig.Renderers.IMarkdownRenderer.Render(Markdig.Syntax.MarkdownObject)">
            <summary>
            Renders the specified markdown object.
            </summary>
            <param name="markdownObject">The markdown object.</param>
            <returns>The result of the rendering.</returns>
        </member>
        <member name="T:Markdig.Renderers.MarkdownObjectRenderer`2">
            <summary>
            A base class for rendering <see cref="T:Markdig.Syntax.Block" /> and <see cref="T:Markdig.Syntax.Inlines.Inline" /> Markdown objects.
            </summary>
            <typeparam name="TRenderer">The type of the renderer.</typeparam>
            <typeparam name="TObject">The type of the object.</typeparam>
            <seealso cref="T:Markdig.Renderers.IMarkdownObjectRenderer" />
        </member>
        <member name="P:Markdig.Renderers.MarkdownObjectRenderer`2.TryWriters">
            <summary>
            Gets the optional writers attached to this instance.
            </summary>
        </member>
        <member name="M:Markdig.Renderers.MarkdownObjectRenderer`2.Write(`0,`1)">
            <summary>
            Writes the specified Markdown object to the renderer.
            </summary>
            <param name="renderer">The renderer.</param>
            <param name="obj">The markdown object.</param>
        </member>
        <member name="T:Markdig.Renderers.Normalize.CodeBlockRenderer">
            <summary>
            An Normalize renderer for a <see cref="T:Markdig.Syntax.CodeBlock"/> and <see cref="T:Markdig.Syntax.FencedCodeBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.HeadingRenderer">
            <summary>
            An Normalize renderer for a <see cref="T:Markdig.Syntax.HeadingBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.AutolinkInlineRenderer">
            <summary>
            A Normalize renderer for an <see cref="T:Markdig.Syntax.Inlines.AutolinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.CodeInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.CodeInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.DelimiterInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.DelimiterInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.EmphasisInlineRenderer">
            <summary>
            A Normalize renderer for an <see cref="T:Markdig.Syntax.Inlines.EmphasisInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.LineBreakInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.LineBreakInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="P:Markdig.Renderers.Normalize.Inlines.LineBreakInlineRenderer.RenderAsHardlineBreak">
            <summary>
            Gets or sets a value indicating whether to render this softline break as a Normalize hardline break tag (&lt;br /&gt;)
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.LinkInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.LinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.LiteralInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.LiteralInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.NormalizeHtmlEntityInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.HtmlEntityInline"/>.
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Normalize.Inlines.NormalizeHtmlInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.HtmlInline"/>.
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Normalize.ListRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.ListBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1">
            <summary>
            A base class for Normalize rendering <see cref="T:Markdig.Syntax.Block"/> and <see cref="T:Markdig.Syntax.Inlines.Inline"/> Markdown objects.
            </summary>
            <typeparam name="TObject">The type of the object.</typeparam>
            <seealso cref="T:Markdig.Renderers.IMarkdownObjectRenderer" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.NormalizeOptions">
            <summary>
            Defines the options used by <see cref="T:Markdig.Renderers.Normalize.NormalizeRenderer"/>
            </summary>
        </member>
        <member name="M:Markdig.Renderers.Normalize.NormalizeOptions.#ctor">
            <summary>
            Initialize a new instance of <see cref="T:Markdig.Renderers.Normalize.NormalizeOptions"/>
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Normalize.NormalizeOptions.SpaceAfterQuoteBlock">
            <summary>
            Adds a space after a QuoteBlock &gt;. Default is <c>true</c>
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Normalize.NormalizeOptions.EmptyLineAfterCodeBlock">
            <summary>
            Adds an empty line after a code block (fenced and tabbed). Default is <c>true</c>
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Normalize.NormalizeOptions.EmptyLineAfterHeading">
            <summary>
            Adds an empty line after an heading. Default is <c>true</c>
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Normalize.NormalizeOptions.EmptyLineAfterThematicBreak">
            <summary>
            Adds an empty line after an thematic break. Default is <c>true</c>
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Normalize.NormalizeOptions.ListItemCharacter">
            <summary>
            The bullet character used for list items. Default is <c>null</c> leaving the original bullet character as-is.
            </summary>
        </member>
        <member name="P:Markdig.Renderers.Normalize.NormalizeOptions.ExpandAutoLinks">
            <summary>
            Expands AutoLinks to the normal inline representation. Default is <c>true</c>
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Normalize.NormalizeRenderer">
            <summary>
            Default HTML renderer for a Markdown <see cref="T:Markdig.Syntax.MarkdownDocument"/> object.
            </summary>
            <seealso cref="T:Markdig.Renderers.TextRendererBase`1" />
        </member>
        <member name="M:Markdig.Renderers.Normalize.NormalizeRenderer.#ctor(System.IO.TextWriter,Markdig.Renderers.Normalize.NormalizeOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.Normalize.NormalizeRenderer"/> class.
            </summary>
            <param name="writer">The writer.</param>
            <param name="options">The normalize options</param>
        </member>
        <member name="M:Markdig.Renderers.Normalize.NormalizeRenderer.WriteLeafRawLines(Markdig.Syntax.LeafBlock,System.Boolean,System.Boolean)">
            <summary>
            Writes the lines of a <see cref="T:Markdig.Syntax.LeafBlock"/>
            </summary>
            <param name="leafBlock">The leaf block.</param>
            <param name="writeEndOfLines">if set to <c>true</c> write end of lines.</param>
            <param name="indent">Whether to write indents.</param>
            <returns>This instance</returns>
        </member>
        <member name="T:Markdig.Renderers.Normalize.ParagraphRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.ParagraphBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.QuoteBlockRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.QuoteBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Normalize.ThematicBreakRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.ThematicBreakBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Normalize.NormalizeObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.ObjectRendererCollection">
            <summary>
            A collection of <see cref="T:Markdig.Renderers.IMarkdownObjectRenderer"/>.
            </summary>
            <seealso cref="T:Markdig.Helpers.OrderedList`1" />
        </member>
        <member name="T:Markdig.Renderers.RendererBase">
            <summary>
            Base class for a <see cref="T:Markdig.Renderers.IMarkdownRenderer"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.IMarkdownRenderer" />
        </member>
        <member name="M:Markdig.Renderers.RendererBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.RendererBase"/> class.
            </summary>
        </member>
        <member name="E:Markdig.Renderers.RendererBase.ObjectWriteBefore">
            <summary>
            Occurs when before writing an object.
            </summary>
        </member>
        <member name="E:Markdig.Renderers.RendererBase.ObjectWriteAfter">
            <summary>
            Occurs when after writing an object.
            </summary>
        </member>
        <member name="M:Markdig.Renderers.RendererBase.WriteChildren(Markdig.Syntax.ContainerBlock)">
            <summary>
            Writes the children of the specified <see cref="T:Markdig.Syntax.ContainerBlock"/>.
            </summary>
            <param name="containerBlock">The container block.</param>
        </member>
        <member name="M:Markdig.Renderers.RendererBase.WriteChildren(Markdig.Syntax.Inlines.ContainerInline)">
            <summary>
            Writes the children of the specified <see cref="T:Markdig.Syntax.Inlines.ContainerInline"/>.
            </summary>
            <param name="containerInline">The container inline.</param>
        </member>
        <member name="M:Markdig.Renderers.RendererBase.Write(Markdig.Syntax.MarkdownObject)">
            <summary>
            Writes the specified Markdown object.
            </summary>
            <param name="obj">The Markdown object to write to this renderer.</param>
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.CodeBlockRenderer">
            <summary>
            An Roundtrip renderer for a <see cref="T:Markdig.Syntax.CodeBlock"/> and <see cref="T:Markdig.Syntax.FencedCodeBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.HeadingRenderer">
            <summary>
            An Roundtrip renderer for a <see cref="T:Markdig.Syntax.HeadingBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.AutolinkInlineRenderer">
            <summary>
            A Normalize renderer for an <see cref="T:Markdig.Syntax.Inlines.AutolinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.CodeInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.CodeInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.DelimiterInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.DelimiterInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.EmphasisInlineRenderer">
            <summary>
            A Normalize renderer for an <see cref="T:Markdig.Syntax.Inlines.EmphasisInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.LineBreakInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.LineBreakInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.LinkInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.LinkInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.LiteralInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.LiteralInline"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.RoundtripHtmlEntityInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.HtmlEntityInline"/>.
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.Inlines.RoundtripHtmlInlineRenderer">
            <summary>
            A Normalize renderer for a <see cref="T:Markdig.Syntax.Inlines.HtmlInline"/>.
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.ListRenderer">
            <summary>
            A Roundtrip renderer for a <see cref="T:Markdig.Syntax.ListBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.ParagraphRenderer">
            <summary>
            A Roundtrip renderer for a <see cref="T:Markdig.Syntax.ParagraphBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.QuoteBlockRenderer">
            <summary>
            A Roundtrip renderer for a <see cref="T:Markdig.Syntax.QuoteBlock"/>.
            </summary>
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1">
            <summary>
            A base class for Normalize rendering <see cref="T:Markdig.Syntax.Block"/> and <see cref="T:Markdig.Syntax.Inlines.Inline"/> Markdown objects.
            </summary>
            <typeparam name="TObject">The type of the object.</typeparam>
            <seealso cref="T:Markdig.Renderers.IMarkdownObjectRenderer" />
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.RoundtripRenderer">
            <summary>
            Markdown renderer honoring trivia for a  <see cref="T:Markdig.Syntax.MarkdownDocument"/> object.
            </summary>
            Ensure to call the <see cref="M:Markdig.MarkdownExtensions.EnableTrackTrivia(Markdig.MarkdownPipelineBuilder)"/> extension method when
            parsing markdown to have trivia available for rendering.
        </member>
        <member name="M:Markdig.Renderers.Roundtrip.RoundtripRenderer.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.Roundtrip.RoundtripRenderer"/> class.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:Markdig.Renderers.Roundtrip.RoundtripRenderer.WriteLeafRawLines(Markdig.Syntax.LeafBlock)">
            <summary>
            Writes the lines of a <see cref="T:Markdig.Syntax.LeafBlock"/>
            </summary>
            <param name="leafBlock">The leaf block.</param>
            <returns>This instance</returns>
        </member>
        <member name="T:Markdig.Renderers.Roundtrip.ThematicBreakRenderer">
            <summary>
            A Roundtrip renderer for a <see cref="T:Markdig.Syntax.ThematicBreakBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.Roundtrip.RoundtripObjectRenderer`1" />
        </member>
        <member name="T:Markdig.Renderers.TextRendererBase">
            <summary>
            A text based <see cref="T:Markdig.Renderers.IMarkdownRenderer"/>.
            </summary>
            <seealso cref="T:Markdig.Renderers.RendererBase" />
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.TextRendererBase"/> class.
            </summary>
            <param name="writer">The writer.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Markdig.Renderers.TextRendererBase.Writer">
            <summary>
            Gets or sets the writer.
            </summary>
            <exception cref="T:System.ArgumentNullException">if the value is null</exception>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase.Render(Markdig.Syntax.MarkdownObject)">
            <summary>
            Renders the specified markdown object (returns the <see cref="P:Markdig.Renderers.TextRendererBase.Writer"/> as a render object).
            </summary>
            <param name="markdownObject">The markdown object.</param>
            <returns></returns>
        </member>
        <member name="T:Markdig.Renderers.TextRendererBase`1">
            <summary>
            Typed <see cref="T:Markdig.Renderers.TextRendererBase"/>.
            </summary>
            <typeparam name="T">Type of the renderer</typeparam>
            <seealso cref="T:Markdig.Renderers.RendererBase" />
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Renderers.TextRendererBase`1"/> class.
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.EnsureLine">
            <summary>
            Ensures a newline.
            </summary>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.Write(System.String)">
            <summary>
            Writes the specified content.
            </summary>
            <param name="content">The content.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.Write(System.Char,System.Int32)">
            <summary>
            Writes the specified char repeated a specified number of times.
            </summary>
            <param name="c">The char to write.</param>
            <param name="count">The number of times to write the char.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.Write(Markdig.Helpers.StringSlice@)">
            <summary>
            Writes the specified slice.
            </summary>
            <param name="slice">The slice.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.Write(Markdig.Helpers.StringSlice)">
            <summary>
            Writes the specified slice.
            </summary>
            <param name="slice">The slice.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.Write(System.Char)">
            <summary>
            Writes the specified character.
            </summary>
            <param name="content">The content.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.Write(System.String,System.Int32,System.Int32)">
            <summary>
            Writes the specified content.
            </summary>
            <param name="content">The content.</param>
            <param name="offset">The offset.</param>
            <param name="length">The length.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.Write(System.ReadOnlySpan{System.Char})">
            <summary>
            Writes the specified content.
            </summary>
            <param name="content">The content.</param>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.WriteLine">
            <summary>
            Writes a newline.
            </summary>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.WriteLine(Markdig.Helpers.NewLine)">
            <summary>
            Writes a newline.
            </summary>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.WriteLine(System.String)">
            <summary>
            Writes a content followed by a newline.
            </summary>
            <param name="content">The content.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.WriteLine(System.Char)">
            <summary>
            Writes a content followed by a newline.
            </summary>
            <param name="content">The content.</param>
            <returns>This instance</returns>
        </member>
        <member name="M:Markdig.Renderers.TextRendererBase`1.WriteLeafInline(Markdig.Syntax.LeafBlock)">
            <summary>
            Writes the inlines of a leaf inline.
            </summary>
            <param name="leafBlock">The leaf block.</param>
            <returns>This instance</returns>
        </member>
        <member name="T:Markdig.Syntax.BlankLineBlock">
            <summary>
            A blank line, used internally by some parsers to store blank lines in a container. They are removed before the end of the document.
            </summary>
            <seealso cref="T:Markdig.Syntax.Block" />
        </member>
        <member name="M:Markdig.Syntax.BlankLineBlock.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.BlankLineBlock"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Block">
            <summary>
            Base class for a block structure. Either a <see cref="T:Markdig.Syntax.LeafBlock"/> or a <see cref="T:Markdig.Syntax.ContainerBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.MarkdownObject" />
        </member>
        <member name="M:Markdig.Syntax.Block.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Block"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Syntax.Block.Parent">
            <summary>
            Gets the parent of this container. May be null.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.Parser">
            <summary>
            Gets the parser associated to this instance.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.IsOpen">
            <summary>
            Gets or sets a value indicating whether this instance is still open.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.IsBreakable">
            <summary>
            Gets or sets a value indicating whether this block is breakable. Default is true.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.NewLine">
            <summary>
            The last newline of this block.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.RemoveAfterProcessInlines">
            <summary>
            Gets or sets a value indicating whether this block must be removed from its container after inlines have been processed.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.TriviaBefore">
            <summary>
            Gets or sets the trivia right before this block.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.TriviaAfter">
            <summary>
            Gets or sets trivia occurring after this block.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.LinesBefore">
            <summary>
            Gets or sets the empty lines occurring before this block.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise null.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Block.LinesAfter">
            <summary>
            Gets or sets the empty lines occurring after this block.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise null.
            </summary>
        </member>
        <member name="E:Markdig.Syntax.Block.ProcessInlinesBegin">
            <summary>
            Occurs when the process of inlines begin.
            </summary>
        </member>
        <member name="E:Markdig.Syntax.Block.ProcessInlinesEnd">
            <summary>
            Occurs when the process of inlines ends for this instance.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Block.OnProcessInlinesBegin(Markdig.Parsers.InlineProcessor)">
            <summary>
            Called when the process of inlines begin.
            </summary>
            <param name="state">The inline parser state.</param>
        </member>
        <member name="M:Markdig.Syntax.Block.OnProcessInlinesEnd(Markdig.Parsers.InlineProcessor)">
            <summary>
            Called when the process of inlines ends.
            </summary>
            <param name="state">The inline parser state.</param>
        </member>
        <member name="T:Markdig.Syntax.BlockExtensions">
            <summary>
            Extensions for <see cref="T:Markdig.Syntax.Block"/>
            </summary>
        </member>
        <member name="T:Markdig.Syntax.CharIteratorHelper">
            <summary>
            Helpers for the <see cref="T:Markdig.Helpers.ICharIterator"/> class.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.CodeBlock">
            <summary>
            Represents an indented code block.
            </summary>
            <remarks>
            Related to CommonMark spec: 4.4 Indented code blocks
            </remarks>
        </member>
        <member name="M:Markdig.Syntax.CodeBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.CodeBlock"/> class.
            </summary>
            <param name="parser">The parser.</param>
        </member>
        <member name="T:Markdig.Syntax.ContainerBlock">
            <summary>
            A base class for container blocks.
            </summary>
            <seealso cref="T:Markdig.Syntax.Block" />
        </member>
        <member name="M:Markdig.Syntax.ContainerBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.ContainerBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Syntax.ContainerBlock.LastChild">
            <summary>
            Gets the last child.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.ContainerBlock.GetEnumerator">
            <summary>
            Specialize enumerator.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Markdig.Syntax.FencedCodeBlock">
            <summary>
            Represents a fenced code block.
            </summary>
            <remarks>
            Related to CommonMark spec: 4.5 Fenced code blocks
            </remarks>
        </member>
        <member name="M:Markdig.Syntax.FencedCodeBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.FencedCodeBlock"/> class.
            </summary>
            <param name="parser">The parser.</param>
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.IndentCount">
            <summary>
            Gets or sets the indent count when the fenced code block was indented
            and we need to remove up to indent count chars spaces from the beginning of a line.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.FencedChar">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.OpeningFencedCharCount">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.TriviaAfterFencedChar">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.Info">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.UnescapedInfo">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.TriviaAfterInfo">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.Arguments">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.UnescapedArguments">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.TriviaAfterArguments">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.InfoNewLine">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.TriviaBeforeClosingFence">
            <inheritdoc />
        </member>
        <member name="P:Markdig.Syntax.FencedCodeBlock.ClosingFencedCharCount">
            <inheritdoc />
        </member>
        <member name="T:Markdig.Syntax.HeadingBlock">
            <summary>
            Represents a heading.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.HeadingBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.HeadingBlock"/> class.
            </summary>
            <param name="parser">The parser.</param>
        </member>
        <member name="P:Markdig.Syntax.HeadingBlock.HeaderChar">
            <summary>
            Gets or sets the header character used to defines this heading (usually #)
            </summary>
        </member>
        <member name="P:Markdig.Syntax.HeadingBlock.Level">
            <summary>
            Gets or sets the level of heading (starting at 1 for the lowest level).
            </summary>
        </member>
        <member name="P:Markdig.Syntax.HeadingBlock.IsSetext">
            <summary>
            True if this heading is a Setext heading.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.HeadingBlock.HeaderCharCount">
            <summary>
            Gets or sets the amount of - or = characters when <see cref="P:Markdig.Syntax.HeadingBlock.IsSetext"/> is true.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.HeadingBlock.SetextNewline">
            <summary>
            Gets or sets the newline of the first line when <see cref="P:Markdig.Syntax.HeadingBlock.IsSetext"/> is true.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.HeadingBlock.TriviaAfterAtxHeaderChar">
            <summary>
            Gets or sets the whitespace after the # character when <see cref="P:Markdig.Syntax.HeadingBlock.IsSetext"/> is false.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.HtmlBlock">
            <summary>
            Represents a group of lines that is treated as raw HTML (and will not be escaped in HTML output).
            </summary>
            <seealso cref="T:Markdig.Syntax.LeafBlock" />
        </member>
        <member name="M:Markdig.Syntax.HtmlBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.HtmlBlock"/> class.
            </summary>
            <param name="parser">The parser.</param>
        </member>
        <member name="P:Markdig.Syntax.HtmlBlock.Type">
            <summary>
            Gets or sets the type of block.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.HtmlBlockType">
            <summary>
            Defines the type of <see cref="T:Markdig.Syntax.HtmlBlock"/>
            </summary>
        </member>
        <member name="F:Markdig.Syntax.HtmlBlockType.DocumentType">
            <summary>
            A SGML document type starting by &lt;!LETTER.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.HtmlBlockType.CData">
            <summary>
            A raw CDATA sequence.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.HtmlBlockType.Comment">
            <summary>
            A HTML comment.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.HtmlBlockType.ProcessingInstruction">
            <summary>
            A SGM processing instruction tag &lt;?
            </summary>
        </member>
        <member name="F:Markdig.Syntax.HtmlBlockType.ScriptPreOrStyle">
            <summary>
            A script pre or style tag.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.HtmlBlockType.InterruptingBlock">
            <summary>
            An HTML interrupting block
            </summary>
        </member>
        <member name="F:Markdig.Syntax.HtmlBlockType.NonInterruptingBlock">
            <summary>
            An HTML non-interrupting block
            </summary>
        </member>
        <member name="T:Markdig.Syntax.IBlock">
            <summary>
            Base interface for a block structure. Either a <see cref="T:Markdig.Syntax.LeafBlock"/> or a <see cref="T:Markdig.Syntax.ContainerBlock"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.IMarkdownObject" />
        </member>
        <member name="P:Markdig.Syntax.IBlock.Column">
            <summary>
            Gets or sets the text column this instance was declared (zero-based).
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IBlock.Line">
            <summary>
            Gets or sets the text line this instance was declared (zero-based).
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IBlock.Parent">
            <summary>
            Gets the parent of this container. May be null.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IBlock.Parser">
            <summary>
            Gets the parser associated to this instance.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IBlock.IsOpen">
            <summary>
            Gets or sets a value indicating whether this instance is still open.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IBlock.IsBreakable">
            <summary>
            Gets or sets a value indicating whether this block is breakable. Default is true.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IBlock.RemoveAfterProcessInlines">
            <summary>
            Gets or sets a value indicating whether this block must be removed from its container after inlines have been processed.
            </summary>
        </member>
        <member name="E:Markdig.Syntax.IBlock.ProcessInlinesBegin">
            <summary>
            Occurs when the process of inlines begin.
            </summary>
        </member>
        <member name="E:Markdig.Syntax.IBlock.ProcessInlinesEnd">
            <summary>
            Occurs when the process of inlines ends for this instance.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IBlock.TriviaBefore">
            <summary>
            Trivia occurring before this block
            </summary>
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
        </member>
        <member name="P:Markdig.Syntax.IBlock.TriviaAfter">
            <summary>
            Trivia occurring after this block
            </summary>
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
        </member>
        <member name="T:Markdig.Syntax.IFencedBlock">
            <summary>
            A common interface for fenced block (e.g: <see cref="T:Markdig.Syntax.FencedCodeBlock"/> or <see cref="T:Markdig.Extensions.CustomContainers.CustomContainer"/>)
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.FencedChar">
            <summary>
            Gets or sets the fenced character used to open and close this fenced code block.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.OpeningFencedCharCount">
            <summary>
            Gets or sets the fenced character count used to open this fenced code block.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.TriviaAfterFencedChar">
            <summary>
            Gets or sets the trivia after the <see cref="P:Markdig.Syntax.IFencedBlock.FencedChar"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.Info">
            <summary>
            Gets or sets the language parsed after the first line of 
            the fenced code block. May be null.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.UnescapedInfo">
            <summary>
            Non-escaped <see cref="P:Markdig.Syntax.IFencedBlock.Info"/> exactly as in source markdown.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.TriviaAfterInfo">
            <summary>
            Gets or sets the trivia after the <see cref="P:Markdig.Syntax.IFencedBlock.Info"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.Arguments">
            <summary>
            Gets or sets the arguments after the <see cref="P:Markdig.Syntax.IFencedBlock.Info"/>.
            May be null.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.UnescapedArguments">
            <summary>
            Non-escaped <see cref="P:Markdig.Syntax.IFencedBlock.Arguments"/> exactly as in source markdown.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.TriviaAfterArguments">
            <summary>
            Gets or sets the trivia after the <see cref="P:Markdig.Syntax.IFencedBlock.Arguments"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.InfoNewLine">
            <summary>
            Newline of the line with the opening fenced chars.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.NewLine.None"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.TriviaBeforeClosingFence">
            <summary>
            Trivia before the closing fenced chars
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.ClosingFencedCharCount">
            <summary>
            Gets or sets the fenced character count used to close this fenced code block.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.IFencedBlock.NewLine">
            <summary>
            Newline after the last line, which is always the line containing the closing fence chars.
            "Inherited" from <see cref="P:Markdig.Syntax.Block.NewLine"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.NewLine.None"/>.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.IMarkdownObject">
            <summary>
            Base interface for a the Markdown syntax tree
            </summary>
        </member>
        <member name="M:Markdig.Syntax.IMarkdownObject.SetData(System.Object,System.Object)">
            <summary>
            Stores a key/value pair for this instance.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <exception cref="T:System.ArgumentNullException">if key is null</exception>
        </member>
        <member name="M:Markdig.Syntax.IMarkdownObject.ContainsData(System.Object)">
            <summary>
            Determines whether this instance contains the specified key data.
            </summary>
            <param name="key">The key.</param>
            <returns><c>true</c> if a data with the key is stored</returns>
            <exception cref="T:System.ArgumentNullException">if key is null</exception>
        </member>
        <member name="M:Markdig.Syntax.IMarkdownObject.GetData(System.Object)">
            <summary>
            Gets the associated data for the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns>The associated data or null if none</returns>
            <exception cref="T:System.ArgumentNullException">if key is null</exception>
        </member>
        <member name="M:Markdig.Syntax.IMarkdownObject.RemoveData(System.Object)">
            <summary>
            Removes the associated data for the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns><c>true</c> if the data was removed; <c>false</c> otherwise</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Markdig.Syntax.Inlines.AutolinkInline">
            <summary>
            An autolink (Section 6.7 CommonMark specs)
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.LeafInline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.AutolinkInline.IsEmail">
            <summary>
            Gets or sets a value indicating whether this instance is an email link.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.AutolinkInline.Url">
            <summary>
            Gets or sets the URL of this link.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.CodeInline">
            <summary>
            Represents a code span (Section 6.3 CommonMark specs)
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.LeafInline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.CodeInline.Delimiter">
            <summary>
            Gets or sets the delimiter character used by this code inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.CodeInline.DelimiterCount">
            <summary>
            Gets or sets the amount of delimiter characters used
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.CodeInline.Content">
            <summary>
            Gets or sets the content of the span.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.CodeInline.ContentWithTrivia">
            <summary>
            Gets or sets the content with trivia and whitespace.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.ContainerInline">
            <summary>
            A base class for container for <see cref="T:Markdig.Syntax.Inlines.Inline"/>.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.Inline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.ContainerInline.ParentBlock">
            <summary>
            Gets the parent block of this inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.ContainerInline.FirstChild">
            <summary>
            Gets the first child.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.ContainerInline.LastChild">
            <summary>
            Gets the last child.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Inlines.ContainerInline.Clear">
            <summary>
            Clears this instance by removing all its children.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Inlines.ContainerInline.AppendChild(Markdig.Syntax.Inlines.Inline)">
            <summary>
            Appends a child to this container.
            </summary>
            <param name="child">The child to append to this container..</param>
            <returns>This instance</returns>
            <exception cref="T:System.ArgumentNullException">If child is null</exception>
            <exception cref="T:System.ArgumentException">Inline has already a parent</exception>
        </member>
        <member name="M:Markdig.Syntax.Inlines.ContainerInline.ContainsChild(Markdig.Syntax.Inlines.Inline)">
            <summary>
            Checks if this instance contains the specified child.
            </summary>
            <param name="childToFind">The child to find.</param>
            <returns><c>true</c> if this instance contains the specified child; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Syntax.Inlines.ContainerInline.FindDescendants``1">
            <summary>
            Finds all the descendants.
            </summary>
            <typeparam name="T">Type of the descendants to find</typeparam>
            <returns>An enumeration of T</returns>
        </member>
        <member name="M:Markdig.Syntax.Inlines.ContainerInline.MoveChildrenAfter(Markdig.Syntax.Inlines.Inline)">
            <summary>
            Moves all the children of this container after the specified inline.
            </summary>
            <param name="parent">The parent.</param>
        </member>
        <member name="M:Markdig.Syntax.Inlines.ContainerInline.EmbraceChildrenBy(Markdig.Syntax.Inlines.ContainerInline)">
            <summary>
            Embraces this instance by the specified container.
            </summary>
            <param name="container">The container to use to embrace this instance.</param>
            <exception cref="T:System.ArgumentNullException">If the container is null</exception>
        </member>
        <member name="T:Markdig.Syntax.Inlines.DelimiterInline">
            <summary>
            Internal delimiter used by some parsers (e.g emphasis, tables).
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.ContainerInline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.DelimiterInline.Parser">
            <summary>
            Gets the parser.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.DelimiterInline.Type">
            <summary>
            Gets or sets the type of this delimiter.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.DelimiterInline.IsActive">
            <summary>
            Gets or sets a value indicating whether this instance is active.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Inlines.DelimiterInline.ToLiteral">
            <summary>
            Converts this delimiter to a literal.
            </summary>
            <returns>The string representation of this delimiter</returns>
        </member>
        <member name="T:Markdig.Syntax.Inlines.DelimiterType">
            <summary>
            Gets the type of a <see cref="T:Markdig.Syntax.Inlines.DelimiterInline"/>.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.DelimiterType.Undefined">
            <summary>
            An undefined open or close delimiter.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.DelimiterType.Open">
            <summary>
            An open delimiter.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.DelimiterType.Close">
            <summary>
            A close delimiter.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.EmphasisDelimiterInline">
            <summary>
            A delimiter used for parsing emphasis.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.DelimiterInline" />
        </member>
        <member name="M:Markdig.Syntax.Inlines.EmphasisDelimiterInline.#ctor(Markdig.Parsers.InlineParser,Markdig.Parsers.Inlines.EmphasisDescriptor)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Inlines.EmphasisDelimiterInline" /> class.
            </summary>
            <param name="parser">The parser.</param>
            <param name="descriptor">The descriptor.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Markdig.Syntax.Inlines.EmphasisDelimiterInline.#ctor(Markdig.Parsers.InlineParser,Markdig.Parsers.Inlines.EmphasisDescriptor,Markdig.Helpers.StringSlice)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Inlines.EmphasisDelimiterInline" /> class.
            </summary>
            <param name="parser">The parser.</param>
            <param name="descriptor">The descriptor.</param>
            <param name="content">The content.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Markdig.Syntax.Inlines.EmphasisDelimiterInline.Descriptor">
            <summary>
            Gets the descriptor for this emphasis.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.EmphasisDelimiterInline.DelimiterChar">
            <summary>
            The delimiter character found.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.EmphasisDelimiterInline.DelimiterCount">
            <summary>
            The number of delimiter characters found for this delimiter.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.EmphasisDelimiterInline.Content">
            <summary>
            The content as a <see cref="T:Markdig.Helpers.StringSlice"/>.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.EmphasisInline">
            <summary>
            An emphasis and strong emphasis (Section 6.4 CommonMark specs).
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.ContainerInline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.EmphasisInline.DelimiterChar">
            <summary>
            Gets or sets the delimiter character of this emphasis.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.EmphasisInline.IsDouble">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Markdig.Syntax.Inlines.EmphasisInline"/> is strong.
            <para>Marked obsolete as EmphasisInline can now be represented by more than two delimiter characters</para>
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.EmphasisInline.DelimiterCount">
            <summary>
            Gets or sets the number of delimiter characters for this emphasis.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.HtmlEntityInline">
            <summary>
            An entity HTML.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.LeafInline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.HtmlEntityInline.Original">
            <summary>
            Gets or sets the original HTML entity name
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.HtmlEntityInline.Transcoded">
            <summary>
            Gets or sets the transcoded literal that will be used for output
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.HtmlInline">
            <summary>
            A Raw HTML (Section 6.8 CommonMark specs).
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.LeafInline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.HtmlInline.Tag">
            <summary>
            Gets or sets the full declaration of this tag.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.IInline">
            <summary>
            Base interface for all syntax tree inlines.
            </summary>
            <seealso cref="T:Markdig.Syntax.IMarkdownObject" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.IInline.Parent">
            <summary>
            Gets the parent container of this inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.IInline.PreviousSibling">
            <summary>
            Gets the previous inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.IInline.NextSibling">
            <summary>
            Gets the next sibling inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.IInline.IsClosed">
            <summary>
            Gets or sets a value indicating whether this instance is closed.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.Inline">
            <summary>
            Base class for all syntax tree inlines.
            </summary>
            <seealso cref="T:Markdig.Syntax.MarkdownObject" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.Inline.Parent">
            <summary>
            Gets the parent container of this inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.Inline.PreviousSibling">
            <summary>
            Gets the previous inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.Inline.NextSibling">
            <summary>
            Gets the next sibling inline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.Inline.IsClosed">
            <summary>
            Gets or sets a value indicating whether this instance is closed.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.InsertAfter(Markdig.Syntax.Inlines.Inline)">
            <summary>
            Inserts the specified inline after this instance.
            </summary>
            <param name="next">The inline to insert after this instance.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException">Inline has already a parent</exception>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.InsertBefore(Markdig.Syntax.Inlines.Inline)">
            <summary>
            Inserts the specified inline before this instance.
            </summary>
            <param name="previous">The inline previous to insert before this instance.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException">Inline has already a parent</exception>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.Remove">
            <summary>
            Removes this instance from the current list and its parent
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.ReplaceBy(Markdig.Syntax.Inlines.Inline,System.Boolean)">
            <summary>
            Replaces this inline by the specified inline.
            </summary>
            <param name="inline">The inline.</param>
            <param name="copyChildren">if set to <c>true</c> the children of this instance are copied to the specified inline.</param>
            <returns>The last children</returns>
            <exception cref="T:System.ArgumentNullException">If inline is null</exception>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.ContainsParentOfType``1">
            <summary>
            Determines whether this instance contains a parent of the specified type.
            </summary>
            <typeparam name="T">Type of the parent to check</typeparam>
            <returns><c>true</c> if this instance contains a parent of the specified type; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.FindParentOfType``1">
            <summary>
            Iterates on parents of the specified type.
            </summary>
            <typeparam name="T">Type of the parent to iterate over</typeparam>
            <returns>An enumeration on the parents of the specified type</returns>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.DumpTo(System.IO.TextWriter)">
            <summary>
            Dumps this instance to <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="writer">The writer.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Markdig.Syntax.Inlines.Inline.DumpTo(System.IO.TextWriter,System.Int32)">
            <summary>
            Dumps this instance to <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="writer">The writer.</param>
            <param name="level">The level of indent.</param>
            <exception cref="T:System.ArgumentNullException">if writer is null</exception>
        </member>
        <member name="T:Markdig.Syntax.Inlines.LeafInline">
            <summary>
            A base class for a leaf inline.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.Inline" />
        </member>
        <member name="T:Markdig.Syntax.Inlines.LineBreakInline">
            <summary>
            A base class for a line break.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.LeafInline" />
        </member>
        <member name="T:Markdig.Syntax.Inlines.LinkDelimiterInline">
            <summary>
            A delimiter for a link.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.DelimiterInline" />
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkDelimiterInline.IsImage">
            <summary>
            Gets or sets a value indicating whether this delimiter is an image link.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkDelimiterInline.Label">
            <summary>
            Gets or sets the label of this link.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.LinkDelimiterInline.LabelSpan">
            <summary>
            The label span
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkDelimiterInline.LabelWithTrivia">
            <summary>
            Gets or sets the <see cref="P:Markdig.Syntax.Inlines.LinkDelimiterInline.Label"/> with trivia.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.LinkInline">
            <summary>
            A Link inline (Section 6.5 CommonMark specs)
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.ContainerInline" />
        </member>
        <member name="T:Markdig.Syntax.Inlines.LinkInline.GetUrlDelegate">
            <summary>
            A delegate to use if it is setup on this instance to allow late binding 
            of a Url.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Markdig.Syntax.Inlines.LinkInline.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Inlines.LinkInline"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Inlines.LinkInline.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Inlines.LinkInline"/> class.
            </summary>
            <param name="url">The URL.</param>
            <param name="title">The title.</param>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.IsImage">
            <summary>
            Gets or sets a value indicating whether this instance is an image link.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.Label">
            <summary>
            Gets or sets the label.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.LinkInline.LabelSpan">
            <summary>
            The label span
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.LabelWithTrivia">
            <summary>
            Gets or sets the <see cref="P:Markdig.Syntax.Inlines.LinkInline.Label"/> with trivia.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.LocalLabel">
            <summary>
            Gets or sets the type of label parsed
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Syntax.Inlines.LocalLabel.None"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.Reference">
            <summary>
            Gets or sets the reference this link is attached to. May be null.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.LinkRefDefLabel">
            <summary>
            Gets or sets the label as matched against the <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.LinkRefDefLabelWithTrivia">
            <summary>
            Gets or sets the <see cref="P:Markdig.Syntax.Inlines.LinkInline.LinkRefDefLabel"/> with trivia as matched against
            the <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/>
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.TriviaBeforeUrl">
            <summary>
            Gets or sets the trivia before the <see cref="P:Markdig.Syntax.Inlines.LinkInline.Url"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.UrlHasPointyBrackets">
            <summary>
            True if the <see cref="P:Markdig.Syntax.Inlines.LinkInline.Url"/> in the source document is enclosed
            in pointy brackets.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            false.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.Url">
            <summary>
            Gets or sets the URL.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.LinkInline.UrlSpan">
            <summary>
            The URL source span.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.UnescapedUrl">
            <summary>
            The <see cref="P:Markdig.Syntax.Inlines.LinkInline.Url"/> but with trivia and unescaped characters
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.TriviaAfterUrl">
            <summary>
            Any trivia after the <see cref="P:Markdig.Syntax.Inlines.LinkInline.Url"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.GetDynamicUrl">
            <summary>
            Gets or sets the GetDynamicUrl delegate. If this property is set, 
            it is used instead of <see cref="P:Markdig.Syntax.Inlines.LinkInline.Url"/> to get the Url from this instance.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.TitleEnclosingCharacter">
            <summary>
            Gets or sets the character used to enclose the <see cref="P:Markdig.Syntax.Inlines.LinkInline.Title"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.Title">
            <summary>
            Gets or sets the title.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.Inlines.LinkInline.TitleSpan">
            <summary>
            The title source span.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.UnescapedTitle">
            <summary>
            Gets or sets the <see cref="P:Markdig.Syntax.Inlines.LinkInline.Title"/> exactly as parsed from the
            source document including unescaped characters
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.TriviaAfterTitle">
            <summary>
            Gets or sets the trivia after the <see cref="P:Markdig.Syntax.Inlines.LinkInline.Title"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.IsShortcut">
            <summary>
            Gets or sets a boolean indicating if this link is a shortcut link to a <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/>
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LinkInline.IsAutoLink">
            <summary>
            Gets or sets a boolean indicating whether the inline link was parsed using markdown syntax or was automatic recognized.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.Inlines.LiteralInline">
            <summary>
            A literal inline.
            </summary>
            <seealso cref="T:Markdig.Syntax.Inlines.LeafInline" />
        </member>
        <member name="M:Markdig.Syntax.Inlines.LiteralInline.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Inlines.LiteralInline"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.Inlines.LiteralInline.#ctor(Markdig.Helpers.StringSlice)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Inlines.LiteralInline"/> class.
            </summary>
            <param name="content">The content.</param>
        </member>
        <member name="M:Markdig.Syntax.Inlines.LiteralInline.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.Inlines.LiteralInline"/> class.
            </summary>
            <param name="text">The text.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="F:Markdig.Syntax.Inlines.LiteralInline.Content">
            <summary>
            The content as a <see cref="T:Markdig.Helpers.StringSlice"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.Inlines.LiteralInline.IsFirstCharacterEscaped">
            <summary>
            A boolean indicating whether the first character of this literal is escaped by `\`.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.LeafBlock">
            <summary>
            Base class for all leaf blocks.
            </summary>
            <seealso cref="T:Markdig.Syntax.Block" />
        </member>
        <member name="M:Markdig.Syntax.LeafBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.LeafBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="F:Markdig.Syntax.LeafBlock.Lines">
            <summary>
            Gets or sets the string lines accumulated for this leaf block.
            May be null after process inlines have occurred.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LeafBlock.Inline">
            <summary>
            Gets or sets the inline syntax tree (may be null).
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LeafBlock.ProcessInlines">
            <summary>
            Gets or sets a value indicating whether <see cref="F:Markdig.Syntax.LeafBlock.Lines"/> must be processed
            as inline into the <see cref="P:Markdig.Syntax.LeafBlock.Inline"/> property.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.LeafBlock.AppendLine(Markdig.Helpers.StringSlice@,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Appends the specified line to this instance.
            </summary>
            <param name="slice">The slice.</param>
            <param name="column">The column.</param>
            <param name="line">The line.</param>
            <param name="sourceLinePosition"></param>
            <param name="trackTrivia">Whether to keep track of trivia such as whitespace, extra heading characters and unescaped string values.</param>
        </member>
        <member name="T:Markdig.Syntax.LinkReferenceDefinition">
            <summary>
            A link reference definition (Section 4.7 CommonMark specs)
            </summary>
            <seealso cref="T:Markdig.Syntax.LeafBlock" />
        </member>
        <member name="T:Markdig.Syntax.LinkReferenceDefinition.CreateLinkInlineDelegate">
            <summary>
            Creates an inline link for the specified <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/>.
            </summary>
            <param name="inlineState">State of the inline.</param>
            <param name="linkRef">The link reference.</param>
            <param name="child">The child.</param>
            <returns>An inline link or null to use the default implementation</returns>
        </member>
        <member name="M:Markdig.Syntax.LinkReferenceDefinition.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.LinkReferenceDefinition.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/> class.
            </summary>
            <param name="label">The label.</param>
            <param name="url">The URL.</param>
            <param name="title">The title.</param>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.Label">
            <summary>
            Gets or sets the label. Text is normalized according to spec.
            </summary>
            https://spec.commonmark.org/0.29/#matches
        </member>
        <member name="F:Markdig.Syntax.LinkReferenceDefinition.LabelSpan">
            <summary>
            The label span
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.LabelWithTrivia">
            <summary>
            Non-normalized Label (includes trivia)
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.TriviaBeforeUrl">
            <summary>
            Whitespace before the <see cref="P:Markdig.Syntax.LinkReferenceDefinition.Url"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.Url">
            <summary>
            Gets or sets the URL.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.LinkReferenceDefinition.UrlSpan">
            <summary>
            The URL span
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.UnescapedUrl">
            <summary>
            Non-normalized <see cref="P:Markdig.Syntax.LinkReferenceDefinition.Url"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.UrlHasPointyBrackets">
            <summary>
            True when the <see cref="P:Markdig.Syntax.LinkReferenceDefinition.Url"/> is enclosed in point brackets in the source document.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            false.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.TriviaBeforeTitle">
            <summary>
            gets or sets the whitespace before a <see cref="P:Markdig.Syntax.LinkReferenceDefinition.Title"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.Title">
            <summary>
            Gets or sets the title.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.LinkReferenceDefinition.TitleSpan">
            <summary>
            The title span
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.UnescapedTitle">
            <summary>
            Non-normalized <see cref="P:Markdig.Syntax.LinkReferenceDefinition.Title"/>.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.TitleEnclosingCharacter">
            <summary>
            Gets or sets the character the <see cref="P:Markdig.Syntax.LinkReferenceDefinition.Title"/> is enclosed in.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise \0.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinition.CreateLinkInline">
            <summary>
            Gets or sets the create link inline callback for this instance.
            </summary>
            <remarks>
            This callback is called when an inline link is matching this reference definition.
            </remarks>
        </member>
        <member name="M:Markdig.Syntax.LinkReferenceDefinition.TryParse``1(``0@,Markdig.Syntax.LinkReferenceDefinition@)">
            <summary>
            Tries to the parse the specified text into a definition.
            </summary>
            <typeparam name="T">Type of the text</typeparam>
            <param name="text">The text.</param>
            <param name="block">The block.</param>
            <returns><c>true</c> if parsing is successful; <c>false</c> otherwise</returns>
        </member>
        <member name="M:Markdig.Syntax.LinkReferenceDefinition.TryParseTrivia``1(``0@,Markdig.Syntax.LinkReferenceDefinition@,Markdig.Syntax.SourceSpan@,Markdig.Syntax.SourceSpan@,Markdig.Syntax.SourceSpan@,Markdig.Syntax.SourceSpan@,Markdig.Syntax.SourceSpan@,Markdig.Syntax.SourceSpan@,Markdig.Syntax.SourceSpan@)">
            <summary>
            Tries to the parse the specified text into a definition.
            </summary>
            <typeparam name="T">Type of the text</typeparam>
            <param name="text">The text.</param>
            <param name="block">The block.</param>
            <param name="triviaBeforeLabel"></param>
            <param name="labelWithTrivia"></param>
            <param name="triviaBeforeUrl"></param>
            <param name="unescapedUrl"></param>
            <param name="triviaBeforeTitle"></param>
            <param name="unescapedTitle"></param>
            <param name="triviaAfterTitle"></param>
            <returns><c>true</c> if parsing is successful; <c>false</c> otherwise</returns>
        </member>
        <member name="T:Markdig.Syntax.LinkReferenceDefinitionExtensions">
            <summary>
            Extension methods for accessing <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/> attached at the document level.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.LinkReferenceDefinitionGroup">
            <summary>
            Contains all the <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/> found in a document.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Syntax.LinkReferenceDefinitionGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.LinkReferenceDefinitionGroup"/> class.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.LinkReferenceDefinitionGroup.Links">
            <summary>
            Gets an association between a label and the corresponding <see cref="T:Markdig.Syntax.LinkReferenceDefinition"/>
            </summary>
        </member>
        <member name="T:Markdig.Syntax.ListBlock">
            <summary>
            A list (Section 5.3 CommonMark specs)
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Syntax.ListBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.ListBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Syntax.ListBlock.IsOrdered">
            <summary>
            Gets or sets a value indicating whether the list is ordered.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.ListBlock.BulletType">
            <summary>
            Gets or sets the bullet character used by this list.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.ListBlock.OrderedStart">
            <summary>
            Gets or sets the ordered start number (valid when <see cref="P:Markdig.Syntax.ListBlock.IsOrdered"/> is <c>true</c>)
            </summary>
        </member>
        <member name="P:Markdig.Syntax.ListBlock.DefaultOrderedStart">
            <summary>
            Gets or sets the default ordered start ("1" for BulletType = '1')
            </summary>
        </member>
        <member name="P:Markdig.Syntax.ListBlock.OrderedDelimiter">
            <summary>
            Gets or sets the ordered delimiter character (usually `.` or `)`) found after an ordered list item.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.ListBlock.IsLoose">
            <summary>
            Gets or sets a value indicating whether this instance is loose.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.ListItemBlock">
            <summary>
            A list item (Section 5.2 CommonMark specs)
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Syntax.ListItemBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.ListItemBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Syntax.ListItemBlock.Order">
            <summary>
            The number defined for this <see cref="T:Markdig.Syntax.ListItemBlock"/> in an ordered list
            </summary>
        </member>
        <member name="P:Markdig.Syntax.ListItemBlock.SourceBullet">
            <summary>
            Gets or sets the bullet as parsed in the source document.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise
            <see cref="F:Markdig.Helpers.StringSlice.Empty"/>.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.MarkdownDocument">
            <summary>
            The root Markdown document.
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Syntax.MarkdownDocument.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.MarkdownDocument"/> class.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.MarkdownDocument.LineCount">
            <summary>
            Gets the number of lines in this <see cref="T:Markdig.Syntax.MarkdownDocument"/>
            </summary>
        </member>
        <member name="P:Markdig.Syntax.MarkdownDocument.LineStartIndexes">
            <summary>
            Gets a list of zero-based indexes of line beginnings in the source span
            <para>Available if <see cref="P:Markdig.MarkdownPipelineBuilder.PreciseSourceLocation"/> is used, otherwise null</para>
            </summary>
        </member>
        <member name="T:Markdig.Syntax.MarkdownObject">
            <summary>
            Base implementation for a the Markdown syntax tree.
            </summary>
        </member>
        <member name="F:Markdig.Syntax.MarkdownObject._attachedDatas">
            <summary>
            The attached datas. Use internally a simple array instead of a Dictionary{Object,Object}
            as we expect less than 5~10 entries, usually typically 1 (HtmlAttributes)
            so it will gives faster access than a Dictionary, and lower memory occupation
            </summary>
        </member>
        <member name="P:Markdig.Syntax.MarkdownObject.Column">
            <summary>
            Gets or sets the text column this instance was declared (zero-based).
            </summary>
        </member>
        <member name="P:Markdig.Syntax.MarkdownObject.Line">
            <summary>
            Gets or sets the text line this instance was declared (zero-based).
            </summary>
        </member>
        <member name="F:Markdig.Syntax.MarkdownObject.Span">
            <summary>
            The source span
            </summary>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObject.ToPositionText">
            <summary>
            Gets a string of the location in the text.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObject.SetData(System.Object,System.Object)">
            <summary>
            Stores a key/value pair for this instance.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <exception cref="T:System.ArgumentNullException">if key is null</exception>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObject.ContainsData(System.Object)">
            <summary>
            Determines whether this instance contains the specified key data.
            </summary>
            <param name="key">The key.</param>
            <returns><c>true</c> if a data with the key is stored</returns>
            <exception cref="T:System.ArgumentNullException">if key is null</exception>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObject.GetData(System.Object)">
            <summary>
            Gets the associated data for the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns>The associated data or null if none</returns>
            <exception cref="T:System.ArgumentNullException">if key is null</exception>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObject.RemoveData(System.Object)">
            <summary>
            Removes the associated data for the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns><c>true</c> if the data was removed; <c>false</c> otherwise</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Markdig.Syntax.MarkdownObjectExtensions">
            <summary>
            Extensions for visiting <see cref="T:Markdig.Syntax.Block"/> or <see cref="T:Markdig.Syntax.Inlines.Inline"/>
            </summary>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObjectExtensions.Descendants(Markdig.Syntax.MarkdownObject)">
            <summary>
            Iterates over the descendant elements for the specified markdown element, including <see cref="T:Markdig.Syntax.Block"/> and <see cref="T:Markdig.Syntax.Inlines.Inline"/>.
            <para>The descendant elements are returned in DFS-like order.</para>
            </summary>
            <param name="markdownObject">The markdown object.</param>
            <returns>An iteration over the descendant elements</returns>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObjectExtensions.Descendants``1(Markdig.Syntax.MarkdownObject)">
            <summary>
            Iterates over the descendant elements for the specified markdown element, including <see cref="T:Markdig.Syntax.Block"/> and <see cref="T:Markdig.Syntax.Inlines.Inline"/> and filters by the type <typeparamref name="T"/>.
            <para>The descendant elements are returned in DFS-like order.</para>
            </summary>
            <typeparam name="T">Type to use for filtering the descendants</typeparam>
            <param name="markdownObject">The markdown object.</param>
            <returns>An iteration over the descendant elements</returns>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObjectExtensions.Descendants``1(Markdig.Syntax.Inlines.ContainerInline)">
            <summary>
            Iterates over the descendant elements for the specified markdown <see cref="T:Markdig.Syntax.Inlines.Inline" /> element and filters by the type <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">Type to use for filtering the descendants</typeparam>
            <param name="inline">The inline markdown object.</param>
            <returns>
            An iteration over the descendant elements
            </returns>
        </member>
        <member name="M:Markdig.Syntax.MarkdownObjectExtensions.Descendants``1(Markdig.Syntax.ContainerBlock)">
            <summary>
            Iterates over the descendant elements for the specified markdown <see cref="T:Markdig.Syntax.Block" /> element and filters by the type <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">Type to use for filtering the descendants</typeparam>
            <param name="block">The markdown object.</param>
            <returns>
            An iteration over the descendant elements
            </returns>
        </member>
        <member name="T:Markdig.Syntax.EmptyBlock">
            <summary>
            Block representing a document with characters but no blocks. This can
            happen when an input document consists solely of trivia.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.ParagraphBlock">
            <summary>
            Represents a paragraph.
            </summary>
            <remarks>
            Related to CommonMark spec: 4.8 Paragraphs
            </remarks>
        </member>
        <member name="M:Markdig.Syntax.ParagraphBlock.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.ParagraphBlock"/> class.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.ParagraphBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.ParagraphBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="T:Markdig.Syntax.QuoteBlock">
            <summary>
            A block quote (Section 5.1 CommonMark specs)
            </summary>
            <seealso cref="T:Markdig.Syntax.ContainerBlock" />
        </member>
        <member name="M:Markdig.Syntax.QuoteBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.QuoteBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
        <member name="P:Markdig.Syntax.QuoteBlock.QuoteLines">
            <summary>
            Gets or sets the trivia per line of this QuoteBlock.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled, otherwise null.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.QuoteBlock.QuoteChar">
            <summary>
            Gets or sets the quote character (usually `&gt;`)
            </summary>
        </member>
        <member name="T:Markdig.Syntax.QuoteBlockLine">
            <summary>
            Represents trivia per line part of a QuoteBlock.
            Trivia: only parsed when <see cref="P:Markdig.MarkdownPipeline.TrackTrivia"/> is enabled.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.QuoteBlockLine.TriviaBefore">
            <summary>
            Gets or sets trivia occurring before the first quote character.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.QuoteBlockLine.QuoteChar">
            <summary>
            True when this QuoteBlock line has a quote character. False when
            this line is a "lazy line".
            </summary>
        </member>
        <member name="P:Markdig.Syntax.QuoteBlockLine.HasSpaceAfterQuoteChar">
            <summary>
            True if a space is parsed right after the quote character.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.QuoteBlockLine.TriviaAfter">
            <summary>
            Gets or sets the trivia after the the space after the quote character.
            The first space is assigned to <see cref="P:Markdig.Syntax.QuoteBlockLine.HasSpaceAfterQuoteChar"/>, subsequent
            trivia is assigned to this property.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.QuoteBlockLine.NewLine">
            <summary>
            Gets or sets the newline of this QuoeBlockLine.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.SourceSpan">
            <summary>
            A span of text.
            </summary>
        </member>
        <member name="M:Markdig.Syntax.SourceSpan.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.SourceSpan"/> struct.
            </summary>
            <param name="start">The start.</param>
            <param name="end">The end.</param>
        </member>
        <member name="P:Markdig.Syntax.SourceSpan.Start">
            <summary>
            Gets or sets the starting character position from the original text source. 
            Note that for inline elements, this is only valid if <see cref="M:Markdig.MarkdownExtensions.UsePreciseSourceLocation(Markdig.MarkdownPipelineBuilder)"/> is setup on the pipeline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.SourceSpan.End">
            <summary>
            Gets or sets the ending character position from the original text source.
            Note that for inline elements, this is only valid if <see cref="M:Markdig.MarkdownExtensions.UsePreciseSourceLocation(Markdig.MarkdownPipelineBuilder)"/> is setup on the pipeline.
            </summary>
        </member>
        <member name="P:Markdig.Syntax.SourceSpan.Length">
            <summary>
            Gets the character length of this element within the original source code.
            </summary>
        </member>
        <member name="T:Markdig.Syntax.ThematicBreakBlock">
            <summary>
            Represents a thematic break (Section 4.1 CommonMark specs).
            </summary>
        </member>
        <member name="M:Markdig.Syntax.ThematicBreakBlock.#ctor(Markdig.Parsers.BlockParser)">
            <summary>
            Initializes a new instance of the <see cref="T:Markdig.Syntax.ThematicBreakBlock"/> class.
            </summary>
            <param name="parser">The parser used to create this block.</param>
        </member>
    </members>
</doc>
