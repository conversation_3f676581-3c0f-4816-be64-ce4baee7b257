using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using Telerik.Reporting;

namespace SETWorks.Code.UTILITIES.TelerikReporting
{
    /// <summary>
    /// Example showing how to integrate all the Telerik reporting optimizations
    /// This demonstrates the complete solution for preventing Redis session overload
    /// </summary>
    public class ReportIntegrationExample
    {
        private static readonly log4net.ILog Log = log4net.LogManager.GetLogger(typeof(ReportIntegrationExample));

        /// <summary>
        /// Example 1: Convert an existing large report to use optimized storage and caching
        /// </summary>
        public static Report CreateOptimizedLargeReport(string reportName, string sqlQuery)
        {
            try
            {
                var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;
                
                // Step 1: Analyze if optimization is needed
                var recordCount = GetRecordCount(connectionString, sqlQuery);
                var shouldOptimize = ReportOptimizationHelper.ShouldOptimizeReport(recordCount);
                
                Log.Info($"Report '{reportName}' has {recordCount} records. Should optimize: {shouldOptimize}");

                if (shouldOptimize)
                {
                    // Step 2: Create optimized report with pagination
                    return CreatePaginatedReport(reportName, connectionString, sqlQuery, recordCount);
                }
                else
                {
                    // Step 3: Use cached version for smaller reports
                    return CreateCachedReport(reportName, connectionString, sqlQuery);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error creating optimized report '{reportName}'", ex);
                throw;
            }
        }

        /// <summary>
        /// Example 2: Create a report with paginated data source
        /// </summary>
        private static Report CreatePaginatedReport(string reportName, string connectionString, 
            string baseQuery, int totalRecords)
        {
            // Create paginated data source
            var paginatedSource = PaginatedReportDataSourceFactory.CreateFromSql(
                connectionString, baseQuery, pageSize: 1000);

            // Create report with paginated data
            var report = new Report
            {
                Name = reportName + "_Paginated",
                DataSource = new ObjectDataSource
                {
                    DataSource = paginatedSource,
                    DataMember = "GetAllData"
                }
            };

            // Add report parameters for monitoring
            report.ReportParameters.Add(new ReportParameter
            {
                Name = "TotalRecords",
                Value = totalRecords.ToString(),
                Visible = false
            });

            report.ReportParameters.Add(new ReportParameter
            {
                Name = "OptimizationType",
                Value = "Paginated",
                Visible = false
            });

            Log.Info($"Created paginated report '{reportName}' with {totalRecords} records");
            return report;
        }

        /// <summary>
        /// Example 3: Create a report with memory caching
        /// </summary>
        private static Report CreateCachedReport(string reportName, string connectionString, string sqlQuery)
        {
            var cacheProvider = CachedReportDataProviderFactory.Default;
            var reportKey = $"{reportName}:{sqlQuery.GetHashCode()}";

            // Create cached data provider
            Func<DataTable> dataProvider = () => ExecuteQuery(connectionString, sqlQuery);
            
            var report = new Report
            {
                Name = reportName + "_Cached",
                DataSource = new ObjectDataSource
                {
                    DataSource = cacheProvider.GetCachedReportData(reportKey, dataProvider),
                    DataMember = "DefaultView"
                }
            };

            report.ReportParameters.Add(new ReportParameter
            {
                Name = "OptimizationType",
                Value = "Cached",
                Visible = false
            });

            Log.Info($"Created cached report '{reportName}'");
            return report;
        }

        /// <summary>
        /// Example 4: Hybrid approach - cached pagination for very large reports
        /// </summary>
        public static Report CreateHybridOptimizedReport(string reportName, string connectionString, 
            string baseQuery, int totalRecords)
        {
            var cacheProvider = CachedReportDataProviderFactory.Default;
            var reportKey = $"{reportName}:Hybrid:{baseQuery.GetHashCode()}";

            // Create cached paginated data source
            Func<int, int, DataTable> dataProvider = (skip, take) =>
            {
                var pagedQuery = $"{baseQuery} ORDER BY (SELECT NULL) OFFSET {skip} ROWS FETCH NEXT {take} ROWS ONLY";
                return ExecuteQuery(connectionString, pagedQuery);
            };

            var paginatedSource = cacheProvider.GetCachedPaginatedData(
                reportKey, dataProvider, totalRecords, pageSize: 500);

            var report = new Report
            {
                Name = reportName + "_Hybrid",
                DataSource = new ObjectDataSource
                {
                    DataSource = paginatedSource,
                    DataMember = "GetAllData"
                }
            };

            report.ReportParameters.Add(new ReportParameter
            {
                Name = "OptimizationType",
                Value = "Hybrid_Cached_Paginated",
                Visible = false
            });

            Log.Info($"Created hybrid optimized report '{reportName}' with {totalRecords} records");
            return report;
        }

        /// <summary>
        /// Example 5: Monitor and maintain report performance
        /// </summary>
        public static void MonitorReportPerformance()
        {
            try
            {
                // Check Redis memory usage
                if (ReportStorageConfiguration.UseRedisStorage())
                {
                    ReportStorageMonitor.LogMemoryUsage();
                    
                    var reportKeyCount = ReportStorageMonitor.GetReportStorageKeyCount();
                    Log.Info($"Current report storage keys: {reportKeyCount}");
                    
                    // Clean up expired entries if needed
                    var cleanedCount = ReportStorageMonitor.ClearExpiredReportData();
                    if (cleanedCount > 0)
                    {
                        Log.Info($"Cleaned up {cleanedCount} expired report entries");
                    }
                }

                // Check cache statistics
                var cacheStats = CachedReportDataProviderFactory.Default.GetCacheStatistics();
                Log.Info($"Cache provider: {cacheStats.CacheProvider}, Default timeout: {cacheStats.DefaultTimeout}");
            }
            catch (Exception ex)
            {
                Log.Error("Error monitoring report performance", ex);
            }
        }

        /// <summary>
        /// Example 6: Migrate existing report to use optimized storage
        /// </summary>
        public static void MigrateExistingReport(Report existingReport, string connectionString)
        {
            try
            {
                // Analyze existing report data source
                if (existingReport.DataSource is SqlDataSource sqlDataSource)
                {
                    var query = sqlDataSource.SelectCommand;
                    var recordCount = GetRecordCount(connectionString, query);
                    
                    var analysis = ReportOptimizationHelper.AnalyzeDataTableSize(
                        ExecuteQuery(connectionString, query));
                    
                    Log.Info($"Report analysis: {analysis.Reason}");
                    
                    if (analysis.ShouldOptimize)
                    {
                        // Replace with optimized data source
                        var optimizedSource = ReportOptimizationHelper.CreateOptimizedSqlReport(
                            existingReport.Name, connectionString, query);
                        
                        existingReport.DataSource = optimizedSource.DataSource;
                        
                        Log.Info($"Migrated report '{existingReport.Name}' to optimized storage");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error migrating report '{existingReport.Name}'", ex);
            }
        }

        #region Helper Methods

        private static int GetRecordCount(string connectionString, string query)
        {
            var countQuery = $"SELECT COUNT(*) FROM ({query}) AS CountQuery";
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var command = new SqlCommand(countQuery, connection))
                {
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        private static DataTable ExecuteQuery(string connectionString, string query)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var adapter = new SqlDataAdapter(query, connection))
                {
                    var dataTable = new DataTable();
                    adapter.Fill(dataTable);
                    return dataTable;
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// Usage examples and best practices
    /// </summary>
    public static class ReportOptimizationBestPractices
    {
        /// <summary>
        /// Best practice: Use this method to determine the optimal strategy for any report
        /// </summary>
        public static string GetOptimalStrategy(int recordCount, int averageRowSizeBytes = 1024)
        {
            var estimatedSizeBytes = recordCount * averageRowSizeBytes;
            var sizeMB = estimatedSizeBytes / 1024.0 / 1024.0;

            if (sizeMB < 5)
                return "Use standard caching with MemoryCacheProvider";
            else if (sizeMB < 50)
                return "Use cached data with shorter timeout";
            else if (sizeMB < 200)
                return "Use pagination with memory caching";
            else
                return "Use Redis storage with pagination and minimal caching";
        }
    }
}
