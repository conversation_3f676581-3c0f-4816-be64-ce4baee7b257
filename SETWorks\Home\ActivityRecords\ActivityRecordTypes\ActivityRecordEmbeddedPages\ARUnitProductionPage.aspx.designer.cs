﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------



public partial class Home_ActivityRecords_ActivityRecordTypes_ARUnitProductionPage
{

    /// <summary>
    /// RadCodeBlockBillableWAI control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadCodeBlock RadCodeBlockBillableWAI;

    /// <summary>
    /// RadScriptManagerActivityRecords22 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadScriptManager RadScriptManagerActivityRecords22;

    /// <summary>
    /// RadAjaxManagerARProxy control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadAjaxManager RadAjaxManagerARProxy;

    /// <summary>
    /// DIVUserComment control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.HtmlControls.HtmlGenericControl DIVUserComment;

    /// <summary>
    /// RadAjaxLoadingPanel222 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadAjaxLoadingPanel RadAjaxLoadingPanel222;

    /// <summary>
    /// ARProductionPanelItems control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel ARProductionPanelItems;

    /// <summary>
    /// LabelNotesLegend control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label LabelNotesLegend;

    /// <summary>
    /// ItemStepValidationSummary control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.HtmlControls.HtmlGenericControl ItemStepValidationSummary;

    /// <summary>
    /// RadGridItems control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadGrid RadGridItems;

    /// <summary>
    /// SQLDataSourceItems control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.SqlDataSource SQLDataSourceItems;

    /// <summary>
    /// SQLDataSourceItem control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.SqlDataSource SQLDataSourceItem;

    /// <summary>
    /// ARProductionPanelTasks control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Panel ARProductionPanelTasks;

    /// <summary>
    /// Label1 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.Label Label1;

    /// <summary>
    /// TaskValidationSummary control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.HtmlControls.HtmlGenericControl TaskValidationSummary;

    /// <summary>
    /// RadGridTasks control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadGrid RadGridTasks;

    /// <summary>
    /// SQLDataSourceTasks control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.SqlDataSource SQLDataSourceTasks;

    /// <summary>
    /// SQLDataSourceTask control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.SqlDataSource SQLDataSourceTask;

    /// <summary>
    /// HiddenACTIVITY_RECORD_GROUP_ID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenACTIVITY_RECORD_GROUP_ID;

    /// <summary>
    /// HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID;

    /// <summary>
    /// HiddenCONSUMER_ID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCONSUMER_ID;

    /// <summary>
    /// HiddenOUTCOME_ID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenOUTCOME_ID;

    /// <summary>
    /// HiddenGOAL_ID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenGOAL_ID;

    /// <summary>
    /// HiddenCURRENTWINDOWUSERID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCURRENTWINDOWUSERID;

    /// <summary>
    /// HiddenCURRENTWINDOWCLIENTID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCURRENTWINDOWCLIENTID;

    /// <summary>
    /// HiddenCURRENTWINDOWUSERNAME control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCURRENTWINDOWUSERNAME;

    /// <summary>
    /// HiddenEDIT_USER_COMMENT_ID control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenEDIT_USER_COMMENT_ID;

    /// <summary>
    /// HiddenCOMMENT_CREATOR control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCOMMENT_CREATOR;

    /// <summary>
    /// HiddenACTION control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenACTION;

    /// <summary>
    /// HiddenACTIVITY_RECORD_COMMENT_TYPE control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenACTIVITY_RECORD_COMMENT_TYPE;

    /// <summary>
    /// HiddenCAN_ADD_AND_EDIT control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCAN_ADD_AND_EDIT;

    /// <summary>
    /// HiddenPOST_SAVE_PARAMETERS control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenPOST_SAVE_PARAMETERS;

    /// <summary>
    /// HiddenUPDate control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenUPDate;

    /// <summary>
    /// HiddenCan_MODIFY_DATES control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::System.Web.UI.WebControls.HiddenField HiddenCan_MODIFY_DATES;

    /// <summary>
    /// RadCodeBlock1 control.
    /// </summary>
    /// <remarks>
    /// Auto-generated field.
    /// To modify move field declaration from designer file to code-behind file.
    /// </remarks>
    protected global::Telerik.Web.UI.RadCodeBlock RadCodeBlock1;
}
