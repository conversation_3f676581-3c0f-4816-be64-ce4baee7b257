﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using BOs;
using SetWorks.Common.Tools.DataExtractor.Core;
using SetWorks.Core.Extensions;

namespace SetWorks.Common.Tools.DataExtractor.V3.Core.GenericTransformations;

public class FormatColumnV3 : ITransformation
{
    private TransformationField[] transformationFields;
    private const string DATE_AND_TIME_FORMAT = "DATE_AND_TIME_FORMAT";
    private const string TIME_FORMAT = "TIME_FORMAT";
    private const string NUMBER_FORMAT = "NUMBER_FORMAT";
    private const string DECIMAL_FORMAT = "DECIMAL_FORMAT";
    private const string DECIMAL_ROUNDING = "DECIMAL_ROUNDING";
    private const string DECIMAL_ROUNDING_PLACE = "DECIMAL_ROUNDING_PLACE";
    private const string TEXT_FORMAT = "TEXT_FORMAT";
    private const string COLUMN_TO_FORMAT = "COL_1";
    private const string DATA_TYPE = "DATA_TYPE";
    private const string INSERT_CURRENT_DATETIME = "INSERT_CURRENT_DATETIME";

    private readonly Dictionary<string, string> DATA_TYPES = new()
    {
        ["TEXT"] = "Text",
        ["DATE_TIME"] = "Date/Time",
        ["NUMERIC"] = "Numeric"
    };

    private readonly Dictionary<string, string> DATE_AND_TIME_FORMAT_TYPES = new ()
    {
        [""] = "",
        ["MM/dd/yyyy"] = "*03/09/2021",
        ["M/d/yyyy"] = "3/9/2021",
        ["MM/dd/yy"] = "03/09/21",
        ["M/d/yy"] = "3/9/21",
        ["MM/dd"] = "03/09",
        ["M/d"] = "3/9",
        ["yyyy"] = "2021",
        ["MM"] = "03",
        ["MMM"] = "Mar",
        ["MMMM"] = "March",
        ["dddd, MMMM dd, yyyy"] = "Tuesday, March 09, 2021",
        ["dddd, MMMM d, yyyy"] = "Tuesday, March 9, 2021",
        ["yyyy-MM-dd"] = "2021-03-09",
        ["yyyy-M-d"] = "2021-3-9",
        ["yy-MM-dd"] = "21-03-09",
        ["yy-M-d"] = "21-3-9",
        ["MM-dd"] = "03-09",
        ["M-d"] = "3-9",
        ["dd-MMM-yy"] = "09-Mar-21",
        ["d-MMM-yy"] = "9-Mar-21",
        ["dd-MMM"] = "09-Mar",
        ["d-MMM"] = "9-Mar",
        ["dd-MMMM-yy"] = "09-March-21",
        ["d-MMMM-yy"] = "9-March-21",
        ["dd-MMMM"] = "09-March",
        ["d-MMMM"] = "9-March",
        ["MMMM dd, yyyy"] = "March 09, 2021",
        ["MMMM d, yyyy"] = "March 9, 2021",
        ["MMMM yyyy"] = "March 2021",
        ["MMMM-yy"] = "March-21",
        ["MMM-yy"] = "Mar-21",
        ["MM/yyyy"] = "03/2021",
        ["M/yyyy"] = "3/2021",
        ["MM-yy"] = "03-21",
        ["M-yy"] = "3-21",
        ["MM/dd/yyyy h:mm tt"] = "03/09/2021 1:09 PM",
        ["M/d/yyyy h:mm tt"] = "3/9/2021 1:09 PM",
        ["MM/dd/yyyy HH:mm"] = "03/09/2021 13:09",
        ["M/d/yyyy HH:mm"] = "3/9/2021 13:09",
        ["MM/dd/yyyy HHmm"] = "03/09/2021 1309",
        ["M/d/yyyy HHmm"] = "3/9/2021 1309",
        ["yyyyMMdd"] = "20210309",
        ["yyyyMMddHHmmss"] = "20210309130948"
    };

    private readonly Dictionary<string, string> TIME_FORMAT_TYPES = new ()
    {
        [""] = "",
        ["h:mm tt"] = "*1:30 PM",
        ["hh:mm tt"] = "01:30 PM",
        ["h:mm:ss tt"] = "1:30:48 PM",
        ["hh:mm:ss tt"] = "01:30:38 PM",
        ["HH:mm"] = "13:30",
        ["HH:mm:ss"] = "13:30:48",
        ["HH:mm"] = "1330"
    };

    private readonly Dictionary<string, string> NUMBER_FORMAT_TYPES = new ()
    {
        [""] = "",
        ["N"] = "Number",
        ["F"] = "Raw Number (without comma separators)",
        ["C"] = "Currency ($)",
        ["P"] = "Percentage (%)"
    };
        
    private readonly Dictionary<string, string> DECIMAL_FORMAT_TYPES = new ()
    {
        ["0"] = "1 (0)",
        ["1"] = "1.0 (1)",
        ["2"] = "1.00 (2)",
        ["3"] = "1.000 (3)",
        ["4"] = "1.0000 (4)",
        ["5"] = "1.00000 (5)",
        ["6"] = "1.000000 (6)"
    };
    
    private readonly Dictionary<string, string> DECIMAL_ROUNDING_TYPES = new ()
    {
        ["DOWN"] = "No Rounding (if 2 decimals, both 1.014 and 1.016 become 1.01)",
        ["UP"] = "Normal Rounding (if 2 decimals, 1.014 rounds to 1.01 and 1.016 rounds to 1.02)",
        ["ALWAYS_UP"] = "Always Round Up (if 2 decimals, both 1.014 and 1.016 round to 1.02)",
    };
    
    private readonly Dictionary<string, string> TEXT_FORMAT_TYPES = new ()
    {
        [""] = "",
        ["LOWERCASE"] = "lower case",
        ["UPPERCASE"] = "UPPER CASE",
        ["TITLECASE"] = "Title Case",
    };

    public string getName()
    {
        return "Format Column";
    }

    public string getCode()
    {
        return nameof(Transformations.FORMAT_COLUMN);
    }
        
    public bool isAdvancedTransformation()
    {
        return false;
    }

    public TransformationCategory getTransformationCategory()
    {
        return TransformationCategory.FormatDataFields;
    }

    public string getInstructions()
    {
        return "Select desired column formatting.";
    }
        
    public bool hasUpdatedSelectedColumns()
    {
        return false;
    }

    public string[] getUpdatedSelectedColumns()
    {
        throw new NotImplementedException();
    }

    public bool ShouldDefer(DataTable _dataTable, List<InputParameter> _inputParameters, ConfigurationField[] _configurationFields, 
        TransformationField[] _transformationFields, string[] _selectedColumns)
    {
        var col = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, COLUMN_TO_FORMAT);
        var key = $"PivotAggregateColumn:{col}";
        if (_dataTable.ExtendedProperties.ContainsKey(key))
        {
            return true;
        }
        var dataType = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, DATA_TYPE);
        if (string.IsNullOrEmpty(dataType))
        {
            return false;
        }

        var numberFormat = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, NUMBER_FORMAT);
        var hasAggValTransform = DataExtractorV3Manager.getBoolInputParameterValue(_inputParameters, "HasAggregateValueTransform");
        var nonDeferNumeric = (string.IsNullOrEmpty(numberFormat) || numberFormat == "N" || numberFormat == "F") && !hasAggValTransform;
        var columnToFormat = DataExtractorV3Manager.getCurrentColumnName(_dataTable.ExtendedProperties, DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, COLUMN_TO_FORMAT));
        var fromRadTimePicker = _dataTable.Columns[columnToFormat].ExtendedProperties.GetValue("FormControl", string.Empty).ToString() == "RadTimePicker";
        _dataTable.Columns[columnToFormat].ExtendedProperties["Numeric"] = !string.IsNullOrEmpty(numberFormat) ? bool.TrueString : bool.FalseString;
        return (dataType.Contains("NUMERIC") && !nonDeferNumeric)|| (dataType.Contains("DATE_TIME") && !fromRadTimePicker);
    }

    public bool isDeprecated()
    {
        return false;
    }

    public TransformationField[] getTransformationFields(int _clientID, Guid _currentUserID, List<InputParameter> _inputParameters)
    {
        return transformationFields ?? (transformationFields = new[]
        {
            new TransformationField(TransformationFieldType.DATASET_COLUMNS, "Column", COLUMN_TO_FORMAT, _isRequired: true),
            new TransformationField(TransformationFieldType.DROPDOWN, "Data Type", DATA_TYPE, _isRequired: true, 
                _valueOptions: DATA_TYPES, _defaultValue: "TEXT"),
            new TransformationField(TransformationFieldType.INFORMATIONAL_LABEL, "", DATA_TYPE_INFO, _isRequired: false),
            new TransformationField(TransformationFieldType.DROPDOWN, "Text Formats", TEXT_FORMAT, _isRequired: false, 
                _valueOptions: TEXT_FORMAT_TYPES),
            new TransformationField(TransformationFieldType.DROPDOWN, "Date and Time Formats", DATE_AND_TIME_FORMAT, _isRequired: false, 
                _valueOptions: DATE_AND_TIME_FORMAT_TYPES),
            new TransformationField(TransformationFieldType.DROPDOWN, "Time Formats", TIME_FORMAT, _isRequired: false,
                _valueOptions: TIME_FORMAT_TYPES),
            new TransformationField(TransformationFieldType.DROPDOWN, "Numeric Formats", NUMBER_FORMAT, _isRequired: false,
                _valueOptions: NUMBER_FORMAT_TYPES),
            new TransformationField(TransformationFieldType.DROPDOWN, "Decimal place Formatting", DECIMAL_FORMAT, _isRequired: false, 
                _valueOptions: DECIMAL_FORMAT_TYPES),
            new TransformationField(TransformationFieldType.DROPDOWN, "Decimal rounding", DECIMAL_ROUNDING, _isRequired: false, 
                _valueOptions: DECIMAL_ROUNDING_TYPES),
            new TransformationField(TransformationFieldType.INTEGER, "Decimal rounding place", DECIMAL_ROUNDING_PLACE, _isRequired: false,
                _description: "This will default to the last place specified in decimal place formatting."),
            new TransformationField(TransformationFieldType.BOOL, "Insert Current Date and Time", INSERT_CURRENT_DATETIME, _isRequired: false, 
                _description: "If enabled, inserts the current date and time into the specified field to be formatted.")
        });

    }

    private const string DATA_TYPE_INFO = "DATA_TYPE_INFO";

    public DataTable transform(DataTable _dataTable, List<InputParameter> _inputParameters, ConfigurationField[] _configurationFields, TransformationField[] _transformationFields, string[] _selectedColumns)
    {
        var columnToFormat = DataExtractorV3Manager.getCurrentColumnName(_transformationFields, _dataTable.ExtendedProperties, COLUMN_TO_FORMAT);
        var dataType = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, DATA_TYPE);
        var insertCurrentDateTime = bool.Parse(DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, "INSERT_CURRENT_DATETIME") ?? bool.FalseString);
        if (insertCurrentDateTime)
        {
            foreach (DataRow row in _dataTable.Rows)
            {
                row[columnToFormat] = DateTime.Now;
            }
        }
        if (_dataTable.ExtendedProperties.ContainsKey("ColumnsRemovedByPivot") &&
            _dataTable.ExtendedProperties["ColumnsRemovedByPivot"].ToString().Split(',').Contains(columnToFormat))
        {
            return _dataTable;
        }
        var textFormatting = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, TEXT_FORMAT);
        var dateTimeFormatting = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, DATE_AND_TIME_FORMAT);
        var timeFormatting = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, TIME_FORMAT);
        var numberFormatting = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, NUMBER_FORMAT);
        var decimalFormatting = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, DECIMAL_FORMAT);
        var decimalRounding = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, DECIMAL_ROUNDING);
        var decimalRoundingPlace = DataExtractorV3Manager.getTransformationFieldValue(_transformationFields, DECIMAL_ROUNDING_PLACE);
        if (numberFormatting == "C")
        {
            _dataTable.ExtendedProperties[$"{columnToFormat}|Currency"] = true;
        }
            
        if (!string.IsNullOrEmpty(dateTimeFormatting))
        {
            return FormatDateTimes(_dataTable, columnToFormat, dateTimeFormatting, dataType);
        }
        if (!string.IsNullOrEmpty(timeFormatting))
        {
            return FormatDateTimes(_dataTable, columnToFormat, timeFormatting, dataType);
        }

        if (!string.IsNullOrEmpty(textFormatting) || dataType == "TEXT")
        {
            _dataTable.Columns[columnToFormat].ExtendedProperties["Text"] = bool.TrueString;
        }

        if (!string.IsNullOrEmpty(textFormatting))
        {
            return FormatText(_dataTable, columnToFormat, textFormatting);
        }
        return !(string.IsNullOrEmpty(numberFormatting) && string.IsNullOrEmpty(decimalFormatting) && string.IsNullOrEmpty(decimalRounding)) 
            ? FormatNumerics(_dataTable, columnToFormat, numberFormatting, decimalFormatting, decimalRounding, decimalRoundingPlace) : _dataTable;
    }

    private DataTable FormatText(DataTable dataTable, string columnToFormat, string textFormatting)
    {
        var dtCloned = dataTable.Clone();
        dtCloned.Columns[columnToFormat].DataType = typeof(string);
        foreach (DataRow row in dataTable.Rows) 
        {
            dtCloned.ImportRow(row);
        }

        foreach (DataRow row in dtCloned.Rows)
        {
            var textVal = row[columnToFormat].ToString();
            if (string.IsNullOrEmpty(textVal)) { continue; }

            row[columnToFormat] = textFormatting switch
            {
                "UPPERCASE" => textVal.ToUpper(),
                "LOWERCASE" => textVal.ToLower(),
                "TITLECASE" => TextHelper.ToTitleCase(textVal),
                _ => row[columnToFormat]
            };
        }
        
        if (!dtCloned.Columns[columnToFormat].ExtendedProperties.ContainsKey("RemoveDefaultFormat"))
        {
            dtCloned.Columns[columnToFormat].ExtendedProperties.Add("RemoveDefaultFormat", columnToFormat);
        }

        return dtCloned;
    }

    private DataTable FormatDateTimes(DataTable _dataTable, string outputColumn, string formatting, string dataType)
    {
        var dtCloned = _dataTable.Clone();
        dtCloned.Columns[outputColumn].DataType = typeof(string);
        var origDateColumn = dtCloned.Columns.Add($"{outputColumn}|OrigDate", typeof(DateTime));
        foreach (DataRow row in _dataTable.Rows) 
        {
            dtCloned.ImportRow(row);
        }

        var fromRadTimePicker = _dataTable.Columns[outputColumn].ExtendedProperties.GetValue("FormControl", string.Empty).ToString() == "RadTimePicker";
        var defaultDateTime = new DateTime();
        foreach (DataRow row in dtCloned.Rows)
        {
            var dateVal = row[outputColumn].ToString();
            if (string.IsNullOrEmpty(dateVal))
            {
                continue;
            }

            var dateParsed = TryParseDate(dateVal, fromRadTimePicker, out var dateTime);
            if(!dateParsed || dateTime.Equals(defaultDateTime))
            {
                continue;
            }

            row[origDateColumn] = dateTime;
            row[outputColumn] = dateTime.ToString(formatting);
        }
        
        if (!dtCloned.Columns[outputColumn].ExtendedProperties.ContainsKey("RemoveDefaultFormat"))
        {
            dtCloned.Columns[outputColumn].ExtendedProperties.Add("RemoveDefaultFormat", outputColumn);
        }

        if (dataType != "TEXT")
        {
            dtCloned.Columns[outputColumn].ExtendedProperties["DateTime"] = bool.TrueString;
            dtCloned.Columns[outputColumn].ExtendedProperties["OrigDateColumn"] = origDateColumn.ColumnName;
        }
        else
        {
            dtCloned.Columns[outputColumn].ExtendedProperties["Text"] = bool.TrueString;
        }

        return dtCloned;
    }

    private static bool TryParseDate(string dateVal, bool fromRadTimePicker, out DateTime dateTime)
    {
        var dateParsed = false;
        if (fromRadTimePicker && dateVal.Contains("("))
        {
            dateVal = dateVal.Substring(0, dateVal.IndexOf('(') - 1);
            dateParsed = DateTimeOffset.TryParseExact(dateVal, "ddd MMM dd yyyy HH:mm:ss 'GMT'K", CultureInfo.InvariantCulture.DateTimeFormat,
                DateTimeStyles.None, out var dateTimeOffset);
            dateTime = dateTimeOffset.DateTime;
        }
        else
        {
            dateParsed = DateTime.TryParse(dateVal, out dateTime);
        }

        return dateParsed;
    }

    private DataTable FormatNumerics(DataTable _dataTable, string outputColumn, string numberFormatting,
        string decimalFormatting, string roundingType, string decimalRoundingPlace)
    {
        var formattedTable = GetFormattedTable(_dataTable, outputColumn, numberFormatting);
                
        foreach (DataRow row in _dataTable.Rows)
        {
            var formattedColumnDataType = formattedTable.Columns[outputColumn].DataType;
            var origColumnDataType = _dataTable.Columns[outputColumn].DataType;
            row[outputColumn] = string.IsNullOrEmpty(row[outputColumn].ToString()) && formattedColumnDataType != typeof(string) &&
                                origColumnDataType == typeof(string) ? "0" : row[outputColumn];
            formattedTable.ImportRow(row);
        }

        foreach (DataRow row in formattedTable.Rows)
        {
                
            var value = string.IsNullOrEmpty(row[outputColumn].ToString()) ? "0" : row[outputColumn].ToString();

            switch (numberFormatting)
            {
                case "P":
                    if (decimal.TryParse(value, out var percentValue))
                    {
                        if (int.TryParse(decimalFormatting, out var decimalPlaces) && decimalFormatting != "0")
                        {
                            var place = GetRoundingPlace(decimalRoundingPlace, decimalPlaces);
                            percentValue = ApplyRounding(percentValue, place, roundingType);
                            row[outputColumn] = string.Format("{0:P" + decimalPlaces + "}", percentValue);
                        }
                        else
                        {
                            row[outputColumn] = $"{percentValue:0%}";
                        }
                    }
                    break;
                case "C":
                    if (decimal.TryParse(value, out var currencyValue))
                    {
                        if (int.TryParse(decimalFormatting, out var decimalPlaces) && decimalFormatting != "0")
                        {
                            var place = GetRoundingPlace(decimalRoundingPlace, decimalPlaces);
                            currencyValue = ApplyRounding(currencyValue, place, roundingType);
                            row[outputColumn] = string.Format("{0:C" + decimalPlaces + "}", currencyValue);
                        }
                        else
                        {
                            row[outputColumn] = $"{currencyValue:C0}";
                        }
                    }
                    break;
                case "F":
                    if (decimal.TryParse(value, out var rawDecimalValue))
                    {
                        if (int.TryParse(decimalFormatting, out var decimalPlaces))
                        {
                            var place = GetRoundingPlace(decimalRoundingPlace, decimalPlaces);
                            rawDecimalValue = ApplyRounding(rawDecimalValue, place, roundingType);
                            row[outputColumn] = string.Format("{0:F" + decimalPlaces + "}", rawDecimalValue);
                        }
                        else
                        {
                            row[outputColumn] = rawDecimalValue;
                        }
                    }
                    break;
                default:
                    if (decimal.TryParse(value, out var decimalValue))
                    {
                        if (int.TryParse(decimalFormatting, out var decimalPlaces))
                        {
                            var place = GetRoundingPlace(decimalRoundingPlace, decimalPlaces);
                            decimalValue = ApplyRounding(decimalValue, place, roundingType);
                            row[outputColumn] = string.Format("{0:N" + decimalPlaces + "}", decimalValue);
                        }
                        else
                        {
                            row[outputColumn] = decimalValue;
                        }
                    }
                    break;
            }
        }
            
        if (!formattedTable.Columns[outputColumn].ExtendedProperties.ContainsKey("RemoveDefaultFormat"))
        {
            formattedTable.Columns[outputColumn].ExtendedProperties.Add("RemoveDefaultFormat", outputColumn);
        }

        return formattedTable;
    }

    private static int GetRoundingPlace(string decimalRoundingPlace, int decimalPlaces)
    {
        var place = int.TryParse(decimalRoundingPlace, out var placeVal) && placeVal <= decimalPlaces ? placeVal : decimalPlaces;
        place = place < 0 ? 0 : place;
        return place;
    }

    private decimal ApplyRounding(decimal value, int decimalPlaces, string roundingType)
    {
        if (decimalPlaces == 0 && roundingType == "UP")
        {
            return Math.Round(value);
        }

        if (roundingType == "UP")
        {
            return Math.Round(value, decimalPlaces);
        }

        var scalingFactor = (decimal)Math.Pow(10, decimalPlaces);
        if (roundingType == "ALWAYS_UP")
        {
            return Math.Ceiling(value * scalingFactor) / scalingFactor;
        }
        if (decimalPlaces == 0)
        {
            return Math.Truncate(value);
        }
        return Math.Floor(value * scalingFactor) / scalingFactor;
    }

    private static DataTable GetFormattedTable(DataTable _table, string outputColumn, string numberFormatting)
    {
        var dtCloned = _table.Clone();
        if (dtCloned.Columns.IndexOf(outputColumn) < 0)
        {
            return _table;
        }
        switch (numberFormatting)
        {
            case "P":
            case "C":
                dtCloned.Columns[outputColumn].DataType = typeof(string);
                break;
            default:
                dtCloned.Columns[outputColumn].DataType = typeof(decimal);
                break;
        }

        return dtCloned;
    }
}