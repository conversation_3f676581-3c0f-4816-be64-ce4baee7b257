<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNet.Identity.EntityFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.RoleStore`1">
            <summary>
                EntityFramework based implementation
            </summary>
            <typeparam name="TRole"></typeparam>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3">
            <summary>
                EntityFramework based implementation
            </summary>
            <typeparam name="TRole"></typeparam>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TUserRole"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.#ctor(System.Data.Entity.DbContext)">
            <summary>
                Constructor which takes a db context and wires up the stores with default instances using the context
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.FindByIdAsync(`1)">
            <summary>
                Find a role by id
            </summary>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.FindByNameAsync(System.String)">
            <summary>
                Find a role by name
            </summary>
            <param name="roleName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.CreateAsync(`0)">
            <summary>
                Insert an entity
            </summary>
            <param name="role"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.DeleteAsync(`0)">
            <summary>
                Mark an entity for deletion
            </summary>
            <param name="role"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.UpdateAsync(`0)">
            <summary>
                Update an entity
            </summary>
            <param name="role"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.Dispose">
            <summary>
                Dispose the store
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.Dispose(System.Boolean)">
            <summary>
                If disposing, calls dispose on the Context.  Always nulls out the Context
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.Context">
            <summary>
                Context for the store
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.DisposeContext">
            <summary>
                If true will call dispose on the DbContext during Dipose
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.RoleStore`3.Roles">
            <summary>
                Returns an IQueryable of users
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`1.#ctor">
            <summary>
                Constructor
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.RoleStore`1.#ctor(System.Data.Entity.DbContext)">
            <summary>
                Constructor
            </summary>
            <param name="context"></param>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.UserStore`1">
            <summary>
                EntityFramework based user store implementation that supports IUserStore, IUserLoginStore, IUserClaimStore and
                IUserRoleStore
            </summary>
            <typeparam name="TUser"></typeparam>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.UserStore`6">
            <summary>
                EntityFramework based user store implementation that supports IUserStore, IUserLoginStore, IUserClaimStore and
                IUserRoleStore
            </summary>
            <typeparam name="TUser"></typeparam>
            <typeparam name="TRole"></typeparam>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TUserLogin"></typeparam>
            <typeparam name="TUserRole"></typeparam>
            <typeparam name="TUserClaim"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.#ctor(System.Data.Entity.DbContext)">
            <summary>
                Constructor which takes a db context and wires up the stores with default instances using the context
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetClaimsAsync(`0)">
            <summary>
                Return the claims for a user
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.AddClaimAsync(`0,System.Security.Claims.Claim)">
            <summary>
                Add a claim to a user
            </summary>
            <param name="user"></param>
            <param name="claim"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.RemoveClaimAsync(`0,System.Security.Claims.Claim)">
            <summary>
                Remove a claim from a user
            </summary>
            <param name="user"></param>
            <param name="claim"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetEmailConfirmedAsync(`0)">
            <summary>
                Returns whether the user email is confirmed
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetEmailConfirmedAsync(`0,System.Boolean)">
            <summary>
                Set IsConfirmed on the user
            </summary>
            <param name="user"></param>
            <param name="confirmed"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetEmailAsync(`0,System.String)">
            <summary>
                Set the user email
            </summary>
            <param name="user"></param>
            <param name="email"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetEmailAsync(`0)">
            <summary>
                Get the user's email
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.FindByEmailAsync(System.String)">
            <summary>
                Find a user by email
            </summary>
            <param name="email"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetLockoutEndDateAsync(`0)">
            <summary>
                Returns the DateTimeOffset that represents the end of a user's lockout, any time in the past should be considered
                not locked out.
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetLockoutEndDateAsync(`0,System.DateTimeOffset)">
            <summary>
                Locks a user out until the specified end date (set to a past date, to unlock a user)
            </summary>
            <param name="user"></param>
            <param name="lockoutEnd"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.IncrementAccessFailedCountAsync(`0)">
            <summary>
                Used to record when an attempt to access the user has failed
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.ResetAccessFailedCountAsync(`0)">
            <summary>
                Used to reset the account access count, typically after the account is successfully accessed
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetAccessFailedCountAsync(`0)">
            <summary>
                Returns the current number of failed access attempts.  This number usually will be reset whenever the password is
                verified or the account is locked out.
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetLockoutEnabledAsync(`0)">
            <summary>
                Returns whether the user can be locked out.
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetLockoutEnabledAsync(`0,System.Boolean)">
            <summary>
                Sets whether the user can be locked out.
            </summary>
            <param name="user"></param>
            <param name="enabled"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.FindByIdAsync(`2)">
            <summary>
                Find a user by id
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.FindByNameAsync(System.String)">
            <summary>
                Find a user by name
            </summary>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.CreateAsync(`0)">
            <summary>
                Insert an entity
            </summary>
            <param name="user"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.DeleteAsync(`0)">
            <summary>
                Mark an entity for deletion
            </summary>
            <param name="user"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.UpdateAsync(`0)">
            <summary>
                Update an entity
            </summary>
            <param name="user"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.Dispose">
            <summary>
                Dispose the store
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.FindAsync(Microsoft.AspNet.Identity.UserLoginInfo)">
            <summary>
                Returns the user associated with this login
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.AddLoginAsync(`0,Microsoft.AspNet.Identity.UserLoginInfo)">
            <summary>
                Add a login to the user
            </summary>
            <param name="user"></param>
            <param name="login"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.RemoveLoginAsync(`0,Microsoft.AspNet.Identity.UserLoginInfo)">
            <summary>
                Remove a login from a user
            </summary>
            <param name="user"></param>
            <param name="login"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetLoginsAsync(`0)">
            <summary>
                Get the logins for a user
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetPasswordHashAsync(`0,System.String)">
            <summary>
                Set the password hash for a user
            </summary>
            <param name="user"></param>
            <param name="passwordHash"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetPasswordHashAsync(`0)">
            <summary>
                Get the password hash for a user
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.HasPasswordAsync(`0)">
            <summary>
                Returns true if the user has a password set
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetPhoneNumberAsync(`0,System.String)">
            <summary>
                Set the user's phone number
            </summary>
            <param name="user"></param>
            <param name="phoneNumber"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetPhoneNumberAsync(`0)">
            <summary>
                Get a user's phone number
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetPhoneNumberConfirmedAsync(`0)">
            <summary>
                Returns whether the user phoneNumber is confirmed
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetPhoneNumberConfirmedAsync(`0,System.Boolean)">
            <summary>
                Set PhoneNumberConfirmed on the user
            </summary>
            <param name="user"></param>
            <param name="confirmed"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.AddToRoleAsync(`0,System.String)">
            <summary>
                Add a user to a role
            </summary>
            <param name="user"></param>
            <param name="roleName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.RemoveFromRoleAsync(`0,System.String)">
            <summary>
                Remove a user from a role
            </summary>
            <param name="user"></param>
            <param name="roleName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetRolesAsync(`0)">
            <summary>
                Get the names of the roles a user is a member of
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.IsInRoleAsync(`0,System.String)">
            <summary>
                Returns true if the user is in the named role
            </summary>
            <param name="user"></param>
            <param name="roleName"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetSecurityStampAsync(`0,System.String)">
            <summary>
                Set the security stamp for the user
            </summary>
            <param name="user"></param>
            <param name="stamp"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetSecurityStampAsync(`0)">
            <summary>
                Get the security stamp for a user
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.SetTwoFactorEnabledAsync(`0,System.Boolean)">
            <summary>
                Set whether two factor authentication is enabled for the user
            </summary>
            <param name="user"></param>
            <param name="enabled"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetTwoFactorEnabledAsync(`0)">
            <summary>
                Gets whether two factor authentication is enabled for the user
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.GetUserAggregateAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            Used to attach child entities to the User aggregate, i.e. Roles, Logins, and Claims
            </summary>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.Dispose(System.Boolean)">
            <summary>
                If disposing, calls dispose on the Context.  Always nulls out the Context
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.Context">
            <summary>
                Context for the store
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.DisposeContext">
            <summary>
                If true will call dispose on the DbContext during Dispose
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.AutoSaveChanges">
            <summary>
                If true will call SaveChanges after Create/Update/Delete
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.UserStore`6.Users">
            <summary>
                Returns an IQueryable of users
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`1.#ctor">
            <summary>
                Default constuctor which uses a new instance of a default EntityyDbContext
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.UserStore`1.#ctor(System.Data.Entity.DbContext)">
            <summary>
                Constructor
            </summary>
            <param name="context"></param>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUserRole">
            <summary>
                EntityType that represents a user belonging to a role
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUserRole`1">
            <summary>
                EntityType that represents a user belonging to a role
            </summary>
            <typeparam name="TKey"></typeparam>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserRole`1.UserId">
            <summary>
                UserId for the user that is in the role
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserRole`1.RoleId">
            <summary>
                RoleId for the role
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1">
            <summary>
                EntityFramework based IIdentityEntityStore that allows query/manipulation of a TEntity set
            </summary>
            <typeparam name="TEntity">Concrete entity type, i.e .User</typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.#ctor(System.Data.Entity.DbContext)">
            <summary>
                Constructor that takes a Context
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.GetByIdAsync(System.Object)">
            <summary>
                FindAsync an entity by ID
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.Create(`0)">
            <summary>
                Insert an entity
            </summary>
            <param name="entity"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.Delete(`0)">
            <summary>
                Mark an entity for deletion
            </summary>
            <param name="entity"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.Update(`0)">
            <summary>
                Update an entity
            </summary>
            <param name="entity"></param>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.Context">
            <summary>
                Context for the store
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.EntitySet">
            <summary>
                Used to query the entities
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.EntityStore`1.DbEntitySet">
            <summary>
                EntitySet for this store
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext">
            <summary>
            Default IdentityDbContext that uses the default entity types for ASP.NET Identity Users, Roles, Claims, Logins. 
            Use this overload to add your own entity types.
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6">
            <summary>
            Generic IdentityDbContext base that can be customized with entity types that extend from the base IdentityUserXXX types.
            </summary>
            <typeparam name="TUser"></typeparam>
            <typeparam name="TRole"></typeparam>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TUserLogin"></typeparam>
            <typeparam name="TUserRole"></typeparam>
            <typeparam name="TUserClaim"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.#ctor">
            <summary>
                Default constructor which uses the "DefaultConnection" connectionString
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.#ctor(System.String)">
            <summary>
                Constructor which takes the connection string to use
            </summary>
            <param name="nameOrConnectionString"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.#ctor(System.Data.Common.DbConnection,System.Data.Entity.Infrastructure.DbCompiledModel,System.Boolean)">
            <summary>
                Constructs a new context instance using the existing connection to connect to a database, and initializes it from
                the given model.  The connection will not be disposed when the context is disposed if contextOwnsConnection is
                false.
            </summary>
            <param name="existingConnection">An existing connection to use for the new context.</param>
            <param name="model">The model that will back this context.</param>
            <param name="contextOwnsConnection">
                Constructs a new context instance using the existing connection to connect to a
                database, and initializes it from the given model.  The connection will not be disposed when the context is
                disposed if contextOwnsConnection is false.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.#ctor(System.Data.Entity.Infrastructure.DbCompiledModel)">
            <summary>
                Constructs a new context instance using conventions to create the name of
                the database to which a connection will be made, and initializes it from
                the given model.  The by-convention name is the full name (namespace + class
                name) of the derived context class.  See the class remarks for how this is
                used to create a connection.
            </summary>
            <param name="model">The model that will back this context.</param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.#ctor(System.Data.Common.DbConnection,System.Boolean)">
            <summary>
                Constructs a new context instance using the existing connection to connect
                to a database.  The connection will not be disposed when the context is disposed
                if contextOwnsConnection is false.
            </summary>
            <param name="existingConnection">An existing connection to use for the new context.</param>
            <param name="contextOwnsConnection">If set to true the connection is disposed when the context is disposed, otherwise
                the caller must dispose the connection.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.#ctor(System.String,System.Data.Entity.Infrastructure.DbCompiledModel)">
            <summary>
                Constructs a new context instance using the given string as the name or connection
                string for the database to which a connection will be made, and initializes
                it from the given model.  See the class remarks for how this is used to create
                a connection.
            </summary>
            <param name="nameOrConnectionString">Either the database name or a connection string.</param>
            <param name="model">The model that will back this context.</param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.OnModelCreating(System.Data.Entity.DbModelBuilder)">
            <summary>
                Maps table names, and sets up relationships between the various user entities
            </summary>
            <param name="modelBuilder"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.ValidateEntity(System.Data.Entity.Infrastructure.DbEntityEntry,System.Collections.Generic.IDictionary{System.Object,System.Object})">
            <summary>
                Validates that UserNames are unique and case insenstive
            </summary>
            <param name="entityEntry"></param>
            <param name="items"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.Users">
            <summary>
                IDbSet of Users
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.Roles">
            <summary>
                IDbSet of Roles
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`6.RequireUniqueEmail">
            <summary>
                If true validates that emails are unique
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext.#ctor">
            <summary>
                Default constructor which uses the DefaultConnection
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext.#ctor(System.String)">
            <summary>
                Constructor which takes the connection string to use
            </summary>
            <param name="nameOrConnectionString"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext.#ctor(System.Data.Common.DbConnection,System.Data.Entity.Infrastructure.DbCompiledModel,System.Boolean)">
            <summary>
                Constructs a new context instance using the existing connection to connect to a database, and initializes it from
                the given model.  The connection will not be disposed when the context is disposed if contextOwnsConnection is
                false.
            </summary>
            <param name="existingConnection">An existing connection to use for the new context.</param>
            <param name="model">The model that will back this context.</param>
            <param name="contextOwnsConnection">
                Constructs a new context instance using the existing connection to connect to a
                database, and initializes it from the given model.  The connection will not be disposed when the context is
                disposed if contextOwnsConnection is false.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext.#ctor(System.Data.Entity.Infrastructure.DbCompiledModel)">
            <summary>
                Constructs a new context instance using conventions to create the name of
                the database to which a connection will be made, and initializes it from
                the given model.  The by-convention name is the full name (namespace + class
                name) of the derived context class.  See the class remarks for how this is
                used to create a connection.
            </summary>
            <param name="model">The model that will back this context.</param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext.#ctor(System.Data.Common.DbConnection,System.Boolean)">
            <summary>
                Constructs a new context instance using the existing connection to connect
                to a database.  The connection will not be disposed when the context is disposed
                if contextOwnsConnection is false.
            </summary>
            <param name="existingConnection">An existing connection to use for the new context.</param>
            <param name="contextOwnsConnection">If set to true the connection is disposed when the context is disposed, otherwise
                the caller must dispose the connection.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext.#ctor(System.String,System.Data.Entity.Infrastructure.DbCompiledModel)">
            <summary>
                Constructs a new context instance using the given string as the name or connection
                string for the database to which a connection will be made, and initializes
                it from the given model.  See the class remarks for how this is used to create
                a connection.
            </summary>
            <param name="nameOrConnectionString">Either the database name or a connection string.</param>
            <param name="model">The model that will back this context.</param>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1">
            <summary>
                DbContext which uses a custom user entity with a string primary key
            </summary>
            <typeparam name="TUser"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1.#ctor">
            <summary>
                Default constructor which uses the DefaultConnection
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1.#ctor(System.String)">
            <summary>
                Constructor which takes the connection string to use
            </summary>
            <param name="nameOrConnectionString"></param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1.#ctor(System.String,System.Boolean)">
            <summary>
                Constructor which takes the connection string to use
            </summary>
            <param name="nameOrConnectionString"></param>
            <param name="throwIfV1Schema">Will throw an exception if the schema matches that of Identity 1.0.0</param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1.#ctor(System.Data.Common.DbConnection,System.Data.Entity.Infrastructure.DbCompiledModel,System.Boolean)">
            <summary>
                Constructs a new context instance using the existing connection to connect to a database, and initializes it from
                the given model.  The connection will not be disposed when the context is disposed if contextOwnsConnection is
                false.
            </summary>
            <param name="existingConnection">An existing connection to use for the new context.</param>
            <param name="model">The model that will back this context.</param>
            <param name="contextOwnsConnection">
                Constructs a new context instance using the existing connection to connect to a
                database, and initializes it from the given model.  The connection will not be disposed when the context is
                disposed if contextOwnsConnection is false.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1.#ctor(System.Data.Entity.Infrastructure.DbCompiledModel)">
            <summary>
                Constructs a new context instance using conventions to create the name of
                the database to which a connection will be made, and initializes it from
                the given model.  The by-convention name is the full name (namespace + class
                name) of the derived context class.  See the class remarks for how this is
                used to create a connection.
            </summary>
            <param name="model">The model that will back this context.</param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1.#ctor(System.Data.Common.DbConnection,System.Boolean)">
            <summary>
                Constructs a new context instance using the existing connection to connect
                to a database.  The connection will not be disposed when the context is disposed
                if contextOwnsConnection is false.
            </summary>
            <param name="existingConnection">An existing connection to use for the new context.</param>
            <param name="contextOwnsConnection">If set to true the connection is disposed when the context is disposed, otherwise
                the caller must dispose the connection.
            </param>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityDbContext`1.#ctor(System.String,System.Data.Entity.Infrastructure.DbCompiledModel)">
            <summary>
                Constructs a new context instance using the given string as the name or connection
                string for the database to which a connection will be made, and initializes
                it from the given model.  See the class remarks for how this is used to create
                a connection.
            </summary>
            <param name="nameOrConnectionString">Either the database name or a connection string.</param>
            <param name="model">The model that will back this context.</param>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.DbValidationFailed">
            <summary>
              Looks up a localized string similar to Database Validation failed..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.DuplicateEmail">
            <summary>
              Looks up a localized string similar to Email {0} is already taken..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.DuplicateUserName">
            <summary>
              Looks up a localized string similar to User name {0} is already taken..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.EntityFailedValidation">
            <summary>
              Looks up a localized string similar to Entity Type {0} failed validation..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.ExternalLoginExists">
            <summary>
              Looks up a localized string similar to A user with that external login already exists..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.IdentityV1SchemaError">
            <summary>
              Looks up a localized string similar to The model backing the &apos;ApplicationDbContext&apos; context has changed since the database was created. This could have happened because the model used by ASP.NET Identity Framework has changed or the model being used in your application has changed. To resolve this issue, you need to update your database. Consider using Code First Migrations to update the database (http://go.microsoft.com/fwlink/?LinkId=301867).  Before you update your database using Code First Migrations, please disable the schema consistency ch [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.IncorrectType">
            <summary>
              Looks up a localized string similar to Incorrect type, expected type of {0}..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.PropertyCannotBeEmpty">
            <summary>
              Looks up a localized string similar to {0} cannot be null or empty..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.RoleAlreadyExists">
            <summary>
              Looks up a localized string similar to Role {0} already exists..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.RoleIsNotEmpty">
            <summary>
              Looks up a localized string similar to Role is not empty..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.RoleNotFound">
            <summary>
              Looks up a localized string similar to Role {0} does not exist..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.UserAlreadyInRole">
            <summary>
              Looks up a localized string similar to User already in role..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.UserIdNotFound">
            <summary>
              Looks up a localized string similar to The UserId cannot be found..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.UserLoginAlreadyExists">
            <summary>
              Looks up a localized string similar to UserLogin already exists for loginProvider: {0} with providerKey: {1}.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.UserNameNotFound">
            <summary>
              Looks up a localized string similar to User {0} does not exist..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.UserNotInRole">
            <summary>
              Looks up a localized string similar to User is not in role..
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityResources.ValueCannotBeNullOrEmpty">
            <summary>
              Looks up a localized string similar to Value cannot be null or empty..
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityRole">
            <summary>
                Represents a Role entity
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityRole`2">
            <summary>
                Represents a Role entity
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TUserRole"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityRole`2.#ctor">
            <summary>
                Constructor
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityRole`2.Users">
            <summary>
                Navigation property for users in the role
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityRole`2.Id">
            <summary>
                Role id
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityRole`2.Name">
            <summary>
                Role name
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityRole.#ctor">
            <summary>
                Constructor
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityRole.#ctor(System.String)">
            <summary>
                Constructor
            </summary>
            <param name="roleName"></param>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUser">
            <summary>
                Default EntityFramework IUser implementation
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4">
            <summary>
                Default EntityFramework IUser implementation
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TLogin"></typeparam>
            <typeparam name="TRole"></typeparam>
            <typeparam name="TClaim"></typeparam>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.#ctor">
            <summary>
                Constructor
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.Email">
            <summary>
                Email
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.EmailConfirmed">
            <summary>
                True if the email is confirmed, default is false
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.PasswordHash">
            <summary>
                The salted/hashed form of the user password
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.SecurityStamp">
            <summary>
                A random value that should change whenever a users credentials have changed (password changed, login removed)
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.PhoneNumber">
            <summary>
                PhoneNumber for the user
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.PhoneNumberConfirmed">
            <summary>
                True if the phone number is confirmed, default is false
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.TwoFactorEnabled">
            <summary>
                Is two factor enabled for the user
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.LockoutEndDateUtc">
            <summary>
                DateTime in UTC when lockout ends, any time in the past is considered not locked out.
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.LockoutEnabled">
            <summary>
                Is lockout enabled for this user
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.AccessFailedCount">
            <summary>
                Used to record failures for the purposes of lockout
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.Roles">
            <summary>
                Navigation property for user roles
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.Claims">
            <summary>
                Navigation property for user claims
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.Logins">
            <summary>
                Navigation property for user logins
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.Id">
            <summary>
                User ID (Primary Key)
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUser`4.UserName">
            <summary>
                User name
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityUser.#ctor">
            <summary>
                Constructor which creates a new Guid for the Id
            </summary>
        </member>
        <member name="M:Microsoft.AspNet.Identity.EntityFramework.IdentityUser.#ctor(System.String)">
            <summary>
                Constructor that takes a userName
            </summary>
            <param name="userName"></param>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUserClaim">
            <summary>
                EntityType that represents one specific user claim
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUserClaim`1">
            <summary>
                EntityType that represents one specific user claim
            </summary>
            <typeparam name="TKey"></typeparam>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserClaim`1.Id">
            <summary>
                Primary key
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserClaim`1.UserId">
            <summary>
                User Id for the user who owns this login
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserClaim`1.ClaimType">
            <summary>
                Claim type
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserClaim`1.ClaimValue">
            <summary>
                Claim value
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUserLogin">
            <summary>
                Entity type for a user's login (i.e. facebook, google)
            </summary>
        </member>
        <member name="T:Microsoft.AspNet.Identity.EntityFramework.IdentityUserLogin`1">
            <summary>
                Entity type for a user's login (i.e. facebook, google)
            </summary>
            <typeparam name="TKey"></typeparam>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserLogin`1.LoginProvider">
            <summary>
                The login provider for the login (i.e. facebook, google)
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserLogin`1.ProviderKey">
            <summary>
                Key representing the login for the provider
            </summary>
        </member>
        <member name="P:Microsoft.AspNet.Identity.EntityFramework.IdentityUserLogin`1.UserId">
            <summary>
                User Id for the user who owns this login
            </summary>
        </member>
    </members>
</doc>
