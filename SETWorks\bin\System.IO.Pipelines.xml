<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.IO.Pipelines</name>
    </assembly>
    <members>
        <member name="T:System.Threading.Tasks.TaskToApm">
            <summary>
            Provides support for efficiently using Tasks to implement the APM (Begin/End) pattern.
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.Begin(System.Threading.Tasks.Task,System.AsyncCallback,System.Object)">
            <summary>
            Marshals the Task as an IAsyncResult, using the supplied callback and state
            to implement the APM pattern.
            </summary>
            <param name="task">The Task to be marshaled.</param>
            <param name="callback">The callback to be invoked upon completion.</param>
            <param name="state">The state to be stored in the IAsyncResult.</param>
            <returns>An IAsyncResult to represent the task's asynchronous operation.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End``1(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.GetTask(System.IAsyncResult)">
            <summary>Gets the task represented by the IAsyncResult.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.ThrowArgumentException(System.IAsyncResult)">
            <summary>Throws an argument exception for the invalid <paramref name="asyncResult"/>.</summary>
        </member>
        <member name="T:System.Threading.Tasks.TaskToApm.TaskAsyncResult">
            <summary>Provides a simple IAsyncResult that wraps a Task.</summary>
            <remarks>
            We could use the Task as the IAsyncResult if the Task's AsyncState is the same as the object state,
            but that's very rare, in particular in a situation where someone cares about allocation, and always
            using TaskAsyncResult simplifies things and enables additional optimizations.
            </remarks>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._task">
            <summary>The wrapped Task.</summary>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._callback">
            <summary>Callback to invoke when the wrapped task completes.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.TaskAsyncResult.#ctor(System.Threading.Tasks.Task,System.Object,System.AsyncCallback)">
            <summary>Initializes the IAsyncResult with the Task to wrap and the associated object state.</summary>
            <param name="task">The Task to wrap.</param>
            <param name="state">The new AsyncState value.</param>
            <param name="callback">Callback to invoke when the wrapped task completes.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.TaskAsyncResult.InvokeCallback">
            <summary>Invokes the callback.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.AsyncState">
            <summary>Gets a user-defined object that qualifies or contains information about an asynchronous operation.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.CompletedSynchronously">
            <summary>Gets a value that indicates whether the asynchronous operation completed synchronously.</summary>
            <remarks>This is set lazily based on whether the <see cref="F:System.Threading.Tasks.TaskToApm.TaskAsyncResult._task"/> has completed by the time this object is created.</remarks>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.IsCompleted">
            <summary>Gets a value that indicates whether the asynchronous operation has completed.</summary>
        </member>
        <member name="P:System.Threading.Tasks.TaskToApm.TaskAsyncResult.AsyncWaitHandle">
            <summary>Gets a <see cref="T:System.Threading.WaitHandle"/> that is used to wait for an asynchronous operation to complete.</summary>
        </member>
        <member name="P:System.IO.Pipelines.BufferSegment.End">
            <summary>
            The End represents the offset into AvailableMemory where the range of "active" bytes ends. At the point when the block is leased
            the End is guaranteed to be equal to Start. The value of Start may be assigned anywhere between 0 and
            Buffer.Length, and must be equal to or less than End.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.BufferSegment.NextSegment">
            <summary>
            Reference to the next block of data when the overall "active" bytes spans multiple blocks. At the point when the block is
            leased Next is guaranteed to be null. Start, End, and Next are used together in order to create a linked-list of discontiguous
            working memory. The "active" memory is grown when bytes are copied in, End is increased, and Next is assigned. The "active"
            memory is shrunk when bytes are consumed, Start is increased, and blocks are returned to the pool.
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.FlushResult">
            <summary>Result returned by <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> call.</summary>
        </member>
        <member name="M:System.IO.Pipelines.FlushResult.#ctor(System.Boolean,System.Boolean)">
            <summary>Initializes a new instance of <see cref="T:System.IO.Pipelines.FlushResult" /> struct setting the <see cref="P:System.IO.Pipelines.FlushResult.IsCanceled" /> and <see cref="P:System.IO.Pipelines.FlushResult.IsCompleted" /> flags.</summary>
            <param name="isCanceled"><see langword="true" /> to indicate the current <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> operation that produced this <see cref="T:System.IO.Pipelines.FlushResult" /> was canceled by <see cref="M:System.IO.Pipelines.PipeWriter.CancelPendingFlush" />; otherwise, <see langword="false" />.</param>
            <param name="isCompleted"><see langword="true" /> to indicate the reader is no longer reading data written to the <see cref="T:System.IO.Pipelines.PipeWriter" />.</param>
        </member>
        <member name="P:System.IO.Pipelines.FlushResult.IsCanceled">
            <summary>Gets a value that indicates whether the current <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> operation was canceled by <see cref="M:System.IO.Pipelines.PipeWriter.CancelPendingFlush" />.</summary>
            <value><see langword="true" /> if the current <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> operation was canceled by <see cref="M:System.IO.Pipelines.PipeWriter.CancelPendingFlush" />; otherwise, <see langword="false" />.</value>
        </member>
        <member name="P:System.IO.Pipelines.FlushResult.IsCompleted">
            <summary>Gets a value that indicates the reader is no longer reading data written to the <see cref="T:System.IO.Pipelines.PipeWriter" />.</summary>
            <value><see langword="true" /> if the reader is no longer reading data written to the <see cref="T:System.IO.Pipelines.PipeWriter" />; otherwise, <see langword="false" />.</value>
        </member>
        <member name="T:System.IO.Pipelines.IDuplexPipe">
            <summary>Defines a class that provides a duplex pipe from which data can be read from and written to.</summary>
        </member>
        <member name="P:System.IO.Pipelines.IDuplexPipe.Input">
            <summary>Gets the <see cref="T:System.IO.Pipelines.PipeReader" /> half of the duplex pipe.</summary>
        </member>
        <member name="P:System.IO.Pipelines.IDuplexPipe.Output">
            <summary>Gets the <see cref="T:System.IO.Pipelines.PipeWriter" /> half of the duplex pipe.</summary>
        </member>
        <member name="T:System.IO.Pipelines.BufferSegmentStack.SegmentAsValueType">
            <summary>
            A simple struct we wrap reference types inside when storing in arrays to
            bypass the CLR's covariant checks when writing to arrays.
            </summary>
            <remarks>
            We use <see cref="T:System.IO.Pipelines.BufferSegmentStack.SegmentAsValueType"/> as a wrapper to avoid paying the cost of covariant checks whenever
            the underlying array that the <see cref="T:System.IO.Pipelines.BufferSegmentStack"/> class uses is written to.
            We've recognized this as a perf win in ETL traces for these stack frames:
            clr!JIT_Stelem_Ref
              clr!ArrayStoreCheck
                clr!ObjIsInstanceOf
            </remarks>
        </member>
        <member name="T:System.IO.Pipelines.Pipe">
            <summary>The default <see cref="T:System.IO.Pipelines.PipeWriter" /> and <see cref="T:System.IO.Pipelines.PipeReader" /> implementation.</summary>
            <summary>The default <see cref="T:System.IO.Pipelines.PipeWriter" /> and <see cref="T:System.IO.Pipelines.PipeReader" /> implementation.</summary>
            <summary>The default <see cref="T:System.IO.Pipelines.PipeWriter" /> and <see cref="T:System.IO.Pipelines.PipeReader" /> implementation.</summary>
        </member>
        <member name="M:System.IO.Pipelines.Pipe.#ctor">
            <summary>Initializes a new instance of the <see cref="T:System.IO.Pipelines.Pipe" /> class using <see cref="P:System.IO.Pipelines.PipeOptions.Default" /> as options.</summary>
        </member>
        <member name="M:System.IO.Pipelines.Pipe.#ctor(System.IO.Pipelines.PipeOptions)">
            <summary>Initializes a new instance of the <see cref="T:System.IO.Pipelines.Pipe" /> class with the specified options.</summary>
            <param name="options">The set of options for this pipe.</param>
        </member>
        <member name="P:System.IO.Pipelines.Pipe.Reader">
            <summary>Gets the <see cref="T:System.IO.Pipelines.PipeReader" /> for this pipe.</summary>
            <value>A <see cref="T:System.IO.Pipelines.PipeReader" /> instance for this pipe.</value>
        </member>
        <member name="P:System.IO.Pipelines.Pipe.Writer">
            <summary>Gets the <see cref="T:System.IO.Pipelines.PipeWriter" /> for this pipe.</summary>
            <value>A <see cref="T:System.IO.Pipelines.PipeWriter" /> instance for this pipe.</value>
        </member>
        <member name="M:System.IO.Pipelines.Pipe.Reset">
            <summary>Resets the pipe.</summary>
        </member>
        <member name="T:System.IO.Pipelines.PipeOptions">
            <summary>Represents a set of <see cref="T:System.IO.Pipelines.Pipe" /> options.</summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.Default">
            <summary>Gets the default instance of <see cref="T:System.IO.Pipelines.PipeOptions" />.</summary>
            <value>A <see cref="T:System.IO.Pipelines.PipeOptions" /> object initialized with default parameters.</value>
        </member>
        <member name="M:System.IO.Pipelines.PipeOptions.#ctor(System.Buffers.MemoryPool{System.Byte},System.IO.Pipelines.PipeScheduler,System.IO.Pipelines.PipeScheduler,System.Int64,System.Int64,System.Int32,System.Boolean)">
            <summary>Initializes a new instance of the <see cref="T:System.IO.Pipelines.PipeOptions" /> class with the specified parameters.</summary>
            <param name="pool">The pool of memory blocks to be used for buffer management.</param>
            <param name="readerScheduler">The <see cref="T:System.IO.Pipelines.PipeScheduler" /> to be used to execute <see cref="T:System.IO.Pipelines.PipeReader" /> callbacks and async continuations.</param>
            <param name="writerScheduler">The <see cref="T:System.IO.Pipelines.PipeScheduler" /> used to execute <see cref="T:System.IO.Pipelines.PipeWriter" /> callbacks and async continuations.</param>
            <param name="pauseWriterThreshold">The number of bytes in the <see cref="T:System.IO.Pipelines.Pipe" /> before <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> starts blocking. A value of zero prevents <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> from ever blocking, effectively making the number of bytes in the <see cref="T:System.IO.Pipelines.Pipe" /> unlimited.</param>
            <param name="resumeWriterThreshold">The number of bytes in the <see cref="T:System.IO.Pipelines.Pipe" /> when <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> stops blocking.</param>
            <param name="minimumSegmentSize">The minimum size of the segment requested from <paramref name="pool" />.</param>
            <param name="useSynchronizationContext"><see langword="true" /> if asynchronous continuations should be executed on the <see cref="T:System.Threading.SynchronizationContext" /> they were captured on; <see langword="false" /> otherwise. This takes precedence over the schedulers specified in <see cref="P:System.IO.Pipelines.PipeOptions.ReaderScheduler" /> and <see cref="P:System.IO.Pipelines.PipeOptions.WriterScheduler" />.</param>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.UseSynchronizationContext">
            <summary>Gets a value that determines if asynchronous callbacks and continuations should be executed on the <see cref="T:System.Threading.SynchronizationContext" /> they were captured on. This takes precedence over the schedulers specified in <see cref="P:System.IO.Pipelines.PipeOptions.ReaderScheduler" /> and <see cref="P:System.IO.Pipelines.PipeOptions.WriterScheduler" />.</summary>
            <value><see langword="true" /> if asynchronous callbacks and continuations should be executed on the <see cref="T:System.Threading.SynchronizationContext" /> they were captured on; otherwise, <see langword="false" />.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.PauseWriterThreshold">
            <summary>Gets the number of bytes in the <see cref="T:System.IO.Pipelines.Pipe" /> when <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> starts blocking.</summary>
            <value>The number of bytes in the <see cref="T:System.IO.Pipelines.Pipe" /> when <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> starts blocking.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.ResumeWriterThreshold">
            <summary>Gets the number of bytes in the <see cref="T:System.IO.Pipelines.Pipe" /> when <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> stops blocking.</summary>
            <value>The number of bytes in the <see cref="T:System.IO.Pipelines.Pipe" /> when <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> stops blocking.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.MinimumSegmentSize">
            <summary>Gets the minimum size of the segment requested from the <see cref="P:System.IO.Pipelines.PipeOptions.Pool" />.</summary>
            <value>The minimum size of the segment requested from the <see cref="P:System.IO.Pipelines.PipeOptions.Pool" />.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.WriterScheduler">
            <summary>Gets the <see cref="T:System.IO.Pipelines.PipeScheduler" /> used to execute <see cref="T:System.IO.Pipelines.PipeWriter" /> callbacks and async continuations.</summary>
            <value>A <see cref="T:System.IO.Pipelines.PipeScheduler" /> object used to execute <see cref="T:System.IO.Pipelines.PipeWriter" /> callbacks and async continuations.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.ReaderScheduler">
            <summary>Gets the <see cref="T:System.IO.Pipelines.PipeScheduler" /> used to execute <see cref="T:System.IO.Pipelines.PipeReader" /> callbacks and async continuations.</summary>
            <value>A <see cref="T:System.IO.Pipelines.PipeScheduler" /> that is used to execute <see cref="T:System.IO.Pipelines.PipeReader" /> callbacks and async continuations.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.Pool">
            <summary>Gets the <see cref="T:System.Buffers.MemoryPool`1" /> object used for buffer management.</summary>
            <value>A pool of memory blocks used for buffer management.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.IsDefaultSharedMemoryPool">
            <summary>
            Returns true if Pool is <see cref="T:System.Buffers.MemoryPool`1"/>.Shared
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.InitialSegmentPoolSize">
            <summary>
            The initialize size of the segment pool
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.MaxSegmentPoolSize">
            <summary>
            The maximum number of segments to pool
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.PipeReader">
            <summary>Defines a class that provides access to a read side of pipe.</summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.TryRead(System.IO.Pipelines.ReadResult@)">
            <summary>Attempts to synchronously read data from the <see cref="T:System.IO.Pipelines.PipeReader" />.</summary>
            <param name="result">When this method returns <see langword="true" />, this value is set to a <see cref="T:System.IO.Pipelines.ReadResult" /> instance that represents the result of the read call; otherwise, this value is set to <see langword="default" />.</param>
            <returns><see langword="true" /> if data was available, or if the call was canceled or the writer was completed; otherwise, <see langword="false" />.</returns>
            <remarks>If the pipe returns <see langword="false" />, there is no need to call <see cref="M:System.IO.Pipelines.PipeReader.AdvanceTo(System.SequencePosition,System.SequencePosition)" />.</remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)">
            <summary>Asynchronously reads a sequence of bytes from the current <see cref="T:System.IO.Pipelines.PipeReader" />.</summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see langword="default" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1" /> representing the asynchronous read operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.ReadAtLeastAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously reads a sequence of bytes from the current <see cref="T:System.IO.Pipelines.PipeReader" />.</summary>
            <param name="minimumSize">The minimum length that needs to be buffered in order to for the call to return.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see langword="default" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1" /> representing the asynchronous read operation.</returns>
            <remarks>The call returns if the <see cref="T:System.IO.Pipelines.PipeReader" /> has read the minimumLength specified, or is cancelled or completed.</remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.ReadAtLeastAsyncCore(System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously reads a sequence of bytes from the current <see cref="T:System.IO.Pipelines.PipeReader" />.</summary>
            <param name="minimumSize">The minimum length that needs to be buffered in order to for the call to return.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see langword="default" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1" /> representing the asynchronous read operation.</returns>
            <remarks>The call returns if the <see cref="T:System.IO.Pipelines.PipeReader" /> has read the minimumLength specified, or is cancelled or completed.</remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.AdvanceTo(System.SequencePosition)">
            <summary>Moves forward the pipeline's read cursor to after the consumed data, marking the data as processed.</summary>
            <param name="consumed">Marks the extent of the data that has been successfully processed.</param>
            <remarks>The memory for the consumed data will be released and no longer available.
            The <see cref="P:System.IO.Pipelines.ReadResult.Buffer" /> previously returned from <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> must not be accessed after this call.
            This is equivalent to calling <see cref="M:System.IO.Pipelines.PipeReader.AdvanceTo(System.SequencePosition,System.SequencePosition)" /> with identical examined and consumed positions.
            The examined data communicates to the pipeline when it should signal more data is available.
            Because the consumed parameter doubles as the examined parameter, the consumed parameter should be greater than or equal to the examined position in the previous call to `AdvanceTo`. Otherwise, an <see cref="T:System.InvalidOperationException" /> is thrown.</remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.AdvanceTo(System.SequencePosition,System.SequencePosition)">
            <summary>Moves forward the pipeline's read cursor to after the consumed data, marking the data as processed, read and examined.</summary>
            <param name="consumed">Marks the extent of the data that has been successfully processed.</param>
            <param name="examined">Marks the extent of the data that has been read and examined.</param>
            <remarks>The memory for the consumed data will be released and no longer available.
            The <see cref="P:System.IO.Pipelines.ReadResult.Buffer" /> previously returned from <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> must not be accessed after this call.
            The examined data communicates to the pipeline when it should signal more data is available.
            The examined parameter should be greater than or equal to the examined position in the previous call to `AdvanceTo`. Otherwise, an <see cref="T:System.InvalidOperationException" /> is thrown.</remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.AsStream(System.Boolean)">
            <summary>Returns a <see cref="T:System.IO.Stream" /> representation of the <see cref="T:System.IO.Pipelines.PipeReader" />.</summary>
            <param name="leaveOpen">An optional flag that indicates whether disposing the returned <see cref="T:System.IO.Stream" /> leaves <see cref="T:System.IO.Pipelines.PipeReader" /> open (<see langword="true" />) or completes <see cref="T:System.IO.Pipelines.PipeReader" /> (<see langword="false" />).</param>
            <returns>A stream that represents the <see cref="T:System.IO.Pipelines.PipeReader" />.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.CancelPendingRead">
            <summary>Cancels the pending <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> operation without causing it to throw and without completing the <see cref="T:System.IO.Pipelines.PipeReader" />. If there is no pending operation, this cancels the next operation.</summary>
            <remarks>The canceled <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> operation returns a <see cref="T:System.IO.Pipelines.ReadResult" /> where <see cref="P:System.IO.Pipelines.ReadResult.IsCanceled" /> is <see langword="true" />.</remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.Complete(System.Exception)">
            <summary>Signals to the producer that the consumer is done reading.</summary>
            <param name="exception">Optional <see cref="T:System.Exception" /> indicating a failure that's causing the pipeline to complete.</param>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.CompleteAsync(System.Exception)">
            <summary>Marks the current pipe reader instance as being complete, meaning no more data will be read from it.</summary>
            <param name="exception">An optional exception that indicates the failure that caused the reader to complete.</param>
            <returns>A value task that represents the asynchronous complete operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.OnWriterCompleted(System.Action{System.Exception,System.Object},System.Object)">
            <summary>Registers a callback that executes when the <see cref="T:System.IO.Pipelines.PipeWriter" /> side of the pipe is completed.</summary>
            <param name="callback">The callback to register.</param>
            <param name="state">The state object to pass to <paramref name="callback" /> when it's invoked.</param>
            <remarks><format type="text/markdown"><![CDATA[
            > [!IMPORTANT]
            > `OnWriterCompleted` may not be invoked on all implementations of <xref:System.IO.Pipelines.PipeWriter>. This method will be removed in a future release.
            ]]></format></remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.Create(System.IO.Stream,System.IO.Pipelines.StreamPipeReaderOptions)">
            <summary>Creates a <see cref="T:System.IO.Pipelines.PipeReader" /> wrapping the specified <see cref="T:System.IO.Stream" />.</summary>
            <param name="stream">The stream that the pipe reader will wrap.</param>
            <param name="readerOptions">The options to configure the pipe reader.</param>
            <returns>A <see cref="T:System.IO.Pipelines.PipeReader" /> that wraps the <see cref="T:System.IO.Stream" />.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.Create(System.Buffers.ReadOnlySequence{System.Byte})">
            <summary>
            Creates a <see cref="T:System.IO.Pipelines.PipeReader"/> wrapping the specified <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
            <param name="sequence">The sequence.</param>
            <returns>A <see cref="T:System.IO.Pipelines.PipeReader"/> that wraps the <see cref="T:System.Buffers.ReadOnlySequence`1"/>.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.CopyToAsync(System.IO.Pipelines.PipeWriter,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the bytes from the <see cref="T:System.IO.Pipelines.PipeReader" /> and writes them to the specified <see cref="T:System.IO.Pipelines.PipeWriter" />, using a specified buffer size and cancellation token.</summary>
            <param name="destination">The pipe writer to which the contents of the current stream will be copied.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A task that represents the asynchronous copy operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.CopyToAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the bytes from the <see cref="T:System.IO.Pipelines.PipeReader" /> and writes them to the specified stream, using a specified cancellation token.</summary>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A task that represents the asynchronous copy operation.</returns>
        </member>
        <member name="T:System.IO.Pipelines.PipeScheduler">
            <summary>Abstraction for running <see cref="T:System.IO.Pipelines.PipeReader" /> and <see cref="T:System.IO.Pipelines.PipeWriter" /> callbacks and continuations.</summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeScheduler.ThreadPool">
            <summary>The <see cref="T:System.IO.Pipelines.PipeScheduler" /> implementation that queues callbacks to the thread pool.</summary>
            <value>A <see cref="T:System.IO.Pipelines.PipeScheduler" /> instance that queues callbacks to the thread pool.</value>
        </member>
        <member name="P:System.IO.Pipelines.PipeScheduler.Inline">
            <summary>The <see cref="T:System.IO.Pipelines.PipeScheduler" /> implementation that runs callbacks inline.</summary>
            <value>A <see cref="T:System.IO.Pipelines.PipeScheduler" /> instance that runs callbacks inline.</value>
        </member>
        <member name="M:System.IO.Pipelines.PipeScheduler.Schedule(System.Action{System.Object},System.Object)">
            <summary>Requests <paramref name="action" /> to be run on scheduler with <paramref name="state" /> being passed in.</summary>
            <param name="action">The single-parameter action delegate to schedule.</param>
            <param name="state">The parameter to pass to the <paramref name="action" /> delegate.</param>
        </member>
        <member name="T:System.IO.Pipelines.PipeWriter">
            <summary>Defines a class that provides a pipeline to which data can be written.</summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.Complete(System.Exception)">
            <summary>Marks the <see cref="T:System.IO.Pipelines.PipeWriter" /> as being complete, meaning no more items will be written to it.</summary>
            <param name="exception">Optional <see cref="T:System.Exception" /> indicating a failure that's causing the pipeline to complete.</param>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.CompleteAsync(System.Exception)">
            <summary>Marks the current pipe writer instance as being complete, meaning no more data will be written to it.</summary>
            <param name="exception">An optional exception that indicates the failure that caused the pipeline to complete.</param>
            <returns>A value task that represents the asynchronous complete operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.CancelPendingFlush">
            <summary>Cancels the pending <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> or <see cref="M:System.IO.Pipelines.PipeWriter.WriteAsync(System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)" /> operation without causing the operation to throw and without completing the <see cref="T:System.IO.Pipelines.PipeWriter" />. If there is no pending operation, this cancels the next operation.</summary>
            <remarks>The canceled <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)" /> or <see cref="M:System.IO.Pipelines.PipeWriter.WriteAsync(System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)" /> operation returns a <see cref="T:System.IO.Pipelines.FlushResult" /> where <see cref="P:System.IO.Pipelines.FlushResult.IsCanceled" /> is <see langword="true" />.</remarks>
        </member>
        <member name="P:System.IO.Pipelines.PipeWriter.CanGetUnflushedBytes">
            <summary>Gets a value that indicates whether the current <see cref="T:System.IO.Pipelines.PipeWriter" /> supports reporting the count of unflushed bytes.</summary>
            <value><see langword="true" />If a class derived from <see cref="T:System.IO.Pipelines.PipeWriter" /> does not support getting the unflushed bytes, calls to <see cref="P:System.IO.Pipelines.PipeWriter.UnflushedBytes" /> throw <see cref="T:System.NotImplementedException" />.</value>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.OnReaderCompleted(System.Action{System.Exception,System.Object},System.Object)">
            <summary>Registers a callback that executes when the <see cref="T:System.IO.Pipelines.PipeReader" /> side of the pipe is completed.</summary>
            <param name="callback">The callback to register.</param>
            <param name="state">The state object to pass to <paramref name="callback" /> when it's invoked.</param>
            <remarks><format type="text/markdown"><![CDATA[
            > [!IMPORTANT]
            > `OnReaderCompleted` may not be invoked on all implementations of <xref:System.IO.Pipelines.PipeWriter>. This method will be removed in a future release.
            ]]></format></remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)">
            <summary>Makes bytes written available to <see cref="T:System.IO.Pipelines.PipeReader" /> and runs <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> continuation.</summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A task that represents and wraps the asynchronous flush operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.Advance(System.Int32)">
            <summary>Notifies the <see cref="T:System.IO.Pipelines.PipeWriter" /> that <paramref name="bytes" /> bytes were written to the output <see cref="T:System.Span`1" /> or <see cref="T:System.Memory`1" />. You must request a new buffer after calling <see cref="M:System.IO.Pipelines.PipeWriter.Advance(System.Int32)" /> to continue writing more data; you cannot write to a previously acquired buffer.</summary>
            <param name="bytes">The number of bytes written to the <see cref="T:System.Span`1" /> or <see cref="T:System.Memory`1" />.</param>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.GetMemory(System.Int32)">
            <summary>Returns a <see cref="T:System.Memory`1" /> to write to that is at least the requested size, as specified by the <paramref name="sizeHint" /> parameter.</summary>
            <param name="sizeHint">The minimum length of the returned <see cref="T:System.Memory`1" />. If 0, a non-empty memory buffer of arbitrary size is returned.</param>
            <returns>A memory buffer of at least <paramref name="sizeHint" /> bytes. If <paramref name="sizeHint" /> is 0, returns a non-empty buffer of arbitrary size.</returns>
            <remarks>There is no guarantee that successive calls will return the same buffer or the same-sized buffer.
            This method never returns <see cref="P:System.Memory`1.Empty" />, but it throws an <see cref="T:System.OutOfMemoryException" /> if the requested buffer size is not available.
            You must request a new buffer after calling <see cref="M:System.IO.Pipelines.PipeWriter.Advance(System.Int32)" /> to continue writing more data; you cannot write to a previously acquired buffer.</remarks>
            <exception cref="T:System.OutOfMemoryException">The requested buffer size is not available.</exception>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.GetSpan(System.Int32)">
            <summary>Returns a <see cref="T:System.Span`1" /> to write to that is at least the requested size, as specified by the <paramref name="sizeHint" /> parameter.</summary>
            <param name="sizeHint">The minimum length of the returned <see cref="T:System.Span`1" />. If 0, a non-empty buffer of arbitrary size is returned.</param>
            <returns>A buffer of at least <paramref name="sizeHint" /> bytes. If <paramref name="sizeHint" /> is 0, returns a non-empty buffer of arbitrary size.</returns>
            <remarks>There is no guarantee that successive calls will return the same buffer or the same-sized buffer.
            This method never returns <see cref="P:System.Span`1.Empty" />, but it throws an <see cref="T:System.OutOfMemoryException" /> if the requested buffer size is not available.
            You must request a new buffer after calling <see cref="M:System.IO.Pipelines.PipeWriter.Advance(System.Int32)" /> to continue writing more data; you cannot write to a previously acquired buffer.</remarks>
            <exception cref="T:System.OutOfMemoryException">The requested buffer size is not available.</exception>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.AsStream(System.Boolean)">
            <summary>Returns a <see cref="T:System.IO.Stream" /> representation of the <see cref="T:System.IO.Pipelines.PipeWriter" />.</summary>
            <param name="leaveOpen">An optional flag that indicates whether disposing the returned <see cref="T:System.IO.Stream" /> leaves <see cref="T:System.IO.Pipelines.PipeReader" /> open (<see langword="true" />) or completes <see cref="T:System.IO.Pipelines.PipeReader" /> (<see langword="false" />).</param>
            <returns>A stream that represents the <see cref="T:System.IO.Pipelines.PipeWriter" />.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.Create(System.IO.Stream,System.IO.Pipelines.StreamPipeWriterOptions)">
            <summary>Creates a <see cref="T:System.IO.Pipelines.PipeWriter" /> wrapping the specified <see cref="T:System.IO.Stream" />.</summary>
            <param name="stream">The stream that the pipe writer will wrap.</param>
            <param name="writerOptions">The options to configure the pipe writer.</param>
            <returns>A <see cref="T:System.IO.Pipelines.PipeWriter" /> that wraps the <see cref="T:System.IO.Stream" />.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.WriteAsync(System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>Writes the specified byte memory range to the pipe and makes data accessible to the <see cref="T:System.IO.Pipelines.PipeReader" />.</summary>
            <param name="source">The read-only byte memory region to write.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A task that represents the asynchronous write operation, and wraps the flush asynchronous operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.CopyFromAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the bytes from the specified stream and writes them to the <see cref="T:System.IO.Pipelines.PipeWriter" />.</summary>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A task that represents the asynchronous copy operation.</returns>
        </member>
        <member name="P:System.IO.Pipelines.PipeWriter.UnflushedBytes">
            <summary>
            When overridden in a derived class, gets the count of unflushed bytes within the current writer.
            </summary>
            <exception cref="T:System.NotImplementedException">The <see cref="T:System.IO.Pipelines.PipeWriter"/> does not support getting the unflushed byte count.</exception>
        </member>
        <member name="T:System.IO.Pipelines.ReadResult">
            <summary>Represents the result of a <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> call.</summary>
        </member>
        <member name="M:System.IO.Pipelines.ReadResult.#ctor(System.Buffers.ReadOnlySequence{System.Byte},System.Boolean,System.Boolean)">
            <summary>Creates a new instance of <see cref="T:System.IO.Pipelines.ReadResult" /> setting <see cref="P:System.IO.Pipelines.ReadResult.IsCanceled" /> and <see cref="P:System.IO.Pipelines.ReadResult.IsCompleted" /> flags.</summary>
            <param name="buffer">The read-only sequence containing the bytes of data that were read in the <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> call.</param>
            <param name="isCanceled">A flag that indicates if the <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> operation that produced this <see cref="T:System.IO.Pipelines.ReadResult" /> was canceled by <see cref="M:System.IO.Pipelines.PipeReader.CancelPendingRead" />.</param>
            <param name="isCompleted">A flag that indicates whether the end of the data stream has been reached.</param>
        </member>
        <member name="P:System.IO.Pipelines.ReadResult.Buffer">
            <summary>Gets the <see cref="T:System.Buffers.ReadOnlySequence`1" /> that was read.</summary>
            <value>A read-only sequence containing the bytes of data that were read in the <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> call.</value>
        </member>
        <member name="P:System.IO.Pipelines.ReadResult.IsCanceled">
            <summary>Gets a value that indicates whether the current <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> operation was canceled by <see cref="M:System.IO.Pipelines.PipeReader.CancelPendingRead" />.</summary>
            <value><see langword="true" /> if the <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)" /> operation that produced this <see cref="T:System.IO.Pipelines.ReadResult" /> was canceled by <see cref="M:System.IO.Pipelines.PipeReader.CancelPendingRead" />; otherwise, <see langword="false" />.</value>
        </member>
        <member name="P:System.IO.Pipelines.ReadResult.IsCompleted">
            <summary>Gets a value that indicates whether the end of the data stream has been reached.</summary>
            <value><see langword="true" /> if the end of the data stream has been reached; otherwise, <see langword="false" />.</value>
        </member>
        <member name="M:System.IO.Pipelines.SequencePipeReader.AdvanceTo(System.SequencePosition)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.SequencePipeReader.AdvanceTo(System.SequencePosition,System.SequencePosition)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.SequencePipeReader.CancelPendingRead">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.SequencePipeReader.Complete(System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.SequencePipeReader.ReadAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.SequencePipeReader.TryRead(System.IO.Pipelines.ReadResult@)">
            <inheritdoc />
        </member>
        <member name="T:System.IO.Pipelines.StreamPipeExtensions">
            <summary>Provides extension methods for <see cref="T:System.IO.Stream" /> that support read and write operations directly into pipes.</summary>
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeExtensions.CopyToAsync(System.IO.Stream,System.IO.Pipelines.PipeWriter,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the bytes from the <see cref="T:System.IO.Stream" /> and writes them to the specified <see cref="T:System.IO.Pipelines.PipeWriter" />, using a cancellation token.</summary>
            <param name="source">The stream from which the contents of the current stream will be copied.</param>
            <param name="destination">The writer to which the contents of the source stream will be copied.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A task that represents the asynchronous copy operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.#ctor(System.IO.Stream,System.IO.Pipelines.StreamPipeReaderOptions)">
            <summary>
            Creates a new StreamPipeReader.
            </summary>
            <param name="readingStream">The stream to read from.</param>
            <param name="options">The options to use.</param>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReader.InnerStream">
            <summary>
            Gets the inner stream that is being read from.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.AdvanceTo(System.SequencePosition)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.AdvanceTo(System.SequencePosition,System.SequencePosition)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.CancelPendingRead">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.Complete(System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.ReadAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.CopyToAsync(System.IO.Pipelines.PipeWriter,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReader.CopyToAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="T:System.IO.Pipelines.StreamPipeReaderOptions">
            <summary>Represents a set of options for controlling the creation of the <see cref="T:System.IO.Pipelines.PipeReader" />.</summary>
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReaderOptions.#ctor(System.Buffers.MemoryPool{System.Byte},System.Int32,System.Int32,System.Boolean)">
            <summary>Initializes a <see cref="T:System.IO.Pipelines.StreamPipeReaderOptions" /> instance, optionally specifying a memory pool, a minimum buffer size, a minimum read size, and whether the underlying stream should be left open after the <see cref="T:System.IO.Pipelines.PipeReader" /> completes.</summary>
            <param name="pool">The memory pool to use when allocating memory. The default value is <see langword="null" />.</param>
            <param name="bufferSize">The minimum buffer size to use when renting memory from the <paramref name="pool" />. The default value is 4096.</param>
            <param name="minimumReadSize">The threshold of remaining bytes in the buffer before a new buffer is allocated. The default value is 1024.</param>
            <param name="leaveOpen"><see langword="true" /> to leave the underlying stream open after the <see cref="T:System.IO.Pipelines.PipeReader" /> completes; <see langword="false" /> to close it. The default is <see langword="false" />.</param>
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeReaderOptions.#ctor(System.Buffers.MemoryPool{System.Byte},System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>Initializes a <see cref="T:System.IO.Pipelines.StreamPipeReaderOptions" /> instance, optionally specifying a memory pool, a minimum buffer size, a minimum read size, and whether the underlying stream should be left open after the <see cref="T:System.IO.Pipelines.PipeReader" /> completes.</summary>
            <param name="pool">The memory pool to use when allocating memory. The default value is <see langword="null" />.</param>
            <param name="bufferSize">The minimum buffer size to use when renting memory from the <paramref name="pool" />. The default value is 4096.</param>
            <param name="minimumReadSize">The threshold of remaining bytes in the buffer before a new buffer is allocated. The default value is 1024.</param>
            <param name="leaveOpen"><see langword="true" /> to leave the underlying stream open after the <see cref="T:System.IO.Pipelines.PipeReader" /> completes; <see langword="false" /> to close it. The default is <see langword="false" />.</param>
            <param name="useZeroByteReads"><see langword="true" /> if reads with an empty buffer should be issued to the underlying stream before allocating memory; otherwise, <see langword="false" />.</param>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReaderOptions.BufferSize">
            <summary>Gets the minimum buffer size to use when renting memory from the <see cref="P:System.IO.Pipelines.StreamPipeReaderOptions.Pool" />.</summary>
            <value>The buffer size.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReaderOptions.MaxBufferSize">
            <summary>Gets the maximum buffer size to use when renting memory from the <see cref="P:System.IO.Pipelines.StreamPipeReaderOptions.Pool" />.</summary>
            <value>The maximum buffer size.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReaderOptions.MinimumReadSize">
            <summary>Gets the threshold of remaining bytes in the buffer before a new buffer is allocated.</summary>
            <value>The minimum read size.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReaderOptions.Pool">
            <summary>Gets the <see cref="T:System.Buffers.MemoryPool`1" /> to use when allocating memory.</summary>
            <value>A memory pool instance.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReaderOptions.LeaveOpen">
            <summary>Gets the value that indicates if the underlying stream should be left open after the <see cref="T:System.IO.Pipelines.PipeReader" /> completes.</summary>
            <value><see langword="true" /> if the underlying stream should be left open after the <see cref="T:System.IO.Pipelines.PipeReader" /> completes; otherwise, <see langword="false" />.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReaderOptions.UseZeroByteReads">
            <summary>Gets the value that indicates if reads with an empty buffer should be issued to the underlying stream, in order to wait for data to arrive before allocating memory.</summary>
            <value><see langword="true" /> if reads with an empty buffer should be issued to the underlying stream before allocating memory; otherwise, <see langword="false" />.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeReaderOptions.IsDefaultSharedMemoryPool">
            <summary>
            Returns true if Pool is <see cref="T:System.Buffers.MemoryPool`1"/>.Shared
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeWriter.InnerStream">
            <summary>
            Gets the inner stream that is being written to.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeWriter.Advance(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeWriter.GetMemory(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeWriter.GetSpan(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeWriter.CancelPendingFlush">
            <inheritdoc />
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeWriter.CanGetUnflushedBytes">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeWriter.Complete(System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeWriter.FlushAsync(System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeWriter.UnflushedBytes">
            <inheritdoc />
        </member>
        <member name="T:System.IO.Pipelines.StreamPipeWriterOptions">
            <summary>Represents a set of options for controlling the creation of the <see cref="T:System.IO.Pipelines.PipeWriter" />.</summary>
        </member>
        <member name="M:System.IO.Pipelines.StreamPipeWriterOptions.#ctor(System.Buffers.MemoryPool{System.Byte},System.Int32,System.Boolean)">
            <summary>Initializes a <see cref="T:System.IO.Pipelines.StreamPipeWriterOptions" /> instance, optionally specifying a memory pool, a minimum buffer size, and whether the underlying stream should be left open after the <see cref="T:System.IO.Pipelines.PipeWriter" /> completes.</summary>
            <param name="pool">The memory pool to use when allocating memory. The default value is <see langword="null" />.</param>
            <param name="minimumBufferSize">The minimum buffer size to use when renting memory from the <paramref name="pool" />. The default value is 4096.</param>
            <param name="leaveOpen"><see langword="true" /> to leave the underlying stream open after the <see cref="T:System.IO.Pipelines.PipeWriter" /> completes; <see langword="false" /> to close it. The default is <see langword="false" />.</param>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeWriterOptions.MinimumBufferSize">
            <summary>Gets the minimum buffer size to use when renting memory from the <see cref="P:System.IO.Pipelines.StreamPipeWriterOptions.Pool" />.</summary>
            <value>An integer representing the minimum buffer size.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeWriterOptions.Pool">
            <summary>Gets the <see cref="T:System.Buffers.MemoryPool`1" /> to use when allocating memory.</summary>
            <value>A memory pool instance.</value>
        </member>
        <member name="P:System.IO.Pipelines.StreamPipeWriterOptions.LeaveOpen">
            <summary>Gets the value that indicates if the underlying stream should be left open after the <see cref="T:System.IO.Pipelines.PipeWriter" /> completes.</summary>
            <value><see langword="true" /> if the underlying stream should be left open after the <see cref="T:System.IO.Pipelines.PipeWriter" /> completes; otherwise, <see langword="false" />.</value>
        </member>
        <member name="T:System.IO.StreamHelpers">
            <summary>Provides methods to help in the implementation of Stream-derived types.</summary>
        </member>
        <member name="M:System.IO.StreamHelpers.ValidateCopyToArgs(System.IO.Stream,System.IO.Stream,System.Int32)">
            <summary>Validate the arguments to CopyTo, as would Stream.CopyTo.</summary>
        </member>
        <member name="P:System.SR.AdvanceToInvalidCursor">
            <summary>The PipeReader has already advanced past the provided position.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedPosNum">
            <summary>Positive number required.</summary>
        </member>
        <member name="P:System.SR.ConcurrentOperationsNotSupported">
            <summary>Concurrent reads or writes are not supported.</summary>
        </member>
        <member name="P:System.SR.FlushCanceledOnPipeWriter">
            <summary>Flush was canceled on underlying PipeWriter.</summary>
        </member>
        <member name="P:System.SR.GetResultBeforeCompleted">
            <summary>Can't GetResult unless awaiter is completed.</summary>
        </member>
        <member name="P:System.SR.InvalidExaminedOrConsumedPosition">
            <summary>The examined position must be greater than or equal to the consumed position.</summary>
        </member>
        <member name="P:System.SR.InvalidExaminedPosition">
            <summary>The examined position cannot be less than the previously examined position.</summary>
        </member>
        <member name="P:System.SR.InvalidZeroByteRead">
            <summary>The PipeReader returned 0 bytes when the ReadResult was not completed or canceled.</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed_StreamClosed">
            <summary>Cannot access a closed stream.</summary>
        </member>
        <member name="P:System.SR.NoReadingOperationToComplete">
            <summary>No reading operation to complete.</summary>
        </member>
        <member name="P:System.SR.NotSupported_UnreadableStream">
            <summary>Stream does not support reading.</summary>
        </member>
        <member name="P:System.SR.NotSupported_UnwritableStream">
            <summary>Stream does not support writing.</summary>
        </member>
        <member name="P:System.SR.ReadCanceledOnPipeReader">
            <summary>Read was canceled on underlying PipeReader.</summary>
        </member>
        <member name="P:System.SR.ReaderAndWriterHasToBeCompleted">
            <summary>Both reader and writer has to be completed to be able to reset the pipe.</summary>
        </member>
        <member name="P:System.SR.ReadingAfterCompleted">
            <summary>Reading is not allowed after reader was completed.</summary>
        </member>
        <member name="P:System.SR.ReadingIsInProgress">
            <summary>Reading is already in progress.</summary>
        </member>
        <member name="P:System.SR.WritingAfterCompleted">
            <summary>Writing is not allowed after writer was completed.</summary>
        </member>
        <member name="P:System.SR.UnflushedBytesNotSupported">
            <summary>UnflushedBytes is not supported.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>
