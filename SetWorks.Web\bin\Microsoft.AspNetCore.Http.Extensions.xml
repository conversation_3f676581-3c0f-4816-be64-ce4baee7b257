<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Http.Extensions</name>
    </assembly>
    <members>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.StreamCopyOperation.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>Asynchronously reads the bytes from the source stream and writes them to another stream.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.StreamCopyOperation.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the bytes from the source stream and writes them to another stream, using a specified buffer size.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 4096.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Extensions.UriHelper">
            <summary>
            A helper class for constructing encoded Uris for use in headers and other Uris.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.BuildRelative(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.QueryString,Microsoft.AspNetCore.Http.FragmentString)">
            <summary>
            Combines the given URI components into a string that is properly encoded for use in HTTP headers.
            </summary>
            <param name="pathBase">The first portion of the request path associated with application root.</param>
            <param name="path">The portion of the request path that identifies the requested resource.</param>
            <param name="query">The query, if any.</param>
            <param name="fragment">The fragment, if any.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.BuildAbsolute(System.String,Microsoft.AspNetCore.Http.HostString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.QueryString,Microsoft.AspNetCore.Http.FragmentString)">
            <summary>
            Combines the given URI components into a string that is properly encoded for use in HTTP headers.
            Note that unicode in the HostString will be encoded as punycode.
            </summary>
            <param name="scheme">http, https, etc.</param>
            <param name="host">The host portion of the uri normally included in the Host header. This may include the port.</param>
            <param name="pathBase">The first portion of the request path associated with application root.</param>
            <param name="path">The portion of the request path that identifies the requested resource.</param>
            <param name="query">The query, if any.</param>
            <param name="fragment">The fragment, if any.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.FromAbsolute(System.String,System.String@,Microsoft.AspNetCore.Http.HostString@,Microsoft.AspNetCore.Http.PathString@,Microsoft.AspNetCore.Http.QueryString@,Microsoft.AspNetCore.Http.FragmentString@)">
            <summary>
            Separates the given absolute URI string into components. Assumes no PathBase.
            </summary>
            <param name="uri">A string representation of the uri.</param>
            <param name="scheme">http, https, etc.</param>
            <param name="host">The host portion of the uri normally included in the Host header. This may include the port.</param>
            <param name="path">The portion of the request path that identifies the requested resource.</param>
            <param name="query">The query, if any.</param>
            <param name="fragment">The fragment, if any.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.Encode(System.Uri)">
            <summary>
            Generates a string from the given absolute or relative Uri that is appropriately encoded for use in
            HTTP headers. Note that a unicode host name will be encoded as punycode.
            </summary>
            <param name="uri">The Uri to encode.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.GetEncodedUrl(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Returns the combined components of the request URL in a fully escaped form suitable for use in HTTP headers
            and other HTTP operations.
            </summary>
            <param name="request">The request to assemble the uri pieces from.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.GetEncodedPathAndQuery(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Returns the relative url 
            </summary>
            <param name="request">The request to assemble the uri pieces from.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Extensions.UriHelper.GetDisplayUrl(Microsoft.AspNetCore.Http.HttpRequest)">
            <summary>
            Returns the combined components of the request URL in a fully un-escaped form (except for the QueryString)
            suitable only for display. This format should not be used in HTTP headers or other HTTP operations.
            </summary>
            <param name="request">The request to assemble the uri pieces from.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.SendFileResponseExtensions">
            <summary>
            Provides extensions for HttpResponse exposing the SendFile extension.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,Microsoft.Extensions.FileProviders.IFileInfo,System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="file">The file.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,Microsoft.Extensions.FileProviders.IFileInfo,System.Int64,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="file">The file.</param>
            <param name="offset">The offset in the file.</param>
            <param name="count">The number of bytes to send, or null to send the remainder of the file.</param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="fileName">The full path to the file.</param>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/>.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.SendFileResponseExtensions.SendFileAsync(Microsoft.AspNetCore.Http.HttpResponse,System.String,System.Int64,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>
            Sends the given file using the SendFile extension.
            </summary>
            <param name="response"></param>
            <param name="fileName">The full path to the file.</param>
            <param name="offset">The offset in the file.</param>
            <param name="count">The number of bytes to send, or null to send the remainder of the file.</param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
    </members>
</doc>
