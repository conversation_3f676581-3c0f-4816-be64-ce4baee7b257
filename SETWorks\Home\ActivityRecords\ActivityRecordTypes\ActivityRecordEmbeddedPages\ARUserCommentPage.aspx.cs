using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;
using System.Web.UI.HtmlControls;
using BOs;
using Forms;
using SetWorks.Common.Tools.SETWorksAI;
using SW.UI;
using Ninject;
using SETWorksDAO.DAO.AI_ASSISTANT_ACTIVITY_RECORD_CONFIGURATION;
using CheckBox = System.Web.UI.WebControls.CheckBox;

public partial class Home_ActivityRecords_ActivityRecordTypes_ARUserCommentPage : SWPageNoSession
{
    private string COMMENT_STATE = "COMMENT_STATE";

    [Inject]
    public IAIConfiguration _aAiConfiguration { get; set; }
    [Inject]
    public IAISystemConfiguration _aiSystemConfiguration { get; set; }
    [Inject]
    public IAIClient _aiClient { get; set; }

    private String USER_COMMENT_ID = "UserCommentID";
    private String CONSUMER_ID = "ConsumerID";
    private String GOAL_ID = "GoalID";
    private String PARENT_CONTAINER = "PARENT_CONTAINER_ID";
    private String COMMENT_TYPE = "COMMENT_TYPE";
    private bool USER_HAS_AI_ASSISTANT_ACCESS = false;
    private DSAIAssistantActivityRecordConfiguration activityRecordsAIAssistantConfig;
    protected void Page_Init(object sender, EventArgs e)
    {
        int clientID = getClientID();
        string userID = getUserID();
        USER_HAS_AI_ASSISTANT_ACCESS = BOAIAccessConfiguration.hasAIEnhancedAccess(clientID, userID);
        activityRecordsAIAssistantConfig = BOAIAssistantActivityRecordConfiguration.getAIAssistantActivityRecordConfiguration(getClientID());
    }
    
    protected void Page_Load(object sender, EventArgs e)
    {
        HiddenCURRENTWINDOWCLIENTID.Value = getClientID().ToString();
        HiddenCURRENTWINDOWUSERID.Value = getUserID();

        if (!Page.IsPostBack)
        {
            if (Request["Action"] != null)
            {
                HiddenACTION.Value = Request["Action"];
            }

            if (Request["ActivityRecordCommentTypeCode"] != null)
            {
                HiddenACTIVITY_RECORD_COMMENT_TYPE.Value = Request["ActivityRecordCommentTypeCode"];
            }

            if (Request["ActivityRecordsGroupID"] != null)
            {
                HiddenACTIVITY_RECORD_GROUP_ID.Value = Request["ActivityRecordsGroupID"];
            }

            if (Request["ActivityRecordsGroupUserCommentID"] != null)
            {
                HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value = Request["ActivityRecordsGroupUserCommentID"];
            }

            if (Request["ConsumerID"] != null)
            {
                HiddenCONSUMER_ID.Value = Request["ConsumerID"];
            }

            if (Request["OutcomeID"] != null)
            {
                HiddenOUTCOME_ID.Value = Request["OutcomeID"];
            }

            if (Request["GoalID"] != null)
            {
                HiddenGOAL_ID.Value = Request["GoalID"];
            }

            if (Request["ServiceID"] != null)
            {
                HiddenSERVICE_ID.Value = Request["ServiceID"];
            }

            if (Request["CommentCreator"] != null)
            {
                HiddenCOMMENT_CREATOR.Value = Request["CommentCreator"];
            }

            if (Request["CanEdit"] != null)
            {
                HiddenCAN_EDIT.Value = Request["CanEdit"];
            }

            if (Request["GroupDate"] != null)
            {
                HiddenGROUP_DATE.Value = Request["GroupDate"];
            }

            if (Request["PostSaveParameters"] != null)
            {
                HiddenPOST_SAVE_PARAMETERS.Value = Request["PostSaveParameters"];
            }

            HiddenConsumerCommentLabel.Value = "Comment";
            if (Request["ConsumerCommentLabel"] != null)
            {
                HiddenConsumerCommentLabel.Value = Request["ConsumerCommentLabel"];
            }

            if (HiddenACTION.Value == "CREATE")
                RadAjaxManagerARProxy.ResponseScripts.Add("addNewCommentPageLoaded();");
        }

        drawUserComments();
    }

    private void drawUserComments()
    {
        long[] iAccess = getUserPrivs();
        DIVUserComment.Controls.Clear();
        if (HiddenACTION.Value == Constants.CREATE)
        {
            ActivityRecordCommentType commentType = ActivityRecordCommentType.NOT_STANDARD_COMMENT;

            if (HiddenACTIVITY_RECORD_COMMENT_TYPE.Value == ActivityRecordCommentType.CONSUMER.ToString())
            {
                commentType = ActivityRecordCommentType.CONSUMER;
            }
            else if (HiddenACTIVITY_RECORD_COMMENT_TYPE.Value == ActivityRecordCommentType.GOAL.ToString())
            {
                commentType = ActivityRecordCommentType.GOAL;
            }
            else if (HiddenACTIVITY_RECORD_COMMENT_TYPE.Value == ActivityRecordCommentType.OVERALL.ToString())
            {
                commentType = ActivityRecordCommentType.OVERALL;
            }

            drawUserCommentSection(iAccess, null, commentType, getUserID(), DateTime.UtcNow, false, Int32.Parse(HiddenCONSUMER_ID.Value), Int32.Parse(HiddenGOAL_ID.Value), true);
        }
        else
        {
            DSActivityRecordGroupUserComment userComment = BOActivityRecordGroup.getActivityRecordGroupUserComment(getClientID(), Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value),
                Int32.Parse(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value));

            drawUserCommentSection(iAccess, userComment, userComment.getActivityRecordCommentType, userComment.getUserID.ToString(), userComment.getLastUpdated,
                userComment.getActivityRecordCommentType == ActivityRecordCommentType.GOAL, userComment.getConsumerID, userComment.getGoalID, true);
        }
    }

    private void drawUserCommentSection(long[] iAccess, DSActivityRecordGroupUserComment _userComment, ActivityRecordCommentType _commentType, String _userID, DateTime _lastUpdatedDate,
        bool _isGoal, int _consumerID, int _goalID, bool _canEdit)
    {
        HtmlGenericControl consumerHeaderPanel = new HtmlGenericControl("div");
        consumerHeaderPanel.Style.Add("max-width", "550px");
        consumerHeaderPanel.ClientIDMode = System.Web.UI.ClientIDMode.Static;
        consumerHeaderPanel.ID = "header-panel-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString();

        HtmlGenericControl consumerCommentBoxPanel = new HtmlGenericControl("div");
        consumerCommentBoxPanel.ID = "comment-panel-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString();
        consumerCommentBoxPanel.Style.Add("max-width", "550px");

        String consumerID = HiddenCONSUMER_ID.Value;

        HtmlGenericControl headerButtonPanel = new HtmlGenericControl("span");
        headerButtonPanel.ClientIDMode = System.Web.UI.ClientIDMode.Static;
        headerButtonPanel.ID = "header-button-panel-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString();
        consumerHeaderPanel.Controls.Add(headerButtonPanel);

        int labelTextBoxHeight = 0;

        if (HiddenACTION.Value == Constants.CREATE.ToString())
        {
            int entityID = getEntityID(_commentType, _consumerID, _goalID);

            Label label = createCommentLabel("consumer-staff-comment-label-" + HiddenCONSUMER_ID.Value + "-" + _userID + "-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value +
                                             "-" + _goalID.ToString());
            label.Text = "<div class='comment-by-label'>New " + HiddenConsumerCommentLabel.Value + " by <b>" + EncoderAndDecoder.Decode(HiddenCOMMENT_CREATOR.Value) +
                         "</b>:</div>";
            consumerHeaderPanel.Controls.Add(label);

            LinkButton cancelButton = createCancelButton(_goalID);
            headerButtonPanel.Controls.Add(cancelButton);

            LinkButton saveButton = createSaveButton(_goalID);
            saveButton.Attributes[PARENT_CONTAINER] = consumerCommentBoxPanel.ID;
            saveButton.Attributes[COMMENT_TYPE] = _commentType.ToString();
            headerButtonPanel.Controls.Add(saveButton);                        

            createGoalKeysPanel(iAccess, consumerHeaderPanel, _consumerID, _goalID, true);
            DSGoal goal = BOGoal.getGoalByGoalID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), _goalID);
            if (_commentType != ActivityRecordCommentType.GOAL || goal.getIncludeCommentForAR)
            {
                RadTextBox consumerCommentTextBox = createCommentTextBox(_goalID);
                consumerCommentTextBox.Height = new Unit("147px");
                consumerCommentTextBox.EnableViewState = false;
                addEmptyMessageContent(consumerCommentTextBox);
                consumerCommentBoxPanel.Controls.Add(consumerCommentTextBox);
                consumerCommentTextBox.Visible = true;
                if (!Privilege.isPrivTrue(iAccess, Privileges15.ENABLE_PASTE_IN_BILLABLE_GROUP_GOAL_COMMENTS))
                {
                    consumerCommentTextBox.Attributes["onpaste"] = "return false;";
                    consumerCommentTextBox.Attributes["ondrop"] = "return false;";
                    consumerCommentTextBox.Attributes["ondragover"] = "return false;";
                    Label disabledPasteLbl = createCommentLabel("disabled-paste-label-" + consumerID + "-" + _userID + "-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" +
                                                                  _goalID.ToString());
                    disabledPasteLbl.Text = "<div class='comment-by-label'>Your organization has disabled the ability to paste in this field.</div>";
                    consumerCommentBoxPanel.Controls.Add(disabledPasteLbl);
                }
            }
            else
            {
                DIVUserComment.Style.Add("height", "115px");
            }

            ajaxify(saveButton.ID, DIVUserComment.ID);
        }
        else
        {
            if (HiddenCAN_EDIT.Value.ToUpper().Equals("TRUE"))
            {
                LinkButton editButton = createEditButton();
                editButton.Attributes[PARENT_CONTAINER] = consumerCommentBoxPanel.ID;
                headerButtonPanel.Controls.Add(editButton);
                ajaxify(editButton.ID, DIVUserComment.ID);

                if (HiddenEDIT_USER_COMMENT_ID.Value == _userComment.getActivityRecordsGroupUserCommentID.ToString())
                {
                    editButton.Visible = false;
                }
            }

            Label label = createCommentLabel("consumer-staff-comment-label-" + consumerID + "-" + _userID + "-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" +
                                             _goalID.ToString());
            label.Text = "<div class='comment-by-label'>" + HiddenConsumerCommentLabel.Value + " by <b>" + EncoderAndDecoder.Decode(HiddenCOMMENT_CREATOR.Value) + "</b> on " +
                         _lastUpdatedDate.ToShortDateString() + " " + _lastUpdatedDate.ToShortTimeString() + ":</div>";
            consumerHeaderPanel.Controls.Add(label);
            DSGoal goal = BOGoal.getGoalByGoalID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), _goalID);

            if (_userComment.getComment.Length < 100)
            {
                labelTextBoxHeight = 40;
            }
            else
            {
                labelTextBoxHeight = 128;
            }

            if (HiddenEDIT_USER_COMMENT_ID.Value == _userComment.getActivityRecordsGroupUserCommentID.ToString())
            {
                LinkButton cancelButton = createCancelButton(_goalID);
                headerButtonPanel.Controls.Add(cancelButton);

                LinkButton saveButton = createSaveButton(_goalID);
                saveButton.Attributes[PARENT_CONTAINER] = consumerCommentBoxPanel.ID;
                saveButton.Attributes[GOAL_ID] = _goalID.ToString();
                headerButtonPanel.Controls.Add(saveButton);

                createGoalKeysPanel(iAccess, consumerHeaderPanel, _consumerID, _goalID, _canEdit);

                if (!String.IsNullOrEmpty(_userComment.getComment) || goal.getIncludeCommentForAR)
                {
                    RadTextBox consumerCommentTextBox = createCommentTextBox(_goalID);
                    consumerCommentTextBox.Height = new Unit("147px");
                    consumerCommentTextBox.EnableViewState = false;
                    addEmptyMessageContent(consumerCommentTextBox);
                    consumerCommentTextBox.Text = _userComment.getComment;
                    consumerCommentBoxPanel.Controls.Add(consumerCommentTextBox);
                    consumerCommentTextBox.Visible = true;
                    if (!Privilege.isPrivTrue(iAccess, Privileges15.ENABLE_PASTE_IN_BILLABLE_GROUP_GOAL_COMMENTS))
                    {
                        consumerCommentTextBox.Attributes["onpaste"] = "return false;";
                        consumerCommentTextBox.Attributes["ondrop"] = "return false;";
                        consumerCommentTextBox.Attributes["ondragover"] = "return false;";

                        Label disabledPasteLbl = createCommentLabel("disabled-paste-label-" + consumerID + "-" + _userID + "-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" +
                                                                    _goalID.ToString());
                        disabledPasteLbl.Text = "<div class='comment-by-label'>Your organization has disabled the ability to paste in this field.</div>";
                        consumerCommentBoxPanel.Controls.Add(disabledPasteLbl);
                    }
                }
                else
                {
                    DIVUserComment.Style.Add("height", "115px");
                }
            }
            else
            {
                createGoalKeysPanel(iAccess, consumerHeaderPanel, _consumerID, _goalID, false);
                if (!String.IsNullOrEmpty(_userComment.getComment))
                {
                    Label labelComment = createCommentLabelValue("consumer-comment-label-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString());
                    labelComment.Text =  _userComment.getComment.Replace("<", "&lt;").Replace("\r\n", "<br />");

                    labelComment.Style.Add("overflow", "auto");

                    labelComment.Visible = true;
                    consumerCommentBoxPanel.Controls.Add(labelComment);

                    if (_userComment.getComment.Length < 100)
                    {
                        labelTextBoxHeight = 65;
                    }
                    else
                    {
                        labelTextBoxHeight = 120;
                    }

                    labelComment.Height = new Unit(labelTextBoxHeight.ToString() + "px");
                }
            }
        }
        
        var canAccessAutoReviewButton = USER_HAS_AI_ASSISTANT_ACCESS && Privilege.isPrivTrue(iAccess, Privileges17.ENABLE_AI_ASSISTANT_AUTO_REVIEW_GROUP_ACTIVITY_RECORD);
        if (canAccessAutoReviewButton)
        {
            if ((_commentType == ActivityRecordCommentType.CONSUMER || _commentType == ActivityRecordCommentType.OVERALL) && 
                (activityRecordsAIAssistantConfig.provideGeneralSuggestions || activityRecordsAIAssistantConfig.reviewGrammar || 
                 activityRecordsAIAssistantConfig.reviewTone || activityRecordsAIAssistantConfig.reviewSubjectiveObjectiveLanguage || 
                 activityRecordsAIAssistantConfig.useCustomInstructions))
            {
                CreateAutoReviewControls(_commentType, _goalID, headerButtonPanel, DIVUserComment);
            }
            else if (_commentType == ActivityRecordCommentType.GOAL && (activityRecordsAIAssistantConfig.reviewOutcomeGoalComments || activityRecordsAIAssistantConfig.useCustomInstructions))
            {
                CreateAutoReviewControls(_commentType, _goalID, headerButtonPanel, DIVUserComment);
            }
        } 

        DIVUserComment.Controls.Add(consumerHeaderPanel);
        DIVUserComment.Controls.Add(consumerCommentBoxPanel);

        pageLoadedPostProcessing(labelTextBoxHeight);
    }

    private void addEmptyMessageContent(RadTextBox _textBox)
    {
        DSService service = BOService.getServiceByServiceIDAndClientID(Int32.Parse(HiddenSERVICE_ID.Value), Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value));

        if (HiddenACTIVITY_RECORD_COMMENT_TYPE.Value == ActivityRecordCommentType.CONSUMER.ToString())
        {
            _textBox.EmptyMessage =
                "Daily notes should include a complete accounting of the appointment including activities, goals worked on, places traveled, and any additional comments or concerns.";
            if (!String.IsNullOrEmpty(service.getDefaultContent))
            {
                _textBox.Text = service.getDefaultContent.Replace("&nbsp;", " ");
            }

            if (!String.IsNullOrEmpty(service.GetEmptyContent))
            {
                _textBox.EmptyMessage = service.GetEmptyContent.Replace("&nbsp;", " ");
            }
        }
        else if (HiddenACTIVITY_RECORD_COMMENT_TYPE.Value == ActivityRecordCommentType.GOAL.ToString())
        {
            if (!String.IsNullOrEmpty(service.GetDefaultGoalComment))
            {
                _textBox.Text = service.GetDefaultGoalComment.Replace("&nbsp;", " ");
            }
        }
    }

    private void addInactiveKeysAlreadyDocumentedOn(List<DSGoalKey> goalKeys, DSActivityRecordGroupGoalKey[] ARGoalKeys, int _goalID)
    {
        foreach (DSActivityRecordGroupGoalKey ARGGoalKey in ARGoalKeys)
        {
            bool found = false;
            DSGoalKey keyToAdd = null;

            foreach (DSGoalKey goalKey in goalKeys)
            {
                if (goalKey.getActivityRecordKeyID == ARGGoalKey.getActivityRecordsKeyID)
                    found = true;
            }

            if (!found)
            {
                //ARGGoalKey.
                DSActivityRecordKey newARKey =
                    BOActivityRecordKey.getActivityRecordKeyByActivityRecordKeyID(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), ARGGoalKey.getActivityRecordsKeyID);

                goalKeys.Add(new DSGoalKey(Int32.Parse(HiddenCURRENTWINDOWCLIENTID.Value), _goalID, newARKey.getActivityRecordKeyID, newARKey));
            }
        }
    }

    private void createGoalKeysPanel(long[] iAccess, HtmlGenericControl _parentDIV, int _consumerID, int _goalID, bool _allowModify)
    {
        DSCodeValue[] keyTypes = BOActivityRecordKey.getActivityRecordKeyTypes(getClientID());
        foreach (DSCodeValue keyType in keyTypes)
        {
            DSGoalKey[] goalKeys =
                BOGoal.getGoalKeysByGoalIDAndKeyTypeIDAndServiceID(getClientID(), _consumerID, _goalID, Int32.Parse(keyType.getCodeValueID), Int32.Parse(HiddenSERVICE_ID.Value));
            List<DSGoalKey> goalKeysUnion = goalKeys.ToList();
            DSActivityRecordGroupGoalKey[] ARGoalKeys = BOActivityRecordGroup.getActivityRecordGroupGoalKeysByConsumerIDAndUserCommentIDAndKeyTypeID(getClientID(),
                Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value), _consumerID, Int32.Parse(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value),
                Int32.Parse(keyType.getCodeValueID));

            addInactiveKeysAlreadyDocumentedOn(goalKeysUnion, ARGoalKeys, _goalID);

            if (goalKeysUnion.Count<DSGoalKey>() == 0)
                continue;
            
            bool allCheckboxes = goalKeysUnion.All(goalKey => goalKey.getActivityRecordKey.getMaxCount == "1");
            bool useRadioButtons = keyType.getCode.Equals("ASSESSMENT_KEYS") && allCheckboxes && _allowModify && 
                                   Privilege.isPrivTrue(iAccess, Privileges15.SELECT_SINGLE_ASSESSMENT_KEY_GROUP_AR);
            RadioButtonList radioButtonList = new RadioButtonList();
            radioButtonList.CssClass = "radio-columns";

            LiteralControl styleDiv = new LiteralControl();
            styleDiv.Text =
                "<div style='width:100%;background:" + (useRadioButtons ? "#EEEEEE" : "#DDDDDD" ) + ";margin-top:0px;padding-bottom:0px;display:block;clear:both;'><div style='background:#999999;color:#EEEEEE;margin-right:0px;'>&nbsp;&nbsp;" +
                keyType.getDescription.ToUpper() + "</div>";
            _parentDIV.Controls.Add(styleDiv);
            bool zebra = true;

            foreach (DSGoalKey goalKey in goalKeysUnion)
            {
                LiteralControl styleDiv2 = new LiteralControl();
                String background = "";
                int keyValue = 0;

                if (HiddenACTION.Value == Constants.MODIFY.ToString())
                {
                    foreach (DSActivityRecordGroupGoalKey ARGoalKey in ARGoalKeys)
                    {
                        if (ARGoalKey.getActivityRecordsKeyID == goalKey.getActivityRecordKeyID)
                            keyValue = ARGoalKey.getValue.Length > 0 ? Int32.Parse(ARGoalKey.getValue) : 0;
                    }
                }

                if (_allowModify)
                {
                    if (Int32.Parse(goalKey.getActivityRecordKey.getMaxCount) > 1) // numeric textbox
                    {
                        if (zebra)
                        {
                            background = "#EEEEEE";
                            styleDiv2.Text = "<div style='background:" + background + ";float:left;padding:5px 5px 5px 5px;height:18px;vertical-align:middle;min-width:fit-content;'>";
                            zebra = false;
                        }
                        else
                        {
                            background = "#FFFFFF";
                            styleDiv2.Text = "<div style='background:" + background + ";float:left;padding:5px 5px 5px 5px;height:18px;vertical-align:middle;min-width:fit-content;'>";
                            zebra = true;
                        }

                        _parentDIV.Controls.Add(styleDiv2);

                        RadNumericTextBox radNumericTextBox = new RadNumericTextBox();
                        radNumericTextBox.Label = goalKey.getActivityRecordKey.getAbbreviation + ":";
                        radNumericTextBox.ToolTip = goalKey.getActivityRecordKey.getDescription;
                        radNumericTextBox.Font.Size = 9;
                        radNumericTextBox.AllowOutOfRangeAutoCorrect = false;
                        radNumericTextBox.MaxValue = Int32.Parse(goalKey.getActivityRecordKey.getMaxCount);
                        radNumericTextBox.MaxLength = 3;
                        radNumericTextBox.MinValue = 0;
                        radNumericTextBox.NumberFormat.DecimalDigits = 0;
                        radNumericTextBox.EmptyMessage = "0";
                        radNumericTextBox.ShowSpinButtons = true;
                        radNumericTextBox.ButtonsPosition = InputButtonsPosition.Right;
                        radNumericTextBox.Type = NumericType.Number;
                        radNumericTextBox.Width = 80;
                        radNumericTextBox.BorderStyle = BorderStyle.None;
                        radNumericTextBox.EnableViewState = false;
                        radNumericTextBox.Text = "0";
                        radNumericTextBox.CssClass = "ARKeyNumericTextBox";
                        radNumericTextBox.Style.Add("background", background);
                        radNumericTextBox.Style.Add("padding-bottom", "5px");
                        radNumericTextBox.ID = "TextBoxKey-" + _goalID + "-" + goalKey.getActivityRecordKeyID;
                        radNumericTextBox.Value = keyValue;

                        _parentDIV.Controls.Add(radNumericTextBox);
                    }
                    else if (goalKey.getActivityRecordKey.getMaxCount == "1") // radiobutton/checkbox
                    {
                        if (useRadioButtons)
                        {
                            ListItem listItem = new ListItem();
                            listItem.Text = "&nbsp;" + goalKey.getActivityRecordKey.getAbbreviation;
                            listItem.Value = goalKey.getActivityRecordKeyID.ToString();
                            listItem.Attributes["Class"] = "ARKeyCheckbox";
                            listItem.Attributes["Title"] = goalKey.getActivityRecordKey.getDescription;
                            listItem.Selected = keyValue == 1;

                            if (goalKey.getActivityRecordKey.FormID != 0)
                            {
                                Form form = BOForms.getFormByClientID(goalKey.getActivityRecordKey.FormID, getClientID(), false);
                                string entityID;
                                if (form.getEntityType.Equals("CONSUMER"))
                                    entityID = _consumerID.ToString();
                                else //ACTIVITY_RECORD
                                    entityID = HiddenACTIVITY_RECORD_GROUP_ID.Value;

                                string clientID = getClientID().ToString();
                                listItem.Attributes.Add("onClick", "launchKeyFormFromRadioButton(this,'" + _consumerID.ToString() + "', '" 
                                                                   + HiddenGROUP_DATE.Value + "', '" + clientID +"', '"
                                                                   + form.getFormID.ToString() + "', '" + form.getEntityType + "', '" + entityID + "');");
                            }

                            radioButtonList.Items.Add(listItem);
                        }
                        else
                        {
                            if (zebra)
                            {
                                background = "#EEEEEE"; //E0EFF1
                                styleDiv2.Text = "<div style='background:" + background + ";float:left;padding:8px 5px 5px 5px;height:17px;vertical-align:middle;'>";
                                zebra = false;
                            }
                            else
                            {
                                background = "#FFFFFF";
                                styleDiv2.Text = "<div style='background:" + background + ";float:left;padding:8px 5px 5px 5px;height:17px;vertical-align:middle;'>";
                                zebra = true;
                            }

                            _parentDIV.Controls.Add(styleDiv2);

                            CheckBox checkboxKey = new CheckBox();
                            checkboxKey.Font.Size = 9;
                            checkboxKey.Text = "&nbsp;" + goalKey.getActivityRecordKey.getAbbreviation;
                            checkboxKey.ToolTip = goalKey.getActivityRecordKey.getDescription;
                            checkboxKey.ID = "CheckBoxKey-" + _goalID + "-" + goalKey.getActivityRecordKeyID;
                            checkboxKey.ClientIDMode = ClientIDMode.Static;
                            checkboxKey.Style.Add("margin-top", "10px");
                            checkboxKey.CssClass = "ARKeyCheckbox";
                            checkboxKey.EnableViewState = false;
                            checkboxKey.Checked = keyValue == 1;
                            
                            if (goalKey.getActivityRecordKey.FormID != 0)
                            {
                                Form form = BOForms.getFormByClientID(goalKey.getActivityRecordKey.FormID, getClientID(), false);
                                string entityID;
                                if (form.getEntityType.Equals("CONSUMER"))
                                    entityID = _consumerID.ToString();
                                else //ACTIVITY_RECORD
                                    entityID = HiddenACTIVITY_RECORD_GROUP_ID.Value;
                                
                                
                                string clientID = getClientID().ToString();
                                checkboxKey.Attributes.Add("onClick", "launchKeyForm('" + _consumerID.ToString() + "', '" + HiddenGROUP_DATE.Value + "', '"
                                                                      + checkboxKey.ID + "', '" + clientID +"', '"
                                                                      + form.getFormID.ToString() + "', '" + form.getEntityType + "', '" + entityID + "');");
                            }

                            _parentDIV.Controls.Add(checkboxKey);
                        }
                    } // end building numeric textbox/radiobutton/checkbox
                }
                else // Don't allow modify
                {
                    if (zebra)
                    {
                        background = "#EEEEEE";
                        styleDiv2.Text = "<div style='background:" + background + ";float:left;padding:5px 5px 0px 5px;height:20px;vertical-align:middle;'>";
                        zebra = false;
                    }
                    else
                    {
                        background = "#FFFFFF";
                        styleDiv2.Text = "<div style='background:" + background + ";float:left;padding:5px 5px 0px 5px;height:20px;vertical-align:middle;'>";
                        zebra = true;
                    }

                    _parentDIV.Controls.Add(styleDiv2);

                    Label valueControl = new Label();
                    valueControl.ID = "LabelKey-" + _goalID + "-" + goalKey.getActivityRecordKeyID;
                    String finalKeyValue = goalKey.getActivityRecordKey.getMaxCount == "1" ? keyValue == 1 ? "Yes" : "No" : keyValue.ToString();
                    valueControl.Text = goalKey.getActivityRecordKey.getAbbreviation + ": " + finalKeyValue;
                    valueControl.ToolTip = goalKey.getActivityRecordKey.getDescription;
                    _parentDIV.Controls.Add(valueControl);
                }

                if (useRadioButtons) continue;
                
                LiteralControl styleDiv3 = new LiteralControl();
                styleDiv3.Text = "</div>";
                _parentDIV.Controls.Add(styleDiv3);
            } // end foreach goalkey

            if (useRadioButtons)
            {
                _parentDIV.Controls.Add(radioButtonList);
            }
            else
            {
                LiteralControl styleDiv32 = new LiteralControl();
                styleDiv32.Text = "<div style='background:#EEEEEE;width:100%;height:22px;'>&nbsp;&nbsp;</div>";
                _parentDIV.Controls.Add(styleDiv32); 
            }

            styleDiv = new LiteralControl();
            styleDiv.Text = "</div>";
            _parentDIV.Controls.Add(styleDiv);
        }
    }

    private RadTextBox createCommentTextBox(int _goalID)
    {
        RadTextBox consumerCommentTextBox = createTextBox("textbox-comment-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString());
        return consumerCommentTextBox;
    }

    private LinkButton createAutoReviewButton(int _goalID)
    {
        LinkButton autoReviewButton = new LinkButton();
        autoReviewButton.ID = "auto-review-button-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID;
        autoReviewButton.Text = "Auto-review";
        autoReviewButton.CssClass = "auto-review-button";
        autoReviewButton.Attributes[USER_COMMENT_ID] = HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value;
        autoReviewButton.Attributes[COMMENT_TYPE] = HiddenACTIVITY_RECORD_COMMENT_TYPE.Value;
        autoReviewButton.Attributes[GOAL_ID] = _goalID.ToString();
        autoReviewButton.Click += AutoReviewButton_Click;
        autoReviewButton.CausesValidation = false;
        return autoReviewButton;
    }

    private LinkButton createSaveButton(int _goalID)
    {
        LinkButton saveButton = new LinkButton();
        saveButton.ID = "consumer-goal-save-button-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString();
        saveButton.Text = "save comment";
        saveButton.CssClass = "edit-button";
        saveButton.Style.Add("padding-left", "5px");
        saveButton.Attributes[USER_COMMENT_ID] = HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value;
        saveButton.Attributes[CONSUMER_ID] = HiddenCONSUMER_ID.Value;
        saveButton.Attributes[COMMENT_TYPE] = HiddenACTIVITY_RECORD_COMMENT_TYPE.Value;
        saveButton.Attributes[GOAL_ID] = _goalID.ToString();
        saveButton.Attributes["draggable"] = "false";
        saveButton.Click += SaveButton_Click;
        saveButton.OnClientClick = "triggerAjaxOnParent()";

        ajaxify(saveButton.ID, DIVUserComment.ID);

        return saveButton;
    }

    private LinkButton createEditButton()
    {
        LinkButton editButton = new LinkButton();
        editButton.ID = "consumer-staff-comment-button-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value;
        editButton.Text = "edit comment";
        editButton.CssClass = "edit-button";
        editButton.Attributes[USER_COMMENT_ID] = HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value;
        editButton.Attributes[COMMENT_TYPE] = HiddenACTIVITY_RECORD_COMMENT_TYPE.Value;
        editButton.Attributes["draggable"] = "false";
        //editButton.Attributes[PARENT_CONTAINER] = goalPanel.ID;
        //editButton.Attributes["savebuttonid"] = saveButton.ID;
        //editButton.Click += ExistingCommentEditButton_Click;
        editButton.OnClientClick = "EditButton_Clicked(\"" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "\");";

        return editButton;
    }

    private LinkButton createCancelButton(int _goalID)
    {
        LinkButton cancelButton = new LinkButton();
        cancelButton.ID = "consumer-goal-cancel-button-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString();
        cancelButton.Text = "cancel";
        cancelButton.CssClass = "edit-button";
        cancelButton.Attributes[USER_COMMENT_ID] = HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value;
        cancelButton.Attributes[CONSUMER_ID] = HiddenCONSUMER_ID.Value;
        cancelButton.Attributes[COMMENT_TYPE] = HiddenACTIVITY_RECORD_COMMENT_TYPE.Value;
        cancelButton.Attributes[GOAL_ID] = _goalID.ToString();
        cancelButton.Attributes["draggable"] = "false";
        cancelButton.OnClientClick = "cancelComment();";

        ajaxify(cancelButton.ID, DIVUserComment.ID);
        ajaxify(cancelButton.ID, HiddenEDIT_USER_COMMENT_ID.ID);

        return cancelButton;
    }
    private LinkButton createCancelIcon(string clientID)
    {
        LinkButton cancelIcon = new LinkButton();
        cancelIcon.ID = $"auto-review-cancel-icon-{clientID}";
        cancelIcon.Text = "✖"; 
        cancelIcon.CssClass = "cancel-icon"; 
        cancelIcon.CommandArgument = clientID;
        cancelIcon.Attributes[USER_COMMENT_ID] = HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value;
        cancelIcon.Attributes[COMMENT_TYPE] = HiddenACTIVITY_RECORD_COMMENT_TYPE.Value;
        cancelIcon.Attributes[GOAL_ID] = HiddenGOAL_ID.Value;
        cancelIcon.Style.Add("color", "#fb4040"); 
        cancelIcon.Style.Add("font-size", "15px");
        cancelIcon.Style.Add("text-decoration", "none"); 
        cancelIcon.Style.Add("border", "none"); 
        cancelIcon.Style.Add("background", "none"); 
        cancelIcon.Style.Add("cursor", "pointer"); 
        cancelIcon.Style.Add("outline", "none"); 
        cancelIcon.Click += CancelIcon_Click;
        cancelIcon.OnClientClick = $"removeAutoReview('{clientID}')";
        return cancelIcon;
    }
    
    protected void CancelIcon_Click(object sender, EventArgs e)
    {
        LinkButton cancelIcon = (LinkButton)sender;
        
        var userCommentID = cancelIcon.Attributes[USER_COMMENT_ID];
        var commentType = cancelIcon.Attributes[COMMENT_TYPE];
        var goalID = commentType == ActivityRecordCommentType.GOAL.ToString() ? Int32.Parse(cancelIcon.Attributes[GOAL_ID]) : 0;
        var autoReviewID = $"auto-review-container-{userCommentID}-{goalID}";
        var autoReviewContainer = (HtmlGenericControl)getControl(DIVUserComment, autoReviewID);
        var autoReviewFooterLabel = (Label)getControl(autoReviewContainer,$"auto-review-footer-label-{userCommentID}-{goalID}");
        
        if (autoReviewContainer != null)
        {
            autoReviewContainer.Style["display"] = "none";
        }

        if (autoReviewFooterLabel != null)
        {
            autoReviewFooterLabel.Style["display"] = "none";
        }
    }

    private HtmlGenericControl createAutoReviewContainer(string userCommentID, int goalID)
    {
        HtmlGenericControl autoReviewContainer = new HtmlGenericControl("div")
        {
            ID = $"auto-review-container-{userCommentID}-{goalID}"
        };
        autoReviewContainer.Attributes["class"] = "auto-review-container";
        autoReviewContainer.Attributes["review-container-id"] = autoReviewContainer.ID;
        autoReviewContainer.Attributes["comment-iframe-id"] = userCommentID;
        return autoReviewContainer;
    }
    private RadLabel CreateAutoReviewRadLabel(string autoReviewID)
    {
        RadLabel autoReviewLabel = new RadLabel
        {
            ID = autoReviewID,
            Text = "", 
            EnableViewState = true
        };
        
        autoReviewLabel.CssClass = "auto-review-textbox";
        autoReviewLabel.Style["height"] = "300px";
        autoReviewLabel.Style["overflow-y"] = "auto";
        autoReviewLabel.Style["resize"] = "none";
        return autoReviewLabel;
    }

    private Label createAutoReviewHeaderLabel(string userCommentID, int goalID)
    {
        Label autoReviewLabel = new Label
        {
            ID = $"auto-review-header-label-{userCommentID}-{goalID}",
            Text = "AI Assistant Review: Use this to improve your comment."
        };
        autoReviewLabel.Attributes["class"] = "auto-review-header-label";
        return autoReviewLabel;
    }
    
    private Label createAutoReviewFooterLabel(string userCommentID, int goalID)
    {
        Label autoReviewLabel = new Label
        {
            ID = $"auto-review-footer-label-{userCommentID}-{goalID}",
            Text = "The AI Assistant may make mistakes. Please verify your work."
        };
        autoReviewLabel.Attributes["class"] = "auto-review-footer-label";
        autoReviewLabel.Attributes["originalID"] = $"auto-review-footer-label-{userCommentID}-{goalID}"; 
        return autoReviewLabel;
    }
    
    private void ajaxify(String _sourceControlID, params String[] _toAjaxifyControlIDs)
    {
        AjaxSetting setting = new AjaxSetting(_sourceControlID);

        foreach (String toAjaxifyControlID in _toAjaxifyControlIDs)
        {
            AjaxUpdatedControl ajaxifyControl = new AjaxUpdatedControl(toAjaxifyControlID, "RadAjaxLoadingPanel222");
            ajaxifyControl.UpdatePanelRenderMode = UpdatePanelRenderMode.Inline;
            setting.UpdatedControls.Add(ajaxifyControl);
        }

        RadAjaxManagerARProxy.AjaxSettings.Add(setting);
    }

    protected void SaveButton_Click(object sender, EventArgs e)
    {
        long[] iAccess = getUserPrivs();
        HiddenEDIT_USER_COMMENT_ID.Value = "0";
        HtmlGenericControl parentDIV = (HtmlGenericControl) getControl(DIVUserComment, ((LinkButton) sender).Attributes[PARENT_CONTAINER]);
        String userCommentID = ((LinkButton) sender).Attributes[USER_COMMENT_ID];
        String commentType = ((LinkButton) sender).Attributes[COMMENT_TYPE];
        int goalID = commentType == ActivityRecordCommentType.GOAL.ToString() ? Int32.Parse(((LinkButton) sender).Attributes[GOAL_ID]) : 0;
        DSGoal goal = BOGoal.getGoalByGoalID(getClientID(), goalID);
        String comment = "";

        if (goal.getIncludeCommentForAR || commentType != ActivityRecordCommentType.GOAL.ToString())
        {
            comment = getUserComment(sender, parentDIV, userCommentID, goalID.ToString());
        }

        if (HiddenACTION.Value == Constants.CREATE)
        {
            int newUserCommentID = 0;

            if (commentType == ActivityRecordCommentType.CONSUMER.ToString())
            {
                int consumerID = Int32.Parse(((LinkButton) sender).Attributes[CONSUMER_ID]);
                DSActivityRecordGroupUserComment userComment = new DSActivityRecordGroupUserComment(Guid.Parse(getUserID()), Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value),
                    BOActivityRecordGroup.getActivityRecordCommentTypeIDByType(getClientID(), ActivityRecordCommentType.CONSUMER), consumerID, 0, 0, comment);
                newUserCommentID = BOActivityRecordGroup.createActivityRecordUserComment(getClientID(), userComment, getUsername());
                HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value = newUserCommentID.ToString();
            }
            else if (commentType == ActivityRecordCommentType.GOAL.ToString())
            {
                int consumerID = Int32.Parse(((LinkButton) sender).Attributes[CONSUMER_ID]);
                DSActivityRecordGroupUserComment userComment = new DSActivityRecordGroupUserComment(Guid.Parse(getUserID()), Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value),
                    BOActivityRecordGroup.getActivityRecordCommentTypeIDByType(getClientID(), ActivityRecordCommentType.GOAL), consumerID, goalID, 0, comment);
                newUserCommentID = BOActivityRecordGroup.createActivityRecordUserComment(getClientID(), userComment, getUsername());
                saveActivityRecordGoalKeys(iAccess, consumerID, goalID, newUserCommentID, getUserID(), Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value));
                HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value = newUserCommentID.ToString();

                //BOActivityRecordGroup.createActivityRecordGroupGoalKey(getClientID()
            }
            else if (commentType == ActivityRecordCommentType.OVERALL.ToString())
            {
                DSActivityRecordGroupUserComment userComment = new DSActivityRecordGroupUserComment(Guid.Parse(getUserID()), Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value),
                    BOActivityRecordGroup.getActivityRecordCommentTypeIDByType(getClientID(), ActivityRecordCommentType.OVERALL), comment);
                newUserCommentID = BOActivityRecordGroup.createActivityRecordUserComment(getClientID(), userComment, getUsername());
                HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value = newUserCommentID.ToString();
            }

            RadAjaxManagerARProxy.ResponseScripts.Add("newCommentSaved(\"" + newUserCommentID.ToString() + "\");");
        }
        else if (HiddenACTION.Value == Constants.MODIFY)
        {
            int consumerID = Int32.Parse(((LinkButton) sender).Attributes[CONSUMER_ID]);

            DSActivityRecordGroupUserComment userComment = new DSActivityRecordGroupUserComment(Int32.Parse(userCommentID), Guid.Parse(getUserID()),
                Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value), BOActivityRecordGroup.getActivityRecordCommentTypeIDByType(getClientID(), commentType), consumerID, goalID, 0,
                comment);

            BOActivityRecordGroup.modifyActivityRecordUserComment(getClientID(), userComment, getUsername());

            if (commentType == ActivityRecordCommentType.GOAL.ToString())
                saveActivityRecordGoalKeys(iAccess, consumerID, goalID, userComment.getActivityRecordsGroupUserCommentID, userComment.getUserID.ToString(),
                    userComment.getActivityRecordsGroupID);

            drawUserComments();
            RadAjaxManagerARProxy.ResponseScripts.Add("existingCommentSaved();");
        }

        ActivityRecordsMisc.updateSignaturesNew(getClientID(), getUserID(), SignatureType.ACTIVITY_RECORD_GROUP, Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value),
            DSActivityRecordType.ACTIVITYRECORD_BILLABLE_GROUP.ToString());
    }

    protected void AutoReviewButton_Click(object sender, EventArgs e)
    {
        HiddenShowAIAssistantTextBox.Value = "True";
        
        BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "AI_ASSISTANT_ACTIVITY_RECORDS_GROUP_AUTO_REVIEW_START", HiddenACTIVITY_RECORD_GROUP_ID.Value, getUsername(), "No Session");

        var autoReviewButton = (LinkButton) sender;
        if (autoReviewButton == null)
            return;
        
        var userCommentID = autoReviewButton.Attributes[USER_COMMENT_ID];
        var commentType = autoReviewButton.Attributes[COMMENT_TYPE];
        var goalID = commentType == ActivityRecordCommentType.GOAL.ToString() ? Int32.Parse(autoReviewButton.Attributes[GOAL_ID]) : 0;

        var autoReviewID = $"auto-review-container-{userCommentID}-{goalID}";
        var autoReviewContainer = (HtmlGenericControl)getControl(DIVUserComment, autoReviewID);
        if (autoReviewContainer == null)
            return;

        var autoReviewTextBoxID = $"auto-review-textbox-{userCommentID}-{goalID}";
        var autoReviewTextBox = (RadLabel)getControl(autoReviewContainer, autoReviewTextBoxID);
        if (autoReviewTextBox == null)
            return;

        var parentControl = autoReviewButton.Parent;
        var targetParentControl = commentType == ActivityRecordCommentType.GOAL.ToString() ? "DIVUserComment" : "DIVUserComment";
        while (parentControl != null && !(parentControl is HtmlGenericControl && ((HtmlGenericControl)parentControl).ID.StartsWith(targetParentControl)))
        {
            parentControl = parentControl.Parent;
        }
        
        var autoReviewFooter = (Label)getControl(autoReviewContainer, $"auto-review-footer-label-{userCommentID}-{goalID}");
        autoReviewFooter.Attributes.Add("style", "max-width:546px;display:block;padding-left:5px;position:relative;margin-bottom:5px;");
        HiddenShowAIAssistantTextBox.Value = "True";
        autoReviewContainer.Style["display"] = "flex !important";
        autoReviewContainer.Style["width"] = "100% !important";
        autoReviewTextBox.Style["width"] = "100%";
        
        var consumerId = 0;
        var consumerName = "";
        var activityRecordsGroupId = 0;
        var activityRecordsGroupUserComment_ID = 0;
        var goalCommentId = 0;
        var activityRecordsId = 0;

        if (parentControl != null && parentControl is HtmlGenericControl)
        {
            foreach (Control control in parentControl.Controls)
            {
                if (control.ID == $"comment-panel-{userCommentID}-{goalID}")
                {
                    HtmlGenericControl parentDIV = (HtmlGenericControl)control;
                    var comment = getCommentsForAutoReview(sender, commentType, parentDIV, userCommentID, goalID.ToString());
                    
                    if (!string.IsNullOrEmpty(comment))
                    {
                        try
                        {
                            _aiSystemConfiguration.ClientID = getClientID();
                            _aiSystemConfiguration.ServiceID = string.IsNullOrEmpty(HiddenSERVICE_ID.Value) ? 0 : Convert.ToInt32(HiddenSERVICE_ID.Value);

                            if (commentType == ActivityRecordCommentType.GOAL.ToString())
                            {
                                consumerId = !string.IsNullOrEmpty(HiddenACTIVITY_RECORD_GROUP_ID.Value) ? int.Parse(HiddenCONSUMER_ID.Value) : 0;
                                consumerName = BOConsumer.getConsumerNameById(getClientID(), consumerId, false, false, true);
                                goalCommentId = !string.IsNullOrEmpty(HiddenACTIVITY_RECORD_GROUP_ID.Value) ? int.Parse(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value) : 0;
                                activityRecordsGroupId = !string.IsNullOrEmpty(HiddenACTIVITY_RECORD_GROUP_ID.Value) ? int.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value) : 0;
                                    
                                var iAccess = getUserPrivs();
                                
                                var goalKeysAndValues = GetActivityRecordGoalInterventionAndAssessmentKeyValues(iAccess, _aiSystemConfiguration.ClientID, consumerId, goalID, true);
                                
                                DSGoal goal = BOGoal.getGoalByGoalID(getClientID(), goalID);
                                var selectedGoal = goal.getShortDescription;

                                if (!String.IsNullOrEmpty(selectedGoal) && !string.IsNullOrEmpty(goal.getLongDescription))
                                {
                                    selectedGoal += ". " + goal.getLongDescription;
                                }
                                else if (string.IsNullOrEmpty(selectedGoal) && string.IsNullOrEmpty(goal.getLongDescription))
                                {
                                    selectedGoal = goal.getLongDescription;
                                }
                                
                                _aiSystemConfiguration.UserMessageContext =
                                    comment + $". The name of the individual receiving the service is {consumerName}. " + 
                                    "This is the particular targeted " +
                                    $"outcomes-associated goal on which the" +
                                    $" service provider is commenting: \"{selectedGoal}. " +
                                    "An 'outcome' is a term that signifies a focused area of progress for the person " +
                                    "receiving services to achieve. A 'goal' is a term that signifies a" +
                                    " small achievable step toward the desired 'outcome'.";

                                if (goalKeysAndValues.Count > 0)
                                {
                                    _aiSystemConfiguration.UserMessageContext += "Additionally, the service provider used these" +
                                        " Intervention and Assessment Keys as part of their goal documentation. " +
                                        "Intervention Keys is a term used to signify " +
                                        "a meaningful interaction by the service provider while working toward a " +
                                        "particular goal. Assessment Keys is a term that signifies areas of " +
                                        "interest for purpose of tracking progress while " +
                                        "working toward as particular goal." +
                                        "Evaluate the comment to determine if it contains any relevant information " +
                                        "regarding the use of the intervention and assessment keys." +
                                        "These are the intervention and assessment keys provided in a list style " +
                                        "json format: [ ";
                                    foreach (GoalKeyValue goalKeyAndValue in goalKeysAndValues)
                                    {
                                        _aiSystemConfiguration.UserMessageContext += "{" + $"KeyType:{goalKeyAndValue.KeyType}, KeyDescription:{goalKeyAndValue.KeyDescription}, KeyValue:{goalKeyAndValue.KeyValue}" + "},";
                                    }

                                    _aiSystemConfiguration.UserMessageContext += "]";
                                }

                                _aiSystemConfiguration.Entity = SetWorks.Common.Tools.SETWorksAI.EntityType.ACTIVITYRECORDS_GROUP_GOAL_COMMENTS;
                            }
                            else if (commentType == ActivityRecordCommentType.OVERALL.ToString())
                            {
                                activityRecordsGroupId = !string.IsNullOrEmpty(HiddenACTIVITY_RECORD_GROUP_ID.Value) ? int.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value) : 0;
                                activityRecordsGroupUserComment_ID = !string.IsNullOrEmpty(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value) ? int.Parse(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value) : 0;
                                _aiSystemConfiguration.UserMessageContext = comment;
                                _aiSystemConfiguration.Entity = SetWorks.Common.Tools.SETWorksAI.EntityType.ACTIVITYRECORDS_GROUP_OVERALL_COMMENTS;
                                _aiSystemConfiguration.ServiceID = 0;
                            }
                            else if (commentType == ActivityRecordCommentType.CONSUMER.ToString())
                            {
                                consumerId = !string.IsNullOrEmpty(HiddenCONSUMER_ID.Value) ? int.Parse(HiddenCONSUMER_ID.Value) : 0;
                                consumerName = BOConsumer.getConsumerNameById(getClientID(), consumerId, false, false, true);
                                activityRecordsGroupId = !string.IsNullOrEmpty(HiddenACTIVITY_RECORD_GROUP_ID.Value) ? int.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value) : 0;
                                activityRecordsGroupUserComment_ID = !string.IsNullOrEmpty(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value) ? int.Parse(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value) : 0;
                                _aiSystemConfiguration.UserMessageContext = comment + $". The name of the individual receiving the service is {consumerName}. ";
                                _aiSystemConfiguration.Entity = SetWorks.Common.Tools.SETWorksAI.EntityType.ACTIVITYRECORDS_GROUP_CONSUMER_COMMENTS;  
                            }

                            _aAiConfiguration.SystemConfiguration = _aiSystemConfiguration;
                            _aiClient.Config = _aAiConfiguration;
                            _aiClient.MakeChatCompletionRequest();
                            autoReviewTextBox.Text = _aiClient.GetChatResponseHtml();
                            
                            if (activityRecordsGroupId != 0)
                            {
                                BOAIAssistantActivityRecordChatHistory.addOrUpdateAIAssistantActivityRecordChatHistory(
                                    _aiSystemConfiguration.ClientID,
                                    Guid.Parse(getUserID()),
                                    _aiSystemConfiguration.Entity.ToString(),
                                    _aiSystemConfiguration.getAIAssistantSystemPrompt(),
                                    _aiSystemConfiguration.getAIAssistantUserPrompt(),
                                    _aiClient.GetChatResponsePlainText(),
                                    consumerId,
                                    activityRecordsGroupId,
                                    activityRecordsGroupUserComment_ID,
                                    activityRecordsId,
                                    goalID,
                                    goalCommentId,
                                    _aiSystemConfiguration.ServiceID);
                            }
                        }
                        catch (Exception ex)
                        {
                            var errorDashboardContext = $"AI Assistant Activity Records Group Error: " + ex.Message;
                            BOError.setError(getClientID(), getUserID().ToString(), "AI_ASSISTANT_ACTIVITY_RECORDS_GROUP_AUTO_REVIEW_ERROR", ex.StackTrace, "No Session", $"Activity Record Group: {activityRecordsGroupId}, Consumer: {consumerId}", errorDashboardContext, ex.GetType().Name);
                            autoReviewTextBox.Text =  "AI Assistant encountered an issue while reviewing this comment.";
                        }
                    }
                    else
                    {
                        autoReviewTextBox.Text =  "There is no comment for the AI Assistant to review.";                  
                    }
                    
                    break;
                }
            }
        }

        BOAudit.setAudit(getClientID(), getUserID(), HttpContext.Current.Request.Url.AbsoluteUri, "AI_ASSISTANT_ACTIVITY_RECORDS_GROUP_AUTO_REVIEW_END", HiddenACTIVITY_RECORD_GROUP_ID.Value, getUsername(), "No Session");
    }
    
    private void CreateAutoReviewControls(ActivityRecordCommentType _commentType, int _goalID, Control headerButtonPanel, Control parentControl)
    {
        LinkButton autoReviewButton = createAutoReviewButton(_goalID);
        autoReviewButton.EnableViewState = false;
        autoReviewButton.Attributes[PARENT_CONTAINER] = parentControl.ID;
        autoReviewButton.Attributes[COMMENT_TYPE] = _commentType.ToString();
        headerButtonPanel.Controls.Add(autoReviewButton);
                
        var autoReviewContainer = createAutoReviewContainer(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value, _goalID);        
        var cancelIcon = createCancelIcon($"{HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value}-{_goalID}");
        autoReviewContainer.Controls.Add(cancelIcon);
        var aiReviewHeaderLabel = createAutoReviewHeaderLabel(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value, _goalID);
        autoReviewContainer.Controls.Add(aiReviewHeaderLabel);
                
        var autoReviewTextBoxID = $"auto-review-textbox-{HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value}-{_goalID}";
        var autoReviewTextBox = CreateAutoReviewRadLabel(autoReviewTextBoxID);
        autoReviewContainer.Controls.Add(autoReviewTextBox);
                
        var aiReviewFooterLabel = createAutoReviewFooterLabel(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value, _goalID);
        autoReviewContainer.Controls.Add(aiReviewFooterLabel);
        
        autoReviewContainer.Style["display"] = "none";
        
        parentControl.Controls.Add(autoReviewContainer);
        
        ajaxify(autoReviewButton.ID, parentControl.ID);
        ajaxify(autoReviewButton.ID, HiddenShowAIAssistantTextBox.ID);
        ajaxify(autoReviewButton.ID, autoReviewTextBox.ID);
        ajaxify(autoReviewButton.ID, autoReviewContainer.ID);
        ajaxify(cancelIcon.ID, parentControl.ID);
        ajaxify(cancelIcon.ID, autoReviewTextBox.ID);
        ajaxify(cancelIcon.ID, autoReviewContainer.ID);
        ajaxify(autoReviewContainer.ID, parentControl.ID);
    }

    private string getCommentsForAutoReview(object sender, string commentType, HtmlGenericControl parentDIV, string userCommentID, string goalID)
    {
        var comment = getUserComment(sender, parentDIV, userCommentID, goalID);
        if(string.IsNullOrEmpty(comment))
        {
            comment = getUserCommentFromLabel(commentType, parentDIV, userCommentID, goalID.ToString());
        }

        return comment;
    }

    private void saveActivityRecordGoalKeys(long[] iAccess, int _consumerID, int _goalID, int _userCommentID, String _userID, int _activityRecordGroupID)
    {
        DSGoalKey[] goalKeys = BOGoal.getGoalKeysByGoalID(getClientID(), _consumerID, _goalID);
        DSActivityRecordGroupGoalKey[] ARGoalKeys = new DSActivityRecordGroupGoalKey[0];

        if (HiddenACTION.Value == Constants.MODIFY)
            ARGoalKeys = BOActivityRecordGroup.getActivityRecordGroupGoalKeysByConsumerIDAndUserCommentID(getClientID(), Int32.Parse(HiddenACTIVITY_RECORD_GROUP_ID.Value),
                _consumerID, Int32.Parse(HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value));
        
        string assesmentKeyCodeValueID = BOCodeValue.getCodeValueByCode("ACTIVITY_RECORD_KEY_TYPE", "ASSESSMENT_KEYS", getClientID().ToString()).getCodeValueID ?? "0";
        bool useRadioButtonListPriv = Privilege.isPrivTrue(iAccess, Privileges15.SELECT_SINGLE_ASSESSMENT_KEY_GROUP_AR);

        foreach (DSGoalKey goalKey in goalKeys)
        {
            bool valueInRadioButtonList = false;
            if (useRadioButtonListPriv && goalKey.getActivityRecordKey.getMaxCount.Equals("1") && 
                goalKey.getActivityRecordKey.getCVKeyTypeID.ToString().Equals(assesmentKeyCodeValueID)) {
                    valueInRadioButtonList = goalKeys.Where(gk =>
                        gk.getActivityRecordKey.getCVKeyTypeID == goalKey.getActivityRecordKey.getCVKeyTypeID).All(
                            gk => gk.getActivityRecordKey.getMaxCount == "1");
            }
            String keyValue = getConsumerGoalKey(valueInRadioButtonList, _consumerID.ToString(), _goalID.ToString(), goalKey.getActivityRecordKeyID);

            if (HiddenACTION.Value == Constants.CREATE.ToString())
            {
                DSActivityRecordGroupGoalKey populatedKey = new DSActivityRecordGroupGoalKey(_consumerID, _goalID, _userID, _userCommentID, _activityRecordGroupID,
                    goalKey.getActivityRecordKeyID, keyValue);
                BOActivityRecordGroup.createActivityRecordGroupGoalKey(getClientID(), populatedKey, HiddenCURRENTWINDOWUSERID.Value);
            }
            else
            {
                int id = 0;

                foreach (DSActivityRecordGroupGoalKey ARGoalKey in ARGoalKeys)
                {
                    if (ARGoalKey.getActivityRecordsKeyID == goalKey.getActivityRecordKeyID)
                        id = ARGoalKey.getActivityRecordsGroupGoalKeyID;
                }

                DSActivityRecordGroupGoalKey populatedKey = new DSActivityRecordGroupGoalKey(id, _consumerID, _goalID, _userID, _userCommentID, _activityRecordGroupID,
                    goalKey.getActivityRecordKeyID, keyValue);
                if (id == 0)
                {
                    BOActivityRecordGroup.createActivityRecordGroupGoalKey(getClientID(), populatedKey, HiddenCURRENTWINDOWUSERID.Value);
                }
                else
                {
                    BOActivityRecordGroup.modifyActivityRecordGroupGoalKey(getClientID(), populatedKey, HiddenCURRENTWINDOWUSERID.Value);
                }
            }
        }
    }

    private String getConsumerGoalKey(bool valueInRadioButtonList, String _consumerID, String _goalID, int _activityRecordsKeyID)
    {
        HtmlGenericControl parent = (HtmlGenericControl) getControl(DIVUserComment, "header-panel-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString());

        if (valueInRadioButtonList)
        {
            ListItem thisRadioButton = parent.Controls.OfType<RadioButtonList>().FirstOrDefault()?.Items.OfType<ListItem>().FirstOrDefault(x => x.Value == _activityRecordsKeyID.ToString());
            return thisRadioButton == null ? "0" : thisRadioButton.Selected ? "1" : "0";
        }

        for (int x = 0; x < parent.Controls.Count; x++)
        {
            if (parent.Controls[x].ID == "CheckBoxKey-" + _goalID + "-" + _activityRecordsKeyID.ToString())
            {
                return ((CheckBox) parent.Controls[x]).Checked ? "1" : "0";
            }
            else if (parent.Controls[x].ID == "TextBoxKey-" + _goalID + "-" + _activityRecordsKeyID.ToString())
            {
                return ((RadNumericTextBox) parent.Controls[x]).Text;
            }
            else if (parent.Controls[x].ID == "LabelKey-" + _goalID + "-" + _activityRecordsKeyID.ToString())
            {
                return ((LiteralControl) parent.Controls[x]).Text;
            }
        }

        return "";
    }
    
    private Dictionary<string, bool> getConsumerGoalKeyValueAndIsSelected(bool valueInRadioButtonList, String _goalID, int _activityRecordsKeyID)
    {
        HtmlGenericControl parent = (HtmlGenericControl) getControl(DIVUserComment, "header-panel-" + HiddenACTIVITY_RECORD_GROUP_USER_COMMENT_ID.Value + "-" + _goalID.ToString());
        var valueAndKeySelected = new Dictionary<string, bool>();
        
        var keyValue = string.Empty;
        var keySelected = false;
        
        if (valueInRadioButtonList)
        {
            ListItem thisRadioButton = parent.Controls.OfType<RadioButtonList>().FirstOrDefault()?.Items.OfType<ListItem>().FirstOrDefault(x => x.Value == _activityRecordsKeyID.ToString());
            keyValue = thisRadioButton == null ? "0" : thisRadioButton.Selected ? "1" : "0";
            keySelected = keyValue.Equals("1");
            valueAndKeySelected.Add(keyValue, keySelected);
            return valueAndKeySelected;
        }

        for (int x = 0; x < parent.Controls.Count; x++)
        {
            if (parent.Controls[x].ID == "CheckBoxKey-" + _goalID + "-" + _activityRecordsKeyID.ToString())
            {
                keyValue = ((CheckBox)parent.Controls[x]).Checked ? "1" : "0";
                keySelected = keyValue.Equals("1");
                valueAndKeySelected.Add(keyValue, keySelected);
                return valueAndKeySelected;
            }
            else if (parent.Controls[x].ID == "TextBoxKey-" + _goalID + "-" + _activityRecordsKeyID.ToString())
            {
                keyValue = ((RadNumericTextBox)parent.Controls[x]).Text;
                keySelected = !string.IsNullOrEmpty(keyValue);
                valueAndKeySelected.Add(keyValue, keySelected);
                return valueAndKeySelected;
            }
            else if (parent.Controls[x].ID == "LabelKey-" + _goalID + "-" + _activityRecordsKeyID.ToString())
            {
                keyValue = ((Label) parent.Controls[x]).Text;
                keySelected = !string.IsNullOrEmpty(keyValue);
                valueAndKeySelected.Add(keyValue, keySelected);
                return valueAndKeySelected;
            }
        }

        return valueAndKeySelected;
    }

    private String getUserComment(object _sender, HtmlGenericControl _parentDIV, String _userCommentID, String _goalID)
    {
        String commentType = ((LinkButton) _sender).Attributes[COMMENT_TYPE];
        
        if (commentType == ActivityRecordCommentType.CONSUMER.ToString() || commentType == ActivityRecordCommentType.OVERALL.ToString() || commentType == ActivityRecordCommentType.GOAL.ToString())
        {
            RadTextBox commentTextbox = (RadTextBox) getControl(_parentDIV, "textbox-comment-" + _userCommentID + "-" + _goalID);
            return commentTextbox?.Text ?? string.Empty;
        }
        
        throw new Exception("getUserComment(): User Comment Type not found");
    }
    
    private String getUserCommentFromLabel(string commentType, HtmlGenericControl _parentDIV, String _userCommentID, String _goalID)
    {
        if (commentType == ActivityRecordCommentType.CONSUMER.ToString() || commentType == ActivityRecordCommentType.OVERALL.ToString() || commentType == ActivityRecordCommentType.GOAL.ToString())
        {
            Label commentTextbox = (Label) getControl(_parentDIV, $"consumer-comment-label-{_userCommentID}-{_goalID}");
            return commentTextbox?.Text ?? string.Empty;
        }
        
        throw new Exception("getUserComment(): User Comment Type not found");
    }

    private void pageLoadedPostProcessing(int textAreaHeight)
    {
        RadAjaxManagerARProxy.ResponseScripts.Add("pageLoaded(\"" + textAreaHeight + "\");");
    }

    #region Helper Methods
    
    private int GetHeaderIndex(HtmlGenericControl commentPanel)
    {
        for (int i = 0; i < commentPanel.Controls.Count; i++)
        {
            HtmlGenericControl header = commentPanel.Controls[i] as HtmlGenericControl;
            if (header != null && header.ID != null && header.ID.StartsWith("comment-header"))
            {
                return i;
            }
        }
        return 0;
    }

    private Control getControl(HtmlGenericControl _parentControl, String _IDOfControlToFind)
    {
        foreach (Control control in _parentControl.Controls)
        {
            if (control.ID == _IDOfControlToFind)
            {
                return control;
            }
        }

        return null;
    }

    private int getEntityID(ActivityRecordCommentType _commentType, int _consumerID, int _goalID)
    {
        int entityID = 0;

        switch (_commentType)
        {
            case ActivityRecordCommentType.CONSUMER:
                entityID = _consumerID;
                break;
            case ActivityRecordCommentType.GOAL:
                entityID = _goalID;
                break;
            case ActivityRecordCommentType.OVERALL:
                entityID = 0;
                break;
        }

        return entityID;
    }

    private LiteralControl createBottomMarginLarge()
    {
        LiteralControl control = new LiteralControl();
        control.Text = "<div style='height:20px;'></div>";

        return control;
    }

    private LiteralControl createBottomMarginSmall()
    {
        LiteralControl control = new LiteralControl();
        control.Text = "<div style='height:5px;'></div>";

        return control;
    }

    private Label createCommentLabel(String labelID)
    {
        Label label = new Label();
        label.ID = labelID;
        label.CssClass = "labelComment";

        return label;
    }

    private Label createCommentLabelValue(String labelID)
    {
        Label label = new Label();
        label.ID = labelID;
        label.CssClass = "labelCommentValue";

        return label;
    }

    private RadTextBox createTextBox(String textBoxID)
    {
        RadTextBox radTextBox = new RadTextBox();
        radTextBox.ID = textBoxID;
        radTextBox.TextMode = InputMode.MultiLine;
        radTextBox.EnableViewState = false;
        radTextBox.Width = new Unit("100%");
        radTextBox.Style.Add("max-width", "550px");
        radTextBox.Style.Add("min-width", "400px");

        return radTextBox;
    }

    private List<GoalKeyValue> GetActivityRecordGoalInterventionAndAssessmentKeyValues(long[] iAccess, int clientID, int _consumerID, int _goalID, bool _allowModify)
    {
        var selectedGoalKeysAndValues = new List<GoalKeyValue>();
        var keyTypes = BOActivityRecordKey.getActivityRecordKeyTypes(clientID);
        var useRadioButtonListPriv = Privilege.isPrivTrue(iAccess, Privileges15.SELECT_SINGLE_ASSESSMENT_KEY_GROUP_AR);
        var goalKeys = BOGoal.getGoalKeysByGoalID(getClientID(), _consumerID, _goalID).ToList();
        
        foreach (DSGoalKey goalKey in goalKeys)
        {
            var newGoalKeyValue = new GoalKeyValue();
            newGoalKeyValue.GoalID = _goalID;
            newGoalKeyValue.GoalKeyID = goalKey.getActivityRecordKeyID;
            newGoalKeyValue.KeyDescription = goalKey.getActivityRecordKey.getDescription ?? goalKey.getActivityRecordKey.getDescription;

            var keyType = keyTypes.FirstOrDefault(kt => kt.getCodeValueID == goalKey.getActivityRecordKey.getCVKeyTypeID.ToString());
            if(keyType == null)
                continue;
            
            newGoalKeyValue.KeyType = keyType.getDescription;
            
            var valueInRadioButtonList = false;
            if (useRadioButtonListPriv && goalKey.getActivityRecordKey.getMaxCount.Equals("1") && newGoalKeyValue.KeyType.Contains("Assessment")) 
            { 
                valueInRadioButtonList = goalKeys.Where(gk => gk.getActivityRecordKey.getCVKeyTypeID == goalKey.getActivityRecordKey.getCVKeyTypeID).All(gk => gk.getActivityRecordKey.getMaxCount == "1");
            }
            
            var keyValuesAndIsSelected = getConsumerGoalKeyValueAndIsSelected(valueInRadioButtonList, _goalID.ToString(), goalKey.getActivityRecordKeyID);

            foreach(var keyValueIsSelected in keyValuesAndIsSelected)
            {
                var keySelected = keyValueIsSelected.Value;
                // if there wasn't any input then ignore from AI Assistant instructions (may change later)
                if(!keySelected)
                    continue;
                
                newGoalKeyValue.KeyValue = keyValueIsSelected.Key;
                newGoalKeyValue.IsKeySelected = keySelected;
            }

            selectedGoalKeysAndValues.Add(newGoalKeyValue);
        }

        return selectedGoalKeysAndValues;
    }

    #endregion

    private class GoalKeyValue
    {
        public int GoalID { get; set; }
        public int GoalKeyID { get; set; }
        public string KeyType { get; set; }
        public string KeyDescription { get; set; }
        public string KeyValue { get; set; }
        public bool IsKeySelected { get; set; }
    }
}