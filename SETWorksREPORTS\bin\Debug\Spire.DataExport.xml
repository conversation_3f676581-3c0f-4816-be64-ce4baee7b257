<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Spire.DataExport</name>
    </assembly>
    <members>
        <member name="T:Spire.DataExport.Access.AccessExport">
            <summary>
            The AccessExport is the export component for data exporting into MS Access file format.
            You should have ADO.NET installed on your system.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Access.AccessExport.TableColumnCreating">
            <summary>
            Occurs before the table column is created.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Access.AccessExport.TableCreated">
            <summary>
            Occurs after the table is created.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Access.AccessExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.Access.AccessExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessExport.AlterTable(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Use the alter table statement to change the table
            </summary>
            <param name="sqls">The alter table statements</param>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.Password">
            <summary>
            The password string of the result MS Access file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.DatabaseName">
            <summary>
            The database name of the result MS Access file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.TableName">
            <summary>
            The table name of the result MS Access to export data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.CreateDatabase">
            <summary>
            The CreateDatabase property determinates whether creates the result MS Access database automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.CreateTable">
            <summary>
            The CreateTable property determinates whether creates the result MS Access table automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.Titles">
            <summary>
            Gets or sets the column titles in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.AddTitles">
            <summary>
            Gets of sets titles are added in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.DataFormats">
            <summary>
            Gets or sets data format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.CustomFormats">
            <summary>
            Gets or sets a special format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.AccessVersion">
            <summary>
            Gets or sets the access version.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.ConvertBinaryToHexString">
            <summary>
            always false, never convert binary data to hex string.
            if false or (DataSource is SqlCommand and column is BLOB), FillExportRow will set
            the corresponding ColExport.IsBinary = true and set the ColExport.DataSource
            to DataReader/DataRow/ListView.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessExport.ColumnsLength">
            <summary>
            Gets or sets the length of the exported columns.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Access.TableColumnCreatingEventHandler">
            <summary>
            Represents the method that handles a Spire.DataExport.Access.AccessExport.TableColumnCreating event.
            </summary>
            <param name="sender">The source of the event, it should be an Spire.DataExport.Access.AccessExport.</param>
            <param name="e">A Spire.DataExport.Access.TableColumnCreatingEventArgs that contains the event data.</param>
        </member>
        <member name="T:Spire.DataExport.Access.TableColumnCreatingEventArgs">
            <summary>
            Provides data for the Spire.DataExport.Access.AccessExport.TableColumnCreating event
            of a Spire.DataExport.Access.AccessExport.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumnCreatingEventArgs.ColumnName">
            <summary>
            Gets the name of the new column.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumnCreatingEventArgs.ColumnDataType">
            <summary>
            Gets or sets the data type of the new column.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumnCreatingEventArgs.ColumnPropertiesDDL">
            <summary>
            Gets or sets the ddl statement which represents the general properties of the new column,
            except the column name and data type.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumnCreatingEventArgs.DataSourceType">
            <summary>
            Gets the type of the data source.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumnCreatingEventArgs.DataSource">
            <summary>
            Gets the data source.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumnCreatingEventArgs.SchemaTable">
            <summary>
            Gets the schema table of the data source.
            If the data source is an sql command, returns the schema of the query result,
            otherwise, it must return null.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Access.TableCreatedEventHandler">
            <summary>
            Represents the method that handles a Spire.DataExport.Access.AccessExport.TableCreated event.
            </summary>
            <param name="sender">The source of the event, it should be an Spire.DataExport.Access.AccessExport.</param>
            <param name="e">A Spire.DataExport.Access.TableCreatedEventArgs that contains the event data.</param>
        </member>
        <member name="T:Spire.DataExport.Access.TableCreatedEventArgs">
            <summary>
            Provides data for the Spire.DataExport.Access.AccessExport.TableCreated event
            of a Spire.DataExport.Access.AccessExport.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableCreatedEventArgs.TableName">
            <summary>
            Gets the name of the new table.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableCreatedEventArgs.Columns">
            <summary>
            Gets or sets the columns of the new table.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableCreatedEventArgs.DataSourceType">
            <summary>
            Gets the type of the data source.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableCreatedEventArgs.DataSource">
            <summary>
            Gets the data source.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableCreatedEventArgs.SchemaTable">
            <summary>
            Gets the schema table of the data source.
            If the data source is an sql command, returns the schema of the query result,
            otherwise, it must return null.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Access.TableColumn">
            <summary>
            Represents the definition of the table column
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumn.ColumnName">
            <summary>
            Gets the name of the new column.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumn.ColumnDataType">
            <summary>
            Gets the data type of the new column.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.TableColumn.ColumnPropertiesDDL">
            <summary>
            Gets the ddl statement which represents the general properties of the new column,
            except the column name and data type.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Access.AccessExportConsts">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Access.ParameterData">
            <summary>
            The sql parameter struct.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Access.AccessWriter">
            <summary>
            The Access writer class.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Access.AccessWriter.#ctor(Spire.DataExport.Common.ExportBase,System.IO.Stream)">
            <summary>
            Create AccessWriter object.
            </summary>
            <param name="Owner">The object of the parent object.</param>
            <param name="Stream"></param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessWriter.CreateTable(System.String,System.String,System.String)">
            <summary>
            Create MS Access table.
            </summary>
            <param name="ConnectStr">Database connection string</param>
            <param name="TableName">MS Access name</param>
            <param name="CommandStr">SQL command table</param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessWriter.CreateInsertCommand(System.String,System.String,Spire.DataExport.Access.ParameterData[])">
            <summary>
            Create insert sql command.
            </summary>
            <param name="Name">Database connection string.</param>
            <param name="InsertCommandDML">Insert sql string</param>
            <param name="ParamsData">Parameters of insert sql.</param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessWriter.AlterTable(System.String,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Use the alter table statement to change the table
            </summary>
            <param name="databaseFile">Database file path.</param>
            <param name="sqls">The alter table statements</param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessWriter.CreateAccessDatabase(System.String,System.Boolean)">
            <summary>
            Create a MS Access database file.
            </summary>
            <param name="DatabaseFile">MS Access database file name.</param>
        </member>
        <member name="M:Spire.DataExport.Access.AccessWriter.WriteData(System.Int32,System.String)">
            <summary>
            Setting parameter value of sql statment.
            </summary>
            <param name="Num">The index of parameters.</param>
            <param name="Data">The value of paraemters.</param>
        </member>
        <member name="P:Spire.DataExport.Access.AccessWriter.InsertCommand">
            <summary>
            Insert command text object.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Access.AccessWriter.AccessVersion">
            <summary>
            Gets or sets the access version.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Access.DatabaseExport">
            <summary>
            The abstract class of database data export .
            </summary>
        </member>
        <member name="M:Spire.DataExport.Access.DatabaseExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.Access.DatabaseExport.SaveToStream(System.IO.Stream)">
            <summary>
            Save export data to the memory stream
            </summary>
            <param name="Stream">Stream object</param>
        </member>
        <member name="T:Spire.DataExport.Common.AdvancedTextExport">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.AdvancedTextExport.Header">
            <summary>
            Gets or sets the text placed before the exproted data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.AdvancedTextExport.Footer">
            <summary>
            Gets or sets the text placed after the exproted data.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ColExport">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ColExport.Name">
            <summary>
            Gets and sets column name.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ColExport.Value">
            <summary>
            Stores and changes the column value in the current data row.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ColumnExport">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ColumnsExport">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ColumnsExport.Item(System.Int32)">
            <summary>
            Use this indexer to access the ColumnExport object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.DataWriter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ColumAlign">
            <summary>
            Column Align
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.ColumAlign.Left">
            <summary>
            Left Align
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.ColumAlign.Center">
            <summary>
            Center Align
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.ColumAlign.Right">
            <summary>
            Right Align
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.PageOrientation">
            <summary>
            Page Orientation
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.PageOrientation.Portrait">
            <summary>
            Portrait
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.PageOrientation.Landscape">
            <summary>
            Landscape
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ActionType">
            <summary>
            Action Type
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.ActionType.None">
            <summary>
            No Action
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.ActionType.OpenView">
            <summary>
            Open result file
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.ActionType.Print">
            <summary>
            Print result file
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.SaveType">
            <summary>
            Save Type
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.SaveType.Attachment">
            <summary>
            Attachment mode
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.SaveType.Inline">
            <summary>
            Inline mode
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ExportBase">
            <summary>
            The ExportBase class is the base class of the Spire.DataExport component suite. 
            </summary>
        </member>
        <member name="F:Spire.DataExport.Common.ExportBase.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.ReadFromFile(System.IO.FileStream,System.IO.Stream,System.Int64)">
            <summary>
            Reads data from a stream until the end is reached. The
            data is returned as a byte array. An IOException is
            thrown if any of the underlying IO calls fail.
            </summary>
            <param name="Source">The stream to read data from</param>
            <param name="Count">The initial buffer length</param>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.SaveToStream(System.IO.Stream)">
            <summary>
            Save export data to the memory stream
            </summary>
            <param name="Stream"></param>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.SaveToMemoryStream(System.IO.Stream,System.IO.TextWriter)">
            <summary>
            Saves export data to the memory/file stream.
            </summary>
            <param name="Stream">Exported stream</param>
            <param name="Writer">Stream writer</param>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.Stop">
            <summary>
            Stops the current process of Save method invocation.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.LoadSpecificationFromFile(System.String)">
            <summary>
            Loads object properties from a file defined.
            </summary>
            <param name="FileName">File name</param>
        </member>
        <member name="M:Spire.DataExport.Common.ExportBase.SaveSpecificationToFile(System.String)">
            <summary>
            Saves object properties to a file.
            </summary>
            <param name="FileName">File name</param>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ExportLongColumn">
            <summary>
            Indicates whether export long char/binary column
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ConvertBinaryToHexString">
            <summary>
            if true, FillExportRow function will convert binary column to hex string,
            except (DataSource is SqlCommand and column is BLOB).
            if false or (DataSource is SqlCommand and column is BLOB), FillExportRow will set
            the corresponding ColExport.IsBinary = true and set the ColExport.DataSource
            to DataReader/DataRow/ListView.
            default is true
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Columns">
            <summary>
            Gets or sets the field bindings and display attributes of the columns in the exported file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ExportIfEmpty">
            <summary>
            Determines whether export when export dataset is empty.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.SkipRows">
            <summary>
            Determines the number of records, which are not exported. If SkipRecCount = 0, then all the records are exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.MaxRows">
            <summary>
            Determines the number of rows that exported from the source table. If MaxRows equals 0, then the recrods exported no limited. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.AutoFitColWidth">
            <summary>
            Gets or sets width of each column  in the result file is set automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.AddTitles">
            <summary>
            Gets of sets titles are added in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ColumnsExport">
            <summary>
            Gets the columns export. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Options">
            <summary>
            Gets or sets Options of data export.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Stoped">
            <summary>
            Indicates whether stop method was called.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Header">
            <summary>
            Gets or sets the text placed before the exproted data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Titles">
            <summary>
            Gets or sets the column titles in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Footer">
            <summary>
            Gets or sets the text placed after the exproted data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.DataFormats">
            <summary>
            Gets or sets data format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.CustomFormats">
            <summary>
            Gets or sets a special format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Title">
            <summary>
            Gets or sets the title of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ColumnsWidth">
            <summary>
            Gets or sets column width of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ColumnsAlign">
            <summary>
            Gets or sets the aligments of the exproted columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ColumnsLength">
            <summary>
            Gets or sets the length of the exported columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.Culture">
            <summary>
            Get the information about Spire.DataExport version.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.DataEncoding">
            <summary>
            Gets or sets the encoding type of result data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.DataSource">
            <summary>
            Gets or sets the data source type.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.SQLCommand">
            <summary>
            Gets or sets the exported SQL command.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.DataTable">
            <summary>
            Gets or sets the exported table.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.ExportBase.ListView">
            <summary>
            Gets or sets the exported list view.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.GetCellParams">
            <summary>
            Occur when the parameters of the record are recieved.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.FetchedRecord">
            <summary>
            Occur when calculate width of every column if AutoFitColWidth property equals true.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.BeginExport">
            <summary>
            Occur when data export before begining.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.EndExport">
            <summary>
            Occur when data export after ending.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.SkippedRecord">
            <summary>
            Occur when source records is skipped.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.ExportedRecord">
            <summary>
            Occur after the export of each source record.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.StopExport">
            <summary>
            Occur when Stop methed calling. 
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.GetExportText">
            <summary>
            Occur when get export of source string.
            </summary>
        </member>
        <member name="E:Spire.DataExport.Common.ExportBase.BeforeExportRow">
            <summary>
            Occur before the export of each source record.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.FormatsExport">
            <summary>
            Summary description for FormatsExport.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Common.FormatsExport.ResetFormats(System.Object)">
            <summary>
            Set to use default data format.
            </summary>
            <param name="Owner">Owner object</param>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.Integer">
            <summary>
            Determines the representation of integer fields in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.Float">
            <summary>
            Gets or sets the float columns in result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.Time">
            <summary>
            Gets or sets the time columns in result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.DateTime">
            <summary>
            Gets or sets the datetime columns in result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.Currency">
            <summary>
            Gets or sets the currency columns in result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.BooleanTrue">
            <summary>
            Gets or sets the False value of the source boolean columns in result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.BooleanFalse">
            <summary>
            Gets or sets the True value of the source boolean columns in result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatsExport.NullString">
            <summary>
            Gets or sets the Null value in result file.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.FormatTextExport">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatTextExport.AddTitles">
            <summary>
            Gets of sets titles are added in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatTextExport.Titles">
            <summary>
            Gets or sets the column titles in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatTextExport.DataFormats">
            <summary>
            Gets or sets data format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatTextExport.CustomFormats">
            <summary>
            Gets or sets a special format for data exported.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.FormatTextSqlExport">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatTextSqlExport.DataFormats">
            <summary>
            Gets or sets data format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.FormatTextSqlExport.CustomFormats">
            <summary>
            Gets or sets a special format for data exported.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.MemoryExport">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.RowExport">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.RowExport.Item(System.Int32)">
            <summary>
            Gets or sets ExportCol object by index.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.TextExport">
            <summary>
            
            </summary>
        </member>
        <member name="M:Spire.DataExport.Common.TextExport.SaveToStream(System.IO.Stream)">
            <summary>
            Save export data to the memory stream
            </summary>
            <param name="Stream">Stream object</param>
        </member>
        <member name="M:Spire.DataExport.Common.TextExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="P:Spire.DataExport.Common.TextExport.FileName">
            <summary>
            Gets or sets a path and a name of the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.TextExport.ShowFile">
            <summary>
            This boolean property allows you to show the resulting file immediately after finishing of an export process.  
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.TextExport.PrintFile">
            <summary>
            This boolean property allows you to print the resulting file immediately after finishing of an export process.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Common.TextExport.ActionAfterExport">
            <summary>
            The property determines you to execute action after data export.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.TxtWriter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.DisposabledObject">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ExportConsts">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ColExportType">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Common.ExportUtils">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Base.Options">
            <summary>
            Options ժҪ˵
            </summary>
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Clipboard.ClipboardExport">
            <summary>
            The ClipboardExport is the export component for data exporting into Windows clipboard..
            </summary>
        </member>
        <member name="M:Spire.DataExport.Clipboard.ClipboardExport.SaveToFile">
            <summary>
            Execute performs the export of source data to Clipboard.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.ExportType">
            <summary>
            Gets or sets clipboard export type.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.Separator">
            <summary>
            Gets or sets the character of exported table columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.Spacing">
            <summary>
            Gets or sets internal spacing of exported table columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.ClipboardViewer">
            <summary>
            Gets or sets the clipboard viewer of data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.ColumnsAlign">
            <summary>
            Gets or sets the aligments of the exproted columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.Header">
            <summary>
            Gets or sets the text placed before the exproted data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.Footer">
            <summary>
            Gets or sets the text placed after the exproted data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.AddTitles">
            <summary>
            Gets of sets titles are added in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.Titles">
            <summary>
            Gets or sets the column titles in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.DataFormats">
            <summary>
            Gets or sets data format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.CustomFormats">
            <summary>
            Gets or sets a special format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.DataEncoding">
            <summary>
            Gets or sets the encoding type of result data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.ShowContent">
            <summary>
            Indicates whether show content of the clipboard data after data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Clipboard.ClipboardExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Clipboard.ClipbrdExportConsts">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.CellItemStylesCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.CellsCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.ChartsCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.ChartSeriesListCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.ColumnFormatsCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.HyperlinksCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.ImagesCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.PicturesCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.CollectionEditors.SheetsCollectionEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Collections.Collection">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Collections.Collection.Item(System.Int32)">
            <summary>
            Gets or sets CollectionItem object by index.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Collections.CollectionItem">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Collections.StringListCollection">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.Collections.StringListCollection.Item(System.Int32)">
            <summary>
            Use this indexer to access the string object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.DBF.DBFExport">
            <summary>
            The DBFExport component allows you to export your data to the DBF format. 
            </summary>
        </member>
        <member name="M:Spire.DataExport.DBF.DBFExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.DBF.DBFExport.SaveToStream(System.IO.Stream)">
            <summary>
            Save export data to the memory stream
            </summary>
            <param name="Stream">Stream object</param>
        </member>
        <member name="M:Spire.DataExport.DBF.DBFExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.DBF.DBFExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.DBF.DBFExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.ColumnsPrecision">
            <summary>
            Determines the column precisions in the exported file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.DefaultFloatSize">
            <summary>
            Determines the default size of float columns in the exported file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.DefaultFloatDecimal">
            <summary>
            Determines the fractional part size of flat columns in the exported file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.DataEncoding">
            <summary>
            Determines the encoding type of the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.ColumnsLength">
            <summary>
            Gets or sets the length of the exported columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.AutoFitColWidth">
            <summary>
            Gets or sets whether calculate width of each column in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.ConvertBinaryToHexString">
            <summary>
            always false, never convert binary data to hex string.
            if false or (DataSource is SqlCommand and column is BLOB), FillExportRow will set
            the corresponding ColExport.IsBinary = true and set the ColExport.DataSource
            to DataReader/DataRow/ListView.
            </summary>
        </member>
        <member name="P:Spire.DataExport.DBF.DBFExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="T:Spire.DataExport.DBF.DbfExportCommon">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.DBF.DbfExportWriter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.DBF.ShortFieldNameGenerator">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.GetExportFieldData">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.NormalFunc">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.DataRowEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.StopExportEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.TextEventHandler">
            <summary>
            
            </summary>	
        </member>
        <member name="T:Spire.DataExport.Delegates.ExportRowEventHandler">
            <summary>
            
            </summary>	
        </member>
        <member name="T:Spire.DataExport.Delegates.CellParamsEventHandler">
            <summary>
            
            </summary>	
        </member>
        <member name="T:Spire.DataExport.Delegates.XLSDataRowEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.XLSTextEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.XLSExportRowEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.HeaderFooterParamsEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.TitleParamsEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.DataParamsEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.AggregateParamsEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.XLSSheetDataEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.XLSSheetEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.RTFStyleEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.RTFTitleStyleEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.RTFDataStyleEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.ListItemProc">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Delegates.CustomItemProc">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Designers.ExportDesigner">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.AggregateParamsEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.CellParamsEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.DataParamsEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.ExportRowEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.HeaderFooterParamsEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.RTFDataStyleEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.RTFTitleStyleEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.StopExportEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.TextEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.TitleParamsEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.XLSDataRowEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.XLSExportRowEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.EventArgs.XLSTextEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Forms.AboutDataExport">
            <summary>
            Summary description for AboutDataExport.
            </summary>
        </member>
        <member name="F:Spire.DataExport.Forms.AboutDataExport.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Forms.AboutDataExport.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Forms.AboutDataExport.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Forms.DataExportColumnsEditor">
            <summary>
            Summary description for DataExportColumnsEditor.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Forms.DataExportColumnsEditor.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Forms.DataExportColumnsEditor.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Forms.frmRegister">
            <summary>
            Register 的摘要说明。
            </summary>
        </member>
        <member name="F:Spire.DataExport.Forms.frmRegister.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:Spire.DataExport.Forms.frmRegister.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
        </member>
        <member name="M:Spire.DataExport.Forms.frmRegister.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要使用代码编辑器修改
            此方法的内容。
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HTMLExport">
            <summary>
            The HTMLExport is the export component for data exporting into HTML file format.
            </summary>
        </member>
        <member name="M:Spire.DataExport.HTML.HTMLExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.HTML.HTMLExport.SaveToStream(System.IO.Stream)">
            <summary>
            Save export data to the memory stream
            </summary>
            <param name="Stream">Stream object</param>
        </member>
        <member name="M:Spire.DataExport.HTML.HTMLExport.Stop">
            <summary>
            Stops the current process of Save method invocation.
            </summary>
        </member>
        <member name="M:Spire.DataExport.HTML.HTMLExport.SaveTemplateToFile(System.String)">
            <summary>
            This method saves the current value of the TableOptions and HtmlOptions properties to the file 
            specified by the FileName parameter. The saved template can be loaded later using the 
            LoadTemplateFromFile() method.
            </summary>
            <param name="FileName">File name</param>
        </member>
        <member name="M:Spire.DataExport.HTML.HTMLExport.LoadTemplateFromFile(System.String)">
            <summary>
            This method loads from the FileName file the template previously saved by the SaveTemplateToFile() procedure.
            </summary>
            <param name="FileName">File name</param>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.UsingCSS">
            <summary>
            Gets or sets whether use CSS file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.CSSFileName">
            <summary>
            Gets or sets css file name.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.OverwriteCSSFile">
            <summary>
            Indicates whether override css file if the file already exists.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.HtmlTableOptions">
            <summary>
            Gets or sets table element options of html document in result html file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.HtmlTextOptions">
            <summary>
            Gets or sets text element options of html document in result html file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.HtmlStyle">
            <summary>
            Gets or sets template style of html document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.PageRecords">
            <summary>
            Gets or sets maximum number of result records in the result html file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.GenerateIndex">
            <summary>
            Indicates whether create the Html index file automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.HtmlIndexOption">
            <summary>
            Gets or sets the options of navigation links.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.BoolAsCheckBox">
            <summary>
            Indicates whether boolean columns are exported as checkboxs.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.InterpretTags">
            <summary>
            Indicates whether all special symbols &lt; &gt; &quot; &amp; found in exported data (text) will be replaced with corresponding &lt; &gt; &quot; &amp; ones.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.Title">
            <summary>
            Gets or sets the title of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.ColumnsAlign">
            <summary>
            Gets or sets the aligments of the exproted columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.DataEncoding">
            <summary>
            Gets or sets the encoding type of result data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HTMLExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlExportCommon">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlConsts">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlExportIndexOption">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HtmlExportIndexOption.LinkTemplate">
            <summary>
            Defines the template string for generating links on the index page to other pages in the collection.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HtmlExportIndexOption.NavigationAlign">
            <summary>
            Defines if there are navigation links (First, Prior, Next, Last) on the bottom of each page in the collection.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HtmlExportIndexOption.PageTitle">
            <summary>
            Defines the caption of the link navigating to the "home" ("Index") page of the collection.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HtmlExportIndexOption.FirstDisplayCaption">
            <summary>
            Defines the caption of the link navigating to the first document of the collection. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HtmlExportIndexOption.PriorDisplayCaption">
            <summary>
            Defines the caption of the link navigating to the previous document in the collection.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HtmlExportIndexOption.NextDisplayCaption">
            <summary>
            Defines the caption of the link navigating to the next document in the collection.
            </summary>
        </member>
        <member name="P:Spire.DataExport.HTML.HtmlExportIndexOption.LastDisplayCaption">
            <summary>
            Defines the caption of the link navigating to the last document of the collection.
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlExportStyles">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlExportWriter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlPageOption">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlTableOptions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.HTML.HtmlTextOptions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.License.DataExportLicense">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.License.DataExportLicenseProvider">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.License.DataExportProtect">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PdfBox">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PdfDocument">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PDFExport">
            <summary>
            The PDFExport is the export component for data exporting into PDF file format.
            </summary>
        </member>
        <member name="M:Spire.DataExport.PDF.PDFExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.PDF.PDFExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.PDF.PDFExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.PDF.PDFExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFExport.PDFOptions">
            <summary>
            Gets or sets options for the result PDF file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFExport.ColumnsWidth">
            <summary>
            Gets or sets column width of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFExport.ColumnsAlign">
            <summary>
            Gets or sets the aligments of the exproted columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFExport.DataEncoding">
            <summary>
            Determines the encoding type of the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFExport.AutoFitColWidth">
            <summary>
            Gets or sets width of each column  in the result file is set automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PdfExportCommon">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PdfExportPageOptions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PdfExportWriter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PdfFont">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PdfFont.PdfFontName">
            <summary>
            Gets or sets the font name of the result PDF document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PdfFont.Encoding">
            <summary>
            Gets or sets the character encoding of the result PDF document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PdfFont.Size">
            <summary>
            Gets or sets font size of the result PDF Document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PdfFont.Color">
            <summary>
            Gets or sets font color of the result PDF Document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PdfFont.CustomFont">
            <summary>
            Defines user font.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PdfFont.AllowCustomFont">
            <summary>
            This property is used for allowing to use the user font.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PdfFont.CustomFontColor">
            <summary>
            Defines the color of the user font.
            </summary>
        </member>
        <member name="T:Spire.DataExport.PDF.PDFOptions">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.HeaderFont">
            <summary>
            Gets or sets font of the result PDF headers.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.TitleFont">
            <summary>
            Gets or sets font of the result PDF tiltes.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.DataFont">
            <summary>
            Gets or sets font of data exported in the result PDF document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.FooterFont">
            <summary>
            Gets or sets font of the result PDF footers.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.RowSpacing">
            <summary>
            Gets or sets internal row spacing in the result PDF document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.ColSpacing">
            <summary>
            Gets or sets internal column spacing in the result PDF document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.GridLineWidth">
            <summary>
            Gets or sets the width of the grid table line in the result PDF document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.PDF.PDFOptions.GridLineColor">
            <summary>
            Gets or sets the color of the grid table lines in the result PDF document.
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.AboutEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.AccessDatabaseNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.CellColorEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.CellDateTimeFormatEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.CellFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.ClipViewerEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.ColumnNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.CultureEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.CurrencyFormatEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.DateTimeFormatEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.DBFFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.ExportColumnsEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.FontNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.HtmlFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.ImageFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.IntegerFormatEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.LaTeXFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.ListComponentEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.PDFFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.PictureNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.RTFFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.SQLFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.TimeFormatEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.TXTFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.WorkSheetNameEditor">
            <summary>
            WorkSheetNameEditor 的摘要说明。
            </summary>
        </member>
        <member name="T:Spire.DataExport.PropEditors.XmlFileNameEditor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfColorTable">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RtfColorTable.Item(System.Int32)">
            <summary>
            Use this indexer to access the RtfColorTableItem object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfColorTableItem">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RTFExport">
            <summary>
            The RTFExport is the export component for data exporting into Rich Text Format file format.
            </summary>
        </member>
        <member name="M:Spire.DataExport.RTF.RTFExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.RTF.RTFExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.RTF.RTFExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.RTF.RTFExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.RTF.RTFExport.Stop">
            <summary>
            Stops the current process of Save method invocation.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFExport.RTFOptions">
            <summary>
            Gets or sets options of the result RTF document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFExport.ColumnsWidth">
            <summary>
            Gets or sets column width of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFExport.ColumnsAlign">
            <summary>
            Gets or sets the aligments of the exproted columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFExport.DataEncoding">
            <summary>
            Determines the encoding type of the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="E:Spire.DataExport.RTF.RTFExport.GetHeaderStyle">
            <summary>
            Occur when the style of header changed.
            </summary>
        </member>
        <member name="E:Spire.DataExport.RTF.RTFExport.GetCaptionStyle">
            <summary>
            Occur when the style of the titles changed.
            </summary>
        </member>
        <member name="E:Spire.DataExport.RTF.RTFExport.GetDataStyle">
            <summary>
            Occur when the data style changed.
            </summary>
        </member>
        <member name="E:Spire.DataExport.RTF.RTFExport.GetFooterStyle">
            <summary>
            Occur when the style of footer changed.
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfExportCommon">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfExportWriter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfFontTable">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RtfFontTable.Item(System.Int32)">
            <summary>
            Use this indexer to access the RtfFontTableItem object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfFontTableItem">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfHeader">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RtfItemStyle">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RTFOptions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RTFStyle">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyle.Font">
            <summary>
            Defines text font parameters for the current style.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyle.FontColor">
            <summary>
            Defines font color for the current style.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyle.BackgroundColor">
            <summary>
            Defines the background color in the current style.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyle.HighlightColor">
            <summary>
            Defines the color of the text highlighting in the current style.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyle.AllowHighlight">
            <summary>
            Enables highlighting text in the current style.
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyle.AllowBackground">
            <summary>
            Enables using the background color in the current style. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyle.Alignment">
            <summary>
            Defines the text alignment for the current style.
            </summary>
        </member>
        <member name="T:Spire.DataExport.RTF.RTFStyles">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.RTF.RTFStyles.Item(System.Int32)">
            <summary>
            Use this indexer to access the RtfStripStyle object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.SQL.SQLExport">
            <summary>
            The SQLExport is the export component for data exporting into SQL Script file format.
            </summary>
        </member>
        <member name="M:Spire.DataExport.SQL.SQLExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.SQL.SQLExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.SQL.SQLExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.SQL.SQLExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.TableName">
            <summary>
            Gets or sets the table name for insert and create table sql statement.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.CommitRowCount">
            <summary>
            Indicates to insert commit statment after define a number of rows.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.CommitAfterScript">
            <summary>
            Indicates whether insert commit statement after data exported complete.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.CommitStatement">
            <summary>
            Gets or sets the sql commit statement.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.CreateTable">
            <summary>
            Indicate whether to generate the create table sql statement.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.EndOfStatement">
            <summary>
            Gets or sets the character for end of each sql statement.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.FormatValues">
            <summary>
            Indicate whether format exported value according to the dataformats property.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.DataEncoding">
            <summary>
            Gets or sets the encoding type of result data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.SQL.SQLExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="T:Spire.DataExport.SQL.SqlExportConsts">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.TXT.CSVOption">
            <summary>
            Options ժҪ˵
            </summary>
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.TXT.TXTExport">
            <summary>
            The TXTExport is the export component for data exporting into TXT, CSV, DIFF, SYLK file format.
            </summary>
        </member>
        <member name="M:Spire.DataExport.TXT.TXTExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.TXT.TXTExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.TXT.TXTExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.TXT.TXTExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.TXT.TXTExport.Stop">
            <summary>
            Stops the current process of Save method invocation.
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.ExportType">
            <summary>
            Gets or sets the format of the output export file. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.CSVOption">
            <summary>
            Gets or sets options of format if the format type equals CSV.
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.TXTSpacing">
            <summary>
            Gets or sets internal columns spacing in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.AutoFitColWidth">
            <summary>
            Gets or sets width of each column  in the result file is set automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.ColumnsAlign">
            <summary>
            Gets or sets the aligments of the exproted columns.
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.ColumnsWidth">
            <summary>
            Gets or sets column width of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.DataEncoding">
            <summary>
            Gets or sets the encoding type of result data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.TXT.TXTExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="T:Spire.DataExport.TypeConverters.CellValueTypeConverter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.TypeConverters.CollectionTypeConverter">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.EmptyFileStream">
            <summary>
            EmptyFileStream ժҪ˵
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.IniSection">
            <summary>Class represents a section similar to an information file(INI) section.
            </summary>
            <remarks>Provides PropertyGrid support.</remarks>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSection.#ctor(System.String,Spire.DataExport.Utils.XMLSetting)">
            <summary>Constructor requiring a name and a reference to the top object.</summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSection.Settings">
            <summary>
            Settings collection property.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSection.Item(System.Int32)">
            <summary>
            Indexer access to the Setting by index.
            </summary>
            <value>The item number</value>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSection.Item(System.String)">
            <summary>
            Indexer access to the Setting by name.
            </summary>
            <value>The item name</value>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSection.Name">
            <summary>
            Section name
            </summary>
            <value>Section name</value>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSection.Parent">
            <summary>
            Parent object is set with the Add method.
            </summary>
            <value>Section name</value>
            <remarks>Could be a IniSection or IniSetting</remarks>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSection.ToString">
            <summary>Meaningful text representation</summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSection.DisplayInPG">
            <summary>
            Sets or returns the visibility in the PropertyGrid control.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.SectionPd">
            <summary>
            Defines a custom type descriptor to be displayed in the property grid.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.IniSections">
            <summary>Collection class of IniSections.</summary>
            <remarks>Provides PropertyGrid support.</remarks>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.#ctor(Spire.DataExport.Utils.XMLSetting)">
            <summary>Constructor requiring a reference to the top object</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.Remove(System.String)">
            <summary>
            Removes a single named item from the collection.
            </summary>
            <param name="itemName">The item name</param>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.Remove(System.Int32)">
            <summary>
            Removes a single index item from the collection.
            </summary>
            <param name="itemNumber">The item number.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.Add(System.String)">
            <summary>
            Adds a named section to the collection.
            </summary>
            <param name="sectionName">The section name.</param>
            <returns>The Section object reference.</returns>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSections.Item(System.Int32)">
            <summary>
            Indexer access to the Section by index.
            </summary>
            <value>The item number</value>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSections.Item(System.String)">
            <summary>
            Indexer access to the Section by name.
            </summary>
            <value>The item name</value>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.ByName(System.String)">
            <summary>Gets the index of a section by name.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetProperties(System.Attribute[])">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetClassName">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetAttributes">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetComponentName">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetConverter">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetDefaultEvent">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetDefaultProperty">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetEditor(System.Type)">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetEvents(System.Attribute[])">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetEvents">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetPropertyOwner(System.ComponentModel.PropertyDescriptor)">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSections.GetProperties">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="T:Spire.DataExport.Utils.IniSetting">
            <summary>Class represents a name-value pairs similar to an information file(INI).</summary>
            <remarks>Provides PropertyGrid support and
            support for sub-setings.</remarks>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.Parent">
            <summary>
            Parent object is set with the Add method.
            </summary>
            <value>Parent object</value>
            <remarks>Could be a IniSection ,IniSetting or IniSettings</remarks>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.#ctor(System.String)">
            <summary>
            Constructor.
            </summary>
            <remarks>Every Setting object should have a name.</remarks>
            <param name="settingName">The name of the setting.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.#ctor">
            <summary>
            Constructor.
            </summary>
            <remarks>Default constructor is required for the propertyBrowser 
            collection editor to add an item.</remarks>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.Value">
            <summary>
            Sets or returns the value of the setting
            </summary>	
            <remarks>The setting type is determined when the value is set. 
            This should be the only way we set a value.</remarks>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.Name">
            <summary>
            The Setting name.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.Tag">
            <summary>
            Extra string information associated with the setting.
            </summary>
            <remarks>Used internally to save controls tag data.</remarks>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.Description">
            <summary>
            An optional description of the setting.
            </summary>
            <remarks>Will be displayed in PropertyGrid.</remarks>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.DisplayInPG">
            <summary>
            Sets or returns the visibility in the PropertyGrid control.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.ReadonlyInPG">
            <summary>
            Can we change a setting in the Property Grid or not.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.TypeName">
            <summary>Read-only. Returns a string type name.</summary>
            <remarks>Used internally to track the object type.</remarks>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.OriginalType">
            <summary>Read-only. Returns the value type.</summary>
            <remarks>Used internally</remarks>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.ToDataSource">
            <summary>
            Conversion to IniSettings collection.
            A setting can act as a DataSource if it is a IniSettings collection.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.Item(System.Int32)">
            <summary>
            Access to Setting in a sub-IniSettings by index.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSetting.Item(System.String)">
            <summary>
            Access to Setting in a sub-IniSettings by name.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~Spire.DataExport.Utils.IniSettings">
            <summary>
            Implicit conversion to IniSettings collection.
            A setting can act as a section to contain a collection of more settings.
            </summary>
            <exception cref="T:System.ApplicationException">Thrown when something
            unexpected happens.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~Spire.DataExport.Utils.IniSection">
            <summary>
            Implicit conversion to IniSection.
            A setting can be a sub-section to contain a collection of more settings.
            </summary>
            <exception cref="T:System.ApplicationException">Thrown when something
            unexpected happens.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Boolean">
            <summary>
            Implicit conversion to bool.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Byte">
            <summary>
            Implicit conversion to byte.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Char">
            <summary>
            Implicit conversion to char.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.DateTime">
            <summary>
            Implicit conversion to DateTime.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Decimal">
            <summary>
            Implicit conversion to Decimal.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Double">
            <summary>
            Implicit conversion to double.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Int16">
            <summary>
            Implicit conversion to Int16.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Int32">
            <summary>
            Implicit conversion to Int32.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Int64">
            <summary>
            Implicit conversion to Int64.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Single">
            <summary>
            Implicit conversion to Single.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.UInt16">
            <summary>
            Implicit conversion to UInt16.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.UInt32">
            <summary>
            Implicit conversion to UInt32.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.UInt64">
            <summary>
            Implicit conversion to UInt64.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.String">
            <summary>
            Implicit conversion to string.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Collections.ArrayList">
            <summary>
            Implicit conversion to ArrayList.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Drawing.Color">
            <summary>
            Implicit conversion to Color.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Drawing.Font">
            <summary>
            Implicit conversion to Font.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Drawing.Size">
            <summary>
            Implicit conversion to Size.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSetting.op_Implicit(Spire.DataExport.Utils.IniSetting)~System.Drawing.Point">
            <summary>
            Implicit conversion to Point.
            Conversion to original type only.
            </summary>
            <remarks>To return an Object type use the Value property.</remarks>
            <exception cref="T:System.Exception">Thrown when no conversion is possible.</exception>
        </member>
        <member name="T:Spire.DataExport.Utils.SettingPd">
            <summary>
            Defines a custom type descriptor to be displayed in the property grid.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.IniSettings">
            <remarks>Provides PropertyGrid support.</remarks>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.#ctor(Spire.DataExport.Utils.XMLSetting)">
            <summary>
            Constructor taking a reference to the top object.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.ToString">
            <summary>
            A friendly description for the PropertyGrid.
            </summary>
            <returns>A friendly description.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.Remove(System.String)">
            <summary>
            Removes a single named item from the collection.
            </summary>
            <param name="itemName">Item name</param>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.Remove(System.Int32)">
            <summary>
            Removes a single indexed item from the collection.
            </summary>
            <param name="itemNumber">Item name</param>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.Add(System.String)">
            <summary>
            Add a new item and return a reference to it.
            </summary>
            <param name="settingName">Setting name</param>
            <returns>The IniSetting object</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.Add(System.String,System.Object)">
            <summary>
            Add a new item, set the value and return a reference to it.
            </summary>
            <param name="settingName">Setting name</param>
            <param name="val">The setting value</param>
            <returns>The IniSetting object</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.Add(System.String,System.Object,System.String)">
            <summary>
            Add a new item, set the value and return a reference to it.
            </summary>
            <param name="settingName">Setting name</param>
            <param name="val">The setting value</param>
            <param name="tag">The extra tag string</param>
            <returns>The IniSetting object</returns>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSettings.Item(System.Int32)">
            <summary>
            Indexer access to the Setting by index.
            </summary>
            <value>The item number</value>
        </member>
        <member name="P:Spire.DataExport.Utils.IniSettings.Item(System.String)">
            <summary>
            Indexer access to the Setting by name.
            </summary>
            <value>The item name</value>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.ByName(System.String)">
            <summary>Required for PropertyGrid</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetProperties(System.Attribute[])">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetClassName">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetAttributes">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetComponentName">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetConverter">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetDefaultEvent">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetDefaultProperty">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetEditor(System.Type)">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetEvents(System.Attribute[])">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetEvents">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetPropertyOwner(System.ComponentModel.PropertyDescriptor)">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.IniSettings.GetProperties">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="T:Spire.DataExport.Utils.NullCollectionEditor">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.NullCollectionEditor.#ctor">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.NullCollectionEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)">
            <summary>Required for PropertyGrid.</summary>
        </member>
        <member name="T:Spire.DataExport.Utils.AppSettingsUtils">
            <summary>
            A utility class designed to work with the XmlSettings class.
            </summary>
            <remarks>
            Contains methods to perform control-specific tasks.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.AddPropertyToBag(Spire.DataExport.Utils.XMLSetting,System.Windows.Forms.Control,System.String)">
            <summary>
            Maintains the property value automatically.
            </summary>
            <param name="control">The control</param>
            <param name="propertyName">Property name</param>
            <param name="XmlXmlSetting">The main AppSettings object.</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.FindControlInPath(System.Windows.Forms.Control,System.String)">
            <summary>
            Return a Control reference with the specified name
            </summary>
            <param name="path">The full path to the control</param>
            <param name="context">The parent control</param>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.BuildControlPathFromRoot(System.Windows.Forms.Control)">
            <summary>
            Return a string containing the full path to the control
            </summary>
            <param name="control">The control</param>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.SetAllPropertiesFromBag(Spire.DataExport.Utils.XMLSetting,System.Windows.Forms.Control)">
            <summary>
            Loads properties for ANY persisted control properties on this parent control.
            </summary>
            <param name="context">The parent control.Usually a form.</param>
            <param name="XmlXmlSetting">The main AppSettings object.</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.SaveAllPropertiesInBag(Spire.DataExport.Utils.XMLSetting,System.Windows.Forms.Control)">
            <summary>
            Saves all persisted properties for this control.
            </summary>
            <param name="context">The parent control.Usually a form.</param>
            <param name="XmlXmlSetting">The main AppSettings object.</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.SaveItemsAndValues(System.Windows.Forms.ComboBox,Spire.DataExport.Utils.IniSection)">
            <summary>
            Saves the contents and selection status of a control.
            </summary>
            <param name="cbo">The ComboBox control source</param>
            <param name="parentSection">The parent IniSection.</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.SaveItemsAndValues(System.Windows.Forms.ListBox,Spire.DataExport.Utils.IniSection)">
            <summary>
            Saves the contents and selection status of a control.
            </summary>
            <param name="lb">The ListBox control source</param>
            <param name="parentSection">The parent IniSection.</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.SaveItemsAndValues(System.Windows.Forms.TreeView,Spire.DataExport.Utils.IniSection)">
            <summary>
            Saves the contents and checked status of a control
            </summary>
            <param name="tv">The TreeView control source</param>
            <param name="parentSection">The parent IniSection.</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.LoadItemsAndValues(System.Windows.Forms.ComboBox,Spire.DataExport.Utils.IniSection)">
            <summary>
            Loads the contents and selection status of a control
            </summary>
            <param name="cbo">The ComboBox control destination</param>
            <param name="parentSection">The IniSection source</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.LoadItemsAndValues(System.Windows.Forms.ListBox,Spire.DataExport.Utils.IniSection)">
            <summary>
            Loads the contents and selection status of a control
            </summary>
            <param name="lb">The ListBox control destination</param>
            <param name="parentSection">The IniSection source</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.LoadItemsAndValues(System.Windows.Forms.TreeView,Spire.DataExport.Utils.IniSection)">
            <summary>
            Loads the contents and checked status of a control
            </summary>
            <param name="tv">The TreeView control destination</param>
            <param name="parentSection">The IniSection source</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.CalcSHA512(System.String)">
            <summary>
            Performs a one-way encryption on the data and returns the result. Useful
            for protected authentication credentials.
            </summary>
            <param name="str">Data to be encrypted.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.Encrypt(System.String,System.String)">
            <summary>
            Encrypts a string using the KeyString.
            </summary>
            <remarks>
            The encrypted data may safely be written and read from plain text ASCII files.
            </remarks>
            <param name="str">String to be encrypted.</param>
            <param name="keyString">Key to use when encrypting the data.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.Decrypt(System.String,System.String)">
            <summary>
            Decrypts a string previously encoded with <see cref="M:Spire.DataExport.Utils.AppSettingsUtils.Encrypt(System.String,System.String)"/> using 
            the given KeyString.
            </summary>
            <param name="str">
            Data that was previously encrypted with <see cref="M:Spire.DataExport.Utils.AppSettingsUtils.Encrypt(System.String,System.String)"/>.
            </param>
            <param name="keyString">
            	Key used when the data was encrypted.
            </param>
        </member>
        <member name="M:Spire.DataExport.Utils.AppSettingsUtils.GetLegalKey(System.String,System.Security.Cryptography.SymmetricAlgorithm)">
            <summary>
            Generate a key that is known to be legal for the chosen algorithm.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.XMLFile">
            <summary>
            Xml file class of reading and wirting
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.XMLFile.FileName">
            <summary>
            XML file path and name
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.LoadFromFile(System.String)">
            <summary>
            Load xml setting file.
            </summary>
            <param name="fileName">xml setting file name</param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.SaveToFile(System.String)">
            <summary>
            Save xml setting to a file.
            </summary>
            <param name="fileName"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.SaveToFile">
            <summary>
            Save xml setting to a file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.ReadValue(System.String,System.String,System.String)">
            <summary>
            Reads value from xml setting file.
            </summary>
            <param name="SectionName">Section name</param>
            <param name="Key">Key value</param>
            <param name="DefaultValue">Default value</param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.WriteValue(System.String,System.String,System.String)">
            <summary>
            Writes value to xml setting file.
            </summary>
            <param name="SectionName">Section name</param>
            <param name="Key">Key</param>
            <param name="Value">Write value</param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.RemoveValue(System.String,System.String)">
            <summary>
            Removes value from xml setting file.
            </summary>
            <param name="SectionName"></param>
            <param name="Key"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.RemoveSection(System.String)">
            <summary>
            Removes section from xml setting file.
            </summary>
            <param name="SectionName"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.ReadValues(System.String,System.Array@)">
            <summary>
            Reads values from xml setting file according to section name.
            </summary>
            <param name="SectionName"></param>
            <param name="values"></param>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile.ReadSections(System.Array@)">
            <summary>
            Reads section name collection from xml setting file.
            </summary>
            <param name="Sections"></param>
        </member>
        <member name="T:Spire.DataExport.Utils.XMLFile1">
            <summary>
            Xml file class of reading and wirting
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.XMLFile1.FileName">
            <summary>
            XML file path and name
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.LoadFromFile(System.String)">
            <summary>
            Load xml setting file.
            </summary>
            <param name="fileName">xml setting file name</param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.SaveToFile(System.String)">
            <summary>
            Save xml setting to a file.
            </summary>
            <param name="fileName"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.SaveToFile">
            <summary>
            Save xml setting to a file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.ReadValue(System.String,System.String,System.String)">
            <summary>
            Reads value from xml setting file.
            </summary>
            <param name="SectionName">Section name</param>
            <param name="Key">Key value</param>
            <param name="DefaultValue">Default value</param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.WriteValue(System.String,System.String,System.String)">
            <summary>
            Writes value to xml setting file.
            </summary>
            <param name="SectionName">Section name</param>
            <param name="Key">Key</param>
            <param name="Value">Write value</param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.RemoveValue(System.String,System.String)">
            <summary>
            Removes value from xml setting file.
            </summary>
            <param name="SectionName"></param>
            <param name="Key"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.RemoveSection(System.String)">
            <summary>
            Removes section from xml setting file.
            </summary>
            <param name="SectionName"></param>
            <returns></returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.ReadValues(System.String,System.Array@)">
            <summary>
            Reads values from xml setting file according to section name.
            </summary>
            <param name="SectionName"></param>
            <param name="values"></param>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLFile1.ReadSections(System.Array@)">
            <summary>
            Reads section name collection from xml setting file.
            </summary>
            <param name="Sections"></param>
        </member>
        <member name="T:Spire.DataExport.Utils.Types">
            <summary>
            Supported data type string constants.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.Tags">
            <summary>
            XML tag names.
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.XMLSetting">
            <summary>
            Top-level class.
            Provides a way to save application settings to xml file format.
            </summary>
            <remarks>
            Used to replace the standard Windows INI file format.
            Sections are named categories to hold collections of settings.
            Settings are name-value pairs able to store many different data types.
            </remarks>
            <example>
            Create an instance of the XMLSettings class.
            <code>
            MySettings = new XMLSettings("MySettings.xml")
            MySettings = new XMLSettings("C:\\MySettings[.xml]")</code>
            Load the file.
            <code>
            bool loaded = MySettings.Load([File]);</code>
            Return a setting passing a default value.
            If the value exists then the existing setting is returned.
            <code>
            BackColor = MySettings.GetVal("FormSettings","BackColor",BackColor);</code>
            Set a value passing a section, name and value.
            Value and section will be created if needed.
            <code>
            MySettings.SetVal("FormSettings","Left",Left);</code>
            Return the same value with 2 different notations.
            <code>
            ret = MySettings.GetVal("FormSettings","Left");
            ret = MySettings.Sections["FormSettings"]["Left"];</code>
            Add a collection of Settings.
            <code>
            IniSettings SubSettings1 = new IniSettings(MySettings);			
            SubSettings1.Add("S1","SubSettings1 test");
            SubSettings1.Add("S2",1234);
            SubSettings1.Add("S3",true);
            MySettings.SetVal("Coll","SubSettings1",SubSettings1);</code>
            Access a sub-setting in that section.
            <code>
            bret = MySettings.Sections["Coll"]["SubSettings1"]["S3"];</code>
            Use as a Datasource.
            <code>
            list1.DisplayMember = "Name";
            list1.DataSource = MySettings.Sections["Coll"]["SubSettings1"].ToDataSource();</code>
            </example>
        </member>
        <member name="F:Spire.DataExport.Utils.XMLSetting.LastXmlError">
            <summary>
            String to hold the last error description generated durring a read or write.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.XMLSetting.XmlComments">
            <summary>
            Reurns the comments at the top of the XML
            after having read the file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.XMLSetting.Dirty">
            <summary>
            Indicates when the settings have changed and need to saved or re-read.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.XMLSetting.Sections">
            <summary>
            Access to the collection of Sections.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.#ctor">
            <summary>Constructor with no file specified.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.#ctor(System.String)">
            <summary>
            Constructor with file name specified.
            </summary>
            <remarks> Passing only a file name without a path will result
             in the file being saved in the same folder as this DLL.
            </remarks>
            <param name="fileName">Full path and file name.</param>
            <exception cref="T:System.ApplicationException">Thrown when the specified
            folder does not exist.</exception>/// 
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.Remove(System.String,System.String)">
            <summary>
            Removes a Setting with a specific name.
            </summary>
            <param name="sectionName">Section name</param>
            <param name="settingName">Setting name</param>
            <exception cref="T:System.NullReferenceException">Thrown when the specified
            section or setting does not exist.</exception>/// 
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.Remove(System.String)">
            <summary>
            Removes a Section with a specific name.
            </summary>
            <param name="sectionName">The name of the section</param>
            <exception cref="T:System.NullReferenceException">Thrown when the specified
            section does not exist.</exception>/// 
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.SetVal(System.String,System.String,System.Object)">
            <summary>
            Creates a section and setting as needed then assigns a value.
            </summary>
            <param name="sectionName">Section name</param>
            <param name="settingName">Setting name</param>
            <param name="oValue">Oject supporting many data types</param>
            <returns>The setting object.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.SetVal(System.String,System.String,System.Object,System.String)">
            <summary>
            Creates a section and setting as needed then assigns a value.
            </summary>
            <param name="sectionName">Section name</param>
            <param name="settingName">Setting name</param>
            <param name="oValue">Oject supporting many data types</param>
            <param name="description">A description of the setting</param>
            <returns>The setting object.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.GetVal(System.String,System.String)">
            <summary>
            Gets the IniSetting object by name.
            </summary>
            <param name="sectionName">Section name</param>
            <param name="settingName">Setting name</param>
            <returns>The setting object.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.GetVal(System.String,System.String,System.Object)">
            <summary>
            Gets the IniSetting object by name passing a default.
            </summary>
            <remarks>Default value will only be assigned if the setting does not exist.</remarks>
            <param name="sectionName">Section name</param>
            <param name="settingName">Setting name</param>
            <param name="oDefault">Default value</param>
            <returns>The setting object.</returns>
        </member>
        <member name="P:Spire.DataExport.Utils.XMLSetting.PathToXmlFile">
            <summary>
            Sets or returns the folder the file will be stored in.
            </summary>
            <remarks>Once set, you can just provide the file name without the path. 
            The value can be overridden by passing a fully dqualified path and file.</remarks>
            <exception cref="T:System.ApplicationException">Thrown when the path is nat valid.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.Save(System.String)">
            <summary>
            Save the settings to the specified path and file.
            </summary>
            <param name="pathAndFileName">Full path and file.</param>
            <returns>True on success, False on failure.</returns>
            <exception cref="T:System.ApplicationException">Thrown when the path specified is nat valid.</exception>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.Save">
            <summary>
            Save the settings to the specified path and file.
            </summary>
            <remarks>Using the file name specified during construction.</remarks>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.Load(System.String)">
            <summary>
            Loads the settings from the specified path and file.
            </summary>
            <param name="pathAndFileName">Full path and file.</param>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.Load">
            <summary>
            Loads the settings from the specified path and file.
            </summary>
            <remarks>Using the file name specified during construction.</remarks>
            <returns>True on success, False on failure.</returns>
        </member>
        <member name="P:Spire.DataExport.Utils.XMLSetting.FileChanged">
            <summary>
            Has the xml file changed since the last read or write?
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.WriteXMLSettings(System.String)">
            <summary>
            Writes all sections and settings to the xml file.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.WriteXMLSection(Spire.DataExport.Utils.IniSettings,System.Xml.XmlTextWriter@)">
            <summary>Writes each sections settings.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.WriteArrayListItems(System.Collections.ArrayList,System.Xml.XmlTextWriter@)">
            <summary>Writes array list to xml.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadXMLSettings(System.String)">
            <summary>Reads data into objects from xml.</summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadEachSectionInSectons(Spire.DataExport.Utils.IniSetting,System.Xml.XmlTextReader@)">
             <summary>
             Creates each section object from xml.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadEachSettingInSubSetting(Spire.DataExport.Utils.IniSetting,System.Xml.XmlTextReader@)">
             <summary>
             Creates each sub-setting object from xml.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadEachArrayListItem(Spire.DataExport.Utils.IniSetting,System.Xml.XmlTextReader@)">
             <summary>
             Reads all the arraylist items
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadEachSubSettingInSettings(Spire.DataExport.Utils.IniSettings,System.Xml.XmlTextReader@)">
             <summary>
             Creates each sub-setting object that has a setting parent.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadEachSettingInSection(Spire.DataExport.Utils.IniSection,System.Xml.XmlTextReader@)">
             <summary>
             Creates each setting object from xml.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadAllSettingData(Spire.DataExport.Utils.IniSetting,System.String,System.Xml.XmlTextReader@)">
             <summary>
             Reads all the possible data in a setting.
             This is the smallest unit of data.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ReadSettingAndGetType(Spire.DataExport.Utils.IniSettings,System.Xml.XmlTextReader@)">
            <summary>
            Create setting and value from xml.
            Cuold be a section or more settings.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XMLSetting.ConvertFromXmlToType(System.String,System.String)">
            <summary>
            Convert from an XML string to the proper type of object
            </summary>
        </member>
        <member name="T:Spire.DataExport.Utils.XPButton">
            <summary>
            Summary description for UserControl1.
            </summary>
        </member>
        <member name="F:Spire.DataExport.Utils.XPButton.ControlState.Normal">
            <summary>The XP control is in the normal state.</summary>
        </member>
        <member name="F:Spire.DataExport.Utils.XPButton.ControlState.Hover">
            <summary>The XP control is in the hover state.</summary>
        </member>
        <member name="F:Spire.DataExport.Utils.XPButton.ControlState.Pressed">
            <summary>The XP control is in the pressed state.</summary>
        </member>
        <member name="F:Spire.DataExport.Utils.XPButton.ControlState.Default">
            <summary>The XP control object is in the default state.</summary>
        </member>
        <member name="F:Spire.DataExport.Utils.XPButton.ControlState.Disabled">
            <summary>The XP control object is in the disabled state.</summary>
        </member>
        <member name="F:Spire.DataExport.Utils.XPButton.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.#cctor">
            <summary>
            Initializes all static fields of the XpButton class.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.XPButton.FlatStyle">
            <summary>
            Initializes a new instance of the XpButton class.
            </summary>
        </member>
        <member name="P:Spire.DataExport.Utils.XPButton.BorderRectangle">
            <value>Gets the clipping rectangle of the XpButton object's border.</value>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.OnDrawNormal(System.Drawing.Graphics)">
            <summary>
            Draws the normal state of the XpButton.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the XpButton.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.OnDrawHover(System.Drawing.Graphics)">
            <summary>
            Draws the hover state of the XpButton.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the XpButton.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.OnDrawPressed(System.Drawing.Graphics)">
            <summary>
            Draws the pressed state of the XpButton.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the XpButton.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.OnDrawNormalEllipse(System.Drawing.Graphics)">
            <summary>
            Draws the default state of the XpButton.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the XpButton.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.OnDrawDisabled(System.Drawing.Graphics)">
            <summary>
            Draws the disabled state of the XpButton.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the XpButton.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.OnDrawTextAndImage(System.Drawing.Graphics)">
            <summary>
            Draws the text of the XpButton.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the XpButton.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.DrawNormalButton(System.Drawing.Graphics)">
            <summary>
            Draws the ordinary look of the XpButton object.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the XpButton.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.DrawOuterShadow(System.Drawing.Graphics)">
            <summary>
            Draws the outer shadow of the XpButton object.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the outer shadow.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.DrawBorder(System.Drawing.Graphics)">
            <summary>
            Draws the dark blue border of the XpButton object.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to paint the border.</param>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.XPButton.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:Spire.DataExport.Utils.ControlPaint.DrawRoundedRectangle(System.Drawing.Graphics,System.Drawing.Pen,System.Drawing.Rectangle,System.Drawing.Size)">
            <summary>
            Draws a rectangle with rounded edges.
            </summary>
            <param name="g">The System.Drawing.Graphics object to be used to draw the rectangle.</param>
            <param name="p">A System.Drawing.Pen object that determines the color, width, and style of the rectangle.</param>
            <param name="rc">A System.Drawing.Rectangle structure that represents the rectangle to draw.</param>
            <param name="size">Pixel indentation that determines the roundness of the corners.</param>
        </member>
        <member name="T:Spire.DataExport.XLS.Borders">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.BoundSheet">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.BoundSheetList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.BoundSheetList.Item(System.Int32)">
            <summary>
            Use this indexer to access the XlsBoundSheet object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.Cell">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.IsBoolean">
            <summary>
            Indicates whether the CellType equals Boolean.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.IsDateTime">
            <summary>
            Indicates whether the CellType equals DateTime
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.IsNumeric">
            <summary>
            Indicates whether the CellType equals Numeric.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.IsString">
            <summary>
            Indicates whether the CellType equals String.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.IsFormula">
            <summary>
            Indicates whether the CellType equals Formula.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.CellType">
            <summary>
            Gets the type of the cell value.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.Column">
            <summary>
            Gets or sets the horizontal position of the cell in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.Row">
            <summary>
            Gets or sets the vertical position of the cell in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.DateTimeFormat">
            <summary>
            Gets or sets the formatting string for the date/time values.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.NumericFormat">
            <summary>
            Gets or sets the formatting string for the numeric values.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.Format">
            <summary>
            Gets or sets the formatting options for the cell in the Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.Value">
            <summary>
            Gets or sets the value of the cell.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.CultureName">
            <summary>
            Gets or sets the culture name.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cell.DisplayName">
            <summary>
            Returns the string of the display name.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellBorder">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellColumn">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellExport">
            <summary>
            The XLSExport is the export component for data exporting into MS Excel file format.
            </summary>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.SaveToStream(System.IO.Stream)">
            <summary>
            Save export data to the memory stream
            </summary>
            <param name="Stream">Stream object</param>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddBoolean(System.UInt16,System.UInt16,System.Boolean)">
            <summary>
            Adds boolean value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">Boolean value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddDateTime(System.UInt16,System.UInt16,System.DateTime)">
            <summary>
            Adds the datetime value to specified cell.
            </summary>
            <param name="Col">Coulumn</param>
            <param name="Row">Row</param>
            <param name="Value">DateTime value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddNumeric(System.UInt16,System.UInt16,System.String,System.Double)">
            <summary>
            Adds the numeric value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="NumericFormat">Numberic Format string</param>
            <param name="Value">Double value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddNumeric(System.UInt16,System.UInt16,System.Double)">
            <summary>
            Adds the numeric value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">Double value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddString(System.UInt16,System.UInt16,System.String)">
            <summary>
            Adds string value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">String value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddFormula(System.UInt16,System.UInt16,System.String)">
            <summary>
            Adds a formula cell in specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">Formula value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddHyperLink(System.UInt16,System.UInt16,System.String,System.String)">
            <summary>
            Adds a hyperlink cell in specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Title">Title</param>
            <param name="Url">Url</param>
            <returns>CellHyperlink object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.CellExport.AddMergedCells(System.UInt16,System.UInt16,System.UInt16,System.UInt16)">
            <summary>
            Adds a merged cell in specified ranges.
            </summary>
            <param name="StartRow">First row in range</param>
            <param name="EndRow">Last row in range</param>
            <param name="StartCol">First column in range</param>
            <param name="EndCol">Last column in range</param>
            <returns>MergedCells object</returns>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.ExportStage">
            <summary>
            Indicates the stage of the export process.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Sheets">
            <summary>
            Gets or sets Excel worksheets in the result Excel file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Pictures">
            <summary>
            Gets or sets pictures in the result Excel file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.ColumnsWidth">
            <summary>
            Gets or sets column width of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.AutoFitColWidth">
            <summary>
            Gets or sets width of each column  in the result file is set automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.AutoFitTitleWidth">
            <summary>
            Gets or sets width of each column's title  in the result file is set automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.AutoFormula">
            <summary>
            Indicates whether automatic detection of cell type is the formula type , when the value of cell start with '=' character.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.SheetOptions">
            <summary>
            Gets or sets options of the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.ColumnFormats">
            <summary>
            Gets or sets column formats of each column.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.ItemType">
            <summary>
            Indicates whether StripStyles should be applied to columns or rows of the result Excel sheet.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.ItemStyles">
            <summary>
            Gets or sets repeating styles for columns or rows in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Hyperlinks">
            <summary>
            Gets or sets hyperlinks in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Notes">
            <summary>
            Gets or sets note cell in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Charts">
            <summary>
            Gets or sets chart in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Images">
            <summary>
            Gets or sets images in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Cells">
            <summary>
            Gets or sets cell value in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.MergedCells">
            <summary>
            Gets or sets merged cell in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.Background">
            <summary>
            Gets or sets background image in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.HeaderRows">
            <summary>
            Gets or sets header rows in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.StartDataCol">
            <summary>
            Gets or sets start column in the result Excel dcoument.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.FooterRows">
            <summary>
            Gets or sets footer rows in the result Excel documents.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.DataSource">
            <summary>
            Gets or sets the data source type.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.SQLCommand">
            <summary>
            Gets or sets the exported SQL command.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.DataTable">
            <summary>
            Gets or sets the exported table.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.ListView">
            <summary>
            Gets or sets the exported list view.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.AdvancedExportedRecord">
            <summary>
            Occur after the export of each source record.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.AdvancedGetExportText">
            <summary>
            Occur when get export of source string.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.AdvancedBeforeExportRow">
            <summary>
            Occur before the export of each source record.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.GetHeaderParams">
            <summary>
            Occur when gets the header cell values.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.GetTitleParams">
            <summary>
            Occur when gets column titles value.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.GetBeforeDataParams">
            <summary>
            Occur when get footer cell value.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.GetDataParams">
            <summary>
            Occur when gets cells value.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.GetAggregateParams">
            <summary>
            Occur when gets aggregate value.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.GetFooterParams">
            <summary>
            Occurs when gets footer cell value.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.BeforeExportSheet">
            <summary>
            Occurs before exporting each worksheet.
            </summary>
        </member>
        <member name="E:Spire.DataExport.XLS.CellExport.AfterExportSheet">
            <summary>
            Occur after exporting each worksheet.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellFont">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellGraphic">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellGraphic.FileName">
            <summary>
            Defines the name of the file that contains the picture.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellHyperlink">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlink.Col">
            <summary>
            Defines the horizontal position of the link.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlink.Row">
            <summary>
            Defines the vertical position of the link.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlink.Format">
            <summary>
            Defines parameters of displaying the hyperlink in the result document. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlink.Style">
            <summary>
            Defines the type of the hyperlink target.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlink.Target">
            <summary>
            Defines the hyperlink target.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlink.Title">
            <summary>
            Defines the hyperlink text.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlink.Tip">
            <summary>
            Defines the text of the hint to display in Excel for the link.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellHyperlinks">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellHyperlinks.Item(System.Int32)">
            <summary>
            Use this indexer to access the CellHyperlink objects by Index.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellImage">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellImage.Column">
            <summary>
            Defines the horizontal position of the image in the result Excel file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellImage.Row">
            <summary>
            Defines the vertical position of the image in the result Excel file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellImage.PictureName">
            <summary>
            Defines the name of the picture that the image uses.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellImage.Title">
            <summary>
            Defines the title of the image that would be displayed in the result Excel file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellImage.Zoom">
            <summary>
            Defines zooming ratio for the image in the result Excel file in percentage wise.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellImages">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellImages.Item(System.Int32)">
            <summary>
            Use this indexer to access the CellImage object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellNote">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNote.Row">
            <summary>
            Gets or sets the vertical position of the note cell.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNote.Col">
            <summary>
            Gets or sets the horizontal position of the note cell.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNote.Lines">
            <summary>
            Gets or sets note text.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNote.Format">
            <summary>
            Gets or sets the formatting string of the note in Excel document.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellNoteFormat">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.Alignment">
            <summary>
            Gets or sets the text alignment in the note cell.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.BackgroundColor">
            <summary>
            Gets or sets background color for the gradient fill.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.ForegroundColor">
            <summary>
            Gets or sets foreground color for the notes.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.FillType">
            <summary>
            Gets or sets the type of filling the note.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.Font">
            <summary>
            Gets or sets text font of the note.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.Transparency">
            <summary>
            Gets or sets the percentage of the note transparency.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.Orientation">
            <summary>
            Gets or sets the note orientation.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNoteFormat.Gradient">
            <summary>
            Gets or sets the type of the gradient fill.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellNotes">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellNotes.Item(System.Int32)">
            <summary>
            Use this indexer to access the CellNote objects by Index.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellPicture">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellPicture.Name">
            <summary>
            Gets or sets the picture name in the result Excel document.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CellPictures">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CellPictures.Item(System.Int32)">
            <summary>
            Use this indexer to access the CellPicture object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.Cells">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Cells.Item(System.Int32)">
            <summary>
            Use this indexer to access the Cell object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.Chart">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.AutoColor">
            <summary>
            Enables or disables the automatic defining colors of the chart series in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.CategoryLabels">
            <summary>
            Allows you to define the data range for the horizontal axis lables of the chart.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.CategoryLabelsType">
            <summary>
            Defines the type of data range for marking the horizontal axis of the chart.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.CategoryLabelsColumn">
            <summary>
            Defines the data column name for the horizontal axis lables of the chart.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.LegendPlacement">
            <summary>
            Defines the position of the chart legend.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.Position">
            <summary>
            Defines the chart position in the result Excel document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.Series">
            <summary>
            Contains the collection of the chart series, which belongs to this chart.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.DataRangeSheet">
            <summary>
            Gets or sets the worksheet of the data range.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.ShowLegend">
            <summary>
            Enables or disables displaying of the chart legend.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.Style">
            <summary>
            Defines the chart style.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Chart.Title">
            <summary>
            Defines the chart title in the result Excel document.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ChartAutoPosition">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ChartCustomPosition">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartCustomPosition.X1">
            <summary>
            Gets or sets the horizontal position of the top left corner of the chart.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartCustomPosition.X2">
            <summary>
            Gets or sets the horisontal position of the bottom right corner of the chart.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartCustomPosition.Y1">
            <summary>
            Gets or sets the vertical position of the top left corner of the chart.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartCustomPosition.Y2">
            <summary>
            Gets or sets the vertical position of the bottom right corner of the chart.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ChartPosition">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.Charts">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.Charts.Item(System.Int32)">
            <summary>
            Use this indexer to access the Chart object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ChartSeries">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartSeries.Color">
            <summary>
            Gets or sets the chart series color.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartSeries.DataColumn">
            <summary>
            Gets or sets column name for the result chart series.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartSeries.DataRange">
            <summary>
            Gets or sets data range for the chart series.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartSeries.DataRangeType">
            <summary>
            Gets or sets the data range type for the chart series.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartSeries.DataRangeSheet">
            <summary>
            Gets or sets the worksheet of the data range.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartSeries.Title">
            <summary>
            Gets or sets the title of the result chart series.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ChartSeriesList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ChartSeriesList.Item(System.Int32)">
            <summary>
            Use this indexer to access the ChartSeries object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ColumnFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ColumnFormats">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ColumnFormats.Item(System.Int32)">
            <summary>
            Use this indexer to access the FieldFormat objects by Index.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ColumnList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ColumnList.Item(System.Int32)">
            <summary>
            Use this indexer to access the XlsColumn object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.DisposabledObject">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.XlsCommon">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.CustomItem">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.CustomItem.ListView">
            <summary>
            Gets or sets the exported list view.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.DataRange">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.DataRange.ColX">
            <summary>
            Gets or sets the left side of the data range.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.DataRange.ColY">
            <summary>
            Gets or sets the right side of the data range.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.DataRange.RowX">
            <summary>
            Gets the top of the data range.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.DataRange.RowY">
            <summary>
            Gets or sets the bottom of the data range.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.FontList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.FontList.Item(System.Int32)">
            <summary>
            Use this indexer to access the CellFont object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.ItemStyles">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.ItemStyles.Item(System.Int32)">
            <summary>
            Use this indexer to access the StripStyle object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.MergedCellList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.MergedCellList.Item(System.Int32)">
            <summary>
            Use this indexer to access the MergedCells object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.MergedCells">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.MergedCells.StartCol">
            <summary>
            Gets or sets the first column of the cell range to merge.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.MergedCells.StartRow">
            <summary>
            Gets or sets the first row of the cell range to merge.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.MergedCells.EndCol">
            <summary>
            Gets or sets the last column of the cell range to merge.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.MergedCells.EndRow">
            <summary>
            Gets or sets the last row of the cell range to merge.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.MergedCells.DisplayName">
            <summary>
            Returns the string of the display name.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.SheetOptions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.StripStyle">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.TextAlignment">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.TextFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.TextFormatList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.TextFormatList.Item(System.Int32)">
            <summary>
            Use this indexer to access the XlsTextFormat object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.WorkSheet">
            <summary>
            
            </summary>
        </member>
        <member name="M:Spire.DataExport.XLS.WorkSheet.AddBoolean(System.UInt16,System.UInt16,System.Boolean)">
            <summary>
            Adds boolean value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">Boolean value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.WorkSheet.AddDateTime(System.UInt16,System.UInt16,System.String,System.DateTime)">
            <summary>
            Adds the datetime value to specified cell.
            </summary>
            <param name="Col">Coulumn</param>
            <param name="Row">Row</param>
            <param name="Value">DateTime value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.WorkSheet.AddNumeric(System.UInt16,System.UInt16,System.String,System.Double)">
            <summary>
            Adds the numeric value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="NumericFormat">Numberic Format string</param>
            <param name="Value">Double value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.WorkSheet.AddNumeric(System.UInt16,System.UInt16,System.Double)">
            <summary>
            Adds the numeric value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">Double value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.WorkSheet.AddString(System.UInt16,System.UInt16,System.String)">
            <summary>
            Adds string value to specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">String value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.WorkSheet.AddFormula(System.UInt16,System.UInt16,System.String)">
            <summary>
            Adds a formula cell in specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Value">Formula value</param>
            <returns>Cell object</returns>
        </member>
        <member name="M:Spire.DataExport.XLS.WorkSheet.AddHyperLink(System.UInt16,System.UInt16,System.String,System.String)">
            <summary>
            Adds a hyperlink cell in specified cell.
            </summary>
            <param name="Col">Column</param>
            <param name="Row">Row</param>
            <param name="Title">Title</param>
            <param name="Url">Url</param>
            <returns>CellHyperlink object</returns>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.AutoFitColWidth">
            <summary>
            Gets or sets width of each column  in the result file is set automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.AutoFitTitleWidth">
            <summary>
            Gets or sets width of each column's title  in the result file is set automatically.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.Columns">
            <summary>
            Gets or sets the field bindings and display attributes of the columns in the exported file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.Header">
            <summary>
            Gets or sets the text placed before the exproted data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.Titles">
            <summary>
            Gets or sets the column titles in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.Footer">
            <summary>
            Gets or sets the text placed after the exproted data.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.CustomFormats">
            <summary>
            Gets or sets a special format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.ColumnsWidth">
            <summary>
            Gets or sets column width of result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.MaxRows">
            <summary>
            Determines the number of rows that exported from the source table. If MaxRows equals 0, then the recrods exported no limited. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.SkipRows">
            <summary>
            Determines the number of records, which are not exported. If SkipRecCount = 0, then all the records are exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.DataSource">
            <summary>
            Gets or sets the data source type.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.SQLCommand">
            <summary>
            Gets or sets the exported SQL command.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.DataTable">
            <summary>
            Gets or sets the exported table.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheet.ListView">
            <summary>
            Gets or sets the exported list view.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.WorkSheets">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.WorkSheets.Item(System.Int32)">
            <summary>
            Use this indexer to access the WorkSheet objects by Index.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.FillType">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.XlsXFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.XlsXFormatColRow">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.XLSXFormatColRowList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.XLSXFormatColRowList.Item(System.Int32)">
            <summary>
            Use this indexer to access the XlsXFormatColRow object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.XlsXFormatField">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.XLSXFormatFieldList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.XLSXFormatFieldList.Item(System.Int32)">
            <summary>
            Use this indexer to access the XlsXFormatField object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XLS.XLSXFormatList">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XLS.XLSXFormatList.Item(System.Int32)">
            <summary>
            Use this indexer to access the XlsXFormat object by Index
            </summary>
        </member>
        <member name="T:Spire.DataExport.XML.XMLExport">
            <summary>
            The XMLExport is the export component for data exporting into XML file format.
            </summary>
        </member>
        <member name="M:Spire.DataExport.XML.XMLExport.SaveToFile">
            <summary>
            Execute performs the export of source data to file.
            </summary>
            <remarks>
            After calling Execute, the file with exported data will be created.
            </remarks>
        </member>
        <member name="M:Spire.DataExport.XML.XMLExport.SaveToHttpResponse(System.String,System.Web.HttpResponse,Spire.DataExport.Common.SaveType)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
            <param name="saveType">Save type : attachment or inline mode</param>
        </member>
        <member name="M:Spire.DataExport.XML.XMLExport.SaveToHttpResponse(System.String,System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="FileName">File Name</param>
            <param name="response">Http response</param>
        </member>
        <member name="M:Spire.DataExport.XML.XMLExport.SaveToHttpResponse(System.Web.HttpResponse)">
            <summary>
            Save export data to the http response.
            </summary>
            <param name="response">Http response</param>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.NotTruncatableColumns">
            <summary>
            Allows you to select string fields that will not be truncated by occurrences of carriage returns. 
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.XmlOptions">
            <summary>
            Gets or sets options of the result xml file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.Titles">
            <summary>
            Gets or sets the column titles in the result file.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.DataFormats">
            <summary>
            Gets or sets data format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.CustomFormats">
            <summary>
            Gets or sets a special format for data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.DataEncoding">
            <summary>
            Gets or sets the encoding type of result data exported.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.ExportNullField">
            <summary>
            Indicate whether format exported value according to the dataformats property.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XMLExport.ExportLongColumn">
            <summary>
            Indicate whether export long char/binary column.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XML.XmlExportConsts">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.DataExport.XML.XmlOptions">
            <summary>
            
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XmlOptions.Version">
            <summary>
            Gets or sets the xml version.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XmlOptions.Encoding">
            <summary>
            Gets or sets the encoding of the result xml document.
            </summary>
        </member>
        <member name="P:Spire.DataExport.XML.XmlOptions.StandAlone">
            <summary>
            Indicates whether the result document standalone.
            </summary>
        </member>
        <member name="T:Spire.DataExport.XML.XmlWriter">
            <summary>
            
            </summary>
        </member>
        <member name="M:Spire.DataExport.XML.XmlWriter.StartTag(System.String,System.String)">
            <summary>
            start tag and close
            </summary>
            <param name="TagName"></param>
            <param name="Options"></param>
        </member>
        <member name="T:Spire.XLS.Delegates.FunctionEvent">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.EventArgs.FunctionEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffBlank">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffBOF">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffBoolErr">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffBoundSheet">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffBoundSheetList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffCell">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffCellList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffColInfo">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffColRow">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffColRowList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffContinue">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffCountry">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffDefColWidth">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffDefRowHeight">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffDimensions">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffEOF">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffExternSheet">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffFont">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffFontList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffFormatList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffFormula">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffHLink">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffLabelSST">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffMSODrawing">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffMSODrawingGroup">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffMulBlank">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffMulRK">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffMultiple">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffName">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffNameList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffNote">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffNumber">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffObj">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffRecord">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffRecordList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffRK">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffSCL">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffSelection">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffSetup">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffShrFmla">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffShrFmlaList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffSST">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffSstList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffString">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffStyle">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffStyleList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffSupbookInternal">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffTXO">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffTXORun">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffWindow1">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffWindow2">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffXF">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.BiffXFList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.Chart3d">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartAI">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartArea">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartAreaFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartAxcExt">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartAxesUsed">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartAxis">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartAxisLineFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartAxisParent">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartBar">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartCatSerRange">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartChart">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartChartFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartDataFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartDefaultText">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartFBI">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartFontX">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartFrame">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartLegend">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartLine">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartLineFormat">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartObjectLink">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartPie">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartPlotGrowth">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartPos">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartRadar">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartRadarArea">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartSeries">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartSeriesText">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartSerToCRT">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartShtProps">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartSIIndex">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartSurface">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartText">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartTick">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ChartValueRange">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoBLIPData">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoBSEData">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoClientAnchor">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoClientData">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoClientTextBox">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoContainer">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoDg">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoDgg">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoObject">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoOPT">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoOPTData">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoOPTDataList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoSp">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoSpgr">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.MsoSplitMenuColors">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ObjCmo">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ObjEnd">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ObjNts">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ObjRecord">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.ObjRecordList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.SstString">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.SstStringList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.SstStrings">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsChartSheet">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsChartSheetList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsCol">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsColInfo">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsColInfoList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsColList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsFile">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsGlobals">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsMSODrawing">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsRow">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsRowList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsSection">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsSstEntry">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsSstIndex">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsString">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsWorkbook">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsWorkSheet">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.File.XlsWorkSheetList">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.IO.ReadOleStream">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.IO.WriteOleStream">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.IO.XlsStream">
            <summary>
            
            </summary>
        </member>
        <member name="T:Spire.XLS.Utils.XlsUtils">
            <summary>
            
            </summary>
        </member>
    </members>
</doc>
