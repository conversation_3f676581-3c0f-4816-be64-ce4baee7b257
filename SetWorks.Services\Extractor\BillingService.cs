using System;
using System.Collections.Generic;
using System.Data;
using BOs;
using Newtonsoft.Json;
using SetWorks.Common.Tools.DataExtractor.Core;
using SetWorks.Common.Tools.DataExtractor.V3;
using SetWorks.Common.Tools.DataExtractor.V3.Core;
using SetWorks.Infrastructure;
using SetWorks.Services.Extractor;
using DataExtractor = SetWorks.Core.Models.DataExtractor;

namespace SetWorks.Services
{
    public class BillingService : DataExtractorRunner
    {
        public void assignFilters(DateTime month, DateTime ServiceEndDate)
        {
            var filterJson = @"
            [
                {
                    'FilterID': 'Consumer',
                    'FilterCode': 'Consumer',
                    'FilterValue': '0'
                },
                {
                    'FilterID': 'Consumer_Status',
                    'FilterCode': 'Consumer_Status',
                    'FilterValue': '2'
                },
                {
                    'FilterID': 'Department_Multi_Select',
                    'FilterCode': 'Department_Multi_Select',
                    'FilterValue': ['0']
                },
                {
                    'FilterID': 'Funding_Source_Multi_Select',
                    'FilterCode': 'Funding_Source_Multi_Select',
                    'FilterValue': ['0']
                },
                {
                    'FilterID': 'Batch',
                    'FilterCode': 'Batch',
                    'FilterValue': '0'
                },
                {
                    'FilterID': 'Date_Range_From',
                    'FilterCode': 'Date_Range_From',
                    'FilterValue': '1/1/2025 12:00:00 AM'
                },
                {
                    'FilterID': 'Date_Range_To',
                    'FilterCode': 'Date_Range_To',
                    'FilterValue': '1/31/2025 12:00:00 AM'
                },
                {
                    'FilterID': 'Contract_Multi_Select',
                    'FilterCode': 'Contract_Multi_Select',
                    'FilterValue': ['0']
                },
                {
                    'FilterID': 'Service_Type',
                    'FilterCode': 'Service_Type',
                    'FilterValue': '0'
                },
                {
                    'FilterID': 'Activity_Record_Service_Multi_Select',
                    'FilterCode': 'Activity_Record_Service_Multi_Select',
                    'FilterValue': ['0']
                },
                {
                    'FilterID': 'Report_Option_Generic_Checkbox18',
                    'FilterCode': 'Report_Option_Generic_Checkbox18',
                    'FilterValue': 'False'
                },
                {
                    'FilterID': 'Generic_Filter7',
                    'FilterCode': 'ActivityRecordDateFilterMode',
                    'FilterValue': '0'
                }
            ]";

            List<DSDataExportFilter> filters = JsonConvert.DeserializeObject<List<DSDataExportFilter>>(filterJson);

            DateTime firstDayOfMonth = new DateTime(month.Year, month.Month, 1);
            DateTime lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            if (ServiceEndDate != DateTime.MinValue && ServiceEndDate < lastDayOfMonth)
            {
                lastDayOfMonth = ServiceEndDate;
            }

            foreach (var filter in filters)
            {
                if (filter.FilterID == "Date_Range_From")
                {
                    filter.FilterValue = firstDayOfMonth.ToString("M/d/yyyy hh:mm:ss tt");
                }
                else if (filter.FilterID == "Date_Range_To")
                {
                    filter.FilterValue = lastDayOfMonth.ToString("M/d/yyyy hh:mm:ss tt");
                }
            }

            reformatDateRangeFilters(filters);
            this.filters = filters;
            
        }
        

    }   
}